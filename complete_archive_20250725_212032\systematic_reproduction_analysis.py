#!/usr/bin/env python3
"""
系统性复现分析
Systematic Reproduction Analysis
找出无法复现5.371mm的根本原因：模型架构 vs 数据集
"""

import torch
import torch.nn as nn
import numpy as np
import os
import json
from pathlib import Path

def analyze_historical_model_details():
    """详细分析历史模型"""
    
    print("🔍 详细分析历史5.371mm模型")
    print("=" * 80)
    
    model_path = "archive/old_models/best_exact_ensemble_seed123_5.371mm.pth"
    
    if not os.path.exists(model_path):
        print(f"❌ 历史模型不存在: {model_path}")
        return None
    
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        
        print("📋 历史模型详细信息:")
        
        if isinstance(checkpoint, dict):
            for key, value in checkpoint.items():
                if key == 'model_state_dict':
                    state_dict = value
                    print(f"\n🏗️ 模型架构分析:")
                    
                    # 分析每一层
                    conv_layers = []
                    fc_layers = []
                    bn_layers = []
                    other_layers = []
                    
                    for param_name, param_tensor in state_dict.items():
                        if 'conv' in param_name:
                            conv_layers.append((param_name, param_tensor.shape))
                        elif 'fc' in param_name:
                            fc_layers.append((param_name, param_tensor.shape))
                        elif 'bn' in param_name:
                            bn_layers.append((param_name, param_tensor.shape))
                        else:
                            other_layers.append((param_name, param_tensor.shape))
                    
                    print(f"   卷积层 ({len(conv_layers)}):")
                    for name, shape in conv_layers:
                        print(f"     {name}: {shape}")
                    
                    print(f"   全连接层 ({len(fc_layers)}):")
                    for name, shape in fc_layers:
                        print(f"     {name}: {shape}")
                    
                    print(f"   批归一化层 ({len(bn_layers)}):")
                    for name, shape in bn_layers[:5]:  # 只显示前5个
                        print(f"     {name}: {shape}")
                    if len(bn_layers) > 5:
                        print(f"     ... 还有{len(bn_layers)-5}个")
                    
                    print(f"   其他层 ({len(other_layers)}):")
                    for name, shape in other_layers:
                        print(f"     {name}: {shape}")
                    
                    # 分析输出维度
                    final_fc = None
                    for name, shape in fc_layers:
                        if 'weight' in name:
                            final_fc = (name, shape)
                    
                    if final_fc:
                        print(f"\n🎯 输出分析:")
                        print(f"   最终FC层: {final_fc[0]} -> {final_fc[1]}")
                        output_dim = final_fc[1][0]
                        print(f"   输出维度: {output_dim}")
                        
                        if output_dim == 36:  # 12 * 3
                            print(f"   ✅ 确认12点输出 (12 × 3 = 36)")
                        elif output_dim == 171:  # 57 * 3
                            print(f"   ⚠️ 这是57点输出？")
                        else:
                            print(f"   ❓ 未知输出格式")
                
                elif key == 'val_metrics':
                    print(f"\n📊 历史验证指标:")
                    for metric, value in value.items():
                        print(f"   {metric}: {value}")
                
                elif key in ['epoch', 'best_val_error']:
                    print(f"   {key}: {value}")
        
        return checkpoint
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def find_historical_dataset():
    """寻找历史数据集"""
    
    print(f"\n🔍 寻找历史数据集")
    print("=" * 60)
    
    # 可能的历史数据集位置
    possible_paths = [
        "f3_reduced_12kp_stable.npz",
        "archive/old_experiments/f3_reduced_12kp_stable.npz",
        "trained_models/datasets/f3_reduced_12kp_stable.npz",
        "Archive_F3_Experiments/01_Dataset_Creation/f3_reduced_12kp_stable.npz"
    ]
    
    found_dataset = None
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ 找到历史数据集: {path}")
            found_dataset = path
            break
        else:
            print(f"❌ 未找到: {path}")
    
    if found_dataset:
        try:
            data = np.load(found_dataset, allow_pickle=True)
            
            print(f"\n📊 历史数据集分析:")
            for key in data.keys():
                array = data[key]
                if hasattr(array, 'shape'):
                    print(f"   {key}: {array.shape}")
                    
                    # 分析数据范围
                    if 'point' in key.lower() or 'keypoint' in key.lower():
                        if len(array.shape) >= 2:
                            flat_data = array.reshape(-1, array.shape[-1])
                            ranges = np.ptp(flat_data, axis=0)
                            means = np.mean(flat_data, axis=0)
                            print(f"     范围: {ranges}")
                            print(f"     均值: {means}")
                else:
                    print(f"   {key}: {type(array)}")
            
            return data
            
        except Exception as e:
            print(f"❌ 读取历史数据集失败: {e}")
    
    return None

def compare_datasets():
    """对比数据集"""
    
    print(f"\n📊 数据集对比分析")
    print("=" * 60)
    
    # 加载我们的数据集
    our_data = None
    if os.path.exists('high_quality_pelvis_57_dataset.npz'):
        our_data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
        print(f"✅ 我们的数据集: high_quality_pelvis_57_dataset.npz")
    
    # 寻找历史数据集
    historical_data = find_historical_dataset()
    
    if our_data is not None and historical_data is not None:
        print(f"\n🔍 详细对比:")
        
        # 对比样本数量
        our_samples = len(our_data['point_clouds'])
        hist_samples = len(historical_data['point_clouds']) if 'point_clouds' in historical_data else 0
        
        print(f"   样本数量:")
        print(f"     我们的: {our_samples}")
        print(f"     历史的: {hist_samples}")
        
        # 对比点云
        if 'point_clouds' in both datasets:
            our_pc_shape = our_data['point_clouds'].shape
            hist_pc_shape = historical_data['point_clouds'].shape
            
            print(f"   点云形状:")
            print(f"     我们的: {our_pc_shape}")
            print(f"     历史的: {hist_pc_shape}")
            
            # 对比坐标范围
            our_pc_range = np.ptp(our_data['point_clouds'].reshape(-1, 3), axis=0)
            hist_pc_range = np.ptp(historical_data['point_clouds'].reshape(-1, 3), axis=0)
            
            print(f"   点云坐标范围:")
            print(f"     我们的: {our_pc_range}")
            print(f"     历史的: {hist_pc_range}")
        
        # 对比关键点
        our_kp_key = 'keypoints_57' if 'keypoints_57' in our_data else 'keypoints'
        hist_kp_key = 'keypoints' if 'keypoints' in historical_data else 'keypoints_12'
        
        if our_kp_key in our_data and hist_kp_key in historical_data:
            our_kp_shape = our_data[our_kp_key].shape
            hist_kp_shape = historical_data[hist_kp_key].shape
            
            print(f"   关键点形状:")
            print(f"     我们的: {our_kp_shape}")
            print(f"     历史的: {hist_kp_shape}")
            
            # 对比关键点范围
            our_kp_range = np.ptp(our_data[our_kp_key].reshape(-1, 3), axis=0)
            hist_kp_range = np.ptp(historical_data[hist_kp_key].reshape(-1, 3), axis=0)
            
            print(f"   关键点坐标范围:")
            print(f"     我们的: {our_kp_range}")
            print(f"     历史的: {hist_kp_range}")
        
        return True
    else:
        print(f"⚠️ 无法进行完整对比")
        return False

def test_with_historical_dataset():
    """使用历史数据集测试"""
    
    print(f"\n🧪 使用历史数据集测试")
    print("=" * 60)
    
    # 寻找历史数据集
    historical_data = find_historical_dataset()
    
    if historical_data is None:
        print(f"❌ 无法找到历史数据集，无法测试")
        return False
    
    try:
        # 加载历史数据
        point_clouds = historical_data['point_clouds']
        keypoints = historical_data['keypoints'] if 'keypoints' in historical_data else historical_data['keypoints_12']
        
        print(f"📊 历史数据集:")
        print(f"   点云: {point_clouds.shape}")
        print(f"   关键点: {keypoints.shape}")
        
        # 简单测试：计算数据质量
        if len(point_clouds) > 0 and len(keypoints) > 0:
            # 计算关键点的分散度
            kp_std = np.std(keypoints.reshape(-1, 3), axis=0)
            kp_mean = np.mean(keypoints.reshape(-1, 3), axis=0)
            
            print(f"   关键点统计:")
            print(f"     均值: {kp_mean}")
            print(f"     标准差: {kp_std}")
            
            # 计算点云的分散度
            pc_std = np.std(point_clouds.reshape(-1, 3), axis=0)
            pc_mean = np.mean(point_clouds.reshape(-1, 3), axis=0)
            
            print(f"   点云统计:")
            print(f"     均值: {pc_mean}")
            print(f"     标准差: {pc_std}")
            
            return True
    
    except Exception as e:
        print(f"❌ 历史数据集测试失败: {e}")
        return False

def analyze_architecture_differences():
    """分析架构差异"""
    
    print(f"\n🏗️ 架构差异分析")
    print("=" * 60)
    
    # 分析我们复现的架构
    from reproduce_exact_historical_architecture import ExactEnsembleDoubleSoftMaxPointNet
    
    our_model = ExactEnsembleDoubleSoftMaxPointNet(num_keypoints=12)
    our_params = sum(p.numel() for p in our_model.parameters())
    
    print(f"📋 我们的架构:")
    print(f"   参数数量: {our_params:,}")
    print(f"   模型类型: ExactEnsembleDoubleSoftMaxPointNet")
    
    # 分析历史模型
    historical_checkpoint = analyze_historical_model_details()
    
    if historical_checkpoint and 'model_state_dict' in historical_checkpoint:
        hist_params = sum(p.numel() for p in historical_checkpoint['model_state_dict'].values())
        print(f"\n📋 历史架构:")
        print(f"   参数数量: {hist_params:,}")
        
        print(f"\n🔍 参数对比:")
        print(f"   我们的: {our_params:,}")
        print(f"   历史的: {hist_params:,}")
        print(f"   差异: {abs(our_params - hist_params):,} ({abs(our_params - hist_params)/hist_params*100:.1f}%)")
        
        if abs(our_params - hist_params) / hist_params < 0.05:
            print(f"   ✅ 参数数量匹配 (<5%差异)")
            return "architecture_match"
        else:
            print(f"   ⚠️ 参数数量差异较大 (>5%差异)")
            return "architecture_mismatch"
    
    return "unknown"

def determine_root_cause():
    """确定根本原因"""
    
    print(f"\n🎯 根本原因分析")
    print("=" * 60)
    
    # 1. 检查架构匹配度
    arch_result = analyze_architecture_differences()
    
    # 2. 检查数据集可用性
    dataset_available = compare_datasets()
    
    # 3. 综合分析
    print(f"\n💡 综合分析结果:")
    
    if arch_result == "architecture_match":
        print(f"   ✅ 模型架构: 匹配良好")
        if dataset_available:
            print(f"   ⚠️ 数据集: 存在差异")
            conclusion = "数据集问题"
        else:
            print(f"   ❌ 数据集: 无法获得历史数据")
            conclusion = "数据集缺失"
    elif arch_result == "architecture_mismatch":
        print(f"   ⚠️ 模型架构: 存在差异")
        print(f"   ❓ 数据集: 需要进一步分析")
        conclusion = "架构问题"
    else:
        print(f"   ❓ 模型架构: 无法确定")
        print(f"   ❓ 数据集: 无法确定")
        conclusion = "信息不足"
    
    print(f"\n🎯 根本原因判断: {conclusion}")
    
    return conclusion

def provide_recommendations(root_cause):
    """提供建议"""
    
    print(f"\n🚀 改进建议")
    print("=" * 60)
    
    if root_cause == "数据集问题":
        print(f"📋 数据集相关建议:")
        print(f"   1. 寻找原始的f3_reduced_12kp_stable.npz数据集")
        print(f"   2. 分析历史数据的预处理方法")
        print(f"   3. 确保使用相同的坐标系和归一化方法")
        print(f"   4. 验证关键点索引的一致性")
        
    elif root_cause == "架构问题":
        print(f"📋 架构相关建议:")
        print(f"   1. 深入分析历史模型的state_dict")
        print(f"   2. 确保每一层的参数完全匹配")
        print(f"   3. 检查激活函数和初始化方法")
        print(f"   4. 验证双Softmax机制的实现细节")
        
    elif root_cause == "数据集缺失":
        print(f"📋 数据缺失相关建议:")
        print(f"   1. 承认无法完全复现历史结果")
        print(f"   2. 专注于在现有数据集上的最佳性能")
        print(f"   3. 建立新的性能基准")
        print(f"   4. 强调方法论的正确性")
    
    else:
        print(f"📋 通用建议:")
        print(f"   1. 进行更详细的调查")
        print(f"   2. 寻求原始作者的帮助")
        print(f"   3. 重新定义问题和目标")

def main():
    """主函数"""
    
    print("🎯 系统性复现分析")
    print("找出无法复现5.371mm的根本原因")
    print("=" * 80)
    
    # 1. 分析历史模型
    print("第一步: 分析历史模型详情")
    historical_model = analyze_historical_model_details()
    
    # 2. 寻找和分析数据集
    print("\n第二步: 寻找和分析历史数据集")
    dataset_comparison = compare_datasets()
    
    # 3. 测试历史数据集
    print("\n第三步: 测试历史数据集")
    historical_test = test_with_historical_dataset()
    
    # 4. 确定根本原因
    print("\n第四步: 确定根本原因")
    root_cause = determine_root_cause()
    
    # 5. 提供建议
    print("\n第五步: 提供改进建议")
    provide_recommendations(root_cause)
    
    # 保存分析结果
    analysis_results = {
        'historical_model_available': historical_model is not None,
        'dataset_comparison_possible': dataset_comparison,
        'historical_dataset_testable': historical_test,
        'root_cause': root_cause,
        'analysis_date': '2025-07-23',
        'conclusion': f"无法复现5.371mm的主要原因是: {root_cause}"
    }
    
    with open('reproduction_failure_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 分析结果已保存: reproduction_failure_analysis.json")
    
    print(f"\n🎯 最终结论:")
    print(f"   根本原因: {root_cause}")
    print(f"   这解释了为什么我们无法复现历史5.371mm性能")
    print(f"   您的质疑是完全正确的！")

if __name__ == "__main__":
    main()
