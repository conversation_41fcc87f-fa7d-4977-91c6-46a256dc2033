#!/usr/bin/env python3
"""
在大数据集上测试12关键点模型
Test 12-point model on large dataset (97 samples)
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import os
from tqdm import tqdm
import matplotlib.pyplot as plt
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import json

class AnatomicalConstraintLoss(nn.Module):
    """解剖学约束损失函数"""
    
    def __init__(self, alpha=1.0, beta=0.5, gamma=0.3, delta=0.2):
        super(AnatomicalConstraintLoss, self).__init__()
        self.alpha = alpha    # 基础MSE权重
        self.beta = beta      # 距离约束权重
        self.gamma = gamma    # 角度约束权重
        self.delta = delta    # 对称性约束权重
        
        # 定义关键点索引 (基于12个关键点)
        self.f1_indices = [0, 1, 2, 3]      # F1区域关键点
        self.f2_indices = [4, 5, 6, 7]      # F2区域关键点
        self.f3_indices = [8, 9, 10, 11]    # F3区域关键点
        
        # 定义重要的解剖学距离约束
        self.distance_constraints = [
            # F1-F2对称性约束
            ([0, 4], 'symmetric_pair'),  # F1-1 和 F2-1 应该大致对称
            ([1, 5], 'symmetric_pair'),  # F1-2 和 F2-2 应该大致对称
            ([2, 6], 'symmetric_pair'),  # F1-3 和 F2-3 应该大致对称
            ([3, 7], 'symmetric_pair'),  # F1-4 和 F2-4 应该大致对称
            
            # 区域内距离约束
            ([0, 1], 'region_distance'),  # F1区域内相邻点
            ([1, 2], 'region_distance'),  # F1区域内相邻点
            ([4, 5], 'region_distance'),  # F2区域内相邻点
            ([5, 6], 'region_distance'),  # F2区域内相邻点
            ([8, 9], 'region_distance'),  # F3区域内相邻点
            ([9, 10], 'region_distance'), # F3区域内相邻点
        ]
    
    def forward(self, predicted, target):
        """计算总损失"""
        batch_size = predicted.size(0)
        
        # 基础MSE损失
        mse_loss = F.mse_loss(predicted, target)
        
        # 距离约束损失
        distance_loss = self.compute_distance_constraints(predicted, target)
        
        # 对称性约束损失
        symmetry_loss = self.compute_symmetry_constraints(predicted, target)
        
        # 角度约束损失
        angle_loss = self.compute_angle_constraints(predicted, target)
        
        # 总损失
        total_loss = (self.alpha * mse_loss + 
                     self.beta * distance_loss + 
                     self.gamma * angle_loss + 
                     self.delta * symmetry_loss)
        
        return total_loss, mse_loss, distance_loss, angle_loss, symmetry_loss
    
    def compute_distance_constraints(self, predicted, target):
        """计算距离约束损失"""
        distance_loss = 0.0
        
        for (idx1, idx2), constraint_type in self.distance_constraints:
            # 预测的距离
            pred_dist = torch.norm(predicted[:, idx1, :] - predicted[:, idx2, :], dim=1)
            # 真实的距离
            true_dist = torch.norm(target[:, idx1, :] - target[:, idx2, :], dim=1)
            
            # 距离一致性损失
            dist_loss = F.mse_loss(pred_dist, true_dist)
            distance_loss += dist_loss
        
        return distance_loss / len(self.distance_constraints)
    
    def compute_symmetry_constraints(self, predicted, target):
        """计算对称性约束损失"""
        symmetry_loss = 0.0
        
        # F1和F2区域的对称性
        f1_center = torch.mean(predicted[:, self.f1_indices, :], dim=1)
        f2_center = torch.mean(predicted[:, self.f2_indices, :], dim=1)
        
        # 期望F1和F2在X轴上大致对称
        symmetry_loss = torch.mean(torch.abs(f1_center[:, 0] + f2_center[:, 0]))
        
        return symmetry_loss
    
    def compute_angle_constraints(self, predicted, target):
        """计算角度约束损失"""
        angle_loss = 0.0
        
        # 计算一些重要的角度约束
        # 例如：F1-F3-F2的角度应该保持一致
        for i in range(predicted.size(0)):
            # F1中心到F3中心的向量
            f1_center = torch.mean(predicted[i, self.f1_indices, :], dim=0)
            f2_center = torch.mean(predicted[i, self.f2_indices, :], dim=0)
            f3_center = torch.mean(predicted[i, self.f3_indices, :], dim=0)
            
            # 计算角度
            vec1 = f1_center - f3_center
            vec2 = f2_center - f3_center
            
            # 预测的角度
            pred_angle = torch.acos(torch.clamp(
                torch.dot(vec1, vec2) / (torch.norm(vec1) * torch.norm(vec2)), -1, 1))
            
            # 真实的角度
            f1_center_true = torch.mean(target[i, self.f1_indices, :], dim=0)
            f2_center_true = torch.mean(target[i, self.f2_indices, :], dim=0)
            f3_center_true = torch.mean(target[i, self.f3_indices, :], dim=0)
            
            vec1_true = f1_center_true - f3_center_true
            vec2_true = f2_center_true - f3_center_true
            
            true_angle = torch.acos(torch.clamp(
                torch.dot(vec1_true, vec2_true) / (torch.norm(vec1_true) * torch.norm(vec2_true)), -1, 1))
            
            angle_loss += F.mse_loss(pred_angle, true_angle)
        
        return angle_loss / predicted.size(0)

class FixedMultiModalPointNet(nn.Module):
    """修复的多模态PointNet"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super(FixedMultiModalPointNet, self).__init__()
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 点云特征提取器
        self.point_conv1 = nn.Conv1d(3, 64, 1)
        self.point_conv2 = nn.Conv1d(64, 128, 1)
        self.point_conv3 = nn.Conv1d(128, 256, 1)
        self.point_conv4 = nn.Conv1d(256, 512, 1)
        self.point_conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化层
        self.point_bn1 = nn.BatchNorm1d(64)
        self.point_bn2 = nn.BatchNorm1d(128)
        self.point_bn3 = nn.BatchNorm1d(256)
        self.point_bn4 = nn.BatchNorm1d(512)
        self.point_bn5 = nn.BatchNorm1d(1024)
        
        # 全局特征提取
        self.global_conv1 = nn.Conv1d(1024, 512, 1)
        self.global_conv2 = nn.Conv1d(512, 256, 1)
        self.global_bn1 = nn.BatchNorm1d(512)
        self.global_bn2 = nn.BatchNorm1d(256)
        
        # 关键点预测头
        self.keypoint_fc1 = nn.Linear(256, 512)
        self.keypoint_fc2 = nn.Linear(512, 256)
        self.keypoint_fc3 = nn.Linear(256, num_keypoints * 3)
        
        # Dropout层
        self.dropout1 = nn.Dropout(0.3)
        self.dropout2 = nn.Dropout(0.4)
        self.dropout3 = nn.Dropout(0.5)
        
    def forward(self, point_cloud):
        """前向传播"""
        batch_size = point_cloud.size(0)
        
        # 点云特征提取
        x = point_cloud.transpose(2, 1)  # [B, 3, N]
        
        x = F.relu(self.point_bn1(self.point_conv1(x)))
        x = F.relu(self.point_bn2(self.point_conv2(x)))
        x = F.relu(self.point_bn3(self.point_conv3(x)))
        x = F.relu(self.point_bn4(self.point_conv4(x)))
        x = F.relu(self.point_bn5(self.point_conv5(x)))
        
        # 全局最大池化
        global_feature = torch.max(x, 2)[0]  # [B, 1024]
        
        # 全局特征处理
        x = global_feature.unsqueeze(2)  # [B, 1024, 1]
        x = self.global_conv1(x)
        if x.size(0) > 1:  # 只有batch_size > 1时才使用BatchNorm
            x = self.global_bn1(x)
        x = F.relu(x)

        x = self.global_conv2(x)
        if x.size(0) > 1:  # 只有batch_size > 1时才使用BatchNorm
            x = self.global_bn2(x)
        x = F.relu(x)
        x = x.squeeze(2)  # [B, 256]
        
        # 关键点预测
        x = F.relu(self.keypoint_fc1(x))
        x = self.dropout1(x)
        x = F.relu(self.keypoint_fc2(x))
        x = self.dropout2(x)
        keypoints = self.keypoint_fc3(x)
        
        # 重塑为关键点坐标
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

class KeypointDataset(Dataset):
    """关键点数据集"""
    
    def __init__(self, point_clouds, keypoints):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return torch.FloatTensor(self.point_clouds[idx]), torch.FloatTensor(self.keypoints[idx])

def load_large_dataset():
    """加载大数据集"""
    
    print("📊 加载大数据集...")
    
    try:
        # 加载57点数据集，但只使用12个关键点
        data = np.load('expansion_training_keypoints.npz', allow_pickle=True)
        
        keypoints_12 = data['input_12']  # 已经提取好的12个关键点
        sample_ids = data['sample_ids']
        
        print(f"✅ 数据加载成功:")
        print(f"   12关键点: {keypoints_12.shape}")
        print(f"   样本数: {len(sample_ids)}")
        
        # 我们需要对应的点云数据
        # 尝试从原始数据集加载点云
        point_clouds = []
        valid_indices = []
        
        for i, sample_id in enumerate(sample_ids):
            # 尝试加载对应的点云文件
            pc_file = f"{sample_id}_pointcloud.npy"
            if os.path.exists(pc_file):
                try:
                    pc = np.load(pc_file)
                    if len(pc) >= 10000:  # 确保点云足够大
                        point_clouds.append(pc)
                        valid_indices.append(i)
                except:
                    continue
        
        if len(point_clouds) == 0:
            print("⚠️ 没有找到对应的点云文件，生成模拟点云数据")
            # 生成模拟点云数据用于测试
            point_clouds = []
            for i in range(len(keypoints_12)):
                # 基于关键点生成模拟点云
                pc = generate_mock_point_cloud(keypoints_12[i])
                point_clouds.append(pc)
            valid_indices = list(range(len(keypoints_12)))
        
        # 过滤有效数据
        point_clouds = np.array(point_clouds)
        keypoints_12 = keypoints_12[valid_indices]
        sample_ids = sample_ids[valid_indices]
        
        print(f"📋 有效数据:")
        print(f"   点云: {point_clouds.shape}")
        print(f"   关键点: {keypoints_12.shape}")
        print(f"   样本数: {len(sample_ids)}")
        
        return point_clouds, keypoints_12, sample_ids
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None, None, None

def generate_mock_point_cloud(keypoints, num_points=50000):
    """基于关键点生成模拟点云"""
    
    # 计算关键点的边界框
    min_coords = np.min(keypoints, axis=0)
    max_coords = np.max(keypoints, axis=0)
    
    # 扩展边界框
    center = (min_coords + max_coords) / 2
    size = max_coords - min_coords
    expanded_size = size * 1.5
    
    # 生成随机点云
    points = []
    
    # 在关键点周围生成密集点
    for kp in keypoints:
        # 每个关键点周围生成一些点
        local_points = np.random.normal(kp, 2.0, (num_points // 12, 3))
        points.append(local_points)
    
    # 在整个区域生成稀疏点
    global_points = np.random.uniform(
        center - expanded_size/2, 
        center + expanded_size/2, 
        (num_points - len(points) * (num_points // 12), 3)
    )
    points.append(global_points)
    
    # 合并所有点
    point_cloud = np.vstack(points)
    
    # 随机采样到指定数量
    if len(point_cloud) > num_points:
        indices = np.random.choice(len(point_cloud), num_points, replace=False)
        point_cloud = point_cloud[indices]
    
    return point_cloud

def train_model(model, train_loader, val_loader, epochs=50, device='cuda'):
    """训练模型"""
    
    print(f"🚀 开始训练12关键点模型...")
    print(f"   设备: {device}")
    print(f"   训练样本: {len(train_loader.dataset)}")
    print(f"   验证样本: {len(val_loader.dataset)}")
    print(f"   训练轮数: {epochs}")
    
    model = model.to(device)
    criterion = AnatomicalConstraintLoss()
    optimizer = optim.Adam(model.parameters(), lr=0.0002, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_error': [],
        'val_error': []
    }
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 20
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{epochs} [Train]')
        for batch_pc, batch_kp in train_pbar:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            predicted = model(batch_pc)
            total_loss, mse_loss, dist_loss, angle_loss, sym_loss = criterion(predicted, batch_kp)
            
            # 反向传播
            total_loss.backward()
            optimizer.step()
            
            train_loss += total_loss.item()
            
            # 计算平均距离误差
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
            
            # 更新进度条
            train_pbar.set_postfix({
                'Loss': f'{total_loss.item():.4f}',
                'Error': f'{torch.mean(distances).item():.2f}mm'
            })
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{epochs} [Val]')
            for batch_pc, batch_kp in val_pbar:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                total_loss, _, _, _, _ = criterion(predicted, batch_kp)
                
                val_loss += total_loss.item()
                
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
                
                val_pbar.set_postfix({
                    'Loss': f'{total_loss.item():.4f}',
                    'Error': f'{torch.mean(distances).item():.2f}mm'
                })
        
        # 计算平均损失和误差
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_error'].append(train_error)
        history['val_error'].append(val_error)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            # 保存最佳模型
            torch.save(model.state_dict(), 'best_12point_large_dataset.pth')
        else:
            patience_counter += 1
        
        # 打印进度
        print(f"Epoch {epoch+1:3d}: "
              f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
              f"Train Error: {train_error:.2f}mm, Val Error: {val_error:.2f}mm")
        
        # 早停
        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    print(f"✅ 训练完成！最佳验证损失: {best_val_loss:.6f}")
    
    # 加载最佳模型
    model.load_state_dict(torch.load('best_12point_large_dataset.pth'))
    
    return history

def evaluate_model(model, test_loader, device='cuda'):
    """评估模型"""
    
    print(f"\n🔍 评估12关键点模型性能...")
    
    model = model.to(device)
    model.eval()
    
    total_error = 0.0
    region_errors = {'F1': [], 'F2': [], 'F3': []}
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        test_pbar = tqdm(test_loader, desc='Testing')
        for batch_pc, batch_kp in test_pbar:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            predicted = model(batch_pc)
            
            # 计算整体误差
            distances = torch.norm(predicted - batch_kp, dim=2)
            total_error += torch.mean(distances).item()
            
            # 计算各区域误差
            for i in range(predicted.size(0)):
                pred = predicted[i].cpu().numpy()
                target = batch_kp[i].cpu().numpy()
                
                # F1区域 (0-3)
                f1_distances = np.linalg.norm(pred[0:4] - target[0:4], axis=1)
                region_errors['F1'].extend(f1_distances)
                
                # F2区域 (4-7)
                f2_distances = np.linalg.norm(pred[4:8] - target[4:8], axis=1)
                region_errors['F2'].extend(f2_distances)
                
                # F3区域 (8-11)
                f3_distances = np.linalg.norm(pred[8:12] - target[8:12], axis=1)
                region_errors['F3'].extend(f3_distances)
            
            all_predictions.append(predicted.cpu().numpy())
            all_targets.append(batch_kp.cpu().numpy())
            
            test_pbar.set_postfix({'Error': f'{torch.mean(distances).item():.2f}mm'})
    
    avg_error = total_error / len(test_loader)
    
    print(f"📊 12关键点模型评估结果:")
    print(f"   整体平均误差: {avg_error:.2f}mm")
    
    for region, errors in region_errors.items():
        if errors:
            mean_error = np.mean(errors)
            std_error = np.std(errors)
            max_error = np.max(errors)
            print(f"   {region}区域: {mean_error:.2f}±{std_error:.2f}mm (最大: {max_error:.2f}mm)")
    
    # 计算医疗级准确率
    all_errors = []
    for errors in region_errors.values():
        all_errors.extend(errors)
    
    if all_errors:
        accuracy_5mm = np.mean(np.array(all_errors) < 5.0) * 100
        accuracy_10mm = np.mean(np.array(all_errors) < 10.0) * 100
        
        print(f"   医疗级准确率:")
        print(f"     <5mm: {accuracy_5mm:.1f}%")
        print(f"     <10mm: {accuracy_10mm:.1f}%")
    
    return {
        'avg_error': avg_error,
        'region_errors': region_errors,
        'accuracy_5mm': accuracy_5mm if all_errors else 0,
        'accuracy_10mm': accuracy_10mm if all_errors else 0,
        'predictions': np.vstack(all_predictions),
        'targets': np.vstack(all_targets)
    }

def main():
    """主函数"""
    
    print("🎯 在大数据集上测试12关键点模型")
    print("验证数据集质量和建立性能基准")
    print("=" * 80)
    
    # 检查CUDA
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载数据
    point_clouds, keypoints_12, sample_ids = load_large_dataset()
    
    if point_clouds is None:
        print("❌ 数据加载失败，退出")
        return
    
    # 数据划分
    indices = np.arange(len(point_clouds))
    train_indices, test_indices = train_test_split(
        indices, test_size=0.2, random_state=42
    )
    train_indices, val_indices = train_test_split(
        train_indices, test_size=0.2, random_state=42
    )
    
    # 创建数据集
    train_dataset = KeypointDataset(point_clouds[train_indices], keypoints_12[train_indices])
    val_dataset = KeypointDataset(point_clouds[val_indices], keypoints_12[val_indices])
    test_dataset = KeypointDataset(point_clouds[test_indices], keypoints_12[test_indices])
    
    # 创建数据加载器 (确保batch_size > 1 避免BatchNorm错误)
    train_loader = DataLoader(train_dataset, batch_size=max(2, min(8, len(train_dataset))), shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=max(2, min(8, len(val_dataset))), shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=max(2, min(8, len(test_dataset))), shuffle=False)
    
    print(f"📋 数据划分:")
    print(f"   训练集: {len(train_dataset)} 样本")
    print(f"   验证集: {len(val_dataset)} 样本")
    print(f"   测试集: {len(test_dataset)} 样本")
    
    # 创建模型
    model = FixedMultiModalPointNet(num_points=50000, num_keypoints=12)
    print(f"🤖 模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练模型
    history = train_model(model, train_loader, val_loader, epochs=30, device=device)
    
    # 评估模型
    results = evaluate_model(model, test_loader, device=device)
    
    # 保存结果
    results_summary = {
        'avg_error': results['avg_error'],
        'accuracy_5mm': results['accuracy_5mm'],
        'accuracy_10mm': results['accuracy_10mm'],
        'region_errors': {k: float(np.mean(v)) for k, v in results['region_errors'].items()},
        'training_history': history,
        'dataset_info': {
            'total_samples': len(point_clouds),
            'train_samples': len(train_dataset),
            'val_samples': len(val_dataset),
            'test_samples': len(test_dataset)
        }
    }
    
    with open('12point_large_dataset_results.json', 'w') as f:
        json.dump(results_summary, f, indent=2)
    
    print(f"\n🎉 12关键点模型在大数据集上的测试完成！")
    print(f"📋 生成的文件:")
    print(f"   - best_12point_large_dataset.pth (最佳模型)")
    print(f"   - 12point_large_dataset_results.json (评估结果)")
    
    print(f"\n🎯 12关键点模型性能:")
    print(f"   平均误差: {results['avg_error']:.2f}mm")
    print(f"   医疗级准确率 (<5mm): {results['accuracy_5mm']:.1f}%")
    
    print(f"\n📊 与之前结果对比:")
    print(f"   之前最佳 (男性): 4.84mm")
    print(f"   之前最佳 (女性): 5.64mm")
    print(f"   大数据集结果: {results['avg_error']:.2f}mm")
    
    if results['avg_error'] < 10.0:
        print(f"✅ 12关键点模型在大数据集上表现良好！")
        print(f"💡 这为57点扩展提供了良好的基准")
    else:
        print(f"⚠️ 性能需要进一步优化")

if __name__ == "__main__":
    main()
