#!/usr/bin/env python3
"""
处理原始医学数据
Process Raw Medical Data
从医生标注的原始数据创建高质量训练数据集
"""

import os
import numpy as np
import pandas as pd
import open3d as o3d
from pathlib import Path
import json
import re
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')

class RawMedicalDataProcessor:
    """原始医学数据处理器"""
    
    def __init__(self, data_root="/home/<USER>/pjc/GCN/data/Data"):
        self.data_root = Path(data_root)
        self.annotations_dir = self.data_root / "annotations"
        self.stl_models_dir = self.data_root / "stl_models"
        
        self.processed_data = {}
        self.quality_report = {}
        
    def scan_raw_data(self):
        """扫描原始数据"""
        
        print("🔍 扫描原始医学数据")
        print("=" * 60)
        
        # 扫描标注文件
        annotation_files = list(self.annotations_dir.glob("*.CSV"))
        stl_files = list(self.stl_models_dir.glob("*.stl"))
        
        print(f"📊 发现数据:")
        print(f"   标注文件: {len(annotation_files)}个")
        print(f"   STL模型: {len(stl_files)}个")
        
        # 分析样本ID
        annotation_ids = set()
        stl_ids = set()
        
        for file in annotation_files:
            match = re.match(r'(\d+)-Table-([XYZ]+|LPS)\.CSV', file.name)
            if match:
                sample_id = match.group(1)
                coord_system = match.group(2)
                annotation_ids.add(sample_id)
                print(f"   标注 {sample_id}: {coord_system}坐标系")
        
        for file in stl_files:
            match = re.match(r'(\d+)-F_(\d+)\.stl', file.name)
            if match:
                sample_id = match.group(1)
                part = match.group(2)
                stl_ids.add(sample_id)
        
        # 找到完整的样本（有标注和STL）
        complete_samples = annotation_ids.intersection(stl_ids)
        print(f"\n✅ 完整样本: {len(complete_samples)}个")
        print(f"   样本ID范围: {min(complete_samples)} - {max(complete_samples)}")
        
        return sorted(complete_samples)
    
    def load_annotation_file(self, sample_id):
        """加载标注文件"""
        
        # 尝试不同的坐标系文件
        possible_files = [
            f"{sample_id}-Table-XYZ.CSV",
            f"{sample_id}-Table-LPS.CSV"
        ]
        
        for filename in possible_files:
            file_path = self.annotations_dir / filename
            if file_path.exists():
                try:
                    # 尝试不同编码
                    for encoding in ['gbk', 'utf-8', 'gb2312']:
                        try:
                            df = pd.read_csv(file_path, encoding=encoding)
                            coord_system = 'LPS' if 'LPS' in filename else 'XYZ'
                            return df, coord_system
                        except UnicodeDecodeError:
                            continue
                    
                    print(f"⚠️ 无法读取 {filename} (编码问题)")
                    return None, None
                    
                except Exception as e:
                    print(f"❌ 读取 {filename} 失败: {e}")
                    return None, None
        
        print(f"❌ 未找到样本 {sample_id} 的标注文件")
        return None, None
    
    def load_stl_models(self, sample_id):
        """加载STL模型"""
        
        stl_models = {}
        
        for part in ['1', '2', '3']:  # F_1, F_2, F_3
            stl_file = self.stl_models_dir / f"{sample_id}-F_{part}.stl"
            
            if stl_file.exists():
                try:
                    mesh = o3d.io.read_triangle_mesh(str(stl_file))
                    if len(mesh.vertices) > 0:
                        stl_models[f'F_{part}'] = mesh
                    else:
                        print(f"⚠️ STL文件 {stl_file.name} 为空")
                except Exception as e:
                    print(f"❌ 读取STL文件 {stl_file.name} 失败: {e}")
            else:
                print(f"❌ 未找到STL文件: {stl_file.name}")
        
        return stl_models
    
    def extract_keypoints_by_region(self, df):
        """按区域提取关键点"""
        
        keypoints_by_region = {'F_1': [], 'F_2': [], 'F_3': []}
        
        for _, row in df.iterrows():
            label = row['label']
            x, y, z = row['X'], row['Y'], row['Z']
            
            # 确定区域
            if label.startswith('F_1-'):
                keypoints_by_region['F_1'].append([x, y, z])
            elif label.startswith('F_2-'):
                keypoints_by_region['F_2'].append([x, y, z])
            elif label.startswith('F_3-'):
                keypoints_by_region['F_3'].append([x, y, z])
        
        # 转换为numpy数组
        for region in keypoints_by_region:
            if keypoints_by_region[region]:
                keypoints_by_region[region] = np.array(keypoints_by_region[region])
            else:
                keypoints_by_region[region] = np.empty((0, 3))
        
        return keypoints_by_region
    
    def mesh_to_point_cloud(self, mesh, num_points=4096):
        """将网格转换为点云"""
        
        try:
            # 采样点云
            point_cloud = mesh.sample_points_uniformly(number_of_points=num_points)
            points = np.asarray(point_cloud.points)
            
            if len(points) == 0:
                print(f"⚠️ 点云采样失败")
                return None
            
            return points
            
        except Exception as e:
            print(f"❌ 网格转点云失败: {e}")
            return None
    
    def validate_data_quality(self, sample_id, keypoints_by_region, point_clouds_by_region):
        """验证数据质量"""
        
        quality_issues = []
        quality_scores = {}
        
        for region in ['F_1', 'F_2', 'F_3']:
            if region not in keypoints_by_region or region not in point_clouds_by_region:
                quality_issues.append(f"{region}区域数据缺失")
                continue
            
            keypoints = keypoints_by_region[region]
            point_cloud = point_clouds_by_region[region]
            
            if len(keypoints) == 0:
                quality_issues.append(f"{region}区域无关键点")
                continue
            
            if len(point_cloud) == 0:
                quality_issues.append(f"{region}区域无点云")
                continue
            
            # 计算表面投影质量
            distances = cdist(keypoints, point_cloud)
            min_distances = np.min(distances, axis=1)
            
            avg_distance = np.mean(min_distances)
            within_1mm = np.mean(min_distances < 1.0) * 100
            within_5mm = np.mean(min_distances < 5.0) * 100
            
            quality_scores[region] = {
                'avg_projection_distance': avg_distance,
                'within_1mm_percent': within_1mm,
                'within_5mm_percent': within_5mm,
                'num_keypoints': len(keypoints),
                'num_points': len(point_cloud)
            }
            
            # 质量检查
            if avg_distance > 5.0:
                quality_issues.append(f"{region}区域投影距离过大: {avg_distance:.2f}mm")
            
            if within_5mm < 80:
                quality_issues.append(f"{region}区域投影精度不足: {within_5mm:.1f}%")
        
        return quality_issues, quality_scores
    
    def process_single_sample(self, sample_id):
        """处理单个样本"""
        
        print(f"\n📋 处理样本 {sample_id}")
        print("-" * 40)
        
        # 1. 加载标注
        df, coord_system = self.load_annotation_file(sample_id)
        if df is None:
            return None
        
        print(f"   坐标系: {coord_system}")
        print(f"   关键点数: {len(df)}")
        
        # 2. 提取关键点
        keypoints_by_region = self.extract_keypoints_by_region(df)
        
        for region, kp in keypoints_by_region.items():
            print(f"   {region}: {len(kp)}个关键点")
        
        # 3. 加载STL模型
        stl_models = self.load_stl_models(sample_id)
        if not stl_models:
            print(f"   ❌ 无STL模型")
            return None
        
        # 4. 转换为点云
        point_clouds_by_region = {}
        for region, mesh in stl_models.items():
            point_cloud = self.mesh_to_point_cloud(mesh)
            if point_cloud is not None:
                point_clouds_by_region[region] = point_cloud
                print(f"   {region}点云: {len(point_cloud)}个点")
        
        # 5. 验证数据质量
        quality_issues, quality_scores = self.validate_data_quality(
            sample_id, keypoints_by_region, point_clouds_by_region
        )
        
        if quality_issues:
            print(f"   ⚠️ 质量问题:")
            for issue in quality_issues:
                print(f"      - {issue}")
        else:
            print(f"   ✅ 质量检查通过")
        
        # 6. 组装样本数据
        sample_data = {
            'sample_id': sample_id,
            'coord_system': coord_system,
            'keypoints_by_region': keypoints_by_region,
            'point_clouds_by_region': point_clouds_by_region,
            'quality_issues': quality_issues,
            'quality_scores': quality_scores
        }
        
        return sample_data
    
    def create_unified_dataset(self, processed_samples):
        """创建统一数据集"""
        
        print(f"\n🔧 创建统一数据集")
        print("=" * 60)
        
        # 过滤高质量样本
        high_quality_samples = []
        
        for sample in processed_samples:
            if sample is None:
                continue
            
            # 质量筛选标准
            has_all_regions = all(
                region in sample['point_clouds_by_region'] and 
                region in sample['keypoints_by_region'] and
                len(sample['keypoints_by_region'][region]) > 0
                for region in ['F_1', 'F_2', 'F_3']
            )
            
            has_minimal_quality = len(sample['quality_issues']) <= 2  # 允许少量质量问题
            
            if has_all_regions and has_minimal_quality:
                high_quality_samples.append(sample)
            else:
                print(f"   ❌ 样本{sample['sample_id']}质量不足，跳过")
        
        print(f"✅ 高质量样本: {len(high_quality_samples)}个")
        
        if len(high_quality_samples) == 0:
            print(f"❌ 无高质量样本，无法创建数据集")
            return None
        
        # 统一坐标系
        unified_samples = self.unify_coordinate_systems(high_quality_samples)
        
        # 创建不同格式的数据集
        datasets = self.create_multiple_datasets(unified_samples)
        
        return datasets
    
    def unify_coordinate_systems(self, samples):
        """统一坐标系"""
        
        print(f"\n🔧 统一坐标系")
        print("-" * 40)
        
        # 分析坐标系分布
        coord_systems = {}
        for sample in samples:
            coord_sys = sample['coord_system']
            if coord_sys not in coord_systems:
                coord_systems[coord_sys] = []
            coord_systems[coord_sys].append(sample)
        
        print(f"📊 坐标系分布:")
        for coord_sys, samples_list in coord_systems.items():
            print(f"   {coord_sys}: {len(samples_list)}个样本")
        
        # 选择主要坐标系
        main_coord_system = max(coord_systems.keys(), key=lambda x: len(coord_systems[x]))
        print(f"✅ 主要坐标系: {main_coord_system}")
        
        # 转换所有样本到主要坐标系
        unified_samples = []
        
        for sample in samples:
            if sample['coord_system'] == main_coord_system:
                unified_samples.append(sample)
            else:
                # 这里可以添加坐标系转换逻辑
                # 暂时跳过不同坐标系的样本
                print(f"   ⚠️ 跳过{sample['coord_system']}坐标系样本{sample['sample_id']}")
        
        print(f"✅ 统一后样本: {len(unified_samples)}个")
        return unified_samples
    
    def create_multiple_datasets(self, samples):
        """创建多种格式的数据集"""
        
        print(f"\n🔧 创建多种数据集格式")
        print("-" * 40)
        
        datasets = {}
        
        # 1. 完整57点数据集
        datasets['full_57_points'] = self.create_full_dataset(samples)
        
        # 2. 渐进式数据集
        datasets['progressive'] = self.create_progressive_datasets(samples)
        
        return datasets
    
    def create_full_dataset(self, samples):
        """创建完整57点数据集"""
        
        print(f"   📊 创建完整57点数据集...")
        
        point_clouds = []
        keypoints_57 = []
        sample_ids = []
        
        for sample in samples:
            # 合并三个区域的点云
            combined_pc = np.vstack([
                sample['point_clouds_by_region']['F_1'],
                sample['point_clouds_by_region']['F_2'],
                sample['point_clouds_by_region']['F_3']
            ])
            
            # 合并三个区域的关键点
            combined_kp = np.vstack([
                sample['keypoints_by_region']['F_1'],
                sample['keypoints_by_region']['F_2'],
                sample['keypoints_by_region']['F_3']
            ])
            
            point_clouds.append(combined_pc)
            keypoints_57.append(combined_kp)
            sample_ids.append(sample['sample_id'])
        
        dataset = {
            'point_clouds': np.array(point_clouds),
            'keypoints_57': np.array(keypoints_57),
            'sample_ids': np.array(sample_ids),
            'description': f'完整57点医学数据集，{len(samples)}个样本'
        }
        
        print(f"      ✅ {len(samples)}个样本，57个关键点")
        return dataset
    
    def create_progressive_datasets(self, samples):
        """创建渐进式数据集"""
        
        print(f"   📊 创建渐进式数据集...")
        
        # 定义渐进式关键点选择策略
        progressive_configs = {
            12: "历史最佳12点",
            15: "增加3个F1点",
            19: "完整F3区域",
            24: "增加F2核心点",
            30: "三区域核心点",
            38: "增加边缘点",
            45: "接近完整",
            57: "完整骨盆"
        }
        
        progressive_datasets = {}
        
        for num_points, description in progressive_configs.items():
            # 这里需要根据医学解剖学知识定义关键点选择策略
            # 暂时创建占位符
            progressive_datasets[num_points] = {
                'description': description,
                'num_samples': len(samples),
                'num_points': num_points
            }
            print(f"      📋 {num_points}点: {description}")
        
        return progressive_datasets

def main():
    """主函数"""
    
    print("🏥 原始医学数据处理系统")
    print("从医生标注的原始数据创建高质量训练数据集")
    print("=" * 80)
    
    # 1. 初始化处理器
    processor = RawMedicalDataProcessor()
    
    # 2. 扫描原始数据
    complete_samples = processor.scan_raw_data()
    
    if len(complete_samples) == 0:
        print("❌ 未找到完整样本")
        return
    
    # 3. 处理所有样本
    print(f"\n🔄 处理 {len(complete_samples)} 个样本...")
    
    processed_samples = []
    for sample_id in complete_samples[:10]:  # 先处理前10个样本测试
        sample_data = processor.process_single_sample(sample_id)
        processed_samples.append(sample_data)
    
    # 4. 创建统一数据集
    datasets = processor.create_unified_dataset(processed_samples)
    
    if datasets:
        # 5. 保存数据集
        print(f"\n💾 保存数据集...")
        
        for dataset_name, dataset in datasets.items():
            if dataset_name == 'full_57_points':
                filename = f'raw_medical_dataset_{dataset_name}.npz'
                np.savez_compressed(
                    filename,
                    **dataset
                )
                print(f"   ✅ 保存: {filename}")
        
        # 6. 生成质量报告
        quality_report = {
            'total_samples_scanned': len(complete_samples),
            'samples_processed': len([s for s in processed_samples if s is not None]),
            'datasets_created': list(datasets.keys()),
            'processing_summary': f'成功处理医学原始数据，创建{len(datasets)}种格式数据集'
        }
        
        with open('raw_medical_data_processing_report.json', 'w') as f:
            json.dump(quality_report, f, indent=2, ensure_ascii=False)
        
        print(f"\n🎯 处理完成!")
        print(f"   📊 扫描样本: {len(complete_samples)}个")
        print(f"   ✅ 成功处理: {len([s for s in processed_samples if s is not None])}个")
        print(f"   📦 创建数据集: {len(datasets)}种格式")
        print(f"   💾 质量报告: raw_medical_data_processing_report.json")
    
    else:
        print(f"❌ 数据集创建失败")

if __name__ == "__main__":
    main()
