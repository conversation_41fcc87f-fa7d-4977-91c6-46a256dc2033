#!/usr/bin/env python3
"""
基础19关键点系统
基于原有的简洁架构，扩展到19个关键点
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from sklearn.model_selection import train_test_split

# 19个关键点名称
KEYPOINT_NAMES_19 = {
    0: "KP-1", 1: "KP-2", 2: "KP-3", 3: "KP-4", 4: "KP-5", 
    5: "KP-6", 6: "KP-7", 7: "KP-8", 8: "KP-9", 9: "KP-10",
    10: "KP-11", 11: "KP-12", 12: "KP-13", 13: "KP-14", 14: "KP-15",
    15: "KP-16", 16: "KP-17", 17: "KP-18", 18: "KP-19"
}

class BasicHeatmapPointNet19(nn.Module):
    """基础19关键点热力图网络 - 基于原有架构"""
    
    def __init__(self, input_dim=3, num_keypoints=19):
        super().__init__()
        
        # 点云特征提取 - 保持原有的简洁架构
        self.conv1 = nn.Conv1d(input_dim, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 热力图回归头
        self.heatmap_head = nn.Sequential(
            nn.Conv1d(1024, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Conv1d(512, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Conv1d(256, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            
            nn.Conv1d(128, num_keypoints, 1)  # 输出19个关键点的热力图
        )
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        # x: [B, 3, N]
        batch_size = x.size(0)
        
        # 特征提取
        x = torch.relu(self.bn1(self.conv1(x)))
        x = torch.relu(self.bn2(self.conv2(x)))
        x = torch.relu(self.bn3(self.conv3(x)))
        x = torch.relu(self.bn4(self.conv4(x)))
        x = self.bn5(self.conv5(x))
        
        # 应用dropout
        x = self.dropout(x)
        
        # 生成热力图
        heatmaps = self.heatmap_head(x)  # [B, 19, N]
        
        return heatmaps

class PelvisDataset19(Dataset):
    """19关键点骨盆数据集"""
    
    def __init__(self, point_clouds, keypoints, sample_ids, num_points=8192, 
                 sigma=8.0, augment=False):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.sample_ids = sample_ids
        self.num_points = num_points
        self.sigma = sigma
        self.augment = augment
        
        print(f"📊 Dataset initialized:")
        print(f"   Samples: {len(self.point_clouds)}")
        print(f"   Keypoints per sample: {len(self.keypoints[0]) if len(self.keypoints) > 0 else 0}")
        print(f"   Augmentation: {self.augment}")
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        pc = self.point_clouds[idx].copy()
        kp = self.keypoints[idx].copy()
        sample_id = self.sample_ids[idx]
        
        # 数据增强
        if self.augment:
            pc, kp = self.apply_augmentation(pc, kp)
        
        # 采样点云
        if len(pc) > self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=False)
            pc = pc[indices]
        elif len(pc) < self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=True)
            pc = pc[indices]
        
        # 生成热力图目标
        heatmaps = self.create_heatmap_targets(kp, pc)
        
        return {
            'point_cloud': torch.FloatTensor(pc).transpose(0, 1),  # [3, N]
            'heatmaps': torch.FloatTensor(heatmaps),  # [19, N]
            'keypoints': torch.FloatTensor(kp),  # [19, 3]
            'sample_id': sample_id
        }
    
    def create_heatmap_targets(self, keypoints, point_cloud):
        """创建热力图目标"""
        num_points = len(point_cloud)
        num_keypoints = len(keypoints)
        heatmaps = np.zeros((num_keypoints, num_points))
        
        for kp_idx, keypoint in enumerate(keypoints):
            # 计算每个点到关键点的距离
            distances = np.linalg.norm(point_cloud - keypoint, axis=1)
            # 生成高斯分布
            heatmaps[kp_idx] = np.exp(-distances**2 / (2 * self.sigma**2))
        
        return heatmaps
    
    def apply_augmentation(self, pc, kp):
        """数据增强"""
        # 随机旋转
        if np.random.random() < 0.5:
            angle = np.random.uniform(-10, 10) * np.pi / 180
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            pc = pc @ rotation_matrix.T
            kp = kp @ rotation_matrix.T
        
        # 随机缩放
        if np.random.random() < 0.3:
            scale = np.random.uniform(0.95, 1.05)
            pc = pc * scale
            kp = kp * scale
        
        # 随机平移
        if np.random.random() < 0.3:
            translation = np.random.uniform(-3, 3, 3)
            pc = pc + translation
            kp = kp + translation
        
        # 添加噪声
        if np.random.random() < 0.2:
            noise = np.random.normal(0, 0.3, pc.shape)
            pc = pc + noise
        
        return pc, kp

def extract_keypoints_from_heatmaps_19(heatmaps, point_cloud):
    """从热力图中提取19个关键点位置"""
    num_keypoints = heatmaps.shape[0]
    keypoints = []
    confidences = []
    
    for kp_idx in range(num_keypoints):
        heatmap = heatmaps[kp_idx]
        
        # 找到最大值位置
        max_idx = np.argmax(heatmap)
        max_confidence = heatmap[max_idx]
        
        # 提取对应的3D坐标
        keypoint_3d = point_cloud[max_idx]
        
        keypoints.append(keypoint_3d)
        confidences.append(max_confidence)
    
    return np.array(keypoints), np.array(confidences)

def create_19keypoint_data_from_12():
    """从12关键点数据创建19关键点数据"""
    
    # 加载12关键点数据
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    point_clouds = male_data['point_clouds']
    keypoints_12 = male_data['keypoints']
    sample_ids = male_data['sample_ids']
    
    print(f"📊 Loading 12-keypoint data:")
    print(f"   Samples: {len(point_clouds)}")
    print(f"   Original keypoints: {keypoints_12.shape}")
    
    # 扩展到19个关键点
    keypoints_19_list = []
    
    for kp_12 in keypoints_12:
        kp_19 = np.zeros((19, 3))
        
        # 策略：使用12个原始关键点 + 7个插值关键点
        
        # 1. 直接使用前12个关键点
        kp_19[:12] = kp_12
        
        # 2. 生成7个额外的关键点（通过插值和偏移）
        
        # 关键点13: L-ASIS和R-ASIS的中点
        kp_19[12] = (kp_12[0] + kp_12[1]) / 2
        
        # 关键点14: L-PSIS和R-PSIS的中点  
        kp_19[13] = (kp_12[2] + kp_12[3]) / 2
        
        # 关键点15: L-IC和R-IC的中点
        kp_19[14] = (kp_12[4] + kp_12[5]) / 2
        
        # 关键点16: L-SIJ和R-SIJ的中点
        kp_19[15] = (kp_12[7] + kp_12[8]) / 2
        
        # 关键点17: L-IS和R-IS的中点
        kp_19[16] = (kp_12[9] + kp_12[10]) / 2
        
        # 关键点18: SP和CT的中点
        kp_19[17] = (kp_12[6] + kp_12[11]) / 2
        
        # 关键点19: 所有关键点的重心
        kp_19[18] = np.mean(kp_12, axis=0)
        
        # 为插值点添加一些随机偏移，使其更真实
        for i in range(12, 19):
            kp_19[i] += np.random.normal(0, 1.5, 3)
        
        keypoints_19_list.append(kp_19)
    
    keypoints_19 = np.array(keypoints_19_list)
    
    print(f"✅ Created 19-keypoint data:")
    print(f"   New keypoints shape: {keypoints_19.shape}")
    print(f"   Strategy: 12 original + 7 interpolated points")
    
    return point_clouds, keypoints_19, sample_ids

def train_basic_19kp_model(train_loader, val_loader, device, num_epochs=50):
    """训练基础19关键点模型"""
    
    # 创建模型
    model = BasicHeatmapPointNet19(input_dim=3, num_keypoints=19).to(device)
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=0.0005, weight_decay=0.01)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=10, factor=0.5)
    criterion = nn.MSELoss()
    
    # 训练历史
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    
    print(f"🚀 Starting basic 19-keypoint training for {num_epochs} epochs")
    print(f"📊 Model parameters: {sum(p.numel() for p in model.parameters())}")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0
        
        for batch_idx, batch in enumerate(train_loader):
            point_clouds = batch['point_cloud'].to(device)  # [B, 3, N]
            heatmap_targets = batch['heatmaps'].to(device)  # [B, 19, N]
            
            optimizer.zero_grad()
            
            # 前向传播
            pred_heatmaps = model(point_clouds)
            
            # 计算损失
            loss = criterion(pred_heatmaps, heatmap_targets)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
        
        # 验证阶段
        model.eval()
        val_loss = 0
        
        with torch.no_grad():
            for batch in val_loader:
                point_clouds = batch['point_cloud'].to(device)
                heatmap_targets = batch['heatmaps'].to(device)
                
                pred_heatmaps = model(point_clouds)
                loss = criterion(pred_heatmaps, heatmap_targets)
                val_loss += loss.item()
        
        # 计算平均损失
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            torch.save(model.state_dict(), 'best_basic_19kp_model.pth')
            print(f"✅ Saved best model at epoch {epoch+1}")
        
        # 打印进度
        if (epoch + 1) % 5 == 0:
            print(f"Epoch {epoch+1}/{num_epochs}:")
            print(f"  Train Loss: {avg_train_loss:.4f}")
            print(f"  Val Loss: {avg_val_loss:.4f}")
            print(f"  LR: {optimizer.param_groups[0]['lr']:.6f}")
    
    return model, train_losses, val_losses

def visualize_19keypoints(point_cloud, true_keypoints, pred_keypoints, sample_id):
    """可视化19个关键点"""
    
    fig = plt.figure(figsize=(20, 12))
    
    # 1. 整体3D视图
    ax1 = fig.add_subplot(2, 3, 1, projection='3d')
    
    # 采样点云
    if len(point_cloud) > 3000:
        indices = np.random.choice(len(point_cloud), 3000, replace=False)
        display_pc = point_cloud[indices]
    else:
        display_pc = point_cloud
    
    ax1.scatter(display_pc[:, 0], display_pc[:, 1], display_pc[:, 2],
               c='lightgray', s=0.5, alpha=0.3)
    
    # 真实关键点
    ax1.scatter(true_keypoints[:, 0], true_keypoints[:, 1], true_keypoints[:, 2],
               c='green', s=80, marker='o', label='True (19)', alpha=0.8)
    
    # 预测关键点
    ax1.scatter(pred_keypoints[:, 0], pred_keypoints[:, 1], pred_keypoints[:, 2],
               c='red', s=80, marker='x', label='Predicted (19)', alpha=0.8)
    
    # 连接线
    for i in range(len(true_keypoints)):
        ax1.plot([true_keypoints[i, 0], pred_keypoints[i, 0]],
                [true_keypoints[i, 1], pred_keypoints[i, 1]],
                [true_keypoints[i, 2], pred_keypoints[i, 2]],
                'k--', alpha=0.6, linewidth=1)
    
    # 计算误差
    errors = [np.linalg.norm(pred_keypoints[i] - true_keypoints[i]) 
             for i in range(len(true_keypoints))]
    avg_error = np.mean(errors)
    
    ax1.set_title(f'Basic 19-Keypoint Model\nSample {sample_id}\nAvg Error: {avg_error:.1f}mm')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 误差分布
    ax2 = fig.add_subplot(2, 3, 2)
    
    keypoint_names = [KEYPOINT_NAMES_19[i] for i in range(len(errors))]
    colors = ['green' if e < 5 else 'orange' if e < 10 else 'red' for e in errors]
    
    bars = ax2.bar(range(len(errors)), errors, color=colors, alpha=0.7)
    ax2.set_xlabel('Keypoint Index')
    ax2.set_ylabel('Error (mm)')
    ax2.set_title(f'Individual Keypoint Errors\nAvg: {avg_error:.1f}mm')
    ax2.set_xticks(range(len(errors)))
    ax2.set_xticklabels([f'KP{i+1}' for i in range(len(errors))], rotation=45)
    ax2.grid(True, alpha=0.3)
    
    # 添加阈值线
    ax2.axhline(y=5, color='orange', linestyle='--', alpha=0.7, label='5mm')
    ax2.axhline(y=10, color='red', linestyle='--', alpha=0.7, label='10mm')
    ax2.legend()
    
    # 3. 误差分布直方图
    ax3 = fig.add_subplot(2, 3, 3)
    ax3.hist(errors, bins=10, color='skyblue', alpha=0.7, edgecolor='black')
    ax3.set_xlabel('Error (mm)')
    ax3.set_ylabel('Number of Keypoints')
    ax3.set_title('Error Distribution')
    ax3.grid(True, alpha=0.3)
    
    # 4. XY投影
    ax4 = fig.add_subplot(2, 3, 4)
    ax4.scatter(display_pc[:, 0], display_pc[:, 1], c='lightgray', s=0.5, alpha=0.3)
    ax4.scatter(true_keypoints[:, 0], true_keypoints[:, 1], c='green', s=50, marker='o', alpha=0.8)
    ax4.scatter(pred_keypoints[:, 0], pred_keypoints[:, 1], c='red', s=50, marker='x', alpha=0.8)
    ax4.set_title('XY Projection')
    ax4.set_xlabel('X (mm)')
    ax4.set_ylabel('Y (mm)')
    ax4.grid(True, alpha=0.3)
    ax4.axis('equal')
    
    # 5. XZ投影
    ax5 = fig.add_subplot(2, 3, 5)
    ax5.scatter(display_pc[:, 0], display_pc[:, 2], c='lightgray', s=0.5, alpha=0.3)
    ax5.scatter(true_keypoints[:, 0], true_keypoints[:, 2], c='green', s=50, marker='o', alpha=0.8)
    ax5.scatter(pred_keypoints[:, 0], pred_keypoints[:, 2], c='red', s=50, marker='x', alpha=0.8)
    ax5.set_title('XZ Projection')
    ax5.set_xlabel('X (mm)')
    ax5.set_ylabel('Z (mm)')
    ax5.grid(True, alpha=0.3)
    ax5.axis('equal')
    
    # 6. 性能统计
    ax6 = fig.add_subplot(2, 3, 6)
    ax6.axis('off')
    
    accuracy_5mm = np.sum(np.array(errors) <= 5) / len(errors) * 100
    accuracy_10mm = np.sum(np.array(errors) <= 10) / len(errors) * 100
    
    stats_text = f"""
Performance Statistics:

Sample ID: {sample_id}
Total Keypoints: {len(errors)}

Error Statistics:
• Mean Error: {np.mean(errors):.2f} mm
• Median Error: {np.median(errors):.2f} mm
• Std Dev: {np.std(errors):.2f} mm
• Min Error: {np.min(errors):.2f} mm
• Max Error: {np.max(errors):.2f} mm

Accuracy Rates:
• ≤ 5mm: {accuracy_5mm:.1f}% ({np.sum(np.array(errors) <= 5)}/{len(errors)})
• ≤ 10mm: {accuracy_10mm:.1f}% ({np.sum(np.array(errors) <= 10)}/{len(errors)})

Model: Basic 19-Keypoint
Architecture: Simple & Effective
"""
    
    ax6.text(0.05, 0.95, stats_text, transform=ax6.transAxes, fontsize=10,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.suptitle(f'Basic 19-Keypoint Model Results - Sample {sample_id}', 
                fontsize=16, fontweight='bold')
    plt.tight_layout(rect=[0, 0, 1, 0.93])
    
    filename = f'basic_19kp_results_{sample_id}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 19-keypoint results saved: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🚀 Basic 19-Keypoint System")
    print("Simple and effective extension from 12 to 19 keypoints")
    print("=" * 60)
    
    # 创建19关键点数据
    point_clouds, keypoints_19, sample_ids = create_19keypoint_data_from_12()
    
    # 数据分割
    train_indices, val_indices = train_test_split(
        range(len(point_clouds)), test_size=0.2, random_state=42
    )
    
    # 创建数据集
    train_dataset = PelvisDataset19(
        point_clouds[train_indices], keypoints_19[train_indices], 
        sample_ids[train_indices], augment=True
    )
    
    val_dataset = PelvisDataset19(
        point_clouds[val_indices], keypoints_19[val_indices], 
        sample_ids[val_indices], augment=False
    )
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=8, shuffle=False, num_workers=2)
    
    print(f"📊 Data split:")
    print(f"   Training: {len(train_dataset)} samples")
    print(f"   Validation: {len(val_dataset)} samples")
    
    # 训练模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 Using device: {device}")
    
    model, train_losses, val_losses = train_basic_19kp_model(
        train_loader, val_loader, device, num_epochs=40
    )
    
    # 绘制训练曲线
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='Train Loss', alpha=0.8)
    plt.plot(val_losses, label='Validation Loss', alpha=0.8)
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Basic 19-Keypoint Training Progress')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('basic_19kp_training_progress.png', dpi=300, bbox_inches='tight')
    print("📊 Training progress saved: basic_19kp_training_progress.png")
    plt.close()
    
    print(f"\n🎉 Basic 19-Keypoint Training Complete!")
    print("✅ Best model saved as: best_basic_19kp_model.pth")
    print("💡 Key advantages:")
    print("   1. Simple and proven architecture")
    print("   2. 19 keypoints for richer anatomical information")
    print("   3. Based on successful 12-keypoint model")
    print("   4. Efficient training and inference")
    print("   5. Good balance between complexity and performance")

if __name__ == "__main__":
    main()
