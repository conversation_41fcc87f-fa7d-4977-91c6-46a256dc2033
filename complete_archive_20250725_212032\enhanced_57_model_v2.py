#!/usr/bin/env python3
"""
增强版57点模型 v2
Enhanced 57-point model v2 with advanced optimizations
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import json
from tqdm import tqdm
import random

class DataAugmentation:
    """数据增强类"""
    
    def __init__(self, rotation_range=15, noise_std=0.01, scale_range=0.1):
        self.rotation_range = rotation_range  # 度
        self.noise_std = noise_std
        self.scale_range = scale_range
    
    def random_rotation(self, points, angle_range):
        """随机旋转"""
        # 只在Z轴旋转，保持解剖学合理性
        angle = np.random.uniform(-angle_range, angle_range) * np.pi / 180
        cos_a, sin_a = np.cos(angle), np.sin(angle)
        
        rotation_matrix = np.array([
            [cos_a, -sin_a, 0],
            [sin_a, cos_a, 0],
            [0, 0, 1]
        ])
        
        return points @ rotation_matrix.T
    
    def add_noise(self, points, std):
        """添加高斯噪声"""
        noise = np.random.normal(0, std, points.shape)
        return points + noise
    
    def random_scale(self, points, scale_range):
        """随机缩放"""
        scale = np.random.uniform(1-scale_range, 1+scale_range)
        return points * scale
    
    def augment(self, point_cloud, keypoints):
        """应用数据增强"""
        # 随机决定是否应用每种增强
        if random.random() > 0.5:
            # 旋转 - 同时应用到点云和关键点
            angle = np.random.uniform(-self.rotation_range, self.rotation_range)
            point_cloud = self.random_rotation(point_cloud, angle)
            keypoints = self.random_rotation(keypoints, angle)
        
        if random.random() > 0.5:
            # 缩放 - 同时应用到点云和关键点
            scale = np.random.uniform(1-self.scale_range, 1+self.scale_range)
            point_cloud = point_cloud * scale
            keypoints = keypoints * scale
        
        if random.random() > 0.5:
            # 噪声 - 只应用到点云
            point_cloud = self.add_noise(point_cloud, self.noise_std)
        
        return point_cloud, keypoints

class EnhancedDataset57(Dataset):
    """增强的57点数据集，支持数据增强"""
    
    def __init__(self, point_clouds, keypoints, augment=False):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.augment = augment
        
        if augment:
            self.augmentation = DataAugmentation()
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        pc = self.point_clouds[idx].copy()
        kp = self.keypoints[idx].copy()
        
        if self.augment:
            pc, kp = self.augmentation.augment(pc, kp)
        
        return torch.FloatTensor(pc), torch.FloatTensor(kp)

class RegionWeightedLoss(nn.Module):
    """区域加权损失函数"""
    
    def __init__(self, f1_weight=1.2, f2_weight=1.2, f3_weight=0.8):
        super(RegionWeightedLoss, self).__init__()
        self.f1_weight = f1_weight  # F1区域权重更高
        self.f2_weight = f2_weight  # F2区域权重更高
        self.f3_weight = f3_weight  # F3区域权重较低（已经表现好）
        
    def forward(self, pred, target):
        # 分区域计算损失
        f1_loss = F.mse_loss(pred[:, 0:19], target[:, 0:19]) * self.f1_weight
        f2_loss = F.mse_loss(pred[:, 19:38], target[:, 19:38]) * self.f2_weight
        f3_loss = F.mse_loss(pred[:, 38:57], target[:, 38:57]) * self.f3_weight
        
        return f1_loss + f2_loss + f3_loss

class EnhancedPointNet57(nn.Module):
    """增强版PointNet57 - 更深的网络"""
    
    def __init__(self, num_keypoints=57, dropout_rate=0.2):  # 降低dropout
        super(EnhancedPointNet57, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 优化的特征提取 - 减少参数
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        # 移除conv6以减少内存使用
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 残差连接
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)

        # 优化的回归头 - 减少参数
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, num_keypoints * 3)
        
        # 批归一化
        self.fc_bn1 = nn.BatchNorm1d(512)
        self.fc_bn2 = nn.BatchNorm1d(256)
        self.fc_bn3 = nn.BatchNorm1d(128)
        
        # Dropout
        self.dropout = nn.Dropout(dropout_rate)
        
        # 权重初始化
        self._initialize_weights()
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"🏗️ EnhancedPointNet57: {total_params:,} 参数")
    
    def _initialize_weights(self):
        """改进的权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取 + 残差连接
        x1 = F.relu(self.bn1(self.conv1(x)))           # [B, 64, N]
        x2 = F.relu(self.bn2(self.conv2(x1)))          # [B, 128, N]
        x3 = F.relu(self.bn3(self.conv3(x2)))          # [B, 256, N]
        x3_res = x3 + self.residual1(x1)               # 残差连接

        x4 = F.relu(self.bn4(self.conv4(x3_res)))      # [B, 512, N]
        x4_res = x4 + self.residual2(x2)               # 残差连接

        x5 = F.relu(self.bn5(self.conv5(x4_res)))      # [B, 1024, N]

        # 全局最大池化
        global_feat = torch.max(x5, 2)[0]              # [B, 1024]

        # 回归
        x = F.relu(self.fc_bn1(self.fc1(global_feat)))
        x = self.dropout(x)
        x = F.relu(self.fc_bn2(self.fc2(x)))
        x = self.dropout(x)
        x = F.relu(self.fc_bn3(self.fc3(x)))
        x = self.dropout(x)

        keypoints = self.fc4(x)
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

def train_enhanced_model(model, train_loader, val_loader, epochs=250, device='cuda'):
    """训练增强模型"""
    
    print(f"🚀 训练增强57点模型...")
    print(f"   更深网络 + 数据增强 + 区域加权损失")
    
    model = model.to(device)
    
    # 优化器 - 使用更小的学习率
    optimizer = optim.AdamW(model.parameters(), lr=0.0003, weight_decay=1e-4)
    
    # 学习率调度 - 更激进的调度
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=50, T_mult=2, eta_min=1e-7
    )
    
    # 区域加权损失
    criterion = RegionWeightedLoss(f1_weight=1.3, f2_weight=1.3, f3_weight=0.7)
    
    history = {'train_loss': [], 'val_loss': [], 'train_error': [], 'val_error': []}
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 40  # 更长的耐心
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
            
            optimizer.step()
            
            train_loss += loss.item()
            
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                val_loss += loss.item()
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_error'].append(train_error)
        history['val_error'].append(val_error)
        
        scheduler.step()
        
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_enhanced_57_model_v2.pth')
        else:
            patience_counter += 1
        
        current_lr = optimizer.param_groups[0]['lr']
        
        if epoch % 10 == 0 or epoch < 10:
            print(f"Epoch {epoch+1:3d}: "
                  f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
                  f"Train Error: {train_error:.4f}, Val Error: {val_error:.4f}, "
                  f"LR: {current_lr:.2e}")
        
        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    model.load_state_dict(torch.load('best_enhanced_57_model_v2.pth'))
    return history

def main():
    print("🎯 增强版57点模型 v2")
    print("更深网络 + 数据增强 + 区域加权损失 + 长时间训练")
    print("=" * 80)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载归一化数据
    print("📊 加载归一化数据...")
    data = np.load('unified_pelvis_57_dataset.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints_57 = data['keypoints_57']
    sample_ids = data['sample_ids']
    
    # 数据归一化
    print("🔧 执行数据归一化...")
    normalized_pc = []
    normalized_kp = []
    scalers = []
    
    for i in range(len(point_clouds)):
        pc = point_clouds[i].copy()
        kp = keypoints_57[i].copy()
        
        combined_data = np.vstack([pc, kp])
        scaler = StandardScaler()
        combined_normalized = scaler.fit_transform(combined_data)
        
        pc_normalized = combined_normalized[:len(pc)]
        kp_normalized = combined_normalized[len(pc):]
        
        normalized_pc.append(pc_normalized)
        normalized_kp.append(kp_normalized)
        scalers.append(scaler)
    
    normalized_pc = np.array(normalized_pc)
    normalized_kp = np.array(normalized_kp)
    
    print(f"✅ 数据归一化完成: {len(sample_ids)} 个样本")
    
    # 数据划分
    indices = np.arange(len(normalized_pc))
    train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
    train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
    
    # 创建增强数据集
    train_dataset = EnhancedDataset57(
        normalized_pc[train_indices], 
        normalized_kp[train_indices], 
        augment=True  # 训练时使用数据增强
    )
    val_dataset = EnhancedDataset57(
        normalized_pc[val_indices], 
        normalized_kp[val_indices], 
        augment=False  # 验证时不使用增强
    )
    test_dataset = EnhancedDataset57(
        normalized_pc[test_indices], 
        normalized_kp[test_indices], 
        augment=False
    )
    
    # 数据加载器
    batch_size = 4  # 更小的批次大小节省内存
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    
    print(f"📋 数据划分: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    print(f"   批次大小: {batch_size}, 训练使用数据增强")
    
    # 创建增强模型
    model = EnhancedPointNet57(num_keypoints=57, dropout_rate=0.2)
    
    # 训练模型
    print(f"\n🚀 开始长时间训练...")
    history = train_enhanced_model(model, train_loader, val_loader, epochs=250, device=device)
    
    print(f"\n🎉 增强版v2训练完成！")
    print(f"💡 关键改进:")
    print(f"   ✅ 更深的网络架构 (2048维特征)")
    print(f"   ✅ 数据增强 (旋转、缩放、噪声)")
    print(f"   ✅ 区域加权损失 (重点优化F1、F2)")
    print(f"   ✅ 长时间训练 (250 epochs)")
    print(f"   ✅ 改进的学习率调度")
    
    print(f"\n🚀 下一步: 运行测试评估真实性能")

if __name__ == "__main__":
    main()
