#!/usr/bin/env python3
"""
简化的医生标注质量分析
Simplified Doctor Annotation Quality Analysis
"""

import numpy as np
import os

def load_data():
    """加载数据"""
    
    print("📊 加载数据...")
    
    # 加载女性数据
    female_path = "archive/old_experiments/f3_reduced_12kp_female.npz"
    if os.path.exists(female_path):
        female_data = np.load(female_path, allow_pickle=True)
        female_pc = female_data['point_clouds']
        female_kp = female_data['keypoints']
        print(f"✅ 女性数据: {len(female_pc)}个样本")
    else:
        print(f"❌ 女性数据不存在")
        return None
    
    # 加载男性数据
    male_path = "archive/old_experiments/f3_reduced_12kp_male.npz"
    if os.path.exists(male_path):
        male_data = np.load(male_path, allow_pickle=True)
        male_pc = male_data['point_clouds']
        male_kp = male_data['keypoints']
        print(f"✅ 男性数据: {len(male_pc)}个样本")
    else:
        print(f"❌ 男性数据不存在")
        return None
    
    return female_pc, female_kp, male_pc, male_kp

def analyze_surface_projection_quality(point_clouds, keypoints, gender_name):
    """分析关键点到表面的投影质量"""
    
    print(f"\n🎯 分析{gender_name}关键点表面投影质量")
    print("=" * 50)
    
    all_distances = []
    
    for i in range(len(point_clouds)):
        pc = point_clouds[i]
        kp = keypoints[i]
        
        for j, keypoint in enumerate(kp):
            # 计算关键点到最近表面点的距离
            distances_to_surface = np.linalg.norm(pc - keypoint, axis=1)
            min_distance = np.min(distances_to_surface)
            all_distances.append(min_distance)
    
    all_distances = np.array(all_distances)
    
    # 统计分析
    mean_dist = np.mean(all_distances)
    std_dist = np.std(all_distances)
    median_dist = np.median(all_distances)
    max_dist = np.max(all_distances)
    
    # 质量分布
    excellent = np.sum(all_distances <= 0.5) / len(all_distances) * 100
    good = np.sum(all_distances <= 1.0) / len(all_distances) * 100
    acceptable = np.sum(all_distances <= 2.0) / len(all_distances) * 100
    poor = np.sum(all_distances > 5.0) / len(all_distances) * 100
    
    print(f"📏 {gender_name}表面投影距离统计:")
    print(f"   平均距离: {mean_dist:.3f}mm")
    print(f"   标准差: {std_dist:.3f}mm")
    print(f"   中位数: {median_dist:.3f}mm")
    print(f"   最大距离: {max_dist:.3f}mm")
    
    print(f"\n📊 {gender_name}标注质量分布:")
    print(f"   优秀(≤0.5mm): {excellent:.1f}%")
    print(f"   良好(≤1.0mm): {good:.1f}%")
    print(f"   可接受(≤2.0mm): {acceptable:.1f}%")
    print(f"   较差(>5.0mm): {poor:.1f}%")
    
    return {
        'mean': mean_dist,
        'std': std_dist,
        'median': median_dist,
        'max': max_dist,
        'excellent_rate': excellent,
        'good_rate': good,
        'acceptable_rate': acceptable,
        'poor_rate': poor
    }

def analyze_annotation_consistency(keypoints, gender_name):
    """分析标注一致性"""
    
    print(f"\n🔄 分析{gender_name}标注一致性")
    print("=" * 50)
    
    # 计算每个关键点的变异性
    variations = []
    
    for kp_idx in range(12):
        # 提取所有样本的第kp_idx个关键点
        kp_coords = keypoints[:, kp_idx, :]
        
        # 计算质心
        centroid = np.mean(kp_coords, axis=0)
        
        # 计算到质心的距离
        distances = np.linalg.norm(kp_coords - centroid, axis=1)
        
        # 变异性
        variation = np.std(distances)
        variations.append(variation)
        
        # 确定区域
        if kp_idx < 4:
            region = "F1"
            local_idx = kp_idx + 1
        elif kp_idx < 8:
            region = "F2"
            local_idx = kp_idx - 3
        else:
            region = "F3"
            local_idx = kp_idx - 7
        
        print(f"   {region}-{local_idx}: 变异性 {variation:.2f}mm")
    
    mean_variation = np.mean(variations)
    max_variation = np.max(variations)
    
    print(f"\n📊 {gender_name}整体一致性:")
    print(f"   平均变异性: {mean_variation:.2f}mm")
    print(f"   最大变异性: {max_variation:.2f}mm")
    
    # 一致性等级
    if mean_variation < 5.0:
        level = "优秀"
    elif mean_variation < 10.0:
        level = "良好"
    elif mean_variation < 15.0:
        level = "可接受"
    else:
        level = "较差"
    
    print(f"   一致性等级: {level}")
    
    return {
        'variations': variations,
        'mean_variation': mean_variation,
        'max_variation': max_variation,
        'consistency_level': level
    }

def analyze_symmetry_consistency(keypoints, gender_name):
    """分析F1-F2对称性一致性"""
    
    print(f"\n🔄 分析{gender_name}F1-F2对称性")
    print("=" * 50)
    
    symmetry_stats = []
    
    for i in range(4):
        f1_idx = i
        f2_idx = i + 4
        
        # 计算所有样本的F1-F2距离
        distances = []
        for sample_idx in range(len(keypoints)):
            f1_point = keypoints[sample_idx, f1_idx]
            f2_point = keypoints[sample_idx, f2_idx]
            distance = np.linalg.norm(f1_point - f2_point)
            distances.append(distance)
        
        distances = np.array(distances)
        mean_dist = np.mean(distances)
        std_dist = np.std(distances)
        cv = std_dist / mean_dist if mean_dist > 0 else 0
        
        print(f"   F1-{i+1} <-> F2-{i+1}: {mean_dist:.2f}±{std_dist:.2f}mm (CV:{cv:.3f})")
        
        symmetry_stats.append({
            'pair': f"F1-{i+1}/F2-{i+1}",
            'mean': mean_dist,
            'std': std_dist,
            'cv': cv
        })
    
    # 整体对称性评估
    all_cvs = [stat['cv'] for stat in symmetry_stats]
    mean_cv = np.mean(all_cvs)
    
    print(f"\n📊 {gender_name}对称性一致性:")
    print(f"   平均变异系数: {mean_cv:.3f}")
    
    if mean_cv < 0.15:
        symmetry_level = "优秀"
    elif mean_cv < 0.25:
        symmetry_level = "良好"
    elif mean_cv < 0.35:
        symmetry_level = "可接受"
    else:
        symmetry_level = "较差"
    
    print(f"   对称性等级: {symmetry_level}")
    
    return {
        'symmetry_stats': symmetry_stats,
        'mean_cv': mean_cv,
        'symmetry_level': symmetry_level
    }

def compare_with_model_performance(annotation_quality, model_performance, gender_name):
    """对比标注质量与模型性能"""
    
    print(f"\n🔍 {gender_name}标注质量 vs 模型性能对比")
    print("=" * 50)
    
    # 标注质量指标
    surface_quality = annotation_quality['surface']['mean']
    consistency_quality = annotation_quality['consistency']['mean_variation']
    
    # 模型性能 (使用我们之前的最佳结果)
    if gender_name == "男性":
        best_model_error = 4.84  # 男性相互辅助模型
    else:
        best_model_error = 5.64  # 女性专门优化模型
    
    print(f"📊 {gender_name}质量对比:")
    print(f"   标注表面距离: {surface_quality:.3f}mm")
    print(f"   标注一致性: {consistency_quality:.2f}mm")
    print(f"   最佳模型误差: {best_model_error:.2f}mm")
    
    # 分析关系
    print(f"\n🔍 {gender_name}质量分析:")
    
    if best_model_error > consistency_quality:
        print(f"   ✅ 模型误差({best_model_error:.2f}mm) > 标注变异性({consistency_quality:.2f}mm)")
        print(f"   💡 说明: 模型性能受限于标注一致性，这是合理的")
    else:
        print(f"   ⚠️ 模型误差({best_model_error:.2f}mm) ≤ 标注变异性({consistency_quality:.2f}mm)")
        print(f"   💡 说明: 模型可能过拟合或标注质量有问题")
    
    if surface_quality < 2.0:
        print(f"   ✅ 表面投影质量良好({surface_quality:.3f}mm < 2.0mm)")
    else:
        print(f"   ⚠️ 表面投影质量有待改进({surface_quality:.3f}mm ≥ 2.0mm)")
    
    return {
        'surface_quality': surface_quality,
        'consistency_quality': consistency_quality,
        'model_error': best_model_error,
        'quality_reasonable': best_model_error > consistency_quality
    }

def main():
    """主函数"""
    
    print("🏥 医生标注质量简化分析")
    print("🎯 重点: 评估标注准确性，排除点云变换影响")
    print("=" * 60)
    
    # 加载数据
    data_result = load_data()
    if data_result is None:
        return
    
    female_pc, female_kp, male_pc, male_kp = data_result
    
    results = {}
    
    # 分析女性数据
    print(f"\n👩 女性数据标注质量分析")
    print("="*60)
    
    female_surface = analyze_surface_projection_quality(female_pc, female_kp, "女性")
    female_consistency = analyze_annotation_consistency(female_kp, "女性")
    female_symmetry = analyze_symmetry_consistency(female_kp, "女性")
    
    results['female'] = {
        'surface': female_surface,
        'consistency': female_consistency,
        'symmetry': female_symmetry
    }
    
    # 分析男性数据
    print(f"\n👨 男性数据标注质量分析")
    print("="*60)
    
    male_surface = analyze_surface_projection_quality(male_pc, male_kp, "男性")
    male_consistency = analyze_annotation_consistency(male_kp, "男性")
    male_symmetry = analyze_symmetry_consistency(male_kp, "男性")
    
    results['male'] = {
        'surface': male_surface,
        'consistency': male_consistency,
        'symmetry': male_symmetry
    }
    
    # 对比分析
    print(f"\n" + "="*60)
    print("📋 标注质量综合对比")
    print("="*60)
    
    print(f"\n🎯 表面投影质量对比:")
    print(f"   女性平均距离: {results['female']['surface']['mean']:.3f}mm")
    print(f"   男性平均距离: {results['male']['surface']['mean']:.3f}mm")
    
    print(f"\n🔄 标注一致性对比:")
    print(f"   女性平均变异性: {results['female']['consistency']['mean_variation']:.2f}mm")
    print(f"   男性平均变异性: {results['male']['consistency']['mean_variation']:.2f}mm")
    
    print(f"\n🔄 对称性一致性对比:")
    print(f"   女性对称性CV: {results['female']['symmetry']['mean_cv']:.3f}")
    print(f"   男性对称性CV: {results['male']['symmetry']['mean_cv']:.3f}")
    
    # 与模型性能对比
    female_comparison = compare_with_model_performance(results['female'], None, "女性")
    male_comparison = compare_with_model_performance(results['male'], None, "男性")
    
    # 最终评估
    print(f"\n" + "="*60)
    print("🎉 最终评估结论")
    print("="*60)
    
    print(f"\n📊 标注质量总结:")
    
    # 女性标注质量
    if (results['female']['surface']['mean'] < 2.0 and 
        results['female']['consistency']['mean_variation'] < 15.0):
        female_quality = "良好"
    elif (results['female']['surface']['mean'] < 3.0 and 
          results['female']['consistency']['mean_variation'] < 20.0):
        female_quality = "可接受"
    else:
        female_quality = "需要改进"
    
    # 男性标注质量
    if (results['male']['surface']['mean'] < 2.0 and 
        results['male']['consistency']['mean_variation'] < 15.0):
        male_quality = "良好"
    elif (results['male']['surface']['mean'] < 3.0 and 
          results['male']['consistency']['mean_variation'] < 20.0):
        male_quality = "可接受"
    else:
        male_quality = "需要改进"
    
    print(f"   女性标注质量: {female_quality}")
    print(f"   男性标注质量: {male_quality}")
    
    print(f"\n💡 关键发现:")
    print(f"   1. 标注表面投影距离: 女性{results['female']['surface']['mean']:.3f}mm, 男性{results['male']['surface']['mean']:.3f}mm")
    print(f"   2. 标注一致性变异: 女性{results['female']['consistency']['mean_variation']:.2f}mm, 男性{results['male']['consistency']['mean_variation']:.2f}mm")
    print(f"   3. 模型性能合理性: 女性{female_comparison['quality_reasonable']}, 男性{male_comparison['quality_reasonable']}")
    
    # 保存结果
    np.save('annotation_quality_analysis.npy', results)
    print(f"\n💾 分析结果已保存")
    
    return results

if __name__ == "__main__":
    main()
