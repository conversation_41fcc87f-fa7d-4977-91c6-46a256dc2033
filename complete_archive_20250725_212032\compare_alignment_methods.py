#!/usr/bin/env python3
"""
对比不同对齐方法的效果
Compare different alignment methods
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import json
from tqdm import tqdm

class FixedMultiModalPointNet(nn.Module):
    """已验证的PointNet架构 - 适配57点"""
    
    def __init__(self, num_points=50000, num_keypoints=57):
        super(FixedMultiModalPointNet, self).__init__()
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 点云特征提取器
        self.point_conv1 = nn.Conv1d(3, 64, 1)
        self.point_conv2 = nn.Conv1d(64, 128, 1)
        self.point_conv3 = nn.Conv1d(128, 256, 1)
        self.point_conv4 = nn.Conv1d(256, 512, 1)
        self.point_conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化层
        self.point_bn1 = nn.BatchNorm1d(64)
        self.point_bn2 = nn.BatchNorm1d(128)
        self.point_bn3 = nn.BatchNorm1d(256)
        self.point_bn4 = nn.BatchNorm1d(512)
        self.point_bn5 = nn.BatchNorm1d(1024)
        
        # 全局特征提取
        self.global_conv1 = nn.Conv1d(1024, 512, 1)
        self.global_conv2 = nn.Conv1d(512, 256, 1)
        self.global_bn1 = nn.BatchNorm1d(512)
        self.global_bn2 = nn.BatchNorm1d(256)
        
        # 关键点预测头
        self.keypoint_fc1 = nn.Linear(256, 512)
        self.keypoint_fc2 = nn.Linear(512, 256)
        self.keypoint_fc3 = nn.Linear(256, num_keypoints * 3)
        
        # Dropout层
        self.dropout1 = nn.Dropout(0.3)
        self.dropout2 = nn.Dropout(0.4)
        
    def forward(self, point_cloud):
        """前向传播"""
        batch_size = point_cloud.size(0)
        
        # 点云特征提取
        x = point_cloud.transpose(2, 1)  # [B, 3, N]
        
        x = F.relu(self.point_bn1(self.point_conv1(x)))
        x = F.relu(self.point_bn2(self.point_conv2(x)))
        x = F.relu(self.point_bn3(self.point_conv3(x)))
        x = F.relu(self.point_bn4(self.point_conv4(x)))
        x = F.relu(self.point_bn5(self.point_conv5(x)))
        
        # 全局最大池化
        global_feature = torch.max(x, 2)[0]  # [B, 1024]
        
        # 全局特征处理
        x = global_feature.unsqueeze(2)  # [B, 1024, 1]
        x = F.relu(self.global_bn1(self.global_conv1(x)))
        x = F.relu(self.global_bn2(self.global_conv2(x)))
        x = x.squeeze(2)  # [B, 256]
        
        # 关键点预测
        x = F.relu(self.keypoint_fc1(x))
        x = self.dropout1(x)
        x = F.relu(self.keypoint_fc2(x))
        x = self.dropout2(x)
        keypoints = self.keypoint_fc3(x)
        
        # 重塑为关键点坐标
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

class Dataset57(Dataset):
    """57点数据集"""
    
    def __init__(self, point_clouds, keypoints):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def load_dataset(alignment_method):
    """加载指定对齐方法的数据集"""
    
    if alignment_method == "simple_translation":
        dataset_file = 'unified_pelvis_57_dataset.npz'
        print("📊 加载简单平移对齐数据集...")
    elif alignment_method == "rigid_body":
        dataset_file = 'properly_aligned_57_dataset.npz'
        print("📊 加载刚体变换对齐数据集...")
    else:
        raise ValueError(f"未知的对齐方法: {alignment_method}")
    
    try:
        data = np.load(dataset_file, allow_pickle=True)
        
        point_clouds = data['point_clouds']
        keypoints_57 = data['keypoints_57']
        sample_ids = data['sample_ids']
        
        print(f"✅ {alignment_method}数据加载成功:")
        print(f"   样本数: {len(sample_ids)}")
        print(f"   点云: {point_clouds.shape}")
        print(f"   57关键点: {keypoints_57.shape}")
        
        return point_clouds, keypoints_57, sample_ids
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None, None, None

def quick_train_and_evaluate(point_clouds, keypoints_57, alignment_method, epochs=20):
    """快速训练和评估模型"""
    
    print(f"\n🚀 快速训练 {alignment_method} 模型...")
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 数据划分
    indices = np.arange(len(point_clouds))
    train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
    train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
    
    # 创建数据集
    train_dataset = Dataset57(point_clouds[train_indices], keypoints_57[train_indices])
    val_dataset = Dataset57(point_clouds[val_indices], keypoints_57[val_indices])
    test_dataset = Dataset57(point_clouds[test_indices], keypoints_57[test_indices])
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False, drop_last=True)
    test_loader = DataLoader(test_dataset, batch_size=4, shuffle=False, drop_last=True)
    
    # 创建模型
    model = FixedMultiModalPointNet(num_points=50000, num_keypoints=57).to(device)
    optimizer = optim.Adam(model.parameters(), lr=0.0002, weight_decay=1e-5)
    criterion = nn.MSELoss()
    
    # 快速训练
    best_val_error = float('inf')
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            loss.backward()
            optimizer.step()
            
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        train_error /= len(train_loader)
        
        # 验证
        model.eval()
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        val_error /= len(val_loader)
        
        if val_error < best_val_error:
            best_val_error = val_error
            torch.save(model.state_dict(), f'best_{alignment_method}_model.pth')
        
        if epoch % 5 == 0:
            print(f"  Epoch {epoch:2d}: Train={train_error:.2f}mm, Val={val_error:.2f}mm")
    
    # 加载最佳模型并测试
    model.load_state_dict(torch.load(f'best_{alignment_method}_model.pth'))
    model.eval()
    
    test_error = 0.0
    region_errors = {'F1': [], 'F2': [], 'F3': []}
    
    with torch.no_grad():
        for batch_pc, batch_kp in test_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            predicted = model(batch_pc)
            
            # 整体误差
            distances = torch.norm(predicted - batch_kp, dim=2)
            test_error += torch.mean(distances).item()
            
            # 区域误差
            for i in range(predicted.size(0)):
                pred = predicted[i].cpu().numpy()
                target = batch_kp[i].cpu().numpy()
                
                # F1区域 (0-18)
                f1_distances = np.linalg.norm(pred[0:19] - target[0:19], axis=1)
                region_errors['F1'].extend(f1_distances)
                
                # F2区域 (19-37)
                f2_distances = np.linalg.norm(pred[19:38] - target[19:38], axis=1)
                region_errors['F2'].extend(f2_distances)
                
                # F3区域 (38-56)
                f3_distances = np.linalg.norm(pred[38:57] - target[38:57], axis=1)
                region_errors['F3'].extend(f3_distances)
    
    test_error /= len(test_loader)
    
    # 计算医疗级准确率
    all_errors = []
    for errors in region_errors.values():
        all_errors.extend(errors)
    
    accuracy_5mm = np.mean(np.array(all_errors) < 5.0) * 100 if all_errors else 0
    accuracy_10mm = np.mean(np.array(all_errors) < 10.0) * 100 if all_errors else 0
    
    results = {
        'alignment_method': alignment_method,
        'test_error': test_error,
        'region_errors': {k: np.mean(v) for k, v in region_errors.items()},
        'accuracy_5mm': accuracy_5mm,
        'accuracy_10mm': accuracy_10mm
    }
    
    print(f"✅ {alignment_method} 模型结果:")
    print(f"   测试误差: {test_error:.2f}mm")
    print(f"   F1区域: {np.mean(region_errors['F1']):.2f}mm")
    print(f"   F2区域: {np.mean(region_errors['F2']):.2f}mm")
    print(f"   F3区域: {np.mean(region_errors['F3']):.2f}mm")
    print(f"   <5mm准确率: {accuracy_5mm:.1f}%")
    print(f"   <10mm准确率: {accuracy_10mm:.1f}%")
    
    return results

def main():
    """主函数"""
    
    print("🎯 对比不同对齐方法的效果")
    print("简单平移 vs 刚体变换")
    print("=" * 80)
    
    results = {}
    
    # 测试简单平移对齐
    print("\n📋 测试1: 简单平移对齐")
    print("-" * 40)
    
    pc1, kp1, ids1 = load_dataset("simple_translation")
    if pc1 is not None:
        results['simple_translation'] = quick_train_and_evaluate(pc1, kp1, "simple_translation")
    
    # 测试刚体变换对齐
    print("\n📋 测试2: 刚体变换对齐")
    print("-" * 40)
    
    pc2, kp2, ids2 = load_dataset("rigid_body")
    if pc2 is not None:
        results['rigid_body'] = quick_train_and_evaluate(pc2, kp2, "rigid_body")
    
    # 对比结果
    print("\n📊 对齐方法对比结果")
    print("=" * 80)
    
    if len(results) == 2:
        simple = results['simple_translation']
        rigid = results['rigid_body']
        
        print(f"方法对比:")
        print(f"                    简单平移    刚体变换    改进")
        print(f"  整体误差:        {simple['test_error']:6.2f}mm   {rigid['test_error']:6.2f}mm   {((simple['test_error']-rigid['test_error'])/simple['test_error']*100):+5.1f}%")
        print(f"  F1区域误差:      {simple['region_errors']['F1']:6.2f}mm   {rigid['region_errors']['F1']:6.2f}mm   {((simple['region_errors']['F1']-rigid['region_errors']['F1'])/simple['region_errors']['F1']*100):+5.1f}%")
        print(f"  F2区域误差:      {simple['region_errors']['F2']:6.2f}mm   {rigid['region_errors']['F2']:6.2f}mm   {((simple['region_errors']['F2']-rigid['region_errors']['F2'])/simple['region_errors']['F2']*100):+5.1f}%")
        print(f"  F3区域误差:      {simple['region_errors']['F3']:6.2f}mm   {rigid['region_errors']['F3']:6.2f}mm   {((simple['region_errors']['F3']-rigid['region_errors']['F3'])/simple['region_errors']['F3']*100):+5.1f}%")
        print(f"  <5mm准确率:      {simple['accuracy_5mm']:6.1f}%    {rigid['accuracy_5mm']:6.1f}%    {(rigid['accuracy_5mm']-simple['accuracy_5mm']):+5.1f}%")
        print(f"  <10mm准确率:     {simple['accuracy_10mm']:6.1f}%    {rigid['accuracy_10mm']:6.1f}%    {(rigid['accuracy_10mm']-simple['accuracy_10mm']):+5.1f}%")
        
        # 结论
        if rigid['test_error'] < simple['test_error']:
            improvement = (simple['test_error'] - rigid['test_error']) / simple['test_error'] * 100
            print(f"\n✅ 刚体变换对齐更好，误差降低 {improvement:.1f}%")
        else:
            degradation = (rigid['test_error'] - simple['test_error']) / simple['test_error'] * 100
            print(f"\n⚠️ 简单平移对齐更好，刚体变换增加误差 {degradation:.1f}%")
        
        # 保存对比结果
        comparison_results = {
            'simple_translation': simple,
            'rigid_body': rigid,
            'comparison_summary': {
                'better_method': 'rigid_body' if rigid['test_error'] < simple['test_error'] else 'simple_translation',
                'error_difference': abs(rigid['test_error'] - simple['test_error']),
                'relative_improvement': ((simple['test_error'] - rigid['test_error']) / simple['test_error'] * 100)
            }
        }
        
        with open('alignment_comparison_results.json', 'w') as f:
            json.dump(comparison_results, f, indent=2)
        
        print(f"\n💾 对比结果已保存: alignment_comparison_results.json")
    
    else:
        print("❌ 无法完成对比，数据加载失败")

if __name__ == "__main__":
    main()
