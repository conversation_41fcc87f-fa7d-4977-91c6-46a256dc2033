#!/usr/bin/env python3
"""
优化的小样本学习最终版本 - 97样本医疗关键点检测
Optimized Few-Shot Learning Final Version for 97 Medical Samples
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import random
from pathlib import Path
import json
from datetime import datetime
from sklearn.model_selection import train_test_split

class OptimizedKeypointNet(nn.Module):
    """优化的关键点检测网络"""
    
    def __init__(self, input_dim=3, hidden_dim=256, output_dim=19*3, dropout=0.2):
        super().__init__()
        
        # 点云特征提取器 (PointNet风格)
        self.point_conv1 = nn.Conv1d(input_dim, 64, 1)
        self.point_conv2 = nn.Conv1d(64, 128, 1)
        self.point_conv3 = nn.Conv1d(128, hidden_dim, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(hidden_dim)
        
        # 全局特征处理
        self.global_conv1 = nn.Linear(hidden_dim, 512)
        self.global_conv2 = nn.Linear(512, 256)
        self.global_conv3 = nn.Linear(256, output_dim)
        
        self.global_bn1 = nn.BatchNorm1d(512)
        self.global_bn2 = nn.BatchNorm1d(256)
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, point_cloud):
        """
        Args:
            point_cloud: (B, N, 3) 点云数据
        Returns:
            keypoints: (B, 19, 3) 预测的关键点
        """
        batch_size = point_cloud.size(0)
        
        # 转换为 (B, 3, N) 用于1D卷积
        x = point_cloud.transpose(2, 1)  # (B, 3, N)
        
        # 点级特征提取
        x = F.relu(self.bn1(self.point_conv1(x)))
        x = F.relu(self.bn2(self.point_conv2(x)))
        x = F.relu(self.bn3(self.point_conv3(x)))
        
        # 全局最大池化
        global_feature = torch.max(x, 2)[0]  # (B, hidden_dim)
        
        # 全局特征处理
        x = F.relu(self.global_bn1(self.global_conv1(global_feature)))
        x = self.dropout(x)
        x = F.relu(self.global_bn2(self.global_conv2(x)))
        x = self.dropout(x)
        x = self.global_conv3(x)
        
        # 重塑为关键点
        keypoints = x.view(batch_size, 19, 3)
        
        return keypoints

class SmartDataAugmentation:
    """智能医疗数据增强"""
    
    def __init__(self):
        self.rotation_range = 0.1  # ±5.7度
        self.scale_range = (0.98, 1.02)
        self.noise_std = 0.01
        
    def augment_sample(self, point_cloud, keypoints):
        """单样本增强"""
        pc = point_cloud.copy()
        kp = keypoints.copy()
        
        # 1. 旋转 (医疗合理范围)
        if np.random.random() < 0.8:
            angle = np.random.uniform(-self.rotation_range, self.rotation_range)
            pc, kp = self.rotate(pc, kp, angle)
        
        # 2. 缩放 (个体差异)
        if np.random.random() < 0.7:
            scale = np.random.uniform(*self.scale_range)
            pc *= scale
            kp *= scale
        
        # 3. 噪声 (扫描噪声)
        if np.random.random() < 0.6:
            noise = np.random.normal(0, self.noise_std, pc.shape)
            pc += noise
        
        # 4. 平移 (位置变化)
        if np.random.random() < 0.5:
            translation = np.random.uniform(-0.05, 0.05, 3)
            pc += translation
            kp += translation
        
        return pc, kp
    
    def rotate(self, pc, kp, angle):
        """旋转变换"""
        cos_a, sin_a = np.cos(angle), np.sin(angle)
        rotation_matrix = np.array([
            [cos_a, -sin_a, 0],
            [sin_a, cos_a, 0],
            [0, 0, 1]
        ])
        return pc @ rotation_matrix.T, kp @ rotation_matrix.T

class OptimizedFewShotTrainer:
    """优化的小样本学习训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        self.augmentation = SmartDataAugmentation()
        
    def load_data(self, data_path='data/raw/high_quality_f3_dataset.npz'):
        """加载和预处理数据"""
        print(f"📦 加载数据: {data_path}")
        
        data = np.load(data_path, allow_pickle=True)
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        # 下采样到4096点
        processed_pcs = []
        for pc in point_clouds:
            if len(pc) > 4096:
                indices = np.random.choice(len(pc), 4096, replace=False)
                pc_sampled = pc[indices]
            else:
                # 如果点数不足，重复采样
                indices = np.random.choice(len(pc), 4096, replace=True)
                pc_sampled = pc[indices]
            processed_pcs.append(pc_sampled)
        
        point_clouds = np.array(processed_pcs)
        
        # 数据标准化
        for i in range(len(point_clouds)):
            pc = point_clouds[i]
            kp = keypoints[i]
            
            # 中心化
            center = np.mean(pc, axis=0)
            pc -= center
            kp -= center
            
            # 缩放到单位球
            scale = np.max(np.linalg.norm(pc, axis=1))
            if scale > 0:
                pc /= scale
                kp /= scale
            
            point_clouds[i] = pc
            keypoints[i] = kp
        
        # 数据划分
        indices = np.arange(len(sample_ids))
        train_val_indices, test_indices = train_test_split(
            indices, test_size=0.15, random_state=42
        )
        train_indices, val_indices = train_test_split(
            train_val_indices, test_size=0.18, random_state=42
        )
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
        
        print(f"✅ 数据准备完成: 训练{len(train_indices)}, 验证{len(val_indices)}, 测试{len(test_indices)}")
        return self.data
    
    def train_model(self, k_shot, epochs=200, lr=0.001):
        """训练模型"""
        print(f"\n🎯 训练 {k_shot}-shot 模型")
        
        # 创建模型
        model = OptimizedKeypointNet().to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        patience = 0
        max_patience = 30
        
        for epoch in range(epochs):
            model.train()
            
            # 采样训练数据
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                min(k_shot, len(self.data['train']['point_clouds'])), 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 数据增强
            aug_pcs = []
            aug_kps = []
            
            # 原始数据
            for pc, kp in zip(train_pcs, train_kps):
                aug_pcs.append(pc)
                aug_kps.append(kp)
                
                # 增强数据
                for _ in range(3):  # 每个样本生成3个增强版本
                    aug_pc, aug_kp = self.augmentation.augment_sample(pc, kp)
                    aug_pcs.append(aug_pc)
                    aug_kps.append(aug_kp)
            
            # 转换为tensor
            aug_pcs = torch.FloatTensor(np.stack(aug_pcs)).to(self.device)
            aug_kps = torch.FloatTensor(np.stack(aug_kps)).to(self.device)
            
            # 训练步骤
            optimizer.zero_grad()
            pred_kps = model(aug_pcs)
            loss = criterion(pred_kps, aug_kps)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            scheduler.step()
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_model(model, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                    patience = 0
                else:
                    patience += 1
                
                print(f"Epoch {epoch:3d}: Loss={loss:.4f}, Val_Error={val_error:.2f}mm, LR={optimizer.param_groups[0]['lr']:.6f}")
                
                if patience >= max_patience:
                    print(f"早停在epoch {epoch}")
                    break
        
        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def evaluate_model(self, model, split='test'):
        """评估模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for pc, kp in zip(pcs, kps):
                pc_tensor = torch.FloatTensor(pc).unsqueeze(0).to(self.device)
                kp_tensor = torch.FloatTensor(kp).unsqueeze(0).to(self.device)
                
                pred_kp = model(pc_tensor)
                error = torch.mean(torch.norm(pred_kp - kp_tensor, dim=2))
                total_error += error.item()
                num_samples += 1
        
        return total_error / num_samples if num_samples > 0 else float('inf')

def run_optimized_experiment():
    """运行优化的小样本学习实验"""
    print("🚀 优化小样本学习实验")
    print("=" * 50)
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    random.seed(42)
    
    # 初始化训练器
    trainer = OptimizedFewShotTrainer()
    
    # 加载数据
    data = trainer.load_data()
    
    # 实验配置
    shot_configs = [3, 5, 10, 15, 20]  # 跳过1-shot，从3-shot开始
    results = {}
    
    for k_shot in shot_configs:
        print(f"\n{'='*60}")
        
        # 训练模型
        model, val_error = trainer.train_model(k_shot, epochs=150, lr=0.002)
        
        # 测试评估
        test_error = trainer.evaluate_model(model, 'test')
        results[k_shot] = test_error
        
        print(f"✅ {k_shot}-shot 最终测试误差: {test_error:.2f}mm")
    
    # 结果分析
    print(f"\n📊 优化实验结果总结")
    print("=" * 50)
    baseline_error = 7.5  # 基于之前的结果调整基线
    
    for k_shot, error in results.items():
        improvement = (baseline_error - error) / baseline_error * 100
        medical_grade = "✅" if error < 5.0 else "❌"
        print(f"{k_shot:2d}-shot: {error:6.2f}mm (改进: {improvement:+5.1f}%) {medical_grade}")
    
    # 找出最佳配置
    best_shot = min(results.keys(), key=lambda k: results[k])
    best_error = results[best_shot]
    
    print(f"\n🏆 最佳配置: {best_shot}-shot")
    print(f"🎯 最佳误差: {best_error:.2f}mm")
    
    if best_error < 2.0:
        print("🎉 达到医疗级精度 (<2mm)!")
    elif best_error < 5.0:
        print("👍 接近医疗应用水平 (<5mm)")
    else:
        print("📈 仍需进一步优化")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = Path("results/few_shot_experiments")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    result_data = {
        "experiment_type": "optimized_few_shot",
        "timestamp": timestamp,
        "results": results,
        "best_configuration": {
            "k_shot": best_shot,
            "error_mm": best_error
        },
        "dataset_info": {
            "total_samples": 97,
            "train_samples": len(data['train']['point_clouds']),
            "val_samples": len(data['val']['point_clouds']),
            "test_samples": len(data['test']['point_clouds'])
        }
    }
    
    with open(results_dir / f"optimized_few_shot_results_{timestamp}.json", 'w') as f:
        json.dump(result_data, f, indent=2)
    
    print(f"\n💾 结果已保存到: results/few_shot_experiments/")
    
    return results

if __name__ == "__main__":
    results = run_optimized_experiment()
