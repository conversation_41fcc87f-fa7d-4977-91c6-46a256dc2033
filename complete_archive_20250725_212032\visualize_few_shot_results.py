#!/usr/bin/env python3
"""
可视化小样本学习结果 - 验证预测精度
Visualize Few-Shot Learning Results - Verify Prediction Accuracy
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json
from pathlib import Path
from sklearn.model_selection import train_test_split

# 导入优化的网络架构
class OptimizedKeypointNet(nn.Module):
    """优化的关键点检测网络"""
    
    def __init__(self, input_dim=3, hidden_dim=256, output_dim=19*3, dropout=0.2):
        super().__init__()
        
        # 点云特征提取器 (PointNet风格)
        self.point_conv1 = nn.Conv1d(input_dim, 64, 1)
        self.point_conv2 = nn.Conv1d(64, 128, 1)
        self.point_conv3 = nn.Conv1d(128, hidden_dim, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(hidden_dim)
        
        # 全局特征处理
        self.global_conv1 = nn.Linear(hidden_dim, 512)
        self.global_conv2 = nn.Linear(512, 256)
        self.global_conv3 = nn.Linear(256, output_dim)
        
        self.global_bn1 = nn.BatchNorm1d(512)
        self.global_bn2 = nn.BatchNorm1d(256)
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, point_cloud):
        batch_size = point_cloud.size(0)
        x = point_cloud.transpose(2, 1)
        
        x = torch.relu(self.bn1(self.point_conv1(x)))
        x = torch.relu(self.bn2(self.point_conv2(x)))
        x = torch.relu(self.bn3(self.point_conv3(x)))
        
        global_feature = torch.max(x, 2)[0]
        
        x = torch.relu(self.global_bn1(self.global_conv1(global_feature)))
        x = self.dropout(x)
        x = torch.relu(self.global_bn2(self.global_conv2(x)))
        x = self.dropout(x)
        x = self.global_conv3(x)
        
        keypoints = x.view(batch_size, 19, 3)
        return keypoints

class FewShotResultVisualizer:
    """小样本学习结果可视化器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def load_and_prepare_data(self, data_path='data/raw/high_quality_f3_dataset.npz'):
        """加载和预处理数据 - 与训练时完全一致"""
        print(f"📦 加载数据: {data_path}")
        
        data = np.load(data_path, allow_pickle=True)
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        print(f"原始数据范围:")
        print(f"  点云: [{np.min(point_clouds):.3f}, {np.max(point_clouds):.3f}]")
        print(f"  关键点: [{np.min(keypoints):.3f}, {np.max(keypoints):.3f}]")
        
        # 下采样到4096点
        processed_pcs = []
        for pc in point_clouds:
            if len(pc) > 4096:
                indices = np.random.choice(len(pc), 4096, replace=False)
                pc_sampled = pc[indices]
            else:
                indices = np.random.choice(len(pc), 4096, replace=True)
                pc_sampled = pc[indices]
            processed_pcs.append(pc_sampled)
        
        point_clouds = np.array(processed_pcs)
        
        # 数据标准化 - 与训练时完全一致
        original_scales = []
        original_centers = []
        
        for i in range(len(point_clouds)):
            pc = point_clouds[i]
            kp = keypoints[i]
            
            # 记录原始中心和尺度
            center = np.mean(pc, axis=0)
            original_centers.append(center)
            
            # 中心化
            pc -= center
            kp -= center
            
            # 缩放到单位球
            scale = np.max(np.linalg.norm(pc, axis=1))
            original_scales.append(scale)
            
            if scale > 0:
                pc /= scale
                kp /= scale
            
            point_clouds[i] = pc
            keypoints[i] = kp
        
        print(f"标准化后数据范围:")
        print(f"  点云: [{np.min(point_clouds):.3f}, {np.max(point_clouds):.3f}]")
        print(f"  关键点: [{np.min(keypoints):.3f}, {np.max(keypoints):.3f}]")
        
        # 数据划分 - 与训练时完全一致
        indices = np.arange(len(sample_ids))
        train_val_indices, test_indices = train_test_split(
            indices, test_size=0.15, random_state=42
        )
        train_indices, val_indices = train_test_split(
            train_val_indices, test_size=0.18, random_state=42
        )
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices],
                'sample_ids': sample_ids[train_indices],
                'scales': [original_scales[i] for i in train_indices],
                'centers': [original_centers[i] for i in train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices],
                'sample_ids': sample_ids[val_indices],
                'scales': [original_scales[i] for i in val_indices],
                'centers': [original_centers[i] for i in val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices],
                'sample_ids': sample_ids[test_indices],
                'scales': [original_scales[i] for i in test_indices],
                'centers': [original_centers[i] for i in test_indices]
            }
        }
        
        print(f"✅ 数据准备完成: 训练{len(train_indices)}, 验证{len(val_indices)}, 测试{len(test_indices)}")
        return self.data
    
    def train_and_evaluate_model(self, k_shot=20):
        """快速训练一个模型用于可视化"""
        print(f"\n🎯 训练 {k_shot}-shot 模型用于可视化")
        
        # 创建模型
        model = OptimizedKeypointNet().to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=0.002, weight_decay=1e-4)
        criterion = nn.MSELoss()
        
        # 简化训练 - 只训练50个epoch用于快速验证
        epochs = 50
        
        for epoch in range(epochs):
            model.train()
            
            # 采样训练数据
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                min(k_shot, len(self.data['train']['point_clouds'])), 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 转换为tensor
            train_pcs = torch.FloatTensor(train_pcs).to(self.device)
            train_kps = torch.FloatTensor(train_kps).to(self.device)
            
            # 训练步骤
            optimizer.zero_grad()
            pred_kps = model(train_pcs)
            loss = criterion(pred_kps, train_kps)
            loss.backward()
            optimizer.step()
            
            if epoch % 10 == 0:
                print(f"Epoch {epoch}: Loss={loss:.6f}")
        
        return model
    
    def calculate_real_world_errors(self, model, split='test', num_samples=5):
        """计算真实世界的误差"""
        model.eval()
        
        pcs = self.data[split]['point_clouds'][:num_samples]
        kps = self.data[split]['keypoints'][:num_samples]
        scales = self.data[split]['scales'][:num_samples]
        centers = self.data[split]['centers'][:num_samples]
        sample_ids = self.data[split]['sample_ids'][:num_samples]
        
        results = []
        
        with torch.no_grad():
            for i, (pc, kp, scale, center, sample_id) in enumerate(zip(pcs, kps, scales, centers, sample_ids)):
                # 预测
                pc_tensor = torch.FloatTensor(pc).unsqueeze(0).to(self.device)
                pred_kp = model(pc_tensor).cpu().numpy()[0]
                
                # 反标准化到原始尺度
                pred_kp_real = pred_kp * scale + center
                kp_real = kp * scale + center
                
                # 计算误差
                errors = np.linalg.norm(pred_kp_real - kp_real, axis=1)
                mean_error = np.mean(errors)
                
                results.append({
                    'sample_id': sample_id,
                    'pred_keypoints': pred_kp_real,
                    'true_keypoints': kp_real,
                    'point_cloud': pc * scale + center,  # 反标准化点云
                    'errors': errors,
                    'mean_error': mean_error,
                    'normalized_pred': pred_kp,
                    'normalized_true': kp,
                    'normalized_pc': pc
                })
                
                print(f"样本 {sample_id}: 平均误差 = {mean_error:.2f}mm")
                print(f"  误差范围: [{np.min(errors):.2f}, {np.max(errors):.2f}]mm")
        
        return results
    
    def visualize_predictions(self, results, save_plots=True):
        """可视化预测结果"""
        print(f"\n📊 可视化预测结果")
        
        for i, result in enumerate(results):
            fig = plt.figure(figsize=(15, 10))
            
            # 3D可视化
            ax1 = fig.add_subplot(221, projection='3d')
            
            # 绘制点云
            pc = result['point_cloud']
            ax1.scatter(pc[:, 0], pc[:, 1], pc[:, 2], 
                       c='lightgray', s=1, alpha=0.3, label='点云')
            
            # 绘制真实关键点
            true_kp = result['true_keypoints']
            ax1.scatter(true_kp[:, 0], true_kp[:, 1], true_kp[:, 2], 
                       c='red', s=50, label='真实关键点', marker='o')
            
            # 绘制预测关键点
            pred_kp = result['pred_keypoints']
            ax1.scatter(pred_kp[:, 0], pred_kp[:, 1], pred_kp[:, 2], 
                       c='blue', s=50, label='预测关键点', marker='^')
            
            # 绘制连接线显示误差
            for j in range(len(true_kp)):
                ax1.plot([true_kp[j, 0], pred_kp[j, 0]], 
                        [true_kp[j, 1], pred_kp[j, 1]], 
                        [true_kp[j, 2], pred_kp[j, 2]], 
                        'k--', alpha=0.5, linewidth=1)
            
            ax1.set_title(f'样本 {result["sample_id"]} - 平均误差: {result["mean_error"]:.2f}mm')
            ax1.legend()
            ax1.set_xlabel('X')
            ax1.set_ylabel('Y')
            ax1.set_zlabel('Z')
            
            # 误差分布
            ax2 = fig.add_subplot(222)
            errors = result['errors']
            ax2.bar(range(len(errors)), errors)
            ax2.set_title(f'各关键点误差分布')
            ax2.set_xlabel('关键点索引')
            ax2.set_ylabel('误差 (mm)')
            ax2.grid(True, alpha=0.3)
            
            # 标准化空间的对比
            ax3 = fig.add_subplot(223, projection='3d')
            norm_pc = result['normalized_pc']
            norm_true = result['normalized_true']
            norm_pred = result['normalized_pred']
            
            ax3.scatter(norm_pc[:, 0], norm_pc[:, 1], norm_pc[:, 2], 
                       c='lightgray', s=1, alpha=0.3)
            ax3.scatter(norm_true[:, 0], norm_true[:, 1], norm_true[:, 2], 
                       c='red', s=30, marker='o')
            ax3.scatter(norm_pred[:, 0], norm_pred[:, 1], norm_pred[:, 2], 
                       c='blue', s=30, marker='^')
            ax3.set_title('标准化空间中的预测')
            
            # 误差统计
            ax4 = fig.add_subplot(224)
            ax4.text(0.1, 0.8, f'样本ID: {result["sample_id"]}', fontsize=12)
            ax4.text(0.1, 0.7, f'平均误差: {result["mean_error"]:.3f} mm', fontsize=12)
            ax4.text(0.1, 0.6, f'最大误差: {np.max(errors):.3f} mm', fontsize=12)
            ax4.text(0.1, 0.5, f'最小误差: {np.min(errors):.3f} mm', fontsize=12)
            ax4.text(0.1, 0.4, f'标准差: {np.std(errors):.3f} mm', fontsize=12)
            ax4.text(0.1, 0.3, f'<1mm的点: {np.sum(errors < 1.0)}/19', fontsize=12)
            ax4.text(0.1, 0.2, f'<5mm的点: {np.sum(errors < 5.0)}/19', fontsize=12)
            ax4.set_xlim(0, 1)
            ax4.set_ylim(0, 1)
            ax4.set_title('误差统计')
            ax4.axis('off')
            
            plt.tight_layout()
            
            if save_plots:
                plots_dir = Path("results/few_shot_visualizations")
                plots_dir.mkdir(parents=True, exist_ok=True)
                plt.savefig(plots_dir / f"sample_{result['sample_id']}_prediction.png", 
                           dpi=150, bbox_inches='tight')
                print(f"保存图片: sample_{result['sample_id']}_prediction.png")
            
            plt.show()

def main():
    """主函数"""
    print("🔍 小样本学习结果验证和可视化")
    print("=" * 60)
    
    # 创建可视化器
    visualizer = FewShotResultVisualizer()
    
    # 加载数据
    data = visualizer.load_and_prepare_data()
    
    # 训练模型
    model = visualizer.train_and_evaluate_model(k_shot=20)
    
    # 计算真实误差
    print(f"\n📏 计算真实世界误差...")
    results = visualizer.calculate_real_world_errors(model, split='test', num_samples=5)
    
    # 总体统计
    all_errors = []
    for result in results:
        all_errors.extend(result['errors'])
    
    print(f"\n📊 总体误差统计:")
    print(f"  平均误差: {np.mean(all_errors):.2f} mm")
    print(f"  中位数误差: {np.median(all_errors):.2f} mm")
    print(f"  标准差: {np.std(all_errors):.2f} mm")
    print(f"  最大误差: {np.max(all_errors):.2f} mm")
    print(f"  最小误差: {np.min(all_errors):.2f} mm")
    print(f"  <1mm的点: {np.sum(np.array(all_errors) < 1.0)}/{len(all_errors)} ({np.sum(np.array(all_errors) < 1.0)/len(all_errors)*100:.1f}%)")
    print(f"  <5mm的点: {np.sum(np.array(all_errors) < 5.0)}/{len(all_errors)} ({np.sum(np.array(all_errors) < 5.0)/len(all_errors)*100:.1f}%)")
    
    # 可视化
    visualizer.visualize_predictions(results, save_plots=True)
    
    return results

if __name__ == "__main__":
    results = main()
