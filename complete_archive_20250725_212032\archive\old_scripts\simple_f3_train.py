#!/usr/bin/env python3
"""
Simple F3 Training Script
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from pathlib import Path
import time

class F3Dataset(torch.utils.data.Dataset):
    def __init__(self, data_dir, split):
        self.split_dir = Path(data_dir) / split
        self.files = list(self.split_dir.glob('*_keypoints.npy'))
        print(f"{split}: {len(self.files)} samples")
        
    def __len__(self):
        return len(self.files)
    
    def __getitem__(self, idx):
        kp_file = self.files[idx]
        sample_id = kp_file.stem.replace('_keypoints', '')
        
        keypoints = np.load(kp_file)
        pc_file = kp_file.parent / f"{sample_id}_pointcloud.npy"
        pointcloud = np.load(pc_file)
        
        return torch.FloatTensor(pointcloud), torch.FloatTensor(keypoints)

class SimpleNet(nn.Module):
    def __init__(self):
        super().__init__()
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.fc1 = nn.Linear(256, 128)
        self.fc2 = nn.Linear(128, 19 * 3)
        
    def forward(self, x):
        x = x.transpose(2, 1)
        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))
        x = torch.relu(self.conv3(x))
        x = torch.max(x, 2)[0]
        x = torch.relu(self.fc1(x))
        x = self.fc2(x)
        return x.view(-1, 19, 3)

def main():
    print("🚀 F3训练开始")
    
    device = torch.device('cuda:2' if torch.cuda.is_available() else 'cpu')
    print(f"设备: {device}")
    
    # 数据
    train_dataset = F3Dataset("F3SimpleDataset", "train")
    val_dataset = F3Dataset("F3SimpleDataset", "val")
    
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False)
    
    # 模型
    model = SimpleNet().to(device)
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    
    print(f"模型参数: {sum(p.numel() for p in model.parameters()):,}")
    
    best_error = float('inf')
    
    for epoch in range(20):
        print(f"\nEpoch {epoch+1}/20")
        
        # 训练
        model.train()
        train_loss = 0
        for pc, kp in train_loader:
            pc, kp = pc.to(device), kp.to(device)
            optimizer.zero_grad()
            pred = model(pc)
            loss = criterion(pred, kp)
            loss.backward()
            optimizer.step()
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # 验证
        model.eval()
        val_loss = 0
        all_errors = []
        
        with torch.no_grad():
            for pc, kp in val_loader:
                pc, kp = pc.to(device), kp.to(device)
                pred = model(pc)
                loss = criterion(pred, kp)
                val_loss += loss.item()
                
                # 计算误差
                errors = torch.norm(pred - kp, dim=2).cpu().numpy()
                all_errors.extend(errors.flatten())
        
        val_loss /= len(val_loader)
        mean_error = np.mean(all_errors)
        
        print(f"训练损失: {train_loss:.4f}")
        print(f"验证损失: {val_loss:.4f}")
        print(f"平均误差: {mean_error:.2f}mm")
        
        if mean_error < best_error:
            best_error = mean_error
            torch.save(model.state_dict(), 'best_f3_simple.pth')
            print(f"✅ 保存最佳模型: {best_error:.2f}mm")
    
    print(f"\n🎯 训练完成! 最佳误差: {best_error:.2f}mm")
    return best_error

if __name__ == "__main__":
    error = main()
    print(f"最终结果: {error:.2f}mm")
