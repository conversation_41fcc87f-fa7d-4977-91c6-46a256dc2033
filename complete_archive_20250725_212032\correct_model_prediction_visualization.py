#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的模型预测可视化 - 应用训练时的归一化
Correct Model Prediction Visualization - Apply Training Normalization
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import torch
import torch.nn as nn
import torch.nn.functional as F
import pandas as pd
import os
from sklearn.preprocessing import StandardScaler

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class AdaptiveKeypointModel(nn.Module):
    """自适应关键点模型 - 根据关键点数量自动调整架构"""
    
    def __init__(self, num_points=50000, num_keypoints=12, architecture_type='auto'):
        super().__init__()
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        self.architecture_type = architecture_type
        
        # 根据关键点数量选择最佳架构
        if architecture_type == 'auto':
            if num_keypoints <= 6:
                self.arch_type = 'lightweight'
            elif num_keypoints <= 12:
                self.arch_type = 'balanced'
            elif num_keypoints <= 28:
                self.arch_type = 'enhanced'
            else:
                self.arch_type = 'deep'
        else:
            self.arch_type = architecture_type
        
        # 构建对应架构
        self._build_architecture()
    
    def _build_architecture(self):
        """根据类型构建架构"""
        
        if self.arch_type == 'lightweight':
            # 轻量级架构 (3-6关键点)
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(256, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, 128), nn.ReLU(), nn.Dropout(0.1),
                nn.Linear(128, self.num_keypoints * 3)
            )
            
        elif self.arch_type == 'balanced':
            # 平衡架构 (7-12关键点)
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(512, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, self.num_keypoints * 3)
            )
            
        elif self.arch_type == 'enhanced':
            # 增强架构 (13-28关键点)
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
                nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(1024, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 1024), nn.ReLU(), nn.Dropout(0.4),
                nn.Linear(1024, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, self.num_keypoints * 3)
            )
            
        else:  # deep
            # 深度架构 (29-57关键点)
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
                nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU(),
                nn.Conv1d(1024, 2048, 1), nn.BatchNorm1d(2048), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(2048, 1024, 1)
            self.predictor = nn.Sequential(
                nn.Linear(1024, 2048), nn.ReLU(), nn.Dropout(0.5),
                nn.Linear(2048, 1024), nn.ReLU(), nn.Dropout(0.4),
                nn.Linear(1024, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, self.num_keypoints * 3)
            )
        
        # 相互辅助机制 (所有架构都有)
        mutual_dim = min(256, max(64, self.num_keypoints * 8))
        self.mutual_assistance = nn.Sequential(
            nn.Linear(self.num_keypoints * 3, mutual_dim),
            nn.ReLU(), nn.Dropout(0.2),
            nn.Linear(mutual_dim, mutual_dim // 2),
            nn.ReLU(),
            nn.Linear(mutual_dim // 2, self.num_keypoints * 3)
        )
    
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取
        features = self.feature_extractor(x)
        
        # 全局特征
        global_features = self.global_conv(features)
        global_feat = torch.max(global_features, 2)[0]
        
        # 初始预测
        initial_kp = self.predictor(global_feat)
        
        # 相互辅助
        assistance = self.mutual_assistance(initial_kp)
        
        # 最终预测
        final_kp = initial_kp + 0.3 * assistance
        final_kp = final_kp.view(batch_size, self.num_keypoints, 3)
        
        return final_kp

def normalize_data_for_inference(point_cloud, keypoints_subset):
    """
    应用训练时的归一化方法
    将点云和关键点合并后使用StandardScaler归一化
    """
    # 合并点云和关键点
    combined_data = np.vstack([point_cloud, keypoints_subset])
    
    # 使用StandardScaler归一化
    scaler = StandardScaler()
    combined_normalized = scaler.fit_transform(combined_data)
    
    # 分离归一化后的数据
    pc_normalized = combined_normalized[:len(point_cloud)]
    kp_normalized = combined_normalized[len(point_cloud):]
    
    return pc_normalized, kp_normalized, scaler

def denormalize_prediction(prediction_normalized, scaler, num_points):
    """
    将归一化的预测结果反归一化到原始尺度
    """
    # 创建虚拟的点云数据用于反归一化
    dummy_pc = np.zeros((num_points, 3))
    
    # 合并虚拟点云和预测关键点
    combined_data = np.vstack([dummy_pc, prediction_normalized])
    
    # 反归一化
    combined_denormalized = scaler.inverse_transform(combined_data)
    
    # 提取关键点部分
    prediction_denormalized = combined_denormalized[num_points:]
    
    return prediction_denormalized

def correct_prediction_test():
    """正确的预测测试 - 应用训练时的归一化"""
    print("🎯 正确的模型预测测试")
    print("应用训练时的StandardScaler归一化")
    print("=" * 60)
    
    # 加载数据
    print("📥 加载数据...")
    data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints_57 = data['keypoints_57']
    
    print(f"✅ 数据加载完成: {len(point_clouds)}个样本")
    
    # 选择测试样本
    sample_idx = 0
    test_pc = point_clouds[sample_idx]
    true_kp_57 = keypoints_57[sample_idx]
    
    print(f"📊 测试样本 {sample_idx}")
    print(f"   原始点云范围: X[{test_pc[:, 0].min():.1f}, {test_pc[:, 0].max():.1f}]")
    print(f"   原始关键点范围: X[{true_kp_57[:, 0].min():.1f}, {true_kp_57[:, 0].max():.1f}]")
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ 使用设备: {device}")
    
    # 测试配置
    test_configs = [
        {'kp': 15, 'arch': 'balanced', 'file': 'best_15kp_balanced.pth'},
        {'kp': 3, 'arch': 'enhanced', 'file': 'best_3kp_enhanced.pth'},
        {'kp': 47, 'arch': 'enhanced', 'file': 'best_47kp_enhanced.pth'},
        {'kp': 57, 'arch': 'balanced', 'file': 'best_57kp_balanced.pth'}
    ]
    
    results = {}
    
    for config in test_configs:
        print(f"\n🔄 测试 {config['kp']}点 {config['arch']}架构...")
        
        if not os.path.exists(config['file']):
            print(f"  ❌ 模型文件不存在: {config['file']}")
            continue
        
        try:
            # 选择对应的真实关键点
            if config['kp'] == 57:
                true_kp_subset = true_kp_57
            else:
                # 均匀采样选择关键点
                indices = np.linspace(0, 56, config['kp'], dtype=int)
                true_kp_subset = true_kp_57[indices]
            
            # 应用训练时的归一化
            pc_normalized, kp_normalized, scaler = normalize_data_for_inference(
                test_pc, true_kp_subset
            )
            
            print(f"  📊 归一化后点云范围: X[{pc_normalized[:, 0].min():.3f}, {pc_normalized[:, 0].max():.3f}]")
            print(f"  📊 归一化后关键点范围: X[{kp_normalized[:, 0].min():.3f}, {kp_normalized[:, 0].max():.3f}]")
            
            # 创建模型
            model = AdaptiveKeypointModel(
                num_points=50000, 
                num_keypoints=config['kp'], 
                architecture_type=config['arch']
            )
            
            # 加载权重
            checkpoint = torch.load(config['file'], map_location=device)
            model.load_state_dict(checkpoint)
            model.to(device)
            model.eval()
            
            print(f"  ✅ 模型加载成功")
            
            # 进行预测
            with torch.no_grad():
                pc_tensor = torch.FloatTensor(pc_normalized).unsqueeze(0).to(device)
                prediction_normalized = model(pc_tensor)
                pred_kp_normalized = prediction_normalized.cpu().numpy()[0]
                
                print(f"  📊 归一化预测范围: X[{pred_kp_normalized[:, 0].min():.3f}, {pred_kp_normalized[:, 0].max():.3f}]")
                
                # 反归一化预测结果
                pred_kp = denormalize_prediction(pred_kp_normalized, scaler, len(test_pc))
                
                print(f"  📊 反归一化预测范围: X[{pred_kp[:, 0].min():.1f}, {pred_kp[:, 0].max():.1f}]")
                
                # 计算误差
                errors = np.linalg.norm(true_kp_subset - pred_kp, axis=1)
                avg_error = np.mean(errors)
                
                print(f"  📊 平均误差: {avg_error:.2f}mm")
                print(f"  📊 误差范围: {errors.min():.2f} - {errors.max():.2f}mm")
                
                # 保存结果
                results[config['kp']] = {
                    'true': true_kp_subset,
                    'pred': pred_kp,
                    'errors': errors,
                    'avg_error': avg_error,
                    'architecture': config['arch']
                }
                
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    return test_pc, results, sample_idx

if __name__ == "__main__":
    test_pc, results, sample_idx = correct_prediction_test()
    
    if results:
        print(f"\n📋 测试总结:")
        print("=" * 50)
        for kp_count, data in results.items():
            print(f"{kp_count:2d}点 {data['architecture']:10s}: {data['avg_error']:6.2f}mm")
    else:
        print("❌ 没有成功测试任何模型")
