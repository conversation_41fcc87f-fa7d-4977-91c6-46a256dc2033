#!/usr/bin/env python3
"""
测试大感受野模型的改进效果
重点验证F3-13 (<PERSON>最高点) 的性能提升
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from train_large_rf_19kp_model import LargeReceptiveFieldPointNet
from basic_19keypoints_system import BasicHeatmapPointNet19, extract_keypoints_from_heatmaps_19

def load_models_for_comparison(device):
    """加载模型进行对比"""
    
    models = {}
    
    # 加载原始19点模型
    try:
        model_original = BasicHeatmapPointNet19(input_dim=3, num_keypoints=19).to(device)
        model_original.load_state_dict(torch.load('best_fixed_19kp_model.pth', map_location=device))
        model_original.eval()
        models['original'] = model_original
        print("✅ 原始19点模型加载成功")
    except Exception as e:
        print(f"❌ 原始19点模型加载失败: {e}")
    
    # 加载大感受野模型
    try:
        model_large_rf = LargeReceptiveFieldPointNet(input_dim=3, num_keypoints=19).to(device)
        model_large_rf.load_state_dict(torch.load('best_large_rf_19kp_model.pth', map_location=device))
        model_large_rf.eval()
        models['large_rf'] = model_large_rf
        print("✅ 大感受野模型加载成功")
    except Exception as e:
        print(f"❌ 大感受野模型加载失败: {e}")
    
    return models

def test_models_performance(models, point_clouds, keypoints, sample_ids, device):
    """测试模型性能"""
    
    results = {}
    
    for model_name, model in models.items():
        print(f"\n🔍 测试 {model_name} 模型...")
        
        sample_results = []
        
        for i in range(len(point_clouds)):
            sample_id = sample_ids[i]
            point_cloud = point_clouds[i]
            true_keypoints = keypoints[i]
            
            # 采样点云
            if len(point_cloud) > 8192:
                indices = np.random.choice(len(point_cloud), 8192, replace=False)
                pc_sampled = point_cloud[indices]
            else:
                pc_sampled = point_cloud
            
            pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
            
            # 预测
            with torch.no_grad():
                pred_heatmaps = model(pc_tensor)
            
            pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze()
            pred_keypoints, confidences = extract_keypoints_from_heatmaps_19(pred_heatmaps_np, pc_sampled)
            
            # 计算每个关键点的误差
            errors = [np.linalg.norm(pred_keypoints[j] - true_keypoints[j]) for j in range(len(true_keypoints))]
            avg_error = np.mean(errors)
            
            sample_results.append({
                'sample_id': sample_id,
                'errors': errors,
                'avg_error': avg_error,
                'confidences': confidences,
                'pred_keypoints': pred_keypoints,
                'true_keypoints': true_keypoints
            })
            
            print(f"   样本 {sample_id}: {avg_error:.2f}mm")
        
        results[model_name] = sample_results
    
    return results

def analyze_f3_13_improvement(results):
    """专门分析F3-13的改进效果"""
    
    print(f"\n🎯 F3-13 (Z最高点) 改进分析:")
    print("=" * 60)
    
    f3_13_results = {}
    
    for model_name, model_results in results.items():
        f3_13_errors = [r['errors'][12] for r in model_results]  # F3-13是索引12
        f3_13_confidences = [r['confidences'][12] for r in model_results]
        
        f3_13_results[model_name] = {
            'errors': f3_13_errors,
            'confidences': f3_13_confidences,
            'mean_error': np.mean(f3_13_errors),
            'std_error': np.std(f3_13_errors),
            'mean_confidence': np.mean(f3_13_confidences),
            'max_error': np.max(f3_13_errors),
            'min_error': np.min(f3_13_errors)
        }
        
        print(f"{model_name} 模型 F3-13 性能:")
        print(f"   平均误差: {f3_13_results[model_name]['mean_error']:.2f}±{f3_13_results[model_name]['std_error']:.2f}mm")
        print(f"   误差范围: {f3_13_results[model_name]['min_error']:.2f} - {f3_13_results[model_name]['max_error']:.2f}mm")
        print(f"   平均置信度: {f3_13_results[model_name]['mean_confidence']:.3f}")
    
    # 计算改进幅度
    if 'original' in f3_13_results and 'large_rf' in f3_13_results:
        improvement = f3_13_results['original']['mean_error'] - f3_13_results['large_rf']['mean_error']
        improvement_pct = improvement / f3_13_results['original']['mean_error'] * 100
        
        print(f"\n🚀 F3-13 改进效果:")
        print(f"   误差减少: {improvement:.2f}mm")
        print(f"   相对改进: {improvement_pct:.1f}%")
        
        if improvement > 0:
            print(f"   ✅ 大感受野模型显著改善了F3-13的性能！")
        else:
            print(f"   ⚠️ 改进效果不明显，需要进一步优化")
    
    return f3_13_results

def analyze_all_keypoints_improvement(results):
    """分析所有关键点的改进效果"""
    
    print(f"\n📊 所有关键点改进分析:")
    print("=" * 60)
    
    if 'original' not in results or 'large_rf' not in results:
        print("❌ 缺少对比模型，无法分析改进效果")
        return
    
    original_results = results['original']
    large_rf_results = results['large_rf']
    
    # 计算每个关键点的平均误差
    original_kp_errors = np.array([r['errors'] for r in original_results])  # [samples, 19]
    large_rf_kp_errors = np.array([r['errors'] for r in large_rf_results])  # [samples, 19]
    
    original_mean = np.mean(original_kp_errors, axis=0)  # [19]
    large_rf_mean = np.mean(large_rf_kp_errors, axis=0)  # [19]
    
    improvements = original_mean - large_rf_mean  # 正值表示改进
    improvement_pcts = improvements / original_mean * 100
    
    print(f"{'KP':<4} {'Original':<10} {'Large RF':<10} {'Improve':<10} {'Improve %':<10} {'Status'}")
    print("-" * 60)
    
    significant_improvements = []
    significant_degradations = []
    
    for kp_idx in range(19):
        status = ""
        if improvements[kp_idx] > 2:
            status = "🚀 大幅改进"
            significant_improvements.append(kp_idx)
        elif improvements[kp_idx] > 0.5:
            status = "✅ 改进"
        elif improvements[kp_idx] > -0.5:
            status = "➖ 持平"
        elif improvements[kp_idx] > -2:
            status = "⚠️ 轻微下降"
        else:
            status = "❌ 显著下降"
            significant_degradations.append(kp_idx)
        
        print(f"F3-{kp_idx+1:<2} {original_mean[kp_idx]:<10.2f} {large_rf_mean[kp_idx]:<10.2f} "
              f"{improvements[kp_idx]:<10.2f} {improvement_pcts[kp_idx]:<10.1f} {status}")
    
    # 总体性能对比
    original_overall = np.mean([r['avg_error'] for r in original_results])
    large_rf_overall = np.mean([r['avg_error'] for r in large_rf_results])
    overall_improvement = original_overall - large_rf_overall
    overall_improvement_pct = overall_improvement / original_overall * 100
    
    print(f"\n🎯 总体性能对比:")
    print(f"   原始模型: {original_overall:.2f}mm")
    print(f"   大感受野模型: {large_rf_overall:.2f}mm")
    print(f"   整体改进: {overall_improvement:.2f}mm ({overall_improvement_pct:.1f}%)")
    
    if significant_improvements:
        print(f"\n🚀 显著改进的关键点:")
        for kp_idx in significant_improvements:
            print(f"   F3-{kp_idx+1}: {original_mean[kp_idx]:.2f}mm → {large_rf_mean[kp_idx]:.2f}mm "
                  f"(改进 {improvements[kp_idx]:.2f}mm)")
    
    if significant_degradations:
        print(f"\n❌ 性能下降的关键点:")
        for kp_idx in significant_degradations:
            print(f"   F3-{kp_idx+1}: {original_mean[kp_idx]:.2f}mm → {large_rf_mean[kp_idx]:.2f}mm "
                  f"(下降 {-improvements[kp_idx]:.2f}mm)")

def create_improvement_visualization(results, f3_13_results):
    """创建改进效果可视化"""
    
    if 'original' not in results or 'large_rf' not in results:
        print("❌ 缺少对比数据，无法创建可视化")
        return
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. F3-13专门对比
    ax1 = axes[0, 0]
    
    f3_13_original = f3_13_results['original']['errors']
    f3_13_large_rf = f3_13_results['large_rf']['errors']
    
    bp = ax1.boxplot([f3_13_original, f3_13_large_rf], 
                     labels=['Original', 'Large RF'], patch_artist=True)
    
    bp['boxes'][0].set_facecolor('lightcoral')
    bp['boxes'][1].set_facecolor('lightgreen')
    
    ax1.set_ylabel('F3-13 Error (mm)')
    ax1.set_title('F3-13 (Z-Max) Performance Comparison')
    ax1.grid(True, alpha=0.3)
    
    # 添加改进信息
    improvement = f3_13_results['original']['mean_error'] - f3_13_results['large_rf']['mean_error']
    ax1.text(0.5, 0.95, f'Improvement: {improvement:.2f}mm', 
             transform=ax1.transAxes, ha='center', va='top',
             bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.8))
    
    # 2. 整体性能对比
    ax2 = axes[0, 1]
    
    original_overall = [r['avg_error'] for r in results['original']]
    large_rf_overall = [r['avg_error'] for r in results['large_rf']]
    
    bp = ax2.boxplot([original_overall, large_rf_overall], 
                     labels=['Original', 'Large RF'], patch_artist=True)
    
    bp['boxes'][0].set_facecolor('lightcoral')
    bp['boxes'][1].set_facecolor('lightgreen')
    
    ax2.set_ylabel('Average Error (mm)')
    ax2.set_title('Overall Performance Comparison')
    ax2.grid(True, alpha=0.3)
    
    # 3. 关键点改进热力图
    ax3 = axes[0, 2]
    
    original_kp_errors = np.array([r['errors'] for r in results['original']])
    large_rf_kp_errors = np.array([r['errors'] for r in results['large_rf']])
    
    original_mean = np.mean(original_kp_errors, axis=0)
    large_rf_mean = np.mean(large_rf_kp_errors, axis=0)
    improvements = original_mean - large_rf_mean
    
    # 创建热力图数据
    heatmap_data = improvements.reshape(1, -1)
    
    im = ax3.imshow(heatmap_data, cmap='RdYlGn', aspect='auto', vmin=-5, vmax=5)
    ax3.set_xticks(range(19))
    ax3.set_xticklabels([f'F3-{i+1}' for i in range(19)], rotation=45)
    ax3.set_yticks([])
    ax3.set_title('Improvement Heatmap (mm)')
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax3)
    cbar.set_label('Improvement (mm)')
    
    # 4. 样本间对比
    ax4 = axes[1, 0]
    
    sample_ids = [r['sample_id'] for r in results['original']]
    x = np.arange(len(sample_ids))
    width = 0.35
    
    ax4.bar(x - width/2, original_overall, width, label='Original', alpha=0.7, color='lightcoral')
    ax4.bar(x + width/2, large_rf_overall, width, label='Large RF', alpha=0.7, color='lightgreen')
    
    ax4.set_xlabel('Sample')
    ax4.set_ylabel('Average Error (mm)')
    ax4.set_title('Error by Sample')
    ax4.set_xticks(x)
    ax4.set_xticklabels([sid[-3:] for sid in sample_ids], rotation=45)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 5. 置信度对比
    ax5 = axes[1, 1]
    
    original_conf = np.mean([r['confidences'] for r in results['original']], axis=0)
    large_rf_conf = np.mean([r['confidences'] for r in results['large_rf']], axis=0)
    
    x_kp = np.arange(19)
    ax5.plot(x_kp, original_conf, 'o-', label='Original', alpha=0.7)
    ax5.plot(x_kp, large_rf_conf, 's-', label='Large RF', alpha=0.7)
    
    ax5.set_xlabel('Keypoint Index')
    ax5.set_ylabel('Average Confidence')
    ax5.set_title('Confidence Comparison')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    # 特别标记F3-13
    ax5.axvline(x=12, color='red', linestyle='--', alpha=0.7, label='F3-13')
    ax5.legend()
    
    # 6. 改进总结
    ax6 = axes[1, 2]
    ax6.axis('off')
    
    # 计算统计信息
    overall_improvement = np.mean(original_overall) - np.mean(large_rf_overall)
    f3_13_improvement = f3_13_results['original']['mean_error'] - f3_13_results['large_rf']['mean_error']
    
    improved_count = np.sum(improvements > 0.5)
    degraded_count = np.sum(improvements < -0.5)
    
    summary_text = f"""
Large Receptive Field Improvement Summary:

Overall Performance:
• Original Model: {np.mean(original_overall):.2f}mm
• Large RF Model: {np.mean(large_rf_overall):.2f}mm
• Overall Improvement: {overall_improvement:.2f}mm

F3-13 (Z-Max) Specific:
• Original: {f3_13_results['original']['mean_error']:.2f}mm
• Large RF: {f3_13_results['large_rf']['mean_error']:.2f}mm
• F3-13 Improvement: {f3_13_improvement:.2f}mm

Keypoint Analysis:
• Improved: {improved_count}/19 keypoints
• Degraded: {degraded_count}/19 keypoints
• Stable: {19-improved_count-degraded_count}/19 keypoints

Key Improvements:
• Larger receptive field (1→12 points)
• Multi-scale feature fusion
• Extreme point specialized branch
• Weighted loss for problem points
• Adaptive heatmap targets

Success Factors:
✓ Dilated convolutions
✓ Multi-scale fusion
✓ Problem-specific optimization
✓ Architectural improvements
"""
    
    ax6.text(0.05, 0.95, summary_text, transform=ax6.transAxes, fontsize=9,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.suptitle('Large Receptive Field Model Improvement Analysis\n'
                'Solving F3-13 Global Maximum Detection Problem', 
                fontsize=16, fontweight='bold')
    plt.tight_layout(rect=[0, 0, 1, 0.93])
    
    filename = 'large_rf_improvement_analysis.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 改进分析保存: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🔍 大感受野模型改进效果测试")
    print("重点验证F3-13 (Z最高点) 的性能提升")
    print("=" * 60)
    
    # 加载数据
    data = np.load('f3_19kp_preprocessed.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    sample_ids = data['sample_ids']
    
    print(f"✅ 数据加载完成: {len(point_clouds)} 样本")
    
    # 加载模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    models = load_models_for_comparison(device)
    
    if len(models) < 2:
        print("❌ 需要至少两个模型进行对比")
        return
    
    # 测试模型性能
    results = test_models_performance(models, point_clouds, keypoints, sample_ids, device)
    
    # 分析F3-13改进效果
    f3_13_results = analyze_f3_13_improvement(results)
    
    # 分析所有关键点改进效果
    analyze_all_keypoints_improvement(results)
    
    # 创建可视化
    create_improvement_visualization(results, f3_13_results)
    
    print(f"\n🎉 大感受野模型测试完成!")
    print("📊 详细分析结果已保存为可视化图表")

if __name__ == "__main__":
    main()
