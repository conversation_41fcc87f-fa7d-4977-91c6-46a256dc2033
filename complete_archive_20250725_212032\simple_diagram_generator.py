#!/usr/bin/env python3
"""
简单图表生成器
Simple Diagram Generator
避免显示问题，直接保存图片
"""

import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from matplotlib.patches import Rectangle, FancyBboxPatch
import matplotlib.patches as mpatches

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_heatmap_vs_direct_comparison():
    """创建Heatmap vs 直接点预测对比图"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # 1. 直接点预测方法
    ax1.set_title('Direct Point Prediction Method', fontsize=12, fontweight='bold')
    ax1.set_xlim(0, 10)
    ax1.set_ylim(0, 8)
    
    # 输入点云
    np.random.seed(42)
    points_x = np.random.uniform(1, 3, 50)
    points_y = np.random.uniform(2, 6, 50)
    ax1.scatter(points_x, points_y, c='lightblue', s=8, alpha=0.6, label='Input Point Cloud')
    
    # 网络处理
    ax1.add_patch(Rectangle((4, 3), 2, 2, facecolor='lightgreen', alpha=0.7))
    ax1.text(5, 4, 'PointNet\nNetwork', ha='center', va='center', fontweight='bold', fontsize=9)
    
    # 直接输出关键点
    keypoints_x = [8, 8.5, 7.5]
    keypoints_y = [4.5, 3.5, 5.5]
    ax1.scatter(keypoints_x, keypoints_y, c='red', s=80, marker='*', label='Predicted Points')
    
    # 箭头
    ax1.arrow(3.2, 4, 0.6, 0, head_width=0.15, head_length=0.1, fc='black', ec='black')
    ax1.arrow(6.2, 4, 1.6, 0, head_width=0.15, head_length=0.1, fc='black', ec='black')
    
    ax1.text(5, 1.5, 'Output: Direct 3D coordinates (x,y,z)', ha='center', fontsize=9, 
             bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
    ax1.text(5, 0.8, 'Pros: Simple & Fast\nCons: No uncertainty info', ha='center', fontsize=8)
    
    ax1.set_xticks([])
    ax1.set_yticks([])
    ax1.legend(fontsize=8)
    
    # 2. Heatmap方法
    ax2.set_title('Heatmap Regression Method', fontsize=12, fontweight='bold')
    ax2.set_xlim(0, 10)
    ax2.set_ylim(0, 8)
    
    # 输入点云
    ax2.scatter(points_x, points_y, c='lightblue', s=8, alpha=0.6, label='Input Point Cloud')
    
    # 网络处理
    ax2.add_patch(Rectangle((4, 3), 2, 2, facecolor='lightcoral', alpha=0.7))
    ax2.text(5, 4, 'Heatmap\nNetwork', ha='center', va='center', fontweight='bold', fontsize=9)
    
    # Heatmap输出
    heatmap_x = np.linspace(7, 9, 15)
    heatmap_y = np.linspace(3, 6, 15)
    X, Y = np.meshgrid(heatmap_x, heatmap_y)
    Z = np.exp(-((X-8)**2 + (Y-4.5)**2)/0.3)  # 高斯分布
    
    contour = ax2.contourf(X, Y, Z, levels=8, cmap='Reds', alpha=0.7)
    ax2.scatter([8], [4.5], c='red', s=80, marker='*', label='Max Probability Point')
    
    # 箭头
    ax2.arrow(3.2, 4, 0.6, 0, head_width=0.15, head_length=0.1, fc='black', ec='black')
    ax2.arrow(6.2, 4, 0.6, 0, head_width=0.15, head_length=0.1, fc='black', ec='black')
    
    ax2.text(5, 1.5, 'Output: Probability heatmap\nConfidence for each point', ha='center', fontsize=9,
             bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
    ax2.text(5, 0.8, 'Pros: Uncertainty quantification\nCons: More complex', ha='center', fontsize=8)
    
    ax2.set_xticks([])
    ax2.set_yticks([])
    ax2.legend(fontsize=8)
    
    plt.tight_layout()
    plt.savefig('heatmap_vs_direct_comparison.png', dpi=300, bbox_inches='tight')
    print("✅ Heatmap vs Direct comparison saved: heatmap_vs_direct_comparison.png")
    plt.close()

def create_gender_split_flowchart():
    """创建男女分离流程图"""
    
    fig, ax = plt.subplots(1, 1, figsize=(14, 8))
    ax.set_xlim(0, 14)
    ax.set_ylim(0, 8)
    ax.set_title('Gender Dataset Split Process', fontsize=14, fontweight='bold', pad=20)
    
    # 1. 原始数据集
    ax.add_patch(FancyBboxPatch((1, 6), 2.5, 1.2, boxstyle="round,pad=0.1", 
                                facecolor='lightblue', edgecolor='blue', linewidth=2))
    ax.text(2.25, 6.6, 'Original Dataset\n(Mixed Gender)', ha='center', va='center', 
            fontsize=9, fontweight='bold')
    ax.text(2.25, 5.7, '20 samples', ha='center', va='center', fontsize=8)
    
    # 2. 特征提取
    ax.add_patch(FancyBboxPatch((5, 6), 2.5, 1.2, boxstyle="round,pad=0.1", 
                                facecolor='lightgreen', edgecolor='green', linewidth=2))
    ax.text(6.25, 6.6, 'Feature Extraction', ha='center', va='center', 
            fontsize=9, fontweight='bold')
    ax.text(6.25, 6.2, 'Pelvic morphology\nfeatures', ha='center', va='center', fontsize=8)
    
    # 3. 聚类分析
    ax.add_patch(FancyBboxPatch((9, 6), 2.5, 1.2, boxstyle="round,pad=0.1", 
                                facecolor='lightyellow', edgecolor='orange', linewidth=2))
    ax.text(10.25, 6.6, 'K-means Clustering\n(k=2)', ha='center', va='center', 
            fontsize=9, fontweight='bold')
    ax.text(10.25, 6.2, 'Automatic grouping', ha='center', va='center', fontsize=8)
    
    # 箭头
    ax.arrow(3.7, 6.6, 1.1, 0, head_width=0.1, head_length=0.15, fc='black', ec='black')
    ax.arrow(7.7, 6.6, 1.1, 0, head_width=0.1, head_length=0.15, fc='black', ec='black')
    
    # 4. 特征对比
    ax.add_patch(FancyBboxPatch((2, 3.5), 4, 1.5, boxstyle="round,pad=0.1", 
                                facecolor='lightcoral', edgecolor='red', linewidth=2))
    ax.text(4, 4.6, 'Feature Comparison', ha='center', va='center', 
            fontsize=10, fontweight='bold')
    
    # 特征对比数据
    ax.text(2.5, 4.2, 'Group A', ha='center', va='center', fontsize=9, fontweight='bold', color='red')
    ax.text(5.5, 4.2, 'Group B', ha='center', va='center', fontsize=9, fontweight='bold', color='blue')
    ax.text(2.5, 3.9, 'Inlet Index: 98.5', ha='center', va='center', fontsize=8)
    ax.text(5.5, 3.9, 'Inlet Index: 92.1', ha='center', va='center', fontsize=8)
    ax.text(2.5, 3.7, 'XY Ratio: 1.35', ha='center', va='center', fontsize=8)
    ax.text(5.5, 3.7, 'XY Ratio: 1.28', ha='center', va='center', fontsize=8)
    
    # 5. 性别判断
    ax.add_patch(FancyBboxPatch((8, 3.5), 4, 1.5, boxstyle="round,pad=0.1", 
                                facecolor='lightpink', edgecolor='purple', linewidth=2))
    ax.text(10, 4.6, 'Gender Assignment', ha='center', va='center', 
            fontsize=10, fontweight='bold')
    ax.text(10, 4.2, 'Inlet Index > 95 → Female', ha='center', va='center', fontsize=9, color='red')
    ax.text(10, 3.9, 'Inlet Index < 95 → Male', ha='center', va='center', fontsize=9, color='blue')
    
    # 箭头向下
    ax.arrow(4, 3.3, 0, -0.8, head_width=0.15, head_length=0.1, fc='red', ec='red')
    ax.arrow(10, 3.3, 0, -0.8, head_width=0.15, head_length=0.1, fc='blue', ec='blue')
    
    # 6. 最终结果
    # 女性数据集
    ax.add_patch(FancyBboxPatch((1, 1), 3, 1.2, boxstyle="round,pad=0.1", 
                                facecolor='pink', edgecolor='red', linewidth=2))
    ax.text(2.5, 1.8, 'Female Dataset', ha='center', va='center', 
            fontsize=10, fontweight='bold', color='red')
    ax.text(2.5, 1.5, '12 samples (60%)', ha='center', va='center', fontsize=9)
    ax.text(2.5, 1.2, 'Model: 4.88mm', ha='center', va='center', fontsize=9, fontweight='bold')
    
    # 男性数据集
    ax.add_patch(FancyBboxPatch((9, 1), 3, 1.2, boxstyle="round,pad=0.1", 
                                facecolor='lightblue', edgecolor='blue', linewidth=2))
    ax.text(10.5, 1.8, 'Male Dataset', ha='center', va='center', 
            fontsize=10, fontweight='bold', color='blue')
    ax.text(10.5, 1.5, '8 samples (40%)', ha='center', va='center', fontsize=9)
    ax.text(10.5, 1.2, 'Model: ~5.8mm', ha='center', va='center', fontsize=9, fontweight='bold')
    
    ax.set_xticks([])
    ax.set_yticks([])
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('gender_dataset_split_flowchart.png', dpi=300, bbox_inches='tight')
    print("✅ Gender split flowchart saved: gender_dataset_split_flowchart.png")
    plt.close()

def create_performance_comparison_chart():
    """创建性能对比图表"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # 1. 方法对比
    ax1.set_title('Method Performance Comparison', fontsize=12, fontweight='bold')
    
    methods = ['Mixed\nTraining', 'Heatmap\n(Female)', 'Direct Point\n(Ensemble)', 'Point\nTransformer']
    errors = [7.2, 4.88, 5.371, 7.129]
    colors = ['gray', 'red', 'green', 'blue']
    
    bars = ax1.bar(methods, errors, color=colors, alpha=0.7, edgecolor='black', linewidth=1)
    
    # 添加数值标签
    for bar, error in zip(bars, errors):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{error}mm', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    # 添加医疗级目标线
    ax1.axhline(y=5.0, color='orange', linestyle='--', linewidth=2, label='Medical Target (5mm)')
    
    ax1.set_ylabel('Average Error (mm)', fontsize=11)
    ax1.set_ylim(0, 8)
    ax1.legend()
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 2. 数据集分布
    ax2.set_title('Dataset Distribution Analysis', fontsize=12, fontweight='bold')
    
    categories = ['Original\nMixed', 'Female\nDataset', 'Male\nDataset']
    sample_counts = [20, 12, 8]
    colors = ['lightgray', 'pink', 'lightblue']
    
    bars = ax2.bar(categories, sample_counts, color=colors, edgecolor='black', linewidth=1)
    
    # 添加数值标签
    for bar, count in zip(bars, sample_counts):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.3,
                f'{count} samples', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    ax2.set_ylabel('Number of Samples', fontsize=11)
    ax2.set_ylim(0, 25)
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 添加改进百分比
    improvement_female = (7.2 - 4.88) / 7.2 * 100
    improvement_male = (7.2 - 5.8) / 7.2 * 100
    
    ax2.text(1, 22, f'Female Model\nImprovement:\n{improvement_female:.1f}%', 
             ha='center', va='center', fontsize=9, 
             bbox=dict(boxstyle='round', facecolor='pink', alpha=0.7))
    ax2.text(2, 18, f'Male Model\nImprovement:\n{improvement_male:.1f}%', 
             ha='center', va='center', fontsize=9,
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.7))
    
    plt.tight_layout()
    plt.savefig('performance_comparison_chart.png', dpi=300, bbox_inches='tight')
    print("✅ Performance comparison chart saved: performance_comparison_chart.png")
    plt.close()

def create_uncertainty_value_diagram():
    """创建不确定性价值示意图"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # 1. 直接点预测的局限性
    ax1.set_title('Direct Point Prediction Limitations', fontsize=12, fontweight='bold')
    ax1.set_xlim(0, 10)
    ax1.set_ylim(0, 8)
    
    # 模拟点云
    np.random.seed(42)
    points = np.random.uniform(1, 9, (100, 2))
    points[:, 1] = points[:, 1] * 0.8 + 1  # 调整y范围
    ax1.scatter(points[:, 0], points[:, 1], c='lightblue', s=3, alpha=0.6)
    
    # 预测点
    pred_point = [5, 4]
    true_point = [5.5, 4.2]
    
    ax1.scatter(*pred_point, c='red', s=100, marker='*', label='Predicted Point')
    ax1.scatter(*true_point, c='green', s=100, marker='o', label='True Point')
    
    # 问号表示不确定性
    ax1.text(2, 6.5, '?', fontsize=60, ha='center', color='red')
    ax1.text(2, 5.5, 'Unknown\nConfidence', ha='center', va='center', fontsize=10,
             bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
    
    ax1.text(8, 6.5, '!', fontsize=60, ha='center', color='orange')
    ax1.text(8, 5.5, 'Cannot identify\ndifficult cases', ha='center', va='center', fontsize=10,
             bbox=dict(boxstyle='round', facecolor='orange', alpha=0.7))
    
    ax1.set_xticks([])
    ax1.set_yticks([])
    ax1.legend()
    
    # 2. Heatmap的优势
    ax2.set_title('Heatmap Regression Advantages', fontsize=12, fontweight='bold')
    ax2.set_xlim(0, 10)
    ax2.set_ylim(0, 8)
    
    # 模拟点云
    ax2.scatter(points[:, 0], points[:, 1], c='lightblue', s=3, alpha=0.6)
    
    # 高置信度区域
    x_high = np.linspace(4, 6, 15)
    y_high = np.linspace(3, 5, 15)
    X_high, Y_high = np.meshgrid(x_high, y_high)
    Z_high = np.exp(-((X_high-5)**2 + (Y_high-4)**2)/0.2)
    
    # 低置信度区域
    x_low = np.linspace(7, 9, 15)
    y_low = np.linspace(5, 7, 15)
    X_low, Y_low = np.meshgrid(x_low, y_low)
    Z_low = 0.3 * np.exp(-((X_low-8)**2 + (Y_low-6)**2)/0.3)
    
    ax2.contourf(X_high, Y_high, Z_high, levels=8, cmap='Reds', alpha=0.7)
    ax2.contourf(X_low, Y_low, Z_low, levels=5, cmap='Blues', alpha=0.5)
    
    ax2.scatter([5], [4], c='red', s=100, marker='*', label='High Confidence')
    ax2.scatter([8], [6], c='blue', s=100, marker='*', label='Low Confidence')
    
    # 置信度标识
    ax2.text(1.5, 6.5, '✓', fontsize=40, ha='center', color='green')
    ax2.text(1.5, 5.5, 'High confidence\nDirect use', ha='center', va='center', fontsize=10,
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.7))
    
    ax2.text(1.5, 2.5, '⚠', fontsize=40, ha='center', color='orange')
    ax2.text(1.5, 1.5, 'Low confidence\nNeed review', ha='center', va='center', fontsize=10,
             bbox=dict(boxstyle='round', facecolor='orange', alpha=0.7))
    
    ax2.set_xticks([])
    ax2.set_yticks([])
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig('uncertainty_value_diagram.png', dpi=300, bbox_inches='tight')
    print("✅ Uncertainty value diagram saved: uncertainty_value_diagram.png")
    plt.close()

def main():
    """主函数"""
    print("📊 生成可视化图表...")
    print("=" * 50)
    
    try:
        # 创建Heatmap对比图
        create_heatmap_vs_direct_comparison()
        
        # 创建男女分离流程图
        create_gender_split_flowchart()
        
        # 创建性能对比图
        create_performance_comparison_chart()
        
        # 创建不确定性价值图
        create_uncertainty_value_diagram()
        
        print("\n✅ 所有图表生成完成!")
        print("生成的图片文件:")
        print("  • heatmap_vs_direct_comparison.png - Heatmap vs 直接点预测对比")
        print("  • gender_dataset_split_flowchart.png - 男女分离流程图")
        print("  • performance_comparison_chart.png - 性能对比图表")
        print("  • uncertainty_value_diagram.png - 不确定性价值示意图")
        
        print(f"\n💡 图表说明:")
        print(f"1. Heatmap对比图展示了两种方法的技术差异")
        print(f"2. 男女分离流程图说明了科学的分离方法")
        print(f"3. 性能对比图显示了各方法的精度差异")
        print(f"4. 不确定性图解释了Heatmap的独特价值")
        
    except Exception as e:
        print(f"❌ 图表生成出错: {e}")
        print("可能是显示环境问题，但文字分析已经完成")

if __name__ == "__main__":
    main()
