"""
简化的预测可视化
避免复杂的3D绘图，专注于清晰的2D对比
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import matplotlib.patches as patches

from save_best_model import BestSimplePointNet
from improved_data_loader import ImprovedDataLoader

class SimplePredictionVisualizer:
    """简化预测可视化器"""
    
    def __init__(self, data_root="output/training_fixed"):
        self.data_root = data_root
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 创建输出目录
        self.output_dir = Path("output/simple_prediction_viz")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🎨 简化预测可视化器初始化")
        
        # 设置matplotlib
        plt.style.use('default')
        plt.rcParams['figure.facecolor'] = 'white'
        
    def load_model_and_predict(self):
        """加载模型并进行预测"""
        print("加载模型和数据...")
        
        # 加载模型
        model = BestSimplePointNet(num_keypoints=57)
        model_path = "output/scale_corrected_training/best_baseline_model.pth"
        
        if Path(model_path).exists():
            checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"✅ 模型加载成功")
        
        model = model.to(self.device)
        model.eval()
        
        # 加载数据
        data_loader_manager = ImprovedDataLoader(
            data_root=self.data_root,
            batch_size=1,
            num_workers=0,
            num_points=512
        )
        
        _, val_loader = data_loader_manager.create_dataloaders(train_ratio=0.8)
        
        # 获取4个样本进行预测
        results = []
        with torch.no_grad():
            for i, (point_cloud, keypoints) in enumerate(val_loader):
                if i >= 4:
                    break
                
                point_cloud = point_cloud.to(self.device)
                keypoints = keypoints.to(self.device)
                
                pred_keypoints = model(point_cloud)
                error = torch.norm(pred_keypoints - keypoints, dim=2).cpu().numpy()
                
                results.append({
                    'point_cloud': point_cloud.cpu().numpy()[0],
                    'ground_truth': keypoints.cpu().numpy()[0],
                    'prediction': pred_keypoints.cpu().numpy()[0],
                    'error': error[0],
                    'sample_id': f'Sample_{i+1}'
                })
        
        print(f"✅ 获得 {len(results)} 个预测结果")
        return results
    
    def create_2d_comparison_grid(self, results):
        """创建2D对比网格图"""
        print("生成2D对比网格图...")
        
        fig, axes = plt.subplots(2, 4, figsize=(20, 10))
        
        for i, result in enumerate(results):
            row = i // 2
            col = (i % 2) * 2
            
            pc = result['point_cloud']
            gt = result['ground_truth']
            pred = result['prediction']
            error = result['error']
            
            # XY平面对比
            ax1 = axes[row, col]
            
            # 绘制点云背景
            ax1.scatter(pc[:, 0], pc[:, 1], c='lightgray', alpha=0.3, s=1, label='Point Cloud')
            
            # 绘制真实关键点
            ax1.scatter(gt[:, 0], gt[:, 1], c='red', s=50, alpha=0.8, 
                       label='Ground Truth', marker='o', edgecolors='darkred', linewidth=1)
            
            # 绘制预测关键点
            ax1.scatter(pred[:, 0], pred[:, 1], c='blue', s=50, alpha=0.8, 
                       label='Prediction', marker='^', edgecolors='darkblue', linewidth=1)
            
            # 连接对应点
            for j in range(len(gt)):
                color = 'red' if error[j] > 5 else 'orange' if error[j] > 3 else 'green'
                alpha = min(1.0, error[j] / 5.0)
                ax1.plot([gt[j, 0], pred[j, 0]], [gt[j, 1], pred[j, 1]], 
                        color=color, alpha=alpha, linewidth=1)
            
            mean_error = np.mean(error)
            accuracy_5mm = (error <= 5.0).mean() * 100
            
            ax1.set_title(f'{result["sample_id"]} - XY View\nError: {mean_error:.2f}mm, Acc: {accuracy_5mm:.1f}%', 
                         fontsize=11, fontweight='bold')
            ax1.set_xlabel('X (mm)')
            ax1.set_ylabel('Y (mm)')
            ax1.grid(True, alpha=0.3)
            ax1.set_aspect('equal')
            
            if i == 0:
                ax1.legend(fontsize=9)
            
            # 误差热图
            ax2 = axes[row, col + 1]
            
            # 创建误差颜色映射
            scatter = ax2.scatter(gt[:, 0], gt[:, 1], c=error, s=80, 
                                cmap='RdYlGn_r', vmin=0, vmax=8, alpha=0.8,
                                edgecolors='black', linewidth=0.5)
            
            ax2.set_title(f'{result["sample_id"]} - Error Heatmap\nMax: {np.max(error):.2f}mm, Min: {np.min(error):.2f}mm', 
                         fontsize=11, fontweight='bold')
            ax2.set_xlabel('X (mm)')
            ax2.set_ylabel('Y (mm)')
            ax2.grid(True, alpha=0.3)
            ax2.set_aspect('equal')
            
            # 添加颜色条
            if i == 0:
                cbar = plt.colorbar(scatter, ax=ax2)
                cbar.set_label('Error (mm)', rotation=270, labelpad=15)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / '2d_comparison_grid.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 2D对比网格图完成")
    
    def create_error_distribution_plot(self, results):
        """创建误差分布图"""
        print("生成误差分布图...")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 收集所有误差数据
        all_errors = []
        sample_errors = []
        sample_names = []
        
        for result in results:
            all_errors.extend(result['error'].tolist())
            sample_errors.append(np.mean(result['error']))
            sample_names.append(result['sample_id'])
        
        all_errors = np.array(all_errors)
        
        # 1. 整体误差分布直方图
        axes[0, 0].hist(all_errors, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].axvline(np.mean(all_errors), color='red', linestyle='--', linewidth=2,
                          label=f'Mean: {np.mean(all_errors):.2f}mm')
        axes[0, 0].axvline(np.median(all_errors), color='green', linestyle='--', linewidth=2,
                          label=f'Median: {np.median(all_errors):.2f}mm')
        axes[0, 0].axvline(5.0, color='orange', linestyle='--', linewidth=2,
                          label='5mm Threshold')
        
        axes[0, 0].set_title('Overall Error Distribution', fontsize=14, fontweight='bold')
        axes[0, 0].set_xlabel('Error (mm)')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 样本间误差对比
        colors = ['red', 'blue', 'green', 'orange']
        bars = axes[0, 1].bar(sample_names, sample_errors, color=colors, alpha=0.7, edgecolor='black')
        axes[0, 1].set_title('Average Error by Sample', fontsize=14, fontweight='bold')
        axes[0, 1].set_ylabel('Average Error (mm)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, error in zip(bars, sample_errors):
            axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.05,
                           f'{error:.2f}mm', ha='center', va='bottom', fontweight='bold')
        
        # 3. 准确率统计
        thresholds = [1, 2, 3, 5, 10]
        accuracies = [(all_errors <= t).mean() * 100 for t in thresholds]
        colors_acc = ['darkred', 'red', 'orange', 'lightgreen', 'green']
        
        bars = axes[1, 0].bar([f'{t}mm' for t in thresholds], accuracies, 
                             color=colors_acc, alpha=0.7, edgecolor='black')
        axes[1, 0].set_title('Accuracy at Different Thresholds', fontsize=14, fontweight='bold')
        axes[1, 0].set_ylabel('Accuracy (%)')
        axes[1, 0].set_ylim(0, 105)
        axes[1, 0].grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, acc in zip(bars, accuracies):
            axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                           f'{acc:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # 4. 关键点误差分析
        # 计算每个关键点的平均误差
        keypoint_errors = np.zeros(57)
        for i in range(57):
            errors_for_kp = [result['error'][i] for result in results]
            keypoint_errors[i] = np.mean(errors_for_kp)
        
        # 找出最好和最差的关键点
        best_kps = np.argsort(keypoint_errors)[:5]
        worst_kps = np.argsort(keypoint_errors)[-5:]
        
        x_pos = np.arange(5)
        width = 0.35
        
        axes[1, 1].bar(x_pos - width/2, keypoint_errors[best_kps], width, 
                      label='Best 5', color='green', alpha=0.7, edgecolor='black')
        axes[1, 1].bar(x_pos + width/2, keypoint_errors[worst_kps], width, 
                      label='Worst 5', color='red', alpha=0.7, edgecolor='black')
        
        axes[1, 1].set_title('Best vs Worst Keypoints', fontsize=14, fontweight='bold')
        axes[1, 1].set_xlabel('Rank')
        axes[1, 1].set_ylabel('Mean Error (mm)')
        axes[1, 1].set_xticks(x_pos)
        axes[1, 1].set_xticklabels([f'{i+1}' for i in range(5)])
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'error_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 误差分布图完成")
        
        return {
            'mean_error': np.mean(all_errors),
            'median_error': np.median(all_errors),
            'std_error': np.std(all_errors),
            'accuracy_5mm': (all_errors <= 5.0).mean() * 100,
            'best_keypoints': best_kps.tolist(),
            'worst_keypoints': worst_kps.tolist(),
            'keypoint_errors': keypoint_errors.tolist()
        }
    
    def create_detailed_sample_view(self, results):
        """创建详细的单样本视图"""
        print("生成详细样本视图...")
        
        # 选择误差最接近平均值的样本
        sample_errors = [np.mean(result['error']) for result in results]
        overall_mean = np.mean(sample_errors)
        best_sample_idx = np.argmin([abs(err - overall_mean) for err in sample_errors])
        
        result = results[best_sample_idx]
        pc = result['point_cloud']
        gt = result['ground_truth']
        pred = result['prediction']
        error = result['error']
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 1. XY平面详细视图
        ax = axes[0, 0]
        
        # 绘制点云
        ax.scatter(pc[:, 0], pc[:, 1], c='lightgray', alpha=0.2, s=2, label='Point Cloud')
        
        # 绘制关键点
        ax.scatter(gt[:, 0], gt[:, 1], c='red', s=60, alpha=0.8, 
                  label='Ground Truth', marker='o', edgecolors='darkred', linewidth=2)
        ax.scatter(pred[:, 0], pred[:, 1], c='blue', s=60, alpha=0.8, 
                  label='Prediction', marker='^', edgecolors='darkblue', linewidth=2)
        
        # 绘制误差向量
        for i in range(len(gt)):
            color = 'red' if error[i] > 5 else 'orange' if error[i] > 3 else 'green'
            ax.annotate('', xy=(pred[i, 0], pred[i, 1]), xytext=(gt[i, 0], gt[i, 1]),
                       arrowprops=dict(arrowstyle='->', color=color, alpha=0.7, lw=2))
        
        ax.set_title(f'{result["sample_id"]} - XY Plane with Error Vectors', 
                    fontsize=14, fontweight='bold')
        ax.set_xlabel('X (mm)')
        ax.set_ylabel('Y (mm)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal')
        
        # 2. XZ平面视图
        ax = axes[0, 1]
        ax.scatter(gt[:, 0], gt[:, 2], c='red', s=60, alpha=0.8, 
                  label='Ground Truth', marker='o', edgecolors='darkred', linewidth=2)
        ax.scatter(pred[:, 0], pred[:, 2], c='blue', s=60, alpha=0.8, 
                  label='Prediction', marker='^', edgecolors='darkblue', linewidth=2)
        
        for i in range(len(gt)):
            color = 'red' if error[i] > 5 else 'orange' if error[i] > 3 else 'green'
            ax.plot([gt[i, 0], pred[i, 0]], [gt[i, 2], pred[i, 2]], 
                   color=color, alpha=0.7, linewidth=2)
        
        ax.set_title('XZ Plane View', fontsize=14, fontweight='bold')
        ax.set_xlabel('X (mm)')
        ax.set_ylabel('Z (mm)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal')
        
        # 3. YZ平面视图
        ax = axes[0, 2]
        ax.scatter(gt[:, 1], gt[:, 2], c='red', s=60, alpha=0.8, 
                  label='Ground Truth', marker='o', edgecolors='darkred', linewidth=2)
        ax.scatter(pred[:, 1], pred[:, 2], c='blue', s=60, alpha=0.8, 
                  label='Prediction', marker='^', edgecolors='darkblue', linewidth=2)
        
        for i in range(len(gt)):
            color = 'red' if error[i] > 5 else 'orange' if error[i] > 3 else 'green'
            ax.plot([gt[i, 1], pred[i, 1]], [gt[i, 2], pred[i, 2]], 
                   color=color, alpha=0.7, linewidth=2)
        
        ax.set_title('YZ Plane View', fontsize=14, fontweight='bold')
        ax.set_xlabel('Y (mm)')
        ax.set_ylabel('Z (mm)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal')
        
        # 4. 误差热图
        ax = axes[1, 0]
        scatter = ax.scatter(gt[:, 0], gt[:, 1], c=error, s=100, 
                           cmap='RdYlGn_r', vmin=0, vmax=8, alpha=0.8,
                           edgecolors='black', linewidth=1)
        
        # 添加关键点编号
        for i in range(len(gt)):
            if error[i] > 4:  # 只标注误差较大的点
                ax.annotate(f'{i}', (gt[i, 0], gt[i, 1]), 
                           xytext=(5, 5), textcoords='offset points',
                           fontsize=8, fontweight='bold', color='white',
                           bbox=dict(boxstyle='round,pad=0.2', facecolor='black', alpha=0.7))
        
        cbar = plt.colorbar(scatter, ax=ax)
        cbar.set_label('Error (mm)', rotation=270, labelpad=15)
        
        ax.set_title('Error Heatmap with Keypoint IDs', fontsize=14, fontweight='bold')
        ax.set_xlabel('X (mm)')
        ax.set_ylabel('Y (mm)')
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal')
        
        # 5. 误差分布
        ax = axes[1, 1]
        ax.hist(error, bins=15, alpha=0.7, color='lightcoral', edgecolor='black')
        ax.axvline(np.mean(error), color='red', linestyle='--', linewidth=2,
                  label=f'Mean: {np.mean(error):.2f}mm')
        ax.axvline(5.0, color='orange', linestyle='--', linewidth=2,
                  label='5mm Threshold')
        
        ax.set_title('Error Distribution for This Sample', fontsize=14, fontweight='bold')
        ax.set_xlabel('Error (mm)')
        ax.set_ylabel('Frequency')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 6. 统计信息
        ax = axes[1, 2]
        ax.axis('off')
        
        stats_text = f"""
Sample: {result['sample_id']}

Error Statistics:
Mean: {np.mean(error):.2f} mm
Std: {np.std(error):.2f} mm
Min: {np.min(error):.2f} mm
Max: {np.max(error):.2f} mm
Median: {np.median(error):.2f} mm

Accuracy:
< 1mm: {(error < 1).sum()}/57 ({(error < 1).mean()*100:.1f}%)
< 2mm: {(error < 2).sum()}/57 ({(error < 2).mean()*100:.1f}%)
< 3mm: {(error < 3).sum()}/57 ({(error < 3).mean()*100:.1f}%)
< 5mm: {(error < 5).sum()}/57 ({(error < 5).mean()*100:.1f}%)

Worst 5 Keypoints:
#{np.argsort(error)[-1]}: {error[np.argsort(error)[-1]]:.2f}mm
#{np.argsort(error)[-2]}: {error[np.argsort(error)[-2]]:.2f}mm
#{np.argsort(error)[-3]}: {error[np.argsort(error)[-3]]:.2f}mm
#{np.argsort(error)[-4]}: {error[np.argsort(error)[-4]]:.2f}mm
#{np.argsort(error)[-5]}: {error[np.argsort(error)[-5]]:.2f}mm

Best 5 Keypoints:
#{np.argsort(error)[0]}: {error[np.argsort(error)[0]]:.2f}mm
#{np.argsort(error)[1]}: {error[np.argsort(error)[1]]:.2f}mm
#{np.argsort(error)[2]}: {error[np.argsort(error)[2]]:.2f}mm
#{np.argsort(error)[3]}: {error[np.argsort(error)[3]]:.2f}mm
#{np.argsort(error)[4]}: {error[np.argsort(error)[4]]:.2f}mm
        """
        
        ax.text(0.05, 0.95, stats_text, transform=ax.transAxes, fontsize=10,
               verticalalignment='top', fontfamily='monospace',
               bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'detailed_sample_view.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 详细样本视图完成")
    
    def run_visualization(self):
        """运行完整的可视化"""
        print("🚀 开始简化预测可视化...")
        
        # 1. 加载模型并预测
        results = self.load_model_and_predict()
        
        # 2. 生成各种可视化
        self.create_2d_comparison_grid(results)
        stats = self.create_error_distribution_plot(results)
        self.create_detailed_sample_view(results)
        
        # 3. 生成总结报告
        with open(self.output_dir / 'visualization_summary.txt', 'w', encoding='utf-8') as f:
            f.write("预测可视化总结报告\n")
            f.write("=" * 30 + "\n\n")
            f.write(f"整体性能:\n")
            f.write(f"平均误差: {stats['mean_error']:.2f} ± {stats['std_error']:.2f}mm\n")
            f.write(f"中位数误差: {stats['median_error']:.2f}mm\n")
            f.write(f"5mm准确率: {stats['accuracy_5mm']:.1f}%\n\n")
            f.write(f"生成的可视化文件:\n")
            f.write(f"- 2d_comparison_grid.png: 4个样本的2D对比网格\n")
            f.write(f"- error_distribution.png: 误差分布和统计分析\n")
            f.write(f"- detailed_sample_view.png: 单个样本的详细多视角分析\n")
        
        print(f"\n🎯 可视化完成!")
        print(f"📁 结果保存在: {self.output_dir}")
        print(f"📊 整体性能: 平均误差 {stats['mean_error']:.2f}mm, 5mm准确率 {stats['accuracy_5mm']:.1f}%")
        
        return results, stats

def main():
    """主函数"""
    visualizer = SimplePredictionVisualizer(data_root="output/training_fixed")
    
    results, stats = visualizer.run_visualization()
    
    print("🎉 简化预测可视化完成!")
    print("生成的图表:")
    print("  📊 2d_comparison_grid.png - 多样本2D对比")
    print("  📈 error_distribution.png - 误差分布分析") 
    print("  🔍 detailed_sample_view.png - 详细单样本分析")

if __name__ == "__main__":
    main()
