#!/usr/bin/env python3
"""
渐进式扩展实施
Progressive Scaling Implementation
从12点开始，逐步扩展到15点、19点等
"""

import sys
import os
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import random

# 添加原始代码路径
sys.path.insert(0, os.path.abspath("archive/old_scripts"))

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

# 导入原始模块
from ensemble_double_softmax_exact import ExactEnsembleDoubleSoftMaxPointNet

class ProgressivePointNet(nn.Module):
    """渐进式PointNet - 支持不同关键点数量"""
    
    def __init__(self, num_keypoints=12, dropout_rate=0.3, num_ensembles=3):
        super(ProgressivePointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        self.num_ensembles = num_ensembles
        
        # 基础特征提取器（固定，可迁移）
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 残差连接
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        # 可变的回归头（根据关键点数量调整）
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(dropout_rate)
        
        # 双Softmax模块（可选，用于推理时精细化）
        self.use_double_softmax = num_keypoints <= 30  # 只在较少点时使用
        
        if self.use_double_softmax:
            from ensemble_double_softmax_exact import ExactDoubleSoftMax
            self.double_softmax_modules = nn.ModuleList([
                ExactDoubleSoftMax(
                    threshold_ratio=0.10 + 0.05 * i,
                    temperature=1.5 + 0.5 * i,
                    weight_ratio=0.7 + 0.1 * i
                ) for i in range(num_ensembles)
            ])
        
        self._initialize_weights()
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"🏗️ ProgressivePointNet({num_keypoints}点): {total_params:,} 参数")
        print(f"   双Softmax: {'启用' if self.use_double_softmax else '禁用'}")
    
    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
    
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取（可迁移部分）
        x1 = F.relu(self.bn1(self.conv1(x)))
        x2 = F.relu(self.bn2(self.conv2(x1)))
        x3 = F.relu(self.bn3(self.conv3(x2)))
        x3_res = x3 + self.residual1(x1)
        
        x4 = F.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = F.relu(self.bn5(self.conv5(x4_res)))
        
        # 全局最大池化
        global_feat = torch.max(x5, 2)[0]  # [B, 1024]
        
        # 回归头
        feat = F.relu(self.bn_fc1(self.fc1(global_feat)))
        feat = self.dropout(feat)
        feat = F.relu(self.bn_fc2(self.fc2(feat)))
        feat = self.dropout(feat)
        feat = F.relu(self.bn_fc3(self.fc3(feat)))
        feat = self.dropout(feat)
        feat = F.relu(self.bn_fc4(self.fc4(feat)))
        feat = self.dropout(feat)
        
        keypoints = self.fc5(feat)
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints
    
    def transfer_from(self, source_model, freeze_features=True):
        """从源模型迁移权重"""
        print(f"🔄 从{source_model.num_keypoints}点模型迁移到{self.num_keypoints}点模型")
        
        # 迁移特征提取器
        feature_layers = [
            'conv1', 'conv2', 'conv3', 'conv4', 'conv5',
            'bn1', 'bn2', 'bn3', 'bn4', 'bn5',
            'residual1', 'residual2'
        ]
        
        for layer_name in feature_layers:
            if hasattr(source_model, layer_name) and hasattr(self, layer_name):
                source_param = getattr(source_model, layer_name).state_dict()
                getattr(self, layer_name).load_state_dict(source_param)
                print(f"   ✅ 迁移 {layer_name}")
        
        # 迁移部分回归头（除了最后一层）
        regression_layers = ['fc1', 'fc2', 'fc3', 'fc4', 'bn_fc1', 'bn_fc2', 'bn_fc3', 'bn_fc4']
        
        for layer_name in regression_layers:
            if hasattr(source_model, layer_name) and hasattr(self, layer_name):
                source_param = getattr(source_model, layer_name).state_dict()
                getattr(self, layer_name).load_state_dict(source_param)
                print(f"   ✅ 迁移 {layer_name}")
        
        # 冻结特征提取器（可选）
        if freeze_features:
            for layer_name in feature_layers:
                if hasattr(self, layer_name):
                    for param in getattr(self, layer_name).parameters():
                        param.requires_grad = False
            print(f"   🔒 冻结特征提取器")

class ProgressiveDataset(Dataset):
    """渐进式数据集"""
    
    def __init__(self, point_clouds, keypoints_57, keypoint_indices):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints_57[:, keypoint_indices, :])
        self.keypoint_indices = keypoint_indices
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def get_keypoint_subsets():
    """获取渐进式关键点子集"""
    
    subsets = {
        12: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17],  # 历史12点
        15: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17, 0, 1, 12],  # 增加3个F1点
        19: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17, 0, 1, 12, 13, 14, 15, 18],  # 完整F3
        24: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17, 0, 1, 12, 13, 14, 15, 18, 19, 20, 21, 22, 23],  # 增加F2
        30: list(range(30)),  # 前30个点
        38: list(range(38)),  # 前38个点
        45: list(range(45)),  # 前45个点
        57: list(range(57))   # 全部57点
    }
    
    return subsets

def train_progressive_model(model, train_loader, val_loader, epochs=80, device='cuda', 
                          target_error=None, model_name="progressive"):
    """训练渐进式模型"""
    
    print(f"🚀 训练{model.num_keypoints}点模型...")
    if target_error:
        print(f"   目标误差: {target_error}mm")
    
    model = model.to(device)
    
    # 自适应学习率（根据关键点数量调整）
    base_lr = 0.0008
    lr_scale = max(0.5, 1.0 - (model.num_keypoints - 12) * 0.02)
    learning_rate = base_lr * lr_scale
    
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=15, min_lr=1e-6
    )
    
    criterion = nn.MSELoss()
    
    best_val_error = float('inf')
    patience_counter = 0
    patience = 25
    history = []
    
    print(f"   学习率: {learning_rate:.2e}")
    print(f"   批次大小: {train_loader.batch_size}")
    
    start_time = time.time()
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            loss.backward()
            
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                val_loss += loss.item()
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_error': train_error,
            'val_error': val_error,
            'lr': current_lr
        })
        
        # 早停检查
        if val_error < best_val_error:
            best_val_error = val_error
            patience_counter = 0
            torch.save(model.state_dict(), f'best_{model_name}_{model.num_keypoints}point.pth')
        else:
            patience_counter += 1
        
        # 打印进度
        if epoch % 10 == 0 or epoch < 5:
            print(f"Epoch {epoch+1:3d}: "
                  f"Train: {train_error:.3f}, Val: {val_error:.3f}, "
                  f"LR: {current_lr:.2e}")
        
        # 目标达成检查
        if target_error and val_error <= target_error:
            print(f"🎉 达到目标误差 {target_error}mm！在第{epoch+1}轮")
            break
        
        if patience_counter >= patience:
            print(f"⏹️ 早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    training_time = time.time() - start_time
    
    # 加载最佳模型
    model.load_state_dict(torch.load(f'best_{model_name}_{model.num_keypoints}point.pth'))
    
    print(f"✅ {model.num_keypoints}点训练完成!")
    print(f"   最佳验证误差: {best_val_error:.3f}")
    print(f"   训练时间: {training_time/60:.1f}分钟")
    
    return model, best_val_error, history

def run_progressive_scaling():
    """运行渐进式扩展"""
    
    print("🎯 开始渐进式扩展实施")
    print("=" * 80)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载数据集
    print("📊 加载数据集...")

    # 尝试加载历史数据集
    try:
        historical_data = np.load('archive/old_experiments/f3_reduced_12kp_stable.npz', allow_pickle=True)
        point_clouds = historical_data['point_clouds']
        keypoints_12_historical = historical_data['keypoints']
        sample_ids = historical_data['sample_ids']
        print(f"✅ 使用历史数据集: {len(sample_ids)}样本")
        use_historical = True

        # 同时加载57点数据集用于扩展
        data_57 = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
        keypoints_57 = data_57['keypoints_57']

    except:
        # 使用57点数据集
        data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
        point_clouds = data['point_clouds']
        keypoints_57 = data['keypoints_57']
        sample_ids = data['sample_ids']
        print(f"✅ 使用57点数据集: {len(sample_ids)}样本")
        use_historical = False
        keypoints_12_historical = None
    
    # 获取关键点子集
    keypoint_subsets = get_keypoint_subsets()
    
    # 渐进式扩展计划
    scaling_plan = [
        {"keypoints": 12, "target_error": 6.0, "epochs": 80},
        {"keypoints": 15, "target_error": 6.5, "epochs": 60},
        {"keypoints": 19, "target_error": 7.2, "epochs": 60},
        {"keypoints": 24, "target_error": 8.0, "epochs": 80},
    ]
    
    results = {}
    previous_model = None
    
    for step in scaling_plan:
        num_keypoints = step["keypoints"]
        target_error = step["target_error"]
        epochs = step["epochs"]
        
        print(f"\n🎯 阶段: {num_keypoints}点扩展")
        print("=" * 50)
        
        # 准备数据
        if use_historical and num_keypoints == 12:
            # 使用历史12点数据
            from sklearn.model_selection import train_test_split
            
            indices = np.arange(len(point_clouds))
            train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
            train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
            
            class Historical12Dataset(Dataset):
                def __init__(self, pc, kp):
                    self.point_clouds = torch.FloatTensor(pc)
                    self.keypoints = torch.FloatTensor(kp)
                def __len__(self):
                    return len(self.point_clouds)
                def __getitem__(self, idx):
                    return self.point_clouds[idx], self.keypoints[idx]
            
            train_dataset = Historical12Dataset(point_clouds[train_indices], keypoints_12_historical[train_indices])
            val_dataset = Historical12Dataset(point_clouds[val_indices], keypoints_12_historical[val_indices])
            
        else:
            # 使用57点数据集的子集
            subset_indices = keypoint_subsets[num_keypoints]
            
            from sklearn.model_selection import train_test_split
            indices = np.arange(len(point_clouds))
            train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
            train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
            
            train_dataset = ProgressiveDataset(point_clouds[train_indices], keypoints_57[train_indices], subset_indices)
            val_dataset = ProgressiveDataset(point_clouds[val_indices], keypoints_57[val_indices], subset_indices)
        
        # 数据加载器
        batch_size = 4
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
        
        print(f"📋 数据: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
        
        # 创建模型
        if num_keypoints == 12 and use_historical:
            # 使用原始架构
            model = ExactEnsembleDoubleSoftMaxPointNet(num_keypoints=12, dropout_rate=0.3, num_ensembles=3)
        else:
            # 使用渐进式架构
            model = ProgressivePointNet(num_keypoints=num_keypoints, dropout_rate=0.3, num_ensembles=3)
            
            # 迁移学习
            if previous_model is not None:
                model.transfer_from(previous_model, freeze_features=(num_keypoints > 19))
        
        # 训练模型
        model, best_error, history = train_progressive_model(
            model, train_loader, val_loader, epochs=epochs, device=device,
            target_error=target_error, model_name=f"progressive_{num_keypoints}"
        )
        
        # 记录结果
        results[num_keypoints] = {
            "best_error": best_error,
            "target_error": target_error,
            "success": best_error <= target_error,
            "history": history
        }
        
        print(f"📊 {num_keypoints}点结果:")
        print(f"   最佳误差: {best_error:.3f}mm")
        print(f"   目标误差: {target_error:.3f}mm")
        print(f"   {'✅ 成功' if best_error <= target_error else '❌ 未达标'}")
        
        # 如果失败，停止扩展
        if best_error > target_error * 1.2:  # 允许20%的容差
            print(f"⚠️ {num_keypoints}点未达标，停止进一步扩展")
            break
        
        # 保存当前模型用于下一步迁移
        previous_model = model
    
    # 保存最终结果
    with open('progressive_scaling_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n🎯 渐进式扩展完成!")
    print(f"💾 结果已保存: progressive_scaling_results.json")
    
    return results

def main():
    """主函数"""
    
    print("🚀 渐进式扩展实施")
    print("从12点开始，逐步扩展到更多关键点")
    print("=" * 80)
    
    set_seed(123)  # 使用历史最佳种子
    
    try:
        results = run_progressive_scaling()
        
        print(f"\n📊 最终总结:")
        for num_keypoints, result in results.items():
            status = "✅ 成功" if result["success"] else "❌ 失败"
            print(f"   {num_keypoints}点: {result['best_error']:.3f}mm (目标{result['target_error']:.3f}mm) {status}")
        
    except Exception as e:
        print(f"❌ 实施失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
