#!/usr/bin/env python3
"""
Minimal GPU Training for F3 Dataset

Ultra-conservative training to avoid segfaults.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import time
import json
import gc

class MinimalF3Dataset(Dataset):
    """Minimal dataset with very small samples"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 512):
        
        self.num_points = num_points
        
        # Load only subset of data
        data = np.load(data_path, allow_pickle=True)
        
        # Use only first 20 samples to avoid memory issues
        point_clouds = data['point_clouds'][:20]
        keypoints = data['keypoints'][:20]
        
        if split == 'train':
            self.point_clouds = point_clouds[:12]
            self.keypoints = keypoints[:12]
        elif split == 'val':
            self.point_clouds = point_clouds[12:16]
            self.keypoints = keypoints[12:16]
        else:  # test
            self.point_clouds = point_clouds[16:20]
            self.keypoints = keypoints[16:20]
        
        print(f"   {split}: {len(self.point_clouds)} 样本, {num_points} 点")
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        # Aggressive downsampling
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints)
        }

class MinimalPointNet(nn.Module):
    """Minimal PointNet to avoid memory issues"""
    
    def __init__(self):
        super(MinimalPointNet, self).__init__()
        
        # Very small network
        self.conv1 = nn.Conv1d(3, 16, 1)
        self.conv2 = nn.Conv1d(16, 32, 1)
        self.conv3 = nn.Conv1d(32, 64, 1)
        
        self.fc1 = nn.Linear(64, 32)
        self.fc2 = nn.Linear(32, 19 * 3)
        
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, x):
        # x: (batch, points, 3)
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # (batch, 3, points)
        
        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))
        x = torch.relu(self.conv3(x))
        
        # Global max pooling
        x = torch.max(x, 2)[0]  # (batch, 64)
        
        x = torch.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.fc2(x)
        
        return x.view(batch_size, 19, 3)

def calculate_distance_error(pred, target):
    """Calculate average distance error"""
    distances = torch.norm(pred - target, dim=2)  # (batch, 19)
    return torch.mean(distances).item()

def train_minimal_gpu():
    """Train minimal model on GPU"""
    
    print("🚀 **最小GPU训练 - F3关键点检测**")
    print("🎯 **目标: 验证GPU训练可行性**")
    print("=" * 60)
    
    # Setup with memory management
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    # Clear GPU cache
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print(f"🧹 清理GPU缓存")
    
    # Minimal dataset
    dataset_path = "high_quality_f3_dataset.npz"
    num_points = 512  # Very small
    
    print(f"📦 创建最小数据集...")
    train_dataset = MinimalF3Dataset(dataset_path, 'train', num_points)
    val_dataset = MinimalF3Dataset(dataset_path, 'val', num_points)
    test_dataset = MinimalF3Dataset(dataset_path, 'test', num_points)
    
    # Minimal batch size
    batch_size = 1  # Single sample per batch
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    print(f"📊 数据统计:")
    print(f"   训练: {len(train_dataset)}, 验证: {len(val_dataset)}, 测试: {len(test_dataset)}")
    print(f"   批大小: {batch_size}, 点云: {num_points} 点")
    
    # Minimal model
    model = MinimalPointNet().to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 最小模型参数: {total_params:,}")
    
    # Training setup
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.01)  # Higher LR for faster convergence
    
    # Short training
    num_epochs = 20
    best_val_error = float('inf')
    history = []
    
    print(f"\n🎯 开始最小训练 ({num_epochs} epochs)")
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        
        # Training
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        for batch_idx, batch in enumerate(train_loader):
            # Clear cache before each batch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                train_error += calculate_distance_error(pred_keypoints, keypoints)
                
            except RuntimeError as e:
                print(f"❌ 训练批次 {batch_idx} 失败: {e}")
                # Clear cache and continue
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                continue
        
        train_loss /= len(train_loader)
        train_error /= len(train_loader)
        
        # Validation
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    
                    val_loss += loss.item()
                    val_error += calculate_distance_error(pred_keypoints, keypoints)
                    
                except RuntimeError as e:
                    print(f"❌ 验证批次失败: {e}")
                    continue
        
        val_loss /= len(val_loader)
        val_error /= len(val_loader)
        
        # Print results
        print(f"训练: Loss={train_loss:.4f}, 误差={train_error:.3f}mm")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_error:.3f}mm")
        
        # Save history
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_error': train_error,
            'val_error': val_error
        })
        
        # Check improvement
        if val_error < best_val_error:
            best_val_error = val_error
            torch.save(model.state_dict(), 'best_minimal_pointnet_f3.pth')
            print(f"🎉 新最佳: {best_val_error:.3f}mm")
        
        # Force cleanup
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    
    # Test evaluation
    print(f"\n🧪 **测试评估**")
    model.load_state_dict(torch.load('best_minimal_pointnet_f3.pth'))
    model.eval()
    
    test_error = 0.0
    test_count = 0
    
    with torch.no_grad():
        for batch in test_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            try:
                pred_keypoints = model(point_cloud)
                error = calculate_distance_error(pred_keypoints, keypoints)
                test_error += error
                test_count += 1
            except RuntimeError as e:
                print(f"❌ 测试批次失败: {e}")
                continue
    
    if test_count > 0:
        test_error /= test_count
    
    print(f"📊 最终结果:")
    print(f"   最佳验证误差: {best_val_error:.3f}mm")
    print(f"   测试误差: {test_error:.3f}mm")
    print(f"   训练时间: {total_time/60:.1f}分钟")
    
    # Assessment
    if test_error <= 5.0:
        print(f"✅ **成功!** GPU训练可行，误差在医疗范围内")
    else:
        print(f"⚠️ **基线结果** GPU训练成功但需要优化")
    
    # Save results
    results = {
        'model_name': 'Minimal_PointNet_F3_GPU',
        'best_val_error_mm': best_val_error,
        'test_error_mm': test_error,
        'training_time_minutes': total_time / 60,
        'total_epochs': num_epochs,
        'model_parameters': total_params,
        'training_history': history,
        'gpu_training_successful': True
    }
    
    with open('minimal_pointnet_f3_gpu_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    return results

if __name__ == "__main__":
    try:
        results = train_minimal_gpu()
        
        print(f"\n🎉 **最小GPU训练完成!**")
        print(f"🎯 验证误差: {results['best_val_error_mm']:.3f}mm")
        print(f"🎯 测试误差: {results['test_error_mm']:.3f}mm")
        print(f"⏱️ 训练时间: {results['training_time_minutes']:.1f}分钟")
        
        if results['test_error_mm'] <= 10.0:
            print(f"✅ **GPU训练成功!** 可以进行更大规模训练")
        else:
            print(f"⚠️ **需要调试** 误差较大")
        
        print(f"\n💡 **下一步**: 基于成功的最小训练，逐步增加:")
        print(f"   1. 点云大小 (512 -> 2048 -> 4096)")
        print(f"   2. 批处理大小 (1 -> 2 -> 4)")
        print(f"   3. 模型复杂度")
        print(f"   4. 训练样本数量")
        
    except Exception as e:
        print(f"❌ 最小训练失败: {e}")
        import traceback
        traceback.print_exc()
