#!/usr/bin/env python3
"""
Debug Segmentation Fault

Identify what's causing the segfault and create a safe version.
"""

import sys
import traceback
import gc

def test_basic_imports():
    """Test basic imports that might cause segfault"""
    
    print("🔍 **测试基本导入**")
    
    try:
        import numpy as np
        print("   ✅ numpy导入成功")
    except Exception as e:
        print(f"   ❌ numpy导入失败: {e}")
        return False
    
    try:
        import pandas as pd
        print("   ✅ pandas导入成功")
    except Exception as e:
        print(f"   ❌ pandas导入失败: {e}")
        return False
    
    try:
        import h5py
        print("   ✅ h5py导入成功")
    except Exception as e:
        print(f"   ❌ h5py导入失败: {e}")
        return False
    
    try:
        import struct
        print("   ✅ struct导入成功")
    except Exception as e:
        print(f"   ❌ struct导入失败: {e}")
        return False
    
    return True

def test_file_operations():
    """Test file operations"""
    
    print("\n🔍 **测试文件操作**")
    
    from pathlib import Path
    
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    
    if not data_dir.exists():
        print(f"   ❌ 数据目录不存在: {data_dir}")
        return False
    
    print(f"   ✅ 数据目录存在: {data_dir}")
    
    # Test CSV file reading
    annotations_dir = data_dir / "annotations"
    csv_files = list(annotations_dir.glob("*-Table-XYZ.CSV"))
    
    print(f"   📊 找到CSV文件: {len(csv_files)}")
    
    if csv_files:
        test_csv = csv_files[0]
        print(f"   🧪 测试读取: {test_csv.name}")
        
        try:
            import pandas as pd
            df = pd.read_csv(test_csv, encoding='gbk')
            print(f"   ✅ CSV读取成功: {len(df)} 行")
        except Exception as e:
            print(f"   ❌ CSV读取失败: {e}")
            return False
    
    return True

def test_stl_reading_safe():
    """Test STL reading with minimal operations"""
    
    print("\n🔍 **测试STL读取 (安全模式)**")
    
    from pathlib import Path
    import struct
    
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    stl_dir = data_dir / "stl_models"
    
    if not stl_dir.exists():
        print(f"   ❌ STL目录不存在: {stl_dir}")
        return False
    
    stl_files = list(stl_dir.glob("*-F_3.stl"))
    
    if not stl_files:
        print(f"   ❌ 没有找到F3 STL文件")
        return False
    
    test_stl = stl_files[0]
    print(f"   🧪 测试读取: {test_stl.name}")
    
    try:
        with open(test_stl, 'rb') as f:
            # 只读取头部信息，不读取所有数据
            header = f.read(80)
            print(f"   ✅ STL头部读取成功: {len(header)} 字节")
            
            # 读取三角形数量
            num_triangles_bytes = f.read(4)
            if len(num_triangles_bytes) == 4:
                num_triangles = struct.unpack('<I', num_triangles_bytes)[0]
                print(f"   ✅ 三角形数量: {num_triangles}")
                
                # 只读取第一个三角形
                if num_triangles > 0:
                    # Skip normal (12 bytes)
                    f.read(12)
                    
                    # Read first vertex
                    vertex_bytes = f.read(12)
                    if len(vertex_bytes) == 12:
                        x, y, z = struct.unpack('<fff', vertex_bytes)
                        print(f"   ✅ 第一个顶点: [{x:.2f}, {y:.2f}, {z:.2f}]")
                    else:
                        print(f"   ❌ 顶点数据不完整")
                        return False
            else:
                print(f"   ❌ 三角形数量读取失败")
                return False
                
    except Exception as e:
        print(f"   ❌ STL读取失败: {e}")
        return False
    
    return True

def test_memory_usage():
    """Test memory usage"""
    
    print("\n🔍 **测试内存使用**")
    
    import psutil
    import os
    
    # Get current process
    process = psutil.Process(os.getpid())
    
    # Memory info
    memory_info = process.memory_info()
    memory_percent = process.memory_percent()
    
    print(f"   📊 当前内存使用:")
    print(f"      RSS: {memory_info.rss / 1024 / 1024:.1f} MB")
    print(f"      VMS: {memory_info.vms / 1024 / 1024:.1f} MB")
    print(f"      百分比: {memory_percent:.1f}%")
    
    # System memory
    system_memory = psutil.virtual_memory()
    print(f"   📊 系统内存:")
    print(f"      总内存: {system_memory.total / 1024 / 1024 / 1024:.1f} GB")
    print(f"      可用内存: {system_memory.available / 1024 / 1024 / 1024:.1f} GB")
    print(f"      使用率: {system_memory.percent:.1f}%")
    
    if system_memory.percent > 90:
        print(f"   ⚠️ 系统内存使用率过高")
        return False
    
    return True

def test_array_operations():
    """Test numpy array operations that might cause segfault"""
    
    print("\n🔍 **测试数组操作**")
    
    import numpy as np
    
    try:
        # Test basic array creation
        arr1 = np.random.rand(1000, 3)
        print(f"   ✅ 创建数组: {arr1.shape}")
        
        # Test array operations
        arr2 = np.random.rand(1000, 3)
        distances = np.linalg.norm(arr1 - arr2, axis=1)
        print(f"   ✅ 距离计算: {len(distances)}")
        
        # Test array indexing
        indices = np.random.choice(len(arr1), 100, replace=False)
        sampled = arr1[indices]
        print(f"   ✅ 数组采样: {sampled.shape}")
        
        # Force garbage collection
        del arr1, arr2, distances, indices, sampled
        gc.collect()
        
    except Exception as e:
        print(f"   ❌ 数组操作失败: {e}")
        return False
    
    return True

def run_comprehensive_test():
    """Run comprehensive test to identify segfault source"""
    
    print("🔍 **段错误诊断测试**")
    print("🎯 **目标: 识别导致段错误的原因**")
    print("=" * 80)
    
    tests = [
        ("基本导入", test_basic_imports),
        ("文件操作", test_file_operations),
        ("STL读取", test_stl_reading_safe),
        ("内存使用", test_memory_usage),
        ("数组操作", test_array_operations)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                print(f"   🎉 {test_name} 测试通过")
            else:
                print(f"   ❌ {test_name} 测试失败")
                
        except Exception as e:
            print(f"   💥 {test_name} 测试崩溃: {e}")
            print(f"   📋 错误详情:")
            traceback.print_exc()
            results[test_name] = False
            break  # Stop at first crash
    
    # Summary
    print(f"\n" + "="*80)
    print(f"📋 **诊断结果总结**")
    print(f"=" * 80)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    # Recommendations
    failed_tests = [name for name, result in results.items() if not result]
    
    if not failed_tests:
        print(f"\n🎉 **所有测试通过，段错误可能由其他原因引起**")
        print(f"   建议:")
        print(f"   1. 检查是否是处理大量数据时的内存问题")
        print(f"   2. 尝试分批处理数据")
        print(f"   3. 使用更保守的内存管理")
    else:
        print(f"\n⚠️ **发现问题测试: {', '.join(failed_tests)}**")
        print(f"   建议:")
        
        if "STL读取" in failed_tests:
            print(f"   1. STL文件可能损坏或格式不正确")
            print(f"   2. 尝试使用其他STL读取库")
            print(f"   3. 检查文件权限和完整性")
        
        if "内存使用" in failed_tests:
            print(f"   1. 系统内存不足")
            print(f"   2. 增加虚拟内存或减少数据量")
            print(f"   3. 使用内存映射文件")
        
        if "数组操作" in failed_tests:
            print(f"   1. numpy版本可能有问题")
            print(f"   2. 尝试重新安装numpy")
            print(f"   3. 检查BLAS库配置")

def main():
    """Main diagnostic function"""
    
    try:
        run_comprehensive_test()
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n💥 诊断过程崩溃: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
