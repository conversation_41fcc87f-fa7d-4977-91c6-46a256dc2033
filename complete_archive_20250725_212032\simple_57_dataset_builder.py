#!/usr/bin/env python3
"""
简化的57关键点数据集构建器
Simplified 57 keypoints dataset builder
"""

import numpy as np
import pandas as pd
import os
from pathlib import Path
import json

def load_existing_point_clouds():
    """加载现有的点云数据"""
    
    print("📊 加载现有点云数据...")
    
    # 查找现有的点云文件
    pc_files = []
    for file in os.listdir('.'):
        if file.endswith('_pointcloud.npy'):
            pc_files.append(file)
    
    print(f"找到 {len(pc_files)} 个点云文件")
    
    point_clouds = {}
    for pc_file in pc_files[:20]:  # 限制处理数量
        sample_id = pc_file.replace('_pointcloud.npy', '')
        try:
            pc = np.load(pc_file)
            point_clouds[sample_id] = pc
            print(f"✅ {sample_id}: {pc.shape}")
        except Exception as e:
            print(f"❌ {sample_id}: {e}")
    
    return point_clouds

def load_57_keypoints_from_csv(sample_id):
    """从CSV文件加载57个关键点"""
    
    data_dir = Path("data/Data/annotations")
    ann_file = data_dir / f"{sample_id}-Table-XYZ.CSV"
    
    if not ann_file.exists():
        return None
    
    try:
        # 尝试不同编码
        for encoding in ['gbk', 'utf-8', 'latin1']:
            try:
                df = pd.read_csv(ann_file, encoding=encoding)
                break
            except:
                continue
        else:
            return None
        
        if len(df) < 57:
            return None
        
        # 提取57个关键点坐标
        keypoints_57 = []
        for idx, row in df.iterrows():
            if idx >= 57:  # 只取前57个
                break
            x, y, z = row['X'], row['Y'], row['Z']
            keypoints_57.append([x, y, z])
        
        return np.array(keypoints_57)
        
    except Exception as e:
        print(f"⚠️ {sample_id}: 关键点加载失败 - {e}")
        return None

def extract_12_from_57(keypoints_57):
    """从57个关键点中提取12个核心关键点"""
    
    # 12点到57点的映射关系
    mapping_12_to_57 = {
        0: 0,   # F1-1 -> 原始索引0
        1: 1,   # F1-2 -> 原始索引1
        2: 2,   # F1-3 -> 原始索引2
        3: 12,  # F1-13 -> 原始索引12
        4: 19,  # F2-1 -> 原始索引19
        5: 20,  # F2-2 -> 原始索引20
        6: 21,  # F2-3 -> 原始索引21
        7: 31,  # F2-13 -> 原始索引31
        8: 38,  # F3-1 -> 原始索引38
        9: 52,  # F3-15 -> 原始索引52
        10: 50, # F3-13 -> 原始索引50
        11: 51, # F3-14 -> 原始索引51
    }
    
    keypoints_12 = np.zeros((12, 3))
    
    for i in range(12):
        original_idx = mapping_12_to_57[i]
        if original_idx < len(keypoints_57):
            keypoints_12[i] = keypoints_57[original_idx]
    
    return keypoints_12

def build_simple_57_dataset():
    """构建简化的57关键点数据集"""
    
    print("🚀 构建简化的57关键点数据集...")
    print("=" * 60)
    
    # 加载现有点云数据
    point_clouds_dict = load_existing_point_clouds()
    
    if not point_clouds_dict:
        print("❌ 没有找到现有的点云数据")
        return
    
    # 准备数据容器
    valid_samples = []
    point_clouds_list = []
    keypoints_57_list = []
    keypoints_12_list = []
    sample_ids_list = []
    
    print(f"\n📋 处理样本...")
    
    # 处理每个有点云的样本
    for sample_id, point_cloud in point_clouds_dict.items():
        print(f"处理样本: {sample_id}")
        
        # 加载57个关键点
        keypoints_57 = load_57_keypoints_from_csv(sample_id)
        
        if keypoints_57 is None:
            print(f"⚠️ {sample_id}: 无法加载57关键点")
            continue
        
        # 提取12个核心关键点
        keypoints_12 = extract_12_from_57(keypoints_57)
        
        # 验证数据
        if point_cloud.shape[0] < 1000:
            print(f"⚠️ {sample_id}: 点云太小 ({point_cloud.shape[0]} < 1000)")
            continue
        
        if keypoints_57.shape[0] != 57:
            print(f"⚠️ {sample_id}: 关键点数量错误 ({keypoints_57.shape[0]} != 57)")
            continue
        
        # 添加到列表
        valid_samples.append(sample_id)
        point_clouds_list.append(point_cloud)
        keypoints_57_list.append(keypoints_57)
        keypoints_12_list.append(keypoints_12)
        sample_ids_list.append(sample_id)
        
        print(f"✅ {sample_id}: 点云{point_cloud.shape}, 57点{keypoints_57.shape}, 12点{keypoints_12.shape}")
    
    if not valid_samples:
        print("❌ 没有有效样本")
        return
    
    # 转换为numpy数组
    point_clouds_array = np.array(point_clouds_list, dtype=object)
    keypoints_57_array = np.array(keypoints_57_list)
    keypoints_12_array = np.array(keypoints_12_list)
    sample_ids_array = np.array(sample_ids_list)
    
    print(f"\n📊 数据集统计:")
    print(f"   有效样本数: {len(valid_samples)}")
    print(f"   点云数组: {len(point_clouds_array)} 个样本")
    print(f"   57关键点: {keypoints_57_array.shape}")
    print(f"   12关键点: {keypoints_12_array.shape}")
    
    # 保存数据集
    print(f"\n💾 保存数据集...")
    
    np.savez('simple_57_dataset.npz',
             point_clouds=point_clouds_array,
             keypoints_57=keypoints_57_array,
             keypoints_12=keypoints_12_array,
             sample_ids=sample_ids_array)
    
    print(f"✅ 数据集已保存: simple_57_dataset.npz")
    
    # 保存映射信息
    mapping_info = {
        'mapping_12_to_57': {
            0: 0, 1: 1, 2: 2, 3: 12, 4: 19, 5: 20,
            6: 21, 7: 31, 8: 38, 9: 52, 10: 50, 11: 51
        },
        'dataset_info': {
            'total_samples': len(valid_samples),
            'keypoints_57': 57,
            'keypoints_12': 12,
            'sample_ids': valid_samples
        }
    }
    
    with open('simple_57_dataset_mapping.json', 'w', encoding='utf-8') as f:
        json.dump(mapping_info, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 映射信息已保存: simple_57_dataset_mapping.json")
    
    return point_clouds_array, keypoints_57_array, keypoints_12_array, sample_ids_array

def validate_57_dataset():
    """验证57关键点数据集"""
    
    print(f"\n🔍 验证57关键点数据集...")
    print("=" * 50)
    
    try:
        data = np.load('simple_57_dataset.npz', allow_pickle=True)
        
        point_clouds = data['point_clouds']
        keypoints_57 = data['keypoints_57']
        keypoints_12 = data['keypoints_12']
        sample_ids = data['sample_ids']
        
        print(f"📊 数据集验证:")
        print(f"   样本数量: {len(sample_ids)}")
        print(f"   57关键点形状: {keypoints_57.shape}")
        print(f"   12关键点形状: {keypoints_12.shape}")
        
        # 验证12点提取的正确性
        print(f"\n✅ 12关键点提取验证:")
        
        # 检查第一个样本
        if len(sample_ids) > 0:
            sample_id = sample_ids[0]
            kp_57 = keypoints_57[0]
            kp_12 = keypoints_12[0]
            
            print(f"   样本 {sample_id}:")
            
            # 验证几个关键映射
            mappings_to_check = [(0, 0), (3, 12), (4, 19), (8, 38)]
            
            for idx_12, idx_57 in mappings_to_check:
                match = np.allclose(kp_12[idx_12], kp_57[idx_57], atol=1e-6)
                print(f"     12点[{idx_12}] vs 57点[{idx_57}]: {match}")
                if not match:
                    print(f"       12点: {kp_12[idx_12]}")
                    print(f"       57点: {kp_57[idx_57]}")
        
        # 检查坐标范围
        print(f"\n📏 坐标范围检查:")
        for axis, axis_name in enumerate(['X', 'Y', 'Z']):
            min_val = keypoints_57[:, :, axis].min()
            max_val = keypoints_57[:, :, axis].max()
            print(f"   {axis_name}轴: {min_val:.1f} ~ {max_val:.1f}")
        
        print(f"\n✅ 数据集验证完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据集验证失败: {e}")
        return False

def create_training_pairs():
    """创建12→57的训练对"""
    
    print(f"\n🔄 创建12→57训练对...")
    print("=" * 50)
    
    try:
        data = np.load('simple_57_dataset.npz', allow_pickle=True)
        
        point_clouds = data['point_clouds']
        keypoints_57 = data['keypoints_57']
        keypoints_12 = data['keypoints_12']
        sample_ids = data['sample_ids']
        
        print(f"📊 创建训练对:")
        print(f"   输入: 12个关键点 {keypoints_12.shape}")
        print(f"   目标: 57个关键点 {keypoints_57.shape}")
        print(f"   样本数: {len(sample_ids)}")
        
        # 保存训练对
        np.savez('expansion_training_pairs.npz',
                 input_12=keypoints_12,
                 target_57=keypoints_57,
                 point_clouds=point_clouds,
                 sample_ids=sample_ids)
        
        print(f"✅ 训练对已保存: expansion_training_pairs.npz")
        
        # 分析扩展复杂度
        print(f"\n📈 扩展复杂度分析:")
        
        # 计算12点之间的平均距离
        distances_12 = []
        for kp_12 in keypoints_12:
            for i in range(12):
                for j in range(i+1, 12):
                    dist = np.linalg.norm(kp_12[i] - kp_12[j])
                    distances_12.append(dist)
        
        avg_dist_12 = np.mean(distances_12)
        
        # 计算57点之间的平均距离
        distances_57 = []
        for kp_57 in keypoints_57[:3]:  # 只计算前3个样本以节省时间
            for i in range(57):
                for j in range(i+1, min(i+10, 57)):  # 只计算邻近点
                    dist = np.linalg.norm(kp_57[i] - kp_57[j])
                    distances_57.append(dist)
        
        avg_dist_57 = np.mean(distances_57)
        
        print(f"   12点平均距离: {avg_dist_12:.2f}mm")
        print(f"   57点平均距离: {avg_dist_57:.2f}mm")
        print(f"   密度增加: {avg_dist_12/avg_dist_57:.2f}倍")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练对创建失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🎯 简化的57关键点数据集构建器")
    print("基于现有点云数据构建57关键点训练集")
    print("=" * 80)
    
    # 步骤1: 构建数据集
    result = build_simple_57_dataset()
    
    if result is not None:
        # 步骤2: 验证数据集
        if validate_57_dataset():
            # 步骤3: 创建训练对
            if create_training_pairs():
                print(f"\n🎉 57关键点数据集构建完成！")
                print(f"📋 生成的文件:")
                print(f"   - simple_57_dataset.npz (完整数据集)")
                print(f"   - simple_57_dataset_mapping.json (映射关系)")
                print(f"   - expansion_training_pairs.npz (训练对)")
                
                print(f"\n🚀 下一步:")
                print(f"   1. 实现12→57扩展网络")
                print(f"   2. 训练扩展模型")
                print(f"   3. 评估扩展效果")
                print(f"   4. 端到端57点模型训练")
            else:
                print(f"❌ 训练对创建失败")
        else:
            print(f"❌ 数据集验证失败")
    else:
        print(f"❌ 数据集构建失败")

if __name__ == "__main__":
    main()
