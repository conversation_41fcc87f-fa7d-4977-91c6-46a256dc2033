#!/usr/bin/env python3
"""
精密优化系统
Precision Optimization System
基于最佳实践的精细化改进
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import json
from tqdm import tqdm
import itertools

class PrecisionDatasetOptimizer:
    """精密数据集优化器"""
    
    def __init__(self, dataset_path='high_quality_pelvis_57_dataset.npz'):
        self.dataset_path = dataset_path
        self.load_dataset()
        
    def load_dataset(self):
        """加载最佳数据集"""
        print("📊 加载最佳高质量数据集...")
        data = np.load(self.dataset_path, allow_pickle=True)
        
        self.point_clouds = data['point_clouds']
        self.keypoints_57 = data['keypoints_57']
        self.sample_ids = data['sample_ids']
        
        print(f"✅ 数据集加载完成: {len(self.sample_ids)} 个样本")
        
    def analyze_regional_performance(self):
        """分析区域性能差异"""
        print("🔍 分析F1、F2、F3区域特征...")
        
        regional_stats = {'F1': [], 'F2': [], 'F3': []}
        
        for i in range(len(self.keypoints_57)):
            kp = self.keypoints_57[i]
            
            # 分析各区域的几何特征
            f1_points = kp[0:19]    # F1区域
            f2_points = kp[19:38]   # F2区域  
            f3_points = kp[38:57]   # F3区域
            
            for region_name, points in [('F1', f1_points), ('F2', f2_points), ('F3', f3_points)]:
                # 计算区域特征
                center = np.mean(points, axis=0)
                spread = np.mean([np.linalg.norm(p - center) for p in points])
                bbox_size = np.ptp(points, axis=0)
                
                regional_stats[region_name].append({
                    'center': center,
                    'spread': spread,
                    'bbox_size': bbox_size,
                    'sample_id': self.sample_ids[i]
                })
        
        # 分析区域差异
        for region in ['F1', 'F2', 'F3']:
            spreads = [s['spread'] for s in regional_stats[region]]
            print(f"   {region}区域平均分散度: {np.mean(spreads):.2f}mm")
        
        return regional_stats
    
    def create_region_aware_normalization(self):
        """创建区域感知的归一化"""
        print("🔧 创建区域感知归一化...")
        
        normalized_pc = []
        normalized_kp = []
        region_scalers = []
        
        for i in range(len(self.point_clouds)):
            pc = self.point_clouds[i].copy()
            kp = self.keypoints_57[i].copy()
            
            # 分别处理每个区域
            f1_kp = kp[0:19]
            f2_kp = kp[19:38]
            f3_kp = kp[38:57]
            
            # 为每个区域创建独立的归一化
            sample_scalers = {}
            normalized_regions = []
            
            for region_name, region_kp in [('F1', f1_kp), ('F2', f2_kp), ('F3', f3_kp)]:
                # 结合点云和该区域关键点进行归一化
                combined_data = np.vstack([pc, region_kp])
                
                scaler = StandardScaler()
                combined_normalized = scaler.fit_transform(combined_data)
                
                # 只取关键点部分
                region_normalized = combined_normalized[len(pc):]
                normalized_regions.append(region_normalized)
                sample_scalers[region_name] = scaler
            
            # 合并归一化后的关键点
            kp_normalized = np.vstack(normalized_regions)
            
            # 使用F3区域的scaler处理点云（因为F3表现最好）
            pc_combined = np.vstack([pc, f3_kp])
            pc_normalized = sample_scalers['F3'].fit_transform(pc_combined)[:len(pc)]
            
            normalized_pc.append(pc_normalized)
            normalized_kp.append(kp_normalized)
            region_scalers.append(sample_scalers)
        
        normalized_pc = np.array(normalized_pc)
        normalized_kp = np.array(normalized_kp)
        
        print(f"✅ 区域感知归一化完成")
        
        return normalized_pc, normalized_kp, region_scalers

class RegionAwarePointNet57(nn.Module):
    """区域感知PointNet57"""
    
    def __init__(self, num_keypoints=57, dropout_rate=0.2):
        super(RegionAwarePointNet57, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 共享特征提取器
        self.shared_conv1 = nn.Conv1d(3, 64, 1)
        self.shared_conv2 = nn.Conv1d(64, 128, 1)
        self.shared_conv3 = nn.Conv1d(128, 256, 1)
        self.shared_conv4 = nn.Conv1d(256, 512, 1)
        self.shared_conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.shared_bn1 = nn.BatchNorm1d(64)
        self.shared_bn2 = nn.BatchNorm1d(128)
        self.shared_bn3 = nn.BatchNorm1d(256)
        self.shared_bn4 = nn.BatchNorm1d(512)
        self.shared_bn5 = nn.BatchNorm1d(1024)
        
        # 区域特定的回归头
        self.f1_head = self._create_region_head(1024, 19)  # F1: 19个关键点
        self.f2_head = self._create_region_head(1024, 19)  # F2: 19个关键点
        self.f3_head = self._create_region_head(1024, 19)  # F3: 19个关键点
        
        # Dropout
        self.dropout = nn.Dropout(dropout_rate)
        
        # 权重初始化
        self._initialize_weights()
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"🏗️ RegionAwarePointNet57: {total_params:,} 参数")
    
    def _create_region_head(self, input_dim, num_points):
        """创建区域特定的回归头"""
        return nn.Sequential(
            nn.Linear(input_dim, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, num_points * 3)
        )
    
    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 共享特征提取
        x = F.relu(self.shared_bn1(self.shared_conv1(x)))
        x = F.relu(self.shared_bn2(self.shared_conv2(x)))
        x = F.relu(self.shared_bn3(self.shared_conv3(x)))
        x = F.relu(self.shared_bn4(self.shared_conv4(x)))
        x = F.relu(self.shared_bn5(self.shared_conv5(x)))
        
        # 全局最大池化
        global_feat = torch.max(x, 2)[0]  # [B, 1024]
        
        # 区域特定预测
        f1_pred = self.f1_head(global_feat).view(batch_size, 19, 3)
        f2_pred = self.f2_head(global_feat).view(batch_size, 19, 3)
        f3_pred = self.f3_head(global_feat).view(batch_size, 19, 3)
        
        # 合并预测结果
        keypoints = torch.cat([f1_pred, f2_pred, f3_pred], dim=1)  # [B, 57, 3]
        
        return keypoints, (f1_pred, f2_pred, f3_pred)

class RegionWeightedLoss(nn.Module):
    """区域加权损失函数"""
    
    def __init__(self, f1_weight=1.2, f2_weight=1.2, f3_weight=0.8):
        super(RegionWeightedLoss, self).__init__()
        self.f1_weight = f1_weight
        self.f2_weight = f2_weight
        self.f3_weight = f3_weight
        
    def forward(self, pred_all, pred_regions, target):
        # 整体损失
        overall_loss = F.mse_loss(pred_all, target)
        
        # 区域特定损失
        f1_pred, f2_pred, f3_pred = pred_regions
        
        f1_loss = F.mse_loss(f1_pred, target[:, 0:19]) * self.f1_weight
        f2_loss = F.mse_loss(f2_pred, target[:, 19:38]) * self.f2_weight
        f3_loss = F.mse_loss(f3_pred, target[:, 38:57]) * self.f3_weight
        
        total_loss = overall_loss + f1_loss + f2_loss + f3_loss
        
        return total_loss

class HyperparameterOptimizer:
    """超参数优化器"""
    
    def __init__(self):
        self.param_grid = {
            'learning_rate': [0.0005, 0.0008, 0.001, 0.0012],
            'dropout_rate': [0.15, 0.2, 0.25, 0.3],
            'weight_decay': [5e-5, 1e-4, 2e-4],
            'batch_size': [6, 8, 10]
        }
    
    def optimize_hyperparameters(self, train_data, val_data, max_trials=12):
        """优化超参数"""
        print(f"🔧 开始超参数优化 (最多{max_trials}次试验)...")
        
        # 生成参数组合
        param_names = list(self.param_grid.keys())
        param_values = list(self.param_grid.values())
        
        # 随机采样参数组合
        import random
        all_combinations = list(itertools.product(*param_values))
        random.shuffle(all_combinations)
        
        best_params = None
        best_score = float('inf')
        results = []
        
        for trial, params in enumerate(all_combinations[:max_trials]):
            param_dict = dict(zip(param_names, params))
            
            print(f"\n🔍 试验 {trial+1}/{max_trials}: {param_dict}")
            
            # 快速训练验证
            score = self._quick_train_eval(train_data, val_data, param_dict)
            
            results.append({
                'params': param_dict,
                'score': score
            })
            
            if score < best_score:
                best_score = score
                best_params = param_dict
                print(f"   ✅ 新的最佳结果: {score:.4f}")
            else:
                print(f"   📊 当前结果: {score:.4f}")
        
        print(f"\n🏆 超参数优化完成!")
        print(f"   最佳参数: {best_params}")
        print(f"   最佳分数: {best_score:.4f}")
        
        return best_params, results
    
    def _quick_train_eval(self, train_data, val_data, params):
        """快速训练评估"""
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 创建数据加载器
        train_loader = DataLoader(train_data, batch_size=params['batch_size'], 
                                shuffle=True, drop_last=True)
        val_loader = DataLoader(val_data, batch_size=params['batch_size'], 
                              shuffle=False, drop_last=True)
        
        # 创建模型
        model = RegionAwarePointNet57(dropout_rate=params['dropout_rate']).to(device)
        optimizer = optim.AdamW(model.parameters(), 
                               lr=params['learning_rate'], 
                               weight_decay=params['weight_decay'])
        criterion = RegionWeightedLoss()
        
        # 快速训练 (15个epoch)
        model.train()
        for epoch in range(15):
            for batch_pc, batch_kp in train_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                optimizer.zero_grad()
                pred_all, pred_regions = model(batch_pc)
                loss = criterion(pred_all, pred_regions, batch_kp)
                loss.backward()
                
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
        
        # 验证
        model.eval()
        val_error = 0.0
        count = 0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                pred_all, _ = model(batch_pc)
                distances = torch.norm(pred_all - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
                count += 1
        
        return val_error / count if count > 0 else float('inf')

def main():
    """主函数"""
    
    print("🎯 精密优化系统")
    print("基于最佳实践的精细化改进")
    print("=" * 80)
    
    # 创建精密数据集优化器
    optimizer = PrecisionDatasetOptimizer()
    
    # 分析区域性能
    regional_stats = optimizer.analyze_regional_performance()
    
    # 创建区域感知归一化
    normalized_pc, normalized_kp, region_scalers = optimizer.create_region_aware_normalization()
    
    # 数据划分
    indices = np.arange(len(normalized_pc))
    train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
    train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
    
    # 创建数据集
    class Dataset57(Dataset):
        def __init__(self, point_clouds, keypoints):
            self.point_clouds = torch.FloatTensor(point_clouds)
            self.keypoints = torch.FloatTensor(keypoints)
        
        def __len__(self):
            return len(self.point_clouds)
        
        def __getitem__(self, idx):
            return self.point_clouds[idx], self.keypoints[idx]
    
    train_dataset = Dataset57(normalized_pc[train_indices], normalized_kp[train_indices])
    val_dataset = Dataset57(normalized_pc[val_indices], normalized_kp[val_indices])
    test_dataset = Dataset57(normalized_pc[test_indices], normalized_kp[test_indices])
    
    print(f"📋 数据划分: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    
    # 超参数优化
    hp_optimizer = HyperparameterOptimizer()
    best_params, hp_results = hp_optimizer.optimize_hyperparameters(
        train_dataset, val_dataset, max_trials=8
    )
    
    print(f"\n🎉 精密优化系统完成!")
    print(f"💡 关键改进:")
    print(f"   ✅ 区域感知归一化")
    print(f"   ✅ 区域特定回归头")
    print(f"   ✅ 区域加权损失")
    print(f"   ✅ 超参数优化")
    
    # 保存优化结果
    optimization_results = {
        'best_hyperparameters': best_params,
        'hyperparameter_trials': hp_results,
        'regional_statistics': regional_stats,
        'optimization_strategy': 'region_aware_precision_optimization'
    }
    
    with open('precision_optimization_results.json', 'w') as f:
        json.dump(optimization_results, f, indent=2, default=str)
    
    print(f"\n💾 优化结果已保存: precision_optimization_results.json")
    print(f"🚀 下一步: 使用最佳参数训练完整模型")

if __name__ == "__main__":
    main()
