import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import cv2
import matplotlib.pyplot as plt
from typing import List, Tuple
import json
import os
import random
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
matplotlib.rcParams['axes.unicode_minus'] = False

def create_yolo_annotation(points: List[Tuple[int, int]], class_id: int, image_size: Tuple[int, int]) -> List[str]:
    """
    将点坐标转换为YOLO格式的标注
    Args:
        points: 点坐标列表 [(x,y),...]
        class_id: 类别ID（0:奇点，1:偶点）
        image_size: 图像大小 (width, height)
    Returns:
        YOLO格式的标注行列表
    """
    annotations = []
    for x, y in points:
        # YOLO格式：<class> <x_center> <y_center> <width> <height>
        # 转换为归一化坐标
        x_center = x / image_size[0]
        y_center = y / image_size[1]
        # 使用固定大小的框（这里用10x10像素）
        width = 10 / image_size[0]
        height = 10 / image_size[1]
        
        annotations.append(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}")
    return annotations

def generate_random_connected_shapes(image_dir: str, num_images: int, image_size: Tuple[int, int] = (256, 256)):
    """
    生成随机连通图形和对应的YOLO格式标注
    """
    # 创建标注目录
    label_dir = os.path.join(os.path.dirname(image_dir), 'labels')
    os.makedirs(label_dir, exist_ok=True)
    
    for i in range(num_images):
        # 创建空白图像
        image = np.zeros(image_size, dtype=np.uint8)
        
        # 生成基础闭合图形（正方形或矩形）
        margin = 40
        width = random.randint(100, image_size[0]-2*margin)
        height = random.randint(100, image_size[1]-2*margin)
        start_x = random.randint(margin, image_size[0]-width-margin)
        start_y = random.randint(margin, image_size[1]-height-margin)
        
        # 画矩形
        points = [
            (start_x, start_y),
            (start_x + width, start_y),
            (start_x + width, start_y + height),
            (start_x, start_y + height)
        ]
        
        # 连接矩形的边
        for j in range(len(points)):
            cv2.line(image, points[j], points[(j+1)%4], 255, 2)
        
        # 添加对角线或内部线条以创建交叉点
        cv2.line(image, points[0], points[2], 255, 2)  # 一条对角线
        
        # 随机添加额外的内部线条
        num_extra_lines = random.randint(1, 2)
        for _ in range(num_extra_lines):
            p1_edge = random.randint(0, 3)
            p2_edge = (p1_edge + 2) % 4
            
            if p1_edge % 2 == 0:  # 水平边
                x1 = min(points[p1_edge][0], points[(p1_edge+1)%4][0])
                x2 = max(points[p1_edge][0], points[(p1_edge+1)%4][0])
                y1 = points[p1_edge][1]
                
                x_start = min(points[p2_edge][0], points[(p2_edge+1)%4][0])
                x_end = max(points[p2_edge][0], points[(p2_edge+1)%4][0])
                y2 = points[p2_edge][1]
                
                start_point = (random.randint(x1, x2), y1)
                end_point = (random.randint(x_start, x_end), y2)
            else:  # 垂直边
                x1 = points[p1_edge][0]
                y1 = min(points[p1_edge][1], points[(p1_edge+1)%4][1])
                y2 = max(points[p1_edge][1], points[(p1_edge+1)%4][1])
                
                x2 = points[p2_edge][0]
                y_start = min(points[p2_edge][1], points[(p2_edge+1)%4][1])
                y_end = max(points[p2_edge][1], points[(p2_edge+1)%4][1])
                
                start_point = (x1, random.randint(y1, y2))
                end_point = (x2, random.randint(y_start, y_end))
            
            cv2.line(image, start_point, end_point, 255, 2)
        
        # 保存图像
        image_name = f'image_{i}.png'
        image_path = os.path.join(image_dir, image_name)
        cv2.imwrite(image_path, image)
        
        # 显示图像以便标注
        plt.figure(figsize=(8, 8))
        plt.imshow(image, cmap='gray')
        plt.title(f'图像 {i}\n请点击标注：左键-奇点，右键-偶点，中键-完成')
        
        # 收集标注点
        singular_points = []  # 奇点
        even_points = []     # 偶点
        
        def onclick(event):
            if event.button == 1:  # 左键：奇点
                singular_points.append((int(event.xdata), int(event.ydata)))
                plt.plot(event.xdata, event.ydata, 'ro', markersize=10)
                plt.draw()
            elif event.button == 3:  # 右键：偶点
                even_points.append((int(event.xdata), int(event.ydata)))
                plt.plot(event.xdata, event.ydata, 'bo', markersize=10)
                plt.draw()
            elif event.button == 2:  # 中键：完成标注
                plt.close()
        
        # 连接点击事件
        plt.connect('button_press_event', onclick)
        plt.show()
        
        # 生成YOLO格式标注
        annotations = []
        annotations.extend(create_yolo_annotation(singular_points, 0, image_size))  # 奇点类别为0
        annotations.extend(create_yolo_annotation(even_points, 1, image_size))      # 偶点类别为1
        
        # 保存标注文件
        label_name = f'image_{i}.txt'
        label_path = os.path.join(label_dir, label_name)
        with open(label_path, 'w') as f:
            f.write('\n'.join(annotations))
        
        print(f"已保存图像和标注：{image_name}")

if __name__ == "__main__":
    # 创建数据集目录结构
    dataset_dir = 'dataset'
    image_dir = os.path.join(dataset_dir, 'images')
    os.makedirs(image_dir, exist_ok=True)
    
    # 生成数据集
    num_images = 10
    generate_random_connected_shapes(image_dir, num_images)
    
    # 创建数据集配置文件
    with open(os.path.join(dataset_dir, 'data.yaml'), 'w') as f:
        yaml_content = f"""
path: {os.path.abspath(dataset_dir)}
train: images  # train images
val: images    # val images

# Classes
names:
  0: singular  # 奇点
  1: even      # 偶点
"""
        f.write(yaml_content)
    
    print(f"""
数据集生成完成！
- 图像保存在：{image_dir}
- 标注保存在：{os.path.join(dataset_dir, 'labels')}
- 配置文件：{os.path.join(dataset_dir, 'data.yaml')}

使用方法：
1. 使用左键标注奇点（红色）
2. 使用右键标注偶点（蓝色）
3. 使用中键完成当前图像的标注
""")