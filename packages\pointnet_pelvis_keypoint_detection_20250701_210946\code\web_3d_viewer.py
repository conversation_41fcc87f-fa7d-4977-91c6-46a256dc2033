"""
基于Web的交互式3D查看器
使用plotly创建更流畅的交互式3D可视化
"""

import torch
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.offline as pyo
from pathlib import Path
import json

from save_best_model import BestSimplePointNet
from improved_data_loader import ImprovedDataLoader

class Web3DViewer:
    """基于Web的3D查看器"""
    
    def __init__(self, data_root="output/training_fixed"):
        self.data_root = data_root
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 创建输出目录
        self.output_dir = Path("output/web_3d_viewer")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🌐 Web 3D查看器初始化")
        
    def load_model_and_data(self):
        """加载模型和数据"""
        print("加载模型和数据...")
        
        # 加载模型
        model = BestSimplePointNet(num_keypoints=57)
        model_path = "output/scale_corrected_training/best_baseline_model.pth"
        
        if Path(model_path).exists():
            checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"✅ 模型加载成功")
        
        model = model.to(self.device)
        model.eval()
        
        # 加载数据
        data_loader_manager = ImprovedDataLoader(
            data_root=self.data_root,
            batch_size=1,
            num_workers=0,
            num_points=512
        )
        
        _, val_loader = data_loader_manager.create_dataloaders(train_ratio=0.8)
        
        # 获取样本进行预测
        samples = []
        print("进行预测...")
        with torch.no_grad():
            for i, (point_cloud, keypoints) in enumerate(val_loader):
                if i >= 6:  # 加载6个样本
                    break
                
                point_cloud = point_cloud.to(self.device)
                keypoints = keypoints.to(self.device)
                
                pred_keypoints = model(point_cloud)
                error = torch.norm(pred_keypoints - keypoints, dim=2).cpu().numpy()
                
                sample_data = {
                    'id': i,
                    'point_cloud': point_cloud.cpu().numpy()[0],
                    'ground_truth': keypoints.cpu().numpy()[0],
                    'prediction': pred_keypoints.cpu().numpy()[0],
                    'error': error[0],
                    'mean_error': np.mean(error[0]),
                    'accuracy_5mm': (error[0] <= 5.0).mean() * 100
                }
                
                samples.append(sample_data)
                print(f"样本 {i+1}: 误差 {sample_data['mean_error']:.2f}mm, 准确率 {sample_data['accuracy_5mm']:.1f}%")
        
        print(f"✅ 加载了 {len(samples)} 个样本")
        return samples
    
    def create_single_sample_plot(self, sample, show_point_cloud=True):
        """创建单个样本的3D图表"""
        pc = sample['point_cloud']
        gt = sample['ground_truth']
        pred = sample['prediction']
        error = sample['error']
        
        fig = go.Figure()
        
        # 1. 添加点云（如果启用）
        if show_point_cloud:
            fig.add_trace(go.Scatter3d(
                x=pc[:, 0], y=pc[:, 1], z=pc[:, 2],
                mode='markers',
                marker=dict(
                    size=1,
                    color='lightgray',
                    opacity=0.1
                ),
                name='Point Cloud',
                hoverinfo='skip'
            ))
        
        # 2. 添加真实关键点
        fig.add_trace(go.Scatter3d(
            x=gt[:, 0], y=gt[:, 1], z=gt[:, 2],
            mode='markers',
            marker=dict(
                size=8,
                color='red',
                symbol='circle',
                line=dict(width=2, color='darkred')
            ),
            name='Ground Truth',
            hovertemplate='<b>Ground Truth</b><br>' +
                         'Point: %{text}<br>' +
                         'X: %{x:.2f}mm<br>' +
                         'Y: %{y:.2f}mm<br>' +
                         'Z: %{z:.2f}mm<extra></extra>',
            text=[f'#{i}' for i in range(len(gt))]
        ))
        
        # 3. 添加预测关键点（根据误差着色）
        fig.add_trace(go.Scatter3d(
            x=pred[:, 0], y=pred[:, 1], z=pred[:, 2],
            mode='markers',
            marker=dict(
                size=8,
                color=error,
                colorscale='RdYlGn_r',
                cmin=0,
                cmax=8,
                symbol='diamond',
                line=dict(width=1, color='black'),
                colorbar=dict(
                    title=dict(text="Error (mm)", side="right"),
                    tickmode="linear",
                    tick0=0,
                    dtick=1
                )
            ),
            name='Prediction',
            hovertemplate='<b>Prediction</b><br>' +
                         'Point: %{text}<br>' +
                         'X: %{x:.2f}mm<br>' +
                         'Y: %{y:.2f}mm<br>' +
                         'Z: %{z:.2f}mm<br>' +
                         'Error: %{marker.color:.2f}mm<extra></extra>',
            text=[f'#{i}' for i in range(len(pred))]
        ))
        
        # 4. 添加连接线
        for i in range(len(gt)):
            # 根据误差设置线条颜色
            if error[i] <= 2:
                line_color = 'green'
            elif error[i] <= 5:
                line_color = 'orange'
            else:
                line_color = 'red'
            
            fig.add_trace(go.Scatter3d(
                x=[gt[i, 0], pred[i, 0]],
                y=[gt[i, 1], pred[i, 1]],
                z=[gt[i, 2], pred[i, 2]],
                mode='lines',
                line=dict(
                    color=line_color,
                    width=3,
                    dash='solid' if error[i] <= 5 else 'dash'
                ),
                showlegend=False,
                hoverinfo='skip'
            ))
        
        # 5. 设置布局
        fig.update_layout(
            title=dict(
                text=f'Sample {sample["id"]+1} - Interactive 3D View<br>' +
                     f'<sub>Mean Error: {sample["mean_error"]:.2f}mm, ' +
                     f'5mm Accuracy: {sample["accuracy_5mm"]:.1f}%</sub>',
                x=0.5,
                font=dict(size=16)
            ),
            scene=dict(
                xaxis_title='X (mm)',
                yaxis_title='Y (mm)',
                zaxis_title='Z (mm)',
                camera=dict(
                    eye=dict(x=1.5, y=1.5, z=1.5)
                ),
                aspectmode='cube'
            ),
            width=1000,
            height=800,
            margin=dict(l=0, r=0, t=50, b=0)
        )
        
        return fig
    
    def create_comparison_dashboard(self, samples):
        """创建对比仪表板"""
        print("创建对比仪表板...")
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=3,
            specs=[[{'type': 'scatter3d'}, {'type': 'scatter3d'}, {'type': 'scatter3d'}],
                   [{'type': 'scatter3d'}, {'type': 'scatter3d'}, {'type': 'scatter3d'}]],
            subplot_titles=[f'Sample {i+1} (Error: {samples[i]["mean_error"]:.2f}mm)' 
                           for i in range(min(6, len(samples)))],
            vertical_spacing=0.1,
            horizontal_spacing=0.05
        )
        
        # 为每个样本添加数据
        for idx, sample in enumerate(samples[:6]):
            row = idx // 3 + 1
            col = idx % 3 + 1
            
            gt = sample['ground_truth']
            pred = sample['prediction']
            error = sample['error']
            
            # 添加真实关键点
            fig.add_trace(
                go.Scatter3d(
                    x=gt[:, 0], y=gt[:, 1], z=gt[:, 2],
                    mode='markers',
                    marker=dict(size=4, color='red'),
                    name=f'GT_{idx+1}',
                    showlegend=(idx == 0)
                ),
                row=row, col=col
            )
            
            # 添加预测关键点
            fig.add_trace(
                go.Scatter3d(
                    x=pred[:, 0], y=pred[:, 1], z=pred[:, 2],
                    mode='markers',
                    marker=dict(
                        size=4, 
                        color=error,
                        colorscale='RdYlGn_r',
                        cmin=0, cmax=8,
                        showscale=(idx == 0)
                    ),
                    name=f'Pred_{idx+1}',
                    showlegend=(idx == 0)
                ),
                row=row, col=col
            )
        
        # 更新布局
        fig.update_layout(
            title='Multi-Sample Comparison Dashboard',
            height=800,
            showlegend=True
        )
        
        return fig
    
    def create_error_analysis_plot(self, samples):
        """创建误差分析图表"""
        print("创建误差分析图表...")
        
        # 收集所有误差数据
        all_errors = []
        sample_ids = []
        keypoint_ids = []
        
        for sample in samples:
            for i, error in enumerate(sample['error']):
                all_errors.append(error)
                sample_ids.append(f"Sample {sample['id']+1}")
                keypoint_ids.append(i)
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=['Error Distribution', 'Error by Sample', 
                           'Error by Keypoint', 'Accuracy Statistics'],
            specs=[[{'type': 'histogram'}, {'type': 'box'}],
                   [{'type': 'scatter'}, {'type': 'bar'}]]
        )
        
        # 1. 误差分布直方图
        fig.add_trace(
            go.Histogram(x=all_errors, nbinsx=30, name='Error Distribution'),
            row=1, col=1
        )
        
        # 2. 按样本的误差箱线图
        for sample in samples:
            fig.add_trace(
                go.Box(y=sample['error'], name=f'S{sample["id"]+1}'),
                row=1, col=2
            )
        
        # 3. 按关键点的误差散点图
        fig.add_trace(
            go.Scatter(
                x=keypoint_ids, y=all_errors,
                mode='markers',
                marker=dict(color=all_errors, colorscale='RdYlGn_r'),
                name='Keypoint Errors'
            ),
            row=2, col=1
        )
        
        # 4. 准确率统计
        thresholds = [1, 2, 3, 5, 10]
        accuracies = [(np.array(all_errors) <= t).mean() * 100 for t in thresholds]
        
        fig.add_trace(
            go.Bar(
                x=[f'{t}mm' for t in thresholds],
                y=accuracies,
                name='Accuracy',
                marker_color=['red', 'orange', 'yellow', 'lightgreen', 'green']
            ),
            row=2, col=2
        )
        
        # 更新布局
        fig.update_layout(
            title='Error Analysis Dashboard',
            height=800,
            showlegend=False
        )
        
        return fig
    
    def generate_html_reports(self, samples):
        """生成HTML报告"""
        print("生成HTML报告...")
        
        # 1. 为每个样本创建单独的3D图表
        for i, sample in enumerate(samples):
            fig = self.create_single_sample_plot(sample)
            
            # 保存为HTML
            html_file = self.output_dir / f'sample_{i+1}_3d_view.html'
            pyo.plot(fig, filename=str(html_file), auto_open=False)
            print(f"✅ 样本 {i+1} 3D视图已保存: {html_file}")
        
        # 2. 创建对比仪表板
        comparison_fig = self.create_comparison_dashboard(samples)
        comparison_html = self.output_dir / 'comparison_dashboard.html'
        pyo.plot(comparison_fig, filename=str(comparison_html), auto_open=False)
        print(f"✅ 对比仪表板已保存: {comparison_html}")
        
        # 3. 创建误差分析图表
        error_fig = self.create_error_analysis_plot(samples)
        error_html = self.output_dir / 'error_analysis.html'
        pyo.plot(error_fig, filename=str(error_html), auto_open=False)
        print(f"✅ 误差分析已保存: {error_html}")
        
        # 4. 创建主页面
        self.create_main_page(samples)
        
        return {
            'individual_views': [f'sample_{i+1}_3d_view.html' for i in range(len(samples))],
            'comparison_dashboard': 'comparison_dashboard.html',
            'error_analysis': 'error_analysis.html',
            'main_page': 'index.html'
        }
    
    def create_main_page(self, samples):
        """创建主页面"""
        print("创建主页面...")
        
        # 计算统计信息
        all_errors = [sample['mean_error'] for sample in samples]
        all_accuracies = [sample['accuracy_5mm'] for sample in samples]
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PointNet 骨盆关键点检测 - 交互式3D查看器</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .stat-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }}
        .stat-value {{
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        .stat-label {{
            font-size: 0.9em;
            opacity: 0.9;
        }}
        .samples-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .sample-card {{
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background-color: #fafafa;
            transition: transform 0.2s;
        }}
        .sample-card:hover {{
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }}
        .sample-title {{
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }}
        .sample-stats {{
            margin-bottom: 15px;
        }}
        .sample-stat {{
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }}
        .view-button {{
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            transition: opacity 0.2s;
        }}
        .view-button:hover {{
            opacity: 0.8;
        }}
        .dashboard-links {{
            text-align: center;
            margin-top: 30px;
        }}
        .dashboard-link {{
            display: inline-block;
            margin: 10px;
            padding: 15px 30px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: transform 0.2s;
        }}
        .dashboard-link:hover {{
            transform: scale(1.05);
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 PointNet 骨盆关键点检测 - 交互式3D查看器</h1>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{len(samples)}</div>
                <div class="stat-label">测试样本</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{np.mean(all_errors):.2f}mm</div>
                <div class="stat-label">平均误差</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{np.mean(all_accuracies):.1f}%</div>
                <div class="stat-label">平均5mm准确率</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{np.min(all_errors):.2f}mm</div>
                <div class="stat-label">最佳误差</div>
            </div>
        </div>
        
        <h2>📊 样本详情</h2>
        <div class="samples-grid">
"""
        
        # 添加每个样本的卡片
        for i, sample in enumerate(samples):
            html_content += f"""
            <div class="sample-card">
                <div class="sample-title">样本 {i+1}</div>
                <div class="sample-stats">
                    <div class="sample-stat">
                        <span>平均误差:</span>
                        <span>{sample['mean_error']:.2f}mm</span>
                    </div>
                    <div class="sample-stat">
                        <span>5mm准确率:</span>
                        <span>{sample['accuracy_5mm']:.1f}%</span>
                    </div>
                    <div class="sample-stat">
                        <span>最大误差:</span>
                        <span>{np.max(sample['error']):.2f}mm</span>
                    </div>
                    <div class="sample-stat">
                        <span>最小误差:</span>
                        <span>{np.min(sample['error']):.2f}mm</span>
                    </div>
                </div>
                <a href="sample_{i+1}_3d_view.html" class="view-button">🎮 查看3D交互视图</a>
            </div>
"""
        
        html_content += f"""
        </div>
        
        <div class="dashboard-links">
            <h2>🔍 分析仪表板</h2>
            <a href="comparison_dashboard.html" class="dashboard-link">
                📊 多样本对比仪表板
            </a>
            <a href="error_analysis.html" class="dashboard-link">
                📈 误差分析仪表板
            </a>
        </div>
        
        <div style="margin-top: 40px; padding: 20px; background-color: #e8f4fd; border-radius: 8px;">
            <h3>🎮 操作说明</h3>
            <ul>
                <li><strong>鼠标拖动:</strong> 旋转3D视图</li>
                <li><strong>滚轮:</strong> 缩放视图</li>
                <li><strong>双击:</strong> 重置视图</li>
                <li><strong>悬停:</strong> 查看详细信息</li>
                <li><strong>图例:</strong> 点击切换显示/隐藏</li>
            </ul>
        </div>
        
        <div style="margin-top: 20px; text-align: center; color: #666;">
            <p>🎉 PointNet 骨盆关键点检测系统 - 交互式3D可视化</p>
            <p>平均误差: {np.mean(all_errors):.2f}mm | 平均准确率: {np.mean(all_accuracies):.1f}%</p>
        </div>
    </div>
</body>
</html>
"""
        
        # 保存主页面
        main_page = self.output_dir / 'index.html'
        with open(main_page, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ 主页面已保存: {main_page}")
    
    def run_web_viewer(self):
        """运行Web查看器"""
        print("🚀 启动Web 3D查看器...")
        
        # 1. 加载数据
        samples = self.load_model_and_data()
        
        # 2. 生成HTML报告
        files = self.generate_html_reports(samples)
        
        # 3. 计算统计信息
        all_errors = [sample['mean_error'] for sample in samples]
        all_accuracies = [sample['accuracy_5mm'] for sample in samples]
        
        stats = {
            'num_samples': len(samples),
            'mean_error': np.mean(all_errors),
            'std_error': np.std(all_errors),
            'mean_accuracy': np.mean(all_accuracies),
            'files': files
        }
        
        print(f"\n🌐 Web 3D查看器已生成!")
        print(f"📁 文件保存在: {self.output_dir}")
        print(f"📊 统计信息:")
        print(f"  • 样本数量: {stats['num_samples']}")
        print(f"  • 平均误差: {stats['mean_error']:.2f} ± {stats['std_error']:.2f}mm")
        print(f"  • 平均准确率: {stats['mean_accuracy']:.1f}%")
        print(f"\n🎮 打开主页面开始浏览:")
        print(f"  {self.output_dir / 'index.html'}")
        
        return stats

def main():
    """主函数"""
    try:
        viewer = Web3DViewer(data_root="output/training_fixed")
        stats = viewer.run_web_viewer()
        
        # 自动打开主页面
        main_page = viewer.output_dir / 'index.html'
        print(f"\n🚀 正在打开主页面...")
        
        return stats
        
    except ImportError:
        print("❌ 需要安装plotly: pip install plotly")
        return None
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
