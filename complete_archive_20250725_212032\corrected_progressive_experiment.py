#!/usr/bin/env python3
"""
修正的渐进式关键点实验
Corrected Progressive Keypoint Experiment
使用原始高性能架构进行正确的对比
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
import json
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class OriginalHighPerformanceModel(nn.Module):
    """原始高性能模型架构 - 与6.60mm基线完全一致"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super().__init__()
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 点云特征提取器 (与原始架构一致)
        self.point_conv1 = nn.Conv1d(3, 64, 1)
        self.point_conv2 = nn.Conv1d(64, 128, 1)
        self.point_conv3 = nn.Conv1d(128, 256, 1)
        self.point_conv4 = nn.Conv1d(256, 512, 1)
        self.point_conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化层
        self.point_bn1 = nn.BatchNorm1d(64)
        self.point_bn2 = nn.BatchNorm1d(128)
        self.point_bn3 = nn.BatchNorm1d(256)
        self.point_bn4 = nn.BatchNorm1d(512)
        self.point_bn5 = nn.BatchNorm1d(1024)
        
        # 全局特征提取
        self.global_conv1 = nn.Conv1d(1024, 512, 1)
        self.global_conv2 = nn.Conv1d(512, 256, 1)
        self.global_bn1 = nn.BatchNorm1d(512)
        self.global_bn2 = nn.BatchNorm1d(256)
        
        # 关键点预测头
        self.keypoint_fc1 = nn.Linear(256, 512)
        self.keypoint_fc2 = nn.Linear(512, 256)
        self.keypoint_fc3 = nn.Linear(256, num_keypoints * 3)
        
        # Dropout层
        self.dropout1 = nn.Dropout(0.3)
        self.dropout2 = nn.Dropout(0.4)
        
    def forward(self, point_cloud):
        batch_size = point_cloud.size(0)
        
        # 点云特征提取
        x = point_cloud.transpose(2, 1)  # [B, 3, N]
        
        x = torch.relu(self.point_bn1(self.point_conv1(x)))
        x = torch.relu(self.point_bn2(self.point_conv2(x)))
        x = torch.relu(self.point_bn3(self.point_conv3(x)))
        x = torch.relu(self.point_bn4(self.point_conv4(x)))
        x = torch.relu(self.point_bn5(self.point_conv5(x)))
        
        # 全局最大池化
        global_feature = torch.max(x, 2)[0]  # [B, 1024]
        
        # 全局特征处理
        x = global_feature.unsqueeze(2)  # [B, 1024, 1]
        x = torch.relu(self.global_bn1(self.global_conv1(x)))
        x = torch.relu(self.global_bn2(self.global_conv2(x)))
        x = x.squeeze(2)  # [B, 256]
        
        # 关键点预测
        x = torch.relu(self.keypoint_fc1(x))
        x = self.dropout1(x)
        x = torch.relu(self.keypoint_fc2(x))
        x = self.dropout2(x)
        keypoints = self.keypoint_fc3(x)
        
        # 重塑为关键点坐标
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

class CorrectedProgressiveExperiment:
    """修正的渐进式实验"""
    
    def __init__(self, device='cuda:1'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        self.experiment_results = []
        
    def load_real_data(self):
        """加载真实数据"""
        print("📥 加载真实12关键点数据")
        print("=" * 50)
        
        try:
            # 加载女性数据
            female_data = np.load('archive/old_experiments/f3_reduced_12kp_female.npz')
            female_pc = female_data['point_clouds']
            female_kp = female_data['keypoints']
            
            # 加载男性数据
            male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz')
            male_pc = male_data['point_clouds']
            male_kp = male_data['keypoints']
            
            # 合并数据
            all_pc = np.vstack([female_pc, male_pc])
            all_kp = np.vstack([female_kp, male_kp])
            
            print(f"✅ 数据加载成功:")
            print(f"   总样本: {len(all_pc)}")
            print(f"   关键点: {all_kp.shape[1]}个")
            
            return all_pc, all_kp
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return None, None
    
    def create_keypoint_subsets(self, keypoints_12):
        """创建关键点子集"""
        print("\n🔢 创建关键点子集")
        print("=" * 50)
        
        keypoint_configs = {
            3: {
                'indices': [0, 5, 11],
                'description': '极简配置 (首中尾)'
            },
            6: {
                'indices': [0, 2, 4, 7, 9, 11],
                'description': '基础配置 (均匀分布)'
            },
            9: {
                'indices': [0, 1, 3, 4, 6, 7, 8, 10, 11],
                'description': '中等配置 (密集采样)'
            },
            12: {
                'indices': list(range(12)),
                'description': '完整配置 (全部关键点)'
            }
        }
        
        keypoint_subsets = {}
        
        for num_kp, config in keypoint_configs.items():
            indices = config['indices']
            subset_kp = keypoints_12[:, indices, :]
            keypoint_subsets[num_kp] = {
                'keypoints': subset_kp,
                'indices': indices,
                'description': config['description']
            }
            
            print(f"  {num_kp}关键点: {config['description']}")
            print(f"    索引: {indices}")
        
        return keypoint_subsets
    
    def train_original_architecture(self, point_clouds, keypoints, num_keypoints, config_info):
        """使用原始高性能架构训练"""
        print(f"\n🎯 训练{num_keypoints}关键点模型 (原始架构)")
        print(f"   配置: {config_info['description']}")
        print("=" * 50)
        
        # 数据分割
        train_pc, test_pc, train_kp, test_kp = train_test_split(
            point_clouds, keypoints, test_size=0.2, random_state=42)
        
        print(f"📊 数据分割:")
        print(f"   训练: {len(train_pc)}样本")
        print(f"   测试: {len(test_pc)}样本")
        
        # 创建原始架构模型
        model = OriginalHighPerformanceModel(num_points=50000, num_keypoints=num_keypoints).to(self.device)
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        
        print(f"🏗️ 原始架构模型:")
        print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 转换为张量
        train_pc_tensor = torch.FloatTensor(train_pc).to(self.device)
        train_kp_tensor = torch.FloatTensor(train_kp).to(self.device)
        test_pc_tensor = torch.FloatTensor(test_pc).to(self.device)
        test_kp_tensor = torch.FloatTensor(test_kp).to(self.device)
        
        # 创建数据加载器
        batch_size = max(2, min(8, len(train_pc) // 4))
        train_dataset = TensorDataset(train_pc_tensor, train_kp_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        
        # 训练循环
        model.train()
        best_loss = float('inf')
        patience = 0
        
        for epoch in range(100):
            epoch_loss = 0.0
            
            for batch_pc, batch_kp in train_loader:
                optimizer.zero_grad()
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                loss.backward()
                optimizer.step()
                epoch_loss += loss.item()
            
            avg_loss = epoch_loss / len(train_loader)
            scheduler.step(avg_loss)
            
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience = 0
                torch.save(model.state_dict(), f'best_original_model_{num_keypoints}kp.pth')
            else:
                patience += 1
                if patience >= 15:
                    print(f"早停于epoch {epoch+1}")
                    break
            
            if epoch % 20 == 0:
                print(f"Epoch {epoch+1}: Loss = {avg_loss:.6f}")
        
        # 加载最佳模型并测试
        model.load_state_dict(torch.load(f'best_original_model_{num_keypoints}kp.pth'))
        model.eval()
        
        with torch.no_grad():
            predicted = model(test_pc_tensor)
            test_errors = torch.norm(predicted - test_kp_tensor, dim=2)
            avg_error = torch.mean(test_errors).item()
            
            # 计算准确率
            sample_errors = torch.mean(test_errors, dim=1)
            errors_5mm = torch.sum(sample_errors <= 5.0).item()
            errors_10mm = torch.sum(sample_errors <= 10.0).item()
            
            acc_5mm = (errors_5mm / len(test_pc)) * 100
            acc_10mm = (errors_10mm / len(test_pc)) * 100
        
        result = {
            'num_keypoints': num_keypoints,
            'description': config_info['description'],
            'keypoint_indices': config_info['indices'],
            'architecture': 'original_high_performance',
            'train_samples': len(train_pc),
            'test_samples': len(test_pc),
            'avg_error': avg_error,
            'accuracy_5mm': acc_5mm,
            'accuracy_10mm': acc_10mm,
            'medical_grade': avg_error <= 10.0,
            'excellent_grade': avg_error <= 5.0,
            'parameters': sum(p.numel() for p in model.parameters()),
            'epochs_trained': epoch + 1
        }
        
        print(f"\n📊 {num_keypoints}关键点原始架构结果:")
        print(f"   平均误差: {result['avg_error']:.2f}mm")
        print(f"   5mm准确率: {result['accuracy_5mm']:.1f}%")
        print(f"   10mm准确率: {result['accuracy_10mm']:.1f}%")
        print(f"   医疗级达标: {'✅' if result['medical_grade'] else '❌'}")
        print(f"   优秀级达标: {'✅' if result['excellent_grade'] else '❌'}")
        print(f"   参数数量: {result['parameters']:,}")
        
        self.experiment_results.append(result)
        return result
    
    def run_corrected_experiment(self):
        """运行修正的实验"""
        print("🚀 开始修正的渐进式关键点实验")
        print("使用原始高性能架构确保公平对比")
        print("=" * 70)
        
        # 加载数据
        point_clouds, keypoints_12 = self.load_real_data()
        if point_clouds is None:
            print("❌ 数据加载失败，退出")
            return
        
        # 创建关键点子集
        keypoint_subsets = self.create_keypoint_subsets(keypoints_12)
        
        # 逐步训练不同数量的关键点模型
        for num_kp in sorted(keypoint_subsets.keys()):
            config = keypoint_subsets[num_kp]
            keypoints = config['keypoints']
            
            try:
                self.train_original_architecture(point_clouds, keypoints, num_kp, config)
            except Exception as e:
                print(f"❌ {num_kp}关键点训练失败: {e}")
                continue
        
        # 保存结果
        self.save_corrected_results()
        
        return self.experiment_results
    
    def save_corrected_results(self):
        """保存修正的实验结果"""
        results = {
            'experiment_type': 'corrected_progressive_keypoint_scaling',
            'description': '使用原始高性能架构的修正实验',
            'architecture': 'original_high_performance_model',
            'baseline_reference': '6.60mm (SimplifiedUniversalModel)',
            'results': self.experiment_results,
            'architecture_details': {
                'feature_extractor': '5层卷积 (64→128→256→512→1024)',
                'global_features': '1024维',
                'prediction_head': '3层全连接 (256→512→256→输出)',
                'total_parameters': '约240万 (12关键点)'
            },
            'comparison_note': '与之前简化架构的对比，验证架构一致性的重要性',
            'timestamp': '2025-07-25'
        }
        
        with open('corrected_progressive_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 修正实验结果已保存到 corrected_progressive_results.json")
    
    def create_comparison_analysis(self):
        """创建对比分析"""
        print("\n📈 创建架构对比分析")
        print("=" * 50)
        
        if not self.experiment_results:
            print("❌ 没有实验结果")
            return
        
        # 提取数据
        keypoint_counts = [r['num_keypoints'] for r in self.experiment_results]
        avg_errors = [r['avg_error'] for r in self.experiment_results]
        
        # 之前简化架构的结果 (用于对比)
        previous_results = {
            3: 9.06,
            6: 8.32,
            9: 7.11,
            12: 7.19
        }
        
        previous_errors = [previous_results[kp] for kp in keypoint_counts]
        
        # 创建对比图
        plt.figure(figsize=(12, 8))
        
        x = np.arange(len(keypoint_counts))
        width = 0.35
        
        bars1 = plt.bar(x - width/2, avg_errors, width, label='原始高性能架构', color='blue', alpha=0.8)
        bars2 = plt.bar(x + width/2, previous_errors, width, label='简化架构', color='red', alpha=0.8)
        
        plt.axhline(y=10, color='orange', linestyle='--', linewidth=2, label='医疗级标准 (10mm)')
        plt.axhline(y=5, color='green', linestyle='--', linewidth=2, label='优秀标准 (5mm)')
        
        plt.xlabel('关键点数量', fontsize=12)
        plt.ylabel('平均误差 (mm)', fontsize=12)
        plt.title('架构对比：原始 vs 简化', fontsize=14, fontweight='bold')
        plt.xticks(x, [f'{kp}kp' for kp in keypoint_counts])
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (bar1, bar2, orig, simp) in enumerate(zip(bars1, bars2, avg_errors, previous_errors)):
            plt.text(bar1.get_x() + bar1.get_width()/2, bar1.get_height() + 0.1,
                    f'{orig:.2f}mm', ha='center', va='bottom', fontweight='bold')
            plt.text(bar2.get_x() + bar2.get_width()/2, bar2.get_height() + 0.1,
                    f'{simp:.2f}mm', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('architecture_comparison_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✅ 架构对比分析图表已保存为 architecture_comparison_analysis.png")
        
        # 计算改进幅度
        improvements = [(prev - curr) for curr, prev in zip(avg_errors, previous_errors)]
        
        print(f"\n📊 架构对比结果:")
        for i, kp in enumerate(keypoint_counts):
            print(f"  {kp}关键点: {avg_errors[i]:.2f}mm vs {previous_errors[i]:.2f}mm (改进{improvements[i]:.2f}mm)")

def main():
    """主函数"""
    print("🎯 修正的渐进式关键点实验")
    print("Corrected Progressive Keypoint Experiment")
    print("=" * 60)
    
    # 创建修正实验器
    experimenter = CorrectedProgressiveExperiment()
    
    # 运行修正实验
    results = experimenter.run_corrected_experiment()
    
    if results:
        # 创建对比分析
        experimenter.create_comparison_analysis()
        
        print(f"\n🎉 修正实验完成:")
        print(f"✅ 使用原始高性能架构")
        print(f"✅ 确保公平的架构对比")
        print(f"✅ 验证架构一致性的重要性")
        print(f"🎯 预期: 性能应该接近或优于6.60mm基线")
    else:
        print("❌ 实验过程中出现问题")

if __name__ == "__main__":
    main()
