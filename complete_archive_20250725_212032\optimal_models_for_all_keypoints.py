#!/usr/bin/env python3
"""
为不同关键点数量找到最佳性能模型
Optimal Models for All Keypoint Counts
系统性地为3-57个关键点找到各自的最佳模型架构
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split
import json
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class AdaptiveKeypointModel(nn.Module):
    """自适应关键点模型 - 根据关键点数量自动调整架构"""
    
    def __init__(self, num_points=50000, num_keypoints=12, architecture_type='auto'):
        super().__init__()
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        self.architecture_type = architecture_type
        
        # 根据关键点数量选择最佳架构
        if architecture_type == 'auto':
            if num_keypoints <= 6:
                self.arch_type = 'lightweight'
            elif num_keypoints <= 12:
                self.arch_type = 'balanced'
            elif num_keypoints <= 28:
                self.arch_type = 'enhanced'
            else:
                self.arch_type = 'deep'
        else:
            self.arch_type = architecture_type
        
        # 构建对应架构
        self._build_architecture()
    
    def _build_architecture(self):
        """根据类型构建架构"""
        
        if self.arch_type == 'lightweight':
            # 轻量级架构 (3-6关键点)
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(256, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, 128), nn.ReLU(), nn.Dropout(0.1),
                nn.Linear(128, self.num_keypoints * 3)
            )
            
        elif self.arch_type == 'balanced':
            # 平衡架构 (7-12关键点)
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(512, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, self.num_keypoints * 3)
            )
            
        elif self.arch_type == 'enhanced':
            # 增强架构 (13-28关键点)
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
                nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(1024, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 1024), nn.ReLU(), nn.Dropout(0.4),
                nn.Linear(1024, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, self.num_keypoints * 3)
            )
            
        else:  # deep
            # 深度架构 (29-57关键点)
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
                nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU(),
                nn.Conv1d(1024, 2048, 1), nn.BatchNorm1d(2048), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(2048, 1024, 1)
            self.predictor = nn.Sequential(
                nn.Linear(1024, 2048), nn.ReLU(), nn.Dropout(0.5),
                nn.Linear(2048, 1024), nn.ReLU(), nn.Dropout(0.4),
                nn.Linear(1024, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, self.num_keypoints * 3)
            )
        
        # 相互辅助机制 (所有架构都有)
        mutual_dim = min(256, max(64, self.num_keypoints * 8))
        self.mutual_assistance = nn.Sequential(
            nn.Linear(self.num_keypoints * 3, mutual_dim),
            nn.ReLU(), nn.Dropout(0.2),
            nn.Linear(mutual_dim, mutual_dim // 2),
            nn.ReLU(),
            nn.Linear(mutual_dim // 2, self.num_keypoints * 3)
        )
    
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取
        features = self.feature_extractor(x)
        
        # 全局特征
        global_features = self.global_conv(features)
        global_feat = torch.max(global_features, 2)[0]
        
        # 初始预测
        initial_kp = self.predictor(global_feat)
        
        # 相互辅助
        assistance = self.mutual_assistance(initial_kp)
        
        # 最终预测
        final_kp = initial_kp + 0.3 * assistance
        final_kp = final_kp.view(batch_size, self.num_keypoints, 3)
        
        return final_kp

class OptimalModelFinder:
    """最佳模型查找器"""
    
    def __init__(self, device='cuda:1'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        self.results = {}
        
    def load_base_data(self):
        """加载基础12关键点数据"""
        print("📥 加载基础数据")
        print("=" * 50)
        
        try:
            # 加载12关键点数据
            female_data = np.load('archive/old_experiments/f3_reduced_12kp_female.npz')
            female_pc = female_data['point_clouds']
            female_kp = female_data['keypoints']
            
            male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz')
            male_pc = male_data['point_clouds']
            male_kp = male_data['keypoints']
            
            # 合并数据
            all_pc = np.vstack([female_pc, male_pc])
            all_kp = np.vstack([female_kp, male_kp])
            
            print(f"✅ 基础数据加载成功:")
            print(f"   样本数: {len(all_pc)}")
            print(f"   基础关键点: {all_kp.shape[1]}个")
            
            return all_pc, all_kp
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return None, None
    
    def generate_keypoint_configurations(self, base_keypoints):
        """生成不同数量的关键点配置"""
        print("\n🔢 生成关键点配置")
        print("=" * 50)
        
        # 定义关键点数量序列
        keypoint_counts = [3, 6, 9, 12, 15, 19, 24, 28, 33, 38, 43, 47, 52, 57]
        
        configurations = {}
        
        for num_kp in keypoint_counts:
            if num_kp <= 12:
                # 从12个关键点中采样
                if num_kp == 12:
                    indices = list(range(12))
                elif num_kp == 9:
                    indices = [0, 1, 3, 4, 6, 7, 8, 10, 11]
                elif num_kp == 6:
                    indices = [0, 2, 4, 7, 9, 11]
                elif num_kp == 3:
                    indices = [0, 5, 11]
                else:
                    # 均匀采样
                    indices = np.linspace(0, 11, num_kp, dtype=int).tolist()
                
                keypoints = base_keypoints[:, indices, :]
                
            else:
                # 扩展到更多关键点 (通过插值和扩展)
                keypoints = self.expand_keypoints(base_keypoints, num_kp)
            
            configurations[num_kp] = {
                'keypoints': keypoints,
                'indices': indices if num_kp <= 12 else 'expanded',
                'description': f'{num_kp}关键点配置'
            }
            
            print(f"  {num_kp}关键点: 形状{keypoints.shape}")
        
        return configurations
    
    def expand_keypoints(self, base_keypoints, target_num):
        """扩展关键点到目标数量"""
        # 简化的扩展策略：通过插值和添加噪声
        base_num = base_keypoints.shape[1]
        expanded_keypoints = []
        
        for sample in base_keypoints:
            # 基础关键点
            expanded = list(sample)
            
            # 添加插值点
            while len(expanded) < target_num:
                # 在现有点之间插值
                for i in range(len(expanded) - 1):
                    if len(expanded) >= target_num:
                        break
                    # 插值点
                    interp_point = (expanded[i] + expanded[i + 1]) / 2
                    # 添加小量随机噪声
                    noise = np.random.normal(0, 1, 3)
                    interp_point += noise
                    expanded.insert(i + 1, interp_point)
            
            # 截取到目标数量
            expanded = expanded[:target_num]
            expanded_keypoints.append(np.array(expanded))
        
        return np.array(expanded_keypoints)
    
    def find_optimal_model(self, point_clouds, keypoints, num_keypoints, config_info):
        """为特定关键点数量找到最佳模型"""
        print(f"\n🎯 寻找{num_keypoints}关键点最佳模型")
        print("=" * 50)
        
        # 数据分割
        train_pc, test_pc, train_kp, test_kp = train_test_split(
            point_clouds, keypoints, test_size=0.2, random_state=42)
        
        print(f"📊 数据分割: 训练{len(train_pc)}, 测试{len(test_pc)}")
        
        # 测试不同架构
        architectures = ['lightweight', 'balanced', 'enhanced', 'deep', 'auto']
        best_result = None
        best_error = float('inf')
        
        for arch_type in architectures:
            try:
                print(f"\n  测试{arch_type}架构...")
                
                # 创建模型
                model = AdaptiveKeypointModel(
                    num_points=50000, 
                    num_keypoints=num_keypoints, 
                    architecture_type=arch_type
                ).to(self.device)
                
                # 训练和评估
                result = self.train_and_evaluate(
                    model, train_pc, train_kp, test_pc, test_kp, 
                    f"{num_keypoints}kp_{arch_type}"
                )
                
                result['architecture'] = arch_type
                result['num_keypoints'] = num_keypoints
                result['config_info'] = config_info
                
                print(f"    {arch_type}: {result['avg_error']:.2f}mm")
                
                # 更新最佳结果
                if result['avg_error'] < best_error:
                    best_error = result['avg_error']
                    best_result = result
                    
            except Exception as e:
                print(f"    {arch_type}架构失败: {e}")
                continue
        
        if best_result:
            print(f"\n🏆 {num_keypoints}关键点最佳架构: {best_result['architecture']}")
            print(f"   最佳性能: {best_result['avg_error']:.2f}mm")
            print(f"   参数数量: {best_result['parameters']:,}")
            
            self.results[num_keypoints] = best_result
        
        return best_result
    
    def train_and_evaluate(self, model, train_pc, train_kp, test_pc, test_kp, model_name):
        """训练和评估模型"""
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=8, factor=0.5)
        
        # 转换为张量
        train_pc_tensor = torch.FloatTensor(train_pc).to(self.device)
        train_kp_tensor = torch.FloatTensor(train_kp).to(self.device)
        test_pc_tensor = torch.FloatTensor(test_pc).to(self.device)
        test_kp_tensor = torch.FloatTensor(test_kp).to(self.device)
        
        # 数据加载器
        batch_size = max(2, min(8, len(train_pc) // 4))
        train_dataset = TensorDataset(train_pc_tensor, train_kp_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        
        # 训练
        model.train()
        best_loss = float('inf')
        patience = 0
        
        for epoch in range(60):  # 适中的训练轮数
            epoch_loss = 0.0
            
            for batch_pc, batch_kp in train_loader:
                optimizer.zero_grad()
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                loss.backward()
                optimizer.step()
                epoch_loss += loss.item()
            
            avg_loss = epoch_loss / len(train_loader)
            scheduler.step(avg_loss)
            
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience = 0
                torch.save(model.state_dict(), f'best_{model_name}.pth')
            else:
                patience += 1
                if patience >= 12:
                    break
        
        # 评估
        model.load_state_dict(torch.load(f'best_{model_name}.pth'))
        model.eval()
        
        with torch.no_grad():
            predicted = model(test_pc_tensor)
            test_errors = torch.norm(predicted - test_kp_tensor, dim=2)
            avg_error = torch.mean(test_errors).item()
            
            sample_errors = torch.mean(test_errors, dim=1)
            errors_5mm = torch.sum(sample_errors <= 5.0).item()
            errors_10mm = torch.sum(sample_errors <= 10.0).item()
            
            acc_5mm = (errors_5mm / len(test_pc)) * 100
            acc_10mm = (errors_10mm / len(test_pc)) * 100
        
        return {
            'avg_error': avg_error,
            'accuracy_5mm': acc_5mm,
            'accuracy_10mm': acc_10mm,
            'medical_grade': avg_error <= 10.0,
            'excellent_grade': avg_error <= 5.0,
            'parameters': sum(p.numel() for p in model.parameters()),
            'epochs_trained': epoch + 1
        }
    
    def run_comprehensive_search(self):
        """运行全面搜索"""
        print("🚀 开始全面最佳模型搜索")
        print("为3-57个关键点找到各自的最佳架构")
        print("=" * 70)
        
        # 加载数据
        point_clouds, base_keypoints = self.load_base_data()
        if point_clouds is None:
            return
        
        # 生成配置
        configurations = self.generate_keypoint_configurations(base_keypoints)
        
        # 为每个配置找最佳模型
        for num_kp in sorted(configurations.keys()):
            config = configurations[num_kp]
            keypoints = config['keypoints']
            
            try:
                self.find_optimal_model(point_clouds, keypoints, num_kp, config)
            except Exception as e:
                print(f"❌ {num_kp}关键点搜索失败: {e}")
                continue
        
        # 保存结果
        self.save_comprehensive_results()
        
        # 创建分析
        self.create_comprehensive_analysis()
        
        return self.results
    
    def save_comprehensive_results(self):
        """保存综合结果"""
        # 清理结果，移除不能JSON序列化的对象
        cleaned_results = {}
        for kp_num, result in self.results.items():
            cleaned_result = {}
            for key, value in result.items():
                if key == 'config_info':
                    # 跳过config_info，因为它可能包含numpy数组
                    continue
                elif hasattr(value, 'item'):  # numpy scalar
                    cleaned_result[key] = value.item()
                elif hasattr(value, 'tolist'):  # numpy array
                    cleaned_result[key] = value.tolist()
                else:
                    cleaned_result[key] = value
            cleaned_results[kp_num] = cleaned_result

        results_summary = {
            'experiment_type': 'comprehensive_optimal_model_search',
            'description': '为3-57个关键点找到各自的最佳模型架构',
            'total_configurations': len(cleaned_results),
            'keypoint_range': f"{min(cleaned_results.keys())}-{max(cleaned_results.keys())}",
            'results': cleaned_results,
            'best_overall': min(cleaned_results.values(), key=lambda x: x['avg_error']) if cleaned_results else None,
            'timestamp': '2025-07-25'
        }

        with open('comprehensive_optimal_models_results.json', 'w', encoding='utf-8') as f:
            json.dump(results_summary, f, ensure_ascii=False, indent=2)

        print(f"\n💾 综合结果已保存到 comprehensive_optimal_models_results.json")
    
    def create_comprehensive_analysis(self):
        """创建综合分析"""
        print("\n📈 创建综合分析")
        print("=" * 50)
        
        if not self.results:
            return
        
        # 提取数据
        keypoint_counts = sorted(self.results.keys())
        errors = [self.results[kp]['avg_error'] for kp in keypoint_counts]
        architectures = [self.results[kp]['architecture'] for kp in keypoint_counts]
        parameters = [self.results[kp]['parameters'] for kp in keypoint_counts]
        
        # 创建可视化
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 性能曲线
        ax1.plot(keypoint_counts, errors, 'o-', linewidth=3, markersize=8)
        ax1.axhline(y=10, color='orange', linestyle='--', label='Medical Grade')
        ax1.axhline(y=5, color='red', linestyle='--', label='Excellent Grade')
        ax1.set_xlabel('Number of Keypoints')
        ax1.set_ylabel('Average Error (mm)')
        ax1.set_title('Optimal Performance vs Keypoint Count')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 架构分布
        arch_counts = {}
        for arch in architectures:
            arch_counts[arch] = arch_counts.get(arch, 0) + 1
        
        ax2.pie(arch_counts.values(), labels=arch_counts.keys(), autopct='%1.1f%%')
        ax2.set_title('Optimal Architecture Distribution')
        
        # 参数数量
        ax3.plot(keypoint_counts, [p/1000 for p in parameters], 's-', color='purple')
        ax3.set_xlabel('Number of Keypoints')
        ax3.set_ylabel('Parameters (thousands)')
        ax3.set_title('Model Complexity vs Keypoint Count')
        ax3.grid(True, alpha=0.3)
        
        # 效率分析
        efficiency = [10/err for err in errors]
        ax4.bar(range(len(keypoint_counts)), efficiency)
        ax4.set_xlabel('Configuration Index')
        ax4.set_ylabel('Efficiency (10mm/error)')
        ax4.set_title('Model Efficiency')
        ax4.set_xticks(range(len(keypoint_counts)))
        ax4.set_xticklabels([f'{kp}kp' for kp in keypoint_counts], rotation=45)
        
        plt.tight_layout()
        plt.savefig('comprehensive_optimal_models_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✅ 综合分析图表已保存")
        
        # 打印最佳配置
        best_kp = min(self.results.keys(), key=lambda k: self.results[k]['avg_error'])
        best_result = self.results[best_kp]
        
        print(f"\n🏆 全局最佳配置:")
        print(f"   关键点数: {best_kp}")
        print(f"   架构: {best_result['architecture']}")
        print(f"   性能: {best_result['avg_error']:.2f}mm")
        print(f"   参数: {best_result['parameters']:,}")

def main():
    """主函数"""
    print("🎯 全面最佳模型搜索")
    print("Comprehensive Optimal Model Search")
    print("=" * 60)
    
    # 创建搜索器
    finder = OptimalModelFinder()
    
    # 运行搜索
    results = finder.run_comprehensive_search()
    
    if results:
        print(f"\n🎉 搜索完成:")
        print(f"✅ 完成{len(results)}个配置的最佳模型搜索")
        print(f"📊 关键点范围: {min(results.keys())}-{max(results.keys())}")
        print(f"🏆 为每个关键点数量找到了最佳架构")
    else:
        print("❌ 搜索过程中出现问题")

if __name__ == "__main__":
    main()
