#!/usr/bin/env python3
"""
前沿方法探索
Frontier Methods Exploration
探索更多前沿的小样本学习和深度学习技术
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pathlib import Path
from datetime import datetime
import json
import math

class ContrastiveLearningPointNet(nn.Module):
    """对比学习PointNet"""
    
    def __init__(self, num_keypoints=19, embedding_dim=512, temperature=0.1):
        super().__init__()
        self.num_keypoints = num_keypoints
        self.embedding_dim = embedding_dim
        self.temperature = temperature
        
        # 编码器
        self.encoder = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
            nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.<PERSON>L<PERSON>(),
            nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
            nn.Conv1d(512, embedding_dim, 1), nn.BatchNorm1d(embedding_dim), nn.ReLU()
        )
        
        # 投影头 (用于对比学习)
        self.projection_head = nn.Sequential(
            nn.Linear(embedding_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 128)
        )
        
        # 关键点预测头
        self.keypoint_head = nn.Sequential(
            nn.Linear(embedding_dim, 384),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(384, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, num_keypoints * 3)
        )
        
    def forward(self, point_cloud, return_embedding=False):
        B, N, _ = point_cloud.shape
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        # 编码
        features = self.encoder(x)  # (B, embedding_dim, N)
        global_features = torch.max(features, dim=2)[0]  # (B, embedding_dim)
        
        # 关键点预测
        keypoints = self.keypoint_head(global_features)
        keypoints = keypoints.view(B, self.num_keypoints, 3)
        
        if return_embedding:
            # 投影用于对比学习
            projections = self.projection_head(global_features)
            return keypoints, projections
        
        return keypoints
    
    def contrastive_loss(self, projections, labels=None):
        """InfoNCE对比损失"""
        # 归一化
        projections = F.normalize(projections, dim=1)
        
        # 计算相似度矩阵
        similarity_matrix = torch.matmul(projections, projections.T) / self.temperature
        
        # 创建正样本掩码 (简化版本，假设相邻样本为正样本对)
        batch_size = projections.size(0)
        mask = torch.eye(batch_size, device=projections.device)
        
        # 对角线为正样本，其他为负样本
        exp_sim = torch.exp(similarity_matrix)
        exp_sim = exp_sim * (1 - mask)  # 移除自身
        
        # InfoNCE损失
        pos_sim = torch.diag(similarity_matrix, diagonal=1)  # 正样本对
        if len(pos_sim) == 0:
            return torch.tensor(0.0, device=projections.device)
        
        neg_sim = torch.sum(exp_sim, dim=1)[:-1]  # 负样本
        loss = -torch.log(torch.exp(pos_sim) / (torch.exp(pos_sim) + neg_sim))
        
        return torch.mean(loss)

class VariationalPointNet(nn.Module):
    """变分自编码器PointNet"""
    
    def __init__(self, num_keypoints=19, latent_dim=256):
        super().__init__()
        self.num_keypoints = num_keypoints
        self.latent_dim = latent_dim
        
        # 编码器
        self.encoder = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
            nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
            nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
            nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 变分层
        self.fc_mu = nn.Linear(1024, latent_dim)
        self.fc_logvar = nn.Linear(1024, latent_dim)
        
        # 解码器
        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, 384),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(384, 256),
            nn.ReLU(),
            nn.Linear(256, num_keypoints * 3)
        )
        
    def encode(self, point_cloud):
        B, N, _ = point_cloud.shape
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        features = self.encoder(x)  # (B, 1024, N)
        global_features = torch.max(features, dim=2)[0]  # (B, 1024)
        
        mu = self.fc_mu(global_features)
        logvar = self.fc_logvar(global_features)
        
        return mu, logvar
    
    def reparameterize(self, mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std
    
    def decode(self, z):
        keypoints = self.decoder(z)
        return keypoints.view(-1, self.num_keypoints, 3)
    
    def forward(self, point_cloud):
        mu, logvar = self.encode(point_cloud)
        z = self.reparameterize(mu, logvar)
        keypoints = self.decode(z)
        return keypoints, mu, logvar
    
    def vae_loss(self, recon_keypoints, target_keypoints, mu, logvar, beta=0.1):
        # 重构损失
        recon_loss = F.mse_loss(recon_keypoints, target_keypoints)
        
        # KL散度
        kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
        kl_loss = kl_loss / (mu.size(0) * mu.size(1))  # 归一化
        
        return recon_loss + beta * kl_loss

class GraphNeuralPointNet(nn.Module):
    """图神经网络PointNet"""
    
    def __init__(self, num_keypoints=19, hidden_dim=256):
        super().__init__()
        self.num_keypoints = num_keypoints
        self.hidden_dim = hidden_dim
        
        # 点特征提取
        self.point_encoder = nn.Sequential(
            nn.Linear(3, 64),
            nn.ReLU(),
            nn.Linear(64, 128),
            nn.ReLU(),
            nn.Linear(128, hidden_dim)
        )
        
        # 图卷积层
        self.graph_conv1 = GraphConvLayer(hidden_dim, hidden_dim)
        self.graph_conv2 = GraphConvLayer(hidden_dim, hidden_dim)
        self.graph_conv3 = GraphConvLayer(hidden_dim, hidden_dim)
        
        # 全局池化
        self.global_pool = nn.Sequential(
            nn.Linear(hidden_dim, 512),
            nn.ReLU(),
            nn.Linear(512, 1024)
        )
        
        # 关键点预测
        self.keypoint_predictor = nn.Sequential(
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, num_keypoints * 3)
        )
        
    def build_graph(self, point_cloud, k=16):
        """构建k-NN图"""
        B, N, _ = point_cloud.shape
        
        # 计算距离矩阵
        dist_matrix = torch.cdist(point_cloud, point_cloud)  # (B, N, N)
        
        # 找到k个最近邻
        _, knn_indices = torch.topk(dist_matrix, k=k+1, dim=2, largest=False)
        knn_indices = knn_indices[:, :, 1:]  # 移除自身
        
        return knn_indices
    
    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape
        
        # 点特征编码
        point_features = self.point_encoder(point_cloud)  # (B, N, hidden_dim)
        
        # 构建图
        knn_indices = self.build_graph(point_cloud, k=16)
        
        # 图卷积
        x = self.graph_conv1(point_features, knn_indices)
        x = self.graph_conv2(x, knn_indices)
        x = self.graph_conv3(x, knn_indices)
        
        # 全局池化
        global_features = torch.max(x, dim=1)[0]  # (B, hidden_dim)
        global_features = self.global_pool(global_features)  # (B, 1024)
        
        # 关键点预测
        keypoints = self.keypoint_predictor(global_features)
        return keypoints.view(B, self.num_keypoints, 3)

class GraphConvLayer(nn.Module):
    """图卷积层"""
    
    def __init__(self, in_dim, out_dim):
        super().__init__()
        self.in_dim = in_dim
        self.out_dim = out_dim
        
        self.linear = nn.Linear(in_dim, out_dim)
        self.neighbor_linear = nn.Linear(in_dim, out_dim)
        self.bn = nn.BatchNorm1d(out_dim)
        
    def forward(self, x, knn_indices):
        B, N, D = x.shape
        
        # 自身特征
        self_features = self.linear(x)  # (B, N, out_dim)
        
        # 邻居特征聚合
        neighbor_features = []
        for b in range(B):
            batch_neighbors = []
            for n in range(N):
                neighbor_idx = knn_indices[b, n]  # (k,)
                neighbors = x[b, neighbor_idx]  # (k, in_dim)
                neighbor_feat = torch.mean(neighbors, dim=0)  # (in_dim,)
                batch_neighbors.append(neighbor_feat)
            neighbor_features.append(torch.stack(batch_neighbors))
        
        neighbor_features = torch.stack(neighbor_features)  # (B, N, in_dim)
        neighbor_features = self.neighbor_linear(neighbor_features)  # (B, N, out_dim)
        
        # 组合特征
        combined = self_features + neighbor_features
        
        # 批归一化和激活
        combined = combined.transpose(1, 2)  # (B, out_dim, N)
        combined = self.bn(combined)
        combined = F.relu(combined)
        combined = combined.transpose(1, 2)  # (B, N, out_dim)
        
        return combined

class AttentionPointNet(nn.Module):
    """注意力机制PointNet"""
    
    def __init__(self, num_keypoints=19, d_model=512, nhead=8, num_layers=3):
        super().__init__()
        self.num_keypoints = num_keypoints
        self.d_model = d_model
        
        # 点特征编码
        self.point_embedding = nn.Sequential(
            nn.Linear(3, 128),
            nn.ReLU(),
            nn.Linear(128, 256),
            nn.ReLU(),
            nn.Linear(256, d_model)
        )
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(d_model)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model, 
            nhead=nhead, 
            dim_feedforward=d_model*2,
            dropout=0.1,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 关键点预测
        self.keypoint_predictor = nn.Sequential(
            nn.Linear(d_model, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, num_keypoints * 3)
        )
        
    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape
        
        # 点嵌入
        point_emb = self.point_embedding(point_cloud)  # (B, N, d_model)
        
        # 位置编码
        point_emb = self.pos_encoding(point_emb)
        
        # Transformer编码
        encoded = self.transformer(point_emb)  # (B, N, d_model)
        
        # 全局池化
        global_features = torch.mean(encoded, dim=1)  # (B, d_model)
        
        # 关键点预测
        keypoints = self.keypoint_predictor(global_features)
        return keypoints.view(B, self.num_keypoints, 3)

class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model, max_len=5000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
        
    def forward(self, x):
        return x + self.pe[:x.size(1), :].transpose(0, 1)

class EnsembleMetaLearner(nn.Module):
    """集成元学习器"""
    
    def __init__(self, num_keypoints=19, num_models=3):
        super().__init__()
        self.num_keypoints = num_keypoints
        self.num_models = num_models
        
        # 多个基础模型
        self.base_models = nn.ModuleList()
        
        # 模型1: 标准PointNet
        model1 = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
            nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
            nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
            nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 模型2: 宽网络
        model2 = nn.Sequential(
            nn.Conv1d(3, 96, 1), nn.BatchNorm1d(96), nn.ReLU(),
            nn.Conv1d(96, 192, 1), nn.BatchNorm1d(192), nn.ReLU(),
            nn.Conv1d(192, 384, 1), nn.BatchNorm1d(384), nn.ReLU(),
            nn.Conv1d(384, 768, 1), nn.BatchNorm1d(768), nn.ReLU(),
            nn.Conv1d(768, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 模型3: 深网络
        model3 = nn.Sequential(
            nn.Conv1d(3, 48, 1), nn.BatchNorm1d(48), nn.ReLU(),
            nn.Conv1d(48, 96, 1), nn.BatchNorm1d(96), nn.ReLU(),
            nn.Conv1d(96, 192, 1), nn.BatchNorm1d(192), nn.ReLU(),
            nn.Conv1d(192, 384, 1), nn.BatchNorm1d(384), nn.ReLU(),
            nn.Conv1d(384, 768, 1), nn.BatchNorm1d(768), nn.ReLU(),
            nn.Conv1d(768, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        self.base_models.extend([model1, model2, model3])
        
        # 元学习头
        self.meta_heads = nn.ModuleList([
            nn.Sequential(
                nn.Linear(1024, 512),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(512, 256),
                nn.ReLU(),
                nn.Linear(256, num_keypoints * 3)
            ) for _ in range(num_models)
        ])
        
        # 动态权重网络
        self.weight_net = nn.Sequential(
            nn.Linear(1024 * num_models, 256),
            nn.ReLU(),
            nn.Linear(256, 64),
            nn.ReLU(),
            nn.Linear(64, num_models),
            nn.Softmax(dim=1)
        )
        
    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        # 多模型特征提取
        features_list = []
        predictions = []
        
        for i, (model, head) in enumerate(zip(self.base_models, self.meta_heads)):
            features = model(x)  # (B, 1024, N)
            global_feat = torch.max(features, dim=2)[0]  # (B, 1024)
            features_list.append(global_feat)
            
            pred = head(global_feat)
            pred = pred.view(B, self.num_keypoints, 3)
            predictions.append(pred)
        
        # 动态权重计算
        combined_features = torch.cat(features_list, dim=1)  # (B, 1024*num_models)
        weights = self.weight_net(combined_features)  # (B, num_models)
        
        # 加权集成
        ensemble_pred = torch.zeros_like(predictions[0])
        for i, pred in enumerate(predictions):
            ensemble_pred += weights[:, i:i+1].unsqueeze(-1) * pred
        
        return ensemble_pred

class FrontierTrainer:
    """前沿方法训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def load_aligned_data(self):
        """加载对齐数据"""
        print("📦 加载F3对齐数据...")
        
        aligned_files = list(Path("data/processed").glob("f3_aligned_dataset_*.npz"))
        if not aligned_files:
            raise FileNotFoundError("未找到F3对齐数据集")
        
        latest_file = max(aligned_files, key=lambda x: x.stat().st_mtime)
        data = np.load(str(latest_file), allow_pickle=True)
        
        point_clouds = np.array(data['point_clouds'], dtype=np.float32)
        keypoints = np.array(data['keypoints'], dtype=np.float32)
        
        # 数据划分
        from sklearn.model_selection import train_test_split
        indices = np.arange(len(point_clouds))
        train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
        train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
        
        print(f"✅ 数据加载完成: {point_clouds.shape}")
        print(f"   训练: {len(train_indices)}, 验证: {len(val_indices)}, 测试: {len(test_indices)}")
        
        return self.data
    
    def train_contrastive_learning(self, epochs=80, lr=0.001):
        """训练对比学习模型"""
        print(f"\n🔄 训练对比学习模型")
        print(f"   策略: InfoNCE对比学习 + 关键点预测")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        model = ContrastiveLearningPointNet(num_keypoints=19).to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
        
        mse_criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        
        for epoch in range(epochs):
            model.train()
            epoch_losses = []
            
            k_shot = min(16, len(self.data['train']['point_clouds']))
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                k_shot, 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 简单增强
            aug_pcs = []
            aug_kps = []
            for pc, kp in zip(train_pcs, train_kps):
                aug_pcs.append(pc)
                aug_kps.append(kp)
                
                # 轻微旋转
                angle = np.random.uniform(-0.01, 0.01)
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], dtype=np.float32)
                
                aug_pc = pc @ rotation.T
                aug_kp = kp @ rotation.T
                aug_pcs.append(aug_pc)
                aug_kps.append(aug_kp)
            
            # 批次训练
            batch_size = 6
            for i in range(0, len(aug_pcs), batch_size):
                batch_pcs = torch.FloatTensor(aug_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(aug_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                
                # 前向传播
                pred_kps, projections = model(batch_pcs, return_embedding=True)
                
                # 关键点损失
                kp_loss = mse_criterion(pred_kps, batch_kps)
                
                # 对比损失
                contrastive_loss = model.contrastive_loss(projections)
                
                # 总损失
                total_loss = kp_loss + 0.1 * contrastive_loss
                
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_losses.append(total_loss.item())
                
                del batch_pcs, batch_kps, pred_kps, projections
                torch.cuda.empty_cache()
            
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_model(model, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm")
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def train_variational_model(self, epochs=80, lr=0.001):
        """训练变分自编码器模型"""
        print(f"\n🎲 训练变分自编码器模型")
        print(f"   策略: VAE潜在空间学习")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        model = VariationalPointNet(num_keypoints=19, latent_dim=256).to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
        
        best_val_error = float('inf')
        best_model_state = None
        
        for epoch in range(epochs):
            model.train()
            epoch_losses = []
            
            k_shot = min(15, len(self.data['train']['point_clouds']))
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                k_shot, 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 批次训练
            batch_size = 4
            for i in range(0, len(train_pcs), batch_size):
                batch_pcs = torch.FloatTensor(train_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(train_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                
                # VAE前向传播
                recon_kps, mu, logvar = model(batch_pcs)
                
                # VAE损失
                loss = model.vae_loss(recon_kps, batch_kps, mu, logvar, beta=0.1)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_losses.append(loss.item())
                
                del batch_pcs, batch_kps, recon_kps, mu, logvar
                torch.cuda.empty_cache()
            
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_vae_model(model, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm")
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def train_graph_neural_network(self, epochs=80, lr=0.001):
        """训练图神经网络"""
        print(f"\n🕸️ 训练图神经网络")
        print(f"   策略: k-NN图 + 图卷积")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        model = GraphNeuralPointNet(num_keypoints=19, hidden_dim=256).to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        
        for epoch in range(epochs):
            model.train()
            epoch_losses = []
            
            k_shot = min(12, len(self.data['train']['point_clouds']))  # 减少批次大小
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                k_shot, 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 批次训练
            batch_size = 2  # 图神经网络内存消耗大
            for i in range(0, len(train_pcs), batch_size):
                batch_pcs = torch.FloatTensor(train_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(train_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                
                pred_kps = model(batch_pcs)
                loss = criterion(pred_kps, batch_kps)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_losses.append(loss.item())
                
                del batch_pcs, batch_kps, pred_kps
                torch.cuda.empty_cache()
            
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_model(model, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm")
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def train_attention_model(self, epochs=80, lr=0.001):
        """训练注意力模型"""
        print(f"\n🎯 训练注意力模型")
        print(f"   策略: Transformer编码器")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        model = AttentionPointNet(num_keypoints=19, d_model=512, nhead=8, num_layers=3).to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        
        for epoch in range(epochs):
            model.train()
            epoch_losses = []
            
            k_shot = min(14, len(self.data['train']['point_clouds']))
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                k_shot, 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 批次训练
            batch_size = 3  # Transformer内存消耗较大
            for i in range(0, len(train_pcs), batch_size):
                batch_pcs = torch.FloatTensor(train_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(train_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                
                pred_kps = model(batch_pcs)
                loss = criterion(pred_kps, batch_kps)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_losses.append(loss.item())
                
                del batch_pcs, batch_kps, pred_kps
                torch.cuda.empty_cache()
            
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_model(model, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm")
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def train_ensemble_meta_learner(self, epochs=80, lr=0.001):
        """训练集成元学习器"""
        print(f"\n🎭 训练集成元学习器")
        print(f"   策略: 多模型集成 + 动态权重")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        model = EnsembleMetaLearner(num_keypoints=19, num_models=3).to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        
        for epoch in range(epochs):
            model.train()
            epoch_losses = []
            
            k_shot = min(16, len(self.data['train']['point_clouds']))
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                k_shot, 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 批次训练
            batch_size = 4
            for i in range(0, len(train_pcs), batch_size):
                batch_pcs = torch.FloatTensor(train_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(train_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                
                pred_kps = model(batch_pcs)
                loss = criterion(pred_kps, batch_kps)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_losses.append(loss.item())
                
                del batch_pcs, batch_kps, pred_kps
                torch.cuda.empty_cache()
            
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_model(model, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm")
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def evaluate_model(self, model, split='test'):
        """评估标准模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for i in range(0, len(pcs), 2):
                batch_pcs = torch.FloatTensor(pcs[i:i+2]).to(self.device)
                batch_kps = torch.FloatTensor(kps[i:i+2]).to(self.device)
                
                pred_kps = model(batch_pcs)
                
                for j in range(len(batch_pcs)):
                    error = torch.mean(torch.norm(pred_kps[j] - batch_kps[j], dim=1))
                    total_error += error.item()
                    num_samples += 1
                
                del batch_pcs, batch_kps, pred_kps
                torch.cuda.empty_cache()
        
        return total_error / num_samples if num_samples > 0 else float('inf')
    
    def evaluate_vae_model(self, model, split='test'):
        """评估VAE模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for i in range(0, len(pcs), 2):
                batch_pcs = torch.FloatTensor(pcs[i:i+2]).to(self.device)
                batch_kps = torch.FloatTensor(kps[i:i+2]).to(self.device)
                
                pred_kps, _, _ = model(batch_pcs)
                
                for j in range(len(batch_pcs)):
                    error = torch.mean(torch.norm(pred_kps[j] - batch_kps[j], dim=1))
                    total_error += error.item()
                    num_samples += 1
                
                del batch_pcs, batch_kps, pred_kps
                torch.cuda.empty_cache()
        
        return total_error / num_samples if num_samples > 0 else float('inf')

def run_frontier_experiments():
    """运行前沿方法实验"""
    print("🚀 前沿方法探索实验")
    print("=" * 60)
    print("探索更多前沿的深度学习技术:")
    print("1. 对比学习 (Contrastive Learning)")
    print("2. 变分自编码器 (Variational AutoEncoder)")
    print("3. 图神经网络 (Graph Neural Network)")
    print("4. 注意力机制 (Attention/Transformer)")
    print("5. 集成元学习 (Ensemble Meta-Learning)")
    
    trainer = FrontierTrainer()
    data = trainer.load_aligned_data()
    
    results = {}
    
    # 1. 对比学习
    print(f"\n{'='*60}")
    try:
        contrastive_model, contrastive_val_error = trainer.train_contrastive_learning(epochs=60, lr=0.001)
        contrastive_test_error = trainer.evaluate_model(contrastive_model, 'test')
        results['contrastive_learning'] = {
            'val_error': contrastive_val_error,
            'test_error': contrastive_test_error
        }
        print(f"✅ 对比学习完成: 验证={contrastive_val_error:.3f}mm, 测试={contrastive_test_error:.3f}mm")
    except Exception as e:
        print(f"❌ 对比学习失败: {e}")
        results['contrastive_learning'] = {'val_error': float('inf'), 'test_error': float('inf')}
    
    # 2. 变分自编码器
    print(f"\n{'='*60}")
    try:
        vae_model, vae_val_error = trainer.train_variational_model(epochs=60, lr=0.001)
        vae_test_error = trainer.evaluate_vae_model(vae_model, 'test')
        results['variational_autoencoder'] = {
            'val_error': vae_val_error,
            'test_error': vae_test_error
        }
        print(f"✅ 变分自编码器完成: 验证={vae_val_error:.3f}mm, 测试={vae_test_error:.3f}mm")
    except Exception as e:
        print(f"❌ 变分自编码器失败: {e}")
        results['variational_autoencoder'] = {'val_error': float('inf'), 'test_error': float('inf')}
    
    # 3. 图神经网络
    print(f"\n{'='*60}")
    try:
        gnn_model, gnn_val_error = trainer.train_graph_neural_network(epochs=60, lr=0.001)
        gnn_test_error = trainer.evaluate_model(gnn_model, 'test')
        results['graph_neural_network'] = {
            'val_error': gnn_val_error,
            'test_error': gnn_test_error
        }
        print(f"✅ 图神经网络完成: 验证={gnn_val_error:.3f}mm, 测试={gnn_test_error:.3f}mm")
    except Exception as e:
        print(f"❌ 图神经网络失败: {e}")
        results['graph_neural_network'] = {'val_error': float('inf'), 'test_error': float('inf')}
    
    # 4. 注意力机制
    print(f"\n{'='*60}")
    try:
        attention_model, attention_val_error = trainer.train_attention_model(epochs=60, lr=0.001)
        attention_test_error = trainer.evaluate_model(attention_model, 'test')
        results['attention_transformer'] = {
            'val_error': attention_val_error,
            'test_error': attention_test_error
        }
        print(f"✅ 注意力机制完成: 验证={attention_val_error:.3f}mm, 测试={attention_test_error:.3f}mm")
    except Exception as e:
        print(f"❌ 注意力机制失败: {e}")
        results['attention_transformer'] = {'val_error': float('inf'), 'test_error': float('inf')}
    
    # 5. 集成元学习
    print(f"\n{'='*60}")
    try:
        ensemble_model, ensemble_val_error = trainer.train_ensemble_meta_learner(epochs=60, lr=0.001)
        ensemble_test_error = trainer.evaluate_model(ensemble_model, 'test')
        results['ensemble_meta_learning'] = {
            'val_error': ensemble_val_error,
            'test_error': ensemble_test_error
        }
        print(f"✅ 集成元学习完成: 验证={ensemble_val_error:.3f}mm, 测试={ensemble_test_error:.3f}mm")
    except Exception as e:
        print(f"❌ 集成元学习失败: {e}")
        results['ensemble_meta_learning'] = {'val_error': float('inf'), 'test_error': float('inf')}
    
    # 结果汇总
    print(f"\n🏆 前沿方法对比:")
    print("=" * 60)
    
    best_method = None
    best_val_error = float('inf')
    best_test_error = float('inf')
    
    for method, result in results.items():
        val_error = result['val_error']
        test_error = result['test_error']
        
        print(f"{method:25s}: 验证={val_error:.3f}mm, 测试={test_error:.3f}mm")
        
        if val_error < best_val_error:
            best_val_error = val_error
            best_test_error = test_error
            best_method = method
    
    print(f"\n🏆 最佳前沿方法: {best_method}")
    print(f"   最佳验证误差: {best_val_error:.3f}mm")
    print(f"   对应测试误差: {best_test_error:.3f}mm")
    
    # 与历史最佳对比
    historical_best = {
        'mixup_original': {'val': 7.041, 'test': 8.363},
        'point_transformer': {'val': 7.129, 'test': 8.127},
        'consistency_regularization': {'val': 7.176, 'test': 8.012},
        'gradient_meta_learning': {'val': 7.277, 'test': 8.039}
    }
    
    print(f"\n📈 与历史最佳方法对比:")
    print("历史最佳:")
    for method, errors in historical_best.items():
        print(f"  {method:25s}: 验证={errors['val']:.3f}mm, 测试={errors['test']:.3f}mm")
    
    print(f"\n当前最佳前沿方法:")
    print(f"  {best_method:25s}: 验证={best_val_error:.3f}mm, 测试={best_test_error:.3f}mm")
    
    # 改进分析
    best_historical_val = min([errors['val'] for errors in historical_best.values()])
    best_historical_test = min([errors['test'] for errors in historical_best.values()])
    
    if best_val_error < best_historical_val:
        val_improvement = (best_historical_val - best_val_error) / best_historical_val * 100
        print(f"🎉 验证误差新纪录！改进: {val_improvement:.1f}%")
    
    if best_test_error < best_historical_test:
        test_improvement = (best_historical_test - best_test_error) / best_historical_test * 100
        print(f"🎉 测试误差新纪录！改进: {test_improvement:.1f}%")
    
    # 医疗级评估
    medical_target = 5.0
    print(f"\n🎯 医疗级精度评估:")
    print(f"医疗级目标:               {medical_target:.1f}mm")
    print(f"当前最佳验证误差:         {best_val_error:.3f}mm")
    print(f"当前最佳测试误差:         {best_test_error:.3f}mm")
    
    if best_val_error <= medical_target:
        print("🎉 验证误差达到医疗级精度！")
    elif best_val_error <= 6.0:
        remaining = best_val_error - medical_target
        print(f"🔥 验证误差非常接近医疗级！还需{remaining:.3f}mm")
    else:
        remaining = best_val_error - medical_target
        print(f"📈 验证误差距离医疗级还需{remaining:.3f}mm")
    
    # 保存结果
    experiment_results = {
        "experiment_timestamp": datetime.now().isoformat(),
        "experiment_type": "frontier_methods_exploration",
        "methods_tested": [
            "contrastive_learning",
            "variational_autoencoder",
            "graph_neural_network",
            "attention_transformer",
            "ensemble_meta_learning"
        ],
        "results": results,
        "best_method": best_method,
        "best_val_error": float(best_val_error),
        "best_test_error": float(best_test_error),
        "historical_comparison": historical_best,
        "medical_target": medical_target,
        "medical_achieved": best_val_error <= medical_target
    }
    
    results_dir = Path("results/frontier_methods")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = results_dir / f"frontier_methods_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(experiment_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 实验结果已保存: {results_file}")
    
    return trainer, results

if __name__ == "__main__":
    trainer, results = run_frontier_experiments()
