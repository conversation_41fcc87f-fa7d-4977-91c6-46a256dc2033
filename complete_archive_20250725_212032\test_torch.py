#!/usr/bin/env python3
"""
测试PyTorch基础功能
"""

print("开始测试PyTorch...")

try:
    print("1. 导入torch...")
    import torch
    print(f"   ✅ PyTorch版本: {torch.__version__}")
    
    print("2. 测试CUDA...")
    print(f"   CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"   GPU数量: {torch.cuda.device_count()}")
        print(f"   当前GPU: {torch.cuda.current_device()}")
    
    print("3. 创建简单张量...")
    x = torch.randn(3, 3)
    print(f"   ✅ CPU张量: {x.shape}")
    
    if torch.cuda.is_available():
        print("4. 测试GPU张量...")
        x_gpu = x.cuda()
        print(f"   ✅ GPU张量: {x_gpu.shape}")
    
    print("5. 测试简单运算...")
    y = torch.randn(3, 3)
    z = x + y
    print(f"   ✅ 运算结果: {z.shape}")
    
    print("✅ PyTorch基础功能测试通过！")
    
except Exception as e:
    print(f"❌ PyTorch测试失败: {e}")
    import traceback
    traceback.print_exc()
