#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复的基准测试 - 正确的数据归一化和训练设置
Fixed Benchmark - Correct Data Normalization and Training Settings
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import matplotlib.pyplot as plt
import pandas as pd
import json
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from torch.utils.data import Dataset, DataLoader
import time

class NormalizedMedicalKeypointDataset(Dataset):
    """带归一化的医疗关键点数据集"""
    
    def __init__(self, point_clouds, keypoints, num_points=50000, scaler=None, fit_scaler=False):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.num_points = num_points
        
        # 数据归一化
        if fit_scaler:
            # 合并所有数据进行归一化
            all_data = []
            for i in range(len(point_clouds)):
                combined = np.vstack([point_clouds[i], keypoints[i]])
                all_data.append(combined)
            
            all_data = np.vstack(all_data)
            self.scaler = StandardScaler()
            self.scaler.fit(all_data)
        else:
            self.scaler = scaler
        
        # 归一化数据
        self.normalized_pcs = []
        self.normalized_kps = []
        
        for i in range(len(point_clouds)):
            combined = np.vstack([point_clouds[i], keypoints[i]])
            normalized = self.scaler.transform(combined)
            
            pc_normalized = normalized[:len(point_clouds[i])]
            kp_normalized = normalized[len(point_clouds[i]):]
            
            self.normalized_pcs.append(pc_normalized)
            self.normalized_kps.append(kp_normalized)
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        pc = self.normalized_pcs[idx].copy()
        kp = self.normalized_kps[idx].copy()
        
        # 重采样点云
        if len(pc) != self.num_points:
            if len(pc) > self.num_points:
                indices = np.random.choice(len(pc), self.num_points, replace=False)
                pc = pc[indices]
            else:
                indices = np.random.choice(len(pc), self.num_points, replace=True)
                pc = pc[indices]
        
        return torch.FloatTensor(pc), torch.FloatTensor(kp)

class ImprovedPointNet(nn.Module):
    """改进的PointNet模型"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super().__init__()
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 回归头
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        x = F.relu(self.bn4(self.conv4(x)))
        x = F.relu(self.bn5(self.conv5(x)))
        
        # 全局特征
        x = torch.max(x, 2)[0]  # [B, 1024]
        
        # 回归
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = F.relu(self.fc3(x))
        x = self.dropout(x)
        x = self.fc4(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

class FixedBenchmarkFramework:
    """修复的基准测试框架"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️ 使用设备: {self.device}")
        
        # 加载数据集
        self.load_dataset()
        
        # 测试配置
        self.models = {
            'Improved_PointNet': ImprovedPointNet,
        }
        
        self.point_counts = [5000, 10000]
        self.keypoint_configs = [12, 28]
        
        self.results = {}
    
    def load_dataset(self):
        """加载数据集"""
        print("📥 加载数据集...")
        
        data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
        self.point_clouds = data['point_clouds']
        self.keypoints_57 = data['keypoints_57']
        
        # 创建数据划分
        indices = np.arange(len(self.point_clouds))
        train_indices, test_val_indices = train_test_split(indices, test_size=0.4, random_state=42)
        val_indices, test_indices = train_test_split(test_val_indices, test_size=0.5, random_state=42)
        
        self.train_indices = train_indices
        self.val_indices = val_indices
        self.test_indices = test_indices
        
        print(f"✅ 数据加载完成:")
        print(f"   训练集: {len(train_indices)} 样本")
        print(f"   验证集: {len(val_indices)} 样本")
        print(f"   测试集: {len(test_indices)} 样本")
        
        # 检查数据范围
        print(f"   点云范围: X[{self.point_clouds[:,:,0].min():.1f}, {self.point_clouds[:,:,0].max():.1f}]")
        print(f"   关键点范围: X[{self.keypoints_57[:,:,0].min():.1f}, {self.keypoints_57[:,:,0].max():.1f}]")
    
    def create_datasets(self, num_points, num_keypoints):
        """创建带归一化的数据集"""
        
        # 选择关键点
        if num_keypoints == 57:
            keypoints = self.keypoints_57
        else:
            indices = np.linspace(0, 56, num_keypoints, dtype=int)
            keypoints = self.keypoints_57[:, indices, :]
        
        # 创建训练集（拟合归一化器）
        train_dataset = NormalizedMedicalKeypointDataset(
            self.point_clouds[self.train_indices],
            keypoints[self.train_indices],
            num_points=num_points,
            fit_scaler=True
        )
        
        # 创建验证集和测试集（使用相同的归一化器）
        val_dataset = NormalizedMedicalKeypointDataset(
            self.point_clouds[self.val_indices],
            keypoints[self.val_indices],
            num_points=num_points,
            scaler=train_dataset.scaler
        )
        
        test_dataset = NormalizedMedicalKeypointDataset(
            self.point_clouds[self.test_indices],
            keypoints[self.test_indices],
            num_points=num_points,
            scaler=train_dataset.scaler
        )
        
        return train_dataset, val_dataset, test_dataset
    
    def train_model(self, model, train_loader, val_loader, epochs=50):
        """改进的训练方法"""
        
        # 使用更小的学习率和更好的优化器设置
        optimizer = torch.optim.AdamW(model.parameters(), lr=0.0005, weight_decay=1e-4)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)
        criterion = nn.MSELoss()
        
        best_val_loss = float('inf')
        patience = 15
        patience_counter = 0
        
        for epoch in range(epochs):
            # 训练
            model.train()
            train_loss = 0.0
            
            for batch_pc, batch_kp in train_loader:
                batch_pc = batch_pc.to(self.device)
                batch_kp = batch_kp.to(self.device)
                
                optimizer.zero_grad()
                pred_kp = model(batch_pc)
                loss = criterion(pred_kp, batch_kp)
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.step()
                train_loss += loss.item()
            
            # 验证
            model.eval()
            val_loss = 0.0
            
            with torch.no_grad():
                for batch_pc, batch_kp in val_loader:
                    batch_pc = batch_pc.to(self.device)
                    batch_kp = batch_kp.to(self.device)
                    
                    pred_kp = model(batch_pc)
                    loss = criterion(pred_kp, batch_kp)
                    val_loss += loss.item()
            
            train_loss /= len(train_loader)
            val_loss /= len(val_loader)
            
            scheduler.step()
            
            # 早停
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                best_model_state = model.state_dict().copy()
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    print(f"    早停于epoch {epoch+1}")
                    break
            
            if (epoch + 1) % 10 == 0:
                print(f"    Epoch {epoch+1}: Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")
        
        # 加载最佳模型
        model.load_state_dict(best_model_state)
        return model
    
    def evaluate_model_with_denormalization(self, model, test_dataset):
        """带反归一化的模型评估"""
        
        model.eval()
        all_errors = []
        
        test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False)
        
        with torch.no_grad():
            for i, (batch_pc, batch_kp_norm) in enumerate(test_loader):
                batch_pc = batch_pc.to(self.device)
                batch_kp_norm = batch_kp_norm.to(self.device)
                
                # 预测归一化的关键点
                pred_kp_norm = model(batch_pc)
                
                # 反归一化到原始尺度
                pred_kp_norm_np = pred_kp_norm.cpu().numpy()[0]
                true_kp_norm_np = batch_kp_norm.cpu().numpy()[0]
                
                # 获取原始测试数据进行反归一化
                test_idx = self.test_indices[i]
                original_pc = self.point_clouds[test_idx]
                
                # 选择对应的关键点
                if len(true_kp_norm_np) == 57:
                    original_kp = self.keypoints_57[test_idx]
                else:
                    indices = np.linspace(0, 56, len(true_kp_norm_np), dtype=int)
                    original_kp = self.keypoints_57[test_idx][indices]
                
                # 创建虚拟数据用于反归一化
                dummy_pc = np.zeros_like(original_pc)
                
                # 反归一化预测结果
                pred_combined = np.vstack([dummy_pc, pred_kp_norm_np])
                pred_denorm = test_dataset.scaler.inverse_transform(pred_combined)
                pred_kp_denorm = pred_denorm[len(dummy_pc):]
                
                # 计算误差（使用原始尺度）
                errors = np.linalg.norm(original_kp - pred_kp_denorm, axis=1)
                all_errors.extend(errors)
        
        all_errors = np.array(all_errors)
        
        return {
            'avg_error': np.mean(all_errors),
            'std_error': np.std(all_errors),
            'median_error': np.median(all_errors),
            'max_error': np.max(all_errors),
            'min_error': np.min(all_errors),
            'medical_rate': np.sum(all_errors <= 10) / len(all_errors) * 100,
            'excellent_rate': np.sum(all_errors <= 5) / len(all_errors) * 100,
            'precision_1mm': np.sum(all_errors <= 1) / len(all_errors) * 100
        }
    
    def run_single_experiment(self, model_name, model_class, num_points, num_keypoints):
        """运行单个实验"""
        
        print(f"\n🔄 实验: {model_name}, {num_points}点, {num_keypoints}关键点")
        
        try:
            # 创建数据集
            train_dataset, val_dataset, test_dataset = self.create_datasets(num_points, num_keypoints)
            
            # 创建数据加载器
            batch_size = min(8, len(train_dataset))
            
            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
            
            # 创建模型
            model = model_class(num_points=num_points, num_keypoints=num_keypoints)
            model = model.to(self.device)
            
            print(f"  📊 模型参数数量: {sum(p.numel() for p in model.parameters())/1e6:.2f}M")
            
            # 训练模型
            start_time = time.time()
            model = self.train_model(model, train_loader, val_loader)
            training_time = time.time() - start_time
            
            # 评估模型（带反归一化）
            results = self.evaluate_model_with_denormalization(model, test_dataset)
            results['training_time'] = training_time
            results['num_params'] = sum(p.numel() for p in model.parameters())
            
            print(f"  ✅ 完成: 平均误差 {results['avg_error']:.2f}mm, 训练时间 {training_time:.1f}s")
            
            return results
            
        except Exception as e:
            print(f"  ❌ 实验失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def run_benchmark(self):
        """运行基准测试"""
        
        print("\n🚀 开始修复的基准测试...")
        print("=" * 80)
        
        for model_name, model_class in self.models.items():
            self.results[model_name] = {}
            
            for num_keypoints in self.keypoint_configs:
                self.results[model_name][num_keypoints] = {}
                
                for num_points in self.point_counts:
                    result = self.run_single_experiment(model_name, model_class, num_points, num_keypoints)
                    
                    if result:
                        self.results[model_name][num_keypoints][num_points] = result
                    else:
                        self.results[model_name][num_keypoints][num_points] = {'error': 'Failed'}
        
        # 保存结果
        self.save_results()
        
        print("\n✅ 修复的基准测试完成！")
        return self.results
    
    def save_results(self):
        """保存结果"""
        
        # 保存为JSON
        with open('fixed_benchmark_results.json', 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        # 转换为CSV格式
        csv_data = []
        for model_name in self.results:
            for num_keypoints in self.results[model_name]:
                for num_points in self.results[model_name][num_keypoints]:
                    result = self.results[model_name][num_keypoints][num_points]
                    if isinstance(result, dict) and 'avg_error' in result:
                        csv_data.append({
                            'Model': model_name,
                            'Keypoints': num_keypoints,
                            'Points': num_points,
                            'Avg_Error_mm': result['avg_error'],
                            'Std_Error_mm': result['std_error'],
                            'Medical_Rate_%': result['medical_rate'],
                            'Excellent_Rate_%': result['excellent_rate'],
                            'Training_Time_s': result['training_time'],
                            'Num_Params_M': result['num_params'] / 1e6
                        })
        
        df = pd.DataFrame(csv_data)
        df.to_csv('fixed_benchmark_results.csv', index=False)
        
        print("💾 结果已保存:")
        print("   📄 fixed_benchmark_results.json")
        print("   📊 fixed_benchmark_results.csv")

if __name__ == "__main__":
    print("🧪 修复的基准测试")
    print("正确的数据归一化和训练设置")
    print("=" * 80)
    
    # 创建基准测试框架
    benchmark = FixedBenchmarkFramework()
    
    # 运行基准测试
    results = benchmark.run_benchmark()
    
    print(f"\n📋 基准测试总结:")
    print(f"   🔬 测试模型: {len(benchmark.models)} 个")
    print(f"   📊 点数配置: {len(benchmark.point_counts)} 种")
    print(f"   🎯 关键点配置: {len(benchmark.keypoint_configs)} 种")
    
    print(f"\n💡 修复的改进:")
    print(f"   • 正确的数据归一化")
    print(f"   • 改进的训练设置")
    print(f"   • 带反归一化的评估")
    print(f"   • 更好的优化器配置")
