This is XeTeX, Version 3.141592653-2.6-0.999997 (TeX Live 2025) (preloaded format=xelatex 2025.4.18)  11 JUL 2025 19:39
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**pelvic_dataset_paper
(./pelvic_dataset_paper.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(c:/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(c:/texlive/2025/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count192
\c@section=\count193
\c@subsection=\count194
\c@subsubsection=\count195
\c@paragraph=\count196
\c@subparagraph=\count197
\c@figure=\count198
\c@table=\count199
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (c:/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18


Package inputenc Warning: inputenc package ignored with utf8 based engines.

) (c:/texlive/2025/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
LaTeX Font Info:    Trying to load font information for T1+lmr on input line 116.
 (c:/texlive/2025/texmf-dist/tex/latex/lm/t1lmr.fd
File: t1lmr.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)) (c:/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(c:/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (c:/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen142
)) (c:/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen143
) (c:/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count266
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count267
\leftroot@=\count268
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count269
\DOTSCASE@=\count270
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen144
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count271
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count272
\dotsspace@=\muskip17
\c@parentequation=\count273
\dspbrk@lvl=\count274
\tag@help=\toks20
\row@=\count275
\column@=\count276
\maxfields@=\count277
\andhelp@=\toks21
\eqnshift@=\dimen145
\alignsep@=\dimen146
\tagshift@=\dimen147
\tagwidth@=\dimen148
\totwidth@=\dimen149
\lineht@=\dimen150
\@envbody=\toks22
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (c:/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (c:/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (c:/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (c:/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks24
) (c:/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (c:/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (c:/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.
 (c:/texlive/2025/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen151
\Gin@req@width=\dimen152
) (c:/texlive/2025/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen153
\lightrulewidth=\dimen154
\cmidrulewidth=\dimen155
\belowrulesep=\dimen156
\belowbottomsep=\dimen157
\aboverulesep=\dimen158
\abovetopsep=\dimen159
\cmidrulesep=\dimen160
\cmidrulekern=\dimen161
\defaultaddspace=\dimen162
\@cmidla=\count278
\@cmidlb=\count279
\@aboverulesep=\dimen163
\@belowrulesep=\dimen164
\@thisruleclass=\count280
\@lastruleclass=\count281
\@thisrulewidth=\dimen165
) (c:/texlive/2025/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip54
\multirow@cntb=\count282
\multirow@dima=\skip55
\bigstrutjot=\dimen166
) (c:/texlive/2025/texmf-dist/tex/latex/tools/array.sty
Package: array 2024/10/17 v2.6g Tabular extension package (FMi)
\col@sep=\dimen167
\ar@mcellbox=\box54
\extrarowheight=\dimen168
\NC@list=\toks25
\extratabsurround=\skip56
\backup@length=\skip57
\ar@cellbox=\box55
) (c:/texlive/2025/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
) (c:/texlive/2025/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip58
\bibsep=\skip59
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count283
) (c:/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (c:/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (c:/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count284
\Gm@cntv=\count285
\c@Gm@tempcnt=\count286
\Gm@bindingoffset=\dimen169
\Gm@wd@mp=\dimen170
\Gm@odd@mp=\dimen171
\Gm@even@mp=\dimen172
\Gm@layoutwidth=\dimen173
\Gm@layoutheight=\dimen174
\Gm@layouthoffset=\dimen175
\Gm@layoutvoffset=\dimen176
\Gm@dimlist=\toks26
) (c:/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (c:/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
 (c:/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (c:/texlive/2025/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)
 (c:/texlive/2025/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen177
\captionmargin=\dimen178
\caption@leftmargin=\dimen179
\caption@rightmargin=\dimen180
\caption@width=\dimen181
\caption@indent=\dimen182
\caption@parindent=\dimen183
\caption@hangindent=\dimen184
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count287
\c@continuedfloat=\count288
) (c:/texlive/2025/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count289
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count290
) (c:/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-05-08 L3 backend support: XeTeX
\g__graphics_track_int=\count291
\l__pdf_internal_box=\box56
\g__pdf_backend_annotation_int=\count292
\g__pdf_backend_link_int=\count293
) (./pelvic_dataset_paper.aux)
\openout1 = `pelvic_dataset_paper.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 472.03123pt, 71.13188pt)
* v-part:(T,H,B)=(71.13188pt, 652.70622pt, 71.13188pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=472.03123pt
* \textheight=652.70622pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package caption Info: Begin \AtBeginDocument code.
Package caption Info: End \AtBeginDocument code.
LaTeX Font Info:    Trying to load font information for U+msa on input line 39.
(c:/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 39.
 (c:/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

Package natbib Warning: Citation `ref1' on page 1 undefined on input line 49.


Package natbib Warning: Citation `ref2' on page 1 undefined on input line 49.


Package natbib Warning: Citation `ref3' on page 1 undefined on input line 53.


Package natbib Warning: Citation `ref4' on page 1 undefined on input line 53.


Package natbib Warning: Citation `ref5' on page 1 undefined on input line 53.



[1

]

[2]

[3]
LaTeX Font Info:    Trying to load font information for T1+lmtt on input line 156.
 (c:/texlive/2025/texmf-dist/tex/latex/lm/t1lmtt.fd
File: t1lmtt.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)
Missing character: There is no ├ ("251C) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no │ ("2502) in font ec-lmtt10!
Missing character: There is no ├ ("251C) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no │ ("2502) in font ec-lmtt10!
Missing character: There is no ├ ("251C) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no │ ("2502) in font ec-lmtt10!
Missing character: There is no └ ("2514) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ├ ("251C) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no │ ("2502) in font ec-lmtt10!
Missing character: There is no ├ ("251C) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no │ ("2502) in font ec-lmtt10!
Missing character: There is no ├ ("251C) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no │ ("2502) in font ec-lmtt10!
Missing character: There is no └ ("2514) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ├ ("251C) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no │ ("2502) in font ec-lmtt10!
Missing character: There is no ├ ("251C) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no │ ("2502) in font ec-lmtt10!
Missing character: There is no ├ ("251C) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no │ ("2502) in font ec-lmtt10!
Missing character: There is no └ ("2514) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no └ ("2514) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ├ ("251C) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ├ ("251C) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ├ ("251C) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no └ ("2514) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!
Missing character: There is no ─ ("2500) in font ec-lmtt10!


[4]

Package natbib Warning: Citation `ref3' on page 5 undefined on input line 273.


Overfull \hbox (63.25961pt too wide) in paragraph at lines 262--276
 [][] 
 []



[5]

LaTeX Warning: `h' float specifier changed to `ht'.


LaTeX Warning: `h' float specifier changed to `ht'.


LaTeX Warning: `h' float specifier changed to `ht'.



[6]
No file pelvic_dataset_paper.bbl.

Package natbib Warning: There were undefined citations.



[7]

[8] (./pelvic_dataset_paper.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.

 ) 
Here is how much of TeX's memory you used:
 5656 strings out of 473832
 87166 string characters out of 5733158
 486938 words of memory out of 5000000
 28670 multiletter control sequences out of 15000+600000
 594128 words of font info for 74 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 57i,9n,65p,1182b,340s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on pelvic_dataset_paper.pdf (8 pages).
