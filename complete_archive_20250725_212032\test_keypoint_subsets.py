#!/usr/bin/env python3
"""
测试关键点子集性能
Test Keypoint Subset Performance
验证不同数量关键点子集的性能
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import json
from tqdm import tqdm

class AdaptivePointNet(nn.Module):
    """自适应PointNet - 支持不同数量的关键点"""
    
    def __init__(self, num_keypoints=12, dropout_rate=0.15):
        super(AdaptivePointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 残差连接
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        # 自适应回归头 - 根据关键点数量调整
        if num_keypoints <= 12:
            # 简单架构适合少量关键点
            self.fc1 = nn.Linear(1024, 512)
            self.fc2 = nn.Linear(512, 256)
            self.fc3 = nn.Linear(256, num_keypoints * 3)
            
            self.fc_bn1 = nn.BatchNorm1d(512)
            self.fc_bn2 = nn.BatchNorm1d(256)
            
            self.dropout1 = nn.Dropout(dropout_rate)
            self.dropout2 = nn.Dropout(dropout_rate)
            
        else:
            # 复杂架构适合更多关键点
            self.fc1 = nn.Linear(1024, 768)
            self.fc2 = nn.Linear(768, 512)
            self.fc3 = nn.Linear(512, 256)
            self.fc4 = nn.Linear(256, num_keypoints * 3)
            
            self.fc_bn1 = nn.BatchNorm1d(768)
            self.fc_bn2 = nn.BatchNorm1d(512)
            self.fc_bn3 = nn.BatchNorm1d(256)
            
            self.dropout1 = nn.Dropout(dropout_rate)
            self.dropout2 = nn.Dropout(dropout_rate * 1.2)
            self.dropout3 = nn.Dropout(dropout_rate * 1.5)
        
        # 权重初始化
        self._initialize_weights()
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"🏗️ AdaptivePointNet({num_keypoints}点): {total_params:,} 参数")
    
    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取 + 残差连接
        x1 = F.relu(self.bn1(self.conv1(x)))
        x2 = F.relu(self.bn2(self.conv2(x1)))
        x3 = F.relu(self.bn3(self.conv3(x2)))
        x3_res = x3 + self.residual1(x1)
        
        x4 = F.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = F.relu(self.bn5(self.conv5(x4_res)))
        
        # 全局最大池化
        global_feat = torch.max(x5, 2)[0]
        
        # 自适应回归
        if self.num_keypoints <= 12:
            x = F.relu(self.fc_bn1(self.fc1(global_feat)))
            x = self.dropout1(x)
            x = F.relu(self.fc_bn2(self.fc2(x)))
            x = self.dropout2(x)
            keypoints = self.fc3(x)
        else:
            x = F.relu(self.fc_bn1(self.fc1(global_feat)))
            x = self.dropout1(x)
            x = F.relu(self.fc_bn2(self.fc2(x)))
            x = self.dropout2(x)
            x = F.relu(self.fc_bn3(self.fc3(x)))
            x = self.dropout3(x)
            keypoints = self.fc4(x)
        
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

class SubsetDataset(Dataset):
    def __init__(self, point_clouds, keypoints, subset_indices):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints[:, subset_indices, :])  # 只选择子集关键点
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def create_subset_normalization(point_clouds, keypoints_57, subset_indices):
    """为关键点子集创建归一化"""
    print(f"🔧 为{len(subset_indices)}点子集创建归一化...")
    
    normalized_pc = []
    normalized_kp = []
    scalers = []
    
    for i in range(len(point_clouds)):
        pc = point_clouds[i].copy()
        kp_subset = keypoints_57[i][subset_indices].copy()  # 只取子集关键点
        
        # 使用子集关键点的中心进行归一化
        subset_center = np.mean(kp_subset, axis=0)
        
        # 中心化
        pc_centered = pc - subset_center
        kp_centered = kp_subset - subset_center
        
        # 合并数据进行统一归一化
        combined_data = np.vstack([pc_centered, kp_centered])
        
        scaler = StandardScaler()
        combined_normalized = scaler.fit_transform(combined_data)
        
        pc_normalized = combined_normalized[:len(pc)]
        kp_normalized = combined_normalized[len(pc):]
        
        normalized_pc.append(pc_normalized)
        normalized_kp.append(kp_normalized)
        scalers.append({'scaler': scaler, 'center': subset_center})
    
    normalized_pc = np.array(normalized_pc)
    normalized_kp = np.array(normalized_kp)
    
    return normalized_pc, normalized_kp, scalers

def train_subset_model(model, train_loader, val_loader, epochs=100, device='cuda'):
    """训练子集模型"""
    
    model = model.to(device)
    
    # 优化器配置
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=20, T_mult=2, eta_min=1e-7
    )
    
    criterion = nn.MSELoss()
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 25
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            loss.backward()
            
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
            optimizer.step()
            
            train_loss += loss.item()
            
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                val_loss += loss.item()
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        scheduler.step()
        
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), f'best_subset_{model.num_keypoints}_model.pth')
        else:
            patience_counter += 1
        
        if epoch % 15 == 0 or epoch < 3:
            current_lr = optimizer.param_groups[0]['lr']
            print(f"Epoch {epoch+1:3d}: "
                  f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
                  f"Train Error: {train_error:.4f}, Val Error: {val_error:.4f}, "
                  f"LR: {current_lr:.2e}")
        
        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    model.load_state_dict(torch.load(f'best_subset_{model.num_keypoints}_model.pth'))
    return model

def test_subset_model(model, test_loader, scalers, test_indices, device='cuda'):
    """测试子集模型"""
    
    model = model.to(device)
    model.eval()
    
    test_predictions = []
    test_targets = []
    
    with torch.no_grad():
        for batch_pc, batch_kp in test_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            predicted = model(batch_pc)
            
            test_predictions.append(predicted.cpu().numpy())
            test_targets.append(batch_kp.cpu().numpy())
    
    test_predictions = np.vstack(test_predictions)
    test_targets = np.vstack(test_targets)
    
    # 反归一化
    real_predictions = []
    real_targets = []
    
    for i, orig_idx in enumerate(test_indices):
        if i < len(test_predictions):
            pred_norm = test_predictions[i]
            target_norm = test_targets[i]
            
            scaler_info = scalers[orig_idx]
            scaler = scaler_info['scaler']
            center = scaler_info['center']
            
            # 反归一化
            dummy_pc = np.zeros((50000, 3))
            
            combined_pred = np.vstack([dummy_pc, pred_norm])
            combined_pred_denorm = scaler.inverse_transform(combined_pred)
            pred_real = combined_pred_denorm[50000:] + center
            
            combined_target = np.vstack([dummy_pc, target_norm])
            combined_target_denorm = scaler.inverse_transform(combined_target)
            target_real = combined_target_denorm[50000:] + center
            
            real_predictions.append(pred_real)
            real_targets.append(target_real)
    
    real_predictions = np.array(real_predictions)
    real_targets = np.array(real_targets)
    
    # 计算误差
    total_error = 0.0
    all_errors = []
    
    for i in range(len(real_predictions)):
        pred = real_predictions[i]
        target = real_targets[i]
        
        distances = np.linalg.norm(pred - target, axis=1)
        total_error += np.mean(distances)
        all_errors.extend(distances)
    
    avg_error = total_error / len(real_predictions)
    
    # 计算准确率
    accuracy_5mm = np.mean(np.array(all_errors) < 5.0) * 100
    accuracy_8mm = np.mean(np.array(all_errors) < 8.0) * 100
    accuracy_10mm = np.mean(np.array(all_errors) < 10.0) * 100
    
    return avg_error, accuracy_5mm, accuracy_8mm, accuracy_10mm

def main():
    """主函数"""
    
    print("🎯 测试关键点子集性能")
    print("验证不同数量关键点子集的性能表现")
    print("=" * 80)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载数据集
    print("📊 加载高质量数据集...")
    data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints_57 = data['keypoints_57']
    sample_ids = data['sample_ids']
    
    # 加载关键点子集配置
    with open('keypoint_difficulty_analysis.json', 'r', encoding='utf-8') as f:
        analysis_results = json.load(f)
    
    subsets_to_test = [12, 18, 24, 30]  # 测试这些数量的子集
    results = {}
    
    print(f"🚀 开始测试{len(subsets_to_test)}个不同的关键点子集...")
    
    for n_points in subsets_to_test:
        print(f"\n{'='*60}")
        print(f"🎯 测试 {n_points} 点子集")
        print(f"{'='*60}")
        
        # 获取子集索引
        subset_indices = analysis_results['suggested_subsets'][str(n_points)]['global_indices']
        region_dist = analysis_results['suggested_subsets'][str(n_points)]['region_distribution']
        
        print(f"📋 子集配置:")
        print(f"   关键点索引: {subset_indices}")
        print(f"   区域分布: F1={region_dist['F1']}, F2={region_dist['F2']}, F3={region_dist['F3']}")
        
        # 创建子集归一化
        normalized_pc, normalized_kp, scalers = create_subset_normalization(
            point_clouds, keypoints_57, subset_indices
        )
        
        # 数据划分
        indices = np.arange(len(normalized_pc))
        train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
        train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
        
        # 创建数据集
        train_dataset = SubsetDataset(normalized_pc[train_indices], normalized_kp[train_indices], 
                                    range(len(subset_indices)))
        val_dataset = SubsetDataset(normalized_pc[val_indices], normalized_kp[val_indices], 
                                  range(len(subset_indices)))
        test_dataset = SubsetDataset(normalized_pc[test_indices], normalized_kp[test_indices], 
                                   range(len(subset_indices)))
        
        # 数据加载器
        batch_size = 8
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
        
        print(f"📋 数据划分: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
        
        # 创建模型
        model = AdaptivePointNet(num_keypoints=n_points, dropout_rate=0.15)
        
        # 训练模型
        print(f"🚀 开始训练{n_points}点模型...")
        model = train_subset_model(model, train_loader, val_loader, epochs=80, device=device)
        
        # 测试模型
        print(f"🔍 测试{n_points}点模型...")
        avg_error, acc_5mm, acc_8mm, acc_10mm = test_subset_model(
            model, test_loader, scalers, test_indices, device=device
        )
        
        # 保存结果
        results[n_points] = {
            'avg_error': float(avg_error),
            'accuracy_5mm': float(acc_5mm),
            'accuracy_8mm': float(acc_8mm),
            'accuracy_10mm': float(acc_10mm),
            'subset_indices': subset_indices,
            'region_distribution': region_dist
        }
        
        print(f"\n🎯 {n_points}点子集结果:")
        print(f"   平均误差: {avg_error:.2f}mm")
        print(f"   <5mm准确率: {acc_5mm:.1f}%")
        print(f"   <8mm准确率: {acc_8mm:.1f}%")
        print(f"   <10mm准确率: {acc_10mm:.1f}%")
    
    # 性能对比
    print(f"\n📊 关键点子集性能对比:")
    print("=" * 80)
    print(f"{'点数':<6} {'误差(mm)':<10} {'<5mm%':<8} {'<8mm%':<8} {'<10mm%':<9} {'vs57点改进'}")
    print("-" * 80)
    
    baseline_57_error = 10.89  # 57点的基线性能
    
    for n_points in sorted(results.keys()):
        result = results[n_points]
        improvement = (baseline_57_error - result['avg_error']) / baseline_57_error * 100
        
        print(f"{n_points:<6} {result['avg_error']:<10.2f} {result['accuracy_5mm']:<8.1f} "
              f"{result['accuracy_8mm']:<8.1f} {result['accuracy_10mm']:<9.1f} {improvement:+6.1f}%")
    
    # 保存完整结果
    final_results = {
        'baseline_57_points': baseline_57_error,
        'subset_results': results,
        'analysis_summary': {
            'best_subset': min(results.keys(), key=lambda k: results[k]['avg_error']),
            'best_error': min(result['avg_error'] for result in results.values()),
            'max_improvement': max((baseline_57_error - result['avg_error']) / baseline_57_error * 100 
                                 for result in results.values())
        }
    }
    
    with open('keypoint_subset_performance_results.json', 'w') as f:
        json.dump(final_results, f, indent=2, default=str)
    
    best_subset = final_results['analysis_summary']['best_subset']
    best_error = final_results['analysis_summary']['best_error']
    max_improvement = final_results['analysis_summary']['max_improvement']
    
    print(f"\n🏆 最佳子集: {best_subset}点")
    print(f"   最佳误差: {best_error:.2f}mm")
    print(f"   最大改进: {max_improvement:.1f}%")
    
    if best_error < 6.0:
        print(f"\n🎉 成功！{best_subset}点子集达到了接近历史5mm的性能！")
        print(f"💡 证明了关键点筛选策略的有效性")
    elif best_error < 8.0:
        print(f"\n✅ 优秀！{best_subset}点子集显著提升了性能")
    else:
        print(f"\n👍 良好！子集策略有一定效果")
    
    print(f"\n💾 详细结果已保存: keypoint_subset_performance_results.json")
    print(f"🎉 关键点子集性能测试完成！")

if __name__ == "__main__":
    main()
