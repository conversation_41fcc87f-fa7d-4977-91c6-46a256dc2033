#!/usr/bin/env python3
"""
测试增强集成模型
Test Enhanced Ensemble Model
评估训练好的增强集成PointNet
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pathlib import Path
from datetime import datetime
import json

# 导入模型架构
from enhanced_simple_ensemble import EnhancedEnsemblePointNet, AdvancedTestTimeAugmentation

class ModelTester:
    """模型测试器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        self.tta = AdvancedTestTimeAugmentation(device)
        
    def load_aligned_data(self):
        """加载对齐数据"""
        print("📦 加载F3对齐数据...")
        
        aligned_files = list(Path("data/processed").glob("f3_aligned_dataset_*.npz"))
        if not aligned_files:
            raise FileNotFoundError("未找到F3对齐数据集")
        
        latest_file = max(aligned_files, key=lambda x: x.stat().st_mtime)
        data = np.load(str(latest_file), allow_pickle=True)
        
        point_clouds = np.array(data['point_clouds'], dtype=np.float32)
        keypoints = np.array(data['keypoints'], dtype=np.float32)
        
        # 数据划分 (使用相同的随机种子)
        from sklearn.model_selection import train_test_split
        indices = np.arange(len(point_clouds))
        train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
        train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
        
        print(f"✅ 数据加载完成: {point_clouds.shape}")
        print(f"   训练: {len(train_indices)}, 验证: {len(val_indices)}, 测试: {len(test_indices)}")
        
        return self.data
    
    def load_trained_model(self):
        """加载训练好的模型"""
        print("\n🔍 寻找训练好的增强集成模型...")
        
        model_dir = Path("trained_models/enhanced_ensemble")
        if not model_dir.exists():
            raise FileNotFoundError("未找到增强集成模型目录")
        
        # 找到最新的模型文件
        model_files = list(model_dir.glob("enhanced_ensemble_*.pth"))
        if not model_files:
            raise FileNotFoundError("未找到增强集成模型文件")
        
        latest_model = max(model_files, key=lambda x: x.stat().st_mtime)
        print(f"   加载模型: {latest_model}")
        
        # 创建模型
        model = EnhancedEnsemblePointNet(num_keypoints=19, num_models=3).to(self.device)
        
        # 加载权重
        checkpoint = torch.load(latest_model, map_location=self.device)
        model.load_state_dict(checkpoint['model_state_dict'])
        
        val_error = checkpoint.get('validation_error', 'Unknown')
        epoch = checkpoint.get('epoch', 'Unknown')
        
        print(f"✅ 模型加载成功")
        print(f"   验证误差: {val_error}mm")
        print(f"   训练轮数: {epoch}")
        
        return model, val_error
    
    def evaluate_model(self, model, split='test'):
        """标准评估"""
        print(f"\n📊 标准评估 ({split}集)...")
        
        model.eval()
        total_error = 0
        num_samples = 0
        individual_errors = []
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for i, (pc, kp) in enumerate(zip(pcs, kps)):
                pc_tensor = torch.FloatTensor(pc).unsqueeze(0).to(self.device)
                kp_tensor = torch.FloatTensor(kp).unsqueeze(0).to(self.device)
                
                pred_kp = model(pc_tensor)
                
                error = torch.mean(torch.norm(pred_kp[0] - kp_tensor[0], dim=1))
                total_error += error.item()
                individual_errors.append(error.item())
                num_samples += 1
                
                del pc_tensor, kp_tensor, pred_kp
                torch.cuda.empty_cache()
        
        avg_error = total_error / num_samples if num_samples > 0 else float('inf')
        std_error = np.std(individual_errors)
        
        print(f"   平均误差: {avg_error:.3f}mm")
        print(f"   标准差: {std_error:.3f}mm")
        print(f"   最小误差: {np.min(individual_errors):.3f}mm")
        print(f"   最大误差: {np.max(individual_errors):.3f}mm")
        
        return avg_error, individual_errors
    
    def evaluate_with_tta(self, model, split='test', num_augmentations=20):
        """TTA评估"""
        print(f"\n🔬 TTA评估 ({split}集, {num_augmentations}次增强)...")
        
        model.eval()
        tta_errors = []
        
        pcs = self.data[split]['point_clouds']
        kps = self.data[split]['keypoints']
        
        for i, (pc, kp) in enumerate(zip(pcs, kps)):
            pc_tensor = torch.FloatTensor(pc).to(self.device)
            kp_tensor = torch.FloatTensor(kp).to(self.device)
            
            # 应用TTA
            tta_pred = self.tta.apply_tta(model, pc_tensor, num_augmentations)
            
            error = torch.mean(torch.norm(tta_pred - kp_tensor, dim=1))
            tta_errors.append(error.item())
            
            if i < 3:  # 显示前几个样本的进度
                print(f"   样本 {i+1}: TTA误差 = {error:.3f}mm")
            
            del pc_tensor, kp_tensor, tta_pred
            torch.cuda.empty_cache()
        
        avg_tta_error = np.mean(tta_errors)
        std_tta_error = np.std(tta_errors)
        
        print(f"   TTA平均误差: {avg_tta_error:.3f}mm")
        print(f"   TTA标准差: {std_tta_error:.3f}mm")
        print(f"   TTA最小误差: {np.min(tta_errors):.3f}mm")
        print(f"   TTA最大误差: {np.max(tta_errors):.3f}mm")
        
        return avg_tta_error, tta_errors
    
    def comprehensive_evaluation(self):
        """综合评估"""
        print("🚀 增强集成PointNet综合评估")
        print("=" * 60)
        
        # 加载数据和模型
        data = self.load_aligned_data()
        model, val_error = self.load_trained_model()
        
        # 验证集评估
        val_error_test, val_individual = self.evaluate_model(model, 'val')
        
        # 测试集标准评估
        test_error, test_individual = self.evaluate_model(model, 'test')
        
        # 测试集TTA评估
        tta_error, tta_individual = self.evaluate_with_tta(model, 'test', num_augmentations=20)
        
        # 结果汇总
        print(f"\n📊 综合评估结果:")
        print("=" * 50)
        print(f"验证集误差:               {val_error_test:.3f}mm")
        print(f"测试集误差 (标准):        {test_error:.3f}mm")
        print(f"测试集误差 (TTA):         {tta_error:.3f}mm")
        
        # 与基线对比
        baseline_error = 8.13
        simple_ensemble_error = 7.19
        
        print(f"\n📈 与基线对比:")
        print(f"基线Point Transformer:     {baseline_error:.2f}mm")
        print(f"简单集成PointNet:         {simple_ensemble_error:.2f}mm")
        print(f"增强集成PointNet:         {test_error:.2f}mm")
        print(f"增强集成 + TTA:           {tta_error:.2f}mm")
        
        # 改进分析
        improvement_vs_baseline = (baseline_error - test_error) / baseline_error * 100
        improvement_vs_simple = (simple_ensemble_error - test_error) / simple_ensemble_error * 100
        tta_improvement = (test_error - tta_error) / test_error * 100
        
        print(f"\n📈 改进分析:")
        print(f"vs 基线改进:              {improvement_vs_baseline:+.1f}%")
        print(f"vs 简单集成改进:          {improvement_vs_simple:+.1f}%")
        print(f"TTA额外改进:              {tta_improvement:+.1f}%")
        
        # 医疗级精度评估
        best_error = min(test_error, tta_error)
        medical_target = 5.0
        
        print(f"\n🎯 医疗级精度评估:")
        print(f"医疗级目标:               {medical_target:.1f}mm")
        print(f"当前最佳结果:             {best_error:.2f}mm")
        
        if best_error <= medical_target:
            print("🎉 成功达到医疗级精度！")
            status = "医疗级精度达成"
        elif best_error <= 6.0:
            remaining = best_error - medical_target
            print(f"🎯 非常接近医疗级！还需改进{remaining:.2f}mm")
            status = f"接近医疗级，还需{remaining:.2f}mm"
        else:
            remaining = best_error - medical_target
            print(f"📈 距离医疗级还需改进{remaining:.2f}mm")
            status = f"需要改进{remaining:.2f}mm"
        
        # 统计分析
        print(f"\n📊 详细统计:")
        print(f"测试集样本数:             {len(test_individual)}")
        print(f"标准评估变异系数:         {np.std(test_individual)/np.mean(test_individual)*100:.1f}%")
        print(f"TTA评估变异系数:          {np.std(tta_individual)/np.mean(tta_individual)*100:.1f}%")
        
        # 保存结果
        results = {
            "evaluation_timestamp": datetime.now().isoformat(),
            "model_type": "EnhancedEnsemblePointNet",
            "validation_error": float(val_error_test),
            "test_error_standard": float(test_error),
            "test_error_tta": float(tta_error),
            "best_error": float(best_error),
            "baseline_error": baseline_error,
            "simple_ensemble_error": simple_ensemble_error,
            "medical_target": medical_target,
            "medical_grade_achieved": best_error <= medical_target,
            "status": status,
            "improvements": {
                "vs_baseline_percent": float(improvement_vs_baseline),
                "vs_simple_ensemble_percent": float(improvement_vs_simple),
                "tta_improvement_percent": float(tta_improvement)
            },
            "statistics": {
                "test_samples": len(test_individual),
                "standard_cv": float(np.std(test_individual)/np.mean(test_individual)*100),
                "tta_cv": float(np.std(tta_individual)/np.mean(tta_individual)*100),
                "test_std": float(np.std(test_individual)),
                "tta_std": float(np.std(tta_individual))
            },
            "individual_errors": {
                "test_standard": [float(x) for x in test_individual],
                "test_tta": [float(x) for x in tta_individual]
            }
        }
        
        # 保存结果
        results_dir = Path("results/enhanced_ensemble_evaluation")
        results_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"enhanced_ensemble_evaluation_{timestamp}.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 评估结果已保存: {results_file}")
        
        return results

def main():
    """主函数"""
    tester = ModelTester()
    results = tester.comprehensive_evaluation()
    return tester, results

if __name__ == "__main__":
    tester, results = main()
