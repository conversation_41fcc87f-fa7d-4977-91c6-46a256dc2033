#!/usr/bin/env python3
"""
轻量级数据预处理优化
Lightweight Data Preprocessing Optimization
目标: 保留个体差异，只做必要的标准化，预期改进5-10%
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json
from pathlib import Path
from datetime import datetime
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler

class LightweightPreprocessor:
    """轻量级预处理器 - 保留医学数据的个体差异"""
    
    def __init__(self, data_path='data/raw/high_quality_f3_dataset.npz'):
        self.data_path = data_path
        self.load_data()
        
    def load_data(self):
        """加载原始数据"""
        print(f"📦 加载原始数据: {self.data_path}")
        
        data = np.load(self.data_path, allow_pickle=True)
        self.sample_ids = data['sample_ids']
        self.point_clouds = data['point_clouds']
        self.keypoints = data['keypoints']
        
        print(f"✅ 数据加载完成: {len(self.sample_ids)} 样本")
        print(f"   点云形状: {self.point_clouds.shape}")
        print(f"   关键点形状: {self.keypoints.shape}")
        
    def analyze_coordinate_systems(self):
        """分析坐标系一致性"""
        print("\n🧭 分析坐标系一致性...")
        
        # 计算每个样本的统计信息
        sample_stats = []
        
        for i, (pc, kp) in enumerate(zip(self.point_clouds, self.keypoints)):
            # 点云统计
            pc_center = np.mean(pc, axis=0)
            pc_std = np.std(pc, axis=0)
            pc_range = np.max(pc, axis=0) - np.min(pc, axis=0)
            
            # 关键点统计
            kp_center = np.mean(kp, axis=0)
            kp_std = np.std(kp, axis=0)
            kp_range = np.max(kp, axis=0) - np.min(kp, axis=0)
            
            # 中心偏移
            center_offset = np.linalg.norm(pc_center - kp_center)
            
            # 尺度比例
            pc_scale = np.linalg.norm(pc_range)
            kp_scale = np.linalg.norm(kp_range)
            scale_ratio = kp_scale / pc_scale if pc_scale > 0 else 0
            
            sample_stats.append({
                'sample_id': self.sample_ids[i],
                'pc_center': pc_center,
                'kp_center': kp_center,
                'center_offset': center_offset,
                'pc_scale': pc_scale,
                'kp_scale': kp_scale,
                'scale_ratio': scale_ratio,
                'pc_range': pc_range,
                'kp_range': kp_range
            })
        
        # 分析一致性
        center_offsets = [s['center_offset'] for s in sample_stats]
        scale_ratios = [s['scale_ratio'] for s in sample_stats]
        
        analysis_result = {
            'center_offset_stats': {
                'mean': np.mean(center_offsets),
                'std': np.std(center_offsets),
                'max': np.max(center_offsets),
                'outliers': np.sum(np.array(center_offsets) > np.mean(center_offsets) + 2*np.std(center_offsets))
            },
            'scale_ratio_stats': {
                'mean': np.mean(scale_ratios),
                'std': np.std(scale_ratios),
                'min': np.min(scale_ratios),
                'max': np.max(scale_ratios),
                'outliers': np.sum(np.abs(np.array(scale_ratios) - np.mean(scale_ratios)) > 2*np.std(scale_ratios))
            },
            'sample_stats': sample_stats
        }
        
        print(f"📊 坐标系分析结果:")
        print(f"   中心偏移: {analysis_result['center_offset_stats']['mean']:.2f}±{analysis_result['center_offset_stats']['std']:.2f}mm")
        print(f"   最大偏移: {analysis_result['center_offset_stats']['max']:.2f}mm")
        print(f"   偏移异常: {analysis_result['center_offset_stats']['outliers']} 样本")
        print(f"   尺度比例: {analysis_result['scale_ratio_stats']['mean']:.3f}±{analysis_result['scale_ratio_stats']['std']:.3f}")
        print(f"   尺度异常: {analysis_result['scale_ratio_stats']['outliers']} 样本")
        
        return analysis_result
    
    def gentle_alignment(self, preserve_individual_differences=True):
        """温和的对齐处理 - 保留个体差异"""
        print("\n🔧 执行温和的坐标对齐...")
        
        aligned_point_clouds = []
        aligned_keypoints = []
        alignment_info = []
        
        for i, (pc, kp) in enumerate(zip(self.point_clouds, self.keypoints)):
            # 1. 温和的中心化 - 使用关键点中心作为参考
            kp_center = np.mean(kp, axis=0)
            pc_center = np.mean(pc, axis=0)
            
            # 只有当偏移过大时才进行对齐
            center_offset = np.linalg.norm(pc_center - kp_center)
            if center_offset > 15.0:  # 15mm阈值，比之前的10mm更宽松
                # 将点云中心对齐到关键点中心
                pc_aligned = pc - pc_center + kp_center
                alignment_applied = True
                alignment_type = "center_alignment"
            else:
                pc_aligned = pc.copy()
                alignment_applied = False
                alignment_type = "no_alignment"
            
            # 2. 保持原始尺度 - 不进行尺度标准化
            # 医学数据的个体差异是有意义的，应该保留
            
            # 3. 温和的方向对齐 - 只修正明显的方向错误
            # 使用PCA检查主方向
            pc_centered = pc_aligned - np.mean(pc_aligned, axis=0)
            if len(pc_centered) > 3:  # 确保有足够的点进行PCA
                try:
                    pca = PCA(n_components=3)
                    pca.fit(pc_centered)
                    
                    # 检查是否需要方向修正（这里采用保守策略）
                    # 只有当主方向与预期方向偏差过大时才修正
                    principal_direction = pca.components_[0]
                    
                    # 简单的方向一致性检查
                    # 如果Z轴主方向向下，可能需要翻转
                    if principal_direction[2] < -0.8:  # 很保守的阈值
                        pc_aligned[:, 2] = -pc_aligned[:, 2]
                        kp[:, 2] = -kp[:, 2]
                        alignment_type += "_z_flip"
                        
                except:
                    # PCA失败时保持原样
                    pass
            
            aligned_point_clouds.append(pc_aligned)
            aligned_keypoints.append(kp)
            
            alignment_info.append({
                'sample_id': self.sample_ids[i],
                'original_center_offset': center_offset,
                'alignment_applied': alignment_applied,
                'alignment_type': alignment_type
            })
        
        self.aligned_point_clouds = np.array(aligned_point_clouds, dtype=object)
        self.aligned_keypoints = np.array(aligned_keypoints)
        
        # 统计对齐结果
        aligned_count = sum(1 for info in alignment_info if info['alignment_applied'])
        print(f"✅ 温和对齐完成:")
        print(f"   需要对齐的样本: {aligned_count}/{len(self.sample_ids)}")
        print(f"   保持原样的样本: {len(self.sample_ids) - aligned_count}")
        
        return alignment_info
    
    def smart_normalization(self, method='robust'):
        """智能标准化 - 保留相对关系"""
        print(f"\n📏 执行智能标准化 (方法: {method})...")
        
        if method == 'robust':
            # 鲁棒标准化 - 使用中位数和四分位距
            normalized_point_clouds = []
            normalized_keypoints = []
            normalization_params = []
            
            for i, (pc, kp) in enumerate(zip(self.aligned_point_clouds, self.aligned_keypoints)):
                # 计算鲁棒统计量
                pc_median = np.median(pc, axis=0)
                pc_mad = np.median(np.abs(pc - pc_median), axis=0)  # 中位数绝对偏差
                
                # 避免除零
                pc_mad = np.where(pc_mad == 0, 1.0, pc_mad)
                
                # 鲁棒标准化
                pc_normalized = (pc - pc_median) / pc_mad
                kp_normalized = (kp - pc_median) / pc_mad
                
                normalized_point_clouds.append(pc_normalized)
                normalized_keypoints.append(kp_normalized)
                
                normalization_params.append({
                    'sample_id': self.sample_ids[i],
                    'median': pc_median,
                    'mad': pc_mad,
                    'method': 'robust'
                })
                
        elif method == 'percentile':
            # 百分位标准化 - 使用5%-95%范围
            normalized_point_clouds = []
            normalized_keypoints = []
            normalization_params = []
            
            for i, (pc, kp) in enumerate(zip(self.aligned_point_clouds, self.aligned_keypoints)):
                # 计算百分位数
                pc_p5 = np.percentile(pc, 5, axis=0)
                pc_p95 = np.percentile(pc, 95, axis=0)
                pc_range = pc_p95 - pc_p5
                
                # 避免除零
                pc_range = np.where(pc_range == 0, 1.0, pc_range)
                
                # 百分位标准化到[0, 1]
                pc_normalized = (pc - pc_p5) / pc_range
                kp_normalized = (kp - pc_p5) / pc_range
                
                normalized_point_clouds.append(pc_normalized)
                normalized_keypoints.append(kp_normalized)
                
                normalization_params.append({
                    'sample_id': self.sample_ids[i],
                    'p5': pc_p5,
                    'p95': pc_p95,
                    'range': pc_range,
                    'method': 'percentile'
                })
                
        elif method == 'none':
            # 不进行标准化，保持原始尺度
            normalized_point_clouds = [pc.copy() for pc in self.aligned_point_clouds]
            normalized_keypoints = self.aligned_keypoints.copy()
            normalization_params = [{'sample_id': sid, 'method': 'none'} for sid in self.sample_ids]
            
        self.normalized_point_clouds = np.array(normalized_point_clouds, dtype=object)
        self.normalized_keypoints = np.array(normalized_keypoints)
        self.normalization_params = normalization_params
        
        print(f"✅ 智能标准化完成 (方法: {method})")
        
        return normalization_params
    
    def quality_control_check(self):
        """质量控制检查"""
        print("\n🔍 执行质量控制检查...")
        
        issues_found = []
        
        for i, (pc, kp) in enumerate(zip(self.normalized_point_clouds, self.normalized_keypoints)):
            sample_issues = []

            # 确保数据类型正确
            pc = np.array(pc, dtype=np.float64)
            kp = np.array(kp, dtype=np.float64)

            # 检查NaN值
            if np.any(np.isnan(pc)) or np.any(np.isnan(kp)):
                sample_issues.append("包含NaN值")

            # 检查无穷值
            if np.any(np.isinf(pc)) or np.any(np.isinf(kp)):
                sample_issues.append("包含无穷值")
            
            # 检查点云是否为空
            if len(pc) == 0:
                sample_issues.append("点云为空")
            
            # 检查关键点数量
            if len(kp) != 19:
                sample_issues.append(f"关键点数量错误: {len(kp)}")
            
            # 检查极端值
            pc_range = np.max(pc) - np.min(pc)
            if pc_range > 1000 or pc_range < 0.001:  # 极端的范围值
                sample_issues.append(f"点云范围异常: {pc_range}")
            
            if sample_issues:
                issues_found.append({
                    'sample_id': self.sample_ids[i],
                    'sample_index': i,
                    'issues': sample_issues
                })
        
        print(f"📊 质量控制结果:")
        print(f"   有问题的样本: {len(issues_found)}/{len(self.sample_ids)}")
        
        if issues_found:
            print("   问题详情:")
            for issue in issues_found[:5]:  # 只显示前5个
                print(f"     样本 {issue['sample_id']}: {', '.join(issue['issues'])}")
        
        return issues_found
    
    def save_preprocessed_data(self, output_path=None):
        """保存预处理后的数据"""
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"data/processed/lightweight_preprocessed_{timestamp}.npz"
        
        output_dir = Path(output_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"\n💾 保存预处理数据到: {output_path}")
        
        # 确保数组形状一致
        max_points = max(len(pc) for pc in self.normalized_point_clouds)
        
        # 填充或截断到统一长度
        uniform_point_clouds = []
        for pc in self.normalized_point_clouds:
            if len(pc) > max_points:
                # 随机采样
                indices = np.random.choice(len(pc), max_points, replace=False)
                uniform_pc = pc[indices]
            elif len(pc) < max_points:
                # 重复采样
                indices = np.random.choice(len(pc), max_points, replace=True)
                uniform_pc = pc[indices]
            else:
                uniform_pc = pc
            uniform_point_clouds.append(uniform_pc)
        
        uniform_point_clouds = np.array(uniform_point_clouds)
        
        # 保存数据
        np.savez_compressed(
            output_path,
            sample_ids=self.sample_ids,
            point_clouds=uniform_point_clouds,
            keypoints=self.normalized_keypoints,
            original_point_clouds=self.point_clouds,
            original_keypoints=self.keypoints,
            normalization_params=self.normalization_params
        )
        
        print(f"✅ 预处理数据保存完成")
        print(f"   预处理后点云形状: {uniform_point_clouds.shape}")
        print(f"   预处理后关键点形状: {self.normalized_keypoints.shape}")
        
        return output_path
    
    def visualize_preprocessing_effects(self, sample_indices=[0, 1, 2], save_plots=True):
        """可视化预处理效果"""
        print(f"\n📊 可视化预处理效果...")
        
        viz_dir = Path("results/preprocessing_visualization")
        viz_dir.mkdir(parents=True, exist_ok=True)
        
        for idx in sample_indices:
            if idx >= len(self.sample_ids):
                continue
                
            sample_id = self.sample_ids[idx]
            
            fig = plt.figure(figsize=(20, 6))
            
            # 原始数据
            ax1 = fig.add_subplot(131, projection='3d')
            orig_pc = self.point_clouds[idx]
            orig_kp = self.keypoints[idx]
            
            ax1.scatter(orig_pc[:, 0], orig_pc[:, 1], orig_pc[:, 2], 
                       c='lightgray', s=1, alpha=0.3, label='Original PC')
            ax1.scatter(orig_kp[:, 0], orig_kp[:, 1], orig_kp[:, 2], 
                       c='red', s=50, label='Original KP')
            ax1.set_title(f'Original Data - {sample_id}')
            ax1.legend()
            
            # 对齐后数据
            ax2 = fig.add_subplot(132, projection='3d')
            aligned_pc = self.aligned_point_clouds[idx]
            aligned_kp = self.aligned_keypoints[idx]
            
            ax2.scatter(aligned_pc[:, 0], aligned_pc[:, 1], aligned_pc[:, 2], 
                       c='lightblue', s=1, alpha=0.3, label='Aligned PC')
            ax2.scatter(aligned_kp[:, 0], aligned_kp[:, 1], aligned_kp[:, 2], 
                       c='blue', s=50, label='Aligned KP')
            ax2.set_title(f'After Alignment - {sample_id}')
            ax2.legend()
            
            # 标准化后数据
            ax3 = fig.add_subplot(133, projection='3d')
            norm_pc = self.normalized_point_clouds[idx]
            norm_kp = self.normalized_keypoints[idx]
            
            ax3.scatter(norm_pc[:, 0], norm_pc[:, 1], norm_pc[:, 2], 
                       c='lightgreen', s=1, alpha=0.3, label='Normalized PC')
            ax3.scatter(norm_kp[:, 0], norm_kp[:, 1], norm_kp[:, 2], 
                       c='green', s=50, label='Normalized KP')
            ax3.set_title(f'After Normalization - {sample_id}')
            ax3.legend()
            
            plt.tight_layout()
            
            if save_plots:
                plt.savefig(viz_dir / f"preprocessing_effect_{sample_id}.png", 
                           dpi=150, bbox_inches='tight')
                print(f"💾 保存可视化: preprocessing_effect_{sample_id}.png")
            
            plt.show()

def run_lightweight_preprocessing():
    """运行轻量级预处理"""
    print("🔧 轻量级数据预处理优化")
    print("=" * 60)
    print("目标: 保留个体差异，只做必要的标准化")
    
    # 创建预处理器
    preprocessor = LightweightPreprocessor()
    
    # 1. 分析坐标系
    coord_analysis = preprocessor.analyze_coordinate_systems()
    
    # 2. 温和对齐
    alignment_info = preprocessor.gentle_alignment()
    
    # 3. 智能标准化 - 提供多种选择
    print(f"\n🤔 选择标准化方法:")
    print(f"1. robust - 鲁棒标准化 (推荐)")
    print(f"2. percentile - 百分位标准化")
    print(f"3. none - 不标准化 (保持原始尺度)")
    
    # 默认使用鲁棒标准化
    normalization_method = 'robust'
    normalization_params = preprocessor.smart_normalization(method=normalization_method)
    
    # 4. 质量控制
    quality_issues = preprocessor.quality_control_check()
    
    # 5. 保存预处理数据
    output_path = preprocessor.save_preprocessed_data()
    
    # 6. 可视化效果
    preprocessor.visualize_preprocessing_effects()
    
    # 7. 生成预处理报告
    preprocessing_report = {
        "preprocessing_timestamp": datetime.now().isoformat(),
        "input_data": str(preprocessor.data_path),
        "output_data": output_path,
        "total_samples": len(preprocessor.sample_ids),
        "coordinate_analysis": coord_analysis,
        "alignment_summary": {
            "samples_aligned": sum(1 for info in alignment_info if info['alignment_applied']),
            "samples_unchanged": sum(1 for info in alignment_info if not info['alignment_applied'])
        },
        "normalization_method": normalization_method,
        "quality_issues": len(quality_issues),
        "expected_improvement": "5-10% 性能提升",
        "next_steps": [
            "使用预处理后的数据重新训练模型",
            "对比预处理前后的性能差异",
            "如果效果好，继续进行模型架构优化"
        ]
    }
    
    # 保存报告
    report_dir = Path("results/preprocessing_reports")
    report_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = report_dir / f"lightweight_preprocessing_report_{timestamp}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(preprocessing_report, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n📊 轻量级预处理完成!")
    print(f"📈 预期改进: 5-10%")
    print(f"💾 预处理数据: {output_path}")
    print(f"📋 详细报告: {report_file}")
    print(f"\n🎯 下一步: 使用预处理后的数据重新训练模型")
    
    return preprocessor, preprocessing_report

if __name__ == "__main__":
    preprocessor, report = run_lightweight_preprocessing()
