#!/usr/bin/env python3
"""
数据集质量改进策略
Dataset Quality Improvement Strategy
系统性分析和改进数据集质量，支持渐进式扩展
"""

import numpy as np
import torch
import json
import matplotlib.pyplot as plt
from pathlib import Path
import open3d as o3d
from sklearn.metrics import pairwise_distances
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')

class DatasetQualityAnalyzer:
    """数据集质量分析器"""
    
    def __init__(self):
        self.historical_data = None
        self.current_data = None
        self.quality_metrics = {}
        
    def load_datasets(self):
        """加载数据集"""
        
        print("📊 加载数据集进行质量分析...")
        
        # 加载历史数据集
        try:
            hist_data = np.load('archive/old_experiments/f3_reduced_12kp_stable.npz', allow_pickle=True)
            self.historical_data = {
                'point_clouds': hist_data['point_clouds'],
                'keypoints': hist_data['keypoints'],
                'sample_ids': hist_data['sample_ids'],
                'name': 'f3_reduced_12kp_stable',
                'performance': 6.067  # 已知性能
            }
            print(f"✅ 历史数据集: {len(self.historical_data['sample_ids'])}样本, 12点")
        except Exception as e:
            print(f"❌ 无法加载历史数据集: {e}")
            return False
        
        # 加载当前数据集
        try:
            curr_data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
            self.current_data = {
                'point_clouds': curr_data['point_clouds'],
                'keypoints_57': curr_data['keypoints_57'],
                'sample_ids': curr_data['sample_ids'],
                'name': 'high_quality_pelvis_57',
                'performance': 10.89  # 已知性能
            }
            print(f"✅ 当前数据集: {len(self.current_data['sample_ids'])}样本, 57点")
        except Exception as e:
            print(f"❌ 无法加载当前数据集: {e}")
            return False
        
        return True
    
    def analyze_coordinate_consistency(self):
        """分析坐标系一致性"""
        
        print("\n🔍 坐标系一致性分析")
        print("=" * 60)
        
        # 历史数据集分析
        hist_kp = self.historical_data['keypoints']
        hist_pc = self.historical_data['point_clouds']
        
        print(f"📋 历史数据集坐标分析:")
        print(f"   关键点范围: [{np.min(hist_kp):.2f}, {np.max(hist_kp):.2f}]")
        print(f"   点云范围: [{np.min(hist_pc):.2f}, {np.max(hist_pc):.2f}]")
        print(f"   关键点中心: {np.mean(hist_kp, axis=(0,1))}")
        print(f"   点云中心: {np.mean(hist_pc, axis=(0,1))}")
        
        # 当前数据集分析（12点子集）
        curr_kp_57 = self.current_data['keypoints_57']
        curr_pc = self.current_data['point_clouds']
        
        # 提取对应的12点
        hist_12_indices = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17]
        curr_kp_12 = curr_kp_57[:, hist_12_indices, :]
        
        print(f"\n📋 当前数据集坐标分析:")
        print(f"   12点子集范围: [{np.min(curr_kp_12):.2f}, {np.max(curr_kp_12):.2f}]")
        print(f"   点云范围: [{np.min(curr_pc):.2f}, {np.max(curr_pc):.2f}]")
        print(f"   12点中心: {np.mean(curr_kp_12, axis=(0,1))}")
        print(f"   点云中心: {np.mean(curr_pc, axis=(0,1))}")
        
        # 坐标系差异分析
        hist_scale = np.std(hist_kp)
        curr_scale = np.std(curr_kp_12)
        scale_ratio = curr_scale / hist_scale
        
        print(f"\n💡 坐标系差异:")
        print(f"   历史数据标准差: {hist_scale:.2f}")
        print(f"   当前数据标准差: {curr_scale:.2f}")
        print(f"   尺度比例: {scale_ratio:.2f}")
        
        self.quality_metrics['coordinate_consistency'] = {
            'historical_range': [float(np.min(hist_kp)), float(np.max(hist_kp))],
            'current_range': [float(np.min(curr_kp_12)), float(np.max(curr_kp_12))],
            'scale_ratio': float(scale_ratio),
            'consistency_score': 1.0 / max(1.0, abs(scale_ratio - 1.0))
        }
        
        return scale_ratio
    
    def analyze_surface_projection_quality(self):
        """分析表面投影质量"""
        
        print("\n🔍 表面投影质量分析")
        print("=" * 60)
        
        def calculate_projection_quality(point_clouds, keypoints, dataset_name):
            """计算投影质量"""
            
            projection_distances = []
            within_5mm_count = 0
            within_1mm_count = 0
            
            for i in range(min(10, len(point_clouds))):  # 分析前10个样本
                pc = point_clouds[i]
                kp = keypoints[i]
                
                # 计算每个关键点到最近点云点的距离
                distances = cdist(kp, pc)
                min_distances = np.min(distances, axis=1)
                
                projection_distances.extend(min_distances)
                within_5mm_count += np.sum(min_distances < 5.0)
                within_1mm_count += np.sum(min_distances < 1.0)
            
            total_keypoints = len(projection_distances)
            avg_distance = np.mean(projection_distances)
            within_5mm_percent = (within_5mm_count / total_keypoints) * 100
            within_1mm_percent = (within_1mm_count / total_keypoints) * 100
            
            print(f"📊 {dataset_name}投影质量:")
            print(f"   平均投影距离: {avg_distance:.3f}mm")
            print(f"   <5mm准确率: {within_5mm_percent:.1f}%")
            print(f"   <1mm准确率: {within_1mm_percent:.1f}%")
            
            return {
                'avg_distance': avg_distance,
                'within_5mm_percent': within_5mm_percent,
                'within_1mm_percent': within_1mm_percent
            }
        
        # 分析历史数据集
        hist_quality = calculate_projection_quality(
            self.historical_data['point_clouds'],
            self.historical_data['keypoints'],
            "历史数据集"
        )
        
        # 分析当前数据集（12点子集）
        hist_12_indices = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17]
        curr_kp_12 = self.current_data['keypoints_57'][:, hist_12_indices, :]
        
        curr_quality = calculate_projection_quality(
            self.current_data['point_clouds'],
            curr_kp_12,
            "当前数据集"
        )
        
        self.quality_metrics['surface_projection'] = {
            'historical': hist_quality,
            'current': curr_quality
        }
        
        return hist_quality, curr_quality
    
    def analyze_annotation_consistency(self):
        """分析标注一致性"""
        
        print("\n🔍 标注一致性分析")
        print("=" * 60)
        
        # 分析关键点间距离的一致性
        def analyze_keypoint_distances(keypoints, dataset_name):
            """分析关键点间距离"""
            
            inter_distances = []
            
            for i in range(len(keypoints)):
                kp = keypoints[i]
                # 计算关键点间的距离
                distances = pairwise_distances(kp)
                # 取上三角矩阵（避免重复）
                upper_tri = np.triu(distances, k=1)
                non_zero_distances = upper_tri[upper_tri > 0]
                inter_distances.extend(non_zero_distances)
            
            mean_distance = np.mean(inter_distances)
            std_distance = np.std(inter_distances)
            cv = std_distance / mean_distance  # 变异系数
            
            print(f"📊 {dataset_name}关键点间距离:")
            print(f"   平均距离: {mean_distance:.2f}mm")
            print(f"   标准差: {std_distance:.2f}mm")
            print(f"   变异系数: {cv:.3f}")
            
            return {
                'mean_distance': mean_distance,
                'std_distance': std_distance,
                'coefficient_variation': cv
            }
        
        # 分析历史数据集
        hist_consistency = analyze_keypoint_distances(
            self.historical_data['keypoints'],
            "历史数据集"
        )
        
        # 分析当前数据集（12点子集）
        hist_12_indices = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17]
        curr_kp_12 = self.current_data['keypoints_57'][:, hist_12_indices, :]
        
        curr_consistency = analyze_keypoint_distances(
            curr_kp_12,
            "当前数据集"
        )
        
        self.quality_metrics['annotation_consistency'] = {
            'historical': hist_consistency,
            'current': curr_consistency
        }
        
        return hist_consistency, curr_consistency
    
    def analyze_data_complexity(self):
        """分析数据复杂度"""
        
        print("\n🔍 数据复杂度分析")
        print("=" * 60)
        
        # 分析点云复杂度
        def analyze_point_cloud_complexity(point_clouds, dataset_name):
            """分析点云复杂度"""
            
            complexities = []
            
            for i in range(min(10, len(point_clouds))):
                pc = point_clouds[i]
                
                # 计算点云的几何复杂度指标
                # 1. 点密度变化
                distances = pairwise_distances(pc)
                np.fill_diagonal(distances, np.inf)
                nearest_distances = np.min(distances, axis=1)
                density_variation = np.std(nearest_distances) / np.mean(nearest_distances)
                
                # 2. 表面曲率估计（简化）
                # 使用最近邻点的法向量变化来估计曲率
                curvature_estimate = np.std(pc, axis=0).mean()
                
                complexities.append({
                    'density_variation': density_variation,
                    'curvature_estimate': curvature_estimate
                })
            
            avg_density_var = np.mean([c['density_variation'] for c in complexities])
            avg_curvature = np.mean([c['curvature_estimate'] for c in complexities])
            
            print(f"📊 {dataset_name}复杂度:")
            print(f"   密度变异: {avg_density_var:.3f}")
            print(f"   曲率估计: {avg_curvature:.3f}")
            
            return {
                'density_variation': avg_density_var,
                'curvature_estimate': avg_curvature
            }
        
        # 分析两个数据集
        hist_complexity = analyze_point_cloud_complexity(
            self.historical_data['point_clouds'],
            "历史数据集"
        )
        
        curr_complexity = analyze_point_cloud_complexity(
            self.current_data['point_clouds'],
            "当前数据集"
        )
        
        self.quality_metrics['data_complexity'] = {
            'historical': hist_complexity,
            'current': curr_complexity
        }
        
        return hist_complexity, curr_complexity

class DatasetImprovementStrategy:
    """数据集改进策略"""
    
    def __init__(self, quality_metrics):
        self.quality_metrics = quality_metrics
        
    def design_coordinate_unification(self):
        """设计坐标系统一策略"""
        
        print("\n🔧 坐标系统一策略")
        print("=" * 60)
        
        coord_metrics = self.quality_metrics['coordinate_consistency']
        scale_ratio = coord_metrics['scale_ratio']
        
        print(f"📋 当前问题:")
        print(f"   尺度比例: {scale_ratio:.2f}")
        print(f"   一致性分数: {coord_metrics['consistency_score']:.3f}")
        
        strategies = []
        
        if abs(scale_ratio - 1.0) > 0.1:
            strategies.append({
                "策略": "尺度归一化",
                "描述": f"将当前数据集缩放{1/scale_ratio:.3f}倍以匹配历史数据集",
                "优先级": "高",
                "预期改进": "30-50%"
            })
        
        strategies.append({
            "策略": "中心对齐",
            "描述": "统一关键点和点云的中心位置",
            "优先级": "高",
            "预期改进": "20-30%"
        })
        
        strategies.append({
            "策略": "坐标系标准化",
            "描述": "建立统一的解剖学坐标系（LPS或RAS）",
            "优先级": "中",
            "预期改进": "10-20%"
        })
        
        print(f"\n💡 改进策略:")
        for strategy in strategies:
            print(f"   • {strategy['策略']} ({strategy['优先级']}优先级)")
            print(f"     {strategy['描述']}")
            print(f"     预期改进: {strategy['预期改进']}")
        
        return strategies
    
    def design_surface_projection_improvement(self):
        """设计表面投影改进策略"""
        
        print("\n🔧 表面投影改进策略")
        print("=" * 60)
        
        proj_metrics = self.quality_metrics['surface_projection']
        hist_quality = proj_metrics['historical']
        curr_quality = proj_metrics['current']
        
        print(f"📋 当前质量对比:")
        print(f"   历史数据集<1mm: {hist_quality['within_1mm_percent']:.1f}%")
        print(f"   当前数据集<1mm: {curr_quality['within_1mm_percent']:.1f}%")
        print(f"   质量差距: {hist_quality['within_1mm_percent'] - curr_quality['within_1mm_percent']:.1f}%")
        
        strategies = []
        
        if curr_quality['within_1mm_percent'] < 80:
            strategies.append({
                "策略": "高精度表面重建",
                "描述": "使用Poisson重建+细分提高表面精度",
                "目标": ">90% <1mm准确率",
                "优先级": "极高"
            })
        
        if curr_quality['avg_distance'] > 2.0:
            strategies.append({
                "策略": "关键点表面投影",
                "描述": "将关键点投影到最近表面点",
                "目标": "平均距离<1mm",
                "优先级": "高"
            })
        
        strategies.append({
            "策略": "质量验证流程",
            "描述": "建立自动化的投影质量检测",
            "目标": "实时质量监控",
            "优先级": "中"
        })
        
        print(f"\n💡 改进策略:")
        for strategy in strategies:
            print(f"   • {strategy['策略']} ({strategy['优先级']}优先级)")
            print(f"     {strategy['描述']}")
            print(f"     目标: {strategy['目标']}")
        
        return strategies
    
    def design_data_augmentation_strategy(self):
        """设计数据增强策略"""
        
        print("\n🔧 医学约束数据增强策略")
        print("=" * 60)
        
        augmentation_methods = [
            {
                "方法": "解剖学约束旋转",
                "描述": "在解剖学合理范围内旋转（±15度）",
                "增量": "3倍数据",
                "质量": "高",
                "实现难度": "低"
            },
            {
                "方法": "弹性形变",
                "描述": "模拟个体差异的弹性变形",
                "增量": "2倍数据",
                "质量": "中",
                "实现难度": "中"
            },
            {
                "方法": "噪声注入",
                "描述": "添加医学成像噪声（0.1-0.5mm）",
                "增量": "2倍数据",
                "质量": "高",
                "实现难度": "低"
            },
            {
                "方法": "尺度变化",
                "描述": "模拟不同体型（±10%）",
                "增量": "2倍数据",
                "质量": "高",
                "实现难度": "低"
            },
            {
                "方法": "部分遮挡",
                "描述": "模拟扫描不完整情况",
                "增量": "1.5倍数据",
                "质量": "中",
                "实现难度": "中"
            }
        ]
        
        print(f"📋 增强方法评估:")
        print(f"{'方法':<15} {'增量':<10} {'质量':<6} {'难度':<6} {'描述'}")
        print("-" * 70)
        
        total_multiplier = 1.0
        for method in augmentation_methods:
            multiplier = float(method['增量'].replace('倍数据', ''))
            total_multiplier *= multiplier
            print(f"{method['方法']:<15} {method['增量']:<10} {method['质量']:<6} {method['实现难度']:<6} {method['描述']}")
        
        print(f"\n💡 增强效果预估:")
        print(f"   当前样本: 96个")
        print(f"   理论最大: {int(96 * total_multiplier)}个")
        print(f"   推荐组合: {int(96 * 3 * 2)}个 (旋转+噪声)")
        print(f"   保守估计: {int(96 * 2)}个 (仅旋转)")
        
        return augmentation_methods
    
    def create_implementation_roadmap(self):
        """创建实施路线图"""
        
        print("\n🔧 数据集改进实施路线图")
        print("=" * 60)
        
        roadmap = [
            {
                "阶段": "紧急修复（1-2天）",
                "任务": [
                    "坐标系尺度归一化",
                    "中心对齐",
                    "基础质量验证"
                ],
                "目标": "12点性能提升到6.0mm",
                "优先级": "极高"
            },
            {
                "阶段": "质量提升（3-5天）",
                "任务": [
                    "表面投影精度改进",
                    "关键点表面投影",
                    "质量指标建立"
                ],
                "目标": ">90% <1mm投影精度",
                "优先级": "高"
            },
            {
                "阶段": "数据增强（1周）",
                "任务": [
                    "解剖学约束旋转",
                    "医学噪声注入",
                    "增强数据验证"
                ],
                "目标": "扩展到200+样本",
                "优先级": "高"
            },
            {
                "阶段": "多尺度构建（1-2周）",
                "任务": [
                    "12/15/19/24点数据集构建",
                    "渐进式验证",
                    "性能基准建立"
                ],
                "目标": "支持渐进式扩展",
                "优先级": "中"
            }
        ]
        
        print(f"📋 实施计划:")
        for phase in roadmap:
            print(f"\n🎯 {phase['阶段']} ({phase['优先级']}优先级)")
            print(f"   目标: {phase['目标']}")
            for task in phase['任务']:
                print(f"   • {task}")
        
        return roadmap

def main():
    """主函数"""
    
    print("🎯 数据集质量改进策略")
    print("系统性分析和改进数据集质量，支持渐进式扩展")
    print("=" * 80)
    
    # 1. 质量分析
    analyzer = DatasetQualityAnalyzer()
    
    if not analyzer.load_datasets():
        print("❌ 数据集加载失败")
        return
    
    # 执行质量分析
    analyzer.analyze_coordinate_consistency()
    analyzer.analyze_surface_projection_quality()
    analyzer.analyze_annotation_consistency()
    analyzer.analyze_data_complexity()
    
    # 2. 改进策略设计
    strategy = DatasetImprovementStrategy(analyzer.quality_metrics)
    
    coord_strategies = strategy.design_coordinate_unification()
    proj_strategies = strategy.design_surface_projection_improvement()
    aug_methods = strategy.design_data_augmentation_strategy()
    roadmap = strategy.create_implementation_roadmap()
    
    # 3. 保存分析结果
    results = {
        "quality_analysis": analyzer.quality_metrics,
        "improvement_strategies": {
            "coordinate_unification": coord_strategies,
            "surface_projection": proj_strategies,
            "data_augmentation": aug_methods
        },
        "implementation_roadmap": roadmap,
        "recommendations": {
            "immediate_actions": [
                "坐标系尺度归一化",
                "表面投影精度改进",
                "解剖学约束数据增强"
            ],
            "expected_improvements": {
                "12_point_performance": "从当前到6.0mm",
                "15_point_feasibility": "从低到高",
                "data_volume": "从96到200+样本"
            }
        }
    }
    
    with open('dataset_quality_improvement_strategy.json', 'w') as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n🎯 关键发现总结:")
    print(f"   📊 质量差异: 历史数据集明显优于当前数据集")
    print(f"   🔧 主要问题: 坐标系不一致、表面投影精度低")
    print(f"   🚀 改进潜力: 预期性能提升30-50%")
    print(f"   ⏰ 实施时间: 2-4周完成全面改进")
    
    print(f"\n💡 立即行动建议:")
    print(f"   1. 🔥 紧急修复坐标系问题（1-2天）")
    print(f"   2. 🔧 改进表面投影精度（3-5天）")
    print(f"   3. 📈 实施数据增强策略（1周）")
    print(f"   4. 🎯 验证12点→15点扩展（随时）")
    
    print(f"\n💾 详细策略已保存: dataset_quality_improvement_strategy.json")

if __name__ == "__main__":
    main()
