#!/usr/bin/env python3
"""
测试19点扩展
Test 19-Point Expansion
基于15点成功，继续验证15点→19点的渐进式扩展
"""

import sys
import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
from sklearn.model_selection import train_test_split

# 添加原始代码路径
sys.path.insert(0, os.path.abspath("archive/old_scripts"))

def set_seed(seed=42):
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

# 导入原始模块
from ensemble_double_softmax_exact import ExactEnsembleDoubleSoftMaxPointNet

class ProgressiveDataset(Dataset):
    """渐进式数据集"""
    
    def __init__(self, point_clouds, keypoints):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def train_model_with_target(model, train_loader, val_loader, epochs=80, device='cuda', target_error=None):
    """训练模型直到达到目标误差"""
    
    print(f"🚀 训练{model.num_keypoints}点模型...")
    if target_error:
        print(f"   目标误差: {target_error}mm")
    
    model = model.to(device)
    
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=15, min_lr=1e-6
    )
    
    criterion = nn.MSELoss()
    
    best_val_error = float('inf')
    patience_counter = 0
    patience = 30  # 增加patience以获得更好结果
    
    start_time = time.time()
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            loss.backward()
            
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        # 验证
        model.eval()
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        scheduler.step(val_error)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 早停检查
        if val_error < best_val_error:
            best_val_error = val_error
            patience_counter = 0
            torch.save(model.state_dict(), f'best_{model.num_keypoints}point_model.pth')
        else:
            patience_counter += 1
        
        # 打印进度
        if epoch % 10 == 0 or epoch < 5:
            print(f"Epoch {epoch+1:3d}: "
                  f"Train: {train_error:.3f}, Val: {val_error:.3f}, "
                  f"LR: {current_lr:.2e}")
        
        # 目标达成检查
        if target_error and val_error <= target_error:
            print(f"🎉 达到目标误差 {target_error}mm！在第{epoch+1}轮")
            break
        
        if patience_counter >= patience:
            print(f"⏹️ 早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    training_time = time.time() - start_time
    
    # 加载最佳模型
    model.load_state_dict(torch.load(f'best_{model.num_keypoints}point_model.pth'))
    
    print(f"✅ {model.num_keypoints}点训练完成!")
    print(f"   最佳验证误差: {best_val_error:.3f}mm")
    print(f"   训练时间: {training_time/60:.1f}分钟")
    
    return model, best_val_error

def test_progressive_expansion():
    """测试渐进式扩展到19点"""
    
    print("🎯 测试渐进式扩展到19点")
    print("验证12点→15点→19点的完整扩展路径")
    print("=" * 80)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载修复后的数据集
    print("📊 加载修复后数据集...")
    
    try:
        fixed_data = np.load('emergency_fixed_final_dataset.npz', allow_pickle=True)
        point_clouds = fixed_data['point_clouds']
        keypoints_57 = fixed_data['keypoints_57']
        
        print(f"✅ 数据加载成功: {len(point_clouds)}样本")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 定义关键点子集
    keypoint_subsets = {
        12: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17],  # 历史最佳12点
        15: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17, 0, 1, 12],  # 增加3个F1点
        19: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17, 0, 1, 12, 13, 14, 15, 18],  # 完整F3区域
    }
    
    # 目标误差设置（基于之前的成功经验）
    target_errors = {
        12: 7.5,   # 已验证可达到
        15: 8.0,   # 已验证可达到
        19: 9.5    # 基于预测9.139mm，设置稍宽松目标
    }
    
    results = {}
    
    # 测试12点、15点、19点
    for num_points in [12, 15, 19]:
        print(f"\n🎯 测试{num_points}点模型")
        print("=" * 50)
        
        # 准备数据
        subset_indices = keypoint_subsets[num_points]
        subset_keypoints = keypoints_57[:, subset_indices, :]
        
        print(f"📋 关键点选择: {subset_indices}")
        
        # 数据划分
        indices = np.arange(len(point_clouds))
        train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
        train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
        
        train_dataset = ProgressiveDataset(point_clouds[train_indices], subset_keypoints[train_indices])
        val_dataset = ProgressiveDataset(point_clouds[val_indices], subset_keypoints[val_indices])
        
        # 数据加载器
        batch_size = 4
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
        
        print(f"📋 数据: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
        
        # 创建模型
        set_seed(123)  # 确保可重复性
        model = ExactEnsembleDoubleSoftMaxPointNet(num_keypoints=num_points, dropout_rate=0.3, num_ensembles=3)
        
        target_error = target_errors[num_points]
        
        # 训练模型
        model, best_error = train_model_with_target(
            model, train_loader, val_loader, epochs=100, device=device, target_error=target_error
        )
        
        # 记录结果
        results[num_points] = {
            'best_error': best_error,
            'target_error': target_error,
            'success': best_error <= target_error,
            'keypoint_indices': subset_indices
        }
        
        print(f"📊 {num_points}点结果:")
        print(f"   最佳误差: {best_error:.3f}mm")
        print(f"   目标误差: {target_error:.3f}mm")
        print(f"   {'✅ 成功' if best_error <= target_error else '❌ 未达标'}")
        
        # 如果19点失败，提前停止
        if num_points == 19 and not results[num_points]['success']:
            print(f"⚠️ 19点未达标，停止进一步测试")
            break
    
    # 分析完整的渐进式扩展效果
    print(f"\n📊 完整渐进式扩展分析")
    print("=" * 80)
    
    if all(k in results for k in [12, 15, 19]):
        error_12 = results[12]['best_error']
        error_15 = results[15]['best_error']
        error_19 = results[19]['best_error']
        
        expansion_12_15 = (error_15 - error_12) / error_12 * 100
        expansion_15_19 = (error_19 - error_15) / error_15 * 100
        expansion_12_19 = (error_19 - error_12) / error_12 * 100
        
        print(f"📋 扩展效果分析:")
        print(f"   12点基线: {error_12:.3f}mm")
        print(f"   15点扩展: {error_15:.3f}mm (+{expansion_12_15:.1f}%)")
        print(f"   19点扩展: {error_19:.3f}mm (+{expansion_15_19:.1f}%)")
        print(f"   总体扩展: {expansion_12_19:.1f}% (12→19点)")
        
        # 评估扩展成功性
        all_success = all(results[k]['success'] for k in [12, 15, 19])
        
        if all_success:
            print(f"\n🎉 完整渐进式扩展成功！")
            print(f"   ✅ 所有阶段都达到目标")
            print(f"   🚀 可以尝试24点扩展")
            
            # 预测24点性能
            predicted_24 = error_19 * 1.2  # 基于趋势预测
            print(f"\n🔮 24点扩展预测:")
            print(f"   预测误差: {predicted_24:.3f}mm")
            print(f"   可行性: {'✅ 高' if predicted_24 < 12 else '⚠️ 中' if predicted_24 < 15 else '❌ 低'}")
            
        else:
            failed_stages = [k for k in [12, 15, 19] if not results[k]['success']]
            print(f"\n⚠️ 部分阶段未达标: {failed_stages}")
            print(f"   需要优化策略或调整目标")
    
    # 保存结果
    import json
    with open('progressive_expansion_19point_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 测试结果已保存: progressive_expansion_19point_results.json")
    
    return results

def main():
    """主函数"""
    
    print("🚀 19点渐进式扩展测试")
    print("验证完整的12→15→19点扩展路径")
    print("=" * 80)
    
    try:
        results = test_progressive_expansion()
        
        # 最终总结
        print(f"\n🎯 最终总结:")
        
        if results:
            successful_points = [k for k, v in results.items() if v['success']]
            max_successful = max(successful_points) if successful_points else 0
            
            print(f"   成功扩展到: {max_successful}点")
            print(f"   成功阶段: {successful_points}")
            
            if max_successful >= 19:
                print(f"   🎉 渐进式扩展策略完全验证成功！")
                print(f"   💡 证明了数据质量修复的关键作用")
                print(f"   🚀 为进一步扩展奠定了坚实基础")
            elif max_successful >= 15:
                print(f"   ✅ 部分成功，15点扩展已验证")
                print(f"   🔧 19点需要进一步优化")
            else:
                print(f"   ⚠️ 需要重新评估策略")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
