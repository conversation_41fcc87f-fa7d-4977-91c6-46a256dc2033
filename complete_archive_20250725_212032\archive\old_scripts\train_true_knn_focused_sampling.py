#!/usr/bin/env python3
"""
True KNN-Focused Sampling for F3 Keypoint Detection

实现真正的KNN聚焦策略：
1. 为每个关键点找到K个最近邻
2. 在局部区域内密集采样
3. 确保关键点周围信息完整
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
from sklearn.neighbors import NearestNeighbors
import time
import json
import gc
import random

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class TrueKNNFocusedSampler:
    """真正的KNN聚焦采样器"""
    
    def __init__(self, target_points=4096, k_neighbors=200):
        self.target_points = target_points
        self.k_neighbors = k_neighbors  # 每个关键点的近邻数
        
    def knn_focused_sample(self, points, keypoints, focus_ratio=0.8):
        """真正的KNN聚焦采样"""
        
        total_points = len(points)
        if total_points <= self.target_points:
            return np.arange(total_points)
        
        print(f"🎯 KNN聚焦采样: {len(keypoints)}个关键点, 每个K={self.k_neighbors}近邻")
        
        # 分配采样数量
        knn_samples = int(focus_ratio * self.target_points)  # 80% KNN区域
        global_samples = self.target_points - knn_samples    # 20% 全局采样
        
        # 为每个关键点找到K近邻
        knn_indices_set = set()
        
        # 使用sklearn的高效KNN实现
        nbrs = NearestNeighbors(n_neighbors=self.k_neighbors, algorithm='auto').fit(points)
        
        for kp in keypoints:
            # 找到当前关键点的K近邻
            distances, indices = nbrs.kneighbors([kp])
            knn_indices_set.update(indices[0])
        
        knn_indices_list = list(knn_indices_set)
        print(f"   KNN区域总点数: {len(knn_indices_list)} (去重后)")
        
        # 在KNN区域内采样
        if len(knn_indices_list) >= knn_samples:
            # KNN区域点数足够，随机采样
            selected_knn_indices = np.random.choice(
                knn_indices_list, 
                size=knn_samples, 
                replace=False
            )
        else:
            # KNN区域点数不够，全部选择
            selected_knn_indices = np.array(knn_indices_list)
            print(f"   ⚠️ KNN区域点数不足，实际选择{len(selected_knn_indices)}个点")
        
        # 全局采样（排除已选择的KNN点）
        remaining_mask = np.ones(total_points, dtype=bool)
        remaining_mask[selected_knn_indices] = False
        remaining_indices = np.where(remaining_mask)[0]
        
        if len(remaining_indices) > 0 and global_samples > 0:
            actual_global_samples = min(global_samples, len(remaining_indices))
            selected_global_indices = np.random.choice(
                remaining_indices,
                size=actual_global_samples,
                replace=False
            )
        else:
            selected_global_indices = np.array([], dtype=int)
        
        # 合并所有采样结果
        all_indices = np.concatenate([selected_knn_indices, selected_global_indices])
        
        print(f"   最终采样: KNN区域{len(selected_knn_indices)}, 全局{len(selected_global_indices)}, 总计{len(all_indices)}")
        
        # 确保数量正确
        if len(all_indices) < self.target_points:
            # 如果还不够，从剩余点中补充
            remaining_mask = np.ones(total_points, dtype=bool)
            remaining_mask[all_indices] = False
            remaining_indices = np.where(remaining_mask)[0]
            
            if len(remaining_indices) > 0:
                additional_needed = self.target_points - len(all_indices)
                additional_indices = np.random.choice(
                    remaining_indices,
                    size=min(additional_needed, len(remaining_indices)),
                    replace=False
                )
                all_indices = np.concatenate([all_indices, additional_indices])
        
        return all_indices[:self.target_points]
    
    def adaptive_knn_focused_sample(self, points, keypoints, focus_ratio=0.8):
        """自适应KNN聚焦采样 - 根据关键点密度调整K值"""
        
        total_points = len(points)
        if total_points <= self.target_points:
            return np.arange(total_points)
        
        # 计算关键点间的平均距离，自适应调整K值
        kp_distances = []
        for i in range(len(keypoints)):
            for j in range(i+1, len(keypoints)):
                kp_distances.append(np.linalg.norm(keypoints[i] - keypoints[j]))
        
        if kp_distances:
            avg_kp_distance = np.mean(kp_distances)
            # 根据关键点密度调整K值
            density_factor = min(avg_kp_distance / 10.0, 2.0)  # 限制在合理范围
            adaptive_k = int(self.k_neighbors * density_factor)
            adaptive_k = max(50, min(adaptive_k, 500))  # 限制K值范围
        else:
            adaptive_k = self.k_neighbors
        
        print(f"🎯 自适应KNN聚焦: K={adaptive_k} (原始K={self.k_neighbors})")
        
        # 使用自适应K值进行采样
        original_k = self.k_neighbors
        self.k_neighbors = adaptive_k
        result = self.knn_focused_sample(points, keypoints, focus_ratio)
        self.k_neighbors = original_k  # 恢复原始K值
        
        return result

class TrueKNNFocusedF3Dataset(Dataset):
    """使用真正KNN聚焦采样的F3数据集"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42,
                 focus_ratio: float = 0.8, k_neighbors: int = 200, adaptive_k: bool = True):
        
        self.num_points = num_points
        self.augment = augment
        self.split = split
        self.focus_ratio = focus_ratio
        self.adaptive_k = adaptive_k
        
        # 初始化KNN聚焦采样器
        self.sampler = TrueKNNFocusedSampler(target_points=num_points, k_neighbors=k_neighbors)
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        print(f"📊 原始数据: {len(sample_ids)} 样本")
        print(f"📊 点云密度: {len(point_clouds[0])} 点 (50K高质量)")
        print(f"🎯 KNN聚焦配置: K={k_neighbors}, 聚焦比例{focus_ratio*100:.0f}%")
        print(f"🔧 自适应K值: {'启用' if adaptive_k else '禁用'}")
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
        
        print(f"   {split}: {len(self.sample_ids)} 样本")
        
        # 预计算所有样本的KNN采样索引
        print(f"🔄 预计算{split}集KNN采样索引...")
        self.sampling_indices = []
        
        for i in range(len(self.sample_ids)):
            if len(self.point_clouds[i]) > self.num_points:
                if self.adaptive_k:
                    indices = self.sampler.adaptive_knn_focused_sample(
                        self.point_clouds[i], 
                        self.keypoints[i], 
                        focus_ratio=self.focus_ratio
                    )
                else:
                    indices = self.sampler.knn_focused_sample(
                        self.point_clouds[i], 
                        self.keypoints[i], 
                        focus_ratio=self.focus_ratio
                    )
                self.sampling_indices.append(indices)
            else:
                self.sampling_indices.append(np.arange(len(self.point_clouds[i])))
        
        print(f"✅ {split}集KNN采样索引预计算完成")
    
    def __len__(self):
        return len(self.sample_ids)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        # 使用预计算的KNN采样索引
        selected_indices = self.sampling_indices[idx]
        point_cloud = point_cloud[selected_indices]
        
        # 保守的数据增强
        if self.augment and self.split == 'train':
            # 轻微旋转
            if np.random.random() < 0.7:
                angle = np.random.uniform(-0.08, 0.08)  # ±4.6度
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                point_cloud = point_cloud @ rotation.T
                keypoints = keypoints @ rotation.T
            
            # 小幅平移
            if np.random.random() < 0.6:
                translation = np.random.uniform(-0.4, 0.4, 3)
                point_cloud += translation
                keypoints += translation
            
            # 轻微缩放
            if np.random.random() < 0.5:
                scale = np.random.uniform(0.99, 1.01, 3)
                point_cloud *= scale
                keypoints *= scale
            
            # 轻微噪声
            if np.random.random() < 0.6:
                noise_level = np.random.choice([0.02, 0.03, 0.04])
                noise = np.random.normal(0, noise_level, point_cloud.shape)
                point_cloud += noise
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

# 使用之前成功的保守PointNet架构
class ConservativePointNet(nn.Module):
    """保守优化PointNet"""
    
    def __init__(self, num_keypoints: int = 19):
        super(ConservativePointNet, self).__init__()
        
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(0.4)  # 增加dropout
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        x1 = torch.relu(self.bn1(self.conv1(x)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        
        x3_res = x3 + self.residual1(x1)
        
        x4 = torch.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = torch.relu(self.bn5(self.conv5(x4_res)))
        
        global_feat = torch.max(x5, 2)[0]
        
        x = torch.relu(self.bn_fc1(self.fc1(global_feat)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc2(self.fc2(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc3(self.fc3(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc4(self.fc4(x)))
        x = self.dropout(x)
        x = self.fc5(x)
        
        return x.view(batch_size, 19, 3)

class ImprovedLoss(nn.Module):
    """改进损失函数"""
    
    def __init__(self, alpha=0.8, beta=0.2):
        super(ImprovedLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, pred, target):
        mse_loss = F.mse_loss(pred, target)
        smooth_l1_loss = F.smooth_l1_loss(pred, target)
        total_loss = self.alpha * mse_loss + self.beta * smooth_l1_loss
        return total_loss

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def train_true_knn_focused(k_neighbors=200, focus_ratio=0.8, adaptive_k=True):
    """训练真正的KNN聚焦采样模型"""

    print("🚀 **真正KNN聚焦采样训练 - F3关键点检测**")
    print("🎯 **核心改进**: 真正的K近邻聚焦 + 稳定学习率调度")
    print(f"📊 **配置**: K={k_neighbors}, 聚焦比例{focus_ratio*100:.0f}%, 自适应K={'启用' if adaptive_k else '禁用'}")
    print("📈 **目标**: 超越7.631mm保守基线，实现稳定收敛")
    print("=" * 80)

    set_seed(42)

    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")

    if torch.cuda.is_available():
        torch.cuda.empty_cache()

    # 数据集
    dataset_path = "high_quality_f3_dataset.npz"
    test_samples = ['600114', '600115', '600116', '600117', '600118',
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']

    train_dataset = TrueKNNFocusedF3Dataset(dataset_path, 'train', num_points=4096,
                                          test_samples=test_samples, augment=True,
                                          seed=42, focus_ratio=focus_ratio,
                                          k_neighbors=k_neighbors, adaptive_k=adaptive_k)
    val_dataset = TrueKNNFocusedF3Dataset(dataset_path, 'val', num_points=4096,
                                        test_samples=test_samples, augment=False,
                                        seed=42, focus_ratio=focus_ratio,
                                        k_neighbors=k_neighbors, adaptive_k=adaptive_k)
    test_dataset = TrueKNNFocusedF3Dataset(dataset_path, 'test', num_points=4096,
                                         test_samples=test_samples, augment=False,
                                         seed=42, focus_ratio=focus_ratio,
                                         k_neighbors=k_neighbors, adaptive_k=adaptive_k)

    batch_size = 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)

    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")

    # 模型
    model = ConservativePointNet(num_keypoints=19).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 真正KNN聚焦PointNet参数: {total_params:,}")

    # 改进的训练配置
    criterion = ImprovedLoss(alpha=0.8, beta=0.2)
    optimizer = optim.AdamW(model.parameters(), lr=0.0005, weight_decay=5e-4)  # 降低学习率，增加权重衰减

    # 使用更稳定的学习率调度器
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=8, min_lr=1e-6
    )

    num_epochs = 100
    best_val_error = float('inf')
    patience = 15  # 减少早停耐心
    patience_counter = 0
    history = []
    min_delta = 0.01  # 最小改进阈值

    print(f"🎯 改进训练配置:")
    print(f"   学习率: 0.0005 (降低)")
    print(f"   权重衰减: 5e-4 (增加)")
    print(f"   调度器: ReduceLROnPlateau (更稳定)")
    print(f"   早停耐心: {patience} (减少)")
    print(f"   最小改进: {min_delta}mm")

    print(f"\n🚀 开始真正KNN聚焦训练")
    start_time = time.time()

    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 50)

        # 训练
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}

        for batch in train_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)

            optimizer.zero_grad()

            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)

                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()

                train_loss += loss.item()

                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]

            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue

        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)

        # 验证
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}

        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)

                try:
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()

                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]

                except RuntimeError as e:
                    continue

        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)

        # 学习率调度
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']

        # 打印结果
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"5mm={train_metrics['within_5mm_percent']:.1f}%, 7mm={train_metrics['within_7mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"5mm={val_metrics['within_5mm_percent']:.1f}%, 7mm={val_metrics['within_7mm_percent']:.1f}%")
        print(f"学习率: {current_lr:.2e}")

        # 保存历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'learning_rate': current_lr
        })

        # 检查改进（使用最小改进阈值）
        current_error = val_metrics['mean_distance']
        improvement = best_val_error - current_error

        if improvement > min_delta:
            best_val_error = current_error
            patience_counter = 0

            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics,
                'config': {
                    'k_neighbors': k_neighbors,
                    'focus_ratio': focus_ratio,
                    'adaptive_k': adaptive_k
                }
            }, 'best_true_knn_focused_f3.pth')

            print(f"🎉 新最佳! 验证误差: {best_val_error:.3f}mm (改进{improvement:.3f}mm)")

            if best_val_error <= 5.0:
                print(f"🏆 **突破5mm目标!**")
            elif best_val_error < 7.631:
                print(f"✅ **优于保守基线!** 超越7.631mm")
            elif best_val_error < 8.543:
                print(f"✅ **优于随机基线!** 超越8.543mm")
        else:
            patience_counter += 1
            print(f"⏳ 无显著改善 ({patience_counter}/{patience}) 改进{improvement:.3f}mm < {min_delta}mm")

        if patience_counter >= patience:
            print("🛑 早停触发")
            break

        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

    total_time = time.time() - start_time

    return model, test_loader, best_val_error, total_time, history

def main():
    """主函数 - 测试真正的KNN聚焦采样"""

    print("🚀 **真正KNN聚焦采样实验**")
    print("🎯 **核心改进**: 实现真正的K近邻聚焦策略")
    print("=" * 80)

    # 测试配置
    configs = [
        {'k_neighbors': 200, 'focus_ratio': 0.8, 'adaptive_k': True},
        # 可以添加更多配置进行对比
    ]

    results = []

    for config in configs:
        print(f"\n{'='*80}")
        print(f"🧪 **测试配置**: K={config['k_neighbors']}, 聚焦{config['focus_ratio']*100:.0f}%, 自适应K={config['adaptive_k']}**")
        print(f"{'='*80}")

        try:
            # 训练模型
            model, test_loader, best_val_error, training_time, history = train_true_knn_focused(**config)

            print(f"\n🎯 **KNN聚焦训练完成!**")
            print(f"   最佳验证误差: {best_val_error:.3f}mm")
            print(f"   训练时间: {training_time/60:.1f}分钟")

            # 测试评估
            device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')

            checkpoint = torch.load('best_true_knn_focused_f3.pth', map_location=device)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval()

            test_metrics = {'mean_distance': 0, 'std_distance': 0,
                           'within_1mm_percent': 0, 'within_3mm_percent': 0,
                           'within_5mm_percent': 0, 'within_7mm_percent': 0}

            num_batches = 0
            all_distances = []

            with torch.no_grad():
                for batch in test_loader:
                    point_cloud = batch['point_cloud'].to(device)
                    keypoints = batch['keypoints'].to(device)
                    sample_ids = batch['sample_id']

                    try:
                        pred_keypoints = model(point_cloud)

                        # 计算每个样本的误差
                        for i in range(len(sample_ids)):
                            pred_single = pred_keypoints[i:i+1]
                            target_single = keypoints[i:i+1]

                            metrics = calculate_metrics(pred_single, target_single)
                            all_distances.append(metrics['mean_distance'])

                        # 批次指标
                        metrics = calculate_metrics(pred_keypoints, keypoints)
                        for key in test_metrics:
                            test_metrics[key] += metrics[key]
                        num_batches += 1

                    except RuntimeError as e:
                        continue

            # 平均测试指标
            for key in test_metrics:
                test_metrics[key] /= num_batches

            all_distances = np.array(all_distances)

            print(f"\n📊 **真正KNN聚焦测试结果**")
            print(f"   测试误差: {np.mean(all_distances):.3f}±{np.std(all_distances):.3f}mm")
            print(f"   中位数误差: {np.median(all_distances):.3f}mm")
            print(f"   最小误差: {np.min(all_distances):.3f}mm")
            print(f"   最大误差: {np.max(all_distances):.3f}mm")

            print(f"\n📈 **精度分布**")
            print(f"   ≤1mm: {test_metrics['within_1mm_percent']:.1f}%")
            print(f"   ≤3mm: {test_metrics['within_3mm_percent']:.1f}%")
            print(f"   ≤5mm: {test_metrics['within_5mm_percent']:.1f}%")
            print(f"   ≤7mm: {test_metrics['within_7mm_percent']:.1f}%")

            # 与基线对比
            random_baseline = 8.543
            conservative_baseline = 7.631
            test_error = np.mean(all_distances)

            improvement_vs_random = (random_baseline - test_error) / random_baseline * 100
            improvement_vs_conservative = (conservative_baseline - test_error) / conservative_baseline * 100

            print(f"\n📈 **性能对比**")
            print(f"   随机采样基线: {random_baseline:.3f}mm")
            print(f"   保守基线: {conservative_baseline:.3f}mm")
            print(f"   真正KNN聚焦: {test_error:.3f}mm")
            print(f"   vs随机基线: {improvement_vs_random:+.1f}%")
            print(f"   vs保守基线: {improvement_vs_conservative:+.1f}%")

            # 评估效果
            if test_error <= 5.0:
                print(f"\n🏆 **突破5mm目标!** 真正KNN聚焦大成功!")
            elif test_error < conservative_baseline:
                print(f"\n✅ **优于保守基线!** 真正KNN聚焦有效")
            elif test_error < random_baseline:
                print(f"\n✅ **优于随机基线!** 真正KNN聚焦有一定效果")
            else:
                print(f"\n⚠️ **效果不佳** 需要进一步调整")

            # 保存结果
            result = {
                'config': config,
                'best_val_error': float(best_val_error),
                'test_error': float(test_error),
                'test_std': float(np.std(all_distances)),
                'test_metrics': {k: float(v) for k, v in test_metrics.items()},
                'training_time_minutes': float(training_time / 60),
                'improvement_vs_random_percent': float(improvement_vs_random),
                'improvement_vs_conservative_percent': float(improvement_vs_conservative),
                'all_distances': all_distances.tolist(),
                'history': history
            }
            results.append(result)

        except Exception as e:
            print(f"❌ 配置{config}训练失败: {e}")
            import traceback
            traceback.print_exc()
            continue

    # 保存所有结果
    final_results = {
        'method': 'True KNN Focused Sampling',
        'results': results,
        'baselines': {
            'random_sampling': 8.543,
            'conservative_baseline': 7.631,
            'previous_knn_attempt': 7.978
        }
    }

    with open('true_knn_focused_results.json', 'w', encoding='utf-8') as f:
        json.dump(final_results, f, indent=2, ensure_ascii=False)

    print(f"\n💾 **所有结果已保存**: true_knn_focused_results.json")

    # 总结
    print(f"\n🎉 **真正KNN聚焦实验完成!**")
    if results:
        best_result = min(results, key=lambda x: x['test_error'])
        print(f"🏆 最佳配置: K={best_result['config']['k_neighbors']}, 聚焦{best_result['config']['focus_ratio']*100:.0f}%")
        print(f"🎯 最佳测试误差: {best_result['test_error']:.3f}mm")
        print(f"📈 vs保守基线改进: {best_result['improvement_vs_conservative_percent']:.1f}%")

        if best_result['test_error'] < 7.631:
            print(f"✅ **真正KNN聚焦策略成功!**")
        else:
            print(f"💡 **需要进一步优化KNN策略**")

if __name__ == "__main__":
    set_seed(42)
    main()
