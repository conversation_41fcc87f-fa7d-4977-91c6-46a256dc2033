#!/usr/bin/env python3
"""
阶段1: 数据质量提升实施
Phase 1: Data Quality Improvement Implementation
目标: 从9.12mm降低到7.5-8.0mm (15-20%改进)
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json
from pathlib import Path
from datetime import datetime
import seaborn as sns
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler

class DataQualityAnalyzer:
    """数据质量分析器"""
    
    def __init__(self, data_path='data/raw/high_quality_f3_dataset.npz'):
        self.data_path = data_path
        self.load_data()
        
    def load_data(self):
        """加载数据"""
        print(f"📦 加载数据进行质量分析: {self.data_path}")
        
        data = np.load(self.data_path, allow_pickle=True)
        self.sample_ids = data['sample_ids']
        self.point_clouds = data['point_clouds']
        self.keypoints = data['keypoints']
        
        print(f"✅ 数据加载完成: {len(self.sample_ids)} 样本")
        
    def analyze_annotation_consistency(self):
        """分析标注一致性"""
        print("\n🔍 分析标注一致性...")
        
        # 计算关键点间距离
        inter_keypoint_distances = []
        
        for i, kps in enumerate(self.keypoints):
            distances = []
            for j in range(len(kps)):
                for k in range(j+1, len(kps)):
                    dist = np.linalg.norm(kps[j] - kps[k])
                    distances.append(dist)
            inter_keypoint_distances.append(distances)
        
        inter_keypoint_distances = np.array(inter_keypoint_distances)
        
        # 统计分析
        mean_distances = np.mean(inter_keypoint_distances, axis=0)
        std_distances = np.std(inter_keypoint_distances, axis=0)
        cv_distances = std_distances / mean_distances  # 变异系数
        
        # 识别异常样本
        outlier_threshold = 2.0  # 2倍标准差
        outlier_samples = []
        
        for i, distances in enumerate(inter_keypoint_distances):
            z_scores = np.abs((distances - mean_distances) / std_distances)
            if np.any(z_scores > outlier_threshold):
                outlier_samples.append({
                    'sample_id': self.sample_ids[i],
                    'sample_index': i,
                    'max_z_score': np.max(z_scores),
                    'outlier_distances': np.sum(z_scores > outlier_threshold)
                })
        
        consistency_report = {
            'total_samples': len(self.sample_ids),
            'outlier_samples': len(outlier_samples),
            'outlier_percentage': len(outlier_samples) / len(self.sample_ids) * 100,
            'mean_cv': np.mean(cv_distances),
            'high_variance_distances': np.sum(cv_distances > 0.3),
            'outlier_details': outlier_samples
        }
        
        print(f"📊 标注一致性报告:")
        print(f"   异常样本: {len(outlier_samples)}/{len(self.sample_ids)} ({consistency_report['outlier_percentage']:.1f}%)")
        print(f"   平均变异系数: {consistency_report['mean_cv']:.3f}")
        print(f"   高变异距离: {consistency_report['high_variance_distances']}")
        
        return consistency_report
    
    def analyze_coordinate_alignment(self):
        """分析坐标系对齐情况"""
        print("\n🧭 分析坐标系对齐...")
        
        # 计算每个样本的质心
        centroids = []
        for pc in self.point_clouds:
            centroid = np.mean(pc, axis=0)
            centroids.append(centroid)
        
        centroids = np.array(centroids)
        
        # 计算关键点质心
        kp_centroids = []
        for kp in self.keypoints:
            kp_centroid = np.mean(kp, axis=0)
            kp_centroids.append(kp_centroid)
        
        kp_centroids = np.array(kp_centroids)
        
        # 分析对齐情况
        centroid_distances = np.linalg.norm(centroids - kp_centroids, axis=1)
        
        # 计算主方向
        principal_directions = []
        for pc in self.point_clouds:
            # 简化的主成分分析
            centered_pc = pc - np.mean(pc, axis=0)
            cov_matrix = np.cov(centered_pc.T)
            eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)
            principal_direction = eigenvectors[:, np.argmax(eigenvalues)]
            principal_directions.append(principal_direction)
        
        principal_directions = np.array(principal_directions)
        
        # 分析方向一致性
        reference_direction = principal_directions[0]
        direction_similarities = []
        for direction in principal_directions:
            similarity = np.abs(np.dot(direction, reference_direction))
            direction_similarities.append(similarity)
        
        alignment_report = {
            'centroid_alignment': {
                'mean_distance': np.mean(centroid_distances),
                'std_distance': np.std(centroid_distances),
                'max_distance': np.max(centroid_distances),
                'misaligned_samples': np.sum(centroid_distances > np.mean(centroid_distances) + 2*np.std(centroid_distances))
            },
            'orientation_alignment': {
                'mean_similarity': np.mean(direction_similarities),
                'std_similarity': np.std(direction_similarities),
                'min_similarity': np.min(direction_similarities),
                'misoriented_samples': np.sum(np.array(direction_similarities) < 0.8)
            }
        }
        
        print(f"📊 坐标对齐报告:")
        print(f"   质心偏移: {alignment_report['centroid_alignment']['mean_distance']:.2f}±{alignment_report['centroid_alignment']['std_distance']:.2f}")
        print(f"   方向一致性: {alignment_report['orientation_alignment']['mean_similarity']:.3f}")
        print(f"   错位样本: {alignment_report['centroid_alignment']['misaligned_samples']}")
        
        return alignment_report
    
    def detect_anomalous_samples(self):
        """检测异常样本"""
        print("\n🚨 检测异常样本...")
        
        # 特征提取
        features = []
        for i, (pc, kp) in enumerate(zip(self.point_clouds, self.keypoints)):
            # 点云特征
            pc_features = [
                len(pc),  # 点数
                np.mean(np.linalg.norm(pc, axis=1)),  # 平均距离原点距离
                np.std(np.linalg.norm(pc, axis=1)),   # 距离标准差
                np.max(pc, axis=0) - np.min(pc, axis=0)  # 包围盒大小
            ]
            pc_features = np.concatenate([pc_features[:3], pc_features[3]])
            
            # 关键点特征
            kp_features = [
                np.mean(np.linalg.norm(kp, axis=1)),  # 关键点平均距离
                np.std(np.linalg.norm(kp, axis=1)),   # 关键点距离标准差
                np.mean(np.linalg.norm(kp[1:] - kp[:-1], axis=1))  # 相邻关键点距离
            ]
            
            combined_features = np.concatenate([pc_features, kp_features])
            features.append(combined_features)
        
        features = np.array(features)
        
        # 标准化特征
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        # 使用DBSCAN检测异常
        dbscan = DBSCAN(eps=1.5, min_samples=3)
        clusters = dbscan.fit_predict(features_scaled)
        
        # 识别异常样本 (标记为-1的样本)
        anomalous_indices = np.where(clusters == -1)[0]
        
        anomaly_report = {
            'total_samples': len(self.sample_ids),
            'anomalous_samples': len(anomalous_indices),
            'anomaly_percentage': len(anomalous_indices) / len(self.sample_ids) * 100,
            'anomalous_sample_ids': [self.sample_ids[i] for i in anomalous_indices],
            'cluster_distribution': {int(k): int(v) for k, v in zip(*np.unique(clusters, return_counts=True))}
        }
        
        print(f"📊 异常检测报告:")
        print(f"   异常样本: {len(anomalous_indices)}/{len(self.sample_ids)} ({anomaly_report['anomaly_percentage']:.1f}%)")
        print(f"   聚类分布: {anomaly_report['cluster_distribution']}")
        
        return anomaly_report, anomalous_indices
    
    def visualize_quality_issues(self, save_plots=True):
        """可视化质量问题"""
        print("\n📊 生成质量分析可视化...")
        
        # 创建可视化目录
        viz_dir = Path("results/data_quality_analysis")
        viz_dir.mkdir(parents=True, exist_ok=True)
        
        # 1. 关键点距离分布
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 计算所有样本的关键点间距离
        all_distances = []
        for kps in self.keypoints:
            for i in range(len(kps)):
                for j in range(i+1, len(kps)):
                    dist = np.linalg.norm(kps[i] - kps[j])
                    all_distances.append(dist)
        
        axes[0, 0].hist(all_distances, bins=50, alpha=0.7, edgecolor='black')
        axes[0, 0].set_title('关键点间距离分布')
        axes[0, 0].set_xlabel('距离 (mm)')
        axes[0, 0].set_ylabel('频次')
        
        # 2. 点云大小分布
        pc_sizes = [len(pc) for pc in self.point_clouds]
        axes[0, 1].hist(pc_sizes, bins=20, alpha=0.7, edgecolor='black')
        axes[0, 1].set_title('点云大小分布')
        axes[0, 1].set_xlabel('点数')
        axes[0, 1].set_ylabel('频次')
        
        # 3. 关键点分布范围
        kp_ranges = []
        for kp in self.keypoints:
            range_x = np.max(kp[:, 0]) - np.min(kp[:, 0])
            range_y = np.max(kp[:, 1]) - np.min(kp[:, 1])
            range_z = np.max(kp[:, 2]) - np.min(kp[:, 2])
            kp_ranges.append([range_x, range_y, range_z])
        
        kp_ranges = np.array(kp_ranges)
        
        axes[1, 0].boxplot([kp_ranges[:, 0], kp_ranges[:, 1], kp_ranges[:, 2]], 
                          labels=['X', 'Y', 'Z'])
        axes[1, 0].set_title('关键点分布范围')
        axes[1, 0].set_ylabel('范围 (mm)')
        
        # 4. 样本质心分布
        centroids = [np.mean(pc, axis=0) for pc in self.point_clouds]
        centroids = np.array(centroids)
        
        axes[1, 1].scatter(centroids[:, 0], centroids[:, 1], alpha=0.6)
        axes[1, 1].set_title('样本质心分布 (X-Y平面)')
        axes[1, 1].set_xlabel('X (mm)')
        axes[1, 1].set_ylabel('Y (mm)')
        
        plt.tight_layout()
        
        if save_plots:
            plt.savefig(viz_dir / "data_quality_overview.png", dpi=150, bbox_inches='tight')
            print(f"💾 保存质量分析图: {viz_dir}/data_quality_overview.png")
        
        plt.show()
    
    def generate_quality_report(self):
        """生成完整的数据质量报告"""
        print("\n📋 生成数据质量报告...")
        
        # 执行所有分析
        consistency_report = self.analyze_annotation_consistency()
        alignment_report = self.analyze_coordinate_alignment()
        anomaly_report, anomalous_indices = self.detect_anomalous_samples()
        
        # 综合质量评分
        quality_score = 100
        
        # 扣分项
        quality_score -= consistency_report['outlier_percentage'] * 2  # 异常样本扣分
        quality_score -= max(0, (consistency_report['mean_cv'] - 0.2) * 100)  # 高变异扣分
        quality_score -= anomaly_report['anomaly_percentage'] * 1.5  # 异常样本扣分
        quality_score -= alignment_report['centroid_alignment']['misaligned_samples'] * 3  # 错位扣分
        
        quality_score = max(0, quality_score)
        
        # 生成改进建议
        improvement_suggestions = []
        
        if consistency_report['outlier_percentage'] > 10:
            improvement_suggestions.append("🔧 需要重新检查和修正异常样本的标注")
        
        if alignment_report['centroid_alignment']['misaligned_samples'] > 5:
            improvement_suggestions.append("🧭 需要进行坐标系对齐和标准化")
        
        if anomaly_report['anomaly_percentage'] > 15:
            improvement_suggestions.append("🚨 建议移除或修正异常样本")
        
        if consistency_report['mean_cv'] > 0.3:
            improvement_suggestions.append("📏 需要提高标注一致性和精度")
        
        # 完整报告
        complete_report = {
            "analysis_timestamp": datetime.now().isoformat(),
            "dataset_info": {
                "total_samples": len(self.sample_ids),
                "total_keypoints": len(self.keypoints[0]) if len(self.keypoints) > 0 else 0,
                "data_source": str(self.data_path)
            },
            "quality_score": round(quality_score, 1),
            "consistency_analysis": consistency_report,
            "alignment_analysis": alignment_report,
            "anomaly_analysis": anomaly_report,
            "improvement_suggestions": improvement_suggestions,
            "recommended_actions": {
                "immediate": [
                    "移除或修正异常样本",
                    "标准化坐标系统"
                ],
                "short_term": [
                    "重新标注质量差的样本",
                    "实施数据验证流程"
                ],
                "long_term": [
                    "增加高质量样本",
                    "建立标注质量控制体系"
                ]
            },
            "expected_improvement": {
                "if_all_actions_taken": "15-25%性能提升",
                "target_error_reduction": "从9.12mm降低到7.5-8.0mm"
            }
        }
        
        # 保存报告
        report_dir = Path("results/data_quality_reports")
        report_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = report_dir / f"data_quality_report_{timestamp}.json"
        
        # 转换numpy类型为Python原生类型
        def convert_numpy_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            return obj

        complete_report_serializable = convert_numpy_types(complete_report)

        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(complete_report_serializable, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 数据质量评估完成!")
        print(f"📈 质量评分: {quality_score:.1f}/100")
        print(f"💡 改进建议数: {len(improvement_suggestions)}")
        print(f"🎯 预期改进: {complete_report['expected_improvement']['target_error_reduction']}")
        print(f"💾 完整报告已保存: {report_file}")
        
        return complete_report

def main():
    """主函数 - 执行数据质量分析"""
    
    print("🔍 阶段1: 数据质量提升分析")
    print("=" * 60)
    print("目标: 识别和修正数据质量问题，预期改进15-20%")
    
    # 创建分析器
    analyzer = DataQualityAnalyzer()
    
    # 生成可视化
    analyzer.visualize_quality_issues()
    
    # 生成完整报告
    report = analyzer.generate_quality_report()
    
    print(f"\n🎯 下一步行动计划:")
    for i, action in enumerate(report['recommended_actions']['immediate'], 1):
        print(f"  {i}. {action}")
    
    return report

if __name__ == "__main__":
    report = main()
