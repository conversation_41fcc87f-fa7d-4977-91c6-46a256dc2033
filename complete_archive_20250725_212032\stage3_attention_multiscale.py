#!/usr/bin/env python3
"""
第三阶段优化：注意力机制 + 多尺度特征融合
Stage 3 Optimization: Attention Mechanism + Multi-scale Feature Fusion
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import os
from tqdm import tqdm

class SpatialAttention(nn.Module):
    """空间注意力模块"""
    
    def __init__(self, in_channels):
        super(SpatialAttention, self).__init__()
        self.conv1 = nn.Conv1d(in_channels, in_channels // 4, 1)
        self.conv2 = nn.Conv1d(in_channels // 4, in_channels // 8, 1)
        self.conv3 = nn.Conv1d(in_channels // 8, 1, 1)
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        # x: [batch_size, channels, num_points]
        attention = self.conv1(x)
        attention = F.relu(attention)
        attention = self.conv2(attention)
        attention = F.relu(attention)
        attention = self.conv3(attention)
        attention = self.sigmoid(attention)
        
        return x * attention

class MultiScaleFeatureExtractor(nn.Module):
    """多尺度特征提取器"""
    
    def __init__(self, in_channels, out_channels):
        super(MultiScaleFeatureExtractor, self).__init__()
        
        # 不同尺度的卷积
        self.conv1x1 = nn.Conv1d(in_channels, out_channels // 4, 1)
        self.conv3x1 = nn.Sequential(
            nn.Conv1d(in_channels, out_channels // 4, 1),
            nn.Conv1d(out_channels // 4, out_channels // 4, 3, padding=1)
        )
        self.conv5x1 = nn.Sequential(
            nn.Conv1d(in_channels, out_channels // 4, 1),
            nn.Conv1d(out_channels // 4, out_channels // 4, 5, padding=2)
        )
        self.conv7x1 = nn.Sequential(
            nn.Conv1d(in_channels, out_channels // 4, 1),
            nn.Conv1d(out_channels // 4, out_channels // 4, 7, padding=3)
        )
        
        self.bn = nn.BatchNorm1d(out_channels)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        # 多尺度特征提取
        feat1 = self.conv1x1(x)
        feat3 = self.conv3x1(x)
        feat5 = self.conv5x1(x)
        feat7 = self.conv7x1(x)
        
        # 特征融合
        combined = torch.cat([feat1, feat3, feat5, feat7], dim=1)
        combined = self.bn(combined)
        combined = self.relu(combined)
        
        return combined

class EnhancedHeatmapRegressionNet(nn.Module):
    """增强的Heatmap回归网络 (注意力机制 + 多尺度特征)"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super(EnhancedHeatmapRegressionNet, self).__init__()
        
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 多尺度特征提取
        self.multiscale1 = MultiScaleFeatureExtractor(3, 64)
        self.multiscale2 = MultiScaleFeatureExtractor(64, 128)
        self.multiscale3 = MultiScaleFeatureExtractor(128, 256)
        self.multiscale4 = MultiScaleFeatureExtractor(256, 512)
        
        # 空间注意力模块
        self.attention1 = SpatialAttention(64)
        self.attention2 = SpatialAttention(128)
        self.attention3 = SpatialAttention(256)
        self.attention4 = SpatialAttention(512)
        
        # 全局特征
        self.global_conv = nn.Conv1d(512, 1024, 1)
        
        # 特征金字塔融合
        self.pyramid_conv1 = nn.Conv1d(1024 + 256, 512, 1)
        self.pyramid_conv2 = nn.Conv1d(512 + 128, 256, 1)
        self.pyramid_conv3 = nn.Conv1d(256 + 64, 128, 1)
        
        # Heatmap生成
        self.heatmap_conv1 = nn.Conv1d(128, 64, 1)
        self.heatmap_conv2 = nn.Conv1d(64, 32, 1)
        self.heatmap_conv3 = nn.Conv1d(32, num_keypoints, 1)
        
        # 激活函数和正则化
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.25)  # 稍微减少dropout
        
        # 批归一化
        self.bn_global = nn.BatchNorm1d(1024)
        self.bn_pyramid1 = nn.BatchNorm1d(512)
        self.bn_pyramid2 = nn.BatchNorm1d(256)
        self.bn_pyramid3 = nn.BatchNorm1d(128)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        # 多尺度特征提取 + 注意力机制
        x1 = self.multiscale1(x)      # [B, 64, N]
        x1 = self.attention1(x1)
        
        x2 = self.multiscale2(x1)     # [B, 128, N]
        x2 = self.attention2(x2)
        
        x3 = self.multiscale3(x2)     # [B, 256, N]
        x3 = self.attention3(x3)
        
        x4 = self.multiscale4(x3)     # [B, 512, N]
        x4 = self.attention4(x4)
        
        # 全局特征
        global_feat = self.relu(self.bn_global(self.global_conv(x4)))
        global_feat = torch.max(global_feat, 2, keepdim=True)[0]
        
        # 扩展全局特征
        global_feat_expanded = global_feat.repeat(1, 1, self.num_points)
        
        # 特征金字塔融合 (自顶向下)
        # 第一层融合: 全局特征 + x3
        pyramid1 = torch.cat([x3, global_feat_expanded], 1)
        pyramid1 = self.relu(self.bn_pyramid1(self.pyramid_conv1(pyramid1)))
        pyramid1 = self.dropout(pyramid1)
        
        # 第二层融合: pyramid1 + x2
        pyramid2 = torch.cat([x2, pyramid1], 1)
        pyramid2 = self.relu(self.bn_pyramid2(self.pyramid_conv2(pyramid2)))
        pyramid2 = self.dropout(pyramid2)
        
        # 第三层融合: pyramid2 + x1
        pyramid3 = torch.cat([x1, pyramid2], 1)
        pyramid3 = self.relu(self.bn_pyramid3(self.pyramid_conv3(pyramid3)))
        
        # 生成热图
        heatmap = self.relu(self.heatmap_conv1(pyramid3))
        heatmap = self.relu(self.heatmap_conv2(heatmap))
        heatmap = self.heatmap_conv3(heatmap)
        
        # 转置并应用softmax
        heatmap = heatmap.transpose(2, 1)
        
        # 对每个关键点的热图进行softmax
        heatmap_list = []
        for i in range(self.num_keypoints):
            hm_i = torch.softmax(heatmap[:, :, i], dim=1)
            heatmap_list.append(hm_i.unsqueeze(2))
        
        final_heatmap = torch.cat(heatmap_list, dim=2)
        
        return final_heatmap

def load_clean_data():
    """加载清洁数据"""
    
    print("📊 加载清洁数据...")
    
    # 加载女性原始数据
    female_original_path = "archive/old_experiments/f3_reduced_12kp_female.npz"
    if os.path.exists(female_original_path):
        female_data = np.load(female_original_path, allow_pickle=True)
        female_pc = female_data['point_clouds']
        female_kp = female_data['keypoints']
        print(f"✅ 女性原始数据: {len(female_pc)}个样本")
    else:
        print(f"❌ 女性原始数据不存在")
        return None
    
    # 加载男性原始数据
    male_original_path = "archive/old_experiments/f3_reduced_12kp_male.npz"
    if os.path.exists(male_original_path):
        male_data = np.load(male_original_path, allow_pickle=True)
        male_pc = male_data['point_clouds']
        male_kp = male_data['keypoints']
        print(f"✅ 男性原始数据: {len(male_pc)}个样本")
    else:
        print(f"❌ 男性原始数据不存在")
        return None
    
    return female_pc, female_kp, male_pc, male_kp

def analyze_data_quality(pc_data, kp_data, gender_name):
    """分析数据质量"""
    
    quality_scores = []
    
    for i in range(len(pc_data)):
        pc = pc_data[i]
        kp = kp_data[i]
        
        # 计算质量指标
        quality_metrics = {}
        
        # 1. 点云密度一致性
        pc_std = np.std(np.linalg.norm(pc, axis=1))
        quality_metrics['pc_density'] = 1.0 / (1.0 + pc_std)
        
        # 2. 关键点到点云的距离
        distances = []
        for kp_point in kp:
            dist_to_pc = np.min(np.linalg.norm(pc - kp_point, axis=1))
            distances.append(dist_to_pc)
        avg_distance = np.mean(distances)
        quality_metrics['kp_to_pc_distance'] = 1.0 / (1.0 + avg_distance)
        
        # 3. 关键点分布合理性
        kp_center = np.mean(kp, axis=0)
        kp_spread = np.std(np.linalg.norm(kp - kp_center, axis=1))
        quality_metrics['kp_spread'] = 1.0 / (1.0 + abs(kp_spread - 50))
        
        # 4. 点云完整性
        pc_center = np.mean(pc, axis=0)
        pc_coverage = np.std(np.linalg.norm(pc - pc_center, axis=1))
        quality_metrics['pc_coverage'] = min(1.0, pc_coverage / 100)
        
        # 综合质量分数
        overall_quality = np.mean(list(quality_metrics.values()))
        quality_scores.append({
            'index': i,
            'overall_quality': overall_quality,
            'metrics': quality_metrics,
            'avg_kp_distance': avg_distance
        })
    
    # 排序
    quality_scores.sort(key=lambda x: x['overall_quality'])
    
    return quality_scores

def remove_outliers(pc_data, kp_data, quality_scores, removal_ratio=0.08):
    """移除异常样本"""
    
    n_samples = len(pc_data)
    n_remove = int(n_samples * removal_ratio)
    
    # 获取要移除的样本索引
    outlier_indices = set([score['index'] for score in quality_scores[:n_remove]])
    
    # 创建清洁数据
    clean_pc = []
    clean_kp = []
    
    for i in range(n_samples):
        if i not in outlier_indices:
            clean_pc.append(pc_data[i])
            clean_kp.append(kp_data[i])
    
    clean_pc = np.array(clean_pc)
    clean_kp = np.array(clean_kp)
    
    return clean_pc, clean_kp, outlier_indices

def split_clean_data(pc, kp, gender_name):
    """分割清洁数据"""
    
    n_samples = len(pc)
    n_train = int(n_samples * 0.7)
    n_val = int(n_samples * 0.15)
    n_test = n_samples - n_train - n_val
    
    # 随机打乱
    indices = np.random.permutation(n_samples)
    
    train_indices = indices[:n_train]
    val_indices = indices[n_train:n_train + n_val]
    test_indices = indices[n_train + n_val:]
    
    # 分割数据
    train_pc = pc[train_indices]
    train_kp = kp[train_indices]
    
    val_pc = pc[val_indices]
    val_kp = kp[val_indices]
    
    test_pc = pc[test_indices]
    test_kp = kp[test_indices]
    
    return (train_pc, train_kp), (val_pc, val_kp), (test_pc, test_kp)

def augment_clean_training_data(train_pc, train_kp, target_size=None):
    """对清洁训练数据进行增强"""
    
    if target_size is None:
        target_size = len(train_pc) * 12  # 稍微增加增强倍数
    
    augmented_pc = []
    augmented_kp = []
    
    # 保留原始数据
    for i in range(len(train_pc)):
        augmented_pc.append(train_pc[i])
        augmented_kp.append(train_kp[i])
    
    # 生成增强数据
    n_augment_needed = target_size - len(train_pc)
    
    for i in tqdm(range(n_augment_needed), desc="生成增强数据"):
        # 随机选择一个原始样本
        idx = np.random.randint(0, len(train_pc))
        original_pc = train_pc[idx].copy()
        original_kp = train_kp[idx].copy()
        
        # 随机选择增强方法
        aug_type = np.random.choice(['rotation', 'noise', 'scale', 'jitter'])
        
        if aug_type == 'rotation':
            # 3D旋转增强
            angles = np.random.uniform(-8, 8, 3)  # 稍微减小角度
            aug_pc, aug_kp = apply_3d_rotation(original_pc, original_kp, angles)
        elif aug_type == 'noise':
            # 噪声增强
            noise_pc = np.random.normal(0, 0.8, original_pc.shape)
            noise_kp = np.random.normal(0, 1.2, original_kp.shape)
            aug_pc = original_pc + noise_pc
            aug_kp = original_kp + noise_kp
        elif aug_type == 'scale':
            # 尺度增强
            scale_factor = np.random.uniform(0.96, 1.04)
            aug_pc = original_pc * scale_factor
            aug_kp = original_kp * scale_factor
        else:  # jitter
            # 抖动增强
            jitter_pc = np.random.normal(0, 0.5, original_pc.shape)
            jitter_kp = np.random.normal(0, 0.8, original_kp.shape)
            aug_pc = original_pc + jitter_pc
            aug_kp = original_kp + jitter_kp
        
        augmented_pc.append(aug_pc)
        augmented_kp.append(aug_kp)
    
    augmented_pc = np.array(augmented_pc)
    augmented_kp = np.array(augmented_kp)
    
    return augmented_pc, augmented_kp

def apply_3d_rotation(point_cloud, keypoints, angles_deg):
    """应用3D旋转"""
    
    angles = np.radians(angles_deg)
    
    # 旋转矩阵
    cos_x, sin_x = np.cos(angles[0]), np.sin(angles[0])
    cos_y, sin_y = np.cos(angles[1]), np.sin(angles[1])
    cos_z, sin_z = np.cos(angles[2]), np.sin(angles[2])
    
    # X轴旋转
    Rx = np.array([[1, 0, 0],
                   [0, cos_x, -sin_x],
                   [0, sin_x, cos_x]])
    
    # Y轴旋转
    Ry = np.array([[cos_y, 0, sin_y],
                   [0, 1, 0],
                   [-sin_y, 0, cos_y]])
    
    # Z轴旋转
    Rz = np.array([[cos_z, -sin_z, 0],
                   [sin_z, cos_z, 0],
                   [0, 0, 1]])
    
    # 组合旋转
    R = Rz @ Ry @ Rx
    
    # 应用旋转
    rotated_pc = point_cloud @ R.T
    rotated_kp = keypoints @ R.T
    
    return rotated_pc, rotated_kp

def generate_heatmap_from_keypoints(keypoints, point_cloud, sigma=4.8):
    """从关键点生成热图 (稍微调整sigma)"""
    heatmaps = []
    
    for kp in keypoints:
        distances = np.linalg.norm(point_cloud - kp, axis=1)
        heatmap = np.exp(-distances**2 / (2 * sigma**2))
        
        if np.sum(heatmap) > 0:
            heatmap = heatmap / np.sum(heatmap)
        
        heatmaps.append(heatmap)
    
    return np.array(heatmaps)

def heatmap_loss_function(pred_heatmap, target_heatmap):
    """热图损失函数"""
    
    # 检查并调整target_heatmap的维度
    if len(target_heatmap.shape) == 3 and target_heatmap.shape[1] == 12:
        target_heatmap = target_heatmap.transpose(1, 2)
    
    # KL散度损失
    kl_loss = nn.KLDivLoss(reduction='batchmean')
    
    total_loss = 0
    batch_size, num_points, num_keypoints = pred_heatmap.shape
    
    for i in range(num_keypoints):
        log_pred = torch.log(pred_heatmap[:, :, i] + 1e-8)
        loss_i = kl_loss(log_pred, target_heatmap[:, :, i])
        total_loss += loss_i
    
    return total_loss / num_keypoints

def extract_keypoints_from_heatmap(heatmap, point_cloud):
    """从热图中提取关键点坐标"""
    
    batch_size, num_points, num_keypoints = heatmap.shape
    keypoints = torch.zeros(batch_size, num_keypoints, 3)
    
    for b in range(batch_size):
        for k in range(num_keypoints):
            weights = heatmap[b, :, k]
            weighted_coords = point_cloud[b] * weights.unsqueeze(1)
            keypoint = torch.sum(weighted_coords, dim=0) / torch.sum(weights)
            keypoints[b, k] = keypoint
    
    return keypoints

def train_enhanced_model(train_pc, train_kp, val_pc, val_kp, gender_name, model_id):
    """训练增强模型"""
    
    print(f"\n🚀 训练{gender_name}增强模型 #{model_id}")
    print("=" * 60)
    
    # 生成训练数据的热图
    print("🔥 生成训练数据热图...")
    train_hm = []
    for i in tqdm(range(len(train_pc)), desc="训练热图"):
        hm = generate_heatmap_from_keypoints(train_kp[i], train_pc[i])
        train_hm.append(hm)
    train_hm = np.array(train_hm)
    
    # 生成验证数据的热图
    print("🔥 生成验证数据热图...")
    val_hm = []
    for i in tqdm(range(len(val_pc)), desc="验证热图"):
        hm = generate_heatmap_from_keypoints(val_kp[i], val_pc[i])
        val_hm.append(hm)
    val_hm = np.array(val_hm)
    
    # 设备设置
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    
    # 模型初始化
    torch.manual_seed(42 + model_id * 150)  # 更大的种子差异
    model = EnhancedHeatmapRegressionNet(num_points=50000, num_keypoints=12)
    model = model.to(device)
    
    print(f"🏗️ {gender_name}增强模型 #{model_id}:")
    print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"   训练样本: {len(train_pc)}")
    print(f"   验证样本: {len(val_pc)}")
    print(f"   随机种子: {42 + model_id * 150}")
    print(f"   架构特点: 注意力机制 + 多尺度特征 + 特征金字塔")
    
    # 优化器 - 稍微降低学习率
    base_lr = 0.00025 + model_id * 0.000025  # 0.00025, 0.000275, 0.0003
    optimizer = optim.Adam(model.parameters(), lr=base_lr, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=60, eta_min=5e-7)
    
    # 训练参数
    num_epochs = 60  # 增加训练轮数
    batch_size = 3   # 减小批次大小以适应更大的模型
    best_val_error = float('inf')
    patience = 20    # 增加耐心
    patience_counter = 0
    
    print(f"🎯 训练参数:")
    print(f"   训练轮数: {num_epochs}")
    print(f"   批次大小: {batch_size}")
    print(f"   学习率: {base_lr}")
    print(f"   优化器: Adam + CosineAnnealingLR")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        n_train_batches = len(train_pc) // batch_size
        
        for i in range(n_train_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(train_pc))
            
            batch_pc = torch.FloatTensor(train_pc[start_idx:end_idx]).to(device)
            batch_hm = torch.FloatTensor(train_hm[start_idx:end_idx]).to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            pred_hm = model(batch_pc)
            loss = heatmap_loss_function(pred_hm, batch_hm)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        avg_train_loss = train_loss / n_train_batches
        
        # 验证阶段
        model.eval()
        val_errors = []
        
        with torch.no_grad():
            n_val_batches = len(val_pc) // batch_size + (1 if len(val_pc) % batch_size > 0 else 0)
            
            for i in range(n_val_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(val_pc))
                
                batch_pc = torch.FloatTensor(val_pc[start_idx:end_idx]).to(device)
                batch_kp = torch.FloatTensor(val_kp[start_idx:end_idx]).to(device)
                
                pred_hm = model(batch_pc)
                pred_kp = extract_keypoints_from_heatmap(pred_hm.cpu(), batch_pc.cpu())
                
                for j in range(len(batch_kp)):
                    error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j].cpu(), dim=1))
                    val_errors.append(error.item())
        
        avg_val_error = np.mean(val_errors)
        
        # 学习率调度
        scheduler.step()
        
        if epoch % 10 == 0:
            print(f"Epoch {epoch+1}/{num_epochs}: Loss={avg_train_loss:.4f}, Val={avg_val_error:.2f}mm, LR={scheduler.get_last_lr()[0]:.6f}")
        
        # 早停和模型保存
        if avg_val_error < best_val_error:
            best_val_error = avg_val_error
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'val_error': avg_val_error,
            }, f'enhanced_{gender_name}_model_{model_id}.pth')
        else:
            patience_counter += 1
            if patience_counter >= patience:
                break
    
    print(f"✅ 增强模型 #{model_id} 训练完成，最佳验证误差: {best_val_error:.2f}mm")
    
    return model, best_val_error

def test_enhanced_ensemble_models(models, test_pc, test_kp, gender_name):
    """测试增强集成模型"""
    
    print(f"\n🧪 测试{gender_name}增强集成模型")
    print("=" * 60)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    
    # 将所有模型设置为评估模式
    for model in models:
        model.to(device)
        model.eval()
    
    test_errors = []
    individual_errors = [[] for _ in range(len(models))]
    
    batch_size = 3  # 与训练时保持一致
    with torch.no_grad():
        n_test_batches = len(test_pc) // batch_size + (1 if len(test_pc) % batch_size > 0 else 0)
        
        for i in range(n_test_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(test_pc))
            
            batch_pc = torch.FloatTensor(test_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(test_kp[start_idx:end_idx]).to(device)
            
            # 获取所有模型的预测
            ensemble_predictions = []
            for model_idx, model in enumerate(models):
                pred_hm = model(batch_pc)
                pred_kp = extract_keypoints_from_heatmap(pred_hm.cpu(), batch_pc.cpu())
                ensemble_predictions.append(pred_kp)
                
                # 计算单个模型误差
                for j in range(len(batch_kp)):
                    error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j].cpu(), dim=1))
                    individual_errors[model_idx].append(error.item())
            
            # 集成预测 (简单平均)
            ensemble_pred = torch.mean(torch.stack(ensemble_predictions), dim=0)
            
            # 计算集成误差
            for j in range(len(batch_kp)):
                error = torch.mean(torch.norm(ensemble_pred[j] - batch_kp[j].cpu(), dim=1))
                test_errors.append(error.item())
    
    ensemble_error = np.mean(test_errors)
    individual_avg_errors = [np.mean(errors) for errors in individual_errors]
    
    print(f"📊 {gender_name}增强集成模型测试结果:")
    print(f"   测试样本数: {len(test_pc)} (清洁数据)")
    print(f"   集成模型误差: {ensemble_error:.2f}mm")
    print(f"   单个模型误差:")
    for i, error in enumerate(individual_avg_errors):
        print(f"     增强模型 #{i+1}: {error:.2f}mm")
    print(f"   集成改进: {np.mean(individual_avg_errors) - ensemble_error:.2f}mm")
    print(f"   医疗级状态: {'✅ 达标' if ensemble_error < 5.0 else '❌ 未达标'}")
    print(f"   4.5mm目标: {'🎉 达成' if ensemble_error < 4.5 else '📈 接近'}")
    
    return ensemble_error, individual_avg_errors

def main():
    """主函数"""
    
    print("🚀 第三阶段优化：注意力机制 + 多尺度特征融合")
    print("🎯 目标: 从4.85mm突破到4.5mm")
    print("🔧 技术: 空间注意力 + 多尺度特征 + 特征金字塔")
    print("=" * 80)
    
    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 加载数据
    data_result = load_clean_data()
    if data_result is None:
        return
    
    female_pc, female_kp, male_pc, male_kp = data_result
    
    results = {}
    
    # 处理女性数据
    print(f"\n" + "="*80)
    print("👩 处理女性数据")
    print("="*80)
    
    # 分析数据质量并清洁
    female_quality = analyze_data_quality(female_pc, female_kp, "女性")
    female_clean_pc, female_clean_kp, female_outliers = remove_outliers(
        female_pc, female_kp, female_quality, removal_ratio=0.08)
    
    # 分割清洁数据
    (female_train_pc, female_train_kp), (female_val_pc, female_val_kp), (female_test_pc, female_test_kp) = \
        split_clean_data(female_clean_pc, female_clean_kp, "女性")
    
    # 增强女性训练数据
    female_aug_train_pc, female_aug_train_kp = augment_clean_training_data(
        female_train_pc, female_train_kp, target_size=180)
    
    # 训练女性增强模型 (3个模型)
    female_models = []
    female_val_errors = []
    
    for model_id in range(3):
        model, val_error = train_enhanced_model(
            female_aug_train_pc, female_aug_train_kp,
            female_val_pc, female_val_kp, "女性", model_id)
        female_models.append(model)
        female_val_errors.append(val_error)
    
    # 测试女性增强集成模型
    female_ensemble_error, female_individual_errors = test_enhanced_ensemble_models(
        female_models, female_test_pc, female_test_kp, "女性")
    
    results['female'] = {
        'ensemble_error': female_ensemble_error,
        'individual_errors': female_individual_errors,
        'val_errors': female_val_errors,
        'test_samples': len(female_test_pc)
    }
    
    # 处理男性数据
    print(f"\n" + "="*80)
    print("👨 处理男性数据")
    print("="*80)
    
    # 分析数据质量并清洁
    male_quality = analyze_data_quality(male_pc, male_kp, "男性")
    male_clean_pc, male_clean_kp, male_outliers = remove_outliers(
        male_pc, male_kp, male_quality, removal_ratio=0.08)
    
    # 分割清洁数据
    (male_train_pc, male_train_kp), (male_val_pc, male_val_kp), (male_test_pc, male_test_kp) = \
        split_clean_data(male_clean_pc, male_clean_kp, "男性")
    
    # 增强男性训练数据
    male_aug_train_pc, male_aug_train_kp = augment_clean_training_data(
        male_train_pc, male_train_kp, target_size=540)
    
    # 训练男性增强模型 (3个模型)
    male_models = []
    male_val_errors = []
    
    for model_id in range(3):
        model, val_error = train_enhanced_model(
            male_aug_train_pc, male_aug_train_kp,
            male_val_pc, male_val_kp, "男性", model_id)
        male_models.append(model)
        male_val_errors.append(val_error)
    
    # 测试男性增强集成模型
    male_ensemble_error, male_individual_errors = test_enhanced_ensemble_models(
        male_models, male_test_pc, male_test_kp, "男性")
    
    results['male'] = {
        'ensemble_error': male_ensemble_error,
        'individual_errors': male_individual_errors,
        'val_errors': male_val_errors,
        'test_samples': len(male_test_pc)
    }
    
    # 总结结果
    print(f"\n" + "="*80)
    print("🎉 第三阶段增强优化结果总结")
    print("="*80)
    
    print(f"📊 女性增强集成模型:")
    print(f"   集成模型误差: {results['female']['ensemble_error']:.2f}mm")
    print(f"   单个模型误差: {', '.join([f'{e:.2f}mm' for e in results['female']['individual_errors']])}")
    print(f"   集成改进: {np.mean(results['female']['individual_errors']) - results['female']['ensemble_error']:.2f}mm")
    print(f"   测试样本: {results['female']['test_samples']}个")
    
    print(f"\n📊 男性增强集成模型:")
    print(f"   集成模型误差: {results['male']['ensemble_error']:.2f}mm")
    print(f"   单个模型误差: {', '.join([f'{e:.2f}mm' for e in results['male']['individual_errors']])}")
    print(f"   集成改进: {np.mean(results['male']['individual_errors']) - results['male']['ensemble_error']:.2f}mm")
    print(f"   测试样本: {results['male']['test_samples']}个")
    
    print(f"\n📈 与第二阶段对比:")
    print(f"   女性模型: {results['female']['ensemble_error']:.2f}mm vs 第二阶段4.72mm")
    print(f"   男性模型: {results['male']['ensemble_error']:.2f}mm vs 第二阶段4.99mm")
    
    # 计算改进幅度
    female_improvement = 4.72 - results['female']['ensemble_error']
    male_improvement = 4.99 - results['male']['ensemble_error']
    
    print(f"\n💡 第三阶段改进效果:")
    print(f"   女性模型改进: {female_improvement:.2f}mm")
    print(f"   男性模型改进: {male_improvement:.2f}mm")
    print(f"   平均改进: {(female_improvement + male_improvement) / 2:.2f}mm")
    
    # 检查是否达到4.5mm目标
    avg_error = (results['female']['ensemble_error'] + results['male']['ensemble_error']) / 2
    print(f"\n🎯 4.5mm目标检查:")
    print(f"   当前平均误差: {avg_error:.2f}mm")
    if avg_error < 4.5:
        print("🎉 已达到4.5mm目标!")
    else:
        print(f"📈 距离4.5mm目标还差: {avg_error - 4.5:.2f}mm")
    
    # 检查单个模型是否达到4.5mm
    female_achieved = results['female']['ensemble_error'] < 4.5
    male_achieved = results['male']['ensemble_error'] < 4.5
    
    print(f"\n🏆 分性别目标达成情况:")
    print(f"   女性模型: {'🎉 已达成4.5mm目标' if female_achieved else '📈 未达成4.5mm目标'}")
    print(f"   男性模型: {'🎉 已达成4.5mm目标' if male_achieved else '📈 未达成4.5mm目标'}")
    
    return results

if __name__ == "__main__":
    main()
