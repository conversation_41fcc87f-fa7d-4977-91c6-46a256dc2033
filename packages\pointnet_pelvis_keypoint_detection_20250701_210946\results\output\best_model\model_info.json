{"model_name": "BestSimplePointNet", "architecture": "极简PointNet", "total_parameters": 97451, "trainable_parameters": 97451, "model_size_mb": 0.3717460632324219, "performance": {"best_5mm_accuracy": 94.7, "best_mean_error": 2.62, "best_10mm_accuracy": 99.9, "training_epochs": 15, "convergence_epoch": 1}, "architecture_details": {"input_dim": 3, "conv_layers": [{"in_channels": 3, "out_channels": 64, "kernel_size": 1}, {"in_channels": 64, "out_channels": 128, "kernel_size": 1}, {"in_channels": 128, "out_channels": 256, "kernel_size": 1}], "pooling": "global_max_pooling", "fc_layers": [{"in_features": 256, "out_features": 128}, {"in_features": 128, "out_features": 171}], "dropout_rate": 0.2, "activation": "ReLU", "batch_norm": true}, "training_config": {"optimizer": "<PERSON>", "learning_rate": 0.001, "loss_function": "MSELoss", "batch_size": 2, "num_points": 512, "data_augmentation": true}, "success_factors": ["极简架构避免过拟合", "直接MSE损失最有效", "合适的模型容量", "高质量的数据集", "简单的训练策略"], "timestamp": "2025-07-01T20:25:21.933562"}