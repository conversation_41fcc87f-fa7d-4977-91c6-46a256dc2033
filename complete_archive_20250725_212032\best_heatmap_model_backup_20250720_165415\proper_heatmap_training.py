#!/usr/bin/env python3
"""
正确的Heatmap架构训练
Proper Heatmap Architecture Training
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import os
from tqdm import tqdm

class HeatmapRegressionNet(nn.Module):
    """真正的Heatmap回归网络"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super(HeatmapRegressionNet, self).__init__()
        
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 点云特征提取 (类似PointNet)
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        # 全局特征
        self.global_conv = nn.Conv1d(512, 1024, 1)
        
        # 特征融合
        self.fusion_conv1 = nn.Conv1d(1024 + 256, 512, 1)
        self.fusion_conv2 = nn.Conv1d(512, 256, 1)
        
        # Heatmap生成
        self.heatmap_conv1 = nn.Conv1d(256, 128, 1)
        self.heatmap_conv2 = nn.Conv1d(128, 64, 1)
        self.heatmap_conv3 = nn.Conv1d(64, num_keypoints, 1)
        
        # 激活函数
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.3)
        
        # Batch Normalization
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
    def forward(self, x):
        # x: (batch_size, num_points, 3)
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # (batch_size, 3, num_points)
        
        # 点云特征提取
        x1 = self.relu(self.bn1(self.conv1(x)))
        x2 = self.relu(self.bn2(self.conv2(x1)))
        x3 = self.relu(self.bn3(self.conv3(x2)))
        x4 = self.relu(self.bn4(self.conv4(x3)))
        
        # 全局特征
        global_feat = self.relu(self.global_conv(x4))
        global_feat = torch.max(global_feat, 2, keepdim=True)[0]
        
        # 扩展全局特征
        global_feat_expanded = global_feat.repeat(1, 1, self.num_points)
        
        # 融合局部和全局特征
        combined_feat = torch.cat([x3, global_feat_expanded], 1)
        
        # 特征融合
        fused = self.relu(self.fusion_conv1(combined_feat))
        fused = self.dropout(fused)
        fused = self.relu(self.fusion_conv2(fused))
        
        # 生成热图
        heatmap = self.relu(self.heatmap_conv1(fused))
        heatmap = self.relu(self.heatmap_conv2(heatmap))
        heatmap = self.heatmap_conv3(heatmap)
        
        # 转置并应用softmax
        heatmap = heatmap.transpose(2, 1)  # (batch_size, num_points, num_keypoints)
        
        # 对每个关键点的热图进行softmax
        heatmap_list = []
        for i in range(self.num_keypoints):
            hm_i = torch.softmax(heatmap[:, :, i], dim=1)
            heatmap_list.append(hm_i.unsqueeze(2))
        
        heatmap = torch.cat(heatmap_list, dim=2)
        
        return heatmap

def heatmap_loss_function(pred_heatmap, target_heatmap):
    """Heatmap损失函数"""

    # 检查维度
    if pred_heatmap.shape != target_heatmap.shape:
        # target_heatmap可能是(batch, keypoints, points)，需要转置
        if len(target_heatmap.shape) == 3 and target_heatmap.shape[1] == 12:
            target_heatmap = target_heatmap.transpose(1, 2)  # (batch, points, keypoints)

    # KL散度损失
    kl_loss = nn.KLDivLoss(reduction='batchmean')

    total_loss = 0
    batch_size, num_points, num_keypoints = pred_heatmap.shape

    for i in range(num_keypoints):
        # 对预测热图取对数
        log_pred = torch.log(pred_heatmap[:, :, i] + 1e-8)

        # 计算KL散度
        loss_i = kl_loss(log_pred, target_heatmap[:, :, i])
        total_loss += loss_i

    return total_loss / num_keypoints

def extract_keypoints_from_heatmap(heatmap, point_cloud):
    """从热图中提取关键点坐标"""
    
    batch_size, num_points, num_keypoints = heatmap.shape
    keypoints = torch.zeros(batch_size, num_keypoints, 3)
    
    for b in range(batch_size):
        for k in range(num_keypoints):
            # 加权平均方法 (更稳定)
            weights = heatmap[b, :, k]
            weighted_coords = point_cloud[b] * weights.unsqueeze(1)
            keypoint = torch.sum(weighted_coords, dim=0) / torch.sum(weights)
            keypoints[b, k] = keypoint
    
    return keypoints

def load_heatmap_data():
    """加载Heatmap数据"""
    
    print("🔥 加载Heatmap增强数据...")
    
    data_path = "f3_reduced_12kp_female_augmented.npz"
    if not os.path.exists(data_path):
        print(f"❌ 数据文件不存在: {data_path}")
        return None
    
    data = np.load(data_path, allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    heatmaps = data['heatmaps']
    
    print(f"✅ 数据加载成功:")
    print(f"   样本数: {len(point_clouds)}")
    print(f"   点云形状: {point_clouds.shape}")
    print(f"   关键点形状: {keypoints.shape}")
    print(f"   热图形状: {heatmaps.shape}")
    
    # 数据分割
    n_samples = len(point_clouds)
    n_train = int(n_samples * 0.7)
    n_val = int(n_samples * 0.15)
    
    indices = np.random.permutation(n_samples)
    train_indices = indices[:n_train]
    val_indices = indices[n_train:n_train + n_val]
    test_indices = indices[n_train + n_val:]
    
    train_data = (point_clouds[train_indices], keypoints[train_indices], heatmaps[train_indices])
    val_data = (point_clouds[val_indices], keypoints[val_indices], heatmaps[val_indices])
    test_data = (point_clouds[test_indices], keypoints[test_indices], heatmaps[test_indices])
    
    print(f"📋 数据分割:")
    print(f"   训练集: {len(train_indices)}个样本")
    print(f"   验证集: {len(val_indices)}个样本")
    print(f"   测试集: {len(test_indices)}个样本")
    
    return train_data, val_data, test_data

def train_heatmap_model():
    """训练Heatmap模型"""
    
    print("🔥 开始训练真正的Heatmap模型!")
    print("🎯 这次用正确的架构!")
    print("=" * 80)
    
    # 加载数据
    data_result = load_heatmap_data()
    if data_result is None:
        return
    
    train_data, val_data, test_data = data_result
    
    # 设备设置
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ 使用设备: {device}")
    
    # 模型初始化
    model = HeatmapRegressionNet(num_points=50000, num_keypoints=12)
    model = model.to(device)
    
    print(f"🏗️ 模型架构: HeatmapRegressionNet")
    print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 优化器
    optimizer = optim.Adam(model.parameters(), lr=0.0005, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=15, gamma=0.7)
    
    # 训练参数
    num_epochs = 40
    batch_size = 4  # 小批次避免内存问题
    best_val_error = float('inf')
    
    print(f"🎯 训练参数:")
    print(f"   训练轮数: {num_epochs}")
    print(f"   批次大小: {batch_size}")
    print(f"   学习率: 0.0005")
    
    # 准备数据
    train_pc, train_kp, train_hm = train_data
    val_pc, val_kp, val_hm = val_data
    test_pc, test_kp, test_hm = test_data
    
    print(f"\n🚀 开始训练...")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        n_train_batches = len(train_pc) // batch_size
        
        train_pbar = tqdm(range(n_train_batches), desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for i in train_pbar:
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(train_pc))
            
            batch_pc = torch.FloatTensor(train_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(train_kp[start_idx:end_idx]).to(device)
            batch_hm = torch.FloatTensor(train_hm[start_idx:end_idx]).to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            pred_hm = model(batch_pc)
            loss = heatmap_loss_function(pred_hm, batch_hm)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_pbar.set_postfix({'Loss': f'{loss.item():.4f}'})
        
        avg_train_loss = train_loss / n_train_batches
        
        # 验证阶段
        model.eval()
        val_errors = []
        val_loss = 0.0
        
        with torch.no_grad():
            n_val_batches = len(val_pc) // batch_size + (1 if len(val_pc) % batch_size > 0 else 0)
            
            val_pbar = tqdm(range(n_val_batches), desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for i in val_pbar:
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(val_pc))
                
                batch_pc = torch.FloatTensor(val_pc[start_idx:end_idx]).to(device)
                batch_kp = torch.FloatTensor(val_kp[start_idx:end_idx]).to(device)
                batch_hm = torch.FloatTensor(val_hm[start_idx:end_idx]).to(device)
                
                pred_hm = model(batch_pc)
                loss = heatmap_loss_function(pred_hm, batch_hm)
                val_loss += loss.item()
                
                # 提取关键点并计算误差
                pred_kp = extract_keypoints_from_heatmap(pred_hm.cpu(), batch_pc.cpu())
                error = torch.mean(torch.norm(pred_kp - batch_kp.cpu(), dim=2))
                val_errors.append(error.item())
                
                val_pbar.set_postfix({'Loss': f'{loss.item():.4f}', 'Error': f'{error.item():.2f}mm'})
        
        avg_val_loss = val_loss / n_val_batches
        avg_val_error = np.mean(val_errors)
        
        # 学习率调度
        scheduler.step()
        
        print(f"\nEpoch {epoch+1}/{num_epochs}:")
        print(f"  训练损失: {avg_train_loss:.4f}")
        print(f"  验证损失: {avg_val_loss:.4f}")
        print(f"  验证误差: {avg_val_error:.2f}mm")
        print(f"  学习率: {scheduler.get_last_lr()[0]:.6f}")
        
        # 保存最佳模型
        if avg_val_error < best_val_error:
            best_val_error = avg_val_error
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'val_error': avg_val_error,
            }, 'best_heatmap_augmented_model.pth')
            print(f"  ✅ 保存最佳模型 (验证误差: {avg_val_error:.2f}mm)")
    
    # 测试最佳模型
    print(f"\n🧪 测试最佳模型...")
    checkpoint = torch.load('best_heatmap_augmented_model.pth')
    model.load_state_dict(checkpoint['model_state_dict'])
    
    model.eval()
    test_errors = []
    
    with torch.no_grad():
        n_test_batches = len(test_pc) // batch_size + (1 if len(test_pc) % batch_size > 0 else 0)
        
        test_pbar = tqdm(range(n_test_batches), desc='Testing')
        for i in test_pbar:
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(test_pc))
            
            batch_pc = torch.FloatTensor(test_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(test_kp[start_idx:end_idx]).to(device)
            
            pred_hm = model(batch_pc)
            pred_kp = extract_keypoints_from_heatmap(pred_hm.cpu(), batch_pc.cpu())
            
            error = torch.mean(torch.norm(pred_kp - batch_kp.cpu(), dim=2))
            test_errors.append(error.item())
            
            test_pbar.set_postfix({'Error': f'{error.item():.2f}mm'})
    
    final_test_error = np.mean(test_errors)
    
    print(f"\n🎉 Heatmap模型训练完成!")
    print(f"=" * 80)
    print(f"📊 最终结果:")
    print(f"   最佳验证误差: {best_val_error:.2f}mm")
    print(f"   最终测试误差: {final_test_error:.2f}mm")
    print(f"   训练样本数: {len(train_pc)}")
    
    # 关键对比
    print(f"\n🏆 关键对比:")
    print(f"   原始Heatmap (25样本): 4.88mm")
    print(f"   增强Heatmap (250样本): {final_test_error:.2f}mm")
    
    if final_test_error < 4.88:
        improvement = ((4.88 - final_test_error) / 4.88) * 100
        print(f"   🎉 性能提升: {improvement:.1f}%")
        if final_test_error < 4.0:
            print(f"   🏆 突破4mm大关!")
        if final_test_error < 3.5:
            print(f"   🚀 达到3.5mm级别!")
    else:
        print(f"   📊 性能差异: +{final_test_error - 4.88:.2f}mm")
    
    print(f"\n💡 这次用了正确的架构!")
    print(f"   ✅ Heatmap回归网络")
    print(f"   ✅ 概率分布建模")
    print(f"   ✅ 与原始方法一致")

def main():
    """主函数"""
    
    print("🔥 正确的Heatmap架构训练")
    print("😅 之前用错架构了，这次用对的!")
    print("=" * 80)
    
    train_heatmap_model()

if __name__ == "__main__":
    main()
