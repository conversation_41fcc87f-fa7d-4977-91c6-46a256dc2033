#!/usr/bin/env python3
"""
调试热图问题
分析为什么热图可视化都是同一个颜色
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from heatmap_keypoint_system import HeatmapPointNet, HeatmapKeypointDataset

def debug_heatmap_predictions():
    """调试热图预测问题"""
    
    print("🔍 **调试热图预测问题**")
    print("分析为什么所有点都是同一个颜色")
    print("=" * 80)
    
    # 加载数据
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    # 选择一个样本
    sample_idx = 0
    sample_id = sample_ids[sample_idx]
    point_cloud = point_clouds[sample_idx]
    true_keypoints = keypoints[sample_idx]
    
    print(f"📊 **分析样本: {sample_id}**")
    print(f"   点云形状: {point_cloud.shape}")
    print(f"   关键点形状: {true_keypoints.shape}")
    
    # 采样点云
    if len(point_cloud) > 8192:
        indices = np.random.choice(len(point_cloud), 8192, replace=False)
        pc_sampled = point_cloud[indices]
    else:
        pc_sampled = point_cloud
    
    print(f"   采样后点云形状: {pc_sampled.shape}")
    
    # 加载模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model.eval()
        print(f"   ✅ 模型加载成功")
    except Exception as e:
        print(f"   ❌ 模型加载失败: {e}")
        return
    
    # 预测热图
    pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)  # [1, 3, N]
    
    with torch.no_grad():
        pred_heatmaps = model(pc_tensor)  # [1, 12, N]
    
    # 转换为numpy
    pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T  # [N, 12]
    
    print(f"   预测热图形状: {pred_heatmaps_np.shape}")
    
    # 分析热图统计信息
    print(f"\n📈 **热图统计分析**:")
    for i in range(12):
        heatmap = pred_heatmaps_np[:, i]
        print(f"   关键点{i}:")
        print(f"     最小值: {np.min(heatmap):.6f}")
        print(f"     最大值: {np.max(heatmap):.6f}")
        print(f"     均值: {np.mean(heatmap):.6f}")
        print(f"     标准差: {np.std(heatmap):.6f}")
        print(f"     唯一值数量: {len(np.unique(heatmap))}")
        
        # 检查是否所有值都相同
        if np.std(heatmap) < 1e-6:
            print(f"     ⚠️ 所有值几乎相同!")
        
        # 检查值的范围
        if np.max(heatmap) - np.min(heatmap) < 0.01:
            print(f"     ⚠️ 值的范围太小!")
    
    return pc_sampled, pred_heatmaps_np, true_keypoints

def debug_training_data():
    """调试训练数据生成"""
    
    print(f"\n🔍 **调试训练数据生成**:")
    
    # 加载数据
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    # 创建数据集
    dataset = HeatmapKeypointDataset(
        point_clouds[:1],  # 只用一个样本
        keypoints[:1],
        sample_ids[:1],
        sigma=2.0
    )
    
    # 获取一个样本
    pc, heatmaps, sample_id = dataset[0]
    
    print(f"   样本ID: {sample_id}")
    print(f"   点云形状: {pc.shape}")
    print(f"   热图形状: {heatmaps.shape}")
    
    # 分析生成的热图
    heatmaps_np = heatmaps.numpy().T  # [N, 12]
    
    print(f"\n📈 **生成的训练热图统计**:")
    for i in range(12):
        heatmap = heatmaps_np[:, i]
        print(f"   关键点{i}:")
        print(f"     最小值: {np.min(heatmap):.6f}")
        print(f"     最大值: {np.max(heatmap):.6f}")
        print(f"     均值: {np.mean(heatmap):.6f}")
        print(f"     标准差: {np.std(heatmap):.6f}")
        print(f"     >0.5的点数: {np.sum(heatmap > 0.5)}")
        print(f"     >0.1的点数: {np.sum(heatmap > 0.1)}")

def test_different_sigma_values():
    """测试不同的sigma值"""
    
    print(f"\n🧪 **测试不同sigma值的影响**:")
    
    # 加载数据
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    sample_ids = male_data['sample_ids']
    
    sigma_values = [0.5, 1.0, 2.0, 5.0, 10.0]
    
    for sigma in sigma_values:
        print(f"\n   Sigma = {sigma}:")
        
        dataset = HeatmapKeypointDataset(
            point_clouds[:1],
            keypoints[:1],
            sample_ids[:1],
            sigma=sigma
        )
        
        pc, heatmaps, _ = dataset[0]
        heatmaps_np = heatmaps.numpy().T  # [N, 12]
        
        # 分析第一个关键点
        heatmap = heatmaps_np[:, 0]
        print(f"     最大值: {np.max(heatmap):.6f}")
        print(f"     >0.5的点数: {np.sum(heatmap > 0.5)}")
        print(f"     >0.1的点数: {np.sum(heatmap > 0.1)}")
        print(f"     >0.01的点数: {np.sum(heatmap > 0.01)}")

def create_test_visualization():
    """创建测试可视化"""
    
    print(f"\n📊 **创建测试可视化**:")
    
    pc_sampled, pred_heatmaps_np, true_keypoints = debug_heatmap_predictions()
    
    # 选择第一个关键点进行可视化
    keypoint_idx = 0
    heatmap = pred_heatmaps_np[:, keypoint_idx]
    
    fig = plt.figure(figsize=(15, 5))
    
    # 原始热图可视化
    ax1 = fig.add_subplot(131, projection='3d')
    scatter = ax1.scatter(pc_sampled[:, 0], pc_sampled[:, 1], pc_sampled[:, 2], 
                         c=heatmap, cmap='viridis', s=1, alpha=0.8)
    ax1.set_title(f'Original Heatmap KP{keypoint_idx}')
    plt.colorbar(scatter, ax=ax1, shrink=0.8)
    
    # 增强对比度
    heatmap_enhanced = (heatmap - np.min(heatmap)) / (np.max(heatmap) - np.min(heatmap))
    ax2 = fig.add_subplot(132, projection='3d')
    scatter2 = ax2.scatter(pc_sampled[:, 0], pc_sampled[:, 1], pc_sampled[:, 2], 
                          c=heatmap_enhanced, cmap='hot', s=1, alpha=0.8)
    ax2.set_title(f'Enhanced Contrast KP{keypoint_idx}')
    plt.colorbar(scatter2, ax=ax2, shrink=0.8)
    
    # 直方图
    ax3 = fig.add_subplot(133)
    ax3.hist(heatmap, bins=50, alpha=0.7, color='blue')
    ax3.set_xlabel('Probability Value')
    ax3.set_ylabel('Frequency')
    ax3.set_title(f'Heatmap Value Distribution')
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('debug_heatmap_visualization.png', dpi=300, bbox_inches='tight')
    print(f"   📊 调试可视化已保存: debug_heatmap_visualization.png")
    plt.close()

def analyze_model_architecture():
    """分析模型架构问题"""
    
    print(f"\n🏗️ **分析模型架构**:")
    
    model = HeatmapPointNet()
    
    # 创建测试输入
    test_input = torch.randn(1, 3, 8192)
    
    print(f"   输入形状: {test_input.shape}")
    
    # 前向传播
    with torch.no_grad():
        output = model(test_input)
    
    print(f"   输出形状: {output.shape}")
    print(f"   输出范围: [{torch.min(output):.6f}, {torch.max(output):.6f}]")
    print(f"   输出均值: {torch.mean(output):.6f}")
    print(f"   输出标准差: {torch.std(output):.6f}")
    
    # 检查sigmoid激活
    print(f"   使用Sigmoid激活函数: 输出应该在[0,1]范围内")
    
    if torch.min(output) < 0 or torch.max(output) > 1:
        print(f"   ⚠️ 输出超出[0,1]范围!")
    
    # 检查梯度
    test_input.requires_grad_(True)
    output = model(test_input)
    loss = torch.mean(output)
    loss.backward()
    
    if test_input.grad is not None:
        print(f"   梯度范围: [{torch.min(test_input.grad):.6f}, {torch.max(test_input.grad):.6f}]")
    else:
        print(f"   ⚠️ 没有梯度!")

def main():
    """主函数"""
    
    print("🔧 **热图问题调试工具**")
    print("分析为什么热图可视化都是同一个颜色")
    print("=" * 80)
    
    # 1. 调试热图预测
    debug_heatmap_predictions()
    
    # 2. 调试训练数据生成
    debug_training_data()
    
    # 3. 测试不同sigma值
    test_different_sigma_values()
    
    # 4. 分析模型架构
    analyze_model_architecture()
    
    # 5. 创建测试可视化
    create_test_visualization()
    
    print(f"\n💡 **可能的问题和解决方案**:")
    print(f"   1. Sigma值太小 → 热图过于集中")
    print(f"   2. 模型未充分训练 → 输出趋于平均")
    print(f"   3. 损失函数问题 → 无法学习有效特征")
    print(f"   4. 数据预处理问题 → 标签生成错误")
    print(f"   5. 可视化范围问题 → 颜色映射不当")

if __name__ == "__main__":
    main()
