<svg width="1400" height="900" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="1400" height="900" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1400" height="70" fill="url(#headerGrad)"/>
  <text x="700" y="45" text-anchor="middle" fill="white" 
        font-family="SimHei, Arial, sans-serif" font-size="32" font-weight="bold">
    监督信号在关键点检测中的具体使用
  </text>
  
  <!-- Main content -->
  <rect x="30" y="90" width="1340" height="780" rx="15" fill="white" stroke="#374151" stroke-width="2"/>
  
  <!-- Stage 1: Ground Truth Generation -->
  <rect x="60" y="120" width="1280" height="180" rx="10" fill="#fef7ff" stroke="#8b5cf6" stroke-width="2"/>
  <text x="700" y="150" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="24" font-weight="bold">
    阶段1：监督标签生成（Ground Truth Generation）
  </text>
  
  <!-- Original point cloud -->
  <circle cx="150" cy="220" r="40" fill="#3b82f6" opacity="0.3"/>
  <circle cx="140" cy="210" r="3" fill="#1e40af"/>
  <circle cx="160" cy="230" r="3" fill="#1e40af"/>
  <circle cx="145" cy="235" r="3" fill="#1e40af"/>
  <circle cx="155" cy="205" r="3" fill="#1e40af"/>
  <circle cx="150" cy="220" r="4" fill="#dc2626" stroke="#ffffff" stroke-width="2"/>
  <text x="150" y="280" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    原始点云
  </text>
  <text x="150" y="295" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    红点：人工标注关键点
  </text>
  
  <!-- Arrow 1 -->
  <path d="M 200 220 L 280 220" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
    </marker>
  </defs>
  
  <!-- KNN process -->
  <rect x="300" y="170" width="200" height="100" rx="5" fill="#dbeafe" stroke="#3b82f6" stroke-width="1"/>
  <text x="400" y="190" text-anchor="middle" fill="#1e40af" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    KNN算法
  </text>
  <text x="400" y="210" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    选择k=15个最近邻点
  </text>
  <text x="400" y="230" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    作为潜在区域
  </text>
  <text x="400" y="250" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    标签：正样本(1)
  </text>
  
  <!-- Arrow 2 -->
  <path d="M 520 220 L 600 220" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Labeled regions -->
  <circle cx="680" cy="220" r="40" fill="#22c55e" opacity="0.3"/>
  <circle cx="670" cy="210" r="3" fill="#15803d"/>
  <circle cx="690" cy="230" r="3" fill="#15803d"/>
  <circle cx="675" cy="235" r="3" fill="#15803d"/>
  <circle cx="685" cy="205" r="3" fill="#15803d"/>
  <circle cx="680" cy="220" r="4" fill="#dc2626" stroke="#ffffff" stroke-width="2"/>
  
  <circle cx="800" cy="180" r="25" fill="#f87171" opacity="0.3"/>
  <circle cx="795" cy="175" r="2" fill="#6b7280"/>
  <circle cx="805" cy="185" r="2" fill="#6b7280"/>
  <circle cx="800" cy="180" r="2" fill="#6b7280"/>
  
  <text x="680" y="280" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    潜在区域：标签=1
  </text>
  <text x="800" y="220" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    背景区域：标签=0
  </text>
  
  <!-- Stage 2: Region Detection Training -->
  <rect x="60" y="320" width="1280" height="180" rx="10" fill="#f0f9ff" stroke="#3b82f6" stroke-width="2"/>
  <text x="700" y="350" text-anchor="middle" fill="#1e40af" 
        font-family="SimHei, Arial, sans-serif" font-size="24" font-weight="bold">
    阶段2：潜在区域检测训练（Region Detection Training）
  </text>
  
  <!-- Network training -->
  <rect x="100" y="370" width="150" height="80" rx="5" fill="#fef2f2" stroke="#f87171" stroke-width="1"/>
  <text x="175" y="395" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    分割网络
  </text>
  <text x="175" y="415" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    预测每个点的
  </text>
  <text x="175" y="430" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    分类概率
  </text>
  
  <!-- Loss function -->
  <rect x="300" y="370" width="200" height="80" rx="5" fill="#fff7ed" stroke="#f59e0b" stroke-width="1"/>
  <text x="400" y="395" text-anchor="middle" fill="#d97706" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    损失函数
  </text>
  <text x="400" y="415" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    交叉熵损失 + Penalty Dice Loss
  </text>
  <text x="400" y="430" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    L = L_CE + L_Dice
  </text>
  
  <!-- Supervision signal -->
  <rect x="550" y="370" width="180" height="80" rx="5" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="640" y="395" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    监督信号
  </text>
  <text x="640" y="415" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    Ground Truth标签
  </text>
  <text x="640" y="430" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    {0, 1, 2, ..., n}
  </text>
  
  <!-- Training process -->
  <path d="M 250 410 L 290 410" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 500 410 L 540 410" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Stage 3: Keypoint Localization Training -->
  <rect x="60" y="520" width="1280" height="200" rx="10" fill="#f0fdf4" stroke="#22c55e" stroke-width="2"/>
  <text x="700" y="550" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="24" font-weight="bold">
    阶段3：关键点定位训练（Keypoint Localization Training）
  </text>
  
  <!-- Predicted regions -->
  <rect x="100" y="570" width="120" height="80" rx="5" fill="#dbeafe" stroke="#3b82f6" stroke-width="1"/>
  <text x="160" y="595" text-anchor="middle" fill="#1e40af" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    预测的
  </text>
  <text x="160" y="615" text-anchor="middle" fill="#1e40af" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    潜在区域
  </text>
  <text x="160" y="635" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    R_i = {p_1, p_2, ..., p_k}
  </text>
  
  <!-- PointNet + DS -->
  <rect x="270" y="570" width="150" height="80" rx="5" fill="#fef7ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="345" y="595" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    PointNet +
  </text>
  <text x="345" y="615" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    双SoftMax
  </text>
  <text x="345" y="635" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    计算权重矩阵W
  </text>
  
  <!-- Weighted average -->
  <rect x="470" y="570" width="150" height="80" rx="5" fill="#fff7ed" stroke="#f59e0b" stroke-width="1"/>
  <text x="545" y="595" text-anchor="middle" fill="#d97706" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    加权平均
  </text>
  <text x="545" y="615" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    kp_pred = Σ(W_i × p_i)
  </text>
  <text x="545" y="635" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    预测关键点坐标
  </text>
  
  <!-- Ground truth keypoint -->
  <rect x="670" y="570" width="150" height="80" rx="5" fill="#fef2f2" stroke="#f87171" stroke-width="1"/>
  <text x="745" y="595" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    Ground Truth
  </text>
  <text x="745" y="615" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    关键点坐标
  </text>
  <text x="745" y="635" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    kp_gt = (x, y, z)
  </text>
  
  <!-- Loss calculation -->
  <rect x="870" y="570" width="150" height="80" rx="5" fill="#f3f4f6" stroke="#9ca3af" stroke-width="1"/>
  <text x="945" y="595" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    损失计算
  </text>
  <text x="945" y="615" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    L = ||kp_pred - kp_gt||₂
  </text>
  <text x="945" y="635" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    欧几里得距离
  </text>
  
  <!-- Arrows for stage 3 -->
  <path d="M 220 610 L 260 610" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 420 610 L 460 610" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 620 610 L 660 610" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 820 610 L 860 610" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Key insights -->
  <rect x="60" y="740" width="1280" height="110" rx="10" fill="#f8fafc" stroke="#374151" stroke-width="1"/>
  <text x="700" y="770" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    🔑 监督信号的关键作用
  </text>
  
  <text x="100" y="800" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="16">
    • <tspan fill="#dc2626" font-weight="bold">区域检测监督</tspan>：使用人工标注关键点的邻域作为正样本，训练网络识别潜在区域
  </text>
  <text x="100" y="825" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="16">
    • <tspan fill="#15803d" font-weight="bold">精确定位监督</tspan>：使用真实关键点坐标作为回归目标，训练网络精确定位关键点
  </text>
  <text x="100" y="850" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="16">
    • <tspan fill="#7c3aed" font-weight="bold">端到端训练</tspan>：两个阶段的监督信号共同指导整个网络的学习过程
  </text>
</svg>
