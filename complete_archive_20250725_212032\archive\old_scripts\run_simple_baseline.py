#!/usr/bin/env python3
"""
运行简单随机采样基线训练
"""

import torch
import numpy as np
import time
import json
from train_simple_random_baseline import train_simple_baseline, calculate_metrics

def main():
    print("🚀 **简单随机采样基线 - F3关键点检测**")
    print("📊 **目标: 建立基线，对比智能采样效果**")
    print("=" * 80)
    
    # 训练模型
    model, test_loader, best_val_error, total_time, history = train_simple_baseline()
    
    print("\n" + "=" * 80)
    print("🏁 **训练完成!**")
    print(f"⏱️  总训练时间: {total_time/60:.1f}分钟")
    print(f"🎯 最佳验证误差: {best_val_error:.3f}mm")
    
    # 测试评估
    print("\n📊 **测试集评估**")
    print("-" * 40)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    
    # 加载最佳模型
    checkpoint = torch.load('best_simple_random_f3.pth', map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    test_metrics = {'mean_distance': 0, 'std_distance': 0, 
                   'within_1mm_percent': 0, 'within_3mm_percent': 0,
                   'within_5mm_percent': 0, 'within_7mm_percent': 0}
    
    num_batches = 0
    
    with torch.no_grad():
        for batch in test_loader:
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            try:
                pred_keypoints = model(point_cloud)
                metrics = calculate_metrics(pred_keypoints, keypoints)
                
                for key in test_metrics:
                    test_metrics[key] += metrics[key]
                num_batches += 1
                
            except RuntimeError as e:
                print(f"❌ 测试批次失败: {e}")
                continue
    
    # 平均测试指标
    for key in test_metrics:
        test_metrics[key] /= num_batches
    
    print(f"🎯 测试误差: {test_metrics['mean_distance']:.3f} ± {test_metrics['std_distance']:.3f}mm")
    print(f"📊 精度分布:")
    print(f"   <1mm: {test_metrics['within_1mm_percent']:.1f}%")
    print(f"   <3mm: {test_metrics['within_3mm_percent']:.1f}%")
    print(f"   <5mm: {test_metrics['within_5mm_percent']:.1f}%")
    print(f"   <7mm: {test_metrics['within_7mm_percent']:.1f}%")
    
    # 保存结果
    results = {
        'method': 'Simple Random Sampling Baseline',
        'best_val_error': best_val_error,
        'test_metrics': test_metrics,
        'training_time_minutes': total_time / 60,
        'history': history
    }
    
    with open('simple_random_baseline_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 结果已保存到: simple_random_baseline_results.json")
    
    # 性能分析
    print("\n" + "=" * 80)
    print("📈 **性能分析**")
    print("-" * 40)
    
    if test_metrics['mean_distance'] <= 5.0:
        print("🏆 **优秀!** 达到5mm医疗级精度目标")
    elif test_metrics['mean_distance'] <= 7.631:
        print("✅ **良好!** 优于保守基线7.631mm")
    elif test_metrics['mean_distance'] <= 10.0:
        print("⚠️  **一般** 需要进一步优化")
    else:
        print("❌ **较差** 需要重新设计")
    
    print(f"📊 与保守基线(7.631mm)对比: {((test_metrics['mean_distance'] - 7.631) / 7.631 * 100):+.1f}%")
    
    if test_metrics['within_5mm_percent'] >= 90:
        print("🎯 **医疗级精度!** >90%样本在5mm内")
    elif test_metrics['within_5mm_percent'] >= 70:
        print("✅ **良好精度** >70%样本在5mm内")
    else:
        print("⚠️  **精度待提升** 需要优化")
    
    print("\n🔍 **下一步建议:**")
    if test_metrics['mean_distance'] > 7.631:
        print("1. 简单随机采样效果不佳，智能采样有必要性")
        print("2. 可以尝试密度感知采样或重要性采样")
        print("3. 考虑增加训练数据或改进数据增强")
    else:
        print("1. 简单随机采样已有不错效果")
        print("2. 智能采样可能带来进一步提升")
        print("3. 重点关注采样策略的边际收益")

if __name__ == "__main__":
    main()
