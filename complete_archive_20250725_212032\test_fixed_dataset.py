#!/usr/bin/env python3
"""
测试修复后数据集
Test Fixed Dataset
验证紧急修复后的数据集质量和模型性能
"""

import sys
import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
from sklearn.model_selection import train_test_split

# 添加原始代码路径
sys.path.insert(0, os.path.abspath("archive/old_scripts"))

def set_seed(seed=42):
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

# 导入原始模块
from ensemble_double_softmax_exact import ExactEnsembleDoubleSoftMaxPointNet

class FixedDataset(Dataset):
    """修复后数据集"""
    
    def __init__(self, point_clouds, keypoints):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def quick_train_test(model, train_loader, val_loader, epochs=50, device='cuda'):
    """快速训练测试"""
    
    print(f"🚀 快速训练测试 ({epochs}轮)")
    
    model = model.to(device)
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
    criterion = nn.MSELoss()
    
    best_val_error = float('inf')
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            loss.backward()
            
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        # 验证
        model.eval()
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        if val_error < best_val_error:
            best_val_error = val_error
        
        # 每10轮打印一次
        if epoch % 10 == 0 or epoch < 5:
            print(f"Epoch {epoch+1:2d}: Train: {train_error:.3f}, Val: {val_error:.3f}")
    
    print(f"✅ 最佳验证误差: {best_val_error:.3f}mm")
    return best_val_error

def test_dataset_comparison():
    """测试数据集对比"""
    
    print("🔍 数据集质量对比测试")
    print("=" * 80)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 测试配置
    batch_size = 4
    epochs = 50
    
    results = {}
    
    # 1. 测试历史数据集（基准）
    print(f"\n📊 测试1: 历史数据集（基准）")
    print("-" * 50)
    
    try:
        hist_data = np.load('archive/old_experiments/f3_reduced_12kp_stable.npz', allow_pickle=True)
        hist_pc = hist_data['point_clouds']
        hist_kp = hist_data['keypoints']
        
        # 数据划分
        indices = np.arange(len(hist_pc))
        train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
        train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
        
        train_dataset = FixedDataset(hist_pc[train_indices], hist_kp[train_indices])
        val_dataset = FixedDataset(hist_pc[val_indices], hist_kp[val_indices])
        
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
        
        print(f"   数据: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
        
        # 训练模型
        set_seed(123)
        model = ExactEnsembleDoubleSoftMaxPointNet(num_keypoints=12, dropout_rate=0.3, num_ensembles=3)
        hist_error = quick_train_test(model, train_loader, val_loader, epochs, device)
        
        results['historical'] = {
            'error': hist_error,
            'samples': len(hist_pc),
            'description': '历史基准数据集'
        }
        
    except Exception as e:
        print(f"❌ 历史数据集测试失败: {e}")
        results['historical'] = {'error': float('inf'), 'samples': 0}
    
    # 2. 测试原始当前数据集
    print(f"\n📊 测试2: 原始当前数据集")
    print("-" * 50)
    
    try:
        curr_data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
        curr_pc = curr_data['point_clouds']
        curr_kp_57 = curr_data['keypoints_57']
        
        # 提取12点子集
        hist_12_indices = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17]
        curr_kp_12 = curr_kp_57[:, hist_12_indices, :]
        
        # 数据划分
        indices = np.arange(len(curr_pc))
        train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
        train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
        
        train_dataset = FixedDataset(curr_pc[train_indices], curr_kp_12[train_indices])
        val_dataset = FixedDataset(curr_pc[val_indices], curr_kp_12[val_indices])
        
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
        
        print(f"   数据: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
        
        # 训练模型
        set_seed(123)
        model = ExactEnsembleDoubleSoftMaxPointNet(num_keypoints=12, dropout_rate=0.3, num_ensembles=3)
        orig_error = quick_train_test(model, train_loader, val_loader, epochs, device)
        
        results['original'] = {
            'error': orig_error,
            'samples': len(curr_pc),
            'description': '原始当前数据集'
        }
        
    except Exception as e:
        print(f"❌ 原始数据集测试失败: {e}")
        results['original'] = {'error': float('inf'), 'samples': 0}
    
    # 3. 测试修复后数据集
    print(f"\n📊 测试3: 修复后数据集")
    print("-" * 50)
    
    try:
        fixed_data = np.load('emergency_fixed_final_dataset.npz', allow_pickle=True)
        fixed_pc = fixed_data['point_clouds']
        fixed_kp_12 = fixed_data['keypoints_12']
        
        # 数据划分
        indices = np.arange(len(fixed_pc))
        train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
        train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
        
        train_dataset = FixedDataset(fixed_pc[train_indices], fixed_kp_12[train_indices])
        val_dataset = FixedDataset(fixed_pc[val_indices], fixed_kp_12[val_indices])
        
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
        
        print(f"   数据: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
        
        # 训练模型
        set_seed(123)
        model = ExactEnsembleDoubleSoftMaxPointNet(num_keypoints=12, dropout_rate=0.3, num_ensembles=3)
        fixed_error = quick_train_test(model, train_loader, val_loader, epochs, device)
        
        results['fixed'] = {
            'error': fixed_error,
            'samples': len(fixed_pc),
            'description': '紧急修复数据集'
        }
        
    except Exception as e:
        print(f"❌ 修复数据集测试失败: {e}")
        results['fixed'] = {'error': float('inf'), 'samples': 0}
    
    # 4. 结果分析
    print(f"\n📊 数据集质量对比结果")
    print("=" * 80)
    
    print(f"{'数据集':<15} {'误差(mm)':<10} {'样本数':<8} {'vs历史':<10} {'vs原始':<10} {'描述'}")
    print("-" * 80)
    
    hist_error = results.get('historical', {}).get('error', float('inf'))
    orig_error = results.get('original', {}).get('error', float('inf'))
    fixed_error = results.get('fixed', {}).get('error', float('inf'))
    
    for name, result in results.items():
        error = result['error']
        samples = result['samples']
        desc = result['description']
        
        if hist_error != float('inf'):
            vs_hist = f"{(error - hist_error)/hist_error*100:+.1f}%"
        else:
            vs_hist = "N/A"
        
        if orig_error != float('inf'):
            vs_orig = f"{(error - orig_error)/orig_error*100:+.1f}%"
        else:
            vs_orig = "N/A"
        
        print(f"{desc:<15} {error:<10.3f} {samples:<8} {vs_hist:<10} {vs_orig:<10} {name}")
    
    # 5. 改进效果评估
    print(f"\n🎯 改进效果评估")
    print("=" * 60)
    
    if fixed_error != float('inf') and orig_error != float('inf'):
        improvement = (orig_error - fixed_error) / orig_error * 100
        print(f"   修复改进幅度: {improvement:.1f}%")
        
        if improvement > 20:
            print(f"   ✅ 显著改进！修复效果明显")
        elif improvement > 10:
            print(f"   ✅ 良好改进！修复有效")
        elif improvement > 0:
            print(f"   ⚠️ 轻微改进，需要进一步优化")
        else:
            print(f"   ❌ 无改进或性能下降，需要重新分析")
    
    if fixed_error != float('inf') and hist_error != float('inf'):
        gap_to_hist = (fixed_error - hist_error) / hist_error * 100
        print(f"   与历史基准差距: {gap_to_hist:+.1f}%")
        
        if gap_to_hist < 10:
            print(f"   🎉 接近历史最佳性能！")
        elif gap_to_hist < 20:
            print(f"   ✅ 性能良好，可以进行扩展")
        else:
            print(f"   ⚠️ 仍有差距，需要进一步改进")
    
    # 6. 15点扩展可行性评估
    if fixed_error < 8.0:  # 如果12点性能良好
        print(f"\n🚀 15点扩展可行性评估")
        print("=" * 60)
        
        print(f"   12点基线性能: {fixed_error:.3f}mm")
        print(f"   15点预期性能: {fixed_error * 1.1:.3f}mm (预估)")
        print(f"   扩展建议: {'✅ 立即尝试' if fixed_error < 7.0 else '⚠️ 谨慎尝试'}")
    
    return results

def main():
    """主函数"""
    
    print("🔍 测试修复后数据集")
    print("验证紧急修复的效果和渐进式扩展可行性")
    print("=" * 80)
    
    try:
        results = test_dataset_comparison()
        
        # 保存测试结果
        import json
        with open('dataset_fix_validation_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 测试结果已保存: dataset_fix_validation_results.json")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
