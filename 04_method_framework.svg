<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="stage1Grad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:0.6" />
    </linearGradient>
    <linearGradient id="stage2Grad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:0.6" />
    </linearGradient>
  </defs>
  
  <rect width="1280" height="720" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="url(#headerGrad)"/>
  <text x="640" y="50" text-anchor="middle" fill="white" 
        font-family="Arial, sans-serif" font-size="36" font-weight="bold">
    Two-Stage Coarse-to-Fine Framework
  </text>
  
  <!-- Input Point Cloud -->
  <rect x="50" y="100" width="180" height="520" rx="15" fill="white" stroke="#6b7280" stroke-width="2"/>
  <text x="140" y="130" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    Input Point Cloud
  </text>
  
  <!-- Point cloud visualization -->
  <circle cx="140" cy="200" r="50" fill="#e5e7eb" stroke="#9ca3af" stroke-width="2"/>
  <g opacity="0.8">
    <!-- Dense points simulation -->
    <circle cx="120" cy="180" r="1.5" fill="#3b82f6"/>
    <circle cx="135" cy="175" r="1.5" fill="#3b82f6"/>
    <circle cx="150" cy="185" r="1.5" fill="#3b82f6"/>
    <circle cx="125" cy="195" r="1.5" fill="#3b82f6"/>
    <circle cx="145" cy="200" r="1.5" fill="#3b82f6"/>
    <circle cx="160" cy="190" r="1.5" fill="#3b82f6"/>
    <circle cx="130" cy="210" r="1.5" fill="#3b82f6"/>
    <circle cx="155" cy="215" r="1.5" fill="#3b82f6"/>
    <circle cx="140" cy="220" r="1.5" fill="#3b82f6"/>
    <circle cx="115" cy="205" r="1.5" fill="#3b82f6"/>
    <circle cx="165" cy="205" r="1.5" fill="#3b82f6"/>
    <circle cx="125" cy="165" r="1.5" fill="#3b82f6"/>
    <circle cx="155" cy="170" r="1.5" fill="#3b82f6"/>
    <circle cx="110" cy="190" r="1.5" fill="#3b82f6"/>
    <circle cx="170" cy="195" r="1.5" fill="#3b82f6"/>
  </g>
  
  <text x="70" y="280" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    Dense Point Cloud
  </text>
  <text x="70" y="300" fill="#6b7280" font-family="Arial, sans-serif" font-size="11">
    • 60K points (skull)
  </text>
  <text x="70" y="315" fill="#6b7280" font-family="Arial, sans-serif" font-size="11">
    • 20K points (tibia)
  </text>
  <text x="70" y="330" fill="#6b7280" font-family="Arial, sans-serif" font-size="11">
    • Complex topology
  </text>
  
  <text x="70" y="360" fill="#374151" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Target:
  </text>
  <text x="70" y="380" fill="#6b7280" font-family="Arial, sans-serif" font-size="11">
    • 14 keypoints (skull)
  </text>
  <text x="70" y="395" fill="#6b7280" font-family="Arial, sans-serif" font-size="11">
    • 8 keypoints (tibia)
  </text>
  <text x="70" y="410" fill="#6b7280" font-family="Arial, sans-serif" font-size="11">
    • Semantic labels
  </text>
  <text x="70" y="425" fill="#6b7280" font-family="Arial, sans-serif" font-size="11">
    • Anatomical significance
  </text>
  
  <!-- Arrow 1 -->
  <path d="M 240 360 L 280 360" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
    </marker>
  </defs>
  
  <!-- Stage 1: Potential Region Detection -->
  <rect x="290" y="100" width="400" height="520" rx="15" fill="url(#stage1Grad)" stroke="#1e40af" stroke-width="3"/>
  <text x="490" y="130" text-anchor="middle" fill="white" 
        font-family="Arial, sans-serif" font-size="20" font-weight="bold">
    Stage 1: Potential Region Detection
  </text>
  <text x="490" y="155" text-anchor="middle" fill="rgba(255,255,255,0.9)" 
        font-family="Arial, sans-serif" font-size="14">
    "Coarse Detection" - Identify keypoint-related regions
  </text>
  
  <!-- Network architecture for stage 1 -->
  <rect x="310" y="180" width="360" height="200" rx="10" fill="rgba(255,255,255,0.9)" stroke="white" stroke-width="2"/>
  <text x="490" y="205" text-anchor="middle" fill="#1e40af" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    Multi-scale Feature Extraction Network
  </text>
  
  <!-- Feature extraction blocks -->
  <rect x="330" y="220" width="80" height="40" rx="5" fill="#dbeafe" stroke="#3b82f6" stroke-width="1"/>
  <text x="370" y="245" text-anchor="middle" fill="#1e40af" 
        font-family="Arial, sans-serif" font-size="10" font-weight="bold">
    FPS + Ball Query
  </text>
  
  <rect x="430" y="220" width="80" height="40" rx="5" fill="#dbeafe" stroke="#3b82f6" stroke-width="1"/>
  <text x="470" y="245" text-anchor="middle" fill="#1e40af" 
        font-family="Arial, sans-serif" font-size="10" font-weight="bold">
    Cross-fusion
  </text>
  
  <rect x="530" y="220" width="80" height="40" rx="5" fill="#dbeafe" stroke="#3b82f6" stroke-width="1"/>
  <text x="570" y="245" text-anchor="middle" fill="#1e40af" 
        font-family="Arial, sans-serif" font-size="10" font-weight="bold">
    Residual Modules
  </text>
  
  <!-- Arrows between blocks -->
  <path d="M 410 240 L 425 240" stroke="#3b82f6" stroke-width="2" fill="none" marker-end="url(#bluearrow)"/>
  <path d="M 510 240 L 525 240" stroke="#3b82f6" stroke-width="2" fill="none" marker-end="url(#bluearrow)"/>
  <defs>
    <marker id="bluearrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#3b82f6"/>
    </marker>
  </defs>
  
  <!-- Loss function -->
  <rect x="330" y="280" width="280" height="80" rx="8" fill="#fef2f2" stroke="#ef4444" stroke-width="1"/>
  <text x="470" y="305" text-anchor="middle" fill="#dc2626" 
        font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Penalty Dice Loss
  </text>
  <text x="340" y="325" fill="#7f1d1d" font-family="Arial, sans-serif" font-size="11">
    L = Σyᵢlog(ŷᵢ) + (1 - ΣDice(i)) + MAX exp(αᵢ)
  </text>
  <text x="340" y="345" fill="#7f1d1d" font-family="Arial, sans-serif" font-size="11">
    • Addresses class imbalance
  </text>
  <text x="340" y="355" fill="#7f1d1d" font-family="Arial, sans-serif" font-size="11">
    • Focuses on small critical regions
  </text>
  
  <!-- Output of stage 1 -->
  <rect x="310" y="390" width="360" height="100" rx="8" fill="rgba(255,255,255,0.9)" stroke="white" stroke-width="2"/>
  <text x="490" y="415" text-anchor="middle" fill="#1e40af" 
        font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Output: Potential Regions {Rᵢ}ⁿᵢ₌₁
  </text>
  <text x="320" y="440" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • K-nearest neighbors (k=15 skull, k=20 tibia)
  </text>
  <text x="320" y="455" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • Center points {spᵢ}ⁿᵢ₌₁
  </text>
  <text x="320" y="470" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • Reduced search space for Stage 2
  </text>
  
  <!-- Metrics -->
  <rect x="310" y="500" width="360" height="100" rx="8" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="1"/>
  <text x="490" y="525" text-anchor="middle" fill="#0c4a6e" 
        font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Evaluation Metrics
  </text>
  <text x="320" y="545" fill="#374151" font-family="Arial, sans-serif" font-size="11">
    • mIoU: 66.60% (k=40, with Penalty Dice Loss)
  </text>
  <text x="320" y="560" fill="#374151" font-family="Arial, sans-serif" font-size="11">
    • MRE: 0.01450 (normalized point clouds)
  </text>
  <text x="320" y="575" fill="#374151" font-family="Arial, sans-serif" font-size="11">
    • Success in detecting small regions
  </text>
  
  <!-- Arrow 2 -->
  <path d="M 700 360 L 740 360" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Stage 2: Precise Keypoint Localization -->
  <rect x="750" y="100" width="400" height="520" rx="15" fill="url(#stage2Grad)" stroke="#d97706" stroke-width="3"/>
  <text x="950" y="130" text-anchor="middle" fill="white" 
        font-family="Arial, sans-serif" font-size="20" font-weight="bold">
    Stage 2: Precise Keypoint Localization
  </text>
  <text x="950" y="155" text-anchor="middle" fill="rgba(255,255,255,0.9)" 
        font-family="Arial, sans-serif" font-size="14">
    "Fine Detection" - Accurate keypoint positioning
  </text>
  
  <!-- Network architecture for stage 2 -->
  <rect x="770" y="180" width="360" height="200" rx="10" fill="rgba(255,255,255,0.9)" stroke="white" stroke-width="2"/>
  <text x="950" y="205" text-anchor="middle" fill="#d97706" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    PointNet + Residual + Double SoftMax
  </text>
  
  <!-- Processing blocks -->
  <rect x="790" y="220" width="70" height="40" rx="5" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
  <text x="825" y="245" text-anchor="middle" fill="#d97706" 
        font-family="Arial, sans-serif" font-size="9" font-weight="bold">
    T-Net
  </text>
  
  <rect x="880" y="220" width="70" height="40" rx="5" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
  <text x="915" y="245" text-anchor="middle" fill="#d97706" 
        font-family="Arial, sans-serif" font-size="9" font-weight="bold">
    PointNet
  </text>
  
  <rect x="970" y="220" width="70" height="40" rx="5" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
  <text x="1005" y="245" text-anchor="middle" fill="#d97706" 
        font-family="Arial, sans-serif" font-size="9" font-weight="bold">
    Residual
  </text>
  
  <rect x="1060" y="220" width="70" height="40" rx="5" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
  <text x="1095" y="245" text-anchor="middle" fill="#d97706" 
        font-family="Arial, sans-serif" font-size="9" font-weight="bold">
    Double SM
  </text>
  
  <!-- Arrows between blocks -->
  <path d="M 860 240 L 875 240" stroke="#f59e0b" stroke-width="2" fill="none" marker-end="url(#orangearrow)"/>
  <path d="M 950 240 L 965 240" stroke="#f59e0b" stroke-width="2" fill="none" marker-end="url(#orangearrow)"/>
  <path d="M 1040 240 L 1055 240" stroke="#f59e0b" stroke-width="2" fill="none" marker-end="url(#orangearrow)"/>
  <defs>
    <marker id="orangearrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#f59e0b"/>
    </marker>
  </defs>
  
  <!-- Double SoftMax mechanism -->
  <rect x="790" y="280" width="340" height="80" rx="8" fill="#fef7ff" stroke="#a855f7" stroke-width="1"/>
  <text x="960" y="305" text-anchor="middle" fill="#7c3aed" 
        font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Double SoftMax Weighting Mechanism
  </text>
  <text x="800" y="325" fill="#6b21a8" font-family="Arial, sans-serif" font-size="11">
    1st SoftMax: Initial weights → 2nd SoftMax: Threshold filtering
  </text>
  <text x="800" y="340" fill="#6b21a8" font-family="Arial, sans-serif" font-size="11">
    Final: kpᵢ = Σ(ωⱼ · WS(ωⱼ) · coordⱼ) / Σ(ωⱼ · WS(ωⱼ))
  </text>
  <text x="800" y="355" fill="#6b21a8" font-family="Arial, sans-serif" font-size="11">
    • Reduces influence of distant points
  </text>
  
  <!-- Output of stage 2 -->
  <rect x="770" y="390" width="360" height="100" rx="8" fill="rgba(255,255,255,0.9)" stroke="white" stroke-width="2"/>
  <text x="950" y="415" text-anchor="middle" fill="#d97706" 
        font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Output: Final Keypoints {kpᵢ}ⁿᵢ₌₁
  </text>
  <text x="780" y="440" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • Weighted average of coordinates
  </text>
  <text x="780" y="455" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • Semantic labels preserved
  </text>
  <text x="780" y="470" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • High precision localization
  </text>
  
  <!-- Final results -->
  <rect x="770" y="500" width="360" height="100" rx="8" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="950" y="525" text-anchor="middle" fill="#15803d" 
        font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Performance Results
  </text>
  <text x="780" y="545" fill="#374151" font-family="Arial, sans-serif" font-size="11">
    • Skull: 1.43mm MRE, 76.00% SDR (2mm)
  </text>
  <text x="780" y="560" fill="#374151" font-family="Arial, sans-serif" font-size="11">
    • Tibia: 1.54mm MRE, 76.40% SDR (2mm)
  </text>
  <text x="780" y="575" fill="#374151" font-family="Arial, sans-serif" font-size="11">
    • Comparable to 2D state-of-the-art methods
  </text>
</svg>
