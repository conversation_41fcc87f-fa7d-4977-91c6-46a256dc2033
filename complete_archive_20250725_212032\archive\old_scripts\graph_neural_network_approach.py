#!/usr/bin/env python3
"""
图神经网络方案
基于DGCNN的边缘卷积和局部图结构
专门针对骶骨孔洞等局部几何特征
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

def knn(x, k):
    """K近邻搜索"""
    inner = -2*torch.matmul(x.transpose(2, 1), x)
    xx = torch.sum(x**2, dim=1, keepdim=True)
    pairwise_distance = -xx - inner - xx.transpose(2, 1)
 
    idx = pairwise_distance.topk(k=k, dim=-1)[1]   # (batch_size, num_points, k)
    return idx

def get_graph_feature(x, k=20, idx=None):
    """获取图特征"""
    batch_size = x.size(0)
    num_points = x.size(2)
    x = x.view(batch_size, -1, num_points)
    
    if idx is None:
        idx = knn(x, k=k)   # (batch_size, num_points, k)
    
    device = torch.device('cuda')

    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1)*num_points

    idx = idx + idx_base

    idx = idx.view(-1)
 
    _, num_dims, _ = x.size()

    x = x.transpose(2, 1).contiguous()   # (batch_size, num_points, num_dims)  -> (batch_size*num_points, num_dims) #   batch_size * num_points * k + range(0, batch_size*num_points)
    feature = x.view(batch_size*num_points, -1)[idx, :]
    feature = feature.view(batch_size, num_points, k, num_dims) 
    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)
    
    feature = torch.cat((feature-x, x), dim=3).permute(0, 3, 1, 2).contiguous()
  
    return feature      # (batch_size, 2*num_dims, num_points, k)

class EdgeConv(nn.Module):
    """边缘卷积层"""
    
    def __init__(self, in_channels, out_channels, k=20):
        super(EdgeConv, self).__init__()
        
        self.k = k
        self.conv = nn.Sequential(
            nn.Conv2d(in_channels*2, out_channels, kernel_size=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.LeakyReLU(negative_slope=0.2)
        )
        
    def forward(self, x):
        x = get_graph_feature(x, k=self.k)
        x = self.conv(x)
        x = x.max(dim=-1, keepdim=False)[0]
        return x

class AnatomicalGraphAttention(nn.Module):
    """解剖结构图注意力"""
    
    def __init__(self, in_channels, out_channels, k=20):
        super(AnatomicalGraphAttention, self).__init__()
        
        self.k = k
        self.in_channels = in_channels
        self.out_channels = out_channels
        
        # 注意力权重计算
        self.attention_conv = nn.Sequential(
            nn.Conv2d(in_channels*2, in_channels//4, 1),
            nn.BatchNorm2d(in_channels//4),
            nn.LeakyReLU(0.2),
            nn.Conv2d(in_channels//4, 1, 1),
            nn.Sigmoid()
        )
        
        # 特征变换
        self.feature_conv = nn.Sequential(
            nn.Conv2d(in_channels*2, out_channels, 1),
            nn.BatchNorm2d(out_channels),
            nn.LeakyReLU(0.2)
        )
        
    def forward(self, x):
        # 获取图特征
        graph_feature = get_graph_feature(x, k=self.k)  # [B, 2*C, N, k]
        
        # 计算注意力权重
        attention_weights = self.attention_conv(graph_feature)  # [B, 1, N, k]
        
        # 应用注意力
        attended_features = graph_feature * attention_weights
        
        # 特征变换
        output_features = self.feature_conv(attended_features)
        
        # 聚合
        output = output_features.max(dim=-1)[0]  # [B, out_channels, N]
        
        return output

class LocalStructureDetector(nn.Module):
    """局部结构检测器 - 专门检测孔洞等特征"""
    
    def __init__(self, in_channels, k=20):
        super(LocalStructureDetector, self).__init__()
        
        self.k = k
        
        # 曲率特征提取
        self.curvature_conv = nn.Sequential(
            nn.Conv2d(in_channels*2, 64, 1),
            nn.BatchNorm2d(64),
            nn.LeakyReLU(0.2),
            nn.Conv2d(64, 32, 1),
            nn.BatchNorm2d(32),
            nn.LeakyReLU(0.2)
        )
        
        # 法向量估计
        self.normal_conv = nn.Sequential(
            nn.Conv2d(6, 64, 1),  # 相对坐标 + 原坐标
            nn.BatchNorm2d(64),
            nn.LeakyReLU(0.2),
            nn.Conv2d(64, 32, 1),
            nn.BatchNorm2d(32),
            nn.LeakyReLU(0.2)
        )
        
        # 孔洞检测
        self.hole_detector = nn.Sequential(
            nn.Conv2d(64, 128, 1),  # 曲率 + 法向量特征
            nn.BatchNorm2d(128),
            nn.LeakyReLU(0.2),
            nn.Conv2d(128, 64, 1),
            nn.BatchNorm2d(64),
            nn.LeakyReLU(0.2),
            nn.Conv2d(64, 1, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x, xyz):
        """
        Args:
            x: [B, C, N] 特征
            xyz: [B, 3, N] 坐标
        """
        # 获取图特征
        graph_feature = get_graph_feature(x, k=self.k)  # [B, 2*C, N, k]
        graph_xyz = get_graph_feature(xyz, k=self.k)    # [B, 6, N, k]
        
        # 曲率特征
        curvature_features = self.curvature_conv(graph_feature)  # [B, 32, N, k]
        
        # 法向量特征
        normal_features = self.normal_conv(graph_xyz)  # [B, 32, N, k]
        
        # 组合特征
        combined_features = torch.cat([curvature_features, normal_features], dim=1)  # [B, 64, N, k]
        
        # 孔洞检测
        hole_scores = self.hole_detector(combined_features)  # [B, 1, N, k]
        
        # 聚合
        hole_features = (combined_features * hole_scores).max(dim=-1)[0]  # [B, 64, N]
        
        return hole_features, hole_scores.max(dim=-1)[0]  # 特征 + 孔洞分数

class DGCNNWithAnatomicalFeatures(nn.Module):
    """带解剖特征的DGCNN"""
    
    def __init__(self, num_keypoints=12, k=20, dropout=0.3):
        super(DGCNNWithAnatomicalFeatures, self).__init__()
        
        self.num_keypoints = num_keypoints
        self.k = k
        
        # 边缘卷积层
        self.edge_conv1 = EdgeConv(3, 64, k)
        self.edge_conv2 = EdgeConv(64, 128, k)
        self.edge_conv3 = EdgeConv(128, 256, k)
        
        # 解剖结构注意力
        self.anat_att1 = AnatomicalGraphAttention(64, 64, k)
        self.anat_att2 = AnatomicalGraphAttention(128, 128, k)
        self.anat_att3 = AnatomicalGraphAttention(256, 256, k)
        
        # 局部结构检测器
        self.structure_detector1 = LocalStructureDetector(64, k)
        self.structure_detector2 = LocalStructureDetector(128, k)
        self.structure_detector3 = LocalStructureDetector(256, k)
        
        # 特征融合
        self.feature_fusion = nn.Sequential(
            nn.Conv1d(64+128+256+64+64+64, 1024, 1),  # 所有特征
            nn.BatchNorm1d(1024),
            nn.LeakyReLU(0.2),
            nn.Dropout(dropout),
            nn.Conv1d(1024, 512, 1),
            nn.BatchNorm1d(512),
            nn.LeakyReLU(0.2),
            nn.Dropout(dropout)
        )
        
        # 关键点预测
        self.keypoint_head = nn.Sequential(
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.LeakyReLU(0.2),
            nn.Dropout(dropout),
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.LeakyReLU(0.2),
            nn.Dropout(dropout),
            nn.Linear(128, num_keypoints * 3)
        )
        
        print(f"🧠 解剖特征DGCNN: {num_keypoints}个关键点")
        print(f"   - 边缘卷积 + 图注意力")
        print(f"   - 局部结构检测 (孔洞特征)")
        print(f"   - 多层特征融合")
        
    def forward(self, x):
        """
        Args:
            x: [B, N, 3] 输入点云
        """
        batch_size = x.size(0)
        xyz = x.transpose(2, 1)  # [B, 3, N]
        
        # 第一层
        x1 = self.edge_conv1(xyz)  # [B, 64, N]
        x1_att = self.anat_att1(x1)  # [B, 64, N]
        struct1, hole1 = self.structure_detector1(x1, xyz)  # [B, 64, N], [B, 1, N]
        
        # 第二层
        x2 = self.edge_conv2(x1_att)  # [B, 128, N]
        x2_att = self.anat_att2(x2)  # [B, 128, N]
        struct2, hole2 = self.structure_detector2(x2, xyz)  # [B, 64, N], [B, 1, N]
        
        # 第三层
        x3 = self.edge_conv3(x2_att)  # [B, 256, N]
        x3_att = self.anat_att3(x3)  # [B, 256, N]
        struct3, hole3 = self.structure_detector3(x3, xyz)  # [B, 64, N], [B, 1, N]
        
        # 特征融合
        all_features = torch.cat([x1_att, x2_att, x3_att, struct1, struct2, struct3], dim=1)
        fused_features = self.feature_fusion(all_features)  # [B, 512, N]
        
        # 全局特征
        global_features = fused_features.max(dim=-1)[0]  # [B, 512]
        
        # 关键点预测
        keypoints = self.keypoint_head(global_features)  # [B, num_keypoints * 3]
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints, (hole1, hole2, hole3)  # 返回关键点和孔洞检测结果

def test_dgcnn_anatomical():
    """测试解剖特征DGCNN"""
    
    print("🧪 **测试解剖特征DGCNN**")
    print("🎯 **图神经网络 + 局部结构检测**")
    print("=" * 80)
    
    batch_size = 2
    num_points = 4096
    num_keypoints = 12
    
    # 创建测试数据
    test_input = torch.randn(batch_size, num_points, 3)
    
    print(f"📊 测试输入: {test_input.shape}")
    
    # 测试模型
    model = DGCNNWithAnatomicalFeatures(num_keypoints=num_keypoints, k=20)
    
    with torch.no_grad():
        keypoints, hole_scores = model(test_input)
        print(f"📊 关键点输出: {keypoints.shape}")
        print(f"📊 孔洞检测: {len(hole_scores)}层")
    
    # 参数统计
    total_params = sum(p.numel() for p in model.parameters())
    print(f"\n📊 模型参数: {total_params:,}")
    
    print(f"\n✅ 解剖特征DGCNN测试通过!")
    
    return model

if __name__ == "__main__":
    model = test_dgcnn_anatomical()
    
    print(f"\n🎉 **解剖特征DGCNN准备完成!**")
    print("=" * 50)
    print(f"🔬 核心创新:")
    print(f"   1. 边缘卷积捕获局部几何关系")
    print(f"   2. 图注意力聚焦重要结构")
    print(f"   3. 专门的孔洞检测器")
    print(f"   4. 曲率和法向量特征")
    print(f"🎯 预期改进:")
    print(f"   - 骶骨孔洞等特征的精确检测")
    print(f"   - 局部几何关系建模")
    print(f"   - 解剖结构感知能力")
