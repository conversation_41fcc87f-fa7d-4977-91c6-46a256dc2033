#!/usr/bin/env python3
"""
高级数据集改进系统
Advanced Dataset Improvement System
基于验证的数据质量改进策略，继续深度优化
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
import json
import os
from tqdm import tqdm
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

class DatasetQualityEnhancer:
    """数据集质量增强器"""
    
    def __init__(self, dataset_path='high_quality_pelvis_57_dataset.npz'):
        self.dataset_path = dataset_path
        self.load_dataset()
        
    def load_dataset(self):
        """加载高质量数据集"""
        print("📊 加载高质量数据集...")
        data = np.load(self.dataset_path, allow_pickle=True)
        
        self.point_clouds = data['point_clouds']
        self.keypoints_57 = data['keypoints_57']
        self.sample_ids = data['sample_ids']
        self.coordinate_types = data['coordinate_types']
        
        print(f"✅ 数据集加载完成: {len(self.sample_ids)} 个样本")
        
    def analyze_anatomical_consistency(self):
        """分析解剖学一致性"""
        print("🔍 分析解剖学一致性...")
        
        # 计算关键点间的距离特征
        anatomical_features = []
        
        for i in range(len(self.keypoints_57)):
            kp = self.keypoints_57[i]
            
            # 计算重要的解剖学距离
            features = []
            
            # F1-F2区域间距离
            f1_center = np.mean(kp[0:19], axis=0)
            f2_center = np.mean(kp[19:38], axis=0)
            f3_center = np.mean(kp[38:57], axis=0)
            
            f1_f2_dist = np.linalg.norm(f1_center - f2_center)
            f1_f3_dist = np.linalg.norm(f1_center - f3_center)
            f2_f3_dist = np.linalg.norm(f2_center - f3_center)
            
            features.extend([f1_f2_dist, f1_f3_dist, f2_f3_dist])
            
            # 各区域内部的分散度
            f1_spread = np.mean([np.linalg.norm(kp[j] - f1_center) for j in range(0, 19)])
            f2_spread = np.mean([np.linalg.norm(kp[j] - f2_center) for j in range(19, 38)])
            f3_spread = np.mean([np.linalg.norm(kp[j] - f3_center) for j in range(38, 57)])
            
            features.extend([f1_spread, f2_spread, f3_spread])
            
            # 对称性特征（如果适用）
            # 这里可以添加更多解剖学特征
            
            anatomical_features.append(features)
        
        anatomical_features = np.array(anatomical_features)
        
        # 使用聚类分析识别异常样本
        kmeans = KMeans(n_clusters=3, random_state=42)
        clusters = kmeans.fit_predict(anatomical_features)
        
        # 计算每个样本到其聚类中心的距离
        cluster_distances = []
        for i, cluster in enumerate(clusters):
            center = kmeans.cluster_centers_[cluster]
            distance = np.linalg.norm(anatomical_features[i] - center)
            cluster_distances.append(distance)
        
        cluster_distances = np.array(cluster_distances)
        
        # 识别异常样本（距离聚类中心太远）
        threshold = np.mean(cluster_distances) + 2 * np.std(cluster_distances)
        outliers = np.where(cluster_distances > threshold)[0]
        
        print(f"📊 解剖学一致性分析结果:")
        print(f"   聚类数量: 3")
        print(f"   异常样本: {len(outliers)} 个")
        if len(outliers) > 0:
            print(f"   异常样本ID: {[self.sample_ids[i] for i in outliers[:5]]}")
        
        return outliers, anatomical_features, clusters
    
    def create_stratified_splits(self, anatomical_features, clusters):
        """创建分层数据划分"""
        print("🎯 创建分层数据划分...")
        
        # 确保每个聚类在训练、验证、测试集中都有代表
        train_indices = []
        val_indices = []
        test_indices = []
        
        for cluster_id in np.unique(clusters):
            cluster_samples = np.where(clusters == cluster_id)[0]
            
            # 按比例划分每个聚类
            cluster_train, cluster_temp = train_test_split(
                cluster_samples, test_size=0.4, random_state=42
            )
            cluster_val, cluster_test = train_test_split(
                cluster_temp, test_size=0.5, random_state=42
            )
            
            train_indices.extend(cluster_train)
            val_indices.extend(cluster_val)
            test_indices.extend(cluster_test)
        
        print(f"📋 分层划分结果:")
        print(f"   训练集: {len(train_indices)} 样本")
        print(f"   验证集: {len(val_indices)} 样本")
        print(f"   测试集: {len(test_indices)} 样本")
        
        # 验证每个聚类的分布
        for cluster_id in np.unique(clusters):
            train_count = sum(1 for i in train_indices if clusters[i] == cluster_id)
            val_count = sum(1 for i in val_indices if clusters[i] == cluster_id)
            test_count = sum(1 for i in test_indices if clusters[i] == cluster_id)
            print(f"   聚类{cluster_id}: 训练{train_count}, 验证{val_count}, 测试{test_count}")
        
        return train_indices, val_indices, test_indices
    
    def apply_intelligent_augmentation(self, point_cloud, keypoints, augment_type='conservative'):
        """智能数据增强"""
        
        if augment_type == 'conservative':
            # 保守的医学数据增强
            
            # 1. 小角度旋转（只在Z轴，保持解剖学合理性）
            if np.random.random() > 0.5:
                angle = np.random.uniform(-5, 5) * np.pi / 180  # ±5度
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation_matrix = np.array([
                    [cos_a, -sin_a, 0],
                    [sin_a, cos_a, 0],
                    [0, 0, 1]
                ])
                point_cloud = point_cloud @ rotation_matrix.T
                keypoints = keypoints @ rotation_matrix.T
            
            # 2. 轻微缩放
            if np.random.random() > 0.5:
                scale = np.random.uniform(0.98, 1.02)  # ±2%
                point_cloud = point_cloud * scale
                keypoints = keypoints * scale
            
            # 3. 小幅平移
            if np.random.random() > 0.5:
                # 计算数据范围
                data_range = np.ptp(np.vstack([point_cloud, keypoints]), axis=0)
                translation = np.random.uniform(-0.01, 0.01, 3) * data_range
                point_cloud = point_cloud + translation
                keypoints = keypoints + translation
            
            # 4. 轻微噪声（只加到点云）
            if np.random.random() > 0.5:
                noise_std = np.std(point_cloud) * 0.005  # 0.5%的噪声
                noise = np.random.normal(0, noise_std, point_cloud.shape)
                point_cloud = point_cloud + noise
        
        return point_cloud, keypoints
    
    def create_enhanced_dataset(self, use_augmentation=True, augmentation_factor=2):
        """创建增强数据集"""
        print(f"🚀 创建增强数据集...")
        print(f"   数据增强: {'启用' if use_augmentation else '禁用'}")
        if use_augmentation:
            print(f"   增强倍数: {augmentation_factor}x")
        
        # 分析解剖学一致性
        outliers, anatomical_features, clusters = self.analyze_anatomical_consistency()
        
        # 移除异常样本
        valid_indices = [i for i in range(len(self.sample_ids)) if i not in outliers]
        
        enhanced_point_clouds = []
        enhanced_keypoints = []
        enhanced_sample_ids = []
        enhanced_augmentation_flags = []
        
        # 添加原始高质量样本
        for i in valid_indices:
            enhanced_point_clouds.append(self.point_clouds[i])
            enhanced_keypoints.append(self.keypoints_57[i])
            enhanced_sample_ids.append(f"{self.sample_ids[i]}_original")
            enhanced_augmentation_flags.append(False)
        
        # 添加增强样本
        if use_augmentation:
            for aug_round in range(augmentation_factor):
                for i in valid_indices:
                    pc_aug, kp_aug = self.apply_intelligent_augmentation(
                        self.point_clouds[i].copy(),
                        self.keypoints_57[i].copy(),
                        augment_type='conservative'
                    )
                    
                    enhanced_point_clouds.append(pc_aug)
                    enhanced_keypoints.append(kp_aug)
                    enhanced_sample_ids.append(f"{self.sample_ids[i]}_aug_{aug_round+1}")
                    enhanced_augmentation_flags.append(True)
        
        enhanced_point_clouds = np.array(enhanced_point_clouds)
        enhanced_keypoints = np.array(enhanced_keypoints)
        
        print(f"✅ 增强数据集创建完成:")
        print(f"   原始样本: {len(valid_indices)}")
        print(f"   增强样本: {len(enhanced_point_clouds) - len(valid_indices)}")
        print(f"   总样本数: {len(enhanced_point_clouds)}")
        
        return (enhanced_point_clouds, enhanced_keypoints, enhanced_sample_ids, 
                enhanced_augmentation_flags, valid_indices, anatomical_features, clusters)

class AdaptivePointNet57(nn.Module):
    """自适应PointNet57 - 基于最佳实践的架构"""
    
    def __init__(self, num_keypoints=57, dropout_rate=0.25):
        super(AdaptivePointNet57, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 特征提取 - 基于验证的最佳配置
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 轻量级残差连接
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        # 自适应回归头
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, num_keypoints * 3)
        
        # 批归一化
        self.fc_bn1 = nn.BatchNorm1d(512)
        self.fc_bn2 = nn.BatchNorm1d(256)
        self.fc_bn3 = nn.BatchNorm1d(128)
        
        # 自适应Dropout
        self.dropout = nn.Dropout(dropout_rate)
        
        # 权重初始化
        self._initialize_weights()
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"🏗️ AdaptivePointNet57: {total_params:,} 参数")
    
    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取 + 残差连接
        x1 = F.relu(self.bn1(self.conv1(x)))
        x2 = F.relu(self.bn2(self.conv2(x1)))
        x3 = F.relu(self.bn3(self.conv3(x2)))
        x3_res = x3 + self.residual1(x1)  # 残差连接
        
        x4 = F.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)  # 残差连接
        
        x5 = F.relu(self.bn5(self.conv5(x4_res)))
        
        # 全局最大池化
        global_feat = torch.max(x5, 2)[0]
        
        # 自适应回归
        x = F.relu(self.fc_bn1(self.fc1(global_feat)))
        x = self.dropout(x)
        x = F.relu(self.fc_bn2(self.fc2(x)))
        x = self.dropout(x)
        x = F.relu(self.fc_bn3(self.fc3(x)))
        x = self.dropout(x)
        
        keypoints = self.fc4(x)
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

def main():
    """主函数 - 高级数据集改进系统"""
    
    print("🎯 高级数据集改进系统")
    print("基于验证的数据质量改进策略，继续深度优化")
    print("=" * 80)
    
    # 创建数据集质量增强器
    enhancer = DatasetQualityEnhancer()
    
    # 创建增强数据集
    (enhanced_pc, enhanced_kp, enhanced_ids, aug_flags, 
     valid_indices, anatomical_features, clusters) = enhancer.create_enhanced_dataset(
        use_augmentation=True, augmentation_factor=1  # 保守的增强
    )
    
    # 创建分层数据划分
    train_indices, val_indices, test_indices = enhancer.create_stratified_splits(
        anatomical_features[valid_indices], clusters[valid_indices]
    )
    
    # 调整索引以适应增强数据集
    original_count = len(valid_indices)
    
    # 扩展索引以包含增强样本
    enhanced_train_indices = []
    enhanced_val_indices = []
    enhanced_test_indices = []
    
    for i in train_indices:
        enhanced_train_indices.append(i)  # 原始样本
        enhanced_train_indices.append(i + original_count)  # 增强样本
    
    for i in val_indices:
        enhanced_val_indices.append(i)  # 只使用原始样本进行验证
    
    for i in test_indices:
        enhanced_test_indices.append(i)  # 只使用原始样本进行测试
    
    print(f"📋 最终数据划分:")
    print(f"   训练集: {len(enhanced_train_indices)} 样本 (包含增强)")
    print(f"   验证集: {len(enhanced_val_indices)} 样本 (仅原始)")
    print(f"   测试集: {len(enhanced_test_indices)} 样本 (仅原始)")
    
    # 保存增强数据集
    np.savez_compressed(
        'enhanced_high_quality_57_dataset.npz',
        point_clouds=enhanced_pc,
        keypoints_57=enhanced_kp,
        sample_ids=enhanced_ids,
        augmentation_flags=aug_flags,
        train_indices=enhanced_train_indices,
        val_indices=enhanced_val_indices,
        test_indices=enhanced_test_indices,
        anatomical_features=anatomical_features,
        clusters=clusters,
        metadata={
            'enhancement_strategy': 'conservative_augmentation',
            'anatomical_consistency_analysis': 'applied',
            'stratified_splitting': 'applied',
            'outlier_removal': 'applied'
        }
    )
    
    print(f"\n✅ 高级增强数据集已保存: enhanced_high_quality_57_dataset.npz")
    print(f"🎉 数据集改进系统完成！")
    print(f"\n💡 关键改进:")
    print(f"   ✅ 解剖学一致性分析")
    print(f"   ✅ 智能异常检测")
    print(f"   ✅ 分层数据划分")
    print(f"   ✅ 保守的医学数据增强")
    print(f"   ✅ 自适应模型架构")
    
    print(f"\n🚀 下一步:")
    print(f"   1. 使用增强数据集训练自适应模型")
    print(f"   2. 验证改进效果")
    print(f"   3. 进行性能对比分析")

if __name__ == "__main__":
    main()
