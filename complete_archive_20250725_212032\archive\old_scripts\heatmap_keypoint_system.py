#!/usr/bin/env python3
"""
3D医学点云热图关键点预测系统
实现基于概率分布的关键点检测，提供不确定性量化
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns
import time
import os

class HeatmapKeypointDataset(Dataset):
    """热图关键点数据集"""
    
    def __init__(self, point_clouds, keypoints, sample_ids, num_points=8192, sigma=2.0, augment=False):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.sample_ids = sample_ids
        self.num_points = num_points
        self.sigma = sigma  # 高斯分布的标准差
        self.augment = augment
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        pc = self.point_clouds[idx].copy()
        kp = self.keypoints[idx].copy()
        
        # 随机采样点云
        if len(pc) > self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=False)
            pc = pc[indices]
        elif len(pc) < self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=True)
            pc = pc[indices]
        
        if self.augment:
            pc, kp = self.apply_augmentation(pc, kp)
        
        # 生成热图标签
        heatmaps = self.generate_heatmaps(pc, kp)
        
        # 转换为tensor
        pc = torch.FloatTensor(pc).transpose(0, 1)  # [3, N]
        heatmaps = torch.FloatTensor(heatmaps).transpose(0, 1)  # [12, N]
        
        return pc, heatmaps, self.sample_ids[idx]
    
    def apply_augmentation(self, pc, kp):
        """数据增强"""
        # 随机旋转
        if np.random.random() > 0.5:
            angle = np.random.uniform(-5, 5) * np.pi / 180
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            pc = pc @ rotation_matrix.T
            kp = kp @ rotation_matrix.T
        
        # 随机缩放
        if np.random.random() > 0.5:
            scale = np.random.uniform(0.95, 1.05)
            pc *= scale
            kp *= scale
        
        return pc, kp
    
    def generate_heatmaps(self, pc, kp):
        """生成高斯热图标签"""
        num_points = len(pc)
        num_keypoints = len(kp)
        heatmaps = np.zeros((num_points, num_keypoints))
        
        for i, keypoint in enumerate(kp):
            # 计算每个点到关键点的距离
            distances = np.linalg.norm(pc - keypoint, axis=1)
            
            # 生成高斯分布
            heatmap = np.exp(-distances**2 / (2 * self.sigma**2))
            
            # 归一化到[0,1]
            if np.max(heatmap) > 0:
                heatmap = heatmap / np.max(heatmap)
            
            heatmaps[:, i] = heatmap
        
        return heatmaps

class HeatmapPointNet(nn.Module):
    """热图预测PointNet"""
    
    def __init__(self, num_points=8192, num_keypoints=12):
        super(HeatmapPointNet, self).__init__()
        
        # 特征提取层
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        
        # 全局特征
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.bn4 = nn.BatchNorm1d(512)
        
        # 热图预测头
        self.conv5 = nn.Conv1d(512 + 256, 256, 1)  # 全局+局部特征
        self.conv6 = nn.Conv1d(256, 128, 1)
        self.conv7 = nn.Conv1d(128, num_keypoints, 1)  # 输出每个关键点的概率
        
        self.bn5 = nn.BatchNorm1d(256)
        self.bn6 = nn.BatchNorm1d(128)
        
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        # x: [B, 3, N]
        batch_size, _, num_points = x.size()
        
        # 局部特征提取
        x1 = self.relu(self.bn1(self.conv1(x)))
        x2 = self.relu(self.bn2(self.conv2(x1)))
        x3 = self.relu(self.bn3(self.conv3(x2)))
        
        # 全局特征
        x4 = self.relu(self.bn4(self.conv4(x3)))
        global_feat = torch.max(x4, 2)[0]  # [B, 512]
        
        # 扩展全局特征到每个点
        global_feat_expanded = global_feat.unsqueeze(2).repeat(1, 1, num_points)  # [B, 512, N]
        
        # 融合全局和局部特征
        combined_feat = torch.cat([x3, global_feat_expanded], dim=1)  # [B, 768, N]
        
        # 热图预测
        x5 = self.relu(self.bn5(self.conv5(combined_feat)))
        x5 = self.dropout(x5)
        x6 = self.relu(self.bn6(self.conv6(x5)))
        x6 = self.dropout(x6)
        heatmaps = self.sigmoid(self.conv7(x6))  # [B, 12, N]
        
        return heatmaps

class FocalLoss(nn.Module):
    """Focal Loss for heatmap regression"""
    
    def __init__(self, alpha=1, gamma=2):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
    
    def forward(self, pred, target):
        # pred: [B, 12, N], target: [B, 12, N]
        bce_loss = nn.functional.binary_cross_entropy(pred, target, reduction='none')
        
        # 计算focal weight
        pt = torch.where(target == 1, pred, 1 - pred)
        focal_weight = self.alpha * (1 - pt) ** self.gamma
        
        focal_loss = focal_weight * bce_loss
        return focal_loss.mean()

def load_male_dataset():
    """加载男性数据集"""
    print("📊 **加载男性数据集**")
    
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    print(f"   男性样本数: {len(sample_ids)}")
    print(f"   点云形状: {point_clouds.shape}")
    print(f"   关键点形状: {keypoints.shape}")
    
    return sample_ids, point_clouds, keypoints

def create_train_val_split(sample_ids, point_clouds, keypoints, test_size=0.2):
    """创建训练验证分割"""
    print(f"\n📋 **创建数据分割**")
    
    n_samples = len(sample_ids)
    
    # 80/20分割
    train_idx, val_idx = train_test_split(
        range(n_samples), 
        test_size=test_size,
        random_state=42,
        shuffle=True
    )
    
    train_data = {
        'sample_ids': sample_ids[train_idx],
        'point_clouds': point_clouds[train_idx],
        'keypoints': keypoints[train_idx]
    }
    
    val_data = {
        'sample_ids': sample_ids[val_idx],
        'point_clouds': point_clouds[val_idx],
        'keypoints': keypoints[val_idx]
    }
    
    print(f"   训练集: {len(train_idx)}个样本")
    print(f"   验证集: {len(val_idx)}个样本")
    
    return train_data, val_data

def train_heatmap_model(train_data, val_data, epochs=150, batch_size=2):
    """训练热图模型"""
    print(f"\n🚀 **训练热图预测模型**")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"   使用设备: {device}")
    
    # 创建数据集和数据加载器
    train_dataset = HeatmapKeypointDataset(
        train_data['point_clouds'],
        train_data['keypoints'],
        train_data['sample_ids'],
        sigma=2.0,  # 高斯分布标准差
        augment=True
    )
    
    val_dataset = HeatmapKeypointDataset(
        val_data['point_clouds'],
        val_data['keypoints'],
        val_data['sample_ids'],
        sigma=2.0,
        augment=False
    )
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    # 创建模型
    model = HeatmapPointNet().to(device)
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=20, factor=0.5)
    
    # 使用Focal Loss
    criterion = FocalLoss(alpha=1, gamma=2)
    
    # 训练历史
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    patience_counter = 0
    
    print(f"   开始训练...")
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        
        for batch_idx, (pc, heatmaps, _) in enumerate(train_loader):
            pc, heatmaps = pc.to(device), heatmaps.to(device)
            
            optimizer.zero_grad()
            pred_heatmaps = model(pc)
            loss = criterion(pred_heatmaps, heatmaps)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for pc, heatmaps, _ in val_loader:
                pc, heatmaps = pc.to(device), heatmaps.to(device)
                pred_heatmaps = model(pc)
                loss = criterion(pred_heatmaps, heatmaps)
                val_loss += loss.item()
        
        val_loss /= len(val_loader)
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_heatmap_model.pth')
        else:
            patience_counter += 1
        
        if epoch % 20 == 0:
            print(f"   Epoch {epoch}: Train Loss={train_loss:.6f}, Val Loss={val_loss:.6f}")
        
        # 早停
        if patience_counter >= 30:
            print(f"   早停于epoch {epoch}")
            break
    
    print(f"   训练完成! 最佳验证损失: {best_val_loss:.6f}")
    
    return model, train_losses, val_losses

def extract_keypoints_from_heatmaps(heatmaps, point_cloud, threshold=0.5):
    """从热图中提取关键点坐标"""
    # heatmaps: [12, N], point_cloud: [N, 3]
    keypoints = []
    confidences = []
    
    for i in range(heatmaps.shape[0]):
        heatmap = heatmaps[i]
        
        # 找到高置信度的点
        high_conf_mask = heatmap > threshold
        
        if np.sum(high_conf_mask) > 0:
            # 加权平均计算关键点位置
            weights = heatmap[high_conf_mask]
            points = point_cloud[high_conf_mask]
            
            # 加权平均
            keypoint = np.average(points, axis=0, weights=weights)
            confidence = np.max(heatmap)
        else:
            # 如果没有高置信度点，使用最高置信度点
            max_idx = np.argmax(heatmap)
            keypoint = point_cloud[max_idx]
            confidence = heatmap[max_idx]
        
        keypoints.append(keypoint)
        confidences.append(confidence)
    
    return np.array(keypoints), np.array(confidences)

def evaluate_heatmap_model(model, val_data, device):
    """评估热图模型"""
    print(f"\n📊 **评估热图模型**")
    
    model.eval()
    
    val_dataset = HeatmapKeypointDataset(
        val_data['point_clouds'],
        val_data['keypoints'],
        val_data['sample_ids'],
        augment=False
    )
    
    val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False)
    
    total_error = 0.0
    total_samples = 0
    sample_results = {}
    
    with torch.no_grad():
        for pc, true_heatmaps, sample_id in val_loader:
            pc = pc.to(device)
            pred_heatmaps = model(pc)
            
            # 转换为numpy
            pc_np = pc.cpu().numpy().squeeze().T  # [N, 3]
            pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T  # [N, 12]
            true_keypoints = val_data['keypoints'][total_samples]
            
            # 从热图提取关键点
            pred_keypoints, confidences = extract_keypoints_from_heatmaps(
                pred_heatmaps_np.T, pc_np
            )
            
            # 计算误差
            errors = []
            for i in range(12):
                error = np.linalg.norm(pred_keypoints[i] - true_keypoints[i])
                errors.append(error)
            
            avg_error = np.mean(errors)
            avg_confidence = np.mean(confidences)
            
            sample_results[sample_id[0]] = {
                'avg_error': avg_error,
                'errors': errors,
                'confidences': confidences,
                'avg_confidence': avg_confidence
            }
            
            total_error += avg_error
            total_samples += 1
    
    overall_avg_error = total_error / total_samples
    
    print(f"   总体平均误差: {overall_avg_error:.2f}mm")
    print(f"   评估样本数: {total_samples}")
    
    return sample_results, overall_avg_error

def create_training_plots(train_losses, val_losses):
    """创建训练过程图"""
    print(f"\n📊 **创建训练过程可视化**")

    plt.figure(figsize=(12, 5))

    # 损失曲线
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='Training Loss', color='blue')
    plt.plot(val_losses, label='Validation Loss', color='red')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 性能分析
    plt.subplot(1, 2, 2)
    epochs = len(train_losses)
    improvement = (train_losses[0] - train_losses[-1]) / train_losses[0] * 100

    plt.text(0.1, 0.8, f'Training Summary:', fontsize=14, fontweight='bold', transform=plt.gca().transAxes)
    plt.text(0.1, 0.7, f'Total Epochs: {epochs}', fontsize=12, transform=plt.gca().transAxes)
    plt.text(0.1, 0.6, f'Final Train Loss: {train_losses[-1]:.6f}', fontsize=12, transform=plt.gca().transAxes)
    plt.text(0.1, 0.5, f'Final Val Loss: {val_losses[-1]:.6f}', fontsize=12, transform=plt.gca().transAxes)
    plt.text(0.1, 0.4, f'Improvement: {improvement:.1f}%', fontsize=12, transform=plt.gca().transAxes)
    plt.text(0.1, 0.3, f'Model: HeatmapPointNet', fontsize=12, transform=plt.gca().transAxes)
    plt.text(0.1, 0.2, f'Loss: Focal Loss', fontsize=12, transform=plt.gca().transAxes)

    plt.axis('off')
    plt.title('Training Statistics')

    plt.tight_layout()
    plt.savefig('heatmap_training_progress.png', dpi=300, bbox_inches='tight')
    print(f"   📊 训练过程图已保存: heatmap_training_progress.png")
    plt.close()

def main():
    """主函数"""
    print("🎯 **3D医学点云热图关键点预测系统**")
    print("基于概率分布的关键点检测，提供不确定性量化")
    print("=" * 80)

    # 加载数据
    sample_ids, point_clouds, keypoints = load_male_dataset()

    # 创建训练验证分割
    train_data, val_data = create_train_val_split(sample_ids, point_clouds, keypoints)

    # 训练模型
    model, train_losses, val_losses = train_heatmap_model(train_data, val_data)

    # 创建训练过程图
    create_training_plots(train_losses, val_losses)

    # 加载最佳模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.load_state_dict(torch.load('best_heatmap_model.pth'))

    # 评估模型
    sample_results, overall_avg_error = evaluate_heatmap_model(model, val_data, device)

    print(f"\n🎉 **热图预测系统训练完成!**")
    print(f"✅ 模型已保存: best_heatmap_model.pth")
    print(f"✅ 总体性能: {overall_avg_error:.2f}mm")
    print(f"✅ 提供了不确定性量化功能")
    print(f"✅ 准备进行可视化分析")

    return model, sample_results, val_data

if __name__ == "__main__":
    main()
