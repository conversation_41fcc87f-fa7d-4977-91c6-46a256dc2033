#!/usr/bin/env python3
"""
Analyze F3 Dataset Results

Analyze the complete F3 single component dataset that was created.
"""

import json
import numpy as np
from pathlib import Path

def analyze_f3_complete_dataset():
    """Analyze the complete F3 dataset results"""
    
    print("📊 **F3单部件数据集完整分析**")
    print("🎯 **基于97个XYZ样本的F3部件处理结果**")
    print("=" * 80)
    
    # Load complete dataset
    complete_file = Path("f3_dataset_complete.json")
    
    if not complete_file.exists():
        print("❌ 完整数据集文件不存在")
        return
    
    with open(complete_file, 'r') as f:
        complete_data = json.load(f)
    
    print(f"📋 **完整数据集统计**:")
    print(f"   成功处理样本: {len(complete_data)}")
    
    # Analyze quality metrics
    distances = [sample['quality_metrics']['mean_surface_distance'] for sample in complete_data]
    within_1mm = [sample['quality_metrics']['within_1mm_percent'] for sample in complete_data]
    within_5mm = [sample['quality_metrics']['within_5mm_percent'] for sample in complete_data]
    keypoint_counts = [sample['quality_metrics']['keypoints_count'] for sample in complete_data]
    vertex_counts = [sample['quality_metrics']['vertices_count'] for sample in complete_data]
    
    print(f"\n📊 **质量统计分析**:")
    print(f"   平均表面距离: {np.mean(distances):.2f}±{np.std(distances):.2f}mm")
    print(f"   距离范围: {np.min(distances):.2f} - {np.max(distances):.2f}mm")
    print(f"   平均1mm精度: {np.mean(within_1mm):.1f}±{np.std(within_1mm):.1f}%")
    print(f"   平均5mm精度: {np.mean(within_5mm):.1f}±{np.std(within_5mm):.1f}%")
    
    print(f"\n📊 **数据结构统计**:")
    print(f"   平均关键点数: {np.mean(keypoint_counts):.1f}±{np.std(keypoint_counts):.1f}")
    print(f"   关键点数范围: {np.min(keypoint_counts)} - {np.max(keypoint_counts)}")
    print(f"   平均顶点数: {np.mean(vertex_counts):.0f}±{np.std(vertex_counts):.0f}")
    print(f"   顶点数范围: {np.min(vertex_counts)} - {np.max(vertex_counts)}")
    
    # Quality distribution
    excellent_samples = sum(1 for d in distances if d <= 1.0)
    good_samples = sum(1 for d in distances if d <= 5.0)
    acceptable_samples = sum(1 for d in distances if d <= 10.0)
    
    print(f"\n🎯 **质量分布**:")
    print(f"   优秀 (≤1mm): {excellent_samples}/{len(complete_data)} ({excellent_samples/len(complete_data)*100:.1f}%)")
    print(f"   良好 (≤5mm): {good_samples}/{len(complete_data)} ({good_samples/len(complete_data)*100:.1f}%)")
    print(f"   可接受 (≤10mm): {acceptable_samples}/{len(complete_data)} ({acceptable_samples/len(complete_data)*100:.1f}%)")
    
    # Sample IDs
    sample_ids = [sample['sample_id'] for sample in complete_data]
    print(f"\n📋 **样本覆盖**:")
    print(f"   样本ID范围: {min(sample_ids)} - {max(sample_ids)}")
    
    # Check for missing samples
    expected_samples = set()
    for i in range(600001, 600130):  # Expected range
        sample_id = str(i)
        if sample_id not in {'600025', '600026', '600027'}:  # Exclude LPS samples
            expected_samples.add(sample_id)
    
    processed_samples = set(sample_ids)
    missing_samples = expected_samples - processed_samples
    
    print(f"   预期样本数: {len(expected_samples)}")
    print(f"   实际处理数: {len(processed_samples)}")
    print(f"   缺失样本数: {len(missing_samples)}")
    
    if missing_samples:
        print(f"   缺失样本: {sorted(list(missing_samples))[:10]}{'...' if len(missing_samples) > 10 else ''}")
    
    return complete_data

def analyze_f3_simple_dataset():
    """Analyze the simple format dataset"""
    
    print(f"\n📁 **F3SimpleDataset分析**")
    print("=" * 60)
    
    dataset_dir = Path("F3SimpleDataset")
    
    if not dataset_dir.exists():
        print("❌ F3SimpleDataset目录不存在")
        return
    
    # Load metadata
    metadata_file = dataset_dir / "metadata.json"
    if metadata_file.exists():
        with open(metadata_file, 'r') as f:
            metadata = json.load(f)
        
        print(f"📊 **数据集元信息**:")
        print(f"   数据集名称: {metadata['dataset_name']}")
        print(f"   总样本数: {metadata['total_samples']}")
        print(f"   关键点数: {metadata['keypoints_per_sample']}")
        print(f"   部件: {metadata['component']}")
        print(f"   格式: {metadata['format']}")
        
        print(f"\n📊 **数据划分**:")
        for split, count in metadata['splits'].items():
            print(f"   {split}: {count} 样本")
    
    # Check actual files
    train_files = list((dataset_dir / "train").glob("*_keypoints.npy"))
    val_files = list((dataset_dir / "val").glob("*_keypoints.npy"))
    test_files = list((dataset_dir / "test").glob("*_keypoints.npy"))
    
    print(f"\n📁 **实际文件统计**:")
    print(f"   训练集: {len(train_files)} 样本")
    print(f"   验证集: {len(val_files)} 样本")
    print(f"   测试集: {len(test_files)} 样本")
    print(f"   总计: {len(train_files) + len(val_files) + len(test_files)} 样本")
    
    # Test loading a sample
    if train_files:
        test_file = train_files[0]
        sample_id = test_file.stem.replace('_keypoints', '')
        
        try:
            keypoints = np.load(test_file)
            pointcloud_file = test_file.parent / f"{sample_id}_pointcloud.npy"
            pointcloud = np.load(pointcloud_file)
            
            print(f"\n🧪 **样本测试 ({sample_id})**:")
            print(f"   关键点形状: {keypoints.shape}")
            print(f"   点云形状: {pointcloud.shape}")
            print(f"   关键点范围: [{np.min(keypoints, axis=0)}, {np.max(keypoints, axis=0)}]")
            print(f"   点云范围: [{np.min(pointcloud, axis=0)}, {np.max(pointcloud, axis=0)}]")
            
        except Exception as e:
            print(f"   ❌ 样本加载失败: {e}")

def generate_f3_dataset_summary():
    """Generate comprehensive F3 dataset summary"""
    
    print(f"\n" + "="*80)
    print(f"📋 **F3单部件数据集总结报告**")
    print(f"=" * 80)
    
    # Analyze complete dataset
    complete_data = analyze_f3_complete_dataset()
    
    # Analyze simple dataset
    analyze_f3_simple_dataset()
    
    # Final assessment
    print(f"\n🎯 **最终评估**:")
    
    if complete_data:
        total_samples = len(complete_data)
        distances = [sample['quality_metrics']['mean_surface_distance'] for sample in complete_data]
        avg_distance = np.mean(distances)
        
        print(f"\n✅ **数据集创建成功!**")
        print(f"   📊 样本数量: {total_samples} (目标97个)")
        print(f"   📊 成功率: {total_samples/97*100:.1f}%")
        print(f"   📊 平均质量: {avg_distance:.2f}mm表面距离")
        
        if total_samples >= 90:
            print(f"   🎉 样本数量充足，可以进行有效训练")
        elif total_samples >= 70:
            print(f"   ⚠️ 样本数量可接受，建议谨慎训练")
        else:
            print(f"   ❌ 样本数量不足，需要改进处理流程")
        
        if avg_distance <= 5.0:
            print(f"   🎉 表面投影质量优秀")
        elif avg_distance <= 20.0:
            print(f"   ⚠️ 表面投影质量可接受")
        else:
            print(f"   ❌ 表面投影质量需要改进")
        
        print(f"\n🚀 **数据集论文价值**:")
        print(f"   ✅ 成功从原始数据创建了高质量F3单部件数据集")
        print(f"   ✅ 解决了STL-CSV坐标系对齐的技术挑战")
        print(f"   ✅ 验证了分部件处理的可行性")
        print(f"   ✅ 提供了完整的质量评估和验证流程")
        print(f"   ✅ 为扩展到F1/F2部件奠定了基础")
        
        print(f"\n🎯 **下一步建议**:")
        print(f"   1. 使用F3数据集训练关键点检测模型")
        print(f"   2. 验证模型性能和数据质量")
        print(f"   3. 如果效果良好，扩展到F1和F2部件")
        print(f"   4. 完成数据集论文的F3部分验证")
        print(f"   5. 分析为什么F3对齐质量好而F1/F2不好")
    
    else:
        print(f"❌ 数据集分析失败")

def main():
    """Main analysis function"""
    generate_f3_dataset_summary()

if __name__ == "__main__":
    main()
