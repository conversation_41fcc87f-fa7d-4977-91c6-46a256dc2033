{"method": "Detailed Diagnostic Training", "focus_ratio": 0.7, "best_val_error": 7.978360271453857, "training_time_minutes": 0.8764425118764242, "history": [{"epoch": 1, "train_loss": 629.9093053481158, "val_loss": 654.7827270507812, "train_metrics": {"mean_distance": 45.669125500847315, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 46.525759887695315, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00011763487149422567, "epoch_time": 1.1475090980529785, "avg_batch_time": 0.05864780089434456, "avg_gradient_norm": 919.510548132974, "gradient_norm_std": 601.7186554038444}, {"epoch": 2, "train_loss": 627.8552605124081, "val_loss": 652.359521484375, "train_metrics": {"mean_distance": 45.59250595990349, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 46.44236602783203, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00022681216083904245, "epoch_time": 0.5324373245239258, "avg_batch_time": 0.02485060691833496, "avg_gradient_norm": 655.3740879014595, "gradient_norm_std": 426.4547266899467}, {"epoch": 3, "train_loss": 624.2740083582261, "val_loss": 650.2146362304687, "train_metrics": {"mean_distance": 45.44153594970703, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 46.351280212402344, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00039671904368029226, "epoch_time": 0.5274240970611572, "avg_batch_time": 0.02472076696508071, "avg_gradient_norm": 427.490971376638, "gradient_norm_std": 164.25950455622467}, {"epoch": 4, "train_loss": 617.8638377470129, "val_loss": 640.192919921875, "train_metrics": {"mean_distance": 45.18149903241326, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 45.9417236328125, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0006105280892239888, "epoch_time": 0.526749849319458, "avg_batch_time": 0.024856286890366498, "avg_gradient_norm": 391.89603132840256, "gradient_norm_std": 203.12692066994666}, {"epoch": 5, "train_loss": 606.9803035960477, "val_loss": 621.63916015625, "train_metrics": {"mean_distance": 44.73855568380917, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 45.15133972167969, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008470638344350262, "epoch_time": 0.5341403484344482, "avg_batch_time": 0.02530797790078556, "avg_gradient_norm": 420.4420877725967, "gradient_norm_std": 105.09812746611375}, {"epoch": 6, "train_loss": 594.2092536477481, "val_loss": 618.8882934570313, "train_metrics": {"mean_distance": 44.20373669792624, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 45.06227798461914, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.001082899983590225, "epoch_time": 0.5241785049438477, "avg_batch_time": 0.024727961596320656, "avg_gradient_norm": 371.18718568910975, "gradient_norm_std": 140.44603763638315}, {"epoch": 7, "train_loss": 573.1142865349265, "val_loss": 609.3942504882813, "train_metrics": {"mean_distance": 43.305112053366265, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 44.62669067382812, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0012946795283549826, "epoch_time": 0.53324294090271, "avg_batch_time": 0.025205079246969783, "avg_gradient_norm": 371.7593999661317, "gradient_norm_std": 135.01514096550235}, {"epoch": 8, "train_loss": 543.9397672765396, "val_loss": 596.482373046875, "train_metrics": {"mean_distance": 42.02799269732307, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 44.09004669189453, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0014614280056968586, "epoch_time": 0.5392029285430908, "avg_batch_time": 0.025024708579568303, "avg_gradient_norm": 384.6896854704558, "gradient_norm_std": 180.131785125575}, {"epoch": 9, "train_loss": 514.9444508272059, "val_loss": 555.8199890136718, "train_metrics": {"mean_distance": 40.72841958438649, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 42.40257263183594, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0015666307905721187, "epoch_time": 0.5359079837799072, "avg_batch_time": 0.02510502759148093, "avg_gradient_norm": 653.2843568542552, "gradient_norm_std": 390.8097112790131}, {"epoch": 10, "train_loss": 484.5235128963695, "val_loss": 480.8031494140625, "train_metrics": {"mean_distance": 39.34349351770737, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 39.02989807128906, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0015999983143806002, "epoch_time": 0.5279135704040527, "avg_batch_time": 0.02486836209016688, "avg_gradient_norm": 305.1046339138901, "gradient_norm_std": 64.66377023873238}, {"epoch": 11, "train_loss": 432.96713436351104, "val_loss": 479.12346801757815, "train_metrics": {"mean_distance": 36.86116319544175, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 38.88583908081055, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0015994539212903613, "epoch_time": 0.5238828659057617, "avg_batch_time": 0.0245967752793256, "avg_gradient_norm": 355.9022764475695, "gradient_norm_std": 162.61323136671925}, {"epoch": 12, "train_loss": 395.5219367532169, "val_loss": 417.61500244140626, "train_metrics": {"mean_distance": 34.89902765610639, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 35.87335968017578, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0015979360040818508, "epoch_time": 0.5212979316711426, "avg_batch_time": 0.024627377005184397, "avg_gradient_norm": 373.36891612373347, "gradient_norm_std": 117.71919906836467}, {"epoch": 13, "train_loss": 345.24178000057447, "val_loss": 331.3870361328125, "train_metrics": {"mean_distance": 32.24192136876724, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 31.302871704101562, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0015954464121033705, "epoch_time": 0.5318760871887207, "avg_batch_time": 0.024932510712567496, "avg_gradient_norm": 271.15592137748035, "gradient_norm_std": 76.12610837536371}, {"epoch": 14, "train_loss": 300.97762343462773, "val_loss": 270.4227233886719, "train_metrics": {"mean_distance": 29.59081470265108, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 27.658404541015624, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0015919881785392534, "epoch_time": 0.5153453350067139, "avg_batch_time": 0.02425997397478889, "avg_gradient_norm": 292.1480201206081, "gradient_norm_std": 60.406355317648185}, {"epoch": 15, "train_loss": 253.09896312040442, "val_loss": 284.36733093261716, "train_metrics": {"mean_distance": 26.691547505995807, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 28.447607803344727, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0015875655167143976, "epoch_time": 0.5561110973358154, "avg_batch_time": 0.026143789291381836, "avg_gradient_norm": 248.8258802477097, "gradient_norm_std": 151.48782479245028}, {"epoch": 16, "train_loss": 211.98680114746094, "val_loss": 194.63945617675782, "train_metrics": {"mean_distance": 23.845766067504883, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 22.716540145874024, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0015821838149609783, "epoch_time": 0.5243299007415771, "avg_batch_time": 0.024605610791374657, "avg_gradient_norm": 377.23710090681914, "gradient_norm_std": 252.32139210599337}, {"epoch": 17, "train_loss": 186.58955158906824, "val_loss": 210.75200805664062, "train_metrics": {"mean_distance": 21.995877209831686, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 23.63212432861328, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0015758496300535938, "epoch_time": 0.5229716300964355, "avg_batch_time": 0.02467215762418859, "avg_gradient_norm": 263.08436008280535, "gradient_norm_std": 122.00920120722532}, {"epoch": 18, "train_loss": 145.14034001967485, "val_loss": 169.9010009765625, "train_metrics": {"mean_distance": 18.870217379401712, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 20.868740844726563, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0015685706792208474, "epoch_time": 0.5504133701324463, "avg_batch_time": 0.02554632635677562, "avg_gradient_norm": 258.05692354116064, "gradient_norm_std": 249.893240094285}, {"epoch": 19, "train_loss": 120.28855357450597, "val_loss": 102.83538360595703, "train_metrics": {"mean_distance": 16.830474348629224, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 14.968876457214355, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0015603558307430928, "epoch_time": 0.5717518329620361, "avg_batch_time": 0.026997033287497127, "avg_gradient_norm": 322.5654837755531, "gradient_norm_std": 281.0027917262999}, {"epoch": 20, "train_loss": 105.38164004157571, "val_loss": 83.9905776977539, "train_metrics": {"mean_distance": 15.540756449979895, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 13.530379676818848, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0015512150931477993, "epoch_time": 0.5510032176971436, "avg_batch_time": 0.025909157360301298, "avg_gradient_norm": 259.62516366490905, "gradient_norm_std": 148.19132970174863}, {"epoch": 21, "train_loss": 95.37515348546646, "val_loss": 73.65599670410157, "train_metrics": {"mean_distance": 14.598413186914781, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 12.33755931854248, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0015411596030157045, "epoch_time": 0.5586907863616943, "avg_batch_time": 0.026411210789399987, "avg_gradient_norm": 199.68758190914542, "gradient_norm_std": 115.290952551402}, {"epoch": 22, "train_loss": 76.48044765696807, "val_loss": 108.5322250366211, "train_metrics": {"mean_distance": 12.953038271735696, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 14.627346611022949, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0015302016114126045, "epoch_time": 0.5640041828155518, "avg_batch_time": 0.02641913470099954, "avg_gradient_norm": 171.01823036285725, "gradient_norm_std": 190.17547151887308}, {"epoch": 23, "train_loss": 63.37818953570198, "val_loss": 53.243831634521484, "train_metrics": {"mean_distance": 11.82741008085363, "within_5mm_percent": 0.0, "within_7mm_percent": 1.4705882352941178}, "val_metrics": {"mean_distance": 10.274073791503906, "within_5mm_percent": 0.0, "within_7mm_percent": 15.0}, "learning_rate": 0.0015183544689633185, "epoch_time": 0.5455560684204102, "avg_batch_time": 0.02557217373567469, "avg_gradient_norm": 120.33394390706292, "gradient_norm_std": 70.10506340247157}, {"epoch": 24, "train_loss": 52.933032765107995, "val_loss": 54.55683479309082, "train_metrics": {"mean_distance": 10.701501285328584, "within_5mm_percent": 0.0, "within_7mm_percent": 4.411764705882353}, "val_metrics": {"mean_distance": 10.056498908996582, "within_5mm_percent": 0.0, "within_7mm_percent": 20.0}, "learning_rate": 0.0015056326095860086, "epoch_time": 0.532416820526123, "avg_batch_time": 0.02483262735254624, "avg_gradient_norm": 143.06631891925147, "gradient_norm_std": 163.33375349799996}, {"epoch": 25, "train_loss": 48.642052706550146, "val_loss": 68.61898727416992, "train_metrics": {"mean_distance": 10.389436048619887, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 8.823529411764707}, "val_metrics": {"mean_distance": 10.574845218658448, "within_5mm_percent": 0.0, "within_7mm_percent": 35.0}, "learning_rate": 0.0014920515329066742, "epoch_time": 0.52549147605896, "avg_batch_time": 0.024687963373520794, "avg_gradient_norm": 161.5591273086554, "gradient_norm_std": 161.50896617209494}, {"epoch": 26, "train_loss": 45.92980081894819, "val_loss": 39.57778549194336, "train_metrics": {"mean_distance": 10.288371703203987, "within_5mm_percent": 0.0, "within_7mm_percent": 8.823529411764707}, "val_metrics": {"mean_distance": 8.978647232055664, "within_5mm_percent": 5.0, "within_7mm_percent": 35.0}, "learning_rate": 0.001477627785375244, "epoch_time": 0.5267207622528076, "avg_batch_time": 0.024822866215425378, "avg_gradient_norm": 144.25883300010716, "gradient_norm_std": 95.13877056822355}, {"epoch": 27, "train_loss": 41.69870174632353, "val_loss": 58.23286399841309, "train_metrics": {"mean_distance": 10.096141057855943, "within_5mm_percent": 0.0, "within_7mm_percent": 11.764705882352942}, "val_metrics": {"mean_distance": 9.857360649108887, "within_5mm_percent": 0.0, "within_7mm_percent": 35.0}, "learning_rate": 0.0014623789401062774, "epoch_time": 0.5488600730895996, "avg_batch_time": 0.025591008803423715, "avg_gradient_norm": 123.54092128349157, "gradient_norm_std": 104.08324545580075}, {"epoch": 28, "train_loss": 41.73340528151568, "val_loss": 49.30961952209473, "train_metrics": {"mean_distance": 9.965320727404427, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 19.11764705882353}, "val_metrics": {"mean_distance": 10.190374851226807, "within_5mm_percent": 0.0, "within_7mm_percent": 40.0}, "learning_rate": 0.00144632357546883, "epoch_time": 0.5238339900970459, "avg_batch_time": 0.024671161876005286, "avg_gradient_norm": 128.46893268794702, "gradient_norm_std": 120.19103801982526}, {"epoch": 29, "train_loss": 35.94666638093836, "val_loss": 83.58614845275879, "train_metrics": {"mean_distance": 9.418791827033548, "within_5mm_percent": 0.0, "within_7mm_percent": 20.58823529411765}, "val_metrics": {"mean_distance": 10.691886711120606, "within_5mm_percent": 5.0, "within_7mm_percent": 35.0}, "learning_rate": 0.0014294812524515733, "epoch_time": 0.5673553943634033, "avg_batch_time": 0.02654434652889476, "avg_gradient_norm": 179.31389583634856, "gradient_norm_std": 244.39574488305476}, {"epoch": 30, "train_loss": 31.628134895773496, "val_loss": 67.21826629638672, "train_metrics": {"mean_distance": 8.985800069921156, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 16.176470588235293}, "val_metrics": {"mean_distance": 10.813373947143555, "within_5mm_percent": 15.0, "within_7mm_percent": 35.0}, "learning_rate": 0.0014118724908307426, "epoch_time": 0.5298006534576416, "avg_batch_time": 0.024879708009607652, "avg_gradient_norm": 76.31804357350539, "gradient_norm_std": 62.169100315593724}, {"epoch": 31, "train_loss": 36.75959957347197, "val_loss": 99.99304504394532, "train_metrics": {"mean_distance": 9.7669677734375, "within_5mm_percent": 0.0, "within_7mm_percent": 16.176470588235293}, "val_metrics": {"mean_distance": 11.396531200408935, "within_5mm_percent": 5.0, "within_7mm_percent": 35.0}, "learning_rate": 0.0013935187441699516, "epoch_time": 0.5286464691162109, "avg_batch_time": 0.024908963371725643, "avg_gradient_norm": 142.486829643881, "gradient_norm_std": 227.6943092401083}, {"epoch": 32, "train_loss": 32.437910977531885, "val_loss": 171.4830551147461, "train_metrics": {"mean_distance": 9.32222287795123, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 22.058823529411764}, "val_metrics": {"mean_distance": 13.665163326263428, "within_5mm_percent": 10.0, "within_7mm_percent": 25.0}, "learning_rate": 0.001374442373682328, "epoch_time": 0.5249693393707275, "avg_batch_time": 0.024698706234202665, "avg_gradient_norm": 103.72764814644086, "gradient_norm_std": 101.1093712330897}, {"epoch": 33, "train_loss": 30.872504402609433, "val_loss": 49.25848808288574, "train_metrics": {"mean_distance": 9.01388687245986, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 33.8235294117647}, "val_metrics": {"mean_distance": 9.237497711181641, "within_5mm_percent": 20.0, "within_7mm_percent": 40.0}, "learning_rate": 0.0013546666209868215, "epoch_time": 0.5254487991333008, "avg_batch_time": 0.024657754337086397, "avg_gradient_norm": 102.19361279507984, "gradient_norm_std": 73.56047510195421}, {"epoch": 34, "train_loss": 30.9918732362635, "val_loss": 42.59203987121582, "train_metrics": {"mean_distance": 9.149340180789723, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 20.58823529411765}, "val_metrics": {"mean_distance": 9.336031913757324, "within_5mm_percent": 5.0, "within_7mm_percent": 35.0}, "learning_rate": 0.0013342155797918703, "epoch_time": 0.524038553237915, "avg_batch_time": 0.024665145313038546, "avg_gradient_norm": 113.35105023578522, "gradient_norm_std": 104.69142584030486}, {"epoch": 35, "train_loss": 30.719402762020337, "val_loss": 32.71208229064941, "train_metrics": {"mean_distance": 9.017501746906953, "within_5mm_percent": 0.0, "within_7mm_percent": 22.058823529411764}, "val_metrics": {"mean_distance": 8.858813858032226, "within_5mm_percent": 15.0, "within_7mm_percent": 35.0}, "learning_rate": 0.0013131141665409293, "epoch_time": 0.5306215286254883, "avg_batch_time": 0.024901558371151194, "avg_gradient_norm": 78.32650026792162, "gradient_norm_std": 45.32694438958963}, {"epoch": 36, "train_loss": 28.32321772855871, "val_loss": 31.21744155883789, "train_metrics": {"mean_distance": 8.728397481581744, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 26.470588235294116}, "val_metrics": {"mean_distance": 8.53800573348999, "within_5mm_percent": 10.0, "within_7mm_percent": 45.0}, "learning_rate": 0.0012913880900556212, "epoch_time": 0.527977705001831, "avg_batch_time": 0.024723123101627126, "avg_gradient_norm": 75.34772308766091, "gradient_norm_std": 37.83019350681219}, {"epoch": 37, "train_loss": 29.60847820955164, "val_loss": 36.632137870788576, "train_metrics": {"mean_distance": 9.03302806966445, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 27.941176470588236}, "val_metrics": {"mean_distance": 8.658934402465821, "within_5mm_percent": 15.0, "within_7mm_percent": 35.0}, "learning_rate": 0.0012690638202134978, "epoch_time": 0.5372910499572754, "avg_batch_time": 0.025248008615830365, "avg_gradient_norm": 84.02524591570155, "gradient_norm_std": 55.15572326023106}, {"epoch": 38, "train_loss": 30.035740852355957, "val_loss": 92.51634922027588, "train_metrics": {"mean_distance": 9.10352886424345, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 26.470588235294116}, "val_metrics": {"mean_distance": 10.645439434051514, "within_5mm_percent": 15.0, "within_7mm_percent": 35.0}, "learning_rate": 0.0012461685556985713, "epoch_time": 0.5337693691253662, "avg_batch_time": 0.02513616225298713, "avg_gradient_norm": 84.30269971631374, "gradient_norm_std": 49.72452658805387}, {"epoch": 39, "train_loss": 32.75605594410616, "val_loss": 92.5695255279541, "train_metrics": {"mean_distance": 9.481632429010729, "within_5mm_percent": 0.0, "within_7mm_percent": 23.529411764705884}, "val_metrics": {"mean_distance": 10.868459129333496, "within_5mm_percent": 5.0, "within_7mm_percent": 45.0}, "learning_rate": 0.0012227301908639082, "epoch_time": 0.5219593048095703, "avg_batch_time": 0.024611823699053598, "avg_gradient_norm": 471.3938897474982, "gradient_norm_std": 1325.8322804371646}, {"epoch": 40, "train_loss": 29.957376928890454, "val_loss": 33.64165344238281, "train_metrics": {"mean_distance": 8.994322187760297, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 25.0}, "val_metrics": {"mean_distance": 8.661225509643554, "within_5mm_percent": 15.0, "within_7mm_percent": 50.0}, "learning_rate": 0.0011987772817466566, "epoch_time": 0.5275864601135254, "avg_batch_time": 0.024793751099530387, "avg_gradient_norm": 75.19043989695218, "gradient_norm_std": 40.06212977553669}, {"epoch": 41, "train_loss": 28.808555210337918, "val_loss": 48.96487293243408, "train_metrics": {"mean_distance": 8.836727619171143, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 23.529411764705884}, "val_metrics": {"mean_distance": 9.50881519317627, "within_5mm_percent": 20.0, "within_7mm_percent": 45.0}, "learning_rate": 0.0011743390112769125, "epoch_time": 0.5201945304870605, "avg_batch_time": 0.02454414087183335, "avg_gradient_norm": 79.17047226050214, "gradient_norm_std": 42.33539627403915}, {"epoch": 42, "train_loss": 26.997911789838007, "val_loss": 36.08110008239746, "train_metrics": {"mean_distance": 8.47898640352137, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 35.294117647058826}, "val_metrics": {"mean_distance": 8.711649322509766, "within_5mm_percent": 20.0, "within_7mm_percent": 50.0}, "learning_rate": 0.0011494451537228164, "epoch_time": 0.5295205116271973, "avg_batch_time": 0.024703460581162396, "avg_gradient_norm": 54.52038146278483, "gradient_norm_std": 21.095377085065422}, {"epoch": 43, "train_loss": 25.75480337703929, "val_loss": 44.29079742431641, "train_metrics": {"mean_distance": 8.479687634636374, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 35.294117647058826}, "val_metrics": {"mean_distance": 8.768980979919434, "within_5mm_percent": 20.0, "within_7mm_percent": 50.0}, "learning_rate": 0.0011241260384151934, "epoch_time": 0.5510907173156738, "avg_batch_time": 0.02614306001102223, "avg_gradient_norm": 56.51924211623697, "gradient_norm_std": 31.129631654145527}, {"epoch": 44, "train_loss": 25.00118429520551, "val_loss": 38.82220859527588, "train_metrics": {"mean_distance": 8.37256627924302, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 32.35294117647059}, "val_metrics": {"mean_distance": 9.343464756011963, "within_5mm_percent": 10.0, "within_7mm_percent": 40.0}, "learning_rate": 0.0010984125127959348, "epoch_time": 0.555997371673584, "avg_batch_time": 0.026406091802260456, "avg_gradient_norm": 54.35043925872292, "gradient_norm_std": 26.108561862531378}, {"epoch": 45, "train_loss": 29.474767460542566, "val_loss": 36.188344383239745, "train_metrics": {"mean_distance": 8.938651758081773, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 25.0}, "val_metrics": {"mean_distance": 9.360232639312745, "within_5mm_percent": 10.0, "within_7mm_percent": 35.0}, "learning_rate": 0.0010723359048351432, "epoch_time": 0.5221998691558838, "avg_batch_time": 0.02453916213091682, "avg_gradient_norm": 81.42133665589662, "gradient_norm_std": 81.70992161128267}, {"epoch": 46, "train_loss": 26.506776529199936, "val_loss": 27.76139602661133, "train_metrics": {"mean_distance": 8.448298734777113, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 30.88235294117647}, "val_metrics": {"mean_distance": 8.414722442626953, "within_5mm_percent": 10.0, "within_7mm_percent": 35.0}, "learning_rate": 0.0010459279848628246, "epoch_time": 0.5256757736206055, "avg_batch_time": 0.02471491869758157, "avg_gradient_norm": 63.14887779138347, "gradient_norm_std": 83.79951927242956}, {"epoch": 47, "train_loss": 26.870413668015424, "val_loss": 33.2436487197876, "train_metrics": {"mean_distance": 8.586806970484117, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 26.470588235294116}, "val_metrics": {"mean_distance": 8.782428169250489, "within_5mm_percent": 20.0, "within_7mm_percent": 40.0}, "learning_rate": 0.0010192209268616377, "epoch_time": 0.524163007736206, "avg_batch_time": 0.024507957346299115, "avg_gradient_norm": 81.9257172246, "gradient_norm_std": 50.290435444364796}, {"epoch": 48, "train_loss": 24.611642276539524, "val_loss": 28.180837059020995, "train_metrics": {"mean_distance": 8.337448176215677, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 30.88235294117647}, "val_metrics": {"mean_distance": 8.472080039978028, "within_5mm_percent": 15.0, "within_7mm_percent": 40.0}, "learning_rate": 0.0009922472692678495, "epoch_time": 0.5453803539276123, "avg_batch_time": 0.025689910439883962, "avg_gradient_norm": 66.10665705058946, "gradient_norm_std": 25.426980360633934}, {"epoch": 49, "train_loss": 30.75280621472527, "val_loss": 29.872745704650878, "train_metrics": {"mean_distance": 9.234128531287698, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 19.11764705882353}, "val_metrics": {"mean_distance": 8.714791297912598, "within_5mm_percent": 5.0, "within_7mm_percent": 40.0}, "learning_rate": 0.0009650398753282647, "epoch_time": 0.5232827663421631, "avg_batch_time": 0.02459105323342716, "avg_gradient_norm": 218.07895115868533, "gradient_norm_std": 440.29596473979194}, {"epoch": 50, "train_loss": 31.980248170740463, "val_loss": 31.67571105957031, "train_metrics": {"mean_distance": 9.454631637124454, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 17.647058823529413}, "val_metrics": {"mean_distance": 8.859328269958496, "within_5mm_percent": 5.0, "within_7mm_percent": 40.0}, "learning_rate": 0.0009376318930614213, "epoch_time": 0.5540421009063721, "avg_batch_time": 0.026016922558055204, "avg_gradient_norm": 283.81945411601083, "gradient_norm_std": 548.0966552321793}, {"epoch": 51, "train_loss": 32.13412991692038, "val_loss": 26.352131843566895, "train_metrics": {"mean_distance": 9.423444635727826, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 20.58823529411765}, "val_metrics": {"mean_distance": 7.978360271453857, "within_5mm_percent": 15.0, "within_7mm_percent": 40.0}, "learning_rate": 0.0009100567148718352, "epoch_time": 0.5243704319000244, "avg_batch_time": 0.02459265204036937, "avg_gradient_norm": 141.33158601885273, "gradient_norm_std": 159.16902275912483}, {"epoch": 52, "train_loss": 34.28057760350845, "val_loss": 41.17502002716064, "train_metrics": {"mean_distance": 9.739959043615004, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 13.235294117647058}, "val_metrics": {"mean_distance": 9.355626010894776, "within_5mm_percent": 10.0, "within_7mm_percent": 40.0}, "learning_rate": 0.0008823479368664996, "epoch_time": 0.5215797424316406, "avg_batch_time": 0.024478505639468923, "avg_gradient_norm": 108.3106508421934, "gradient_norm_std": 90.15688953036451}, {"epoch": 53, "train_loss": 33.44290553822237, "val_loss": 27.9086483001709, "train_metrics": {"mean_distance": 9.542518335230211, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 22.058823529411764}, "val_metrics": {"mean_distance": 8.2763277053833, "within_5mm_percent": 20.0, "within_7mm_percent": 50.0}, "learning_rate": 0.0008545393179232042, "epoch_time": 0.5245802402496338, "avg_batch_time": 0.02458798184114344, "avg_gradient_norm": 407.89137768114506, "gradient_norm_std": 1068.917856197466}, {"epoch": 54, "train_loss": 34.24247696820427, "val_loss": 29.334613037109374, "train_metrics": {"mean_distance": 9.563184233272777, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 23.529411764705884}, "val_metrics": {"mean_distance": 8.420331478118896, "within_5mm_percent": 20.0, "within_7mm_percent": 40.0}, "learning_rate": 0.0008266647385605421, "epoch_time": 0.524944543838501, "avg_batch_time": 0.0245512233060949, "avg_gradient_norm": 131.88472597424115, "gradient_norm_std": 204.90736479763083}, {"epoch": 55, "train_loss": 29.930494476767148, "val_loss": 45.33567581176758, "train_metrics": {"mean_distance": 9.082790599149817, "within_5mm_percent": 0.0, "within_7mm_percent": 30.88235294117647}, "val_metrics": {"mean_distance": 10.13100357055664, "within_5mm_percent": 5.0, "within_7mm_percent": 20.0}, "learning_rate": 0.0007987581596597167, "epoch_time": 0.5342757701873779, "avg_batch_time": 0.025075561860028434, "avg_gradient_norm": 186.30414667189314, "gradient_norm_std": 177.7044212480644}, {"epoch": 56, "train_loss": 26.902432610006894, "val_loss": 54.476182746887204, "train_metrics": {"mean_distance": 8.529429407680736, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 41.1764705882353}, "val_metrics": {"mean_distance": 10.166904067993164, "within_5mm_percent": 5.0, "within_7mm_percent": 35.0}, "learning_rate": 0.0007708535810884399, "epoch_time": 0.5255310535430908, "avg_batch_time": 0.024567197350894705, "avg_gradient_norm": 198.98300872767123, "gradient_norm_std": 515.398738598883}, {"epoch": 57, "train_loss": 33.546866697423596, "val_loss": 75.9115982055664, "train_metrics": {"mean_distance": 9.447702912723317, "within_5mm_percent": 0.0, "within_7mm_percent": 27.941176470588236}, "val_metrics": {"mean_distance": 10.300859260559083, "within_5mm_percent": 15.0, "within_7mm_percent": 40.0}, "learning_rate": 0.0007429850002773296, "epoch_time": 0.52101731300354, "avg_batch_time": 0.024452798506792855, "avg_gradient_norm": 571.2570399042672, "gradient_norm_std": 1183.4012105327797}, {"epoch": 58, "train_loss": 27.675458178800696, "val_loss": 69.27501010894775, "train_metrics": {"mean_distance": 8.750394428477568, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 35.294117647058826}, "val_metrics": {"mean_distance": 10.54871187210083, "within_5mm_percent": 20.0, "within_7mm_percent": 35.0}, "learning_rate": 0.0007151863707992791, "epoch_time": 0.5384891033172607, "avg_batch_time": 0.025097538443172678, "avg_gradient_norm": 272.67763927795704, "gradient_norm_std": 698.3090089291687}, {"epoch": 59, "train_loss": 30.401686780592975, "val_loss": 56.98813095092773, "train_metrics": {"mean_distance": 9.109193914076862, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 29.41176470588235}, "val_metrics": {"mean_distance": 10.056581687927245, "within_5mm_percent": 15.0, "within_7mm_percent": 35.0}, "learning_rate": 0.0006874915610022564, "epoch_time": 0.5328385829925537, "avg_batch_time": 0.024695915334364948, "avg_gradient_norm": 218.26369619497788, "gradient_norm_std": 432.3289950526896}, {"epoch": 60, "train_loss": 29.149808098288144, "val_loss": 57.659108352661136, "train_metrics": {"mean_distance": 8.991850909064798, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 27.941176470588236}, "val_metrics": {"mean_distance": 9.934020805358887, "within_5mm_percent": 10.0, "within_7mm_percent": 40.0}, "learning_rate": 0.0006599343127459402, "epoch_time": 0.5177228450775146, "avg_batch_time": 0.024387415717629826, "avg_gradient_norm": 87.21816158076815, "gradient_norm_std": 79.8888505468051}, {"epoch": 61, "train_loss": 31.548636885250318, "val_loss": 41.168650245666505, "train_metrics": {"mean_distance": 9.270969951854033, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 32.35294117647059}, "val_metrics": {"mean_distance": 8.980710887908936, "within_5mm_percent": 20.0, "within_7mm_percent": 40.0}, "learning_rate": 0.0006325482002924631, "epoch_time": 0.5357544422149658, "avg_batch_time": 0.02498915616203757, "avg_gradient_norm": 75.9796690767074, "gradient_norm_std": 43.66046033053097}, {"epoch": 62, "train_loss": 26.374610788681927, "val_loss": 37.739876174926756, "train_metrics": {"mean_distance": 8.466621286728802, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 30.88235294117647}, "val_metrics": {"mean_distance": 9.01049575805664, "within_5mm_percent": 15.0, "within_7mm_percent": 35.0}, "learning_rate": 0.0006053665894013416, "epoch_time": 0.5287222862243652, "avg_batch_time": 0.02491607385523179, "avg_gradient_norm": 66.54134798205908, "gradient_norm_std": 55.77050931682561}, {"epoch": 63, "train_loss": 32.40806203729966, "val_loss": 44.95929775238037, "train_metrics": {"mean_distance": 9.351511085734648, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 23.529411764705884}, "val_metrics": {"mean_distance": 9.501998043060302, "within_5mm_percent": 20.0, "within_7mm_percent": 30.0}, "learning_rate": 0.000578422596678441, "epoch_time": 0.5237488746643066, "avg_batch_time": 0.02455883867600385, "avg_gradient_norm": 108.87032302419139, "gradient_norm_std": 83.76296316095585}, {"epoch": 64, "train_loss": 28.528543752782486, "val_loss": 41.15164623260498, "train_metrics": {"mean_distance": 8.888062897850485, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 26.470588235294116}, "val_metrics": {"mean_distance": 8.990232563018798, "within_5mm_percent": 20.0, "within_7mm_percent": 40.0}, "learning_rate": 0.00055174904922849, "epoch_time": 0.551469087600708, "avg_batch_time": 0.025568036472096163, "avg_gradient_norm": 109.76383603953494, "gradient_norm_std": 158.67139435701984}, {"epoch": 65, "train_loss": 33.39916139490464, "val_loss": 38.32558326721191, "train_metrics": {"mean_distance": 9.67104690215167, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 23.529411764705884}, "val_metrics": {"mean_distance": 8.948682308197021, "within_5mm_percent": 15.0, "within_7mm_percent": 40.0}, "learning_rate": 0.000525378444660311, "epoch_time": 0.5622968673706055, "avg_batch_time": 0.026314595166374657, "avg_gradient_norm": 160.93942380838942, "gradient_norm_std": 160.24607491654595}, {"epoch": 66, "train_loss": 27.938435386208926, "val_loss": 53.23749713897705, "train_metrics": {"mean_distance": 8.826925558202406, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 29.41176470588235}, "val_metrics": {"mean_distance": 10.296331024169922, "within_5mm_percent": 15.0, "within_7mm_percent": 25.0}, "learning_rate": 0.0004993429114934909, "epoch_time": 0.4913330078125, "avg_batch_time": 0.022901689305024987, "avg_gradient_norm": 85.33075965133699, "gradient_norm_std": 60.911988449846035}, {"epoch": 67, "train_loss": 29.27515074786018, "val_loss": 48.59017772674561, "train_metrics": {"mean_distance": 8.951206712161794, "within_5mm_percent": 0.0, "within_7mm_percent": 27.941176470588236}, "val_metrics": {"mean_distance": 10.213173961639404, "within_5mm_percent": 20.0, "within_7mm_percent": 30.0}, "learning_rate": 0.00047367417001472614, "epoch_time": 0.49158692359924316, "avg_batch_time": 0.02283913948956658, "avg_gradient_norm": 149.61101157960866, "gradient_norm_std": 220.0259007208173}, {"epoch": 68, "train_loss": 27.887846441829907, "val_loss": 59.1688835144043, "train_metrics": {"mean_distance": 8.683870343601003, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 36.76470588235294}, "val_metrics": {"mean_distance": 9.494951820373535, "within_5mm_percent": 25.0, "within_7mm_percent": 50.0}, "learning_rate": 0.00044840349363154265, "epoch_time": 0.4936959743499756, "avg_batch_time": 0.02281636350295123, "avg_gradient_norm": 172.77004577270154, "gradient_norm_std": 235.98938602526934}, {"epoch": 69, "train_loss": 27.95833183737362, "val_loss": 31.261708641052245, "train_metrics": {"mean_distance": 8.731036775252399, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 35.294117647058826}, "val_metrics": {"mean_distance": 8.432703971862793, "within_5mm_percent": 15.0, "within_7mm_percent": 50.0}, "learning_rate": 0.0004235616707704638, "epoch_time": 0.547069787979126, "avg_batch_time": 0.02556935478659237, "avg_gradient_norm": 122.32709575597313, "gradient_norm_std": 124.37680542133535}, {"epoch": 70, "train_loss": 29.74445567411535, "val_loss": 39.209251403808594, "train_metrics": {"mean_distance": 9.063053103054271, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 26.470588235294116}, "val_metrics": {"mean_distance": 8.891169452667237, "within_5mm_percent": 10.0, "within_7mm_percent": 50.0}, "learning_rate": 0.00039917896736605676, "epoch_time": 0.5940337181091309, "avg_batch_time": 0.027286725885727826, "avg_gradient_norm": 87.02105064614271, "gradient_norm_std": 73.19948544422282}, {"epoch": 71, "train_loss": 29.666701429030475, "val_loss": 73.0043794631958, "train_metrics": {"mean_distance": 9.076842448290657, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 20.58823529411765}, "val_metrics": {"mean_distance": 10.160272216796875, "within_5mm_percent": 15.0, "within_7mm_percent": 50.0}, "learning_rate": 0.00037528508998655125, "epoch_time": 0.5581412315368652, "avg_batch_time": 0.02579355239868164, "avg_gradient_norm": 411.1510050621396, "gradient_norm_std": 1178.1695396276964}, {"epoch": 72, "train_loss": 32.51132106781006, "val_loss": 73.27234268188477, "train_metrics": {"mean_distance": 9.502334202037138, "within_5mm_percent": 0.0, "within_7mm_percent": 19.11764705882353}, "val_metrics": {"mean_distance": 10.1635910987854, "within_5mm_percent": 10.0, "within_7mm_percent": 50.0}, "learning_rate": 0.0003519091496409659, "epoch_time": 0.561901330947876, "avg_batch_time": 0.02595677095301011, "avg_gradient_norm": 138.1591854274909, "gradient_norm_std": 168.94534704843414}, {"epoch": 73, "train_loss": 30.537978789385626, "val_loss": 151.43311157226563, "train_metrics": {"mean_distance": 9.159053493948544, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 20.58823529411765}, "val_metrics": {"mean_distance": 12.719176292419434, "within_5mm_percent": 10.0, "within_7mm_percent": 40.0}, "learning_rate": 0.00032907962631182323, "epoch_time": 0.5156776905059814, "avg_batch_time": 0.02423555710736443, "avg_gradient_norm": 108.75026479174117, "gradient_norm_std": 102.13697544247998}, {"epoch": 74, "train_loss": 24.118419815512265, "val_loss": 58.1470344543457, "train_metrics": {"mean_distance": 8.250502866857191, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 26.470588235294116}, "val_metrics": {"mean_distance": 10.33273286819458, "within_5mm_percent": 5.0, "within_7mm_percent": 45.0}, "learning_rate": 0.0003068243342566817, "epoch_time": 0.5202786922454834, "avg_batch_time": 0.024628709344302908, "avg_gradient_norm": 68.88192824494328, "gradient_norm_std": 56.9891131037665}, {"epoch": 75, "train_loss": 23.927980086382696, "val_loss": 104.9006814956665, "train_metrics": {"mean_distance": 8.145962098065544, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 33.8235294117647}, "val_metrics": {"mean_distance": 11.917644119262695, "within_5mm_percent": 5.0, "within_7mm_percent": 40.0}, "learning_rate": 0.0002851703881207455, "epoch_time": 0.5226802825927734, "avg_batch_time": 0.02476044262156767, "avg_gradient_norm": 92.46933630563801, "gradient_norm_std": 93.06705909028341}, {"epoch": 76, "train_loss": 26.480528607087976, "val_loss": 61.52136459350586, "train_metrics": {"mean_distance": 8.59087604634902, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 30.88235294117647}, "val_metrics": {"mean_distance": 10.273417091369629, "within_5mm_percent": 5.0, "within_7mm_percent": 45.0}, "learning_rate": 0.00026414416990184656, "epoch_time": 0.5219571590423584, "avg_batch_time": 0.024536553551169002, "avg_gradient_norm": 67.97850366736255, "gradient_norm_std": 35.169291753949125}], "training_stats": {"dataset_creation_time": 243.15972018241882, "total_epochs": 76, "avg_epoch_time": 0.541315430089047, "avg_batch_time": 0.025452598883271588, "avg_gradient_norm": 211.71511647027907, "gradient_norm_std": 380.8240756509657, "final_learning_rate": 0.00026414416990184656}}