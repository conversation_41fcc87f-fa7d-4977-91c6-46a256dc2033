#!/usr/bin/env python3
"""
保守的损失函数改进 - 从简单的Wing Loss开始
基于12关键点成功配置，渐进式改进
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class SimpleWingLoss(nn.Module):
    """
    简化的Wing Loss - 专门为小误差优化
    """
    
    def __init__(self, omega=10.0, epsilon=2.0):
        super(SimpleWingLoss, self).__init__()
        self.omega = omega
        self.epsilon = epsilon
        self.C = self.omega - self.omega * np.log(1 + self.omega / self.epsilon)
    
    def forward(self, pred, target):
        diff = torch.abs(pred - target)
        
        # 小误差区域使用log函数
        small_error_mask = diff < self.omega
        small_error_loss = self.omega * torch.log(1 + diff[small_error_mask] / self.epsilon)
        
        # 大误差区域使用线性函数
        large_error_loss = diff[~small_error_mask] - self.C
        
        # 合并损失
        loss = torch.zeros_like(diff)
        loss[small_error_mask] = small_error_loss
        loss[~small_error_mask] = large_error_loss
        
        return loss.mean()

class ConservativeImprovedLoss(nn.Module):
    """
    保守的改进损失函数 - Wing Loss + MSE Loss
    """
    
    def __init__(self, wing_weight=0.7, mse_weight=0.3, omega=8.0, epsilon=1.5):
        super(ConservativeImprovedLoss, self).__init__()
        self.wing_weight = wing_weight
        self.mse_weight = mse_weight
        self.wing_loss = SimpleWingLoss(omega=omega, epsilon=epsilon)
        
        print(f"🎯 保守改进损失函数:")
        print(f"   Wing Loss权重: {wing_weight} (omega={omega}, epsilon={epsilon})")
        print(f"   MSE Loss权重: {mse_weight}")
    
    def forward(self, pred, target):
        wing_loss_val = self.wing_loss(pred, target)
        mse_loss_val = F.mse_loss(pred, target)
        
        total_loss = self.wing_weight * wing_loss_val + self.mse_weight * mse_loss_val
        
        return total_loss, {
            'wing_loss': wing_loss_val,
            'mse_loss': mse_loss_val,
            'total_loss': total_loss
        }

class ReducedKeypointsF3Dataset(Dataset):
    """12关键点F3数据集"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points
        self.augment = augment
        self.split = split
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
    
    def __len__(self):
        return len(self.sample_ids)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        # 保守的数据增强
        if self.augment and self.split == 'train':
            if np.random.random() < 0.6:
                angle = np.random.uniform(-0.05, 0.05)
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                point_cloud = point_cloud @ rotation.T
                keypoints = keypoints @ rotation.T
            
            if np.random.random() < 0.5:
                translation = np.random.uniform(-0.2, 0.2, 3)
                point_cloud += translation
                keypoints += translation
            
            if np.random.random() < 0.4:
                scale = np.random.uniform(0.995, 1.005, 3)
                point_cloud *= scale
                keypoints *= scale
            
            if np.random.random() < 0.5:
                noise_level = np.random.choice([0.01, 0.015, 0.02])
                noise = np.random.normal(0, noise_level, point_cloud.shape)
                point_cloud += noise
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

class AdaptivePointNet(nn.Module):
    """自适应关键点数量的PointNet"""
    
    def __init__(self, num_keypoints: int):
        super(AdaptivePointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        x1 = torch.relu(self.bn1(self.conv1(x)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        
        x3_res = x3 + self.residual1(x1)
        
        x4 = torch.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = torch.relu(self.bn5(self.conv5(x4_res)))
        
        global_feat = torch.max(x5, 2)[0]
        
        x = torch.relu(self.bn_fc1(self.fc1(global_feat)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc2(self.fc2(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc3(self.fc3(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc4(self.fc4(x)))
        x = self.dropout(x)
        x = self.fc5(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def train_conservative_improved():
    """保守的改进训练"""
    
    print(f"🚀 **保守改进训练 - Wing Loss优化**")
    print(f"🎯 **基础**: 12关键点成功配置 (6.208mm)")
    print(f"📈 **目标**: 保守改进，稳步提升")
    print(f"🔧 **改进**: Wing Loss + MSE Loss (简单组合)")
    print("=" * 80)
    
    set_seed(42)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 数据集
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    train_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'train', 
                                            num_points=4096, test_samples=test_samples, 
                                            augment=True, seed=42)
    val_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'val', 
                                          num_points=4096, test_samples=test_samples, 
                                          augment=False, seed=42)
    
    batch_size = 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
    
    # 模型
    model = AdaptivePointNet(num_keypoints=12).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 模型参数: {total_params:,}")
    
    # 保守的改进损失函数
    criterion = ConservativeImprovedLoss(
        wing_weight=0.7,
        mse_weight=0.3,
        omega=8.0,
        epsilon=1.5
    )
    
    # 优化器
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
    
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=12, min_lr=1e-6
    )
    
    num_epochs = 150
    best_val_error = float('inf')
    patience = 20
    patience_counter = 0
    history = []
    min_delta = 0.005
    
    print(f"🎯 训练配置: {num_epochs}轮, 耐心{patience}, 最小改进{min_delta}mm")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 40)
        
        # 训练
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        for batch in train_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)
                loss, loss_components = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                        
            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue
        
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)
                    loss, loss_components = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # 学习率调度
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 打印结果
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"5mm={train_metrics['within_5mm_percent']:.1f}%, 7mm={train_metrics['within_7mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"5mm={val_metrics['within_5mm_percent']:.1f}%, 7mm={val_metrics['within_7mm_percent']:.1f}%")
        print(f"学习率: {current_lr:.2e}")
        
        # 保存历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'learning_rate': current_lr
        })
        
        # 检查改进
        current_error = val_metrics['mean_distance']
        improvement = best_val_error - current_error
        
        if improvement > min_delta:
            best_val_error = current_error
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics,
                'config': 'conservative_wing_loss_12kp'
            }, f'best_conservative_wing_12kp_{best_val_error:.3f}mm.pth')
            
            print(f"🎉 新最佳! 验证误差: {best_val_error:.3f}mm (改进{improvement:.3f}mm)")
            
            if best_val_error <= 5.0:
                print(f"🏆 **突破5mm医疗级目标!**")
            elif best_val_error < 6.0:
                print(f"🎯 **突破6mm目标!** 向医疗级精度迈进")
            elif best_val_error < 6.208:
                print(f"✅ **超越基线!** Wing Loss改进有效")
        else:
            patience_counter += 1
            print(f"⏳ 无显著改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停触发")
            break
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    
    # 保存结果
    results = {
        'method': 'Conservative Wing Loss Improvement',
        'baseline_error': 6.208,
        'best_val_error': float(best_val_error),
        'improvement': float((6.208 - best_val_error) / 6.208 * 100),
        'training_time_minutes': float(total_time / 60),
        'epochs_trained': len(history),
        'history': history
    }
    
    with open('conservative_wing_loss_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 **保守改进完成!**")
    print(f"📊 基线误差: 6.208mm")
    print(f"🎯 最佳误差: {best_val_error:.3f}mm")
    print(f"📈 改进幅度: {(6.208 - best_val_error) / 6.208 * 100:.1f}%")
    print(f"⏱️  训练时间: {total_time/60:.1f}分钟")
    
    return best_val_error, results

if __name__ == "__main__":
    set_seed(42)
    best_error, results = train_conservative_improved()
