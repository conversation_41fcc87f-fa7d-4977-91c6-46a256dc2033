#!/usr/bin/env python3
"""
渐进式扩展实施（修复版）
Progressive Scaling Implementation (Fixed)
修复变量作用域问题，从12点开始逐步扩展
"""

import sys
import os
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import random
from sklearn.model_selection import train_test_split

# 添加原始代码路径
sys.path.insert(0, os.path.abspath("archive/old_scripts"))

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

# 导入原始模块
from ensemble_double_softmax_exact import ExactEnsembleDoubleSoftMaxPointNet

class ProgressiveDataset(Dataset):
    """渐进式数据集"""
    
    def __init__(self, point_clouds, keypoints_57, keypoint_indices):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints_57[:, keypoint_indices, :])
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

class Historical12Dataset(Dataset):
    """历史12点数据集"""
    
    def __init__(self, point_clouds, keypoints_12):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints_12)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def get_keypoint_subsets():
    """获取渐进式关键点子集"""
    
    subsets = {
        12: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17],  # 历史12点
        15: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17, 0, 1, 12],  # 增加3个F1点
        19: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17, 0, 1, 12, 13, 14, 15, 18],  # 完整F3
        24: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17, 0, 1, 12, 13, 14, 15, 18, 19, 20, 21, 22, 23],  # 增加F2
    }
    
    return subsets

def train_progressive_model(model, train_loader, val_loader, epochs=80, device='cuda', 
                          target_error=None, model_name="progressive"):
    """训练渐进式模型"""
    
    print(f"🚀 训练{model.num_keypoints}点模型...")
    if target_error:
        print(f"   目标误差: {target_error}mm")
    
    model = model.to(device)
    
    # 学习率配置
    learning_rate = 0.0008
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=15, min_lr=1e-6
    )
    
    criterion = nn.MSELoss()
    
    best_val_error = float('inf')
    patience_counter = 0
    patience = 25
    
    print(f"   学习率: {learning_rate:.2e}")
    print(f"   批次大小: {train_loader.batch_size}")
    
    start_time = time.time()
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            loss.backward()
            
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                val_loss += loss.item()
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 早停检查
        if val_error < best_val_error:
            best_val_error = val_error
            patience_counter = 0
            torch.save(model.state_dict(), f'best_{model_name}_{model.num_keypoints}point.pth')
        else:
            patience_counter += 1
        
        # 打印进度
        if epoch % 10 == 0 or epoch < 5:
            print(f"Epoch {epoch+1:3d}: "
                  f"Train: {train_error:.3f}, Val: {val_error:.3f}, "
                  f"LR: {current_lr:.2e}")
        
        # 目标达成检查
        if target_error and val_error <= target_error:
            print(f"🎉 达到目标误差 {target_error}mm！在第{epoch+1}轮")
            break
        
        if patience_counter >= patience:
            print(f"⏹️ 早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    training_time = time.time() - start_time
    
    # 加载最佳模型
    model.load_state_dict(torch.load(f'best_{model_name}_{model.num_keypoints}point.pth'))
    
    print(f"✅ {model.num_keypoints}点训练完成!")
    print(f"   最佳验证误差: {best_val_error:.3f}")
    print(f"   训练时间: {training_time/60:.1f}分钟")
    
    return model, best_val_error

def run_progressive_scaling():
    """运行渐进式扩展"""
    
    print("🎯 开始渐进式扩展实施")
    print("=" * 80)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载数据集
    print("📊 加载数据集...")
    
    # 加载历史12点数据集
    historical_data = np.load('archive/old_experiments/f3_reduced_12kp_stable.npz', allow_pickle=True)
    point_clouds_hist = historical_data['point_clouds']
    keypoints_12_historical = historical_data['keypoints']
    sample_ids_hist = historical_data['sample_ids']
    print(f"✅ 历史12点数据集: {len(sample_ids_hist)}样本")
    
    # 加载57点数据集
    data_57 = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
    point_clouds_57 = data_57['point_clouds']
    keypoints_57 = data_57['keypoints_57']
    sample_ids_57 = data_57['sample_ids']
    print(f"✅ 57点数据集: {len(sample_ids_57)}样本")
    
    # 获取关键点子集
    keypoint_subsets = get_keypoint_subsets()
    
    # 渐进式扩展计划
    scaling_plan = [
        {"keypoints": 12, "target_error": 6.0, "epochs": 80, "use_historical": True},
        {"keypoints": 15, "target_error": 6.5, "epochs": 60, "use_historical": False},
        {"keypoints": 19, "target_error": 7.2, "epochs": 60, "use_historical": False},
        {"keypoints": 24, "target_error": 8.0, "epochs": 80, "use_historical": False},
    ]
    
    results = {}
    
    for step in scaling_plan:
        num_keypoints = step["keypoints"]
        target_error = step["target_error"]
        epochs = step["epochs"]
        use_historical = step["use_historical"]
        
        print(f"\n🎯 阶段: {num_keypoints}点扩展")
        print("=" * 50)
        
        # 准备数据
        if use_historical:
            # 使用历史12点数据
            indices = np.arange(len(point_clouds_hist))
            train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
            train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
            
            train_dataset = Historical12Dataset(point_clouds_hist[train_indices], keypoints_12_historical[train_indices])
            val_dataset = Historical12Dataset(point_clouds_hist[val_indices], keypoints_12_historical[val_indices])
            
        else:
            # 使用57点数据集的子集
            subset_indices = keypoint_subsets[num_keypoints]
            
            indices = np.arange(len(point_clouds_57))
            train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
            train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
            
            train_dataset = ProgressiveDataset(point_clouds_57[train_indices], keypoints_57[train_indices], subset_indices)
            val_dataset = ProgressiveDataset(point_clouds_57[val_indices], keypoints_57[val_indices], subset_indices)
        
        # 数据加载器
        batch_size = 4
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
        
        print(f"📋 数据: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
        
        # 创建模型
        model = ExactEnsembleDoubleSoftMaxPointNet(num_keypoints=num_keypoints, dropout_rate=0.3, num_ensembles=3)
        
        # 训练模型
        model, best_error = train_progressive_model(
            model, train_loader, val_loader, epochs=epochs, device=device,
            target_error=target_error, model_name=f"progressive_{num_keypoints}"
        )
        
        # 记录结果
        results[num_keypoints] = {
            "best_error": best_error,
            "target_error": target_error,
            "success": best_error <= target_error,
            "use_historical": use_historical
        }
        
        print(f"📊 {num_keypoints}点结果:")
        print(f"   最佳误差: {best_error:.3f}mm")
        print(f"   目标误差: {target_error:.3f}mm")
        print(f"   {'✅ 成功' if best_error <= target_error else '❌ 未达标'}")
        
        # 如果失败，停止扩展
        if best_error > target_error * 1.3:  # 允许30%的容差
            print(f"⚠️ {num_keypoints}点严重未达标，停止进一步扩展")
            break
    
    # 保存最终结果
    with open('progressive_scaling_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n🎯 渐进式扩展完成!")
    print(f"💾 结果已保存: progressive_scaling_results.json")
    
    return results

def main():
    """主函数"""
    
    print("🚀 渐进式扩展实施（修复版）")
    print("从12点开始，逐步扩展到更多关键点")
    print("=" * 80)
    
    set_seed(123)  # 使用历史最佳种子
    
    try:
        results = run_progressive_scaling()
        
        print(f"\n📊 最终总结:")
        for num_keypoints, result in results.items():
            status = "✅ 成功" if result["success"] else "❌ 失败"
            data_source = "历史数据" if result["use_historical"] else "57点子集"
            print(f"   {num_keypoints}点: {result['best_error']:.3f}mm (目标{result['target_error']:.3f}mm) {status} [{data_source}]")
        
        # 分析结果
        successful_steps = [k for k, v in results.items() if v["success"]]
        if successful_steps:
            max_successful = max(successful_steps)
            print(f"\n🎉 成功扩展到: {max_successful}点")
            print(f"💡 证明了渐进式扩展的可行性")
        else:
            print(f"\n⚠️ 所有步骤都未达标，需要调整策略")
        
    except Exception as e:
        print(f"❌ 实施失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
