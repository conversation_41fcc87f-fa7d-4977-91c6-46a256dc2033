# 训练模型管理

## 目录结构
```
trained_models/
├── best_performers/     # 最佳性能模型
├── improvements/        # 改进实验模型  
├── experiments/         # 架构实验模型
├── current_session/     # 当前会话模型
├── archived/           # 存档模型
├── results/            # 训练结果JSON文件
├── model_inventory.json # 模型清单
└── README.md           # 本文件
```

## 最佳性能模型

### 🥇 当前最佳: 集成双Softmax
- **文件**: `best_集成双softmax_improvement_5.829mm.pth`
- **性能**: 5.829mm
- **描述**: 3个双Softmax模块集成，突破5.959mm基线

### 🥈 基线+双Softmax  
- **文件**: `best_baseline_double_softmax_5.959mm.pth`
- **性能**: 5.959mm
- **描述**: 基线架构+双Softmax精细化，突破6mm目标

## 使用方法

### 加载最佳模型
```python
import torch
from baseline_with_double_softmax import BaselineAdaptivePointNet

# 加载集成双Softmax模型
model = BaselineAdaptivePointNet(num_keypoints=12, softmax_type="ensemble")
checkpoint = torch.load("trained_models/best_performers/best_集成双softmax_improvement_5.829mm.pth")
model.load_state_dict(checkpoint['model_state_dict'])
```

### 查看模型清单
```python
import json
with open("trained_models/model_inventory.json", "r") as f:
    inventory = json.load(f)
print(f"总模型数: {inventory['summary']['total_models']}")
```

## 性能进展

1. **12关键点基线**: 6.208mm
2. **基线+双Softmax**: 5.959mm (+4.0%)
3. **集成双Softmax**: 5.829mm (+2.2%)

## 下一步目标

- **目标**: 5.5mm (医疗级精度)
- **策略**: 文献方法借鉴 + 进一步集成优化
