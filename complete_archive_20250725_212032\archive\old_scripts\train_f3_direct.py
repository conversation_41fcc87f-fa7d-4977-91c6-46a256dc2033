#!/usr/bin/env python3
"""
Train F3 Direct - Direct Training Script

Directly train F3 keypoint detection model using existing F3SimpleDataset.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from pathlib import Path
import time
import json

class F3Dataset(torch.utils.data.Dataset):
    """F3 dataset loader"""
    
    def __init__(self, data_dir: str, split: str = 'train'):
        self.data_dir = Path(data_dir)
        self.split = split
        
        # Load sample files
        split_dir = self.data_dir / split
        self.keypoint_files = list(split_dir.glob('*_keypoints.npy'))
        
        print(f"📂 {split}: {len(self.keypoint_files)} samples")
        
    def __len__(self):
        return len(self.keypoint_files)
    
    def __getitem__(self, idx):
        keypoint_file = self.keypoint_files[idx]
        sample_id = keypoint_file.stem.replace('_keypoints', '')
        
        # Load data
        keypoints = np.load(keypoint_file)  # (19, 3)
        pointcloud_file = keypoint_file.parent / f"{sample_id}_pointcloud.npy"
        point_cloud = np.load(pointcloud_file)  # (2000, 3)
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': sample_id
        }

class F3PointNet(nn.Module):
    """Simple PointNet for F3"""
    
    def __init__(self):
        super(F3PointNet, self).__init__()
        
        # Feature extraction
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
        # Regression head
        self.fc1 = nn.Linear(512, 256)
        self.fc2 = nn.Linear(256, 128)
        self.fc3 = nn.Linear(128, 19 * 3)  # 19 keypoints * 3 coords
        
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        # x: (batch, num_points, 3)
        x = x.transpose(2, 1)  # (batch, 3, num_points)
        
        # Feature extraction
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.relu(self.bn2(self.conv2(x)))
        x = self.relu(self.bn3(self.conv3(x)))
        x = self.relu(self.bn4(self.conv4(x)))
        
        # Global max pooling
        x = torch.max(x, 2)[0]  # (batch, 512)
        
        # Regression
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        # Reshape to keypoints
        x = x.view(-1, 19, 3)
        
        return x

def calculate_metrics(pred, target):
    """Calculate evaluation metrics"""
    pred_np = pred.cpu().numpy()
    target_np = target.cpu().numpy()
    
    distances = np.linalg.norm(pred_np - target_np, axis=-1)
    mean_error = np.mean(distances)
    
    acc_1mm = np.mean(distances <= 1.0) * 100
    acc_5mm = np.mean(distances <= 5.0) * 100
    acc_10mm = np.mean(distances <= 10.0) * 100
    
    return {
        'mean_error': mean_error,
        'acc_1mm': acc_1mm,
        'acc_5mm': acc_5mm,
        'acc_10mm': acc_10mm
    }

def main():
    print("🚀 **F3关键点检测模型训练**")
    print("=" * 60)
    
    # Setup
    device = torch.device('cuda:2' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # Data
    train_dataset = F3Dataset("F3SimpleDataset", "train")
    val_dataset = F3Dataset("F3SimpleDataset", "val")
    
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False)
    
    # Model
    model = F3PointNet().to(device)
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    
    print(f"🧠 模型参数: {sum(p.numel() for p in model.parameters()):,}")
    
    # Training
    best_error = float('inf')
    history = []
    
    print(f"\n🎯 开始训练")
    
    for epoch in range(30):
        print(f"\nEpoch {epoch+1}/30")
        
        # Train
        model.train()
        train_loss = 0
        
        for batch in train_loader:
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            pred = model(point_cloud)
            loss = criterion(pred, keypoints)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # Validation
        model.eval()
        val_loss = 0
        all_pred = []
        all_target = []
        
        with torch.no_grad():
            for batch in val_loader:
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                pred = model(point_cloud)
                loss = criterion(pred, keypoints)
                
                val_loss += loss.item()
                all_pred.append(pred.cpu())
                all_target.append(keypoints.cpu())
        
        val_loss /= len(val_loader)
        
        # Metrics
        all_pred = torch.cat(all_pred, dim=0)
        all_target = torch.cat(all_target, dim=0)
        metrics = calculate_metrics(all_pred, all_target)
        
        # Record
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'val_error': metrics['mean_error'],
            'acc_1mm': metrics['acc_1mm'],
            'acc_5mm': metrics['acc_5mm'],
            'acc_10mm': metrics['acc_10mm']
        })
        
        # Print
        print(f"  训练损失: {train_loss:.4f}")
        print(f"  验证损失: {val_loss:.4f}")
        print(f"  验证误差: {metrics['mean_error']:.2f}mm")
        print(f"  1mm精度: {metrics['acc_1mm']:.1f}%")
        print(f"  5mm精度: {metrics['acc_5mm']:.1f}%")
        print(f"  10mm精度: {metrics['acc_10mm']:.1f}%")
        
        # Save best
        if metrics['mean_error'] < best_error:
            best_error = metrics['mean_error']
            torch.save({
                'model_state_dict': model.state_dict(),
                'epoch': epoch + 1,
                'error': best_error,
                'metrics': metrics
            }, 'best_f3_model.pth')
            print(f"  ✅ 保存最佳模型: {best_error:.2f}mm")
        
        # Early success check
        if metrics['mean_error'] <= 10.0:
            print(f"  🎯 达到可接受精度!")
            if metrics['mean_error'] <= 5.0:
                print(f"  🎉 达到优秀精度!")
                break
    
    # Results
    print(f"\n🎯 **训练完成**")
    print(f"   最佳误差: {best_error:.2f}mm")
    print(f"   训练轮数: {len(history)}")
    
    # Save results
    results = {
        'best_error': best_error,
        'total_epochs': len(history),
        'history': history,
        'dataset_info': {
            'train_samples': len(train_dataset),
            'val_samples': len(val_dataset)
        }
    }
    
    with open('f3_training_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"📁 结果已保存: f3_training_results.json")
    print(f"📁 模型已保存: best_f3_model.pth")
    
    return best_error

if __name__ == "__main__":
    try:
        error = main()
        print(f"\n🎉 F3训练成功! 最终误差: {error:.2f}mm")
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
