================================================================================
🔍 医疗关键点数据集异常检查报告
================================================================================
📅 生成时间: 2025-07-19 17:16:56
📊 总样本数: 97
⚠️  可疑样本数: 68 (70.1%)

📊 数据集统计摘要:
----------------------------------------
关键点间距离: 64.9±27.6mm
  范围: 8.4 - 165.7mm
点云大小: 50000±0 点
  范围: 50000 - 50000 点
点云尺寸 (X×Y×Z): 127.7×101.4×142.3mm

🚨 可疑样本详细列表:
--------------------------------------------------------------------------------
 1. 样本 600072 (索引: 90)
    问题数量: 4 (高危: 0, 中危: 4)
    🟡 X轴尺寸异常: X轴范围: 252.0, 期望: 127.7±36.6
       建议: 检查X轴坐标是否正确
    🟡 Y轴尺寸异常: Y轴范围: 147.9, 期望: 101.4±11.3
       建议: 检查Y轴坐标是否正确
    🟡 Z轴尺寸异常: Z轴范围: 266.9, 期望: 142.3±22.3
       建议: 检查Z轴坐标是否正确
    🟡 关键点过远: 最大距离: 135.7mm
       建议: 检查是否有标注错误的关键点

 2. 样本 600099 (索引: 93)
    问题数量: 3 (高危: 0, 中危: 3)
    🟡 X轴尺寸异常: X轴范围: 418.0, 期望: 127.7±36.6
       建议: 检查X轴坐标是否正确
    🟡 Z轴尺寸异常: Z轴范围: 210.8, 期望: 142.3±22.3
       建议: 检查Z轴坐标是否正确
    🟡 关键点过远: 最大距离: 140.9mm
       建议: 检查是否有标注错误的关键点

 3. 样本 600036 (索引: 13)
    问题数量: 2 (高危: 0, 中危: 2)
    🟡 X轴尺寸异常: X轴范围: 201.3, 期望: 127.7±36.6
       建议: 检查X轴坐标是否正确
    🟡 关键点过远: 最大距离: 137.4mm
       建议: 检查是否有标注错误的关键点

 4. 样本 600043 (索引: 46)
    问题数量: 2 (高危: 0, 中危: 2)
    🟡 Z轴尺寸异常: Z轴范围: 234.9, 期望: 142.3±22.3
       建议: 检查Z轴坐标是否正确
    🟡 关键点过远: 最大距离: 136.7mm
       建议: 检查是否有标注错误的关键点

 5. 样本 600047 (索引: 52)
    问题数量: 2 (高危: 0, 中危: 2)
    🟡 关键点-点云中心偏移: 偏移距离: 10.3mm
       建议: 检查关键点和点云是否在同一坐标系
    🟡 关键点过远: 最大距离: 165.7mm
       建议: 检查是否有标注错误的关键点

 6. 样本 600071 (索引: 53)
    问题数量: 2 (高危: 0, 中危: 2)
    🟡 Y轴尺寸异常: Y轴范围: 149.0, 期望: 101.4±11.3
       建议: 检查Y轴坐标是否正确
    🟡 关键点过远: 最大距离: 146.3mm
       建议: 检查是否有标注错误的关键点

 7. 样本 600054 (索引: 85)
    问题数量: 2 (高危: 0, 中危: 2)
    🟡 关键点-点云中心偏移: 偏移距离: 10.7mm
       建议: 检查关键点和点云是否在同一坐标系
    🟡 关键点过远: 最大距离: 146.1mm
       建议: 检查是否有标注错误的关键点

 8. 样本 600111 (索引: 95)
    问题数量: 2 (高危: 0, 中危: 2)
    🟡 关键点-点云中心偏移: 偏移距离: 10.5mm
       建议: 检查关键点和点云是否在同一坐标系
    🟡 关键点过远: 最大距离: 160.6mm
       建议: 检查是否有标注错误的关键点

 9. 样本 600114 (索引: 0)
    问题数量: 1 (高危: 0, 中危: 1)
    🟡 关键点过远: 最大距离: 137.1mm
       建议: 检查是否有标注错误的关键点

10. 样本 600076 (索引: 1)
    问题数量: 1 (高危: 0, 中危: 1)
    🟡 关键点过远: 最大距离: 142.7mm
       建议: 检查是否有标注错误的关键点

11. 样本 600086 (索引: 2)
    问题数量: 1 (高危: 0, 中危: 1)
    🟡 关键点过远: 最大距离: 149.3mm
       建议: 检查是否有标注错误的关键点

12. 样本 600107 (索引: 3)
    问题数量: 1 (高危: 0, 中危: 1)
    🟡 关键点过远: 最大距离: 135.2mm
       建议: 检查是否有标注错误的关键点

13. 样本 600128 (索引: 4)
    问题数量: 1 (高危: 0, 中危: 1)
    🟡 关键点过远: 最大距离: 143.8mm
       建议: 检查是否有标注错误的关键点

14. 样本 600124 (索引: 5)
    问题数量: 1 (高危: 0, 中危: 1)
    🟡 关键点过远: 最大距离: 155.4mm
       建议: 检查是否有标注错误的关键点

15. 样本 600053 (索引: 6)
    问题数量: 1 (高危: 0, 中危: 1)
    🟡 关键点过远: 最大距离: 146.5mm
       建议: 检查是否有标注错误的关键点

16. 样本 600120 (索引: 7)
    问题数量: 1 (高危: 0, 中危: 1)
    🟡 关键点过远: 最大距离: 148.4mm
       建议: 检查是否有标注错误的关键点

17. 样本 600105 (索引: 9)
    问题数量: 1 (高危: 0, 中危: 1)
    🟡 关键点过远: 最大距离: 148.5mm
       建议: 检查是否有标注错误的关键点

18. 样本 600052 (索引: 11)
    问题数量: 1 (高危: 0, 中危: 1)
    🟡 关键点过远: 最大距离: 149.2mm
       建议: 检查是否有标注错误的关键点

19. 样本 600050 (索引: 14)
    问题数量: 1 (高危: 0, 中危: 1)
    🟡 关键点过远: 最大距离: 143.6mm
       建议: 检查是否有标注错误的关键点

20. 样本 600090 (索引: 15)
    问题数量: 1 (高危: 0, 中危: 1)
    🟡 关键点过远: 最大距离: 146.7mm
       建议: 检查是否有标注错误的关键点

... 还有 48 个可疑样本未显示

💡 总体检查建议:
----------------------------------------
1. 🔴 高优先级: 0 个样本需要立即检查
   - 重点检查关键点标注是否正确
   - 验证坐标系是否一致
   - 考虑重新标注或移除

2. 🟡 中优先级: 68 个样本建议检查
   - 检查数据预处理是否正确
   - 验证标注精度
   - 可考虑保留但需要注意

3. 🎯 改进建议:
   - 建立标注质量控制流程
   - 统一坐标系和尺度标准
   - 增加标注一致性检查
   - 考虑引入多人标注验证