#!/usr/bin/env python3
"""
Train F3 Only - Pure Training Script

Train F3 keypoint detection model using the existing F3SimpleDataset.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from pathlib import Path
import time
import json

class F3SimpleDataset(torch.utils.data.Dataset):
    """Dataset for F3 single component keypoints"""
    
    def __init__(self, data_dir: str, split: str = 'train', 
                 num_points: int = 2000, augment: bool = False):
        self.data_dir = Path(data_dir)
        self.split = split
        self.num_points = num_points
        self.augment = augment
        
        # Load sample files
        split_dir = self.data_dir / split
        self.keypoint_files = list(split_dir.glob('*_keypoints.npy'))
        
        print(f"📂 {split} dataset: {len(self.keypoint_files)} samples")
        
    def __len__(self):
        return len(self.keypoint_files)
    
    def __getitem__(self, idx):
        keypoint_file = self.keypoint_files[idx]
        sample_id = keypoint_file.stem.replace('_keypoints', '')
        
        # Load keypoints and point cloud
        keypoints = np.load(keypoint_file)  # (19, 3)
        pointcloud_file = keypoint_file.parent / f"{sample_id}_pointcloud.npy"
        point_cloud = np.load(pointcloud_file)  # (2000, 3)
        
        # Sample points if needed
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        elif len(point_cloud) < self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=True)
            point_cloud = point_cloud[indices]
            
        # Data augmentation for F3 component
        if self.augment and self.split == 'train':
            # Small rotation around Z-axis (preserve anatomical orientation)
            angle = np.random.uniform(-np.pi/24, np.pi/24)  # ±7.5 degrees
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            point_cloud = point_cloud @ rotation_matrix.T
            keypoints = keypoints @ rotation_matrix.T
            
            # Small translation
            translation = np.random.uniform(-1.0, 1.0, 3)
            point_cloud += translation
            keypoints += translation
            
            # Small scaling
            scale = np.random.uniform(0.98, 1.02)
            point_cloud *= scale
            keypoints *= scale
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),  # (N, 3)
            'keypoints': torch.FloatTensor(keypoints),  # (19, 3)
            'sample_id': sample_id
        }

class SimplePointNet(nn.Module):
    """Simplified PointNet for F3 keypoint detection"""
    
    def __init__(self, num_keypoints=19, num_points=2000):
        super(SimplePointNet, self).__init__()
        self.num_keypoints = num_keypoints
        self.num_points = num_points
        
        # Point feature extraction
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        
        # Global feature
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # Keypoint regression
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        # x: (batch, num_points, 3)
        x = x.transpose(2, 1)  # (batch, 3, num_points)
        
        # Point feature extraction
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.relu(self.bn2(self.conv2(x)))
        x = self.relu(self.bn3(self.conv3(x)))
        
        # Global feature
        x = self.relu(self.bn4(self.conv4(x)))
        x = self.relu(self.bn5(self.conv5(x)))
        
        # Global max pooling
        x = torch.max(x, 2)[0]  # (batch, 1024)
        
        # Keypoint regression
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        # Reshape to keypoints
        x = x.view(-1, self.num_keypoints, 3)
        
        return x

def calculate_f3_metrics(pred_keypoints: torch.Tensor, 
                        target_keypoints: torch.Tensor):
    """Calculate F3-specific evaluation metrics"""
    
    # Convert to numpy
    pred_np = pred_keypoints.cpu().numpy()
    target_np = target_keypoints.cpu().numpy()
    
    # Calculate distances (already in mm scale)
    distances = np.linalg.norm(pred_np - target_np, axis=-1)  # (batch, 19)
    
    # Overall metrics
    mean_error = np.mean(distances)
    
    # Medical accuracy thresholds
    excellent_acc = np.mean(distances <= 1.0) * 100   # ≤1mm
    very_good_acc = np.mean(distances <= 2.0) * 100   # ≤2mm
    good_acc = np.mean(distances <= 5.0) * 100        # ≤5mm
    acceptable_acc = np.mean(distances <= 10.0) * 100 # ≤10mm
    
    return {
        'mean_error': mean_error,
        'excellent_percentage': excellent_acc,
        'very_good_percentage': very_good_acc,
        'good_percentage': good_acc,
        'acceptable_percentage': acceptable_acc
    }

def main():
    """Main training function"""
    
    print("🚀 **F3单部件关键点检测模型训练**")
    print("🎯 **目标: 验证F3数据集质量和模型性能**")
    print("📊 **数据集: F3SimpleDataset (96样本, 19关键点)**")
    print("=" * 80)
    
    # Check if dataset exists
    dataset_dir = "F3SimpleDataset"
    if not Path(dataset_dir).exists():
        print(f"❌ 数据集目录不存在: {dataset_dir}")
        print(f"请先运行数据集创建脚本")
        return
    
    # Device setup
    device = torch.device('cuda:2' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")

    # Clear GPU cache
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print(f"🧹 清理GPU缓存完成")

    # Dataset setup
    num_points = 2000
    
    train_dataset = F3SimpleDataset(dataset_dir, split='train', 
                                   num_points=num_points, augment=True)
    val_dataset = F3SimpleDataset(dataset_dir, split='val', 
                                 num_points=num_points, augment=False)
    
    # Data loaders
    batch_size = 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, 
                            shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, 
                          shuffle=False, num_workers=2)
    
    print(f"📊 数据集统计:")
    print(f"   训练样本: {len(train_dataset)}")
    print(f"   验证样本: {len(val_dataset)}")
    print(f"   批量大小: {batch_size}")
    print(f"   F3关键点数: 19个")
    
    # Model setup
    model = SimplePointNet(num_keypoints=19, num_points=num_points).to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 F3-PointNet模型:")
    print(f"   总参数: {total_params:,}")
    print(f"   模型大小: {total_params * 4 / (1024 * 1024):.1f}MB")
    
    # Training setup
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=0.0001)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=10, min_lr=1e-6, verbose=True
    )
    
    # Training configuration
    num_epochs = 50
    best_val_error = float('inf')
    patience_counter = 0
    patience = 15
    
    # Training history
    training_history = []
    
    print(f"\n🎯 开始F3模型训练")
    print(f"📈 训练轮数: {num_epochs}, 早停耐心: {patience}")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 50)
        
        # Training phase
        model.train()
        train_loss = 0.0
        
        for batch_idx, batch in enumerate(train_loader):
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            # Forward pass
            pred_keypoints = model(point_cloud)
            
            # Calculate loss
            loss = criterion(pred_keypoints, keypoints)
            
            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        all_pred_keypoints = []
        all_target_keypoints = []
        
        with torch.no_grad():
            for batch in val_loader:
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                val_loss += loss.item()
                all_pred_keypoints.append(pred_keypoints.cpu())
                all_target_keypoints.append(keypoints.cpu())
        
        val_loss /= len(val_loader)
        
        # Calculate metrics
        all_pred = torch.cat(all_pred_keypoints, dim=0)
        all_target = torch.cat(all_target_keypoints, dim=0)
        metrics = calculate_f3_metrics(all_pred, all_target)
        
        # Learning rate scheduling
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # Record training history
        epoch_record = {
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'val_error_mm': metrics['mean_error'],
            'learning_rate': current_lr,
            'excellent_acc': metrics['excellent_percentage'],
            'good_acc': metrics['good_percentage']
        }
        training_history.append(epoch_record)
        
        # Print progress
        print(f"   训练损失: {train_loss:.4f}")
        print(f"   验证损失: {val_loss:.4f}")
        print(f"   验证误差: {metrics['mean_error']:.3f}mm")
        print(f"   1mm精度: {metrics['excellent_percentage']:.1f}%")
        print(f"   5mm精度: {metrics['good_percentage']:.1f}%")
        print(f"   学习率: {current_lr:.2e}")
        
        # Save best model
        if metrics['mean_error'] < best_val_error:
            best_val_error = metrics['mean_error']
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_error': best_val_error,
                'metrics': metrics
            }, 'best_f3_model.pth')
            
            print(f"   ✅ 保存最佳F3模型 (误差: {best_val_error:.3f}mm)")
        else:
            patience_counter += 1
        
        # Early stopping
        if patience_counter >= patience:
            print(f"   ⏹️ 早停: {patience}轮无改进")
            break
        
        # Check if we achieve good performance
        if metrics['mean_error'] <= 10.0:
            print(f"   🎯 达到可接受精度! (≤10mm)")
            if metrics['mean_error'] <= 5.0:
                print(f"   🎉 达到良好精度! (≤5mm)")
    
    total_time = time.time() - start_time
    
    print(f"\n🎯 **F3模型训练完成!**")
    print(f"   训练时间: {total_time/60:.1f} 分钟")
    print(f"   最佳验证误差: {best_val_error:.3f}mm")
    print(f"   性能评估: {'🎉 优秀' if best_val_error <= 5.0 else '✅ 良好' if best_val_error <= 10.0 else '⚠️ 需改进'}")
    
    # Save training results
    results = {
        'model_type': 'F3_SingleComponent_PointNet',
        'dataset': 'F3SimpleDataset',
        'training_completed': True,
        'best_validation_error_mm': best_val_error,
        'training_time_minutes': total_time / 60,
        'total_epochs': len(training_history),
        'dataset_samples': {
            'train': len(train_dataset),
            'val': len(val_dataset)
        },
        'model_parameters': total_params,
        'training_history': training_history
    }
    
    with open('f3_training_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📁 训练结果已保存: f3_training_results.json")
    print(f"📁 最佳模型已保存: best_f3_model.pth")
    
    return best_val_error, total_time

if __name__ == "__main__":
    try:
        best_error, training_time = main()
        print(f"\n🎉 **F3模型训练成功完成!**")
        print(f"🎯 最终结果: {best_error:.3f}mm 误差")
        print(f"⏱️ 训练时间: {training_time/60:.1f} 分钟")
        print(f"📊 这验证了F3数据集的质量和可用性!")
        
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        import traceback
        traceback.print_exc()
