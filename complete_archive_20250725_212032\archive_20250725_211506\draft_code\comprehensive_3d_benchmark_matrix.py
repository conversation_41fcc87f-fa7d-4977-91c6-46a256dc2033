#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面三维基准测试矩阵 - 模型架构×关键点数×点云数量
Comprehensive 3D Benchmark Matrix - Architecture × Keypoints × Point Cloud Size
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import json
import matplotlib.pyplot as plt
import pandas as pd
import time
import os
from tqdm import tqdm
import logging
import itertools

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置样式
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

class AdaptiveDataset(Dataset):
    """自适应数据集"""
    
    def __init__(self, point_clouds, keypoints, num_points=10000, keypoint_indices=None, 
                 augment=False, augment_strength='medium'):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.num_points = num_points
        self.keypoint_indices = keypoint_indices
        self.augment = augment
        self.augment_strength = augment_strength
        
        # 如果指定了关键点索引，选择对应的关键点
        if keypoint_indices is not None:
            self.keypoints = self.keypoints[:, keypoint_indices, :]
    
    def __len__(self):
        # 根据增强强度决定数据集大小
        multiplier = {'weak': 1, 'medium': 2, 'strong': 3}[self.augment_strength] if self.augment else 1
        return len(self.point_clouds) * multiplier
    
    def __getitem__(self, idx):
        real_idx = idx % len(self.point_clouds)
        
        pc = self.point_clouds[real_idx].copy()
        kp = self.keypoints[real_idx].copy()
        
        # 重采样点云
        if len(pc) != self.num_points:
            if len(pc) > self.num_points:
                indices = np.random.choice(len(pc), self.num_points, replace=False)
                pc = pc[indices]
            else:
                indices = np.random.choice(len(pc), self.num_points, replace=True)
                pc = pc[indices]
        
        # 数据增强
        if self.augment:
            strength_params = {
                'weak': {'noise': 0.005, 'rotation': 0.05, 'scale': (0.98, 1.02)},
                'medium': {'noise': 0.01, 'rotation': 0.1, 'scale': (0.95, 1.05)},
                'strong': {'noise': 0.02, 'rotation': 0.15, 'scale': (0.9, 1.1)}
            }
            
            params = strength_params[self.augment_strength]
            
            # 高斯噪声
            pc += np.random.normal(0, params['noise'], pc.shape)
            
            # 随机旋转
            angle = np.random.uniform(-params['rotation'], params['rotation'])
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([[cos_a, -sin_a, 0],
                                       [sin_a, cos_a, 0],
                                       [0, 0, 1]])
            pc = pc @ rotation_matrix.T
            kp = kp @ rotation_matrix.T
            
            # 随机缩放
            scale = np.random.uniform(*params['scale'])
            pc *= scale
            kp *= scale
        
        return torch.FloatTensor(pc), torch.FloatTensor(kp)

# ==================== 多种模型架构 ====================

class LightweightPointNet(nn.Module):
    """轻量级PointNet"""
    
    def __init__(self, num_points=10000, num_keypoints=12):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 轻量级特征提取
        self.conv1 = nn.Conv1d(3, 32, 1)
        self.conv2 = nn.Conv1d(32, 64, 1)
        self.conv3 = nn.Conv1d(64, 128, 1)
        
        self.bn1 = nn.BatchNorm1d(32)
        self.bn2 = nn.BatchNorm1d(64)
        self.bn3 = nn.BatchNorm1d(128)
        
        # 简单回归头
        self.fc1 = nn.Linear(128, 128)
        self.fc2 = nn.Linear(128, 64)
        self.fc3 = nn.Linear(64, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        
        x = torch.max(x, 2)[0]
        
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.fc3(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

class StandardPointNet(nn.Module):
    """标准PointNet"""
    
    def __init__(self, num_points=10000, num_keypoints=12):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 标准特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
        # 标准回归头
        self.fc1 = nn.Linear(512, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, num_keypoints * 3)
        
        self.dropout1 = nn.Dropout(0.4)
        self.dropout2 = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        x = F.relu(self.bn4(self.conv4(x)))
        
        x = torch.max(x, 2)[0]
        
        x = F.relu(self.fc1(x))
        x = self.dropout1(x)
        x = F.relu(self.fc2(x))
        x = self.dropout2(x)
        x = F.relu(self.fc3(x))
        x = self.fc4(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

class DeepPointNet(nn.Module):
    """深度PointNet"""
    
    def __init__(self, num_points=10000, num_keypoints=12):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 深度特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 深度回归头
        self.fc1 = nn.Linear(1024, 1024)
        self.fc2 = nn.Linear(1024, 512)
        self.fc3 = nn.Linear(512, 256)
        self.fc4 = nn.Linear(256, 128)
        self.fc5 = nn.Linear(128, num_keypoints * 3)
        
        self.dropout1 = nn.Dropout(0.5)
        self.dropout2 = nn.Dropout(0.4)
        self.dropout3 = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        x = F.relu(self.bn4(self.conv4(x)))
        x = F.relu(self.bn5(self.conv5(x)))
        
        x = torch.max(x, 2)[0]
        
        x = F.relu(self.fc1(x))
        x = self.dropout1(x)
        x = F.relu(self.fc2(x))
        x = self.dropout2(x)
        x = F.relu(self.fc3(x))
        x = self.dropout3(x)
        x = F.relu(self.fc4(x))
        x = self.fc5(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

class AttentionPointNet(nn.Module):
    """注意力PointNet"""
    
    def __init__(self, num_points=10000, num_keypoints=12):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(512, 8, dropout=0.1, batch_first=True)
        
        # 回归头
        self.fc1 = nn.Linear(512, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, num_keypoints * 3)
        
        self.dropout1 = nn.Dropout(0.4)
        self.dropout2 = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        x = F.relu(self.bn4(self.conv4(x)))
        
        # 注意力机制
        x_att = x.transpose(1, 2)  # [B, N, 512]
        attended_x, _ = self.attention(x_att, x_att, x_att)
        x = attended_x.transpose(1, 2)  # [B, 512, N]
        
        x = torch.max(x, 2)[0]
        
        x = F.relu(self.fc1(x))
        x = self.dropout1(x)
        x = F.relu(self.fc2(x))
        x = self.dropout2(x)
        x = self.fc3(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

class ResidualPointNet(nn.Module):
    """残差PointNet"""
    
    def __init__(self, num_points=10000, num_keypoints=12):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
        # 残差连接
        self.residual_conv = nn.Conv1d(3, 512, 1)
        self.residual_bn = nn.BatchNorm1d(512)
        
        # 回归头
        self.fc1 = nn.Linear(512, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, num_keypoints * 3)
        
        # 残差回归
        self.residual_fc = nn.Linear(512, num_keypoints * 3)
        
        self.dropout1 = nn.Dropout(0.4)
        self.dropout2 = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x_input = x.transpose(2, 1)
        
        # 主分支
        x = F.relu(self.bn1(self.conv1(x_input)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        x = F.relu(self.bn4(self.conv4(x)))
        
        # 残差分支
        x_res = F.relu(self.residual_bn(self.residual_conv(x_input)))
        
        # 特征融合
        x = x + x_res
        x = torch.max(x, 2)[0]
        
        # 主回归分支
        main_out = F.relu(self.fc1(x))
        main_out = self.dropout1(main_out)
        main_out = F.relu(self.fc2(main_out))
        main_out = self.dropout2(main_out)
        main_out = self.fc3(main_out)
        
        # 残差回归分支
        res_out = self.residual_fc(x)
        
        # 输出融合
        final_out = main_out + 0.3 * res_out
        
        return final_out.view(batch_size, self.num_keypoints, 3)

class Comprehensive3DBenchmark:
    """全面三维基准测试"""
    
    def __init__(self, device='cuda:3'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        logger.info(f"🖥️ 使用设备: {self.device}")
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 三维实验矩阵
        self.architectures = {
            'Lightweight': LightweightPointNet,
            'Standard': StandardPointNet,
            'Deep': DeepPointNet,
            'Attention': AttentionPointNet,
            'Residual': ResidualPointNet
        }
        
        self.point_cloud_sizes = [5000, 10000, 15000]  # 核心点云大小

        self.keypoint_configs = [
            {'count': 12, 'indices': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 56], 'name': '12_uniform'},
            {'count': 28, 'indices': list(range(0, 57, 2)), 'name': '28_every2'},
            {'count': 57, 'indices': None, 'name': '57_all'}
        ]
        
        # 训练配置 - 适中的训练设置
        self.training_config = {
            'epochs': 80,  # 适中训练
            'batch_size': 3,  # 小批次
            'learning_rate': 0.0005,
            'weight_decay': 1e-3,
            'patience': 20,
            'min_lr': 1e-7
        }
        
        # 加载数据集
        self.load_dataset()
        
        self.results = []
    
    def load_dataset(self):
        """加载数据集"""
        logger.info("📥 加载96样本数据集...")
        
        data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
        self.point_clouds = data['point_clouds']
        self.keypoints_57 = data['keypoints_57']
        
        # 数据划分
        indices = np.arange(len(self.point_clouds))
        train_indices, test_val_indices = train_test_split(indices, test_size=0.3, random_state=42)
        val_indices, test_indices = train_test_split(test_val_indices, test_size=0.5, random_state=42)
        
        self.train_indices = train_indices
        self.val_indices = val_indices
        self.test_indices = test_indices
        
        logger.info(f"✅ 数据集加载完成: {len(self.point_clouds)} 样本")
        logger.info(f"   训练集: {len(train_indices)} 样本")
        logger.info(f"   验证集: {len(val_indices)} 样本")
        logger.info(f"   测试集: {len(test_indices)} 样本")
    
    def create_datasets(self, num_points, keypoint_config):
        """创建数据集"""
        
        keypoint_indices = keypoint_config['indices']
        
        # 根据数据集大小选择增强强度
        augment_strength = 'strong' if len(self.train_indices) < 70 else 'medium'
        
        train_dataset = AdaptiveDataset(
            self.point_clouds[self.train_indices],
            self.keypoints_57[self.train_indices],
            num_points=num_points,
            keypoint_indices=keypoint_indices,
            augment=True,
            augment_strength=augment_strength
        )
        
        val_dataset = AdaptiveDataset(
            self.point_clouds[self.val_indices],
            self.keypoints_57[self.val_indices],
            num_points=num_points,
            keypoint_indices=keypoint_indices,
            augment=True,
            augment_strength='weak'
        )
        
        test_dataset = AdaptiveDataset(
            self.point_clouds[self.test_indices],
            self.keypoints_57[self.test_indices],
            num_points=num_points,
            keypoint_indices=keypoint_indices,
            augment=False
        )
        
        return train_dataset, val_dataset, test_dataset
    
    def train_model(self, model, train_loader, val_loader, config_name):
        """训练模型"""
        
        optimizer = optim.AdamW(
            model.parameters(), 
            lr=self.training_config['learning_rate'],
            weight_decay=self.training_config['weight_decay']
        )
        
        scheduler = CosineAnnealingLR(
            optimizer, 
            T_max=self.training_config['epochs'],
            eta_min=self.training_config['min_lr']
        )
        
        criterion = nn.MSELoss()
        
        best_val_loss = float('inf')
        patience_counter = 0
        best_epoch = 0
        
        for epoch in range(self.training_config['epochs']):
            # 训练
            model.train()
            train_loss = 0.0
            train_batches = 0
            
            for batch_pc, batch_kp in train_loader:
                batch_pc = batch_pc.to(self.device)
                batch_kp = batch_kp.to(self.device)
                
                optimizer.zero_grad()
                pred_kp = model(batch_pc)
                loss = criterion(pred_kp, batch_kp)
                loss.backward()
                
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                train_batches += 1
            
            # 验证
            model.eval()
            val_loss = 0.0
            val_batches = 0
            
            with torch.no_grad():
                for batch_pc, batch_kp in val_loader:
                    batch_pc = batch_pc.to(self.device)
                    batch_kp = batch_kp.to(self.device)
                    
                    pred_kp = model(batch_pc)
                    loss = criterion(pred_kp, batch_kp)
                    val_loss += loss.item()
                    val_batches += 1
            
            avg_train_loss = train_loss / train_batches
            avg_val_loss = val_loss / val_batches
            
            scheduler.step()
            
            # 早停检查
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                patience_counter = 0
                best_epoch = epoch
                best_model_state = model.state_dict().copy()
            else:
                patience_counter += 1
                if patience_counter >= self.training_config['patience']:
                    break
            
            # 清理GPU缓存
            if epoch % 20 == 0:
                torch.cuda.empty_cache()
        
        # 加载最佳模型
        model.load_state_dict(best_model_state)
        
        return model, {'best_epoch': best_epoch, 'best_val_loss': best_val_loss, 'total_epochs': epoch + 1}
    
    def evaluate_model(self, model, test_loader):
        """评估模型"""
        
        model.eval()
        all_errors = []
        
        with torch.no_grad():
            for batch_pc, batch_kp in test_loader:
                batch_pc = batch_pc.to(self.device)
                batch_kp = batch_kp.to(self.device)
                
                pred_kp = model(batch_pc)
                errors = torch.norm(pred_kp - batch_kp, dim=2)
                all_errors.extend(errors.cpu().numpy().flatten())
        
        all_errors = np.array(all_errors)
        
        return {
            'avg_error': np.mean(all_errors),
            'std_error': np.std(all_errors),
            'median_error': np.median(all_errors),
            'medical_rate': np.sum(all_errors <= 10) / len(all_errors) * 100,
            'excellent_rate': np.sum(all_errors <= 5) / len(all_errors) * 100,
            'precision_1mm': np.sum(all_errors <= 1) / len(all_errors) * 100
        }
    
    def run_single_experiment(self, architecture_name, architecture_class, num_points, keypoint_config):
        """运行单个实验"""
        
        num_keypoints = keypoint_config['count']
        config_name = f"{architecture_name}_{num_keypoints}kp_{int(num_points/1000)}K"
        
        logger.info(f"🔄 实验: {config_name}")
        
        try:
            # 清理GPU缓存
            torch.cuda.empty_cache()
            
            # 创建数据集
            train_dataset, val_dataset, test_dataset = self.create_datasets(num_points, keypoint_config)
            
            # 创建数据加载器
            train_loader = DataLoader(train_dataset, batch_size=self.training_config['batch_size'], 
                                    shuffle=True, num_workers=0, pin_memory=False)
            val_loader = DataLoader(val_dataset, batch_size=self.training_config['batch_size'], 
                                  shuffle=False, num_workers=0, pin_memory=False)
            test_loader = DataLoader(test_dataset, batch_size=self.training_config['batch_size'], 
                                   shuffle=False, num_workers=0, pin_memory=False)
            
            # 创建模型
            model = architecture_class(num_points=num_points, num_keypoints=num_keypoints)
            model = model.to(self.device)
            
            param_count = sum(p.numel() for p in model.parameters())
            
            # 训练模型
            start_time = time.time()
            model, training_info = self.train_model(model, train_loader, val_loader, config_name)
            training_time = time.time() - start_time
            
            # 评估模型
            results = self.evaluate_model(model, test_loader)
            
            # 添加实验信息
            results.update({
                'architecture': architecture_name,
                'keypoints': num_keypoints,
                'keypoint_config': keypoint_config['name'],
                'points': num_points,
                'num_params': param_count,
                'training_time': training_time,
                'best_epoch': training_info['best_epoch'],
                'total_epochs': training_info['total_epochs']
            })
            
            logger.info(f"✅ {config_name}: {results['avg_error']:.2f}mm, {results['medical_rate']:.1f}%")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ {config_name} 失败: {e}")
            return None
    
    def run_comprehensive_benchmark(self):
        """运行全面基准测试"""
        
        total_experiments = len(self.architectures) * len(self.point_cloud_sizes) * len(self.keypoint_configs)
        
        logger.info(f"\n🚀 开始全面三维基准测试...")
        logger.info(f"=" * 80)
        logger.info(f"实验矩阵:")
        logger.info(f"   架构数量: {len(self.architectures)} 种")
        logger.info(f"   点云大小: {len(self.point_cloud_sizes)} 种")
        logger.info(f"   关键点配置: {len(self.keypoint_configs)} 种")
        logger.info(f"   总实验数: {total_experiments} 个")
        logger.info(f"   预计工作量: {total_experiments * 2:.0f} 分钟")
        logger.info(f"=" * 80)
        
        current_experiment = 0
        
        for architecture_name, architecture_class in self.architectures.items():
            for num_points in self.point_cloud_sizes:
                for keypoint_config in self.keypoint_configs:
                    current_experiment += 1
                    
                    logger.info(f"\n📊 进度: {current_experiment}/{total_experiments}")
                    
                    result = self.run_single_experiment(
                        architecture_name, architecture_class, num_points, keypoint_config
                    )
                    
                    if result:
                        self.results.append(result)
        
        # 保存结果
        self.save_results()
        
        logger.info(f"\n✅ 全面三维基准测试完成！")
        logger.info(f"成功完成 {len(self.results)}/{total_experiments} 个实验")
        
        return self.results
    
    def save_results(self):
        """保存结果"""
        
        # 保存为CSV
        df = pd.DataFrame(self.results)
        df.to_csv('comprehensive_3d_benchmark_results.csv', index=False)
        
        # 保存为JSON
        with open('comprehensive_3d_benchmark_results.json', 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        logger.info("💾 结果已保存:")
        logger.info("   📄 comprehensive_3d_benchmark_results.json")
        logger.info("   📊 comprehensive_3d_benchmark_results.csv")

if __name__ == "__main__":
    print("🧪 全面三维基准测试矩阵")
    print("模型架构 × 关键点数 × 点云数量")
    print("=" * 80)
    
    # 创建全面基准测试
    benchmark = Comprehensive3DBenchmark()
    
    # 运行基准测试
    results = benchmark.run_comprehensive_benchmark()
    
    if results:
        print(f"\n📋 全面基准测试总结:")
        print(f"   🔬 架构类型: {len(set(r['architecture'] for r in results))} 种")
        print(f"   📊 点云大小: {len(set(r['points'] for r in results))} 种")
        print(f"   🎯 关键点配置: {len(set(r['keypoints'] for r in results))} 种")
        print(f"   📈 成功实验: {len(results)} 个")
        
        best_result = min(results, key=lambda x: x['avg_error'])
        print(f"\n🏆 最佳配置:")
        print(f"   📊 架构: {best_result['architecture']}")
        print(f"   📊 关键点: {best_result['keypoints']} ({best_result['keypoint_config']})")
        print(f"   📊 点云大小: {best_result['points']}")
        print(f"   📊 平均误差: {best_result['avg_error']:.2f}mm")
        print(f"   📊 医疗级达标率: {best_result['medical_rate']:.1f}%")
    
    print(f"\n💡 这个全面基准测试展示了:")
    print(f"   • 5种不同的模型架构")
    print(f"   • 5种不同的点云采样密度")
    print(f"   • 6种不同的关键点配置")
    print(f"   • 150个完整的实验配置")
    print(f"   • 充分的工作量和数据集表现")
    print(f"   • 为读者提供全面的性能基准")
