#!/usr/bin/env python3
"""
稳定的突破性方法
Stable Breakthrough Methods
专注于稳定且有效的突破性改进
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pathlib import Path
from datetime import datetime
import json

class DeepPointNet(nn.Module):
    """深度PointNet - 稳定的突破性架构"""
    
    def __init__(self, num_keypoints=19):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 深度特征提取器
        self.feature_extractor = nn.Sequential(
            # 第一层
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(0.1),
            
            # 第二层
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.1),
            
            # 第三层
            nn.Conv1d(128, 256, 1),
            nn.<PERSON>chNorm1d(256),
            nn.ReL<PERSON>(),
            nn.Dropout(0.1),
            
            # 第四层
            nn.Conv1d(256, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.1),
            
            # 第五层
            nn.Conv1d(512, 1024, 1),
            nn.BatchNorm1d(1024),
            nn.ReLU(),
            nn.Dropout(0.1),
            
            # 第六层
            nn.Conv1d(1024, 2048, 1),
            nn.BatchNorm1d(2048),
            nn.ReLU()
        )
        
        # 全局特征处理器
        self.global_mlp = nn.Sequential(
            nn.Linear(2048, 1024),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 关键点回归器
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, num_keypoints * 3)
        )
        
    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape
        
        # 转换为卷积格式
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        # 深度特征提取
        features = self.feature_extractor(x)  # (B, 2048, N)
        
        # 全局最大池化
        global_feat = torch.max(features, dim=2)[0]  # (B, 2048)
        
        # 全局特征处理
        processed_feat = self.global_mlp(global_feat)  # (B, 256)
        
        # 关键点预测
        keypoints = self.keypoint_regressor(processed_feat)
        return keypoints.view(B, self.num_keypoints, 3)

class EnsemblePointNet(nn.Module):
    """集成PointNet - 多个网络的集成"""
    
    def __init__(self, num_keypoints=19, num_models=3):
        super().__init__()
        self.num_keypoints = num_keypoints
        self.num_models = num_models
        
        # 创建多个不同的子网络
        self.models = nn.ModuleList()
        
        for i in range(num_models):
            # 每个模型有不同的架构
            if i == 0:
                # 模型1: 标准深度
                model = nn.Sequential(
                    nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                    nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                    nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                    nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
                    nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
                )
            elif i == 1:
                # 模型2: 更宽的网络
                model = nn.Sequential(
                    nn.Conv1d(3, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                    nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                    nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
                    nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
                )
            else:
                # 模型3: 更深的网络
                model = nn.Sequential(
                    nn.Conv1d(3, 32, 1), nn.BatchNorm1d(32), nn.ReLU(),
                    nn.Conv1d(32, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                    nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                    nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                    nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
                    nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
                )
            
            self.models.append(model)
        
        # 每个模型的回归器
        self.regressors = nn.ModuleList([
            nn.Sequential(
                nn.Linear(1024, 512),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(512, 256),
                nn.ReLU(),
                nn.Linear(256, num_keypoints * 3)
            ) for _ in range(num_models)
        ])
        
        # 集成权重
        self.ensemble_weights = nn.Parameter(torch.ones(num_models) / num_models)
        
    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        predictions = []
        
        for i, (model, regressor) in enumerate(zip(self.models, self.regressors)):
            # 特征提取
            features = model(x)  # (B, 1024, N)
            global_feat = torch.max(features, dim=2)[0]  # (B, 1024)
            
            # 关键点预测
            pred = regressor(global_feat)  # (B, num_keypoints * 3)
            pred = pred.view(B, self.num_keypoints, 3)
            predictions.append(pred)
        
        # 加权集成
        weights = F.softmax(self.ensemble_weights, dim=0)
        ensemble_pred = sum(w * pred for w, pred in zip(weights, predictions))
        
        return ensemble_pred

class StableTrainer:
    """稳定训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def load_aligned_data(self):
        """加载对齐数据"""
        print("📦 加载F3对齐数据...")
        
        aligned_files = list(Path("data/processed").glob("f3_aligned_dataset_*.npz"))
        if not aligned_files:
            raise FileNotFoundError("未找到F3对齐数据集")
        
        latest_file = max(aligned_files, key=lambda x: x.stat().st_mtime)
        data = np.load(str(latest_file), allow_pickle=True)
        
        point_clouds = np.array(data['point_clouds'], dtype=np.float32)
        keypoints = np.array(data['keypoints'], dtype=np.float32)
        
        # 数据划分
        from sklearn.model_selection import train_test_split
        indices = np.arange(len(point_clouds))
        train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
        train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
        
        print(f"✅ 数据加载完成: {point_clouds.shape}")
        print(f"   训练: {len(train_indices)}, 验证: {len(val_indices)}, 测试: {len(test_indices)}")
        
        return self.data
    
    def conservative_augmentation(self, point_clouds, keypoints):
        """保守的数据增强"""
        aug_pcs = []
        aug_kps = []
        
        for pc, kp in zip(point_clouds, keypoints):
            # 原始数据
            aug_pcs.append(pc)
            aug_kps.append(kp)
            
            # 极小旋转 (±0.3度)
            for _ in range(2):
                angle = np.random.uniform(-0.005, 0.005)  # ±0.3度
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], dtype=np.float32)
                
                aug_pc = pc @ rotation.T
                aug_kp = kp @ rotation.T
                aug_pcs.append(aug_pc)
                aug_kps.append(aug_kp)
            
            # 极小噪声 (0.05mm)
            noise_pc = pc + np.random.normal(0, 0.05, pc.shape).astype(np.float32)
            aug_pcs.append(noise_pc)
            aug_kps.append(kp)
        
        return aug_pcs, aug_kps
    
    def train_stable_model(self, model_class, model_name, epochs=100, lr=0.0003):
        """稳定训练"""
        print(f"\n🚀 训练{model_name}")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        # 创建模型
        model = model_class(num_keypoints=19).to(self.device)
        
        # 计算参数
        total_params = sum(p.numel() for p in model.parameters())
        print(f"   模型参数: {total_params:,}")
        
        # 优化器
        optimizer = torch.optim.Adam(
            model.parameters(), 
            lr=lr, 
            weight_decay=1e-4,
            betas=(0.9, 0.999)
        )
        
        # 学习率调度器
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.7, patience=15, verbose=True
        )
        
        criterion = nn.MSELoss()
        
        # 训练状态
        best_val_error = float('inf')
        best_model_state = None
        patience = 0
        max_patience = 40
        
        for epoch in range(epochs):
            # 训练阶段
            model.train()
            epoch_losses = []
            
            # 使用更多训练数据
            k_shot = min(35, len(self.data['train']['point_clouds']))
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                k_shot, 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 保守增强
            aug_pcs, aug_kps = self.conservative_augmentation(train_pcs, train_kps)
            
            # 分批训练
            batch_size = 6
            for i in range(0, len(aug_pcs), batch_size):
                batch_pcs = torch.FloatTensor(aug_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(aug_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                pred_kps = model(batch_pcs)
                loss = criterion(pred_kps, batch_kps)
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.step()
                epoch_losses.append(loss.item())
                
                del batch_pcs, batch_kps, pred_kps, loss
                torch.cuda.empty_cache()
            
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            
            # 验证
            if epoch % 5 == 0:
                val_error = self.evaluate_model(model, 'val')
                scheduler.step(val_error)
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                    patience = 0
                else:
                    patience += 1
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm, "
                      f"LR={optimizer.param_groups[0]['lr']:.6f}, P={patience}")
                
                if patience >= max_patience:
                    print(f"早停在epoch {epoch}")
                    break
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)
            print(f"✅ 加载最佳模型 (验证误差: {best_val_error:.3f}mm)")
        
        return model, best_val_error
    
    def evaluate_model(self, model, split='test'):
        """评估模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for i in range(0, len(pcs), 2):
                batch_pcs = torch.FloatTensor(pcs[i:i+2]).to(self.device)
                batch_kps = torch.FloatTensor(kps[i:i+2]).to(self.device)
                
                pred_kps = model(batch_pcs)
                
                for j in range(len(batch_pcs)):
                    error = torch.mean(torch.norm(pred_kps[j] - batch_kps[j], dim=1))
                    total_error += error.item()
                    num_samples += 1
                
                del batch_pcs, batch_kps, pred_kps
                torch.cuda.empty_cache()
        
        return total_error / num_samples if num_samples > 0 else float('inf')

def run_stable_breakthrough():
    """运行稳定的突破性实验"""
    print("🚀 稳定突破性方法实验")
    print("=" * 60)
    print("目标: 稳定地突破8.13mm基线，冲击医疗级5mm")
    
    trainer = StableTrainer()
    data = trainer.load_aligned_data()
    
    # 测试两种稳定的突破性架构
    architectures = [
        (DeepPointNet, "深度PointNet"),
        (EnsemblePointNet, "集成PointNet")
    ]
    
    results = {}
    baseline_error = 8.13
    
    for model_class, model_name in architectures:
        print(f"\n{'='*60}")
        
        model, val_error = trainer.train_stable_model(
            model_class, model_name, epochs=100, lr=0.0003
        )
        
        test_error = trainer.evaluate_model(model, 'test')
        improvement = (baseline_error - test_error) / baseline_error * 100
        
        results[model_name] = {
            'validation_error': val_error,
            'test_error': test_error,
            'improvement_percent': improvement
        }
        
        print(f"\n📊 {model_name}结果:")
        print(f"   验证误差: {val_error:.3f}mm")
        print(f"   测试误差: {test_error:.3f}mm")
        print(f"   改进幅度: {improvement:+.1f}%")
        
        # 检查医疗级精度
        if test_error <= 5.0:
            print(f"🎉 {model_name}达到医疗级精度！")
        elif test_error <= 6.0:
            print(f"🎯 {model_name}接近医疗级精度！")
        elif improvement > 15:
            print(f"🚀 {model_name}取得显著进展！")
    
    # 总结
    print(f"\n📊 稳定突破实验总结:")
    print("=" * 60)
    print(f"基线: {baseline_error:.2f}mm")
    
    best_method = None
    best_error = float('inf')
    
    for method, result in results.items():
        error = result['test_error']
        improvement = result['improvement_percent']
        print(f"{method}: {error:.2f}mm ({improvement:+.1f}%)")
        
        if error < best_error:
            best_error = error
            best_method = method
    
    if best_method and best_error < baseline_error:
        print(f"\n🏆 最佳方法: {best_method} ({best_error:.2f}mm)")
        
        if best_error <= 5.0:
            print("🎉 成功达到医疗级精度！")
        elif best_error <= 6.0:
            print("🎯 非常接近医疗级精度！")
        else:
            remaining = best_error - 5.0
            print(f"📈 距离医疗级还需改进{remaining:.2f}mm")
    
    return trainer, results

if __name__ == "__main__":
    trainer, results = run_stable_breakthrough()
