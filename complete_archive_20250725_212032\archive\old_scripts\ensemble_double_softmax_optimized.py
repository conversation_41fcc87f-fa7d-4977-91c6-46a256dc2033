#!/usr/bin/env python3
"""
集成双Softmax优化版本
基于5.829mm最佳结果，进一步优化
目标: 从5.829mm突破到5.5mm甚至5.0mm
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class OptimizedDoubleSoftMax(nn.Module):
    """优化的双Softmax机制"""
    
    def __init__(self, threshold_ratio=0.15, temperature=2.0, weight_ratio=0.8):
        super(OptimizedDoubleSoftMax, self).__init__()
        
        self.threshold_ratio = threshold_ratio
        self.temperature = temperature
        self.weight_ratio = weight_ratio
        
        # 权重计算网络
        self.weight_net = nn.Sequential(
            nn.Linear(3, 64),
            nn.<PERSON><PERSON><PERSON>(),
            nn.<PERSON>chNorm1d(64),
            nn.<PERSON>ar(64, 32),
            nn.<PERSON>L<PERSON>(),
            nn.BatchNorm1d(32),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 1)
        )
        
    def forward(self, points, predicted_keypoint):
        """双Softmax权重计算"""
        # 计算相对位置
        relative_pos = points - predicted_keypoint.unsqueeze(0)
        
        # 第一个Softmax - 基于距离的权重
        distances = torch.norm(relative_pos, dim=1)
        distance_weights = F.softmax(-distances**2 / (2 * self.temperature**2), dim=0)
        
        # 第二个Softmax - 基于神经网络的权重
        if len(relative_pos) > 1:
            nn_weights = self.weight_net(relative_pos).squeeze(-1)
            nn_weights = F.softmax(nn_weights / self.temperature, dim=0)
        else:
            nn_weights = torch.ones_like(distance_weights)
        
        # 权重组合
        combined_weights = self.weight_ratio * distance_weights + (1 - self.weight_ratio) * nn_weights
        
        # 阈值过滤
        threshold = torch.quantile(combined_weights, 1 - self.threshold_ratio)
        filter_mask = combined_weights >= threshold
        
        # 确保至少保留一些点
        if filter_mask.sum() < 3:
            _, top_indices = torch.topk(combined_weights, min(5, len(combined_weights)))
            filter_mask = torch.zeros_like(combined_weights, dtype=torch.bool)
            filter_mask[top_indices] = True
        
        # 重新归一化
        filtered_weights = combined_weights * filter_mask.float()
        sum_weights = torch.sum(filtered_weights)
        
        if sum_weights > 1e-8:
            final_weights = filtered_weights / sum_weights
        else:
            final_weights = combined_weights / combined_weights.sum()
        
        # 加权平均得到精细化关键点
        refined_keypoint = torch.sum(final_weights.unsqueeze(-1) * points, dim=0)
        
        return refined_keypoint

class EnsembleDoubleSoftMaxPointNet(nn.Module):
    """集成双Softmax PointNet - 基于5.829mm成功配置"""
    
    def __init__(self, num_keypoints: int, dropout_rate: float = 0.3, num_ensembles: int = 3):
        super(EnsembleDoubleSoftMaxPointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        self.num_ensembles = num_ensembles
        
        # 基线架构 (完全保持成功配置)
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(dropout_rate)
        
        # 集成双Softmax模块 (基于成功配置)
        self.double_softmax_modules = nn.ModuleList([
            OptimizedDoubleSoftMax(
                threshold_ratio=0.10 + 0.05 * i,    # 0.10, 0.15, 0.20
                temperature=1.5 + 0.5 * i,          # 1.5, 2.0, 2.5
                weight_ratio=0.7 + 0.1 * i          # 0.7, 0.8, 0.9
            ) for i in range(num_ensembles)
        ])
        
        print(f"🧠 集成双Softmax PointNet: {num_keypoints}个关键点")
        print(f"   集成数量: {num_ensembles}个双Softmax模块")
        print(f"   基于5.829mm成功配置")
        
    def forward(self, x):
        batch_size = x.size(0)
        x_input = x.transpose(2, 1)
        
        # 基线模型前向传播 (完全一致)
        x1 = torch.relu(self.bn1(self.conv1(x_input)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        
        x3_res = x3 + self.residual1(x1)
        
        x4 = torch.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = torch.relu(self.bn5(self.conv5(x4_res)))
        
        global_feat = torch.max(x5, 2)[0]
        
        feat = torch.relu(self.bn_fc1(self.fc1(global_feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc2(self.fc2(feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc3(self.fc3(feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc4(self.fc4(feat)))
        feat = self.dropout(feat)
        feat = self.fc5(feat)
        
        keypoints = feat.view(batch_size, self.num_keypoints, 3)
        
        # 推理时应用集成双Softmax精细化
        if not self.training:
            keypoints = self.apply_ensemble_double_softmax_refinement(x, keypoints)
        
        return keypoints
    
    def apply_ensemble_double_softmax_refinement(self, points, predicted_keypoints):
        """应用集成双Softmax精细化"""
        batch_size = points.shape[0]
        refined_keypoints = []
        
        for b in range(batch_size):
            batch_points = points[b]
            batch_keypoints = predicted_keypoints[b]
            
            batch_refined = []
            for k in range(self.num_keypoints):
                kp_pred = batch_keypoints[k]
                
                # 选择候选点
                distances = torch.norm(batch_points - kp_pred.unsqueeze(0), dim=1)
                K = min(256, batch_points.shape[0])  # 候选点数量
                _, nearest_indices = torch.topk(distances, K, largest=False)
                candidate_points = batch_points[nearest_indices]
                
                # 集成多个双Softmax的结果
                ensemble_results = []
                for softmax_module in self.double_softmax_modules:
                    refined_kp = softmax_module(candidate_points, kp_pred)
                    ensemble_results.append(refined_kp)
                
                # 简单平均集成
                ensemble_keypoint = torch.stack(ensemble_results).mean(dim=0)
                batch_refined.append(ensemble_keypoint)
            
            refined_keypoints.append(torch.stack(batch_refined))
        
        return torch.stack(refined_keypoints)

class ReducedKeypointsF3Dataset(Dataset):
    """12关键点F3数据集 (复用成功配置)"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points
        self.augment = augment
        self.split = split
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
    
    def __len__(self):
        return len(self.sample_ids)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        # 数据增强 (保持成功配置)
        if self.augment and self.split == 'train':
            if np.random.random() < 0.7:
                angle = np.random.uniform(-0.08, 0.08)
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                point_cloud = point_cloud @ rotation.T
                keypoints = keypoints @ rotation.T
            
            if np.random.random() < 0.6:
                translation = np.random.uniform(-0.4, 0.4, 3)
                point_cloud += translation
                keypoints += translation
            
            if np.random.random() < 0.5:
                scale = np.random.uniform(0.99, 1.01, 3)
                point_cloud *= scale
                keypoints *= scale
            
            if np.random.random() < 0.6:
                noise_level = np.random.choice([0.02, 0.03, 0.04])
                noise = np.random.normal(0, noise_level, point_cloud.shape)
                point_cloud += noise
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def test_ensemble_double_softmax():
    """测试集成双Softmax模型"""
    
    print("🧪 **测试集成双Softmax模型**")
    print("🎯 **基于5.829mm成功配置**")
    print("=" * 80)
    
    batch_size = 4
    num_points = 4096
    num_keypoints = 12
    
    # 创建测试数据
    test_input = torch.randn(batch_size, num_points, 3)
    
    print(f"📊 测试输入: {test_input.shape}")
    
    # 测试模型
    model = EnsembleDoubleSoftMaxPointNet(num_keypoints=num_keypoints, num_ensembles=3)
    
    with torch.no_grad():
        # 训练模式
        model.train()
        output_train = model(test_input)
        print(f"   训练模式输出: {output_train.shape}")
        
        # 推理模式
        model.eval()
        output_eval = model(test_input)
        print(f"   推理模式输出: {output_eval.shape}")
    
    # 参数统计
    total_params = sum(p.numel() for p in model.parameters())
    print(f"\n📊 模型参数: {total_params:,}")
    
    print(f"\n✅ 集成双Softmax测试通过!")
    
    return model

if __name__ == "__main__":
    set_seed(42)
    
    print("🚀 **集成双Softmax优化版本**")
    print("📚 **基于5.829mm最佳结果**")
    print("🎯 **目标: 突破5.5mm甚至5.0mm**")
    print("=" * 80)
    
    # 测试模型
    model = test_ensemble_double_softmax()
    
    print(f"\n🎉 **集成双Softmax准备完成!**")
    print("=" * 50)
    print(f"🔬 核心优势: 经过验证的最佳方法")
    print(f"🎯 当前最佳: 5.829mm")
    print(f"🏆 目标精度: 5.0mm医疗级")
    print(f"📈 优化策略: 精细参数调优 + 更好的集成")
