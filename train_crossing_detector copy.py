import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import cv2
import matplotlib.pyplot as plt
from typing import List, Tuple
import json
import os
import random
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
matplotlib.rcParams['axes.unicode_minus'] = False

class CrossingPointDataset(Dataset):
    def __init__(self, image_dir: str, label_file: str, transform=None):
        """
        数据集类
        Args:
            image_dir: 图像目录
            label_file: 标注文件路径 (JSON格式)
            transform: 数据增强转换
        """
        self.image_dir = image_dir
        self.transform = transform
        
        # 加载标注数据
        with open(label_file, 'r') as f:
            self.annotations = json.load(f)
            
    def __len__(self):
        return len(self.annotations)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, List[Tuple[int, int]]]:
        """
        获取数据项
        Args:
            idx: 数据索引
        Returns:
            图像张量和交叉点列表
        """
        image_name = list(self.annotations.keys())[idx]
        image_path = os.path.join(self.image_dir, image_name)
        image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
        crossing_points = self.annotations[image_name]['crossing_points']
        
        if self.transform:
            image = self.transform(image)
        
        return torch.FloatTensor(image).unsqueeze(0), crossing_points

def generate_random_connected_shapes(image_dir: str, num_images: int, image_size: Tuple[int, int] = (256, 256)):
    for i in range(num_images):
        image = np.zeros(image_size, dtype=np.uint8)
        num_points = random.randint(5, 15)
        points = [(random.randint(0, image_size[0]-1), random.randint(0, image_size[1]-1)) for _ in range(num_points)]
        
        # 画点
        for point in points:
            cv2.circle(image, point, 3, 255, -1)
        
        # 画线，确保图形是连通的
        for j in range(num_points - 1):
            cv2.line(image, points[j], points[j+1], 255, 1)
        
        # 连接最后一个点和第一个点，确保图形是连通的
        cv2.line(image, points[-1], points[0], 255, 1)
        
        # 保存图像
        image_name = f'image_{i}.png'
        image_path = os.path.join(image_dir, image_name)
        cv2.imwrite(image_path, image)
        
        # 显示图像以便手动标记
        plt.imshow(image, cmap='gray')
        plt.title(f'Image {i}')
        plt.show()
        
        # 等待用户标记
        input(f"请标记图像 {image_name} 的奇偶点，然后按 Enter 键继续...")

if __name__ == "__main__":
    image_dir = 'dataset/images'
    num_images = 10
    os.makedirs(image_dir, exist_ok=True)
    generate_random_connected_shapes(image_dir, num_images)

    print(f"已生成 {num_images} 个随机连通图形并保存在 {image_dir} 目录中。")