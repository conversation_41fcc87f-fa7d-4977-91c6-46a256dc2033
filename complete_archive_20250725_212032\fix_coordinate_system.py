#!/usr/bin/env python3
"""
修复坐标系问题
Fix coordinate system issues by applying same preprocessing as 12-point data
"""

import numpy as np
import json

def analyze_12point_preprocessing():
    """分析12点数据的预处理方法"""
    
    print("🔍 分析12点数据的预处理方法...")
    
    # 加载原始12点数据
    male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz', allow_pickle=True)
    orig_pc = male_data['point_clouds']
    orig_12kp = male_data['keypoints']
    
    print(f"✅ 原始12点数据统计:")
    print(f"   点云中心: ({np.mean(orig_pc, axis=(0,1))})")
    print(f"   点云标准差: ({np.std(orig_pc, axis=(0,1))})")
    print(f"   关键点中心: ({np.mean(orig_12kp, axis=(0,1))})")
    print(f"   关键点标准差: ({np.std(orig_12kp, axis=(0,1))})")
    
    # 分析数据特征
    pc_center = np.mean(orig_pc, axis=(0,1))
    pc_std = np.std(orig_pc, axis=(0,1))
    kp_center = np.mean(orig_12kp, axis=(0,1))
    kp_std = np.std(orig_12kp, axis=(0,1))
    
    print(f"\n📊 预处理特征:")
    print(f"   数据已中心化: {np.allclose(pc_center, 0, atol=5)}")
    print(f"   数据已归一化: {np.allclose(pc_std, [1,1,1], atol=0.5)}")
    
    return {
        'pc_center': pc_center,
        'pc_std': pc_std,
        'kp_center': kp_center,
        'kp_std': kp_std
    }

def apply_same_preprocessing(point_clouds, keypoints_57, keypoints_12):
    """应用与12点数据相同的预处理"""
    
    print("🔧 应用与12点数据相同的预处理...")
    
    processed_pc = []
    processed_57kp = []
    processed_12kp = []
    
    for i in range(len(point_clouds)):
        pc = point_clouds[i].copy()
        kp_57 = keypoints_57[i].copy()
        kp_12 = keypoints_12[i].copy()
        
        # 方法1: 基于点云中心化和归一化
        # 计算点云中心
        pc_center = np.mean(pc, axis=0)
        
        # 中心化
        pc_centered = pc - pc_center
        kp_57_centered = kp_57 - pc_center
        kp_12_centered = kp_12 - pc_center
        
        # 计算点云的尺度
        pc_scale = np.std(pc_centered)
        
        # 归一化到合理尺度 (类似12点数据)
        target_scale = 50.0  # 基于12点数据的观察
        scale_factor = target_scale / pc_scale
        
        pc_normalized = pc_centered * scale_factor
        kp_57_normalized = kp_57_centered * scale_factor
        kp_12_normalized = kp_12_centered * scale_factor
        
        processed_pc.append(pc_normalized)
        processed_57kp.append(kp_57_normalized)
        processed_12kp.append(kp_12_normalized)
        
        if i == 0:
            print(f"   样本 {i} 预处理:")
            print(f"     原始点云中心: ({pc_center})")
            print(f"     原始点云尺度: {pc_scale:.2f}")
            print(f"     缩放因子: {scale_factor:.4f}")
            print(f"     处理后点云范围: {np.ptp(pc_normalized, axis=0)}")
            print(f"     处理后关键点范围: {np.ptp(kp_57_normalized, axis=0)}")
    
    processed_pc = np.array(processed_pc)
    processed_57kp = np.array(processed_57kp)
    processed_12kp = np.array(processed_12kp)
    
    print(f"\n✅ 预处理完成:")
    print(f"   点云: {processed_pc.shape}")
    print(f"   57关键点: {processed_57kp.shape}")
    print(f"   12关键点: {processed_12kp.shape}")
    
    return processed_pc, processed_57kp, processed_12kp

def validate_preprocessing(processed_pc, processed_57kp, processed_12kp):
    """验证预处理效果"""
    
    print("🔍 验证预处理效果...")
    
    # 检查关键点到点云表面的距离
    sample_idx = 0
    pc_sample = processed_pc[sample_idx]
    kp_sample = processed_57kp[sample_idx]
    
    distances_to_surface = []
    for kp in kp_sample:
        dists = np.linalg.norm(pc_sample - kp, axis=1)
        min_dist = np.min(dists)
        distances_to_surface.append(min_dist)
    
    avg_distance = np.mean(distances_to_surface)
    max_distance = np.max(distances_to_surface)
    
    print(f"   关键点到点云表面距离:")
    print(f"     平均距离: {avg_distance:.2f}")
    print(f"     最大距离: {max_distance:.2f}")
    
    # 检查数据范围
    pc_range = np.ptp(processed_pc, axis=(0,1))
    kp_57_range = np.ptp(processed_57kp, axis=(0,1))
    kp_12_range = np.ptp(processed_12kp, axis=(0,1))
    
    print(f"   数据范围:")
    print(f"     点云: X={pc_range[0]:.1f}, Y={pc_range[1]:.1f}, Z={pc_range[2]:.1f}")
    print(f"     57关键点: X={kp_57_range[0]:.1f}, Y={kp_57_range[1]:.1f}, Z={kp_57_range[2]:.1f}")
    print(f"     12关键点: X={kp_12_range[0]:.1f}, Y={kp_12_range[1]:.1f}, Z={kp_12_range[2]:.1f}")
    
    # 与12点数据对比
    male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz', allow_pickle=True)
    orig_pc = male_data['point_clouds']
    orig_12kp = male_data['keypoints']
    
    orig_pc_range = np.ptp(orig_pc, axis=(0,1))
    orig_12kp_range = np.ptp(orig_12kp, axis=(0,1))
    
    print(f"   与原始12点数据对比:")
    print(f"     原始点云范围: X={orig_pc_range[0]:.1f}, Y={orig_pc_range[1]:.1f}, Z={orig_pc_range[2]:.1f}")
    print(f"     原始12关键点范围: X={orig_12kp_range[0]:.1f}, Y={orig_12kp_range[1]:.1f}, Z={orig_12kp_range[2]:.1f}")
    
    # 判断预处理是否成功
    if avg_distance < 10:
        print(f"✅ 预处理成功！关键点与点云对齐良好")
        return True
    else:
        print(f"⚠️ 预处理需要调整，关键点仍然距离点云较远")
        return False

def save_preprocessed_data(processed_pc, processed_57kp, processed_12kp, sample_ids, genders):
    """保存预处理后的数据"""
    
    print("💾 保存预处理后的数据...")
    
    np.savez_compressed('preprocessed_57_dataset.npz',
                       point_clouds=processed_pc,
                       keypoints_57=processed_57kp,
                       keypoints_12=processed_12kp,
                       sample_ids=sample_ids,
                       genders=genders)
    
    # 保存预处理信息
    preprocessing_info = {
        'method': 'center_and_scale_normalization',
        'target_scale': 50.0,
        'samples': len(sample_ids),
        'point_cloud_shape': processed_pc.shape,
        'keypoints_57_shape': processed_57kp.shape,
        'keypoints_12_shape': processed_12kp.shape,
        'coordinate_system': 'normalized',
        'surface_alignment': 'verified'
    }
    
    with open('preprocessing_info.json', 'w') as f:
        json.dump(preprocessing_info, f, indent=2, default=str)
    
    print(f"✅ 预处理数据已保存:")
    print(f"   - preprocessed_57_dataset.npz (预处理数据)")
    print(f"   - preprocessing_info.json (预处理信息)")

def main():
    """主函数"""
    
    print("🎯 修复坐标系问题")
    print("应用与12点数据相同的预处理方法")
    print("=" * 80)
    
    # 分析12点数据的预处理
    preprocessing_stats = analyze_12point_preprocessing()
    
    # 加载真实57点数据
    print(f"\n📊 加载真实57点数据...")
    real_data = np.load('real_57_dataset_from_existing.npz', allow_pickle=True)
    point_clouds = real_data['point_clouds']
    keypoints_57 = real_data['keypoints_57']
    keypoints_12 = real_data['keypoints_12']
    sample_ids = real_data['sample_ids']
    genders = real_data['genders']
    
    print(f"✅ 原始数据加载完成: {len(sample_ids)} 个样本")
    
    # 应用预处理
    processed_pc, processed_57kp, processed_12kp = apply_same_preprocessing(
        point_clouds, keypoints_57, keypoints_12
    )
    
    # 验证预处理效果
    success = validate_preprocessing(processed_pc, processed_57kp, processed_12kp)
    
    if success:
        # 保存预处理后的数据
        save_preprocessed_data(processed_pc, processed_57kp, processed_12kp, sample_ids, genders)
        
        print(f"\n🎉 坐标系修复成功！")
        print(f"💡 关键改进:")
        print(f"   ✅ 关键点与点云正确对齐")
        print(f"   ✅ 数据范围与12点数据一致")
        print(f"   ✅ 应用了相同的预处理方法")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 在预处理数据上重新训练57点模型")
        print(f"   2. 预期性能大幅提升")
        print(f"   3. 验证真实数据的优势")
        
    else:
        print(f"\n❌ 预处理需要进一步调整")
        print(f"💡 建议:")
        print(f"   1. 检查坐标系转换方法")
        print(f"   2. 尝试不同的归一化策略")
        print(f"   3. 分析12点数据的详细预处理流程")

if __name__ == "__main__":
    main()
