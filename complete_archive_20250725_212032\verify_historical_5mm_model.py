#!/usr/bin/env python3
"""
验证历史5mm模型
Verify Historical 5mm Model
直接加载和测试历史最佳模型
"""

import torch
import torch.nn as nn
import numpy as np
import os
from pathlib import Path

def analyze_historical_model():
    """分析历史模型"""
    
    print("🔍 分析历史5.371mm模型")
    print("=" * 60)
    
    # 历史最佳模型路径
    historical_model_path = "archive/old_models/best_exact_ensemble_seed123_5.371mm.pth"
    
    if not os.path.exists(historical_model_path):
        print(f"❌ 历史模型文件不存在: {historical_model_path}")
        return False
    
    try:
        # 加载历史模型
        print(f"📂 加载历史模型: {historical_model_path}")
        checkpoint = torch.load(historical_model_path, map_location='cpu')
        
        print(f"✅ 模型加载成功")
        
        # 分析checkpoint结构
        if isinstance(checkpoint, dict):
            print(f"\n📋 Checkpoint内容:")
            for key, value in checkpoint.items():
                if key == 'model_state_dict':
                    state_dict = value
                    print(f"   - {key}: {len(state_dict)} 个参数层")
                    
                    # 分析模型结构
                    print(f"\n🏗️ 模型结构分析:")
                    total_params = 0
                    conv_layers = 0
                    fc_layers = 0
                    
                    for param_name, param_tensor in state_dict.items():
                        total_params += param_tensor.numel()
                        
                        if 'conv' in param_name and 'weight' in param_name:
                            conv_layers += 1
                            print(f"     Conv层 {conv_layers}: {param_name} -> {param_tensor.shape}")
                        elif 'fc' in param_name and 'weight' in param_name:
                            fc_layers += 1
                            print(f"     FC层 {fc_layers}: {param_name} -> {param_tensor.shape}")
                    
                    print(f"\n   总参数数: {total_params:,}")
                    print(f"   卷积层数: {conv_layers}")
                    print(f"   全连接层数: {fc_layers}")
                    
                elif key in ['epoch', 'best_val_error', 'val_metrics']:
                    print(f"   - {key}: {value}")
                elif key == 'optimizer_state_dict':
                    print(f"   - {key}: 优化器状态")
                else:
                    print(f"   - {key}: {type(value)}")
        
        # 尝试推断输出维度
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
            
            # 查找最后的全连接层
            last_fc_layers = []
            for param_name, param_tensor in state_dict.items():
                if 'fc' in param_name and 'weight' in param_name:
                    last_fc_layers.append((param_name, param_tensor.shape))
            
            if last_fc_layers:
                last_layer_name, last_layer_shape = last_fc_layers[-1]
                output_dim = last_layer_shape[0]
                
                print(f"\n🎯 输出分析:")
                print(f"   最后FC层: {last_layer_name}")
                print(f"   输出维度: {output_dim}")
                
                # 推断关键点数量
                if output_dim % 3 == 0:
                    num_keypoints = output_dim // 3
                    print(f"   推断关键点数: {num_keypoints}")
                    
                    if num_keypoints == 12:
                        print(f"   ✅ 确认是12点模型")
                    elif num_keypoints == 57:
                        print(f"   ⚠️ 这是57点模型？")
                    else:
                        print(f"   ❓ 未知的关键点数量")
                else:
                    print(f"   ❓ 输出维度不是3的倍数")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型分析失败: {e}")
        return False

def check_historical_dataset():
    """检查历史数据集"""
    
    print(f"\n🔍 检查可能的历史数据集")
    print("=" * 60)
    
    # 可能的历史数据集文件
    possible_datasets = [
        "f3_reduced_12kp_stable.npz",
        "archive/old_datasets/f3_reduced_12kp_stable.npz",
        "trained_models/results/f3_complete_dataset_info.json"
    ]
    
    found_datasets = []
    
    for dataset_path in possible_datasets:
        if os.path.exists(dataset_path):
            found_datasets.append(dataset_path)
            print(f"✅ 找到数据集: {dataset_path}")
            
            try:
                if dataset_path.endswith('.npz'):
                    data = np.load(dataset_path, allow_pickle=True)
                    print(f"   数据集内容:")
                    for key in data.keys():
                        array = data[key]
                        if hasattr(array, 'shape'):
                            print(f"     {key}: {array.shape}")
                        else:
                            print(f"     {key}: {type(array)}")
                elif dataset_path.endswith('.json'):
                    import json
                    with open(dataset_path, 'r') as f:
                        info = json.load(f)
                    print(f"   数据集信息:")
                    for key, value in info.items():
                        if isinstance(value, dict):
                            print(f"     {key}: {type(value)} with {len(value)} keys")
                        else:
                            print(f"     {key}: {value}")
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
        else:
            print(f"❌ 未找到: {dataset_path}")
    
    if not found_datasets:
        print(f"⚠️ 未找到任何历史数据集文件")
        return False
    
    return True

def compare_with_current_approach():
    """与当前方法对比"""
    
    print(f"\n📊 方法对比分析")
    print("=" * 60)
    
    comparison = {
        "历史5.371mm模型": {
            "关键点数": "12点",
            "数据集": "未知(可能是f3_reduced_12kp_stable)",
            "架构": "exact_ensemble_seed123",
            "性能": "5.371mm",
            "状态": "已存在但无法复现"
        },
        "我们的12点模型": {
            "关键点数": "12点",
            "数据集": "high_quality_pelvis_57_dataset筛选",
            "架构": "AdaptivePointNet",
            "性能": "14.91mm",
            "状态": "可复现但性能差"
        },
        "我们的57点模型": {
            "关键点数": "57点",
            "数据集": "high_quality_pelvis_57_dataset",
            "架构": "UltimatePointNet57",
            "性能": "10.89mm",
            "状态": "最佳但仍不如历史12点"
        }
    }
    
    print(f"{'方法':<20} {'关键点':<8} {'性能':<10} {'状态'}")
    print("-" * 60)
    
    for method, info in comparison.items():
        print(f"{method:<20} {info['关键点']:<8} {info['性能']:<10} {info['状态']}")
    
    print(f"\n💡 关键问题:")
    print(f"   1. 我们无法复现历史5.371mm的12点性能")
    print(f"   2. 即使我们的57点(10.89mm)也不如历史12点(5.371mm)")
    print(f"   3. 这表明存在根本性的差异")

def investigate_possible_causes():
    """调查可能的原因"""
    
    print(f"\n🔍 可能原因调查")
    print("=" * 60)
    
    possible_causes = [
        {
            "原因": "数据集完全不同",
            "可能性": "高",
            "说明": "历史模型可能使用了不同来源、不同标注方法的数据集",
            "验证方法": "寻找原始历史数据集"
        },
        {
            "原因": "评估方法不同",
            "可能性": "中",
            "说明": "不同的误差计算方法、坐标系、或评估标准",
            "验证方法": "检查历史评估代码"
        },
        {
            "原因": "模型架构差异",
            "可能性": "中",
            "说明": "我们没有完全复现历史最佳架构的关键细节",
            "验证方法": "深入分析历史模型结构"
        },
        {
            "原因": "训练策略差异",
            "可能性": "中",
            "说明": "不同的数据预处理、训练技巧、超参数",
            "验证方法": "寻找历史训练代码"
        },
        {
            "原因": "任务定义差异",
            "可能性": "高",
            "说明": "历史12点可能是更简单的任务或不同的关键点选择",
            "验证方法": "确认历史任务的具体定义"
        }
    ]
    
    print(f"{'原因':<20} {'可能性':<8} {'说明'}")
    print("-" * 80)
    
    for cause in possible_causes:
        print(f"{cause['原因']:<20} {cause['可能性']:<8} {cause['说明']}")
    
    print(f"\n🎯 最可能的解释:")
    print(f"   历史5.371mm模型很可能:")
    print(f"   1. 使用了完全不同的数据集")
    print(f"   2. 或者是在不同的任务定义下")
    print(f"   3. 我们当前的任务可能本质上更困难")

def main():
    """主函数"""
    
    print("🎯 验证历史5mm模型")
    print("直接分析历史最佳模型，找出差异原因")
    print("=" * 80)
    
    # 1. 分析历史模型
    model_analyzed = analyze_historical_model()
    
    # 2. 检查历史数据集
    dataset_found = check_historical_dataset()
    
    # 3. 对比分析
    compare_with_current_approach()
    
    # 4. 调查原因
    investigate_possible_causes()
    
    print(f"\n🎯 结论:")
    if model_analyzed:
        print(f"   ✅ 历史5.371mm模型确实存在")
        print(f"   ❌ 但我们无法复现其性能")
        print(f"   💡 这表明存在根本性的任务或数据差异")
    else:
        print(f"   ❌ 无法分析历史模型")
    
    print(f"\n💭 您的质疑完全正确:")
    print(f"   ✅ 我们确实没有复现出5mm性能")
    print(f"   ✅ 即使是12点也达不到历史水平")
    print(f"   ✅ 这说明问题比我们想象的更复杂")
    
    print(f"\n🚀 下一步建议:")
    print(f"   1. 寻找并使用历史数据集")
    print(f"   2. 深入分析历史模型架构")
    print(f"   3. 重新定义任务和评估标准")
    print(f"   4. 承认当前方法的局限性")

if __name__ == "__main__":
    main()
