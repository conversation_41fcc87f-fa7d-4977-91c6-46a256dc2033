<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>

  <rect width="1280" height="720" fill="url(#bgGrad)"/>

  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="url(#headerGrad)"/>
  <text x="640" y="50" text-anchor="middle" fill="white"
        font-family="SimHei, Arial, sans-serif" font-size="36" font-weight="bold">
    加权平均的核心作用机制
  </text>

  <!-- Main content -->
  <rect x="50" y="100" width="1180" height="580" rx="15" fill="white" stroke="#374151" stroke-width="2"/>

  <!-- Core concept -->
  <rect x="80" y="130" width="1120" height="100" rx="10" fill="#f0f9ff" stroke="#3b82f6" stroke-width="2"/>
  <text x="640" y="160" text-anchor="middle" fill="#1e40af"
        font-family="SimHei, Arial, sans-serif" font-size="24" font-weight="bold">
    核心概念：加权平均实现亚像素级精确定位
  </text>
  <text x="640" y="190" text-anchor="middle" fill="#374151"
        font-family="SimHei, Arial, sans-serif" font-size="16">
    通过双SoftMax机制计算精确权重，将多个高质量点的坐标进行加权组合
  </text>
  <text x="640" y="215" text-anchor="middle" fill="#1e40af"
        font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    kp_i = Σ(ω_j · WS(ω_j) · coord_j) / Σ(ω_j · WS(ω_j))
  </text>

  <!-- Three main sections -->
  <!-- Section 1: Mathematical principle -->
  <rect x="80" y="250" width="360" height="400" rx="10" fill="#fef7ff" stroke="#8b5cf6" stroke-width="2"/>
  <text x="260" y="280" text-anchor="middle" fill="#7c3aed"
        font-family="SimHei, Arial, sans-serif" font-size="18" font-weight="bold">
    🔢 数学原理
  </text>

  <!-- Mathematical details -->
  <text x="90" y="310" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    双SoftMax权重计算：
  </text>
  <text x="90" y="335" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    第一次：ω_j = exp(f_j) / Σexp(f_k)
  </text>
  <text x="90" y="355" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    阈值过滤：保留ω_j ≥ 1/k - ε的点
  </text>
  <text x="90" y="375" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    第二次：WS(ω_j) = 1/(1+exp(-M·(ω_j-θ)))
  </text>

  <text x="90" y="410" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    加权平均公式：
  </text>
  <rect x="90" y="420" width="340" height="40" rx="5" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="260" y="445" text-anchor="middle" fill="#7c3aed"
        font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    kp_i = Σ(ω_j·WS(ω_j)·coord_j) / Σ(ω_j·WS(ω_j))
  </text>

  <text x="90" y="485" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    关键特性：
  </text>
  <text x="90" y="510" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 连续空间定位：结果不限于原始点
  </text>
  <text x="90" y="530" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 权重归一化：确保数值稳定性
  </text>
  <text x="90" y="550" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 选择性强：只有高权重点参与
  </text>
  <text x="90" y="570" fill="#7c3aed" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    • 亚像素精度：突破离散限制
  </text>

  <text x="90" y="605" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    数值示例：
  </text>
  <text x="90" y="625" fill="#374151" font-family="Arial, sans-serif" font-size="11">
    权重[0.4, 0.35, 0.25] × 坐标[(1,2,3), (2,3,4), (3,4,5)]
    = (1.75, 2.75, 3.75) ← 新的精确坐标
  </text>

  <!-- Section 2: Practical advantages -->
  <rect x="460" y="250" width="360" height="400" rx="10" fill="#f0fdf4" stroke="#22c55e" stroke-width="2"/>
  <text x="640" y="280" text-anchor="middle" fill="#15803d"
        font-family="SimHei, Arial, sans-serif" font-size="18" font-weight="bold">
    🎯 实际优势
  </text>

  <text x="470" y="310" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    1. 亚像素级精度
  </text>
  <text x="470" y="335" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 突破离散点云的限制
  </text>
  <text x="470" y="355" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 在连续空间中精确定位
  </text>
  <text x="470" y="375" fill="#15803d" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    • 医学应用中毫米级精度提升
  </text>

  <text x="470" y="410" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    2. 抗噪声稳定性
  </text>
  <text x="470" y="435" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 多点平均降低单点误差影响
  </text>
  <text x="470" y="455" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 双SoftMax过滤低质量点
  </text>
  <text x="470" y="475" fill="#15803d" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    • 提高预测的鲁棒性
  </text>

  <text x="470" y="510" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    3. 权重选择性
  </text>
  <text x="470" y="535" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 阈值机制过滤无关点
  </text>
  <text x="470" y="555" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 放大重要点的影响力
  </text>
  <text x="470" y="575" fill="#15803d" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    • 实现智能化权重分配
  </text>

  <text x="470" y="610" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    实验验证：
  </text>
  <rect x="470" y="620" width="340" height="25" rx="5" fill="#dcfce7" stroke="#22c55e" stroke-width="1"/>
  <text x="640" y="637" text-anchor="middle" fill="#15803d"
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    相比单一SoftMax提升0.19mm精度
  </text>

  <!-- Section 3: Visual example -->
  <rect x="840" y="250" width="360" height="400" rx="10" fill="#fff7ed" stroke="#f59e0b" stroke-width="2"/>
  <text x="1020" y="280" text-anchor="middle" fill="#d97706"
        font-family="SimHei, Arial, sans-serif" font-size="18" font-weight="bold">
    📊 可视化示例
  </text>

  <!-- Visual example content -->
  <text x="850" y="310" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    区域内5个点的权重分布：
  </text>

  <!-- Before: uniform weights -->
  <rect x="850" y="325" width="340" height="80" rx="5" fill="#fef2f2" stroke="#f87171" stroke-width="1"/>
  <text x="1020" y="345" text-anchor="middle" fill="#dc2626"
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    单一SoftMax：权重均匀化
  </text>
  <text x="860" y="365" fill="#374151" font-family="Arial, sans-serif" font-size="11">
    点1: 0.22  点2: 0.20  点3: 0.18  点4: 0.24  点5: 0.16
  </text>
  <text x="860" y="385" fill="#dc2626" font-family="SimHei, Arial, sans-serif" font-size="11">
    权重差异小，所有点几乎等权重参与
  </text>

  <!-- After: sharp weights -->
  <rect x="850" y="415" width="340" height="80" rx="5" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="1020" y="435" text-anchor="middle" fill="#15803d"
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    双SoftMax：权重尖锐化
  </text>
  <text x="860" y="455" fill="#374151" font-family="Arial, sans-serif" font-size="11">
    点1: 0.32  点2: 0.30  点3: 0.00  点4: 0.38  点5: 0.00
  </text>
  <text x="860" y="475" fill="#15803d" font-family="SimHei, Arial, sans-serif" font-size="11">
    只有高权重点参与，权重差异明显
  </text>

  <!-- Final calculation -->
  <text x="850" y="515" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    最终关键点计算：
  </text>
  <rect x="850" y="525" width="340" height="60" rx="5" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="860" y="545" fill="#374151" font-family="Arial, sans-serif" font-size="11">
    kp = 0.32×P₁ + 0.30×P₂ + 0.38×P₄
  </text>
  <text x="860" y="565" fill="#7c3aed" font-family="SimHei, Arial, sans-serif" font-size="11" font-weight="bold">
    = 精确的亚像素级关键点坐标
  </text>

  <!-- Key insight -->
  <text x="850" y="605" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    核心洞察：
  </text>
  <text x="850" y="625" fill="#d97706" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    加权平均 = 智能插值 + 噪声抑制
  </text>
  <text x="850" y="645" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="11">
    在连续空间中找到最优解
  </text>
</svg>
