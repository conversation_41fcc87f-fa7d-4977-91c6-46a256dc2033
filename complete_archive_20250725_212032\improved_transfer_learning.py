#!/usr/bin/env python3
"""
改进的迁移学习方案
Improved Transfer Learning Approach
分析失败原因并提出更好的解决方案
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
import json
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

class TransferLearningAnalyzer:
    """迁移学习分析器"""
    
    def __init__(self):
        self.analysis_results = {}
        
    def analyze_transfer_failure(self):
        """分析迁移学习失败的原因"""
        print("🔍 分析迁移学习失败原因")
        print("=" * 60)
        
        failure_analysis = {
            "实验结果": {
                "基线模型": "6.60mm (简化通用模型)",
                "迁移学习模型": "10.42mm (PointNet++迁移)",
                "性能下降": "3.82mm (57.9%下降)",
                "结论": "迁移学习失败"
            },
            
            "失败原因分析": {
                "1. 模拟预训练权重问题": [
                    "没有使用真实的预训练权重",
                    "模拟的权重可能不如随机初始化",
                    "缺乏大规模数据集的预训练知识"
                ],
                
                "2. 架构复杂度过高": [
                    "166万参数对97样本数据集过于复杂",
                    "严重过拟合，泛化能力差",
                    "训练数据不足以支撑如此复杂的模型"
                ],
                
                "3. 迁移策略不当": [
                    "冻结-解冻策略可能不适合小数据集",
                    "学习率设置可能不合适",
                    "缺乏领域适配的有效机制"
                ],
                
                "4. 数据集特异性": [
                    "医疗关键点检测任务特异性强",
                    "通用3D特征可能不适用",
                    "需要更多医疗领域的先验知识"
                ]
            },
            
            "关键洞察": [
                "小数据集不适合复杂的迁移学习模型",
                "简单有效的架构比复杂的预训练模型更好",
                "MutualAssistanceNet的相互辅助机制更适合小数据集",
                "数据质量和架构设计比预训练权重更重要"
            ]
        }
        
        print("❌ 失败原因:")
        for category, reasons in failure_analysis["失败原因分析"].items():
            print(f"  {category}:")
            for reason in reasons:
                print(f"    • {reason}")
        
        print(f"\n💡 关键洞察:")
        for insight in failure_analysis["关键洞察"]:
            print(f"  • {insight}")
        
        return failure_analysis

class LightweightTransferModel(nn.Module):
    """轻量级迁移学习模型"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super().__init__()
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 轻量级特征提取器 (基于成功的MutualAssistanceNet)
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
        )
        
        # 知识蒸馏适配层
        self.knowledge_adapter = nn.Sequential(
            nn.Conv1d(256, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.1),
        )
        
        # 相互辅助机制 (从成功模型借鉴)
        self.mutual_assistance = nn.Sequential(
            nn.Linear(num_keypoints * 3, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, num_keypoints * 3)
        )
        
        # 关键点预测头
        self.keypoint_predictor = nn.Sequential(
            nn.Linear(256, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, num_keypoints * 3)
        )
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取
        features = self.feature_extractor(x)  # [B, 256, N]
        
        # 知识适配
        adapted_features = self.knowledge_adapter(features)  # [B, 256, N]
        
        # 全局特征
        global_features = torch.max(adapted_features, 2)[0]  # [B, 256]
        
        # 初始预测
        initial_kp = self.keypoint_predictor(global_features)  # [B, num_keypoints*3]
        
        # 相互辅助
        assistance = self.mutual_assistance(initial_kp)  # [B, num_keypoints*3]
        
        # 最终预测 (残差连接)
        final_kp = initial_kp + 0.3 * assistance
        final_kp = final_kp.view(batch_size, self.num_keypoints, 3)
        
        return final_kp

class ImprovedTransferTrainer:
    """改进的迁移学习训练器"""
    
    def __init__(self, device='cuda:1'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        
    def train_lightweight_transfer_model(self):
        """训练轻量级迁移学习模型"""
        print("\n🎯 训练轻量级迁移学习模型")
        print("=" * 50)
        
        try:
            # 加载数据
            female_data = np.load('archive/old_experiments/f3_reduced_12kp_female.npz')
            female_pc = female_data['point_clouds']
            female_kp = female_data['keypoints']
            
            male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz')
            male_pc = male_data['point_clouds']
            male_kp = male_data['keypoints']
            
            # 合并数据
            all_pc = np.vstack([female_pc, male_pc])
            all_kp = np.vstack([female_kp, male_kp])
            
            # 数据分割
            train_pc, test_pc, train_kp, test_kp = train_test_split(
                all_pc, all_kp, test_size=0.2, random_state=42)
            
            print(f"📊 数据分割:")
            print(f"   训练: {len(train_pc)}样本")
            print(f"   测试: {len(test_pc)}样本")
            
            # 创建轻量级模型
            model = LightweightTransferModel(num_points=50000, num_keypoints=12).to(self.device)
            criterion = nn.MSELoss()
            optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
            
            print(f"🏗️ 轻量级迁移模型:")
            print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
            
            # 转换为张量
            train_pc_tensor = torch.FloatTensor(train_pc).to(self.device)
            train_kp_tensor = torch.FloatTensor(train_kp).to(self.device)
            test_pc_tensor = torch.FloatTensor(test_pc).to(self.device)
            test_kp_tensor = torch.FloatTensor(test_kp).to(self.device)
            
            # 创建数据加载器
            batch_size = min(8, len(train_pc) // 4) if len(train_pc) >= 16 else 4
            train_dataset = TensorDataset(train_pc_tensor, train_kp_tensor)
            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
            
            # 训练循环
            model.train()
            best_loss = float('inf')
            patience = 0
            
            for epoch in range(100):
                epoch_loss = 0.0
                
                for batch_pc, batch_kp in train_loader:
                    optimizer.zero_grad()
                    
                    predicted = model(batch_pc)
                    loss = criterion(predicted, batch_kp)
                    
                    loss.backward()
                    optimizer.step()
                    epoch_loss += loss.item()
                
                avg_loss = epoch_loss / len(train_loader)
                scheduler.step(avg_loss)
                
                if avg_loss < best_loss:
                    best_loss = avg_loss
                    patience = 0
                    torch.save(model.state_dict(), 'best_lightweight_transfer.pth')
                else:
                    patience += 1
                    if patience >= 15:
                        print(f"早停于epoch {epoch+1}")
                        break
                
                if epoch % 20 == 0:
                    print(f"Epoch {epoch+1}: Loss = {avg_loss:.6f}")
            
            # 加载最佳模型并测试
            model.load_state_dict(torch.load('best_lightweight_transfer.pth'))
            model.eval()
            
            with torch.no_grad():
                predicted = model(test_pc_tensor)
                test_errors = torch.norm(predicted - test_kp_tensor, dim=2)
                avg_error = torch.mean(test_errors).item()
                
                # 计算准确率
                sample_errors = torch.mean(test_errors, dim=1)
                errors_5mm = torch.sum(sample_errors <= 5.0).item()
                errors_10mm = torch.sum(sample_errors <= 10.0).item()
                
                acc_5mm = (errors_5mm / len(test_pc)) * 100
                acc_10mm = (errors_10mm / len(test_pc)) * 100
            
            result = {
                'model_type': 'lightweight_transfer',
                'test_samples': len(test_pc),
                'avg_error': avg_error,
                'accuracy_5mm': acc_5mm,
                'accuracy_10mm': acc_10mm,
                'medical_grade': avg_error <= 10.0,
                'excellent_grade': avg_error <= 5.0,
                'parameters': sum(p.numel() for p in model.parameters())
            }
            
            print(f"\n📊 轻量级迁移模型结果:")
            print(f"   测试样本: {result['test_samples']}")
            print(f"   平均误差: {result['avg_error']:.2f}mm")
            print(f"   5mm准确率: {result['accuracy_5mm']:.1f}%")
            print(f"   10mm准确率: {result['accuracy_10mm']:.1f}%")
            print(f"   医疗级达标: {'✅' if result['medical_grade'] else '❌'}")
            print(f"   优秀级达标: {'✅' if result['excellent_grade'] else '❌'}")
            print(f"   参数数量: {result['parameters']:,}")
            
            return result
            
        except Exception as e:
            print(f"❌ 训练失败: {e}")
            return None

def create_better_transfer_strategies():
    """创建更好的迁移学习策略"""
    print("\n💡 更好的迁移学习策略")
    print("=" * 50)
    
    strategies = {
        "策略1: 知识蒸馏": {
            "方法": "从大模型向小模型蒸馏知识",
            "优势": [
                "保持模型轻量级",
                "获得大模型的知识",
                "适合小数据集"
            ],
            "实现": "使用教师-学生网络架构",
            "预期改进": "1-2mm"
        },
        
        "策略2: 特征对齐": {
            "方法": "对齐预训练特征和目标特征",
            "优势": [
                "减少领域差异",
                "保持特征表示能力",
                "渐进式适配"
            ],
            "实现": "添加特征对齐损失函数",
            "预期改进": "1-2mm"
        },
        
        "策略3: 元学习": {
            "方法": "学习如何快速适应新任务",
            "优势": [
                "快速适应能力",
                "少样本学习",
                "泛化能力强"
            ],
            "实现": "MAML或Reptile算法",
            "预期改进": "2-3mm"
        },
        
        "策略4: 自监督预训练": {
            "方法": "在医疗数据上进行自监督预训练",
            "优势": [
                "领域特异性强",
                "无需标注数据",
                "可扩展性好"
            ],
            "实现": "旋转预测、重建、对比学习",
            "预期改进": "2-4mm"
        }
    }
    
    print("🎯 推荐的迁移学习策略:")
    for strategy, details in strategies.items():
        print(f"\n{strategy}:")
        print(f"  方法: {details['方法']}")
        print(f"  预期改进: {details['预期改进']}")
        print(f"  实现: {details['实现']}")
    
    return strategies

def create_practical_recommendations():
    """创建实用建议"""
    print("\n🎯 实用建议")
    print("=" * 50)
    
    recommendations = {
        "立即可行": [
            "继续使用简化通用模型 (6.60mm)",
            "优化MutualAssistanceNet架构",
            "收集更多高质量数据",
            "改进数据增强策略"
        ],
        
        "短期改进 (1-2个月)": [
            "实现知识蒸馏方法",
            "尝试自监督预训练",
            "优化超参数和训练策略",
            "实验不同的损失函数"
        ],
        
        "中期目标 (3-6个月)": [
            "收集大规模医疗点云数据",
            "实现真正的预训练模型",
            "开发领域特异性架构",
            "建立完整的评估体系"
        ],
        
        "长期愿景 (6-12个月)": [
            "建立医疗AI预训练模型库",
            "开发通用医疗关键点检测系统",
            "推动行业标准制定",
            "实现商业化应用"
        ]
    }
    
    print("📋 实用建议:")
    for phase, actions in recommendations.items():
        print(f"\n{phase}:")
        for action in actions:
            print(f"  • {action}")
    
    return recommendations

def main():
    """主函数"""
    print("🔍 改进的迁移学习方案")
    print("Improved Transfer Learning Approach")
    print("=" * 70)
    
    # 分析失败原因
    analyzer = TransferLearningAnalyzer()
    failure_analysis = analyzer.analyze_transfer_failure()
    
    # 训练轻量级迁移模型
    trainer = ImprovedTransferTrainer()
    result = trainer.train_lightweight_transfer_model()
    
    # 创建更好的策略
    strategies = create_better_transfer_strategies()
    
    # 实用建议
    recommendations = create_practical_recommendations()
    
    # 保存分析结果
    analysis_report = {
        'analysis_goal': '分析迁移学习失败原因并提出改进方案',
        'failure_analysis': failure_analysis,
        'lightweight_model_result': result,
        'better_strategies': strategies,
        'recommendations': recommendations,
        'key_conclusions': [
            '复杂的迁移学习模型不适合小数据集',
            '简单有效的架构比复杂预训练更重要',
            'MutualAssistanceNet的相互辅助机制是关键',
            '数据质量和架构设计优先于预训练权重'
        ],
        'timestamp': '2025-07-25'
    }
    
    with open('improved_transfer_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 改进分析报告已保存到 improved_transfer_analysis.json")
    
    if result:
        print(f"\n🎉 核心结论:")
        print(f"✅ 复杂迁移学习失败: 10.42mm vs 6.60mm基线")
        print(f"✅ 轻量级迁移模型: {result['avg_error']:.2f}mm")
        print(f"✅ 关键洞察: 简单架构 + 相互辅助机制更有效")
        print(f"✅ 建议: 继续优化MutualAssistanceNet而非复杂迁移")

if __name__ == "__main__":
    main()
