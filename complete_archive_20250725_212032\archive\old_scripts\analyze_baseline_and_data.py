#!/usr/bin/env python3
"""
深入分析基线和数据集
检查7.579mm基线的真实性和数据集质量
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import json
from balanced_pointnet import BalancedPointNet, BalancedDataset, calculate_metrics

def analyze_dataset():
    """分析数据集的基本统计信息"""
    print("🔍 **数据集深度分析**")
    print("=" * 60)
    
    # 加载数据
    data = np.load('f3_reduced_12kp_stable.npz', allow_pickle=True)
    sample_ids = data['sample_ids']
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    
    print(f"📊 数据集基本信息:")
    print(f"   总样本数: {len(sample_ids)}")
    print(f"   点云数组形状: {point_clouds.shape}")
    print(f"   关键点数组形状: {keypoints.shape}")
    
    # 分析点云统计
    print(f"\n📈 点云统计分析:")
    point_counts = [len(pc) for pc in point_clouds]
    print(f"   点数范围: {min(point_counts)} - {max(point_counts)}")
    print(f"   平均点数: {np.mean(point_counts):.1f}")
    print(f"   点数标准差: {np.std(point_counts):.1f}")
    
    # 分析关键点统计
    print(f"\n🎯 关键点统计分析:")
    all_keypoints = np.concatenate(keypoints, axis=0)  # [N*12, 3]
    print(f"   关键点坐标范围:")
    print(f"     X: {all_keypoints[:, 0].min():.3f} - {all_keypoints[:, 0].max():.3f}")
    print(f"     Y: {all_keypoints[:, 1].min():.3f} - {all_keypoints[:, 1].max():.3f}")
    print(f"     Z: {all_keypoints[:, 2].min():.3f} - {all_keypoints[:, 2].max():.3f}")
    
    # 分析关键点间距离
    print(f"\n📏 关键点间距离分析:")
    inter_distances = []
    for kps in keypoints:
        for i in range(len(kps)):
            for j in range(i+1, len(kps)):
                dist = np.linalg.norm(kps[i] - kps[j])
                inter_distances.append(dist)
    
    inter_distances = np.array(inter_distances)
    print(f"   关键点间距离范围: {inter_distances.min():.3f} - {inter_distances.max():.3f}")
    print(f"   平均距离: {inter_distances.mean():.3f}")
    print(f"   距离标准差: {inter_distances.std():.3f}")
    
    # 分析数据集分割
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    test_mask = np.isin(sample_ids, test_samples)
    train_val_mask = ~test_mask
    
    print(f"\n📂 数据集分割:")
    print(f"   训练+验证: {train_val_mask.sum()} 样本")
    print(f"   测试: {test_mask.sum()} 样本")
    print(f"   测试样本ID: {sample_ids[test_mask]}")
    
    return {
        'total_samples': len(sample_ids),
        'point_counts': point_counts,
        'keypoint_stats': {
            'range_x': (all_keypoints[:, 0].min(), all_keypoints[:, 0].max()),
            'range_y': (all_keypoints[:, 1].min(), all_keypoints[:, 1].max()),
            'range_z': (all_keypoints[:, 2].min(), all_keypoints[:, 2].max()),
        },
        'inter_distances': inter_distances,
        'split_info': {
            'train_val': train_val_mask.sum(),
            'test': test_mask.sum()
        }
    }

def create_simple_baseline():
    """创建一个极简基线模型"""
    print("\n🔧 **创建极简基线模型**")
    print("=" * 60)
    
    class SimpleBaseline(nn.Module):
        def __init__(self, num_keypoints=12):
            super(SimpleBaseline, self).__init__()
            # 最简单的模型：直接预测平均关键点
            self.avg_keypoints = nn.Parameter(torch.randn(num_keypoints, 3))
            
        def forward(self, x):
            batch_size = x.size(0)
            return self.avg_keypoints.unsqueeze(0).repeat(batch_size, 1, 1)
    
    return SimpleBaseline()

def test_simple_baseline():
    """测试极简基线的性能"""
    print("\n🧪 **测试极简基线性能**")
    print("=" * 60)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    
    # 数据集
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    test_dataset = BalancedDataset('f3_reduced_12kp_stable.npz', 'test', 
                                 num_points=1024, test_samples=test_samples, 
                                 augment=False, seed=42)
    
    # 计算数据集的平均关键点
    all_keypoints = []
    for i in range(len(test_dataset)):
        sample = test_dataset[i]
        all_keypoints.append(sample['keypoints'].numpy())
    
    all_keypoints = np.array(all_keypoints)  # [N, 12, 3]
    mean_keypoints = np.mean(all_keypoints, axis=0)  # [12, 3]
    
    print(f"📊 测试集统计:")
    print(f"   样本数: {len(test_dataset)}")
    print(f"   平均关键点形状: {mean_keypoints.shape}")
    
    # 计算预测平均关键点的误差
    errors = []
    for i in range(len(test_dataset)):
        sample = test_dataset[i]
        target = sample['keypoints'].numpy()
        pred = mean_keypoints
        
        # 计算误差
        distances = np.linalg.norm(pred - target, axis=1)
        avg_distance = np.mean(distances)
        errors.append(avg_distance)
    
    errors = np.array(errors)
    
    print(f"\n📈 极简基线性能 (预测平均关键点):")
    print(f"   平均误差: {errors.mean():.3f}mm")
    print(f"   误差标准差: {errors.std():.3f}mm")
    print(f"   误差范围: {errors.min():.3f} - {errors.max():.3f}mm")
    
    # 与声称的基线对比
    claimed_baseline = 7.579
    print(f"\n🔍 与声称基线对比:")
    print(f"   声称基线: {claimed_baseline:.3f}mm")
    print(f"   极简基线: {errors.mean():.3f}mm")
    print(f"   差异: {abs(errors.mean() - claimed_baseline):.3f}mm")
    
    if errors.mean() < claimed_baseline:
        print(f"⚠️  极简基线竟然比声称基线更好！这很可疑...")
    else:
        print(f"✅ 极简基线比声称基线差，这是合理的")
    
    return {
        'simple_baseline_error': errors.mean(),
        'simple_baseline_std': errors.std(),
        'claimed_baseline': claimed_baseline,
        'individual_errors': errors
    }

def analyze_best_model_claims():
    """分析最佳模型的声称性能"""
    print("\n🔍 **分析最佳模型声称性能**")
    print("=" * 60)
    
    # 检查是否有保存的最佳模型
    import os
    model_files = [f for f in os.listdir('.') if f.startswith('best_') and f.endswith('.pth')]
    
    print(f"📁 发现的模型文件:")
    for f in model_files:
        print(f"   {f}")
    
    # 尝试加载并测试一些模型
    if model_files:
        print(f"\n🧪 测试保存的模型:")
        
        device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
        
        test_samples = ['600114', '600115', '600116', '600117', '600118', 
                       '600119', '600120', '600121', '600122', '600123',
                       '600124', '600125', '600126', '600127', '600128']
        
        test_dataset = BalancedDataset('f3_reduced_12kp_stable.npz', 'test', 
                                     num_points=3072, test_samples=test_samples, 
                                     augment=False, seed=42)
        
        test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=4, shuffle=False)
        
        for model_file in model_files[:3]:  # 只测试前3个
            try:
                print(f"\n   测试模型: {model_file}")
                
                # 根据文件名判断模型类型
                if 'balanced' in model_file:
                    model = BalancedPointNet(num_keypoints=12, dropout_rate=0.4)
                else:
                    continue  # 跳过其他类型的模型
                
                # 加载模型
                checkpoint = torch.load(model_file, map_location=device)
                model.load_state_dict(checkpoint['model_state_dict'])
                model.to(device)
                model.eval()
                
                # 测试模型
                all_errors = []
                with torch.no_grad():
                    for batch in test_loader:
                        point_cloud = batch['point_cloud'].to(device)
                        keypoints = batch['keypoints'].to(device)
                        
                        pred_keypoints = model(point_cloud)
                        
                        # 计算误差
                        distances = torch.norm(pred_keypoints - keypoints, dim=2)
                        avg_distances = torch.mean(distances, dim=1)
                        all_errors.extend(avg_distances.cpu().numpy())
                
                actual_error = np.mean(all_errors)
                claimed_error = checkpoint.get('best_test_error', 'unknown')
                
                print(f"     声称误差: {claimed_error}")
                print(f"     实际误差: {actual_error:.3f}mm")
                print(f"     差异: {abs(float(claimed_error) - actual_error):.3f}mm" if claimed_error != 'unknown' else "无法比较")
                
            except Exception as e:
                print(f"     ❌ 加载失败: {e}")
    
    else:
        print(f"   没有找到保存的模型文件")

def comprehensive_analysis():
    """综合分析"""
    print("\n🎯 **综合分析报告**")
    print("=" * 80)
    
    # 数据集分析
    dataset_stats = analyze_dataset()
    
    # 极简基线测试
    baseline_results = test_simple_baseline()
    
    # 最佳模型分析
    analyze_best_model_claims()
    
    # 综合结论
    print(f"\n📋 **综合结论**")
    print("=" * 60)
    
    print(f"1. 数据集规模: {dataset_stats['total_samples']}个样本 (非常小)")
    print(f"2. 关键点间平均距离: {dataset_stats['inter_distances'].mean():.3f}mm")
    print(f"3. 极简基线误差: {baseline_results['simple_baseline_error']:.3f}mm")
    print(f"4. 声称的最佳基线: {baseline_results['claimed_baseline']:.3f}mm")
    
    # 判断数据集质量
    if dataset_stats['total_samples'] < 100:
        print(f"⚠️  数据集过小，容易导致不稳定的结果")
    
    if baseline_results['simple_baseline_error'] < baseline_results['claimed_baseline']:
        print(f"🚨 极简基线比声称基线更好，这很可疑！")
    
    # 保存分析结果
    analysis_results = {
        'dataset_stats': dataset_stats,
        'baseline_results': baseline_results,
        'analysis_date': '2025-07-17',
        'conclusions': [
            f"数据集规模过小: {dataset_stats['total_samples']}样本",
            f"极简基线: {baseline_results['simple_baseline_error']:.3f}mm",
            f"声称基线: {baseline_results['claimed_baseline']:.3f}mm"
        ]
    }
    
    # 转换numpy数组为列表以便JSON序列化
    analysis_results['dataset_stats']['point_counts'] = [int(x) for x in analysis_results['dataset_stats']['point_counts']]
    analysis_results['dataset_stats']['inter_distances'] = analysis_results['dataset_stats']['inter_distances'].tolist()
    analysis_results['baseline_results']['individual_errors'] = analysis_results['baseline_results']['individual_errors'].tolist()
    
    with open('comprehensive_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 分析结果已保存到: comprehensive_analysis_results.json")

if __name__ == "__main__":
    print("🔍 **基线和数据集深度分析**")
    print("🎯 **目标**: 验证7.579mm基线的真实性")
    print("📊 **方法**: 数据统计 + 极简基线 + 模型验证")
    print("=" * 80)
    
    comprehensive_analysis()
