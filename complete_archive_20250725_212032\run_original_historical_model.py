#!/usr/bin/env python3
"""
运行原始历史模型
Run Original Historical Model
使用找到的原始训练代码来复现5.371mm性能
"""

import sys
import os
import torch
import numpy as np

def check_dependencies():
    """检查依赖文件"""
    
    print("🔍 检查原始代码依赖...")
    
    required_files = [
        "archive/old_scripts/ensemble_double_softmax_exact.py",
        "archive/old_scripts/train_exact_ensemble_double_softmax.py", 
        "archive/old_experiments/f3_reduced_12kp_stable.npz"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✅ 找到: {file_path}")
    
    if missing_files:
        print(f"\n❌ 缺少文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print(f"✅ 所有依赖文件都存在")
    return True

def setup_python_path():
    """设置Python路径"""
    
    print("🔧 设置Python路径...")
    
    # 添加archive/old_scripts到Python路径
    old_scripts_path = os.path.abspath("archive/old_scripts")
    if old_scripts_path not in sys.path:
        sys.path.insert(0, old_scripts_path)
        print(f"✅ 添加路径: {old_scripts_path}")
    
    # 添加当前目录
    current_path = os.path.abspath(".")
    if current_path not in sys.path:
        sys.path.insert(0, current_path)
        print(f"✅ 添加路径: {current_path}")

def run_original_training():
    """运行原始训练代码"""
    
    print("🚀 运行原始历史模型训练...")
    print("=" * 80)
    
    try:
        # 导入原始模块
        from ensemble_double_softmax_exact import ExactEnsembleDoubleSoftMaxPointNet, ExactReducedKeypointsF3Dataset
        from train_exact_ensemble_double_softmax import train_exact_ensemble_double_softmax, run_multiple_seeds
        
        print("✅ 成功导入原始模块")
        
        # 检查数据集
        dataset_path = "archive/old_experiments/f3_reduced_12kp_stable.npz"
        if not os.path.exists(dataset_path):
            print(f"❌ 数据集不存在: {dataset_path}")
            return False
        
        print(f"✅ 数据集存在: {dataset_path}")
        
        # 运行单个种子训练（种子123是历史最佳）
        print(f"\n🎯 运行种子123训练（历史最佳种子）...")
        best_error, history = train_exact_ensemble_double_softmax(seed=123)
        
        print(f"\n📊 种子123结果:")
        print(f"   最佳验证误差: {best_error:.3f}mm")
        print(f"   历史目标: 5.371mm")
        print(f"   差距: {best_error - 5.371:.3f}mm")
        
        if best_error < 6.0:
            print(f"🎉 成功！接近历史性能！")
        elif best_error < 8.0:
            print(f"✅ 良好！显著改进")
        else:
            print(f"⚠️ 仍需优化")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print(f"💡 尝试手动复制原始代码...")
        return False
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        return False

def run_multiple_seeds_training():
    """运行多种子训练"""
    
    print("🚀 运行多种子训练...")
    print("=" * 80)
    
    try:
        from train_exact_ensemble_double_softmax import run_multiple_seeds
        
        # 运行多种子训练
        results = run_multiple_seeds()
        
        print(f"\n📊 多种子训练完成")
        print(f"   结果数量: {len(results)}")
        
        # 分析结果
        valid_results = [r for r in results if 'best_error' in r and r['best_error'] != float('inf')]
        
        if valid_results:
            best_result = min(valid_results, key=lambda x: x['best_error'])
            print(f"   最佳结果: {best_result['best_error']:.3f}mm (种子{best_result['seed']})")
            print(f"   历史目标: 5.371mm")
            print(f"   差距: {best_result['best_error'] - 5.371:.3f}mm")
            
            if best_result['best_error'] < 6.0:
                print(f"🎉 多种子训练成功！")
            else:
                print(f"⚠️ 多种子训练仍有差距")
        else:
            print(f"❌ 所有种子都失败了")
        
        return True
        
    except Exception as e:
        print(f"❌ 多种子训练失败: {e}")
        return False

def test_original_model_loading():
    """测试原始模型加载"""
    
    print("🔍 测试原始模型加载...")
    
    try:
        # 尝试加载历史模型
        historical_model_path = "archive/old_models/best_exact_ensemble_seed123_5.371mm.pth"
        
        if os.path.exists(historical_model_path):
            print(f"✅ 找到历史模型: {historical_model_path}")
            
            checkpoint = torch.load(historical_model_path, map_location='cpu')
            print(f"✅ 成功加载历史模型")
            
            if 'model_state_dict' in checkpoint:
                print(f"✅ 找到模型状态字典")
                
                # 尝试创建模型并加载权重
                from ensemble_double_softmax_exact import ExactEnsembleDoubleSoftMaxPointNet
                
                model = ExactEnsembleDoubleSoftMaxPointNet(num_keypoints=12, dropout_rate=0.3, num_ensembles=3)
                model.load_state_dict(checkpoint['model_state_dict'])
                print(f"✅ 成功加载历史模型权重")
                
                # 测试推理
                test_input = torch.randn(1, 4096, 3)
                model.eval()
                with torch.no_grad():
                    output = model(test_input)
                    print(f"✅ 模型推理成功，输出形状: {output.shape}")
                
                return True
            else:
                print(f"❌ 模型状态字典不存在")
                return False
        else:
            print(f"❌ 历史模型文件不存在: {historical_model_path}")
            return False
            
    except Exception as e:
        print(f"❌ 模型加载测试失败: {e}")
        return False

def create_manual_reproduction():
    """手动复现原始代码"""
    
    print("🔧 手动复现原始代码...")
    
    # 这里可以手动复制关键的原始代码
    # 如果自动导入失败的话
    
    print("💡 如果自动运行失败，可以手动执行以下步骤:")
    print("1. cd archive/old_scripts")
    print("2. python train_exact_ensemble_double_softmax.py")
    print("3. 或者 python ensemble_double_softmax_exact.py")

def main():
    """主函数"""
    
    print("🎯 运行原始历史模型")
    print("使用找到的原始训练代码复现5.371mm性能")
    print("=" * 80)
    
    # 1. 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，无法继续")
        return
    
    # 2. 设置Python路径
    setup_python_path()
    
    # 3. 测试模型加载
    print(f"\n" + "="*60)
    model_load_success = test_original_model_loading()
    
    # 4. 运行原始训练
    print(f"\n" + "="*60)
    training_success = run_original_training()
    
    if not training_success:
        print(f"\n⚠️ 单种子训练失败，尝试多种子训练...")
        run_multiple_seeds_training()
    
    # 5. 总结
    print(f"\n🎯 总结:")
    print(f"   模型加载: {'✅ 成功' if model_load_success else '❌ 失败'}")
    print(f"   训练执行: {'✅ 成功' if training_success else '❌ 失败'}")
    
    if model_load_success and training_success:
        print(f"🎉 原始代码运行成功！")
        print(f"💡 现在可以对比我们的实现和原始实现的差异")
    else:
        print(f"⚠️ 部分功能失败，需要进一步调试")
        create_manual_reproduction()
    
    print(f"\n📝 下一步建议:")
    print(f"   1. 对比原始代码和我们的实现")
    print(f"   2. 找出关键差异点")
    print(f"   3. 修复我们的实现")
    print(f"   4. 最终复现5.371mm性能")

if __name__ == "__main__":
    main()
