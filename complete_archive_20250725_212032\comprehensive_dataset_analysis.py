#!/usr/bin/env python3
"""
全面数据集分析 - 发现数据集问题
Comprehensive Dataset Analysis - Identify Dataset Issues
目标: 有理有据地分析数据集现状，发现潜在问题
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns
import pandas as pd
import json
from pathlib import Path
from datetime import datetime
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from scipy import stats
from scipy.spatial.distance import pdist, squareform
import warnings
warnings.filterwarnings('ignore')

class ComprehensiveDatasetAnalyzer:
    """全面数据集分析器"""
    
    def __init__(self, data_path='data/raw/high_quality_f3_dataset.npz'):
        self.data_path = data_path
        self.load_data()
        self.analysis_results = {}
        
    def load_data(self):
        """加载数据"""
        print(f"📦 加载数据集: {self.data_path}")
        
        data = np.load(self.data_path, allow_pickle=True)
        self.sample_ids = data['sample_ids']
        self.point_clouds = data['point_clouds']
        self.keypoints = data['keypoints']
        
        print(f"✅ 数据加载完成: {len(self.sample_ids)} 样本")
        print(f"   样本ID范围: {self.sample_ids[0]} - {self.sample_ids[-1]}")
        
    def basic_statistics(self):
        """基础统计分析"""
        print("\n📊 基础统计分析")
        print("=" * 50)
        
        # 点云统计
        pc_sizes = [len(pc) for pc in self.point_clouds]
        pc_ranges = []
        pc_centers = []
        
        for pc in self.point_clouds:
            pc_array = np.array(pc, dtype=np.float32)
            pc_range = np.max(pc_array, axis=0) - np.min(pc_array, axis=0)
            pc_center = np.mean(pc_array, axis=0)
            pc_ranges.append(pc_range)
            pc_centers.append(pc_center)
        
        pc_ranges = np.array(pc_ranges)
        pc_centers = np.array(pc_centers)
        
        # 关键点统计
        kp_ranges = []
        kp_centers = []
        
        for kp in self.keypoints:
            kp_array = np.array(kp, dtype=np.float32)
            kp_range = np.max(kp_array, axis=0) - np.min(kp_array, axis=0)
            kp_center = np.mean(kp_array, axis=0)
            kp_ranges.append(kp_range)
            kp_centers.append(kp_center)
        
        kp_ranges = np.array(kp_ranges)
        kp_centers = np.array(kp_centers)
        
        stats_summary = {
            "total_samples": len(self.sample_ids),
            "point_cloud_stats": {
                "size_mean": np.mean(pc_sizes),
                "size_std": np.std(pc_sizes),
                "size_min": np.min(pc_sizes),
                "size_max": np.max(pc_sizes),
                "range_mean": np.mean(pc_ranges, axis=0).tolist(),
                "range_std": np.std(pc_ranges, axis=0).tolist(),
                "center_mean": np.mean(pc_centers, axis=0).tolist(),
                "center_std": np.std(pc_centers, axis=0).tolist()
            },
            "keypoint_stats": {
                "count": len(self.keypoints[0]),
                "range_mean": np.mean(kp_ranges, axis=0).tolist(),
                "range_std": np.std(kp_ranges, axis=0).tolist(),
                "center_mean": np.mean(kp_centers, axis=0).tolist(),
                "center_std": np.std(kp_centers, axis=0).tolist()
            }
        }
        
        print(f"📈 点云统计:")
        print(f"   点数: {stats_summary['point_cloud_stats']['size_min']}-{stats_summary['point_cloud_stats']['size_max']} (平均: {stats_summary['point_cloud_stats']['size_mean']:.0f})")
        print(f"   X范围: {stats_summary['point_cloud_stats']['range_mean'][0]:.1f}±{stats_summary['point_cloud_stats']['range_std'][0]:.1f}mm")
        print(f"   Y范围: {stats_summary['point_cloud_stats']['range_mean'][1]:.1f}±{stats_summary['point_cloud_stats']['range_std'][1]:.1f}mm")
        print(f"   Z范围: {stats_summary['point_cloud_stats']['range_mean'][2]:.1f}±{stats_summary['point_cloud_stats']['range_std'][2]:.1f}mm")
        
        print(f"📍 关键点统计:")
        print(f"   数量: {stats_summary['keypoint_stats']['count']}")
        print(f"   X范围: {stats_summary['keypoint_stats']['range_mean'][0]:.1f}±{stats_summary['keypoint_stats']['range_std'][0]:.1f}mm")
        print(f"   Y范围: {stats_summary['keypoint_stats']['range_mean'][1]:.1f}±{stats_summary['keypoint_stats']['range_std'][1]:.1f}mm")
        print(f"   Z范围: {stats_summary['keypoint_stats']['range_mean'][2]:.1f}±{stats_summary['keypoint_stats']['range_std'][2]:.1f}mm")
        
        self.analysis_results['basic_statistics'] = stats_summary
        return stats_summary
    
    def data_quality_assessment(self):
        """数据质量评估"""
        print("\n🔍 数据质量评估")
        print("=" * 50)
        
        quality_issues = []
        
        # 1. 检查缺失值和异常值
        nan_samples = []
        inf_samples = []
        
        for i, (pc, kp) in enumerate(zip(self.point_clouds, self.keypoints)):
            pc_array = np.array(pc, dtype=np.float32)
            kp_array = np.array(kp, dtype=np.float32)
            
            if np.any(np.isnan(pc_array)) or np.any(np.isnan(kp_array)):
                nan_samples.append(self.sample_ids[i])
            
            if np.any(np.isinf(pc_array)) or np.any(np.isinf(kp_array)):
                inf_samples.append(self.sample_ids[i])
        
        if nan_samples:
            quality_issues.append(f"发现{len(nan_samples)}个样本包含NaN值: {nan_samples[:5]}")
        
        if inf_samples:
            quality_issues.append(f"发现{len(inf_samples)}个样本包含无穷值: {inf_samples[:5]}")
        
        # 2. 检查点云和关键点的对齐问题
        alignment_issues = []
        
        for i, (pc, kp) in enumerate(zip(self.point_clouds, self.keypoints)):
            pc_array = np.array(pc, dtype=np.float32)
            kp_array = np.array(kp, dtype=np.float32)
            
            pc_center = np.mean(pc_array, axis=0)
            kp_center = np.mean(kp_array, axis=0)
            
            center_distance = np.linalg.norm(pc_center - kp_center)
            
            if center_distance > 30.0:  # 30mm阈值
                alignment_issues.append({
                    'sample_id': self.sample_ids[i],
                    'center_distance': center_distance,
                    'pc_center': pc_center.tolist(),
                    'kp_center': kp_center.tolist()
                })
        
        if alignment_issues:
            quality_issues.append(f"发现{len(alignment_issues)}个样本存在对齐问题 (中心距离>30mm)")
        
        # 3. 检查尺度一致性
        pc_scales = []
        kp_scales = []
        
        for pc, kp in zip(self.point_clouds, self.keypoints):
            pc_array = np.array(pc, dtype=np.float32)
            kp_array = np.array(kp, dtype=np.float32)
            
            pc_scale = np.linalg.norm(np.max(pc_array, axis=0) - np.min(pc_array, axis=0))
            kp_scale = np.linalg.norm(np.max(kp_array, axis=0) - np.min(kp_array, axis=0))
            
            pc_scales.append(pc_scale)
            kp_scales.append(kp_scale)
        
        pc_scales = np.array(pc_scales)
        kp_scales = np.array(kp_scales)
        
        # 检查尺度异常值
        pc_scale_outliers = np.where((pc_scales < np.percentile(pc_scales, 5)) | 
                                   (pc_scales > np.percentile(pc_scales, 95)))[0]
        
        kp_scale_outliers = np.where((kp_scales < np.percentile(kp_scales, 5)) | 
                                   (kp_scales > np.percentile(kp_scales, 95)))[0]
        
        if len(pc_scale_outliers) > 0:
            quality_issues.append(f"发现{len(pc_scale_outliers)}个点云尺度异常样本")
        
        if len(kp_scale_outliers) > 0:
            quality_issues.append(f"发现{len(kp_scale_outliers)}个关键点尺度异常样本")
        
        # 4. 检查关键点在点云表面的分布
        surface_distance_issues = []
        
        for i, (pc, kp) in enumerate(zip(self.point_clouds[:10], self.keypoints[:10])):  # 只检查前10个样本
            pc_array = np.array(pc, dtype=np.float32)
            kp_array = np.array(kp, dtype=np.float32)
            
            # 计算每个关键点到最近点云点的距离
            min_distances = []
            for kp_point in kp_array:
                distances = np.linalg.norm(pc_array - kp_point, axis=1)
                min_distances.append(np.min(distances))
            
            avg_surface_distance = np.mean(min_distances)
            
            if avg_surface_distance > 2.0:  # 2mm阈值
                surface_distance_issues.append({
                    'sample_id': self.sample_ids[i],
                    'avg_surface_distance': avg_surface_distance,
                    'max_surface_distance': np.max(min_distances)
                })
        
        if surface_distance_issues:
            quality_issues.append(f"发现{len(surface_distance_issues)}个样本的关键点偏离点云表面>2mm")
        
        print(f"🔍 数据质量问题:")
        if quality_issues:
            for issue in quality_issues:
                print(f"   ⚠️ {issue}")
        else:
            print(f"   ✅ 未发现明显的数据质量问题")
        
        quality_assessment = {
            "nan_samples": nan_samples,
            "inf_samples": inf_samples,
            "alignment_issues": alignment_issues,
            "pc_scale_outliers": pc_scale_outliers.tolist(),
            "kp_scale_outliers": kp_scale_outliers.tolist(),
            "surface_distance_issues": surface_distance_issues,
            "quality_issues": quality_issues
        }
        
        self.analysis_results['data_quality'] = quality_assessment
        return quality_assessment
    
    def coordinate_system_analysis(self):
        """坐标系统分析"""
        print("\n🧭 坐标系统分析")
        print("=" * 50)
        
        # 分析坐标系的一致性
        pc_orientations = []
        kp_orientations = []
        
        for pc, kp in zip(self.point_clouds, self.keypoints):
            pc_array = np.array(pc, dtype=np.float32)
            kp_array = np.array(kp, dtype=np.float32)
            
            # 使用PCA分析主方向
            pc_pca = PCA(n_components=3)
            pc_pca.fit(pc_array)
            pc_orientations.append(pc_pca.components_)
            
            kp_pca = PCA(n_components=3)
            kp_pca.fit(kp_array)
            kp_orientations.append(kp_pca.components_)
        
        # 分析坐标范围分布
        all_pc_coords = []
        all_kp_coords = []
        
        for pc, kp in zip(self.point_clouds, self.keypoints):
            pc_array = np.array(pc, dtype=np.float32)
            kp_array = np.array(kp, dtype=np.float32)
            
            all_pc_coords.extend(pc_array.tolist())
            all_kp_coords.extend(kp_array.tolist())
        
        all_pc_coords = np.array(all_pc_coords)
        all_kp_coords = np.array(all_kp_coords)
        
        coord_analysis = {
            "point_cloud_ranges": {
                "x_range": [float(np.min(all_pc_coords[:, 0])), float(np.max(all_pc_coords[:, 0]))],
                "y_range": [float(np.min(all_pc_coords[:, 1])), float(np.max(all_pc_coords[:, 1]))],
                "z_range": [float(np.min(all_pc_coords[:, 2])), float(np.max(all_pc_coords[:, 2]))]
            },
            "keypoint_ranges": {
                "x_range": [float(np.min(all_kp_coords[:, 0])), float(np.max(all_kp_coords[:, 0]))],
                "y_range": [float(np.min(all_kp_coords[:, 1])), float(np.max(all_kp_coords[:, 1]))],
                "z_range": [float(np.min(all_kp_coords[:, 2])), float(np.max(all_kp_coords[:, 2]))]
            }
        }
        
        print(f"📐 坐标范围分析:")
        print(f"   点云 X: [{coord_analysis['point_cloud_ranges']['x_range'][0]:.1f}, {coord_analysis['point_cloud_ranges']['x_range'][1]:.1f}]mm")
        print(f"   点云 Y: [{coord_analysis['point_cloud_ranges']['y_range'][0]:.1f}, {coord_analysis['point_cloud_ranges']['y_range'][1]:.1f}]mm")
        print(f"   点云 Z: [{coord_analysis['point_cloud_ranges']['z_range'][0]:.1f}, {coord_analysis['point_cloud_ranges']['z_range'][1]:.1f}]mm")
        
        print(f"   关键点 X: [{coord_analysis['keypoint_ranges']['x_range'][0]:.1f}, {coord_analysis['keypoint_ranges']['x_range'][1]:.1f}]mm")
        print(f"   关键点 Y: [{coord_analysis['keypoint_ranges']['y_range'][0]:.1f}, {coord_analysis['keypoint_ranges']['y_range'][1]:.1f}]mm")
        print(f"   关键点 Z: [{coord_analysis['keypoint_ranges']['z_range'][0]:.1f}, {coord_analysis['keypoint_ranges']['z_range'][1]:.1f}]mm")
        
        # 检查坐标系一致性
        coord_issues = []
        
        # 检查是否有明显的坐标系不一致
        pc_x_range = coord_analysis['point_cloud_ranges']['x_range'][1] - coord_analysis['point_cloud_ranges']['x_range'][0]
        pc_y_range = coord_analysis['point_cloud_ranges']['y_range'][1] - coord_analysis['point_cloud_ranges']['y_range'][0]
        pc_z_range = coord_analysis['point_cloud_ranges']['z_range'][1] - coord_analysis['point_cloud_ranges']['z_range'][0]
        
        kp_x_range = coord_analysis['keypoint_ranges']['x_range'][1] - coord_analysis['keypoint_ranges']['x_range'][0]
        kp_y_range = coord_analysis['keypoint_ranges']['y_range'][1] - coord_analysis['keypoint_ranges']['y_range'][0]
        kp_z_range = coord_analysis['keypoint_ranges']['z_range'][1] - coord_analysis['keypoint_ranges']['z_range'][0]
        
        if abs(pc_x_range - kp_x_range) > max(pc_x_range, kp_x_range) * 0.5:
            coord_issues.append("X轴范围差异过大")
        
        if abs(pc_y_range - kp_y_range) > max(pc_y_range, kp_y_range) * 0.5:
            coord_issues.append("Y轴范围差异过大")
        
        if abs(pc_z_range - kp_z_range) > max(pc_z_range, kp_z_range) * 0.5:
            coord_issues.append("Z轴范围差异过大")
        
        if coord_issues:
            print(f"   ⚠️ 坐标系问题: {', '.join(coord_issues)}")
        else:
            print(f"   ✅ 坐标系基本一致")
        
        coord_analysis['coordinate_issues'] = coord_issues
        
        self.analysis_results['coordinate_system'] = coord_analysis
        return coord_analysis
    
    def sample_diversity_analysis(self):
        """样本多样性分析"""
        print("\n🎭 样本多样性分析")
        print("=" * 50)
        
        # 提取特征用于聚类分析
        features = []
        
        for pc, kp in zip(self.point_clouds, self.keypoints):
            pc_array = np.array(pc, dtype=np.float32)
            kp_array = np.array(kp, dtype=np.float32)
            
            # 点云特征
            pc_center = np.mean(pc_array, axis=0)
            pc_range = np.max(pc_array, axis=0) - np.min(pc_array, axis=0)
            pc_std = np.std(pc_array, axis=0)
            
            # 关键点特征
            kp_center = np.mean(kp_array, axis=0)
            kp_range = np.max(kp_array, axis=0) - np.min(kp_array, axis=0)
            kp_std = np.std(kp_array, axis=0)
            
            # 组合特征
            feature_vector = np.concatenate([
                pc_center, pc_range, pc_std,
                kp_center, kp_range, kp_std
            ])
            
            features.append(feature_vector)
        
        features = np.array(features)
        
        # 聚类分析
        n_clusters = min(5, len(self.sample_ids) // 10)  # 动态确定聚类数
        if n_clusters >= 2:
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(features)
            
            cluster_sizes = [np.sum(cluster_labels == i) for i in range(n_clusters)]
            
            print(f"📊 聚类分析 ({n_clusters}个聚类):")
            for i, size in enumerate(cluster_sizes):
                print(f"   聚类 {i+1}: {size} 样本 ({size/len(self.sample_ids)*100:.1f}%)")
            
            # 检查聚类平衡性
            cluster_balance = np.std(cluster_sizes) / np.mean(cluster_sizes)
            
            if cluster_balance > 0.5:
                print(f"   ⚠️ 聚类不平衡 (变异系数: {cluster_balance:.2f})")
            else:
                print(f"   ✅ 聚类相对平衡 (变异系数: {cluster_balance:.2f})")
        else:
            cluster_labels = np.zeros(len(self.sample_ids))
            cluster_balance = 0.0
        
        # 样本间距离分析
        sample_distances = pdist(features)
        distance_matrix = squareform(sample_distances)
        
        avg_distance = np.mean(sample_distances)
        min_distance = np.min(sample_distances)
        max_distance = np.max(sample_distances)
        
        print(f"📏 样本间距离分析:")
        print(f"   平均距离: {avg_distance:.2f}")
        print(f"   最小距离: {min_distance:.2f}")
        print(f"   最大距离: {max_distance:.2f}")
        print(f"   距离标准差: {np.std(sample_distances):.2f}")
        
        # 检查是否有重复或过于相似的样本
        similar_threshold = np.percentile(sample_distances, 5)  # 5%分位数
        similar_pairs = np.where(distance_matrix < similar_threshold)
        similar_pairs = [(i, j) for i, j in zip(similar_pairs[0], similar_pairs[1]) if i < j]
        
        if similar_pairs:
            print(f"   ⚠️ 发现{len(similar_pairs)}对过于相似的样本")
        else:
            print(f"   ✅ 未发现过于相似的样本")
        
        diversity_analysis = {
            "cluster_labels": cluster_labels.tolist(),
            "cluster_sizes": cluster_sizes if n_clusters >= 2 else [],
            "cluster_balance": cluster_balance,
            "distance_stats": {
                "mean": avg_distance,
                "std": np.std(sample_distances),
                "min": min_distance,
                "max": max_distance
            },
            "similar_pairs": similar_pairs[:10]  # 只保存前10对
        }
        
        self.analysis_results['sample_diversity'] = diversity_analysis
        return diversity_analysis
    
    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        print("\n📋 生成综合分析报告")
        print("=" * 50)
        
        # 汇总所有问题
        all_issues = []
        
        # 数据质量问题
        if 'data_quality' in self.analysis_results:
            all_issues.extend(self.analysis_results['data_quality']['quality_issues'])
        
        # 坐标系问题
        if 'coordinate_system' in self.analysis_results:
            coord_issues = self.analysis_results['coordinate_system']['coordinate_issues']
            all_issues.extend([f"坐标系问题: {issue}" for issue in coord_issues])
        
        # 多样性问题
        if 'sample_diversity' in self.analysis_results:
            diversity = self.analysis_results['sample_diversity']
            if diversity['cluster_balance'] > 0.5:
                all_issues.append("样本分布不平衡")
            if len(diversity['similar_pairs']) > 0:
                all_issues.append(f"存在{len(diversity['similar_pairs'])}对相似样本")
        
        # 生成问题严重程度评估
        severity_score = 0
        
        if len(all_issues) == 0:
            severity = "优秀"
            severity_score = 0
        elif len(all_issues) <= 2:
            severity = "良好"
            severity_score = 1
        elif len(all_issues) <= 5:
            severity = "一般"
            severity_score = 2
        else:
            severity = "需要改进"
            severity_score = 3
        
        # 生成改进建议
        recommendations = []
        
        if any("NaN" in issue for issue in all_issues):
            recommendations.append("清理包含NaN值的样本")
        
        if any("对齐" in issue for issue in all_issues):
            recommendations.append("修正点云和关键点的对齐问题")
        
        if any("尺度" in issue for issue in all_issues):
            recommendations.append("标准化样本尺度")
        
        if any("表面" in issue for issue in all_issues):
            recommendations.append("将关键点投影到点云表面")
        
        if any("坐标系" in issue for issue in all_issues):
            recommendations.append("统一坐标系")
        
        if any("不平衡" in issue for issue in all_issues):
            recommendations.append("增加数据增强以平衡样本分布")
        
        if any("相似" in issue for issue in all_issues):
            recommendations.append("移除重复或过于相似的样本")
        
        if not recommendations:
            recommendations.append("数据集质量良好，可以直接使用")
        
        comprehensive_report = {
            "analysis_timestamp": datetime.now().isoformat(),
            "dataset_path": str(self.data_path),
            "total_samples": len(self.sample_ids),
            "analysis_summary": {
                "total_issues": len(all_issues),
                "severity": severity,
                "severity_score": severity_score,
                "all_issues": all_issues
            },
            "detailed_results": self.analysis_results,
            "recommendations": recommendations,
            "next_steps": [
                "根据建议修复数据集问题",
                "重新评估修复后的数据集质量",
                "进行模型训练前的最终验证"
            ]
        }
        
        print(f"📊 数据集质量评估:")
        print(f"   总体评级: {severity}")
        print(f"   发现问题: {len(all_issues)}个")
        
        if all_issues:
            print(f"   主要问题:")
            for issue in all_issues:
                print(f"     - {issue}")
        
        print(f"   改进建议:")
        for rec in recommendations:
            print(f"     - {rec}")
        
        return comprehensive_report

def run_comprehensive_analysis():
    """运行全面数据集分析"""
    print("🔬 全面数据集分析")
    print("=" * 60)
    print("目标: 有理有据地发现数据集问题")
    
    # 创建分析器
    analyzer = ComprehensiveDatasetAnalyzer()
    
    # 执行各项分析
    analyzer.basic_statistics()
    analyzer.data_quality_assessment()
    analyzer.coordinate_system_analysis()
    analyzer.sample_diversity_analysis()
    
    # 生成综合报告
    report = analyzer.generate_comprehensive_report()
    
    # 保存报告
    report_dir = Path("results/dataset_analysis")
    report_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = report_dir / f"comprehensive_dataset_analysis_{timestamp}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 分析报告已保存: {report_file}")
    
    return analyzer, report

if __name__ == "__main__":
    analyzer, report = run_comprehensive_analysis()
