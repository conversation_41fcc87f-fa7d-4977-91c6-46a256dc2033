{"method": "Robust Ensemble Double SoftMax", "baseline_test_error": 7.579, "best_val_error": 6.899664402008057, "best_test_error": 12.991808891296387, "improvement_vs_baseline": -71.41851024272843, "val_test_consistency": 6.09214448928833, "training_time_minutes": 3.9659689982732136, "epochs_trained": 140, "history": [{"epoch": 1, "train_loss": 22.116076637716855, "val_loss": 22.450282669067384, "train_metrics": {"mean_distance": 42.249016256893384, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 42.93540649414062, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": {"mean_distance": 43.6439094543457, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0005}, {"epoch": 2, "train_loss": 21.909891913918887, "val_loss": 22.000374603271485, "train_metrics": {"mean_distance": 41.8982593311983, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 41.99613571166992, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 3, "train_loss": 21.700070100672104, "val_loss": 21.32149887084961, "train_metrics": {"mean_distance": 41.5373369104722, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 40.76724548339844, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 4, "train_loss": 21.540655360502356, "val_loss": 21.417002487182618, "train_metrics": {"mean_distance": 41.294043372659125, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 41.04443359375, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 5, "train_loss": 21.328353881835938, "val_loss": 21.384358978271486, "train_metrics": {"mean_distance": 40.919646094827094, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 40.9755355834961, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 6, "train_loss": 21.139346291037167, "val_loss": 20.638908767700194, "train_metrics": {"mean_distance": 40.62472287346335, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 39.72418060302734, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 7, "train_loss": 20.932970271391028, "val_loss": 20.216275024414063, "train_metrics": {"mean_distance": 40.28321658863741, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 38.91538009643555, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 8, "train_loss": 20.760571311501895, "val_loss": 20.0639705657959, "train_metrics": {"mean_distance": 39.98644144394819, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 38.703861236572266, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 9, "train_loss": 20.467086904189166, "val_loss": 19.763861083984374, "train_metrics": {"mean_distance": 39.48122899672564, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 38.17417831420899, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 10, "train_loss": 20.28299331665039, "val_loss": 19.28916816711426, "train_metrics": {"mean_distance": 39.19160753137925, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 37.435218048095706, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 11, "train_loss": 20.06330624748679, "val_loss": 18.401424407958984, "train_metrics": {"mean_distance": 38.818805918974036, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 35.909220123291014, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": {"mean_distance": 37.06161880493164, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0005}, {"epoch": 12, "train_loss": 19.88329315185547, "val_loss": 18.147406387329102, "train_metrics": {"mean_distance": 38.50805417229147, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 35.3700454711914, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 13, "train_loss": 19.71340347738827, "val_loss": 17.501285362243653, "train_metrics": {"mean_distance": 38.23244723151712, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 34.30034828186035, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 14, "train_loss": 19.37532907373765, "val_loss": 17.41548309326172, "train_metrics": {"mean_distance": 37.654486039105585, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 34.14723663330078, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 15, "train_loss": 19.167221405926874, "val_loss": 17.487677001953124, "train_metrics": {"mean_distance": 37.31224688361673, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 34.25497932434082, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 16, "train_loss": 18.874564114738913, "val_loss": 16.777704429626464, "train_metrics": {"mean_distance": 36.81276411168716, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 33.03088035583496, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 17, "train_loss": 18.572662353515625, "val_loss": 15.861879920959472, "train_metrics": {"mean_distance": 36.30966478235581, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 31.465128326416014, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 18, "train_loss": 18.329678479362936, "val_loss": 16.738893699645995, "train_metrics": {"mean_distance": 35.875655005959906, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 32.99935417175293, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 19, "train_loss": 18.01537311778349, "val_loss": 15.762150001525878, "train_metrics": {"mean_distance": 35.3494561139275, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 31.368349838256837, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 20, "train_loss": 17.992142396814682, "val_loss": 15.182776069641113, "train_metrics": {"mean_distance": 35.3038330078125, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 30.388381576538087, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 21, "train_loss": 17.611171834609088, "val_loss": 16.889623832702636, "train_metrics": {"mean_distance": 34.67231986101936, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 33.31274757385254, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": {"mean_distance": 34.15206400553385, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0005}, {"epoch": 22, "train_loss": 17.189628152286303, "val_loss": 16.760885620117186, "train_metrics": {"mean_distance": 33.991487839642694, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 33.02127952575684, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 23, "train_loss": 17.125443795148065, "val_loss": 16.16397171020508, "train_metrics": {"mean_distance": 33.84665376999799, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 32.03563232421875, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 24, "train_loss": 16.903849938336542, "val_loss": 16.574901008605956, "train_metrics": {"mean_distance": 33.46900592130773, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 32.87350311279297, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 25, "train_loss": 16.51427661671358, "val_loss": 15.581664276123046, "train_metrics": {"mean_distance": 32.773545657887176, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 31.103165435791016, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 26, "train_loss": 16.088570763083066, "val_loss": 16.451251983642578, "train_metrics": {"mean_distance": 32.08602826735552, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 32.68623809814453, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 27, "train_loss": 15.713420811821432, "val_loss": 15.407177925109863, "train_metrics": {"mean_distance": 31.422464595121497, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 30.911145782470705, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 28, "train_loss": 15.232646100661334, "val_loss": 14.967051887512207, "train_metrics": {"mean_distance": 30.617981854607077, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 30.149385452270508, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 29, "train_loss": 15.077305962057675, "val_loss": 15.064611625671386, "train_metrics": {"mean_distance": 30.34445504581227, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 30.272030639648438, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 30, "train_loss": 14.782011593089385, "val_loss": 14.51815128326416, "train_metrics": {"mean_distance": 29.83333138858571, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 29.339305114746093, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 31, "train_loss": 14.169626572552849, "val_loss": 15.09557056427002, "train_metrics": {"mean_distance": 28.829424689797793, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 30.40426559448242, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": {"mean_distance": 30.731363932291668, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0005}, {"epoch": 32, "train_loss": 13.979798148660098, "val_loss": 14.171207618713378, "train_metrics": {"mean_distance": 28.47875696070054, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 28.771855926513673, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 33, "train_loss": 13.58677723828484, "val_loss": 13.699264907836914, "train_metrics": {"mean_distance": 27.81686087215648, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 28.010989379882812, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 34, "train_loss": 13.22377519046559, "val_loss": 12.710247802734376, "train_metrics": {"mean_distance": 27.144309436573703, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 26.29537696838379, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 35, "train_loss": 13.040558646706973, "val_loss": 11.217783737182618, "train_metrics": {"mean_distance": 26.85079338971306, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 23.765544891357422, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 36, "train_loss": 12.522322205936208, "val_loss": 10.530808639526366, "train_metrics": {"mean_distance": 25.934952343211453, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 22.59167938232422, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 37, "train_loss": 12.20341334623449, "val_loss": 11.265340042114257, "train_metrics": {"mean_distance": 25.41332099016975, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 23.802685928344726, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 38, "train_loss": 11.776020162245807, "val_loss": 10.905696296691895, "train_metrics": {"mean_distance": 24.66044268888586, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 23.201052856445312, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 39, "train_loss": 11.336906601400937, "val_loss": 10.819816780090331, "train_metrics": {"mean_distance": 23.860846351174747, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 23.043463134765624, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 40, "train_loss": 10.504367323482738, "val_loss": 11.681385040283203, "train_metrics": {"mean_distance": 22.39764752107508, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 24.498713302612305, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 41, "train_loss": 10.194032837362851, "val_loss": 11.140755081176758, "train_metrics": {"mean_distance": 21.81804152096019, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 23.510007095336913, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": {"mean_distance": 24.360552469889324, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0005}, {"epoch": 42, "train_loss": 9.759411867927103, "val_loss": 10.226999473571777, "train_metrics": {"mean_distance": 21.02885616526884, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 21.965655899047853, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 43, "train_loss": 9.321675833533792, "val_loss": 9.953274917602538, "train_metrics": {"mean_distance": 20.103534810683307, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 21.478788757324217, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 44, "train_loss": 8.769698675941019, "val_loss": 8.025521087646485, "train_metrics": {"mean_distance": 19.0715591767255, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 17.71901626586914, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 45, "train_loss": 8.725098581875072, "val_loss": 8.202008152008057, "train_metrics": {"mean_distance": 18.90034787795123, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 17.981727600097656, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 46, "train_loss": 7.995828151702881, "val_loss": 7.999161148071289, "train_metrics": {"mean_distance": 17.516160628374884, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 17.50727996826172, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 47, "train_loss": 8.035592247458066, "val_loss": 6.2779888153076175, "train_metrics": {"mean_distance": 17.537052154541016, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 14.085544967651368, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 48, "train_loss": 7.131006296943216, "val_loss": 6.348061466217041, "train_metrics": {"mean_distance": 15.8088056900922, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 14.140571975708008, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 49, "train_loss": 7.293607627644258, "val_loss": 6.02149772644043, "train_metrics": {"mean_distance": 16.033559743095847, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 13.410492134094238, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 50, "train_loss": 6.8901994929594155, "val_loss": 6.97210464477539, "train_metrics": {"mean_distance": 15.255677447599524, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 15.428782844543457, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 51, "train_loss": 6.725474161260268, "val_loss": 5.503349781036377, "train_metrics": {"mean_distance": 14.780011569752412, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 12.172177505493163, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": {"mean_distance": 12.991808891296387, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0005}, {"epoch": 52, "train_loss": 6.619688118205351, "val_loss": 5.369861507415772, "train_metrics": {"mean_distance": 14.666728244108313, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 11.94846706390381, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 53, "train_loss": 5.800273811115938, "val_loss": 5.77988920211792, "train_metrics": {"mean_distance": 13.007472262663, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 12.939982414245605, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 54, "train_loss": 5.830059612498564, "val_loss": 5.384264373779297, "train_metrics": {"mean_distance": 12.92775187772863, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 12.042637062072753, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 55, "train_loss": 5.665124472449808, "val_loss": 4.6961335182189945, "train_metrics": {"mean_distance": 12.596377484938678, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 10.284531402587891, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 56, "train_loss": 5.589641571044922, "val_loss": 5.091152381896973, "train_metrics": {"mean_distance": 12.404620787676643, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 11.104142189025879, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 57, "train_loss": 5.538052250357235, "val_loss": 7.263418865203858, "train_metrics": {"mean_distance": 12.20464942034553, "within_5mm_percent": 0.0, "within_7mm_percent": 4.411764705882353}, "val_metrics": {"mean_distance": 15.696086120605468, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 58, "train_loss": 5.315021935631247, "val_loss": 4.590984296798706, "train_metrics": {"mean_distance": 11.817634975208955, "within_5mm_percent": 0.0, "within_7mm_percent": 1.4705882352941178}, "val_metrics": {"mean_distance": 10.05522575378418, "within_5mm_percent": 0.0, "within_7mm_percent": 5.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 59, "train_loss": 5.223081224104938, "val_loss": 4.567509126663208, "train_metrics": {"mean_distance": 11.596907896154066, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 10.250384521484374, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 60, "train_loss": 4.789775020935956, "val_loss": 4.130302572250367, "train_metrics": {"mean_distance": 10.68689250946045, "within_5mm_percent": 0.0, "within_7mm_percent": 13.235294117647058}, "val_metrics": {"mean_distance": 9.297222518920899, "within_5mm_percent": 0.0, "within_7mm_percent": 15.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 61, "train_loss": 4.694520641775692, "val_loss": 4.302912759780884, "train_metrics": {"mean_distance": 10.457672175239114, "within_5mm_percent": 0.0, "within_7mm_percent": 14.705882352941176}, "val_metrics": {"mean_distance": 9.548218059539796, "within_5mm_percent": 0.0, "within_7mm_percent": 20.0}, "test_metrics": {"mean_distance": 10.349526405334473, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0005}, {"epoch": 62, "train_loss": 4.7216590713052184, "val_loss": 3.752743673324585, "train_metrics": {"mean_distance": 10.481824482188506, "within_5mm_percent": 0.0, "within_7mm_percent": 14.705882352941176}, "val_metrics": {"mean_distance": 8.4740421295166, "within_5mm_percent": 0.0, "within_7mm_percent": 25.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 63, "train_loss": 4.871470409281113, "val_loss": 3.70866117477417, "train_metrics": {"mean_distance": 10.781249102424173, "within_5mm_percent": 0.0, "within_7mm_percent": 8.823529411764707}, "val_metrics": {"mean_distance": 8.340992164611816, "within_5mm_percent": 0.0, "within_7mm_percent": 25.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 64, "train_loss": 4.378785638248219, "val_loss": 3.5928715229034425, "train_metrics": {"mean_distance": 9.805856396170224, "within_5mm_percent": 0.0, "within_7mm_percent": 16.176470588235293}, "val_metrics": {"mean_distance": 8.200124073028565, "within_5mm_percent": 0.0, "within_7mm_percent": 25.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 65, "train_loss": 4.232632693122415, "val_loss": 3.978880500793457, "train_metrics": {"mean_distance": 9.50417563494514, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 26.470588235294116}, "val_metrics": {"mean_distance": 9.030359554290772, "within_5mm_percent": 0.0, "within_7mm_percent": 25.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 66, "train_loss": 4.348744168001063, "val_loss": 3.411332702636719, "train_metrics": {"mean_distance": 9.731279878055348, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 16.176470588235293}, "val_metrics": {"mean_distance": 7.861301040649414, "within_5mm_percent": 5.0, "within_7mm_percent": 35.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 67, "train_loss": 4.502181544023402, "val_loss": 3.298828601837158, "train_metrics": {"mean_distance": 9.947771969963522, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 27.941176470588236}, "val_metrics": {"mean_distance": 7.583327388763427, "within_5mm_percent": 10.0, "within_7mm_percent": 50.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 68, "train_loss": 4.4344501355115105, "val_loss": 3.2290830612182617, "train_metrics": {"mean_distance": 9.893321878769818, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 20.58823529411765}, "val_metrics": {"mean_distance": 7.4663670539855955, "within_5mm_percent": 10.0, "within_7mm_percent": 45.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 69, "train_loss": 4.034531593322754, "val_loss": 3.334549808502197, "train_metrics": {"mean_distance": 9.122682206770953, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 25.0}, "val_metrics": {"mean_distance": 7.73034553527832, "within_5mm_percent": 10.0, "within_7mm_percent": 35.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 70, "train_loss": 4.13491763788111, "val_loss": 3.9724178314208984, "train_metrics": {"mean_distance": 9.254772522870232, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 19.11764705882353}, "val_metrics": {"mean_distance": 8.902751922607422, "within_5mm_percent": 10.0, "within_7mm_percent": 20.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 71, "train_loss": 4.117249741273768, "val_loss": 3.982779312133789, "train_metrics": {"mean_distance": 9.114324289209703, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 27.941176470588236}, "val_metrics": {"mean_distance": 8.940873050689698, "within_5mm_percent": 15.0, "within_7mm_percent": 25.0}, "test_metrics": {"mean_distance": 9.551003774007162, "within_5mm_percent": 0.0, "within_7mm_percent": 16.666666666666668}, "learning_rate": 0.0005}, {"epoch": 72, "train_loss": 3.898750894209918, "val_loss": 4.757160902023315, "train_metrics": {"mean_distance": 8.75093277762918, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 32.35294117647059}, "val_metrics": {"mean_distance": 10.50761890411377, "within_5mm_percent": 0.0, "within_7mm_percent": 10.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 73, "train_loss": 3.9564074488247143, "val_loss": 3.352022981643677, "train_metrics": {"mean_distance": 8.90219643536736, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 29.41176470588235}, "val_metrics": {"mean_distance": 7.640138053894043, "within_5mm_percent": 20.0, "within_7mm_percent": 35.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 74, "train_loss": 3.8880531787872314, "val_loss": 5.150383472442627, "train_metrics": {"mean_distance": 8.729079078225528, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 38.23529411764706}, "val_metrics": {"mean_distance": 11.354780960083009, "within_5mm_percent": 0.0, "within_7mm_percent": 5.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 75, "train_loss": 4.107160960926729, "val_loss": 7.438245010375977, "train_metrics": {"mean_distance": 9.140652039471794, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 30.88235294117647}, "val_metrics": {"mean_distance": 16.176897621154787, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 76, "train_loss": 3.792424636728623, "val_loss": 5.7069628715515135, "train_metrics": {"mean_distance": 8.569495958440443, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 33.8235294117647}, "val_metrics": {"mean_distance": 12.38487377166748, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0005}, {"epoch": 77, "train_loss": 4.108625173568726, "val_loss": 8.009517383575439, "train_metrics": {"mean_distance": 9.1355201216305, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 25.0}, "val_metrics": {"mean_distance": 17.536464309692384, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0004}, {"epoch": 78, "train_loss": 4.118313130210428, "val_loss": 4.798603820800781, "train_metrics": {"mean_distance": 9.136793332941393, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 30.88235294117647}, "val_metrics": {"mean_distance": 10.582284545898437, "within_5mm_percent": 0.0, "within_7mm_percent": 10.0}, "test_metrics": null, "learning_rate": 0.0004}, {"epoch": 79, "train_loss": 4.117023986928603, "val_loss": 3.9829633712768553, "train_metrics": {"mean_distance": 9.119065453024472, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 25.0}, "val_metrics": {"mean_distance": 8.857178783416748, "within_5mm_percent": 5.0, "within_7mm_percent": 15.0}, "test_metrics": null, "learning_rate": 0.0004}, {"epoch": 80, "train_loss": 3.9251416150261376, "val_loss": 6.199205684661865, "train_metrics": {"mean_distance": 8.808232279384837, "within_5mm_percent": 10.294117647058824, "within_7mm_percent": 32.35294117647059}, "val_metrics": {"mean_distance": 13.469811820983887, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.0004}, {"epoch": 81, "train_loss": 3.9339089393615723, "val_loss": 4.295796155929565, "train_metrics": {"mean_distance": 8.83937095193302, "within_5mm_percent": 10.294117647058824, "within_7mm_percent": 29.41176470588235}, "val_metrics": {"mean_distance": 9.540875434875488, "within_5mm_percent": 10.0, "within_7mm_percent": 15.0}, "test_metrics": {"mean_distance": 10.0401398340861, "within_5mm_percent": 0.0, "within_7mm_percent": 16.666666666666668}, "learning_rate": 0.0004}, {"epoch": 82, "train_loss": 3.9678996591007007, "val_loss": 3.037282705307007, "train_metrics": {"mean_distance": 8.890151079963236, "within_5mm_percent": 10.294117647058824, "within_7mm_percent": 33.8235294117647}, "val_metrics": {"mean_distance": 7.057640647888183, "within_5mm_percent": 15.0, "within_7mm_percent": 50.0}, "test_metrics": null, "learning_rate": 0.0004}, {"epoch": 83, "train_loss": 4.046052666271434, "val_loss": 3.3332640171051025, "train_metrics": {"mean_distance": 8.960507308735567, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 29.41176470588235}, "val_metrics": {"mean_distance": 7.616901588439942, "within_5mm_percent": 20.0, "within_7mm_percent": 35.0}, "test_metrics": null, "learning_rate": 0.0004}, {"epoch": 84, "train_loss": 4.757697175530827, "val_loss": 3.081711530685425, "train_metrics": {"mean_distance": 10.343779760248522, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 27.941176470588236}, "val_metrics": {"mean_distance": 7.147927379608154, "within_5mm_percent": 20.0, "within_7mm_percent": 50.0}, "test_metrics": null, "learning_rate": 0.0004}, {"epoch": 85, "train_loss": 4.075921437319587, "val_loss": 3.282809543609619, "train_metrics": {"mean_distance": 9.042015103732838, "within_5mm_percent": 11.764705882352942, "within_7mm_percent": 33.8235294117647}, "val_metrics": {"mean_distance": 7.579568386077881, "within_5mm_percent": 10.0, "within_7mm_percent": 45.0}, "test_metrics": null, "learning_rate": 0.0004}, {"epoch": 86, "train_loss": 3.815049311693977, "val_loss": 3.1804605960845946, "train_metrics": {"mean_distance": 8.506926087772145, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 33.8235294117647}, "val_metrics": {"mean_distance": 7.33134822845459, "within_5mm_percent": 15.0, "within_7mm_percent": 50.0}, "test_metrics": null, "learning_rate": 0.0004}, {"epoch": 87, "train_loss": 4.058768062030568, "val_loss": 3.173936605453491, "train_metrics": {"mean_distance": 9.058902403887581, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 39.705882352941174}, "val_metrics": {"mean_distance": 7.388772106170654, "within_5mm_percent": 10.0, "within_7mm_percent": 55.0}, "test_metrics": null, "learning_rate": 0.0004}, {"epoch": 88, "train_loss": 4.219354769762824, "val_loss": 3.1611533641815184, "train_metrics": {"mean_distance": 9.339655595667223, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 23.529411764705884}, "val_metrics": {"mean_distance": 7.333680629730225, "within_5mm_percent": 20.0, "within_7mm_percent": 45.0}, "test_metrics": null, "learning_rate": 0.0004}, {"epoch": 89, "train_loss": 3.9074519942788517, "val_loss": 3.114952230453491, "train_metrics": {"mean_distance": 8.753549042870016, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 32.35294117647059}, "val_metrics": {"mean_distance": 7.198510551452637, "within_5mm_percent": 15.0, "within_7mm_percent": 50.0}, "test_metrics": null, "learning_rate": 0.0004}, {"epoch": 90, "train_loss": 4.310831813251271, "val_loss": 4.646889305114746, "train_metrics": {"mean_distance": 9.552438146927777, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 25.0}, "val_metrics": {"mean_distance": 10.3297438621521, "within_5mm_percent": 5.0, "within_7mm_percent": 20.0}, "test_metrics": null, "learning_rate": 0.0004}, {"epoch": 91, "train_loss": 3.706705962910372, "val_loss": 3.4227034568786623, "train_metrics": {"mean_distance": 8.369746965520521, "within_5mm_percent": 11.764705882352942, "within_7mm_percent": 41.1764705882353}, "val_metrics": {"mean_distance": 7.8563456535339355, "within_5mm_percent": 15.0, "within_7mm_percent": 40.0}, "test_metrics": {"mean_distance": 8.836355527242025, "within_5mm_percent": 16.666666666666668, "within_7mm_percent": 33.333333333333336}, "learning_rate": 0.00032}, {"epoch": 92, "train_loss": 3.955233265371884, "val_loss": 3.3808297634124758, "train_metrics": {"mean_distance": 8.84630713743322, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 30.88235294117647}, "val_metrics": {"mean_distance": 7.802528381347656, "within_5mm_percent": 20.0, "within_7mm_percent": 45.0}, "test_metrics": null, "learning_rate": 0.00032}, {"epoch": 93, "train_loss": 4.321845685734468, "val_loss": 3.170033264160156, "train_metrics": {"mean_distance": 9.527690971598906, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 29.41176470588235}, "val_metrics": {"mean_distance": 7.360986804962158, "within_5mm_percent": 15.0, "within_7mm_percent": 40.0}, "test_metrics": null, "learning_rate": 0.00032}, {"epoch": 94, "train_loss": 3.8539302629583023, "val_loss": 3.1431835174560545, "train_metrics": {"mean_distance": 8.649341723498177, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 35.294117647058826}, "val_metrics": {"mean_distance": 7.328272151947021, "within_5mm_percent": 20.0, "within_7mm_percent": 45.0}, "test_metrics": null, "learning_rate": 0.00032}, {"epoch": 95, "train_loss": 3.997156662099502, "val_loss": 3.104611349105835, "train_metrics": {"mean_distance": 8.916765745948343, "within_5mm_percent": 13.235294117647058, "within_7mm_percent": 30.88235294117647}, "val_metrics": {"mean_distance": 7.192332553863525, "within_5mm_percent": 30.0, "within_7mm_percent": 45.0}, "test_metrics": null, "learning_rate": 0.00032}, {"epoch": 96, "train_loss": 3.741352852653055, "val_loss": 3.041618585586548, "train_metrics": {"mean_distance": 8.443403103772331, "within_5mm_percent": 11.764705882352942, "within_7mm_percent": 39.705882352941174}, "val_metrics": {"mean_distance": 7.116698932647705, "within_5mm_percent": 20.0, "within_7mm_percent": 50.0}, "test_metrics": null, "learning_rate": 0.00032}, {"epoch": 97, "train_loss": 3.8659249894759236, "val_loss": 3.0068763732910155, "train_metrics": {"mean_distance": 8.725728652056526, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 32.35294117647059}, "val_metrics": {"mean_distance": 7.077178001403809, "within_5mm_percent": 25.0, "within_7mm_percent": 45.0}, "test_metrics": null, "learning_rate": 0.00032}, {"epoch": 98, "train_loss": 4.041514466790592, "val_loss": 3.229003143310547, "train_metrics": {"mean_distance": 8.999572220970602, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 35.294117647058826}, "val_metrics": {"mean_distance": 7.479890537261963, "within_5mm_percent": 15.0, "within_7mm_percent": 40.0}, "test_metrics": null, "learning_rate": 0.00032}, {"epoch": 99, "train_loss": 3.854265381308163, "val_loss": 2.9661103248596192, "train_metrics": {"mean_distance": 8.683075568255257, "within_5mm_percent": 10.294117647058824, "within_7mm_percent": 32.35294117647059}, "val_metrics": {"mean_distance": 6.95425443649292, "within_5mm_percent": 25.0, "within_7mm_percent": 55.0}, "test_metrics": null, "learning_rate": 0.00032}, {"epoch": 100, "train_loss": 4.09348025041468, "val_loss": 3.60229549407959, "train_metrics": {"mean_distance": 9.104037677540498, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 36.76470588235294}, "val_metrics": {"mean_distance": 8.189247035980225, "within_5mm_percent": 15.0, "within_7mm_percent": 35.0}, "test_metrics": null, "learning_rate": 0.00032}, {"epoch": 101, "train_loss": 3.6737964153289795, "val_loss": 3.015225076675415, "train_metrics": {"mean_distance": 8.342855088851032, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 35.294117647058826}, "val_metrics": {"mean_distance": 7.06598539352417, "within_5mm_percent": 20.0, "within_7mm_percent": 50.0}, "test_metrics": {"mean_distance": 8.226375897725424, "within_5mm_percent": 16.666666666666668, "within_7mm_percent": 33.333333333333336}, "learning_rate": 0.00032}, {"epoch": 102, "train_loss": 4.036986280890072, "val_loss": 3.043031644821167, "train_metrics": {"mean_distance": 9.023488325231215, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 29.41176470588235}, "val_metrics": {"mean_distance": 7.067611026763916, "within_5mm_percent": 15.0, "within_7mm_percent": 45.0}, "test_metrics": null, "learning_rate": 0.00032}, {"epoch": 103, "train_loss": 4.184245656518375, "val_loss": 3.083196258544922, "train_metrics": {"mean_distance": 9.309880060308119, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 33.8235294117647}, "val_metrics": {"mean_distance": 7.192480087280273, "within_5mm_percent": 15.0, "within_7mm_percent": 50.0}, "test_metrics": null, "learning_rate": 0.00032}, {"epoch": 104, "train_loss": 3.8500223440282486, "val_loss": 3.0061106204986574, "train_metrics": {"mean_distance": 8.662874333998737, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 32.35294117647059}, "val_metrics": {"mean_distance": 7.024901866912842, "within_5mm_percent": 20.0, "within_7mm_percent": 55.0}, "test_metrics": null, "learning_rate": 0.00032}, {"epoch": 105, "train_loss": 4.019443483913646, "val_loss": 4.463029193878174, "train_metrics": {"mean_distance": 8.966849327087402, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 26.470588235294116}, "val_metrics": {"mean_distance": 9.97937889099121, "within_5mm_percent": 5.0, "within_7mm_percent": 15.0}, "test_metrics": null, "learning_rate": 0.00032}, {"epoch": 106, "train_loss": 4.362230146632475, "val_loss": 4.712004232406616, "train_metrics": {"mean_distance": 9.57700106676887, "within_5mm_percent": 8.823529411764707, "within_7mm_percent": 35.294117647058826}, "val_metrics": {"mean_distance": 10.4325382232666, "within_5mm_percent": 5.0, "within_7mm_percent": 10.0}, "test_metrics": null, "learning_rate": 0.00032}, {"epoch": 107, "train_loss": 3.791041949216057, "val_loss": 3.3751750946044923, "train_metrics": {"mean_distance": 8.558218423058005, "within_5mm_percent": 10.294117647058824, "within_7mm_percent": 33.8235294117647}, "val_metrics": {"mean_distance": 7.78349723815918, "within_5mm_percent": 20.0, "within_7mm_percent": 35.0}, "test_metrics": null, "learning_rate": 0.00032}, {"epoch": 108, "train_loss": 4.162129682653091, "val_loss": 3.006123733520508, "train_metrics": {"mean_distance": 9.191945608924417, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 33.8235294117647}, "val_metrics": {"mean_distance": 7.043768501281738, "within_5mm_percent": 20.0, "within_7mm_percent": 45.0}, "test_metrics": null, "learning_rate": 0.00025600000000000004}, {"epoch": 109, "train_loss": 4.112996872733621, "val_loss": 5.044490909576416, "train_metrics": {"mean_distance": 9.1658738080193, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 29.41176470588235}, "val_metrics": {"mean_distance": 11.160561180114746, "within_5mm_percent": 0.0, "within_7mm_percent": 5.0}, "test_metrics": null, "learning_rate": 0.00025600000000000004}, {"epoch": 110, "train_loss": 3.8233406824224136, "val_loss": 2.9618204116821287, "train_metrics": {"mean_distance": 8.605532702277689, "within_5mm_percent": 8.823529411764707, "within_7mm_percent": 36.76470588235294}, "val_metrics": {"mean_distance": 6.899664402008057, "within_5mm_percent": 20.0, "within_7mm_percent": 55.0}, "test_metrics": null, "learning_rate": 0.00025600000000000004}, {"epoch": 111, "train_loss": 4.343951000886805, "val_loss": 3.0527062892913817, "train_metrics": {"mean_distance": 9.54912384818582, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 32.35294117647059}, "val_metrics": {"mean_distance": 7.098674201965332, "within_5mm_percent": 25.0, "within_7mm_percent": 45.0}, "test_metrics": {"mean_distance": 7.707077980041504, "within_5mm_percent": 16.666666666666668, "within_7mm_percent": 41.666666666666664}, "learning_rate": 0.00025600000000000004}, {"epoch": 112, "train_loss": 3.960959855247946, "val_loss": 3.0186984539031982, "train_metrics": {"mean_distance": 8.904456110561595, "within_5mm_percent": 10.294117647058824, "within_7mm_percent": 33.8235294117647}, "val_metrics": {"mean_distance": 7.037892532348633, "within_5mm_percent": 15.0, "within_7mm_percent": 40.0}, "test_metrics": null, "learning_rate": 0.00025600000000000004}, {"epoch": 113, "train_loss": 4.28927365471335, "val_loss": 3.9479631423950194, "train_metrics": {"mean_distance": 9.485614720512839, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 23.529411764705884}, "val_metrics": {"mean_distance": 8.843515205383301, "within_5mm_percent": 10.0, "within_7mm_percent": 20.0}, "test_metrics": null, "learning_rate": 0.00025600000000000004}, {"epoch": 114, "train_loss": 3.5870952746447395, "val_loss": 5.8339869499206545, "train_metrics": {"mean_distance": 8.154060195474063, "within_5mm_percent": 10.294117647058824, "within_7mm_percent": 33.8235294117647}, "val_metrics": {"mean_distance": 12.772049713134766, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00025600000000000004}, {"epoch": 115, "train_loss": 4.090091284583597, "val_loss": 7.948384094238281, "train_metrics": {"mean_distance": 9.042004529167624, "within_5mm_percent": 10.294117647058824, "within_7mm_percent": 32.35294117647059}, "val_metrics": {"mean_distance": 17.321697998046876, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00025600000000000004}, {"epoch": 116, "train_loss": 3.843435049057007, "val_loss": 6.868502521514893, "train_metrics": {"mean_distance": 8.577978639041676, "within_5mm_percent": 10.294117647058824, "within_7mm_percent": 35.294117647058826}, "val_metrics": {"mean_distance": 14.929263496398926, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00025600000000000004}, {"epoch": 117, "train_loss": 3.8378419034621296, "val_loss": 5.987487030029297, "train_metrics": {"mean_distance": 8.623325123506433, "within_5mm_percent": 8.823529411764707, "within_7mm_percent": 29.41176470588235}, "val_metrics": {"mean_distance": 13.066683769226074, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00025600000000000004}, {"epoch": 118, "train_loss": 3.9240800492903767, "val_loss": 5.273277854919433, "train_metrics": {"mean_distance": 8.770018942215863, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 36.76470588235294}, "val_metrics": {"mean_distance": 11.711310195922852, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00025600000000000004}, {"epoch": 119, "train_loss": 3.698796678991879, "val_loss": 6.473280620574951, "train_metrics": {"mean_distance": 8.34945650661693, "within_5mm_percent": 10.294117647058824, "within_7mm_percent": 36.76470588235294}, "val_metrics": {"mean_distance": 14.080322265625, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00020480000000000004}, {"epoch": 120, "train_loss": 4.069494303535013, "val_loss": 4.563727951049804, "train_metrics": {"mean_distance": 9.033701532027301, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 29.41176470588235}, "val_metrics": {"mean_distance": 10.16124668121338, "within_5mm_percent": 0.0, "within_7mm_percent": 15.0}, "test_metrics": null, "learning_rate": 0.00020480000000000004}, {"epoch": 121, "train_loss": 3.973003457574283, "val_loss": 5.868366050720215, "train_metrics": {"mean_distance": 8.879102370318245, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 32.35294117647059}, "val_metrics": {"mean_distance": 12.880110549926759, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": {"mean_distance": 11.931396484375, "within_5mm_percent": 0.0, "within_7mm_percent": 16.666666666666668}, "learning_rate": 0.00020480000000000004}, {"epoch": 122, "train_loss": 3.926173350390266, "val_loss": 4.901753711700439, "train_metrics": {"mean_distance": 8.795536013210521, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 27.941176470588236}, "val_metrics": {"mean_distance": 10.902031707763673, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00020480000000000004}, {"epoch": 123, "train_loss": 3.6979216267080868, "val_loss": 6.67204532623291, "train_metrics": {"mean_distance": 8.393705143648035, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 39.705882352941174}, "val_metrics": {"mean_distance": 14.446603584289551, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00020480000000000004}, {"epoch": 124, "train_loss": 3.6359192343319164, "val_loss": 4.394481945037842, "train_metrics": {"mean_distance": 8.150212596444522, "within_5mm_percent": 8.823529411764707, "within_7mm_percent": 42.64705882352941}, "val_metrics": {"mean_distance": 9.787814140319824, "within_5mm_percent": 0.0, "within_7mm_percent": 15.0}, "test_metrics": null, "learning_rate": 0.00020480000000000004}, {"epoch": 125, "train_loss": 3.713396493126364, "val_loss": 7.591022682189942, "train_metrics": {"mean_distance": 8.325738121481503, "within_5mm_percent": 11.764705882352942, "within_7mm_percent": 36.76470588235294}, "val_metrics": {"mean_distance": 16.37008590698242, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00020480000000000004}, {"epoch": 126, "train_loss": 3.9264724675346825, "val_loss": 5.8019109725952145, "train_metrics": {"mean_distance": 8.819925027735094, "within_5mm_percent": 8.823529411764707, "within_7mm_percent": 27.941176470588236}, "val_metrics": {"mean_distance": 12.647083473205566, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00020480000000000004}, {"epoch": 127, "train_loss": 3.7126669182496914, "val_loss": 4.61346549987793, "train_metrics": {"mean_distance": 8.341501600602093, "within_5mm_percent": 10.294117647058824, "within_7mm_percent": 38.23529411764706}, "val_metrics": {"mean_distance": 10.311306953430176, "within_5mm_percent": 0.0, "within_7mm_percent": 10.0}, "test_metrics": null, "learning_rate": 0.00020480000000000004}, {"epoch": 128, "train_loss": 4.2956172718721275, "val_loss": 6.400404930114746, "train_metrics": {"mean_distance": 9.452709197998047, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 23.529411764705884}, "val_metrics": {"mean_distance": 13.834807014465332, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00016384000000000006}, {"epoch": 129, "train_loss": 3.702830609153299, "val_loss": 7.217441177368164, "train_metrics": {"mean_distance": 8.422253019669476, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 33.8235294117647}, "val_metrics": {"mean_distance": 15.57105541229248, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00016384000000000006}, {"epoch": 130, "train_loss": 3.9613504970774933, "val_loss": 8.322721862792969, "train_metrics": {"mean_distance": 8.86478463341208, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 33.8235294117647}, "val_metrics": {"mean_distance": 18.108553314208983, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00016384000000000006}, {"epoch": 131, "train_loss": 3.777178638121661, "val_loss": 5.819451999664307, "train_metrics": {"mean_distance": 8.46225814258351, "within_5mm_percent": 11.764705882352942, "within_7mm_percent": 35.294117647058826}, "val_metrics": {"mean_distance": 12.714667320251465, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": {"mean_distance": 11.91246223449707, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00016384000000000006}, {"epoch": 132, "train_loss": 3.9643132826861214, "val_loss": 6.350015258789062, "train_metrics": {"mean_distance": 8.867318013135124, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 32.35294117647059}, "val_metrics": {"mean_distance": 13.785619354248047, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00016384000000000006}, {"epoch": 133, "train_loss": 3.8389780661639046, "val_loss": 6.85764102935791, "train_metrics": {"mean_distance": 8.59187625436222, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 39.705882352941174}, "val_metrics": {"mean_distance": 14.873072052001953, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00016384000000000006}, {"epoch": 134, "train_loss": 3.7823945073520435, "val_loss": 5.880467319488526, "train_metrics": {"mean_distance": 8.524499780991498, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 33.8235294117647}, "val_metrics": {"mean_distance": 12.794106101989746, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00016384000000000006}, {"epoch": 135, "train_loss": 4.339567128349753, "val_loss": 8.056024742126464, "train_metrics": {"mean_distance": 9.572802964378806, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 30.88235294117647}, "val_metrics": {"mean_distance": 17.510921669006347, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00016384000000000006}, {"epoch": 136, "train_loss": 4.017613971934599, "val_loss": 6.879025650024414, "train_metrics": {"mean_distance": 8.95206563612994, "within_5mm_percent": 8.823529411764707, "within_7mm_percent": 33.8235294117647}, "val_metrics": {"mean_distance": 14.879909706115722, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00016384000000000006}, {"epoch": 137, "train_loss": 3.9862494047950294, "val_loss": 9.557159996032714, "train_metrics": {"mean_distance": 8.915096703697653, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 29.41176470588235}, "val_metrics": {"mean_distance": 20.88297348022461, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00013107200000000006}, {"epoch": 138, "train_loss": 3.838430601007798, "val_loss": 7.812979507446289, "train_metrics": {"mean_distance": 8.551826252656824, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 33.8235294117647}, "val_metrics": {"mean_distance": 16.929665946960448, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00013107200000000006}, {"epoch": 139, "train_loss": 3.644649561713724, "val_loss": 10.410244369506836, "train_metrics": {"mean_distance": 8.23798547071569, "within_5mm_percent": 11.764705882352942, "within_7mm_percent": 38.23529411764706}, "val_metrics": {"mean_distance": 22.676021575927734, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00013107200000000006}, {"epoch": 140, "train_loss": 3.7004791848799763, "val_loss": 8.6943510055542, "train_metrics": {"mean_distance": 8.382420624003691, "within_5mm_percent": 8.823529411764707, "within_7mm_percent": 42.64705882352941}, "val_metrics": {"mean_distance": 18.853686141967774, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "test_metrics": null, "learning_rate": 0.00013107200000000006}], "robust_config": {"dropout_rate": 0.4, "weight_decay": 0.0002, "learning_rate": 0.0005, "loss_function": "<PERSON><PERSON>(0.6) + SmoothL1(0.4)", "num_ensembles": 3, "candidate_points": 200, "augmentation": "conservative"}}