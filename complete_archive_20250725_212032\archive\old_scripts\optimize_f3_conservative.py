#!/usr/bin/env python3
"""
Conservative F3 Optimization

Based on results, complex changes hurt performance. Let's make minimal,
targeted improvements to the working baseline (18.40mm).
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import torch.nn.functional as F
import numpy as np
from pathlib import Path
import time
import json

class F3Dataset(torch.utils.data.Dataset):
    """Original F3 dataset without normalization"""
    
    def __init__(self, data_dir, split, augment=False):
        self.split_dir = Path(data_dir) / split
        self.files = list(self.split_dir.glob('*_keypoints.npy'))
        self.augment = augment and split == 'train'
        print(f"{split}: {len(self.files)} samples")
        
    def __len__(self):
        return len(self.files)
    
    def __getitem__(self, idx):
        kp_file = self.files[idx]
        sample_id = kp_file.stem.replace('_keypoints', '')
        
        keypoints = np.load(kp_file).astype(np.float32)
        pc_file = kp_file.parent / f"{sample_id}_pointcloud.npy"
        pointcloud = np.load(pc_file).astype(np.float32)
        
        # Conservative augmentation only
        if self.augment:
            # Very small rotation
            angle = np.random.uniform(-np.pi/36, np.pi/36)  # ±5 degrees
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ], dtype=np.float32)
            
            pointcloud = pointcloud @ rotation_matrix.T
            keypoints = keypoints @ rotation_matrix.T
            
            # Very small translation
            translation = np.random.uniform(-0.5, 0.5, 3).astype(np.float32)
            pointcloud += translation
            keypoints += translation
        
        return torch.FloatTensor(pointcloud), torch.FloatTensor(keypoints)

# Model 1: Enhanced Simple Net (minimal changes)
class EnhancedSimpleNet(nn.Module):
    """Minimally enhanced version of the working simple net"""
    
    def __init__(self):
        super().__init__()
        # Keep the working architecture, add minimal improvements
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        
        # Add batch normalization (minimal change)
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        
        # Slightly deeper regression head
        self.fc1 = nn.Linear(256, 128)
        self.fc2 = nn.Linear(128, 64)
        self.fc3 = nn.Linear(64, 19 * 3)
        
        # Light dropout
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, x):
        x = x.transpose(2, 1)
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        x = torch.max(x, 2)[0]
        
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.fc3(x)
        
        return x.view(-1, 19, 3)

# Model 2: Deeper Simple Net
class DeeperSimpleNet(nn.Module):
    """Slightly deeper version of simple net"""
    
    def __init__(self):
        super().__init__()
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)  # Add one more layer
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
        self.fc1 = nn.Linear(512, 256)
        self.fc2 = nn.Linear(256, 128)
        self.fc3 = nn.Linear(128, 19 * 3)
        
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, x):
        x = x.transpose(2, 1)
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        x = F.relu(self.bn4(self.conv4(x)))
        x = torch.max(x, 2)[0]
        
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.fc3(x)
        
        return x.view(-1, 19, 3)

# Model 3: Original Simple Net with better training
class OriginalSimpleNet(nn.Module):
    """Exact copy of the working simple net"""
    
    def __init__(self):
        super().__init__()
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.fc1 = nn.Linear(256, 128)
        self.fc2 = nn.Linear(128, 19 * 3)
        
    def forward(self, x):
        x = x.transpose(2, 1)
        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))
        x = torch.relu(self.conv3(x))
        x = torch.max(x, 2)[0]
        x = torch.relu(self.fc1(x))
        x = self.fc2(x)
        return x.view(-1, 19, 3)

def calculate_metrics(pred, target):
    """Calculate evaluation metrics"""
    pred_np = pred.cpu().numpy()
    target_np = target.cpu().numpy()
    
    distances = np.linalg.norm(pred_np - target_np, axis=-1)
    mean_error = np.mean(distances)
    
    acc_5mm = np.mean(distances <= 5.0) * 100
    acc_10mm = np.mean(distances <= 10.0) * 100
    acc_15mm = np.mean(distances <= 15.0) * 100
    
    return {
        'mean_error': mean_error,
        'acc_5mm': acc_5mm,
        'acc_10mm': acc_10mm,
        'acc_15mm': acc_15mm
    }

def train_conservative_model(model, model_name, device, train_loader, val_loader):
    """Train model with conservative approach"""
    
    print(f"\n🚀 训练 {model_name}")
    print("=" * 50)
    
    model = model.to(device)
    criterion = nn.MSELoss()
    
    # Try different optimizers and learning rates
    optimizers = [
        ('Adam-0.001', optim.Adam(model.parameters(), lr=0.001)),
        ('Adam-0.0005', optim.Adam(model.parameters(), lr=0.0005)),
        ('AdamW-0.001', optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-5))
    ]
    
    best_error = float('inf')
    best_config = None
    
    for opt_name, optimizer in optimizers:
        print(f"   尝试优化器: {opt_name}")
        
        # Reset model
        model.apply(lambda m: m.reset_parameters() if hasattr(m, 'reset_parameters') else None)
        
        current_best = float('inf')
        
        for epoch in range(25):
            # Training
            model.train()
            train_loss = 0
            
            for pc, kp in train_loader:
                pc, kp = pc.to(device), kp.to(device)
                
                optimizer.zero_grad()
                pred = model(pc)
                loss = criterion(pred, kp)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
            
            train_loss /= len(train_loader)
            
            # Validation
            model.eval()
            val_loss = 0
            all_pred = []
            all_target = []
            
            with torch.no_grad():
                for pc, kp in val_loader:
                    pc, kp = pc.to(device), kp.to(device)
                    pred = model(pc)
                    loss = criterion(pred, kp)
                    val_loss += loss.item()
                    
                    all_pred.append(pred.cpu())
                    all_target.append(kp.cpu())
            
            val_loss /= len(val_loader)
            
            # Metrics
            all_pred = torch.cat(all_pred, dim=0)
            all_target = torch.cat(all_target, dim=0)
            metrics = calculate_metrics(all_pred, all_target)
            
            if metrics['mean_error'] < current_best:
                current_best = metrics['mean_error']
            
            # Print progress occasionally
            if epoch % 10 == 0 or epoch == 24:
                print(f"      Epoch {epoch+1}: Error={metrics['mean_error']:.2f}mm")
        
        print(f"   {opt_name} 最佳: {current_best:.2f}mm")
        
        if current_best < best_error:
            best_error = current_best
            best_config = opt_name
            # Save best model
            torch.save(model.state_dict(), f'best_{model_name.lower().replace(" ", "_")}.pth')
    
    print(f"   最终最佳: {best_error:.2f}mm (配置: {best_config})")
    
    return best_error, best_config

def main():
    """Main conservative optimization"""
    
    print("🎯 **F3保守优化策略**")
    print("📊 **基线: 18.40mm → 目标: 保守改进到<15mm**")
    print("🧠 **策略: 最小化改动，专注于有效的小改进**")
    print("=" * 80)
    
    # Setup
    device = torch.device('cuda:2' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # Data (no normalization, conservative augmentation)
    train_dataset = F3Dataset("F3SimpleDataset", "train", augment=True)
    val_dataset = F3Dataset("F3SimpleDataset", "val", augment=False)
    
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False, num_workers=2)
    
    # Models to test (conservative improvements)
    models = {
        "Original": OriginalSimpleNet(),
        "Enhanced": EnhancedSimpleNet(),
        "Deeper": DeeperSimpleNet()
    }
    
    results = {}
    
    # Train each model
    for model_name, model in models.items():
        try:
            best_error, best_config = train_conservative_model(
                model, model_name, device, train_loader, val_loader
            )
            results[model_name] = {
                'best_error': best_error,
                'best_config': best_config,
                'parameters': sum(p.numel() for p in model.parameters())
            }
        except Exception as e:
            print(f"❌ {model_name} 训练失败: {e}")
            results[model_name] = {'error': str(e)}
    
    # Results summary
    print(f"\n🏆 **F3保守优化结果**")
    print("=" * 60)
    
    successful_models = {k: v for k, v in results.items() if 'best_error' in v}
    
    if successful_models:
        # Sort by performance
        sorted_models = sorted(successful_models.items(), key=lambda x: x[1]['best_error'])
        
        print(f"📊 **性能排名**:")
        for i, (name, result) in enumerate(sorted_models):
            error = result['best_error']
            config = result['best_config']
            params = result['parameters']
            improvement = (18.40 - error) / 18.40 * 100
            
            print(f"   {i+1}. {name}:")
            print(f"      误差: {error:.2f}mm")
            print(f"      改进: {improvement:+.1f}% vs 基线")
            print(f"      配置: {config}")
            print(f"      参数: {params:,}")
            
            if error <= 10.0:
                print(f"      🎉 医疗级精度!")
            elif error <= 15.0:
                print(f"      ✅ 良好改进")
            elif error < 18.40:
                print(f"      ⚠️ 小幅改进")
            else:
                print(f"      ❌ 性能下降")
        
        best_model, best_result = sorted_models[0]
        print(f"\n🥇 **最佳模型: {best_model}**")
        print(f"   最终误差: {best_result['best_error']:.2f}mm")
        print(f"   vs 基线改进: {(18.40 - best_result['best_error']) / 18.40 * 100:.1f}%")
        
        if best_result['best_error'] <= 10.0:
            print(f"   🎉 成功达到医疗级精度!")
        elif best_result['best_error'] <= 15.0:
            print(f"   ✅ 显著改进，接近医疗级精度")
        elif best_result['best_error'] < 18.40:
            print(f"   ⚠️ 小幅改进，需要更多优化")
        else:
            print(f"   ❌ 未能改进基线")
    
    # Save results
    with open('f3_conservative_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📁 保守优化结果已保存: f3_conservative_results.json")
    
    return results

if __name__ == "__main__":
    try:
        results = main()
        print(f"\n🎉 F3保守优化完成!")
    except Exception as e:
        print(f"❌ 保守优化失败: {e}")
        import traceback
        traceback.print_exc()
