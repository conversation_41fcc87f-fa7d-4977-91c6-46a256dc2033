nohup: ignoring input
🌙 **综合过夜优化策略**
😴 **您可以安心去睡觉，我会持续优化**
🎯 **目标: 寻找突破5.857mm的方法**
================================================================================
🌙 综合过夜优化器启动
📁 结果保存目录: overnight_optimization_20250717_235307
🎯 目标: 突破5.857mm

🌙 **综合过夜优化开始**
🎯 **目标: 寻找突破5.857mm的方法**
⏰ **预计运行时间: 6-8小时**
================================================================================
📊 训练数据: 85个样本
🔧 优化配置: 16个

进度: 1/16 (已运行0.0小时)

🔍 测试配置: 原始最佳
🔧 高级模型: 22,533参数, 配置: {'name': '原始最佳', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 22,533参数, 配置: {'name': '原始最佳', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 22,533参数, 配置: {'name': '原始最佳', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
   结果: 6.572±0.360mm (最佳: 6.274mm)

进度: 2/16 (已运行0.0小时)

🔍 测试配置: 黄金比例维度
🔧 高级模型: 17,481参数, 配置: {'name': '黄金比例维度', 'feature_dims': [28, 56, 112], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 17,481参数, 配置: {'name': '黄金比例维度', 'feature_dims': [28, 56, 112], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 17,481参数, 配置: {'name': '黄金比例维度', 'feature_dims': [28, 56, 112], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
   结果: 6.496±0.180mm (最佳: 6.280mm)

进度: 3/16 (已运行0.0小时)

🔍 测试配置: 紧凑维度
🔧 高级模型: 13,069参数, 配置: {'name': '紧凑维度', 'feature_dims': [24, 48, 96], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.25, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 13,069参数, 配置: {'name': '紧凑维度', 'feature_dims': [24, 48, 96], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.25, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 13,069参数, 配置: {'name': '紧凑维度', 'feature_dims': [24, 48, 96], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.25, 'augment_strategy': 'conservative', 'augment_factor': 2}
   结果: 6.554±0.312mm (最佳: 6.147mm)

进度: 4/16 (已运行0.0小时)

🔍 测试配置: Swish激活
🔧 高级模型: 22,533参数, 配置: {'name': 'Swish激活', 'feature_dims': [32, 64, 128], 'activation': 'swish', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 22,533参数, 配置: {'name': 'Swish激活', 'feature_dims': [32, 64, 128], 'activation': 'swish', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 22,533参数, 配置: {'name': 'Swish激活', 'feature_dims': [32, 64, 128], 'activation': 'swish', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
   结果: 6.384±0.390mm (最佳: 6.051mm)

进度: 5/16 (已运行0.0小时)

🔍 测试配置: GELU激活
🔧 高级模型: 22,533参数, 配置: {'name': 'GELU激活', 'feature_dims': [32, 64, 128], 'activation': 'gelu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 22,533参数, 配置: {'name': 'GELU激活', 'feature_dims': [32, 64, 128], 'activation': 'gelu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 22,533参数, 配置: {'name': 'GELU激活', 'feature_dims': [32, 64, 128], 'activation': 'gelu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
   结果: 6.569±0.263mm (最佳: 6.226mm)

进度: 6/16 (已运行0.1小时)

🔍 测试配置: α=0.52
🔧 高级模型: 22,533参数, 配置: {'name': 'α=0.52', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.52, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 22,533参数, 配置: {'name': 'α=0.52', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.52, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 22,533参数, 配置: {'name': 'α=0.52', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.52, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
   结果: 6.676±0.217mm (最佳: 6.449mm)

进度: 7/16 (已运行0.1小时)

🔍 测试配置: α=0.58
🔧 高级模型: 22,533参数, 配置: {'name': 'α=0.58', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.58, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 22,533参数, 配置: {'name': 'α=0.58', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.58, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 22,533参数, 配置: {'name': 'α=0.58', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.58, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
   结果: 6.656±0.131mm (最佳: 6.557mm)

进度: 8/16 (已运行0.1小时)

🔍 测试配置: α=0.62
🔧 高级模型: 22,533参数, 配置: {'name': 'α=0.62', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.62, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 22,533参数, 配置: {'name': 'α=0.62', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.62, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 22,533参数, 配置: {'name': 'α=0.62', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.62, 'dropout_rate': 0.3, 'augment_strategy': 'conservative', 'augment_factor': 2}
   结果: 6.680±0.136mm (最佳: 6.558mm)

进度: 9/16 (已运行0.1小时)

🔍 测试配置: 残差连接
🔧 高级模型: 28,871参数, 配置: {'name': '残差连接', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'use_residual': True, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 28,871参数, 配置: {'name': '残差连接', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'use_residual': True, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 28,871参数, 配置: {'name': '残差连接', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'use_residual': True, 'augment_strategy': 'conservative', 'augment_factor': 2}
   结果: 6.694±0.281mm (最佳: 6.398mm)

进度: 10/16 (已运行0.1小时)

🔍 测试配置: 注意力机制
🔧 高级模型: 26,694参数, 配置: {'name': '注意力机制', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'use_attention': True, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 26,694参数, 配置: {'name': '注意力机制', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'use_attention': True, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 26,694参数, 配置: {'name': '注意力机制', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'use_attention': True, 'augment_strategy': 'conservative', 'augment_factor': 2}
   结果: 6.797±0.319mm (最佳: 6.532mm)

进度: 11/16 (已运行0.1小时)

🔍 测试配置: LayerNorm
🔧 高级模型: 22,533参数, 配置: {'name': 'LayerNorm', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'use_layernorm': True, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 22,533参数, 配置: {'name': 'LayerNorm', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'use_layernorm': True, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 22,533参数, 配置: {'name': 'LayerNorm', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'use_layernorm': True, 'augment_strategy': 'conservative', 'augment_factor': 2}
   结果: 6.808±0.315mm (最佳: 6.561mm)

进度: 12/16 (已运行0.1小时)

🔍 测试配置: 多重统计先验
🔧 高级模型: 22,535参数, 配置: {'name': '多重统计先验', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'use_multi_prior': True, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 22,535参数, 配置: {'name': '多重统计先验', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'use_multi_prior': True, 'augment_strategy': 'conservative', 'augment_factor': 2}
🔧 高级模型: 22,535参数, 配置: {'name': '多重统计先验', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'use_multi_prior': True, 'augment_strategy': 'conservative', 'augment_factor': 2}
   结果: 6.555±0.225mm (最佳: 6.346mm)

进度: 13/16 (已运行0.1小时)

🔍 测试配置: 残差+注意力
🔧 高级模型: 25,548参数, 配置: {'name': '残差+注意力', 'feature_dims': [28, 56, 112], 'activation': 'swish', 'alpha_init': 0.58, 'dropout_rate': 0.25, 'use_residual': True, 'use_attention': True, 'augment_strategy': 'moderate', 'augment_factor': 3}
🔧 高级模型: 25,548参数, 配置: {'name': '残差+注意力', 'feature_dims': [28, 56, 112], 'activation': 'swish', 'alpha_init': 0.58, 'dropout_rate': 0.25, 'use_residual': True, 'use_attention': True, 'augment_strategy': 'moderate', 'augment_factor': 3}
🔧 高级模型: 25,548参数, 配置: {'name': '残差+注意力', 'feature_dims': [28, 56, 112], 'activation': 'swish', 'alpha_init': 0.58, 'dropout_rate': 0.25, 'use_residual': True, 'use_attention': True, 'augment_strategy': 'moderate', 'augment_factor': 3}
   结果: 6.791±0.335mm (最佳: 6.523mm)

进度: 14/16 (已运行0.1小时)

🔍 测试配置: 全技术组合
🔧 高级模型: 19,026参数, 配置: {'name': '全技术组合', 'feature_dims': [24, 48, 96], 'activation': 'gelu', 'alpha_init': 0.52, 'dropout_rate': 0.2, 'use_residual': True, 'use_attention': True, 'use_layernorm': True, 'use_multi_prior': True, 'augment_strategy': 'moderate', 'augment_factor': 4}
🔧 高级模型: 19,026参数, 配置: {'name': '全技术组合', 'feature_dims': [24, 48, 96], 'activation': 'gelu', 'alpha_init': 0.52, 'dropout_rate': 0.2, 'use_residual': True, 'use_attention': True, 'use_layernorm': True, 'use_multi_prior': True, 'augment_strategy': 'moderate', 'augment_factor': 4}
🔧 高级模型: 19,026参数, 配置: {'name': '全技术组合', 'feature_dims': [24, 48, 96], 'activation': 'gelu', 'alpha_init': 0.52, 'dropout_rate': 0.2, 'use_residual': True, 'use_attention': True, 'use_layernorm': True, 'use_multi_prior': True, 'augment_strategy': 'moderate', 'augment_factor': 4}
   结果: 6.753±0.295mm (最佳: 6.470mm)

进度: 15/16 (已运行0.1小时)

🔍 测试配置: 中等增强
🔧 高级模型: 22,533参数, 配置: {'name': '中等增强', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'augment_strategy': 'moderate', 'augment_factor': 3}
🔧 高级模型: 22,533参数, 配置: {'name': '中等增强', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'augment_strategy': 'moderate', 'augment_factor': 3}
🔧 高级模型: 22,533参数, 配置: {'name': '中等增强', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.3, 'augment_strategy': 'moderate', 'augment_factor': 3}
   结果: 6.725±0.369mm (最佳: 6.337mm)

进度: 16/16 (已运行0.1小时)

🔍 测试配置: 激进增强
🔧 高级模型: 22,533参数, 配置: {'name': '激进增强', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.4, 'augment_strategy': 'aggressive', 'augment_factor': 4}
🔧 高级模型: 22,533参数, 配置: {'name': '激进增强', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.4, 'augment_strategy': 'aggressive', 'augment_factor': 4}
🔧 高级模型: 22,533参数, 配置: {'name': '激进增强', 'feature_dims': [32, 64, 128], 'activation': 'relu', 'alpha_init': 0.55, 'dropout_rate': 0.4, 'augment_strategy': 'aggressive', 'augment_factor': 4}
   结果: 6.699±0.530mm (最佳: 6.160mm)

📊 **最终结果分析**
============================================================
✅ 有效配置: 16/16
🏆 最佳性能: 6.051mm
📈 基线对比: 5.857mm vs 5.857mm
💡 未突破基线，但获得了宝贵经验

🏆 **前10名配置**:
    1. Swish激活              6.051mm (avg: 6.384mm)
    2. 紧凑维度                 6.147mm (avg: 6.554mm)
    3. 激进增强                 6.160mm (avg: 6.699mm)
    4. GELU激活               6.226mm (avg: 6.569mm)
    5. 原始最佳                 6.274mm (avg: 6.572mm)
    6. 黄金比例维度               6.280mm (avg: 6.496mm)
    7. 中等增强                 6.337mm (avg: 6.725mm)
    8. 多重统计先验               6.346mm (avg: 6.555mm)
    9. 残差连接                 6.398mm (avg: 6.694mm)
   10. α=0.52               6.449mm (avg: 6.676mm)

⏰ 总运行时间: 0.2小时
📁 结果保存在: overnight_optimization_20250717_235307/

🎉 **过夜优化完成!**
😊 **您醒来后可以查看结果**
