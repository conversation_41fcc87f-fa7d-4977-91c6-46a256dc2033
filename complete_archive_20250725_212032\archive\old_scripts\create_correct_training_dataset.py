#!/usr/bin/env python3
"""
Create Correct Training Dataset for Medical Keypoint Detection

Based on the verified correct data loader, create proper training datasets.
Support both individual region training (F1/F2/F3) and joint training.
"""

import numpy as np
import pandas as pd
from pathlib import Path
import h5py
import json
from sklearn.model_selection import train_test_split
from correct_data_loader import load_sample_data, validate_alignment
import random

def sample_point_cloud(vertices, target_points=8192):
    """Sample point cloud to target size"""
    if len(vertices) <= target_points:
        # If not enough points, duplicate some randomly
        indices = np.random.choice(len(vertices), target_points, replace=True)
        return vertices[indices]
    else:
        # Random sampling
        indices = np.random.choice(len(vertices), target_points, replace=False)
        return vertices[indices]

def normalize_point_cloud(point_cloud):
    """Normalize point cloud to unit sphere while preserving relative scale"""
    # Center the point cloud
    centroid = np.mean(point_cloud, axis=0)
    centered = point_cloud - centroid
    
    # Scale to unit sphere
    max_dist = np.max(np.linalg.norm(centered, axis=1))
    if max_dist > 0:
        normalized = centered / max_dist
    else:
        normalized = centered
    
    return normalized, centroid, max_dist

def create_region_dataset(region_name, samples_data, target_points=8192, normalize=True):
    """Create dataset for a specific region (F1, F2, or F3)"""
    
    print(f"\n🏗️ **创建{region_name}区域数据集**")
    
    valid_samples = []
    
    for sample_data in samples_data:
        if (region_name in sample_data['stl_data'] and 
            len(sample_data['regions'][region_name]['keypoints']) > 0):
            
            stl_vertices = sample_data['stl_data'][region_name]
            keypoints = sample_data['regions'][region_name]['keypoints']
            
            # Sample point cloud
            sampled_pc = sample_point_cloud(stl_vertices, target_points)
            
            if normalize:
                # Normalize point cloud
                normalized_pc, centroid, scale = normalize_point_cloud(sampled_pc)
                # Apply same transformation to keypoints
                normalized_kps = (keypoints - centroid) / scale
            else:
                normalized_pc = sampled_pc
                normalized_kps = keypoints
                centroid = np.zeros(3)
                scale = 1.0
            
            valid_samples.append({
                'sample_id': sample_data['sample_id'],
                'point_cloud': normalized_pc,
                'keypoints': normalized_kps,
                'original_keypoints': keypoints,
                'centroid': centroid,
                'scale': scale,
                'labels': sample_data['regions'][region_name]['labels']
            })
    
    print(f"   ✅ 成功处理 {len(valid_samples)} 个{region_name}样本")
    return valid_samples

def create_joint_dataset(samples_data, target_points=8192, normalize=True):
    """Create joint dataset combining F1+F2+F3"""
    
    print(f"\n🏗️ **创建联合F1+F2+F3数据集**")
    
    valid_samples = []
    
    for sample_data in samples_data:
        # Check if all regions are available
        if (all(region in sample_data['stl_data'] for region in ['F1', 'F2', 'F3']) and
            all(len(sample_data['regions'][region]['keypoints']) > 0 for region in ['F1', 'F2', 'F3'])):
            
            # Combine all STL vertices
            all_vertices = np.vstack([
                sample_data['stl_data']['F1'],
                sample_data['stl_data']['F2'],
                sample_data['stl_data']['F3']
            ])
            
            # Combine all keypoints (should be 57 total: 19*3)
            all_keypoints = np.vstack([
                sample_data['regions']['F1']['keypoints'],
                sample_data['regions']['F2']['keypoints'],
                sample_data['regions']['F3']['keypoints']
            ])
            
            # Sample combined point cloud
            sampled_pc = sample_point_cloud(all_vertices, target_points)
            
            if normalize:
                # Normalize point cloud
                normalized_pc, centroid, scale = normalize_point_cloud(sampled_pc)
                # Apply same transformation to keypoints
                normalized_kps = (all_keypoints - centroid) / scale
            else:
                normalized_pc = sampled_pc
                normalized_kps = all_keypoints
                centroid = np.zeros(3)
                scale = 1.0
            
            # Combine all labels
            all_labels = (sample_data['regions']['F1']['labels'] + 
                         sample_data['regions']['F2']['labels'] + 
                         sample_data['regions']['F3']['labels'])
            
            valid_samples.append({
                'sample_id': sample_data['sample_id'],
                'point_cloud': normalized_pc,
                'keypoints': normalized_kps,
                'original_keypoints': all_keypoints,
                'centroid': centroid,
                'scale': scale,
                'labels': all_labels
            })
    
    print(f"   ✅ 成功处理 {len(valid_samples)} 个联合样本")
    return valid_samples

def save_dataset_hdf5(dataset_samples, output_path, dataset_name):
    """Save dataset to HDF5 format"""
    
    print(f"\n💾 **保存数据集到 {output_path}**")
    
    with h5py.File(output_path, 'w') as f:
        # Create groups
        train_group = f.create_group('train')
        val_group = f.create_group('val')
        test_group = f.create_group('test')
        
        # Split data
        train_samples, temp_samples = train_test_split(
            dataset_samples, test_size=0.3, random_state=42
        )
        val_samples, test_samples = train_test_split(
            temp_samples, test_size=0.5, random_state=42
        )
        
        print(f"   📊 数据分割: 训练{len(train_samples)} | 验证{len(val_samples)} | 测试{len(test_samples)}")
        
        # Save each split
        for split_name, split_samples, group in [
            ('train', train_samples, train_group),
            ('val', val_samples, val_group),
            ('test', test_samples, test_group)
        ]:
            if split_samples:
                # Stack all data
                point_clouds = np.stack([s['point_cloud'] for s in split_samples])
                keypoints = np.stack([s['keypoints'] for s in split_samples])
                original_keypoints = np.stack([s['original_keypoints'] for s in split_samples])
                centroids = np.stack([s['centroid'] for s in split_samples])
                scales = np.array([s['scale'] for s in split_samples])
                sample_ids = [s['sample_id'] for s in split_samples]
                
                # Save to HDF5
                group.create_dataset('point_clouds', data=point_clouds, compression='gzip')
                group.create_dataset('keypoints', data=keypoints, compression='gzip')
                group.create_dataset('original_keypoints', data=original_keypoints, compression='gzip')
                group.create_dataset('centroids', data=centroids, compression='gzip')
                group.create_dataset('scales', data=scales, compression='gzip')
                
                # Save sample IDs as strings
                dt = h5py.string_dtype(encoding='utf-8')
                group.create_dataset('sample_ids', data=sample_ids, dtype=dt)
                
                print(f"      {split_name}: {point_clouds.shape[0]} 样本")
        
        # Save metadata
        metadata = {
            'dataset_name': dataset_name,
            'total_samples': len(dataset_samples),
            'point_cloud_size': dataset_samples[0]['point_cloud'].shape[0] if dataset_samples else 0,
            'keypoint_count': dataset_samples[0]['keypoints'].shape[0] if dataset_samples else 0,
            'creation_date': str(pd.Timestamp.now()),
            'train_samples': len(train_samples),
            'val_samples': len(val_samples),
            'test_samples': len(test_samples)
        }
        
        f.attrs.update(metadata)
    
    print(f"   ✅ 数据集已保存: {output_path}")
    return output_path

def create_all_datasets():
    """Create all training datasets"""
    
    print("🚀 **创建正确的医疗关键点训练数据集**")
    print("=" * 80)
    
    # Load all available samples
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    annotations_dir = data_dir / "annotations"
    
    xyz_files = list(annotations_dir.glob("*-Table-XYZ.CSV"))
    excluded_samples = {'600025', '600026', '600027'}  # Known problematic samples
    
    print(f"📂 找到 {len(xyz_files)} 个标注文件")
    
    # Load all samples
    all_samples_data = []
    failed_samples = []
    
    for csv_file in xyz_files:
        sample_id = csv_file.stem.split('-')[0]
        
        if sample_id in excluded_samples:
            continue
        
        sample_data = load_sample_data(sample_id)
        
        if sample_data is not None:
            all_samples_data.append(sample_data)
        else:
            failed_samples.append(sample_id)
    
    print(f"✅ 成功加载 {len(all_samples_data)} 个样本")
    if failed_samples:
        print(f"❌ 失败样本: {failed_samples}")
    
    # Create datasets
    datasets_created = []
    
    # 1. Individual region datasets
    for region in ['F1', 'F2', 'F3']:
        region_samples = create_region_dataset(region, all_samples_data)
        
        if len(region_samples) >= 10:  # Minimum samples for meaningful dataset
            output_path = f"medical_keypoint_{region.lower()}_dataset.h5"
            save_dataset_hdf5(region_samples, output_path, f"Medical_{region}_Keypoints")
            datasets_created.append(output_path)
        else:
            print(f"   ⚠️ {region}样本数量不足({len(region_samples)})，跳过")
    
    # 2. Joint dataset
    joint_samples = create_joint_dataset(all_samples_data)
    
    if len(joint_samples) >= 10:
        output_path = "medical_keypoint_joint_dataset.h5"
        save_dataset_hdf5(joint_samples, output_path, "Medical_Joint_F1F2F3_Keypoints")
        datasets_created.append(output_path)
    else:
        print(f"   ⚠️ 联合样本数量不足({len(joint_samples)})，跳过")
    
    # Summary
    print(f"\n🎉 **数据集创建完成**")
    print(f"   创建的数据集: {len(datasets_created)} 个")
    for dataset_path in datasets_created:
        print(f"      📁 {dataset_path}")
    
    return datasets_created

if __name__ == "__main__":
    # Create all datasets
    created_datasets = create_all_datasets()
