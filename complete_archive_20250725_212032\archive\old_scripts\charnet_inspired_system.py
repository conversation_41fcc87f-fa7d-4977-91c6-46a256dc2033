#!/usr/bin/env python3
"""
CHaRNet启发的热力图回归系统
基于CHaRNet论文的思想，为19个骨盆关键点实现热力图回归训练
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from matplotlib.colors import LinearSegmentedColormap

# 19个关键点名称 (F1, F2, F3各19个)
KEYPOINT_NAMES_19 = {
    # F1 region (0-18)
    0: "F1-1", 1: "F1-2", 2: "F1-3", 3: "F1-4", 4: "F1-5", 5: "F1-6",
    6: "F1-7", 7: "F1-8", 8: "F1-9", 9: "F1-10", 10: "F1-11", 11: "F1-12",
    12: "F1-13", 13: "F1-14", 14: "F1-15", 15: "F1-16", 16: "F1-17", 17: "F1-18", 18: "F1-19",
    # F2 region (19-37) 
    19: "F2-1", 20: "F2-2", 21: "F2-3", 22: "F2-4", 23: "F2-5", 24: "F2-6",
    25: "F2-7", 26: "F2-8", 27: "F2-9", 28: "F2-10", 29: "F2-11", 30: "F2-12",
    31: "F2-13", 32: "F2-14", 33: "F2-15", 34: "F2-16", 35: "F2-17", 36: "F2-18", 37: "F2-19",
    # F3 region (38-56)
    38: "F3-1", 39: "F3-2", 40: "F3-3", 41: "F3-4", 42: "F3-5", 43: "F3-6",
    44: "F3-7", 45: "F3-8", 46: "F3-9", 47: "F3-10", 48: "F3-11", 49: "F3-12",
    50: "F3-13", 51: "F3-14", 52: "F3-15", 53: "F3-16", 54: "F3-17", 55: "F3-18", 56: "F3-19"
}

class PointCloudEncoder(nn.Module):
    """点云编码器 - 基于PointNet++思想"""
    
    def __init__(self, input_dim=3, feature_dim=1024):
        super().__init__()
        
        # 多层感知机
        self.conv1 = nn.Conv1d(input_dim, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, feature_dim, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(feature_dim)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        # x: [B, 3, N]
        x = torch.relu(self.bn1(self.conv1(x)))
        x = torch.relu(self.bn2(self.conv2(x)))
        x = torch.relu(self.bn3(self.conv3(x)))
        x = torch.relu(self.bn4(self.conv4(x)))
        x = self.bn5(self.conv5(x))
        
        # 全局特征
        global_feat = torch.max(x, 2)[0]  # [B, feature_dim]
        
        # 点级特征
        point_feat = x  # [B, feature_dim, N]
        
        return global_feat, point_feat

class HeatmapRegressionHead(nn.Module):
    """热力图回归头"""
    
    def __init__(self, feature_dim=1024, num_keypoints=57):
        super().__init__()
        
        # 解码器
        self.decoder = nn.Sequential(
            nn.Conv1d(feature_dim, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Conv1d(512, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Conv1d(256, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            
            nn.Conv1d(128, num_keypoints, 1)  # 输出每个关键点的热力图
        )
        
    def forward(self, point_feat):
        # point_feat: [B, feature_dim, N]
        heatmaps = self.decoder(point_feat)  # [B, num_keypoints, N]
        return heatmaps

class RegionPresenceClassifier(nn.Module):
    """区域存在性分类器 - 类似CHaRNet的牙齿存在性分类"""
    
    def __init__(self, feature_dim=1024, num_regions=3):
        super().__init__()
        
        self.classifier = nn.Sequential(
            nn.Linear(feature_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.5),

            nn.Linear(256, 256),
            nn.ReLU(),
            nn.Dropout(0.5),

            nn.Linear(256, num_regions),  # F1, F2, F3
            nn.Sigmoid()
        )
        
    def forward(self, global_feat):
        # global_feat: [B, feature_dim]
        region_probs = self.classifier(global_feat)  # [B, 3]
        return region_probs

class ConditionedHeatmapRegression(nn.Module):
    """条件热力图回归模块 - CHaRNet的核心思想"""
    
    def __init__(self, num_keypoints=57):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 关键点到区域的映射
        self.keypoint_to_region = self._create_keypoint_region_mapping()
        
    def _create_keypoint_region_mapping(self):
        """创建关键点到区域的映射"""
        mapping = torch.zeros(self.num_keypoints, dtype=torch.long)
        
        # F1: 0-18, F2: 19-37, F3: 38-56
        mapping[0:19] = 0   # F1
        mapping[19:38] = 1  # F2
        mapping[38:57] = 2  # F3
        
        return mapping
    
    def forward(self, initial_heatmaps, region_probs, null_point_idx=-1):
        """
        条件热力图回归
        
        Args:
            initial_heatmaps: [B, num_keypoints, N] - 初始热力图
            region_probs: [B, 3] - 区域存在概率 [F1, F2, F3]
            null_point_idx: int - 空点索引（用于缺失区域）
        """
        B, K, N = initial_heatmaps.shape
        device = initial_heatmaps.device
        
        # 将映射移到正确的设备
        keypoint_to_region = self.keypoint_to_region.to(device)
        
        conditioned_heatmaps = initial_heatmaps.clone()
        
        for b in range(B):
            for k in range(K):
                region_idx = keypoint_to_region[k].item()
                region_prob = region_probs[b, region_idx]
                
                # 原始点的权重 = 区域存在概率
                # 空点的权重 = 1 - 区域存在概率
                if null_point_idx >= 0 and null_point_idx < N:
                    # 调整原始点的权重
                    conditioned_heatmaps[b, k, :null_point_idx] *= region_prob
                    conditioned_heatmaps[b, k, null_point_idx+1:] *= region_prob
                    
                    # 调整空点的权重
                    conditioned_heatmaps[b, k, null_point_idx] *= (1 - region_prob)
                else:
                    # 如果没有空点，只调整所有点的权重
                    conditioned_heatmaps[b, k, :] *= region_prob
        
        return conditioned_heatmaps

class CHaRNetInspired(nn.Module):
    """CHaRNet启发的网络架构"""
    
    def __init__(self, input_dim=3, feature_dim=1024, num_keypoints=57):
        super().__init__()
        
        self.encoder = PointCloudEncoder(input_dim, feature_dim)
        self.heatmap_head = HeatmapRegressionHead(feature_dim, num_keypoints)
        self.region_classifier = RegionPresenceClassifier(feature_dim, 3)
        self.char_module = ConditionedHeatmapRegression(num_keypoints)
        
    def forward(self, x, null_point_idx=-1):
        """
        前向传播
        
        Args:
            x: [B, 3, N] - 输入点云
            null_point_idx: int - 空点索引
        """
        # 编码
        global_feat, point_feat = self.encoder(x)
        
        # 初始热力图回归
        initial_heatmaps = self.heatmap_head(point_feat)
        
        # 区域存在性分类
        region_probs = self.region_classifier(global_feat)
        
        # 条件热力图回归
        final_heatmaps = self.char_module(initial_heatmaps, region_probs, null_point_idx)
        
        return {
            'heatmaps': final_heatmaps,
            'region_probs': region_probs,
            'initial_heatmaps': initial_heatmaps
        }

class CHaRLoss(nn.Module):
    """CHaR损失函数"""
    
    def __init__(self, lambda_heatmap=1.0, lambda_region=0.1):
        super().__init__()
        self.lambda_heatmap = lambda_heatmap
        self.lambda_region = lambda_region
        
        self.mse_loss = nn.MSELoss()
        self.bce_loss = nn.BCELoss()
    
    def forward(self, predictions, targets):
        """
        计算损失
        
        Args:
            predictions: 模型预测结果字典
            targets: 目标字典，包含 'heatmaps' 和 'region_labels'
        """
        # 热力图回归损失
        heatmap_loss = self.mse_loss(predictions['heatmaps'], targets['heatmaps'])
        
        # 区域分类损失
        region_loss = self.bce_loss(predictions['region_probs'], targets['region_labels'])
        
        # 总损失
        total_loss = (self.lambda_heatmap * heatmap_loss + 
                     self.lambda_region * region_loss)
        
        return {
            'total_loss': total_loss,
            'heatmap_loss': heatmap_loss,
            'region_loss': region_loss
        }

def create_heatmap_targets(keypoints, point_cloud, sigma=8.0):
    """创建热力图目标"""
    num_points = len(point_cloud)
    num_keypoints = len(keypoints)
    heatmaps = np.zeros((num_keypoints, num_points))
    
    for kp_idx, keypoint in enumerate(keypoints):
        # 计算每个点到关键点的距离
        distances = np.linalg.norm(point_cloud - keypoint, axis=1)
        # 生成高斯分布
        heatmaps[kp_idx] = np.exp(-distances**2 / (2 * sigma**2))
    
    return heatmaps

def create_region_labels(keypoints, num_regions=3):
    """创建区域标签 - 简化版本，假设所有区域都存在"""
    # 在实际应用中，这里应该根据实际的区域存在情况来设置
    return np.ones(num_regions, dtype=np.float32)

def extract_keypoints_from_heatmaps_char(heatmaps, point_cloud):
    """从热力图中提取关键点位置"""
    num_keypoints = heatmaps.shape[0]
    keypoints = []
    confidences = []
    
    for kp_idx in range(num_keypoints):
        heatmap = heatmaps[kp_idx]
        
        # 找到最大值位置
        max_idx = np.argmax(heatmap)
        max_confidence = heatmap[max_idx]
        
        # 提取对应的3D坐标
        keypoint_3d = point_cloud[max_idx]
        
        keypoints.append(keypoint_3d)
        confidences.append(max_confidence)
    
    return np.array(keypoints), np.array(confidences)

def visualize_char_results(point_cloud, true_keypoints, pred_keypoints, 
                          heatmaps, region_probs, sample_id):
    """可视化CHaR结果"""
    
    fig = plt.figure(figsize=(20, 12))
    
    # 1. 3D点云和关键点
    ax1 = fig.add_subplot(2, 3, 1, projection='3d')
    
    # 采样点云
    if len(point_cloud) > 3000:
        indices = np.random.choice(len(point_cloud), 3000, replace=False)
        display_pc = point_cloud[indices]
    else:
        display_pc = point_cloud
    
    ax1.scatter(display_pc[:, 0], display_pc[:, 1], display_pc[:, 2],
               c='lightgray', s=0.5, alpha=0.3)
    
    # 真实关键点
    ax1.scatter(true_keypoints[:, 0], true_keypoints[:, 1], true_keypoints[:, 2],
               c='green', s=50, marker='o', label='True', alpha=0.8)
    
    # 预测关键点
    ax1.scatter(pred_keypoints[:, 0], pred_keypoints[:, 1], pred_keypoints[:, 2],
               c='red', s=50, marker='x', label='Predicted', alpha=0.8)
    
    ax1.set_title(f'CHaR Results - Sample {sample_id}')
    ax1.legend()
    
    # 2. 区域概率
    ax2 = fig.add_subplot(2, 3, 2)
    regions = ['F1', 'F2', 'F3']
    bars = ax2.bar(regions, region_probs, color=['red', 'green', 'blue'], alpha=0.7)
    ax2.set_ylabel('Presence Probability')
    ax2.set_title('Region Presence Classification')
    ax2.set_ylim([0, 1])
    
    # 添加数值标签
    for bar, prob in zip(bars, region_probs):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{prob:.3f}', ha='center', va='bottom')
    
    # 3-5. 每个区域的热力图示例
    regions_info = [
        ('F1', 0, 19, 'Reds'),
        ('F2', 19, 38, 'Greens'), 
        ('F3', 38, 57, 'Blues')
    ]
    
    for i, (region_name, start_idx, end_idx, cmap) in enumerate(regions_info):
        ax = fig.add_subplot(2, 3, 3+i, projection='3d')
        
        # 选择该区域的一个关键点作为示例
        kp_idx = start_idx + 9  # 选择中间的关键点
        if kp_idx < len(heatmaps):
            heatmap = heatmaps[kp_idx]
            
            # 显示热力图
            scatter = ax.scatter(display_pc[:, 0], display_pc[:, 1], display_pc[:, 2],
                               c=heatmap[:len(display_pc)], cmap=cmap, 
                               s=2, alpha=0.7, vmin=0, vmax=1)
            
            # 标记关键点
            if kp_idx < len(true_keypoints):
                ax.scatter(true_keypoints[kp_idx, 0], true_keypoints[kp_idx, 1], 
                          true_keypoints[kp_idx, 2], c='black', s=100, marker='*')
            
            if kp_idx < len(pred_keypoints):
                ax.scatter(pred_keypoints[kp_idx, 0], pred_keypoints[kp_idx, 1], 
                          pred_keypoints[kp_idx, 2], c='white', s=80, marker='x')
            
            ax.set_title(f'{region_name} Heatmap\nProb: {region_probs[i]:.3f}')
    
    plt.suptitle(f'CHaRNet-Inspired Results - Sample {sample_id}', 
                fontsize=16, fontweight='bold')
    plt.tight_layout(rect=[0, 0, 1, 0.93])
    
    filename = f'char_results_{sample_id}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 CHaR results saved: {filename}")
    plt.close()

def main():
    """主函数 - 演示CHaRNet启发的系统"""
    print("🚀 CHaRNet-Inspired Heatmap Regression System")
    print("Based on CHaRNet paper for robust landmark localization")
    print("=" * 60)
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = CHaRNetInspired(input_dim=3, feature_dim=1024, num_keypoints=57).to(device)
    
    print(f"✅ Model created with {sum(p.numel() for p in model.parameters())} parameters")
    print(f"🔧 Using device: {device}")
    
    # 模拟数据测试
    batch_size = 2
    num_points = 8192
    
    # 创建模拟输入
    x = torch.randn(batch_size, 3, num_points).to(device)
    
    # 前向传播测试
    with torch.no_grad():
        outputs = model(x, null_point_idx=-1)
    
    print(f"\n📊 Model Output Shapes:")
    print(f"   Heatmaps: {outputs['heatmaps'].shape}")
    print(f"   Region Probs: {outputs['region_probs'].shape}")
    print(f"   Initial Heatmaps: {outputs['initial_heatmaps'].shape}")
    
    # 显示区域概率
    region_probs = outputs['region_probs'].cpu().numpy()
    print(f"\n🎯 Region Presence Probabilities:")
    for i, (prob_batch) in enumerate(region_probs):
        print(f"   Batch {i}: F1={prob_batch[0]:.3f}, F2={prob_batch[1]:.3f}, F3={prob_batch[2]:.3f}")
    
    print(f"\n💡 Key Features:")
    print("✅ End-to-end heatmap regression")
    print("✅ Region presence classification (F1/F2/F3)")
    print("✅ Conditioned heatmap regression (CHaR module)")
    print("✅ Support for 57 keypoints (19 per region)")
    print("✅ Robust to missing anatomical regions")
    print("✅ Inspired by CHaRNet dental landmark detection")
    
    print(f"\n🎯 Next Steps:")
    print("1. Load your 19-keypoint dataset")
    print("2. Implement proper training loop with CHaRLoss")
    print("3. Add data augmentation for robustness")
    print("4. Fine-tune hyperparameters")
    print("5. Compare with your current 12-keypoint model")

if __name__ == "__main__":
    main()
