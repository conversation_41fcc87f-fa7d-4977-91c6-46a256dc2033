#!/usr/bin/env python3
"""
修复19关键点数据预处理
应用与12关键点相同的预处理流程
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def load_data():
    """加载原始数据"""
    
    # 12点预处理数据
    data_12kp = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    pc_12kp = data_12kp['point_clouds']
    kp_12kp = data_12kp['keypoints']
    ids_12kp = data_12kp['sample_ids']
    
    # 19点原始数据
    data_19kp = np.load('f3_19kp_real.npz', allow_pickle=True)
    pc_19kp = data_19kp['point_clouds']
    kp_19kp = data_19kp['keypoints']
    ids_19kp = data_19kp['sample_ids']
    
    return pc_12kp, kp_12kp, ids_12kp, pc_19kp, kp_19kp, ids_19kp

def analyze_12kp_preprocessing(pc_12kp, kp_12kp):
    """分析12点数据的预处理方式"""
    
    print("🔍 分析12点数据预处理方式...")
    
    # 分析点云特征
    sample_pc = pc_12kp[0]
    sample_kp = kp_12kp[0]
    
    print(f"12点数据特征:")
    print(f"   点云大小: {len(sample_pc)}")
    print(f"   点云中心: ({np.mean(sample_pc, axis=0)})")
    print(f"   点云范围: X({np.min(sample_pc[:, 0]):.1f}, {np.max(sample_pc[:, 0]):.1f})")
    print(f"            Y({np.min(sample_pc[:, 1]):.1f}, {np.max(sample_pc[:, 1]):.1f})")
    print(f"            Z({np.min(sample_pc[:, 2]):.1f}, {np.max(sample_pc[:, 2]):.1f})")
    
    print(f"   关键点中心: ({np.mean(sample_kp, axis=0)})")
    print(f"   关键点范围: X({np.min(sample_kp[:, 0]):.1f}, {np.max(sample_kp[:, 0]):.1f})")
    print(f"              Y({np.min(sample_kp[:, 1]):.1f}, {np.max(sample_kp[:, 1]):.1f})")
    print(f"              Z({np.min(sample_kp[:, 2]):.1f}, {np.max(sample_kp[:, 2]):.1f})")
    
    # 检查是否中心化
    pc_center = np.mean(sample_pc, axis=0)
    kp_center = np.mean(sample_kp, axis=0)
    
    print(f"\n预处理分析:")
    print(f"   点云是否中心化: {np.allclose(pc_center, [0, 0, 0], atol=5)}")
    print(f"   关键点是否中心化: {np.allclose(kp_center, [0, 0, 0], atol=5)}")
    
    # 检查缩放
    pc_scale = np.max(sample_pc) - np.min(sample_pc)
    print(f"   点云尺度: {pc_scale:.1f}")
    
    return {
        'point_cloud_size': len(sample_pc),
        'pc_center': pc_center,
        'kp_center': kp_center,
        'pc_scale': pc_scale,
        'pc_range': {
            'x': (np.min(sample_pc[:, 0]), np.max(sample_pc[:, 0])),
            'y': (np.min(sample_pc[:, 1]), np.max(sample_pc[:, 1])),
            'z': (np.min(sample_pc[:, 2]), np.max(sample_pc[:, 2]))
        }
    }

def apply_preprocessing_to_19kp(pc_19kp, kp_19kp, preprocessing_info):
    """将12点的预处理应用到19点数据"""
    
    print(f"\n🔧 应用预处理到19点数据...")
    
    processed_pc = []
    processed_kp = []
    
    target_size = preprocessing_info['point_cloud_size']
    
    for i, (pc, kp) in enumerate(zip(pc_19kp, kp_19kp)):
        print(f"   处理样本 {i+1}/{len(pc_19kp)}")
        
        # 1. 中心化 - 以关键点为中心
        kp_center = np.mean(kp, axis=0)
        pc_centered = pc - kp_center
        kp_centered = kp - kp_center
        
        # 2. 缩放 - 匹配12点数据的尺度
        current_scale = np.max(pc_centered) - np.min(pc_centered)
        target_scale = preprocessing_info['pc_scale']
        scale_factor = target_scale / current_scale
        
        pc_scaled = pc_centered * scale_factor
        kp_scaled = kp_centered * scale_factor
        
        # 3. 采样到目标点数
        if len(pc_scaled) > target_size:
            # 下采样
            indices = np.random.choice(len(pc_scaled), target_size, replace=False)
            pc_sampled = pc_scaled[indices]
        elif len(pc_scaled) < target_size:
            # 上采样
            indices = np.random.choice(len(pc_scaled), target_size, replace=True)
            pc_sampled = pc_scaled[indices]
        else:
            pc_sampled = pc_scaled
        
        processed_pc.append(pc_sampled)
        processed_kp.append(kp_scaled)
        
        # 验证处理结果
        if i == 0:
            print(f"     原始点云范围: X({np.min(pc[:, 0]):.1f}, {np.max(pc[:, 0]):.1f})")
            print(f"                  Y({np.min(pc[:, 1]):.1f}, {np.max(pc[:, 1]):.1f})")
            print(f"                  Z({np.min(pc[:, 2]):.1f}, {np.max(pc[:, 2]):.1f})")
            print(f"     处理后范围:   X({np.min(pc_sampled[:, 0]):.1f}, {np.max(pc_sampled[:, 0]):.1f})")
            print(f"                  Y({np.min(pc_sampled[:, 1]):.1f}, {np.max(pc_sampled[:, 1]):.1f})")
            print(f"                  Z({np.min(pc_sampled[:, 2]):.1f}, {np.max(pc_sampled[:, 2]):.1f})")
    
    return np.array(processed_pc), np.array(processed_kp)

def extract_12kp_from_19kp(kp_19kp):
    """从19个关键点中提取对应的12个关键点"""
    
    print(f"\n🎯 从19点中提取对应的12点...")
    
    # 根据F3区域的解剖结构，选择最重要的12个关键点
    # 这需要根据实际的解剖意义来选择
    important_indices = [
        0,   # F3-1
        1,   # F3-2 (前方边界)
        2,   # F3-3
        3,   # F3-4
        6,   # F3-7
        7,   # F3-8
        11,  # F3-12 (后方边界)
        12,  # F3-13 (Z最高点)
        13,  # F3-14 (左边界)
        17,  # F3-18 (Z最低点)
        18,  # F3-19 (右边界)
        9    # F3-10
    ]
    
    kp_12_from_19 = kp_19kp[:, important_indices, :]
    
    print(f"   选择的关键点索引: {important_indices}")
    print(f"   提取的12点形状: {kp_12_from_19.shape}")
    
    return kp_12_from_19

def create_preprocessing_comparison(pc_12kp, kp_12kp, pc_19kp_orig, kp_19kp_orig, 
                                  pc_19kp_proc, kp_19kp_proc, sample_idx=0):
    """创建预处理对比可视化"""
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 选择样本
    pc_12 = pc_12kp[sample_idx]
    kp_12 = kp_12kp[sample_idx]
    pc_19_orig = pc_19kp_orig[sample_idx]
    kp_19_orig = kp_19kp_orig[sample_idx]
    pc_19_proc = pc_19kp_proc[sample_idx]
    kp_19_proc = kp_19kp_proc[sample_idx]
    
    # 1. 12点原始数据
    ax1 = axes[0, 0]
    
    # 采样显示
    if len(pc_12) > 3000:
        idx = np.random.choice(len(pc_12), 3000, replace=False)
        display_pc = pc_12[idx]
    else:
        display_pc = pc_12
    
    ax1.scatter(display_pc[:, 0], display_pc[:, 2], c='blue', s=1, alpha=0.5)
    ax1.scatter(kp_12[:, 0], kp_12[:, 2], c='red', s=50, marker='o')
    ax1.set_title('12-Point Data (Preprocessed)')
    ax1.set_xlabel('X (mm)')
    ax1.set_ylabel('Z (mm)')
    ax1.grid(True, alpha=0.3)
    ax1.axis('equal')
    
    # 2. 19点原始数据
    ax2 = axes[0, 1]
    
    if len(pc_19_orig) > 3000:
        idx = np.random.choice(len(pc_19_orig), 3000, replace=False)
        display_pc = pc_19_orig[idx]
    else:
        display_pc = pc_19_orig
    
    ax2.scatter(display_pc[:, 0], display_pc[:, 2], c='green', s=1, alpha=0.5)
    ax2.scatter(kp_19_orig[:, 0], kp_19_orig[:, 2], c='red', s=30, marker='^')
    ax2.set_title('19-Point Data (Original)')
    ax2.set_xlabel('X (mm)')
    ax2.set_ylabel('Z (mm)')
    ax2.grid(True, alpha=0.3)
    ax2.axis('equal')
    
    # 3. 19点处理后数据
    ax3 = axes[0, 2]
    
    if len(pc_19_proc) > 3000:
        idx = np.random.choice(len(pc_19_proc), 3000, replace=False)
        display_pc = pc_19_proc[idx]
    else:
        display_pc = pc_19_proc
    
    ax3.scatter(display_pc[:, 0], display_pc[:, 2], c='purple', s=1, alpha=0.5)
    ax3.scatter(kp_19_proc[:, 0], kp_19_proc[:, 2], c='red', s=30, marker='^')
    ax3.set_title('19-Point Data (Preprocessed)')
    ax3.set_xlabel('X (mm)')
    ax3.set_ylabel('Z (mm)')
    ax3.grid(True, alpha=0.3)
    ax3.axis('equal')
    
    # 4. 数据范围对比
    ax4 = axes[1, 0]
    
    datasets = ['12-Point', '19-Point\n(Original)', '19-Point\n(Processed)']
    x_ranges = [
        np.max(pc_12[:, 0]) - np.min(pc_12[:, 0]),
        np.max(pc_19_orig[:, 0]) - np.min(pc_19_orig[:, 0]),
        np.max(pc_19_proc[:, 0]) - np.min(pc_19_proc[:, 0])
    ]
    z_ranges = [
        np.max(pc_12[:, 2]) - np.min(pc_12[:, 2]),
        np.max(pc_19_orig[:, 2]) - np.min(pc_19_orig[:, 2]),
        np.max(pc_19_proc[:, 2]) - np.min(pc_19_proc[:, 2])
    ]
    
    x = np.arange(len(datasets))
    width = 0.35
    
    ax4.bar(x - width/2, x_ranges, width, label='X Range', alpha=0.7)
    ax4.bar(x + width/2, z_ranges, width, label='Z Range', alpha=0.7)
    
    ax4.set_ylabel('Range (mm)')
    ax4.set_title('Data Range Comparison')
    ax4.set_xticks(x)
    ax4.set_xticklabels(datasets)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 5. 关键点中心对比
    ax5 = axes[1, 1]
    
    centers = [
        np.mean(kp_12, axis=0),
        np.mean(kp_19_orig, axis=0),
        np.mean(kp_19_proc, axis=0)
    ]
    
    for i, (center, label) in enumerate(zip(centers, datasets)):
        ax5.scatter(center[0], center[2], s=100, label=label)
        ax5.text(center[0], center[2] + 2, f'({center[0]:.1f}, {center[2]:.1f})', 
                ha='center', fontsize=8)
    
    ax5.set_xlabel('X (mm)')
    ax5.set_ylabel('Z (mm)')
    ax5.set_title('Keypoint Centers')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    ax5.axis('equal')
    
    # 6. 统计信息
    ax6 = axes[1, 2]
    ax6.axis('off')
    
    stats_text = f"""
Preprocessing Comparison:

12-Point Data:
• Point Cloud Size: {len(pc_12):,}
• X Range: {np.min(pc_12[:, 0]):.1f} - {np.max(pc_12[:, 0]):.1f}
• Z Range: {np.min(pc_12[:, 2]):.1f} - {np.max(pc_12[:, 2]):.1f}
• Center: ({np.mean(pc_12, axis=0)[0]:.1f}, {np.mean(pc_12, axis=0)[2]:.1f})

19-Point Original:
• Point Cloud Size: {len(pc_19_orig):,}
• X Range: {np.min(pc_19_orig[:, 0]):.1f} - {np.max(pc_19_orig[:, 0]):.1f}
• Z Range: {np.min(pc_19_orig[:, 2]):.1f} - {np.max(pc_19_orig[:, 2]):.1f}
• Center: ({np.mean(pc_19_orig, axis=0)[0]:.1f}, {np.mean(pc_19_orig, axis=0)[2]:.1f})

19-Point Processed:
• Point Cloud Size: {len(pc_19_proc):,}
• X Range: {np.min(pc_19_proc[:, 0]):.1f} - {np.max(pc_19_proc[:, 0]):.1f}
• Z Range: {np.min(pc_19_proc[:, 2]):.1f} - {np.max(pc_19_proc[:, 2]):.1f}
• Center: ({np.mean(pc_19_proc, axis=0)[0]:.1f}, {np.mean(pc_19_proc, axis=0)[2]:.1f})

Preprocessing Applied:
✓ Centering (keypoint-based)
✓ Scaling (match 12-point scale)
✓ Sampling (to 50K points)
"""
    
    ax6.text(0.05, 0.95, stats_text, transform=ax6.transAxes, fontsize=9,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    plt.suptitle('Data Preprocessing Comparison\nAligning 19-Point Data with 12-Point Processing', 
                fontsize=16, fontweight='bold')
    plt.tight_layout(rect=[0, 0, 1, 0.93])
    
    filename = 'preprocessing_comparison.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 预处理对比保存: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🔧 修复19关键点数据预处理")
    print("应用与12关键点相同的预处理流程")
    print("=" * 60)
    
    # 加载数据
    pc_12kp, kp_12kp, ids_12kp, pc_19kp, kp_19kp, ids_19kp = load_data()
    
    print(f"✅ 数据加载完成")
    print(f"   12点数据: {len(pc_12kp)} 样本")
    print(f"   19点数据: {len(pc_19kp)} 样本")
    
    # 分析12点预处理方式
    preprocessing_info = analyze_12kp_preprocessing(pc_12kp, kp_12kp)
    
    # 应用预处理到19点数据
    pc_19kp_processed, kp_19kp_processed = apply_preprocessing_to_19kp(
        pc_19kp, kp_19kp, preprocessing_info
    )
    
    # 从19点中提取12点
    kp_12_from_19 = extract_12kp_from_19kp(kp_19kp_processed)
    
    # 保存处理后的数据
    np.savez('f3_19kp_preprocessed.npz',
             point_clouds=pc_19kp_processed,
             keypoints=kp_19kp_processed,
             keypoints_12=kp_12_from_19,
             sample_ids=ids_19kp)
    
    print(f"\n💾 处理后数据已保存:")
    print(f"   文件: f3_19kp_preprocessed.npz")
    print(f"   19点关键点: {kp_19kp_processed.shape}")
    print(f"   对应12点: {kp_12_from_19.shape}")
    
    # 创建对比可视化
    create_preprocessing_comparison(
        pc_12kp, kp_12kp, pc_19kp, kp_19kp, 
        pc_19kp_processed, kp_19kp_processed
    )
    
    print(f"\n🎯 下一步:")
    print("1. 使用预处理后的19点数据重新训练模型")
    print("2. 对比预处理前后的性能差异")
    print("3. 验证与12点模型的性能是否可比")

if __name__ == "__main__":
    main()
