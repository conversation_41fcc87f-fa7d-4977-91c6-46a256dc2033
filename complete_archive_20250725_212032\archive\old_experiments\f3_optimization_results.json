{"PointNet+Attention": {"best_error": "24.224316", "history": [{"epoch": 1, "train_loss": 5326.8070068359375, "val_loss": 1319.5482177734375, "val_error": "56.81357", "acc_5mm": 0.0, "acc_10mm": 0.3289473684210526}, {"epoch": 2, "train_loss": 3075.9791044347426, "val_loss": 643.5665054321289, "val_error": "40.486443", "acc_5mm": 0.0, "acc_10mm": 1.9736842105263157}, {"epoch": 3, "train_loss": 1651.8448288861443, "val_loss": 641.9199676513672, "val_error": "38.92726", "acc_5mm": 0.0, "acc_10mm": 0.9868421052631579}, {"epoch": 4, "train_loss": 854.1185841279871, "val_loss": 502.66624450683594, "val_error": "35.931286", "acc_5mm": 0.0, "acc_10mm": 1.9736842105263157}, {"epoch": 5, "train_loss": 828.4886384851792, "val_loss": 506.0061569213867, "val_error": "35.137726", "acc_5mm": 0.0, "acc_10mm": 2.631578947368421}, {"epoch": 6, "train_loss": 713.956833783318, "val_loss": 420.1217346191406, "val_error": "31.30826", "acc_5mm": 0.9868421052631579, "acc_10mm": 7.236842105263158}, {"epoch": 7, "train_loss": 673.9405643238741, "val_loss": 410.0635986328125, "val_error": "30.550377", "acc_5mm": 0.6578947368421052, "acc_10mm": 8.881578947368421}, {"epoch": 8, "train_loss": 663.1661215389477, "val_loss": 507.7188186645508, "val_error": "35.508224", "acc_5mm": 0.3289473684210526, "acc_10mm": 2.631578947368421}, {"epoch": 9, "train_loss": 663.1362538057215, "val_loss": 450.26034927368164, "val_error": "33.1697", "acc_5mm": 0.3289473684210526, "acc_10mm": 2.9605263157894735}, {"epoch": 10, "train_loss": 721.815533806296, "val_loss": 580.9876708984375, "val_error": "38.401833", "acc_5mm": 0.3289473684210526, "acc_10mm": 1.3157894736842104}, {"epoch": 11, "train_loss": 587.1576035443475, "val_loss": 439.8842086791992, "val_error": "32.952118", "acc_5mm": 0.6578947368421052, "acc_10mm": 2.9605263157894735}, {"epoch": 12, "train_loss": 648.2809663660386, "val_loss": 540.625373840332, "val_error": "35.821808", "acc_5mm": 0.0, "acc_10mm": 3.9473684210526314}, {"epoch": 13, "train_loss": 617.7737884521484, "val_loss": 410.6395683288574, "val_error": "30.423927", "acc_5mm": 2.302631578947368, "acc_10mm": 11.513157894736842}, {"epoch": 14, "train_loss": 702.3814284380744, "val_loss": 426.54386138916016, "val_error": "31.875803", "acc_5mm": 0.9868421052631579, "acc_10mm": 6.25}, {"epoch": 15, "train_loss": 638.6842364142923, "val_loss": 456.8367385864258, "val_error": "33.755386", "acc_5mm": 0.3289473684210526, "acc_10mm": 3.618421052631579}, {"epoch": 16, "train_loss": 522.6568459903492, "val_loss": 436.12912368774414, "val_error": "32.479908", "acc_5mm": 0.3289473684210526, "acc_10mm": 5.263157894736842}, {"epoch": 17, "train_loss": 665.9569091796875, "val_loss": 329.78843688964844, "val_error": "28.401772", "acc_5mm": 0.3289473684210526, "acc_10mm": 2.9605263157894735}, {"epoch": 18, "train_loss": 429.6362538057215, "val_loss": 325.2088508605957, "val_error": "27.893988", "acc_5mm": 0.9868421052631579, "acc_10mm": 6.25}, {"epoch": 19, "train_loss": 543.2351163976333, "val_loss": 424.1907768249512, "val_error": "32.991398", "acc_5mm": 0.0, "acc_10mm": 1.9736842105263157}, {"epoch": 20, "train_loss": 567.2488125071806, "val_loss": 404.53614044189453, "val_error": "31.394974", "acc_5mm": 0.3289473684210526, "acc_10mm": 2.302631578947368}, {"epoch": 21, "train_loss": 516.3735019459444, "val_loss": 246.98704528808594, "val_error": "24.224316", "acc_5mm": 1.9736842105263157, "acc_10mm": 14.802631578947366}, {"epoch": 22, "train_loss": 459.4956279081457, "val_loss": 257.3076992034912, "val_error": "25.197079", "acc_5mm": 1.3157894736842104, "acc_10mm": 7.894736842105263}, {"epoch": 23, "train_loss": 615.0425702263327, "val_loss": 300.3792190551758, "val_error": "26.202417", "acc_5mm": 3.289473684210526, "acc_10mm": 14.473684210526317}, {"epoch": 24, "train_loss": 435.60451013901655, "val_loss": 347.5844535827637, "val_error": "29.964624", "acc_5mm": 0.0, "acc_10mm": 1.9736842105263157}, {"epoch": 25, "train_loss": 400.4804328469669, "val_loss": 252.85406494140625, "val_error": "24.7284", "acc_5mm": 0.3289473684210526, "acc_10mm": 6.25}], "parameters": 1397305}, "PointNet++": {"best_error": "27.359455", "history": [{"epoch": 1, "train_loss": 2857.496294806985, "val_loss": 2542.8851928710938, "val_error": "80.42909", "acc_5mm": 0.0, "acc_10mm": 0.0}, {"epoch": 2, "train_loss": 1252.4610667509191, "val_loss": 838.7126159667969, "val_error": "45.929226", "acc_5mm": 0.3289473684210526, "acc_10mm": 0.9868421052631579}, {"epoch": 3, "train_loss": 858.318208582261, "val_loss": 495.1233329772949, "val_error": "34.743748", "acc_5mm": 0.0, "acc_10mm": 3.9473684210526314}, {"epoch": 4, "train_loss": 789.3558241900275, "val_loss": 487.0093574523926, "val_error": "34.45118", "acc_5mm": 0.0, "acc_10mm": 4.276315789473684}, {"epoch": 5, "train_loss": 718.7193711224725, "val_loss": 631.6691436767578, "val_error": "40.69212", "acc_5mm": 0.0, "acc_10mm": 1.9736842105263157}, {"epoch": 6, "train_loss": 776.723492790671, "val_loss": 513.2381706237793, "val_error": "35.21581", "acc_5mm": 0.3289473684210526, "acc_10mm": 3.289473684210526}, {"epoch": 7, "train_loss": 722.8579532398898, "val_loss": 549.1523742675781, "val_error": "35.643974", "acc_5mm": 0.6578947368421052, "acc_10mm": 6.578947368421052}, {"epoch": 8, "train_loss": 785.9621510225184, "val_loss": 534.9499320983887, "val_error": "35.848415", "acc_5mm": 0.3289473684210526, "acc_10mm": 2.631578947368421}, {"epoch": 9, "train_loss": 680.1638865751379, "val_loss": 494.2695198059082, "val_error": "34.8167", "acc_5mm": 0.3289473684210526, "acc_10mm": 1.3157894736842104}, {"epoch": 10, "train_loss": 645.2418087230009, "val_loss": 431.4706802368164, "val_error": "32.81198", "acc_5mm": 0.0, "acc_10mm": 1.3157894736842104}, {"epoch": 11, "train_loss": 607.1522881002987, "val_loss": 486.0575866699219, "val_error": "34.78304", "acc_5mm": 0.0, "acc_10mm": 1.9736842105263157}, {"epoch": 12, "train_loss": 676.2900785558364, "val_loss": 613.7589340209961, "val_error": "39.464607", "acc_5mm": 0.6578947368421052, "acc_10mm": 1.3157894736842104}, {"epoch": 13, "train_loss": 734.9829370835248, "val_loss": 487.03108978271484, "val_error": "33.72018", "acc_5mm": 1.3157894736842104, "acc_10mm": 7.565789473684211}, {"epoch": 14, "train_loss": 651.1233340992648, "val_loss": 490.1246566772461, "val_error": "33.799793", "acc_5mm": 0.0, "acc_10mm": 1.3157894736842104}, {"epoch": 15, "train_loss": 560.5794085334329, "val_loss": 596.4162826538086, "val_error": "38.62593", "acc_5mm": 0.3289473684210526, "acc_10mm": 2.9605263157894735}, {"epoch": 16, "train_loss": 545.3963120404412, "val_loss": 434.8463249206543, "val_error": "33.06016", "acc_5mm": 0.6578947368421052, "acc_10mm": 3.618421052631579}, {"epoch": 17, "train_loss": 617.6654833625345, "val_loss": 526.6463623046875, "val_error": "36.428745", "acc_5mm": 0.6578947368421052, "acc_10mm": 3.289473684210526}, {"epoch": 18, "train_loss": 567.3752602969898, "val_loss": 587.3727416992188, "val_error": "38.070038", "acc_5mm": 0.6578947368421052, "acc_10mm": 2.631578947368421}, {"epoch": 19, "train_loss": 533.7476618149701, "val_loss": 403.0373344421387, "val_error": "31.515696", "acc_5mm": 0.9868421052631579, "acc_10mm": 4.934210526315789}, {"epoch": 20, "train_loss": 486.4907127829159, "val_loss": 490.1033744812012, "val_error": "35.120865", "acc_5mm": 0.3289473684210526, "acc_10mm": 2.631578947368421}, {"epoch": 21, "train_loss": 450.0482213637408, "val_loss": 337.83337783813477, "val_error": "28.633715", "acc_5mm": 0.9868421052631579, "acc_10mm": 8.552631578947368}, {"epoch": 22, "train_loss": 399.9833742029527, "val_loss": 323.9948959350586, "val_error": "28.23654", "acc_5mm": 1.3157894736842104, "acc_10mm": 7.236842105263158}, {"epoch": 23, "train_loss": 395.61256318933823, "val_loss": 314.05809020996094, "val_error": "27.359455", "acc_5mm": 2.302631578947368, "acc_10mm": 10.526315789473683}, {"epoch": 24, "train_loss": 487.4202409632066, "val_loss": 417.3089065551758, "val_error": "32.539722", "acc_5mm": 0.0, "acc_10mm": 2.9605263157894735}, {"epoch": 25, "train_loss": 438.47517484777114, "val_loss": 326.80033111572266, "val_error": "28.51687", "acc_5mm": 0.9868421052631579, "acc_10mm": 5.921052631578947}], "parameters": 1726521}, "ResPointNet": {"error": "Expected 3-dimensional tensor, but got 4-dimensional tensor for argument #1 'self' (while checking arguments for adaptive_avg_pool1d)"}}