#!/usr/bin/env python3
"""
基于已验证12点数据的57点扩展
57-point expansion based on proven 12-point data
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import json
import os
from tqdm import tqdm

class AnatomicalExpansionNetwork(nn.Module):
    """解剖学扩展网络"""
    
    def __init__(self, input_dim=36, output_dim=171):
        super().__init__()
        
        # 输入: 12个关键点 × 3坐标 = 36维
        # 输出: 57个关键点 × 3坐标 = 171维
        
        # 解剖学知识编码器
        self.anatomy_encoder = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 256),
            nn.<PERSON><PERSON><PERSON>(),
            nn.Dropout(0.3),
            nn.Linear(256, 512),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 区域特异性解码器
        self.f1_decoder = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, 19 * 3)  # F1区域19个点
        )
        
        self.f2_decoder = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, 19 * 3)  # F2区域19个点
        )
        
        self.f3_decoder = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, 19 * 3)  # F3区域19个点
        )
        
        # 12点到57点的映射
        self.mapping_12_to_57 = {
            0: 0, 1: 1, 2: 2, 3: 12,      # F1区域
            4: 19, 5: 20, 6: 21, 7: 31,   # F2区域
            8: 38, 9: 52, 10: 50, 11: 51  # F3区域
        }
        
    def forward(self, keypoints_12):
        """前向传播"""
        batch_size = keypoints_12.size(0)
        
        # 展平输入
        x = keypoints_12.view(batch_size, -1)
        
        # 解剖学特征编码
        features = self.anatomy_encoder(x)
        
        # 区域特异性解码
        f1_points = self.f1_decoder(features).view(batch_size, 19, 3)
        f2_points = self.f2_decoder(features).view(batch_size, 19, 3)
        f3_points = self.f3_decoder(features).view(batch_size, 19, 3)
        
        # 合并所有区域
        keypoints_57 = torch.cat([f1_points, f2_points, f3_points], dim=1)
        
        # 应用已知12点约束
        keypoints_57 = self.apply_known_constraints(keypoints_57, keypoints_12)
        
        return keypoints_57
    
    def apply_known_constraints(self, keypoints_57, keypoints_12):
        """应用已知12点约束"""
        
        # 将已知的12个点直接复制到对应位置
        for i in range(12):
            target_idx = self.mapping_12_to_57[i]
            keypoints_57[:, target_idx, :] = keypoints_12[:, i, :]
        
        return keypoints_57

class ExpansionDataset(Dataset):
    """扩展数据集"""
    
    def __init__(self, keypoints_12, keypoints_57_target):
        self.keypoints_12 = torch.FloatTensor(keypoints_12)
        self.keypoints_57_target = torch.FloatTensor(keypoints_57_target)
        
    def __len__(self):
        return len(self.keypoints_12)
    
    def __getitem__(self, idx):
        return self.keypoints_12[idx], self.keypoints_57_target[idx]

def generate_anatomical_57_targets(keypoints_12):
    """基于解剖学知识生成57点目标"""
    
    print("🧬 基于解剖学知识生成57点目标...")
    
    targets_57 = []
    
    for kp_12 in keypoints_12:
        kp_57 = np.zeros((57, 3))
        
        # 首先放置已知的12个点
        mapping_12_to_57 = {
            0: 0, 1: 1, 2: 2, 3: 12,      # F1区域
            4: 19, 5: 20, 6: 21, 7: 31,   # F2区域
            8: 38, 9: 52, 10: 50, 11: 51  # F3区域
        }
        
        for i in range(12):
            target_idx = mapping_12_to_57[i]
            kp_57[target_idx] = kp_12[i]
        
        # 为每个区域生成其他点
        generate_f1_region(kp_57, kp_12)
        generate_f2_region(kp_57, kp_12)
        generate_f3_region(kp_57, kp_12)
        
        targets_57.append(kp_57)
    
    return np.array(targets_57)

def generate_f1_region(kp_57, kp_12):
    """生成F1区域的其他点"""
    
    # F1区域已知点: 0, 1, 2, 12 (对应12点中的0, 1, 2, 3)
    known_f1 = [kp_57[0], kp_57[1], kp_57[2], kp_57[12]]
    
    # 计算F1区域中心和主轴
    f1_center = np.mean(known_f1, axis=0)
    
    # 生成F1区域的其他点 (3-11, 13-18)
    for i in range(19):
        if i not in [0, 1, 2, 12]:  # 跳过已知点
            # 基于已知点的相对位置生成
            if i < 12:
                # 前半部分点，在0-2之间插值
                t = (i - 3) / 9.0  # 归一化到0-1
                base_point = kp_57[0] + t * (kp_57[2] - kp_57[0])
            else:
                # 后半部分点，在12周围
                t = (i - 13) / 5.0  # 归一化到0-1
                base_point = kp_57[12] + t * (f1_center - kp_57[12])
            
            # 添加一些解剖学合理的偏移
            offset = np.random.normal(0, 3, 3)  # 3mm标准差
            kp_57[i] = base_point + offset

def generate_f2_region(kp_57, kp_12):
    """生成F2区域的其他点"""
    
    # F2区域已知点: 19, 20, 21, 31 (对应12点中的4, 5, 6, 7)
    known_f2 = [kp_57[19], kp_57[20], kp_57[21], kp_57[31]]
    
    # 计算F2区域中心
    f2_center = np.mean(known_f2, axis=0)
    
    # 生成F2区域的其他点 (22-30, 32-37)
    for i in range(19, 38):
        if i not in [19, 20, 21, 31]:  # 跳过已知点
            # 基于已知点的相对位置生成
            if i < 31:
                # 前半部分点，在19-21之间插值
                t = (i - 22) / 8.0
                base_point = kp_57[19] + t * (kp_57[21] - kp_57[19])
            else:
                # 后半部分点，在31周围
                t = (i - 32) / 5.0
                base_point = kp_57[31] + t * (f2_center - kp_57[31])
            
            # 添加解剖学合理的偏移
            offset = np.random.normal(0, 3, 3)
            kp_57[i] = base_point + offset

def generate_f3_region(kp_57, kp_12):
    """生成F3区域的其他点"""
    
    # F3区域已知点: 38, 50, 51, 52 (对应12点中的8, 10, 11, 9)
    known_f3 = [kp_57[38], kp_57[50], kp_57[51], kp_57[52]]
    
    # 计算F3区域中心
    f3_center = np.mean(known_f3, axis=0)
    
    # 生成F3区域的其他点
    for i in range(38, 57):
        if i not in [38, 50, 51, 52]:  # 跳过已知点
            # 基于已知点的相对位置生成
            if i < 50:
                # 前半部分点，在38周围
                t = (i - 39) / 10.0
                base_point = kp_57[38] + t * (f3_center - kp_57[38])
            else:
                # 后半部分点，在50-52之间
                t = (i - 53) / 3.0 if i > 52 else 0
                base_point = kp_57[50] + t * (kp_57[52] - kp_57[50])
            
            # 添加解剖学合理的偏移
            offset = np.random.normal(0, 4, 3)  # F3区域稍大一些的变异
            kp_57[i] = base_point + offset

def train_expansion_model(model, train_loader, val_loader, epochs=50, device='cuda'):
    """训练扩展模型"""
    
    print(f"🚀 开始训练解剖学扩展网络...")
    print(f"   设备: {device}")
    print(f"   训练样本: {len(train_loader.dataset)}")
    print(f"   验证样本: {len(val_loader.dataset)}")
    print(f"   训练轮数: {epochs}")
    
    model = model.to(device)
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    criterion = nn.MSELoss()
    
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_error': [],
        'val_error': []
    }
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 15
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{epochs} [Train]')
        for batch_12, batch_57 in train_pbar:
            batch_12 = batch_12.to(device)
            batch_57 = batch_57.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            predicted_57 = model(batch_12)
            loss = criterion(predicted_57, batch_57)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            
            # 计算平均距离误差
            with torch.no_grad():
                distances = torch.norm(predicted_57 - batch_57, dim=2)
                train_error += torch.mean(distances).item()
            
            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Error': f'{torch.mean(distances).item():.2f}mm'
            })
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{epochs} [Val]')
            for batch_12, batch_57 in val_pbar:
                batch_12 = batch_12.to(device)
                batch_57 = batch_57.to(device)
                
                predicted_57 = model(batch_12)
                loss = criterion(predicted_57, batch_57)
                
                val_loss += loss.item()
                
                distances = torch.norm(predicted_57 - batch_57, dim=2)
                val_error += torch.mean(distances).item()
                
                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Error': f'{torch.mean(distances).item():.2f}mm'
                })
        
        # 计算平均损失和误差
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_error'].append(train_error)
        history['val_error'].append(val_error)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_anatomical_expansion.pth')
        else:
            patience_counter += 1
        
        # 打印进度
        print(f"Epoch {epoch+1:3d}: "
              f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
              f"Train Error: {train_error:.2f}mm, Val Error: {val_error:.2f}mm")
        
        # 早停
        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    print(f"✅ 训练完成！最佳验证损失: {best_val_loss:.6f}")
    
    # 加载最佳模型
    model.load_state_dict(torch.load('best_anatomical_expansion.pth'))
    
    return history

def main():
    """主函数"""
    
    print("🎯 基于已验证12点数据的57点扩展")
    print("使用解剖学知识生成训练目标")
    print("=" * 80)
    
    # 检查CUDA
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载已验证的12点数据
    try:
        data = np.load('smart_expanded_57_dataset.npz', allow_pickle=True)
        proven_12kp = data['proven_12_keypoints']
        gender_labels = data['gender_labels']
        
        print(f"✅ 已验证12点数据:")
        print(f"   样本数: {len(proven_12kp)}")
        print(f"   12关键点: {proven_12kp.shape}")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 生成解剖学57点目标
    anatomical_57kp = generate_anatomical_57_targets(proven_12kp)
    
    print(f"✅ 生成解剖学57点目标:")
    print(f"   57关键点: {anatomical_57kp.shape}")
    
    # 数据划分
    indices = np.arange(len(proven_12kp))
    train_indices, test_indices = train_test_split(
        indices, test_size=0.2, random_state=42
    )
    train_indices, val_indices = train_test_split(
        train_indices, test_size=0.2, random_state=42
    )
    
    # 创建数据集
    train_dataset = ExpansionDataset(
        proven_12kp[train_indices], 
        anatomical_57kp[train_indices]
    )
    val_dataset = ExpansionDataset(
        proven_12kp[val_indices], 
        anatomical_57kp[val_indices]
    )
    test_dataset = ExpansionDataset(
        proven_12kp[test_indices], 
        anatomical_57kp[test_indices]
    )
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=8, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=8, shuffle=False)
    
    print(f"📋 数据划分:")
    print(f"   训练集: {len(train_dataset)} 样本")
    print(f"   验证集: {len(val_dataset)} 样本")
    print(f"   测试集: {len(test_dataset)} 样本")
    
    # 创建模型
    model = AnatomicalExpansionNetwork()
    print(f"🤖 模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练模型
    history = train_expansion_model(model, train_loader, val_loader, epochs=30, device=device)
    
    # 测试模型
    model.eval()
    test_error = 0.0
    
    with torch.no_grad():
        for batch_12, batch_57 in test_loader:
            batch_12 = batch_12.to(device)
            batch_57 = batch_57.to(device)
            
            predicted_57 = model(batch_12)
            distances = torch.norm(predicted_57 - batch_57, dim=2)
            test_error += torch.mean(distances).item()
    
    test_error /= len(test_loader)
    
    print(f"\n🎯 最终测试结果:")
    print(f"   平均误差: {test_error:.2f}mm")
    
    # 保存结果
    results = {
        'test_error': test_error,
        'training_history': history,
        'model_info': {
            'parameters': sum(p.numel() for p in model.parameters()),
            'architecture': 'AnatomicalExpansionNetwork'
        }
    }
    
    with open('anatomical_expansion_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n🎉 解剖学扩展训练完成！")
    print(f"📋 生成的文件:")
    print(f"   - best_anatomical_expansion.pth (最佳模型)")
    print(f"   - anatomical_expansion_results.json (评估结果)")
    
    if test_error < 20:
        print(f"✅ 扩展网络性能良好！可以用于57点预测")
    else:
        print(f"⚠️ 扩展网络需要进一步优化")

if __name__ == "__main__":
    main()
