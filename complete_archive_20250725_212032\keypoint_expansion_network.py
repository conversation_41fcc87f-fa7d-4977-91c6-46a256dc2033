#!/usr/bin/env python3
"""
12→57关键点扩展网络
Keypoint Expansion Network: 12 → 57 keypoints
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import json
import os

class KeypointExpansionDataset(Dataset):
    """关键点扩展数据集"""
    
    def __init__(self, input_12, target_57):
        self.input_12 = torch.FloatTensor(input_12)
        self.target_57 = torch.FloatTensor(target_57)
        
    def __len__(self):
        return len(self.input_12)
    
    def __getitem__(self, idx):
        return self.input_12[idx], self.target_57[idx]

class AnatomicalConstraintLayer(nn.Module):
    """解剖学约束层"""
    
    def __init__(self):
        super().__init__()
        
    def forward(self, keypoints_57):
        """应用解剖学约束"""
        # keypoints_57: [batch_size, 57, 3]
        
        # F1-F2对称性约束
        keypoints_57 = self.apply_symmetry_constraint(keypoints_57)
        
        # 区域内距离约束
        keypoints_57 = self.apply_distance_constraints(keypoints_57)
        
        return keypoints_57
    
    def apply_symmetry_constraint(self, keypoints):
        """应用F1-F2对称性约束"""
        f1_points = keypoints[:, 0:19, :]    # F1: 0-18
        f2_points = keypoints[:, 19:38, :]   # F2: 19-37
        f3_points = keypoints[:, 38:57, :]   # F3: 38-56
        
        # 计算对称轴（简化版本）
        f1_center = torch.mean(f1_points, dim=1, keepdim=True)
        f2_center = torch.mean(f2_points, dim=1, keepdim=True)
        
        # 轻微的对称性调整
        symmetry_factor = 0.1
        f1_adjusted = f1_points + (f2_center - f1_center) * symmetry_factor * torch.tensor([-1, 0, 0])
        f2_adjusted = f2_points + (f1_center - f2_center) * symmetry_factor * torch.tensor([-1, 0, 0])
        
        return torch.cat([f1_adjusted, f2_adjusted, f3_points], dim=1)
    
    def apply_distance_constraints(self, keypoints):
        """应用距离约束（简化版本）"""
        # 这里可以添加更复杂的距离约束
        return keypoints

class KeypointExpansionNetwork(nn.Module):
    """关键点扩展网络"""
    
    def __init__(self, input_dim=36, hidden_dim=512, output_dim=171):
        super().__init__()
        
        # 输入: 12个关键点 × 3坐标 = 36维
        # 输出: 57个关键点 × 3坐标 = 171维
        
        self.input_projection = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 多层特征提取
        self.feature_layers = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim * 2, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 输出层
        self.output_layer = nn.Linear(hidden_dim, output_dim)
        
        # 解剖学约束层
        self.anatomical_constraints = AnatomicalConstraintLayer()
        
    def forward(self, keypoints_12):
        """前向传播"""
        # keypoints_12: [batch_size, 12, 3]
        batch_size = keypoints_12.size(0)
        
        # 展平输入
        x = keypoints_12.view(batch_size, -1)  # [batch_size, 36]
        
        # 特征提取
        x = self.input_projection(x)
        x = self.feature_layers(x)
        
        # 生成57个关键点
        output = self.output_layer(x)
        keypoints_57 = output.view(batch_size, 57, 3)
        
        # 应用解剖学约束
        constrained_keypoints = self.anatomical_constraints(keypoints_57)
        
        return constrained_keypoints

class ExpansionTrainer:
    """扩展网络训练器"""
    
    def __init__(self, model, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.model = model.to(device)
        self.device = device
        self.training_history = {
            'train_loss': [],
            'val_loss': [],
            'train_error': [],
            'val_error': []
        }
    
    def train_model(self, train_loader, val_loader, epochs=100, lr=0.001):
        """训练模型"""
        
        print(f"🚀 开始训练12→57扩展网络...")
        print(f"   设备: {self.device}")
        print(f"   训练样本: {len(train_loader.dataset)}")
        print(f"   验证样本: {len(val_loader.dataset)}")
        print(f"   训练轮数: {epochs}")
        
        optimizer = optim.Adam(self.model.parameters(), lr=lr, weight_decay=1e-5)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        criterion = nn.MSELoss()
        
        best_val_loss = float('inf')
        patience_counter = 0
        patience = 20
        
        for epoch in range(epochs):
            # 训练阶段
            self.model.train()
            train_loss = 0.0
            train_error = 0.0
            
            for batch_input, batch_target in train_loader:
                batch_input = batch_input.to(self.device)
                batch_target = batch_target.to(self.device)
                
                optimizer.zero_grad()
                
                # 前向传播
                predicted = self.model(batch_input)
                loss = criterion(predicted, batch_target)
                
                # 反向传播
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                
                # 计算平均距离误差
                with torch.no_grad():
                    distances = torch.norm(predicted - batch_target, dim=2)
                    train_error += torch.mean(distances).item()
            
            # 验证阶段
            self.model.eval()
            val_loss = 0.0
            val_error = 0.0
            
            with torch.no_grad():
                for batch_input, batch_target in val_loader:
                    batch_input = batch_input.to(self.device)
                    batch_target = batch_target.to(self.device)
                    
                    predicted = self.model(batch_input)
                    loss = criterion(predicted, batch_target)
                    
                    val_loss += loss.item()
                    
                    distances = torch.norm(predicted - batch_target, dim=2)
                    val_error += torch.mean(distances).item()
            
            # 计算平均损失和误差
            train_loss /= len(train_loader)
            val_loss /= len(val_loader)
            train_error /= len(train_loader)
            val_error /= len(val_loader)
            
            # 记录历史
            self.training_history['train_loss'].append(train_loss)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['train_error'].append(train_error)
            self.training_history['val_error'].append(val_error)
            
            # 学习率调度
            scheduler.step(val_loss)
            
            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # 保存最佳模型
                torch.save(self.model.state_dict(), 'best_expansion_model.pth')
            else:
                patience_counter += 1
            
            # 打印进度
            if epoch % 10 == 0 or epoch == epochs - 1:
                print(f"Epoch {epoch:3d}: "
                      f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
                      f"Train Error: {train_error:.2f}mm, Val Error: {val_error:.2f}mm")
            
            # 早停
            if patience_counter >= patience:
                print(f"早停触发，在第 {epoch} 轮停止训练")
                break
        
        print(f"✅ 训练完成！最佳验证损失: {best_val_loss:.6f}")
        
        # 加载最佳模型
        self.model.load_state_dict(torch.load('best_expansion_model.pth'))
        
        return self.training_history
    
    def evaluate_model(self, test_loader):
        """评估模型"""
        
        print(f"\n🔍 评估扩展网络性能...")
        
        self.model.eval()
        total_error = 0.0
        region_errors = {'F1': [], 'F2': [], 'F3': []}
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch_input, batch_target in test_loader:
                batch_input = batch_input.to(self.device)
                batch_target = batch_target.to(self.device)
                
                predicted = self.model(batch_input)
                
                # 计算整体误差
                distances = torch.norm(predicted - batch_target, dim=2)
                total_error += torch.mean(distances).item()
                
                # 计算各区域误差
                for i in range(predicted.size(0)):
                    pred = predicted[i].cpu().numpy()
                    target = batch_target[i].cpu().numpy()
                    
                    # F1区域 (0-18)
                    f1_distances = np.linalg.norm(pred[0:19] - target[0:19], axis=1)
                    region_errors['F1'].extend(f1_distances)
                    
                    # F2区域 (19-37)
                    f2_distances = np.linalg.norm(pred[19:38] - target[19:38], axis=1)
                    region_errors['F2'].extend(f2_distances)
                    
                    # F3区域 (38-56)
                    f3_distances = np.linalg.norm(pred[38:57] - target[38:57], axis=1)
                    region_errors['F3'].extend(f3_distances)
                
                all_predictions.append(predicted.cpu().numpy())
                all_targets.append(batch_target.cpu().numpy())
        
        avg_error = total_error / len(test_loader)
        
        print(f"📊 扩展网络评估结果:")
        print(f"   整体平均误差: {avg_error:.2f}mm")
        
        for region, errors in region_errors.items():
            if errors:
                mean_error = np.mean(errors)
                std_error = np.std(errors)
                max_error = np.max(errors)
                print(f"   {region}区域: {mean_error:.2f}±{std_error:.2f}mm (最大: {max_error:.2f}mm)")
        
        # 计算医疗级准确率
        all_errors = []
        for errors in region_errors.values():
            all_errors.extend(errors)
        
        if all_errors:
            accuracy_5mm = np.mean(np.array(all_errors) < 5.0) * 100
            accuracy_10mm = np.mean(np.array(all_errors) < 10.0) * 100
            
            print(f"   医疗级准确率:")
            print(f"     <5mm: {accuracy_5mm:.1f}%")
            print(f"     <10mm: {accuracy_10mm:.1f}%")
        
        return {
            'avg_error': avg_error,
            'region_errors': region_errors,
            'accuracy_5mm': accuracy_5mm if all_errors else 0,
            'accuracy_10mm': accuracy_10mm if all_errors else 0,
            'predictions': np.vstack(all_predictions),
            'targets': np.vstack(all_targets)
        }

def load_expansion_data():
    """加载扩展训练数据"""
    
    print("📊 加载12→57扩展训练数据...")
    
    try:
        data = np.load('expansion_training_keypoints.npz', allow_pickle=True)
        
        input_12 = data['input_12']
        target_57 = data['target_57']
        sample_ids = data['sample_ids']
        
        print(f"✅ 数据加载成功:")
        print(f"   输入12点: {input_12.shape}")
        print(f"   目标57点: {target_57.shape}")
        print(f"   样本数: {len(sample_ids)}")
        
        return input_12, target_57, sample_ids
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None, None, None

def create_data_loaders(input_12, target_57, batch_size=8, test_size=0.2):
    """创建数据加载器"""
    
    # 数据划分
    indices = np.arange(len(input_12))
    train_indices, test_indices = train_test_split(
        indices, test_size=test_size, random_state=42
    )
    train_indices, val_indices = train_test_split(
        train_indices, test_size=0.2, random_state=42
    )
    
    # 创建数据集
    train_dataset = KeypointExpansionDataset(input_12[train_indices], target_57[train_indices])
    val_dataset = KeypointExpansionDataset(input_12[val_indices], target_57[val_indices])
    test_dataset = KeypointExpansionDataset(input_12[test_indices], target_57[test_indices])
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    print(f"📋 数据划分:")
    print(f"   训练集: {len(train_dataset)} 样本")
    print(f"   验证集: {len(val_dataset)} 样本")
    print(f"   测试集: {len(test_dataset)} 样本")
    
    return train_loader, val_loader, test_loader

def plot_training_history(history):
    """绘制训练历史"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
    
    # 损失曲线
    ax1.plot(history['train_loss'], label='Train Loss')
    ax1.plot(history['val_loss'], label='Val Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.set_title('Training and Validation Loss')
    ax1.legend()
    ax1.grid(True)
    
    # 误差曲线
    ax2.plot(history['train_error'], label='Train Error (mm)')
    ax2.plot(history['val_error'], label='Val Error (mm)')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Error (mm)')
    ax2.set_title('Training and Validation Error')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig('expansion_training_history.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 训练历史图已保存: expansion_training_history.png")

def main():
    """主函数"""
    
    print("🎯 12→57关键点扩展网络训练")
    print("基于解剖学约束的智能关键点扩展")
    print("=" * 80)
    
    # 加载数据
    input_12, target_57, sample_ids = load_expansion_data()
    
    if input_12 is None:
        print("❌ 数据加载失败，退出")
        return
    
    # 创建数据加载器
    train_loader, val_loader, test_loader = create_data_loaders(input_12, target_57)
    
    # 创建模型
    model = KeypointExpansionNetwork()
    print(f"🤖 模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建训练器
    trainer = ExpansionTrainer(model)
    
    # 训练模型
    history = trainer.train_model(train_loader, val_loader, epochs=100)
    
    # 绘制训练历史
    plot_training_history(history)
    
    # 评估模型
    results = trainer.evaluate_model(test_loader)
    
    # 保存结果
    results_summary = {
        'avg_error': results['avg_error'],
        'accuracy_5mm': results['accuracy_5mm'],
        'accuracy_10mm': results['accuracy_10mm'],
        'region_errors': {k: float(np.mean(v)) for k, v in results['region_errors'].items()},
        'training_history': history
    }
    
    with open('expansion_results.json', 'w') as f:
        json.dump(results_summary, f, indent=2)
    
    print(f"\n🎉 12→57关键点扩展网络训练完成！")
    print(f"📋 生成的文件:")
    print(f"   - best_expansion_model.pth (最佳模型)")
    print(f"   - expansion_training_history.png (训练历史)")
    print(f"   - expansion_results.json (评估结果)")
    
    print(f"\n🎯 扩展网络性能:")
    print(f"   平均误差: {results['avg_error']:.2f}mm")
    print(f"   医疗级准确率 (<5mm): {results['accuracy_5mm']:.1f}%")
    
    if results['avg_error'] < 10.0:
        print(f"✅ 扩展网络性能良好！可以进行下一步端到端训练")
    else:
        print(f"⚠️ 扩展网络需要进一步优化")

if __name__ == "__main__":
    main()
