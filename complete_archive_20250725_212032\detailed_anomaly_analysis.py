#!/usr/bin/env python3
"""
详细异常样本分析 - 输出具体异常信息供人工检查
Detailed Anomaly Analysis - Output Specific Anomaly Information for Manual Review
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json
from pathlib import Path
from datetime import datetime
import pandas as pd

class DetailedAnomalyAnalyzer:
    """详细异常分析器"""
    
    def __init__(self, data_path='data/raw/high_quality_f3_dataset.npz'):
        self.data_path = data_path
        self.load_data()
        
    def load_data(self):
        """加载数据"""
        print(f"📦 加载数据进行详细异常分析: {self.data_path}")
        
        data = np.load(self.data_path, allow_pickle=True)
        self.sample_ids = data['sample_ids']
        self.point_clouds = data['point_clouds']
        self.keypoints = data['keypoints']
        
        print(f"✅ 数据加载完成: {len(self.sample_ids)} 样本")
        
    def analyze_keypoint_distances(self):
        """分析关键点间距离的详细异常"""
        print("\n🔍 详细分析关键点间距离异常...")
        
        # 计算所有样本的关键点间距离
        all_distances = []
        sample_distances = []
        
        for i, kps in enumerate(self.keypoints):
            distances = []
            for j in range(len(kps)):
                for k in range(j+1, len(kps)):
                    dist = np.linalg.norm(kps[j] - kps[k])
                    distances.append(dist)
                    all_distances.append(dist)
            sample_distances.append(distances)
        
        # 计算统计量
        all_distances = np.array(all_distances)
        sample_distances = np.array(sample_distances)
        
        mean_distances = np.mean(sample_distances, axis=0)
        std_distances = np.std(sample_distances, axis=0)
        
        # 详细异常分析
        anomaly_details = []
        
        for i, (sample_id, distances) in enumerate(zip(self.sample_ids, sample_distances)):
            sample_anomalies = []
            
            # 计算Z分数
            z_scores = np.abs((distances - mean_distances) / std_distances)
            
            # 识别异常距离
            outlier_indices = np.where(z_scores > 2.0)[0]
            
            if len(outlier_indices) > 0:
                for outlier_idx in outlier_indices:
                    # 找到对应的关键点对
                    pair_idx = 0
                    for j in range(19):
                        for k in range(j+1, 19):
                            if pair_idx == outlier_idx:
                                keypoint_pair = (j, k)
                                break
                            pair_idx += 1
                        else:
                            continue
                        break
                    
                    anomaly_info = {
                        'type': '关键点间距离异常',
                        'keypoint_pair': keypoint_pair,
                        'keypoint_names': f'F3-{keypoint_pair[0]+1} <-> F3-{keypoint_pair[1]+1}',
                        'actual_distance': distances[outlier_idx],
                        'expected_distance': mean_distances[outlier_idx],
                        'z_score': z_scores[outlier_idx],
                        'deviation_mm': abs(distances[outlier_idx] - mean_distances[outlier_idx])
                    }
                    sample_anomalies.append(anomaly_info)
            
            # 检查整体距离分布
            sample_mean = np.mean(distances)
            global_mean = np.mean(all_distances)
            if abs(sample_mean - global_mean) > 2 * np.std(all_distances):
                sample_anomalies.append({
                    'type': '整体尺度异常',
                    'sample_mean_distance': sample_mean,
                    'global_mean_distance': global_mean,
                    'scale_factor': sample_mean / global_mean,
                    'severity': 'high' if abs(sample_mean - global_mean) > 3 * np.std(all_distances) else 'medium'
                })
            
            if sample_anomalies:
                anomaly_details.append({
                    'sample_id': sample_id,
                    'sample_index': i,
                    'anomaly_count': len(sample_anomalies),
                    'anomalies': sample_anomalies
                })
        
        return anomaly_details
    
    def analyze_keypoint_positions(self):
        """分析关键点位置异常"""
        print("\n📍 详细分析关键点位置异常...")
        
        position_anomalies = []
        
        # 计算每个关键点的统计信息
        keypoint_stats = []
        for kp_idx in range(19):
            positions = [kps[kp_idx] for kps in self.keypoints]
            positions = np.array(positions)
            
            mean_pos = np.mean(positions, axis=0)
            std_pos = np.std(positions, axis=0)
            
            keypoint_stats.append({
                'mean_position': mean_pos,
                'std_position': std_pos,
                'positions': positions
            })
        
        # 检查每个样本的关键点位置
        for i, (sample_id, kps) in enumerate(zip(self.sample_ids, self.keypoints)):
            sample_anomalies = []
            
            for kp_idx in range(19):
                kp_pos = kps[kp_idx]
                expected_pos = keypoint_stats[kp_idx]['mean_position']
                std_pos = keypoint_stats[kp_idx]['std_position']
                
                # 计算位置偏差
                deviation = np.linalg.norm(kp_pos - expected_pos)
                expected_deviation = np.linalg.norm(std_pos)
                
                if deviation > 3 * expected_deviation:  # 3倍标准差
                    sample_anomalies.append({
                        'type': '关键点位置异常',
                        'keypoint_index': kp_idx,
                        'keypoint_name': f'F3-{kp_idx+1}',
                        'actual_position': kp_pos.tolist(),
                        'expected_position': expected_pos.tolist(),
                        'deviation_mm': deviation,
                        'severity': 'high' if deviation > 5 * expected_deviation else 'medium'
                    })
            
            if sample_anomalies:
                position_anomalies.append({
                    'sample_id': sample_id,
                    'sample_index': i,
                    'anomaly_count': len(sample_anomalies),
                    'anomalies': sample_anomalies
                })
        
        return position_anomalies
    
    def analyze_point_cloud_anomalies(self):
        """分析点云异常"""
        print("\n☁️ 详细分析点云异常...")
        
        pc_anomalies = []
        
        # 计算点云统计信息
        pc_sizes = [len(pc) for pc in self.point_clouds]
        pc_bounds = []
        pc_densities = []
        
        for pc in self.point_clouds:
            # 包围盒
            min_bounds = np.min(pc, axis=0)
            max_bounds = np.max(pc, axis=0)
            bounds = max_bounds - min_bounds
            pc_bounds.append(bounds)
            
            # 密度 (点数/体积)
            volume = np.prod(bounds)
            density = len(pc) / volume if volume > 0 else 0
            pc_densities.append(density)
        
        pc_bounds = np.array(pc_bounds)
        pc_densities = np.array(pc_densities)
        
        # 统计量
        mean_size = np.mean(pc_sizes)
        std_size = np.std(pc_sizes)
        mean_bounds = np.mean(pc_bounds, axis=0)
        std_bounds = np.std(pc_bounds, axis=0)
        mean_density = np.mean(pc_densities)
        std_density = np.std(pc_densities)
        
        # 检查异常
        for i, (sample_id, pc) in enumerate(zip(self.sample_ids, self.point_clouds)):
            sample_anomalies = []
            
            # 点数异常
            if abs(len(pc) - mean_size) > 2 * std_size:
                sample_anomalies.append({
                    'type': '点云大小异常',
                    'actual_size': len(pc),
                    'expected_size': int(mean_size),
                    'deviation': abs(len(pc) - mean_size),
                    'severity': 'high' if abs(len(pc) - mean_size) > 3 * std_size else 'medium'
                })
            
            # 包围盒异常
            bounds = pc_bounds[i]
            bounds_z_score = np.abs((bounds - mean_bounds) / std_bounds)
            if np.any(bounds_z_score > 2.5):
                anomalous_dims = ['X', 'Y', 'Z'][np.argmax(bounds_z_score)]
                sample_anomalies.append({
                    'type': '点云尺寸异常',
                    'anomalous_dimension': anomalous_dims,
                    'actual_bounds': bounds.tolist(),
                    'expected_bounds': mean_bounds.tolist(),
                    'max_z_score': np.max(bounds_z_score)
                })
            
            # 密度异常
            if abs(pc_densities[i] - mean_density) > 2 * std_density:
                sample_anomalies.append({
                    'type': '点云密度异常',
                    'actual_density': pc_densities[i],
                    'expected_density': mean_density,
                    'density_ratio': pc_densities[i] / mean_density if mean_density > 0 else 0
                })
            
            if sample_anomalies:
                pc_anomalies.append({
                    'sample_id': sample_id,
                    'sample_index': i,
                    'anomaly_count': len(sample_anomalies),
                    'anomalies': sample_anomalies
                })
        
        return pc_anomalies
    
    def visualize_specific_anomalies(self, anomaly_samples, max_samples=5):
        """可视化特定异常样本"""
        print(f"\n📊 可视化前{max_samples}个异常样本...")
        
        viz_dir = Path("results/anomaly_visualization")
        viz_dir.mkdir(parents=True, exist_ok=True)
        
        for i, anomaly_info in enumerate(anomaly_samples[:max_samples]):
            sample_idx = anomaly_info['sample_index']
            sample_id = anomaly_info['sample_id']
            
            pc = self.point_clouds[sample_idx]
            kps = self.keypoints[sample_idx]
            
            fig = plt.figure(figsize=(15, 10))
            
            # 3D可视化
            ax1 = fig.add_subplot(221, projection='3d')
            
            # 绘制点云
            ax1.scatter(pc[:, 0], pc[:, 1], pc[:, 2], 
                       c='lightgray', s=1, alpha=0.3, label='Point Cloud')
            
            # 绘制关键点
            ax1.scatter(kps[:, 0], kps[:, 1], kps[:, 2], 
                       c='red', s=50, label='Keypoints')
            
            # 标注关键点编号
            for j, kp in enumerate(kps):
                ax1.text(kp[0], kp[1], kp[2], f'F3-{j+1}', fontsize=8)
            
            ax1.set_title(f'Sample {sample_id} - Anomaly Visualization')
            ax1.legend()
            ax1.set_xlabel('X')
            ax1.set_ylabel('Y')
            ax1.set_zlabel('Z')
            
            # 异常信息文本
            ax2 = fig.add_subplot(222)
            anomaly_text = f"Sample ID: {sample_id}\n"
            anomaly_text += f"Anomaly Count: {anomaly_info['total_anomaly_count']}\n\n"
            
            for j, anomaly in enumerate(anomaly_info['anomalies'][:5]):  # 显示前5个异常
                anomaly_text += f"Anomaly {j+1}:\n"
                anomaly_text += f"  Type: {anomaly['type']}\n"
                if 'keypoint_names' in anomaly:
                    anomaly_text += f"  Keypoints: {anomaly['keypoint_names']}\n"
                if 'deviation_mm' in anomaly:
                    anomaly_text += f"  Deviation: {anomaly['deviation_mm']:.2f}mm\n"
                anomaly_text += "\n"
            
            ax2.text(0.05, 0.95, anomaly_text, transform=ax2.transAxes, 
                    fontsize=10, verticalalignment='top', fontfamily='monospace')
            ax2.set_xlim(0, 1)
            ax2.set_ylim(0, 1)
            ax2.axis('off')
            
            # 关键点距离热图
            ax3 = fig.add_subplot(223)
            distance_matrix = np.zeros((19, 19))
            for j in range(19):
                for k in range(19):
                    if j != k:
                        distance_matrix[j, k] = np.linalg.norm(kps[j] - kps[k])
            
            im = ax3.imshow(distance_matrix, cmap='viridis')
            ax3.set_title('Keypoint Distance Matrix')
            ax3.set_xlabel('Keypoint Index')
            ax3.set_ylabel('Keypoint Index')
            plt.colorbar(im, ax=ax3)
            
            # 点云统计
            ax4 = fig.add_subplot(224)
            stats_text = f"Point Cloud Statistics:\n"
            stats_text += f"  Points: {len(pc)}\n"
            stats_text += f"  Bounds X: [{np.min(pc[:, 0]):.1f}, {np.max(pc[:, 0]):.1f}]\n"
            stats_text += f"  Bounds Y: [{np.min(pc[:, 1]):.1f}, {np.max(pc[:, 1]):.1f}]\n"
            stats_text += f"  Bounds Z: [{np.min(pc[:, 2]):.1f}, {np.max(pc[:, 2]):.1f}]\n"
            stats_text += f"  Centroid: [{np.mean(pc, axis=0)[0]:.1f}, {np.mean(pc, axis=0)[1]:.1f}, {np.mean(pc, axis=0)[2]:.1f}]\n"
            
            ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes,
                    fontsize=10, verticalalignment='top', fontfamily='monospace')
            ax4.set_xlim(0, 1)
            ax4.set_ylim(0, 1)
            ax4.axis('off')
            
            plt.tight_layout()
            
            # 保存图片
            plt.savefig(viz_dir / f"anomaly_sample_{sample_id}.png", 
                       dpi=150, bbox_inches='tight')
            print(f"💾 保存异常样本可视化: anomaly_sample_{sample_id}.png")
            
            plt.show()
    
    def generate_detailed_report(self):
        """生成详细的异常报告"""
        print("\n📋 生成详细异常报告...")
        
        # 执行所有分析
        distance_anomalies = self.analyze_keypoint_distances()
        position_anomalies = self.analyze_keypoint_positions()
        pc_anomalies = self.analyze_point_cloud_anomalies()
        
        # 合并所有异常信息
        all_anomalies = {}
        
        # 合并距离异常
        for anomaly in distance_anomalies:
            sample_id = anomaly['sample_id']
            if sample_id not in all_anomalies:
                all_anomalies[sample_id] = {
                    'sample_id': sample_id,
                    'sample_index': anomaly['sample_index'],
                    'anomalies': [],
                    'total_anomaly_count': 0
                }
            all_anomalies[sample_id]['anomalies'].extend(anomaly['anomalies'])
            all_anomalies[sample_id]['total_anomaly_count'] += anomaly['anomaly_count']
        
        # 合并位置异常
        for anomaly in position_anomalies:
            sample_id = anomaly['sample_id']
            if sample_id not in all_anomalies:
                all_anomalies[sample_id] = {
                    'sample_id': sample_id,
                    'sample_index': anomaly['sample_index'],
                    'anomalies': [],
                    'total_anomaly_count': 0
                }
            all_anomalies[sample_id]['anomalies'].extend(anomaly['anomalies'])
            all_anomalies[sample_id]['total_anomaly_count'] += anomaly['anomaly_count']
        
        # 合并点云异常
        for anomaly in pc_anomalies:
            sample_id = anomaly['sample_id']
            if sample_id not in all_anomalies:
                all_anomalies[sample_id] = {
                    'sample_id': sample_id,
                    'sample_index': anomaly['sample_index'],
                    'anomalies': [],
                    'total_anomaly_count': 0
                }
            all_anomalies[sample_id]['anomalies'].extend(anomaly['anomalies'])
            all_anomalies[sample_id]['total_anomaly_count'] += anomaly['anomaly_count']
        
        # 按异常严重程度排序
        sorted_anomalies = sorted(all_anomalies.values(), 
                                key=lambda x: x['total_anomaly_count'], reverse=True)
        
        # 生成详细报告
        detailed_report = {
            "analysis_timestamp": datetime.now().isoformat(),
            "total_samples": len(self.sample_ids),
            "anomalous_samples": len(all_anomalies),
            "anomaly_percentage": len(all_anomalies) / len(self.sample_ids) * 100,
            "anomaly_summary": {
                "distance_anomalies": len(distance_anomalies),
                "position_anomalies": len(position_anomalies),
                "point_cloud_anomalies": len(pc_anomalies)
            },
            "detailed_anomalies": sorted_anomalies
        }
        
        # 保存详细报告
        report_dir = Path("results/detailed_anomaly_reports")
        report_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = report_dir / f"detailed_anomaly_report_{timestamp}.json"
        
        # 转换numpy类型
        def convert_numpy_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            return obj
        
        detailed_report_serializable = convert_numpy_types(detailed_report)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_report_serializable, f, indent=2, ensure_ascii=False)
        
        # 生成CSV格式的简化报告
        csv_data = []
        for anomaly_info in sorted_anomalies:
            for anomaly in anomaly_info['anomalies']:
                csv_data.append({
                    'Sample_ID': anomaly_info['sample_id'],
                    'Sample_Index': anomaly_info['sample_index'],
                    'Anomaly_Type': anomaly['type'],
                    'Severity': anomaly.get('severity', 'medium'),
                    'Details': str(anomaly)
                })
        
        if csv_data:
            df = pd.DataFrame(csv_data)
            csv_file = report_dir / f"anomaly_summary_{timestamp}.csv"
            df.to_csv(csv_file, index=False, encoding='utf-8')
            print(f"💾 CSV报告已保存: {csv_file}")
        
        print(f"\n📊 详细异常分析完成!")
        print(f"📈 异常样本: {len(all_anomalies)}/{len(self.sample_ids)} ({len(all_anomalies)/len(self.sample_ids)*100:.1f}%)")
        print(f"💾 详细报告已保存: {report_file}")
        
        # 打印前10个最严重的异常样本
        print(f"\n🚨 前10个最严重的异常样本:")
        print("-" * 80)
        for i, anomaly_info in enumerate(sorted_anomalies[:10]):
            print(f"{i+1:2d}. Sample {anomaly_info['sample_id']} (Index: {anomaly_info['sample_index']})")
            print(f"    异常数量: {anomaly_info['total_anomaly_count']}")
            for j, anomaly in enumerate(anomaly_info['anomalies'][:3]):  # 显示前3个异常
                print(f"    - {anomaly['type']}")
                if 'keypoint_names' in anomaly:
                    print(f"      关键点: {anomaly['keypoint_names']}")
                if 'deviation_mm' in anomaly:
                    print(f"      偏差: {anomaly['deviation_mm']:.2f}mm")
            print()
        
        # 可视化最严重的异常样本
        if sorted_anomalies:
            self.visualize_specific_anomalies(sorted_anomalies, max_samples=3)
        
        return detailed_report, sorted_anomalies

def main():
    """主函数 - 执行详细异常分析"""
    
    print("🔍 详细异常样本分析")
    print("=" * 60)
    print("目标: 输出具体异常信息供人工检查")
    
    # 创建分析器
    analyzer = DetailedAnomalyAnalyzer()
    
    # 生成详细报告
    report, anomalies = analyzer.generate_detailed_report()
    
    print(f"\n🎯 人工检查建议:")
    print(f"1. 重点检查前10个异常样本的标注质量")
    print(f"2. 验证关键点间距离是否符合解剖学常识")
    print(f"3. 检查点云和关键点的坐标系是否一致")
    print(f"4. 确认异常样本是否需要重新标注或移除")
    
    return report, anomalies

if __name__ == "__main__":
    report, anomalies = main()
