#!/usr/bin/env python3
"""
统一骨盆57点系统 - 基于用户的关键洞察
Unified Pelvis 57-point system based on user's key insight

关键洞察：
- 12点数据集以F3为中心，F1、F2相对于F3定位
- F3是骨盆的中央部分，作为坐标系原点
- 必须将F1、F2、F3作为整体解剖结构处理
- 分别处理各部分会破坏空间关系和坐标对齐
"""

import numpy as np
import pandas as pd
import os
import glob
from pathlib import Path
from tqdm import tqdm
import json
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split

def load_unified_pelvis_from_stl_csv(sample_id, data_dir="/home/<USER>/pjc/GCN/data/Data"):
    """
    从STL和CSV加载统一的骨盆模型
    Load unified pelvis model from STL and CSV files
    """
    
    print(f"🔄 加载统一骨盆模型: {sample_id}")
    
    try:
        import open3d as o3d
        
        # 1. 加载57个关键点 (统一坐标系)
        csv_dir = Path(data_dir) / "annotations"
        csv_files = glob.glob(f"{csv_dir}/{sample_id}-Table-*.CSV")
        
        if not csv_files:
            return None, None, f"未找到CSV文件"
        
        # 优先使用XYZ坐标系
        xyz_files = [f for f in csv_files if 'XYZ' in f]
        csv_file = xyz_files[0] if xyz_files else csv_files[0]
        
        # 加载关键点
        try:
            df = pd.read_csv(csv_file, encoding='gbk')
        except:
            try:
                df = pd.read_csv(csv_file, encoding='utf-8')
            except:
                df = pd.read_csv(csv_file, encoding='latin-1')
        
        keypoints_57 = df[['X', 'Y', 'Z']].values.astype(np.float32)
        
        if len(keypoints_57) != 57:
            return None, None, f"关键点数量错误: {len(keypoints_57)}"
        
        # 2. 加载统一的点云 (F1+F2+F3组合)
        stl_dir = Path(data_dir) / "stl_models"
        combined_points = []
        
        # 按F3->F1->F2的顺序加载，保持与关键点的对应关系
        for region in ['F_3', 'F_1', 'F_2']:  # F3作为中心，F1、F2相对定位
            stl_file = stl_dir / f"{sample_id}-{region}.stl"
            
            if stl_file.exists():
                try:
                    mesh = o3d.io.read_triangle_mesh(str(stl_file))
                    
                    if len(mesh.vertices) > 0:
                        # 每个区域采样相同数量的点，保持平衡
                        pcd = mesh.sample_points_uniformly(number_of_points=16667)  # 50000/3
                        points = np.asarray(pcd.points)
                        combined_points.append(points)
                except Exception as e:
                    print(f"⚠️ {region} STL加载失败: {e}")
                    continue
        
        if len(combined_points) < 3:
            return None, None, f"STL文件不完整，只加载了{len(combined_points)}个区域"
        
        # 合并所有区域的点云，形成统一的骨盆模型
        unified_point_cloud = np.vstack(combined_points)
        
        # 确保点云大小为50000
        if len(unified_point_cloud) > 50000:
            indices = np.random.choice(len(unified_point_cloud), 50000, replace=False)
            unified_point_cloud = unified_point_cloud[indices]
        elif len(unified_point_cloud) < 50000:
            indices = np.random.choice(len(unified_point_cloud), 50000, replace=True)
            unified_point_cloud = unified_point_cloud[indices]
        
        print(f"✅ 统一骨盆模型加载成功:")
        print(f"   点云: {unified_point_cloud.shape}")
        print(f"   关键点: {keypoints_57.shape}")
        print(f"   点云范围: X={np.ptp(unified_point_cloud[:, 0]):.1f}, Y={np.ptp(unified_point_cloud[:, 1]):.1f}, Z={np.ptp(unified_point_cloud[:, 2]):.1f}")
        print(f"   关键点范围: X={np.ptp(keypoints_57[:, 0]):.1f}, Y={np.ptp(keypoints_57[:, 1]):.1f}, Z={np.ptp(keypoints_57[:, 2]):.1f}")
        
        return unified_point_cloud, keypoints_57, "成功"
        
    except Exception as e:
        return None, None, f"加载失败: {e}"

def apply_f3_centered_alignment(point_cloud, keypoints_57):
    """
    应用以F3为中心的对齐方法
    Apply F3-centered alignment method
    """
    
    # F3区域关键点 (索引38-56)
    f3_keypoints = keypoints_57[38:57]  # F3区域的19个关键点
    
    # 计算F3区域的中心作为坐标系原点
    f3_center = np.mean(f3_keypoints, axis=0)
    
    # 将整个模型以F3中心对齐
    aligned_point_cloud = point_cloud - f3_center
    aligned_keypoints_57 = keypoints_57 - f3_center
    
    print(f"🎯 F3中心对齐:")
    print(f"   F3中心偏移: {f3_center}")
    print(f"   对齐后F3中心: {np.mean(aligned_keypoints_57[38:57], axis=0)}")
    
    return aligned_point_cloud, aligned_keypoints_57, f3_center

def extract_12_core_keypoints(keypoints_57):
    """
    从57个关键点中提取12个核心关键点
    Extract 12 core keypoints from 57 keypoints
    """
    
    # 基于解剖学重要性的12点映射
    mapping_12_to_57 = {
        0: 0,   # F1-1 (髂前上棘)
        1: 1,   # F1-2
        2: 2,   # F1-3
        3: 12,  # F1-13
        4: 19,  # F2-1 (髂前上棘)
        5: 20,  # F2-2
        6: 21,  # F2-3
        7: 31,  # F2-13
        8: 38,  # F3-1
        9: 50,  # F3-13 (尾骨)
        10: 51, # F3-14
        11: 39, # F3-2 (骶骨岬)
    }
    
    keypoints_12 = np.zeros((12, 3))
    
    for i in range(12):
        original_idx = mapping_12_to_57[i]
        keypoints_12[i] = keypoints_57[original_idx]
    
    return keypoints_12

def build_unified_57_dataset(max_samples=None):
    """
    构建统一的57点数据集
    Build unified 57-point dataset
    """
    
    print("🎯 构建统一骨盆57点数据集")
    print("基于F3中心的整体解剖结构")
    print("=" * 80)
    
    data_dir = "/home/<USER>/pjc/GCN/data/Data"
    csv_dir = Path(data_dir) / "annotations"
    
    # 获取所有样本ID
    csv_files = glob.glob(f"{csv_dir}/*-Table-*.CSV")
    sample_ids = list(set([Path(f).stem.split('-')[0] for f in csv_files]))
    
    if max_samples:
        sample_ids = sample_ids[:max_samples]
    
    print(f"📊 找到 {len(sample_ids)} 个样本")
    
    all_point_clouds = []
    all_keypoints_57 = []
    all_keypoints_12 = []
    all_sample_ids = []
    processing_log = []
    
    for i, sample_id in enumerate(tqdm(sample_ids, desc="处理样本")):
        try:
            # 加载统一骨盆模型
            point_cloud, keypoints_57, msg = load_unified_pelvis_from_stl_csv(sample_id, data_dir)
            
            if point_cloud is None:
                print(f"⚠️ 样本 {sample_id}: {msg}")
                processing_log.append((sample_id, "失败", msg))
                continue
            
            # 应用F3中心对齐
            aligned_pc, aligned_kp57, f3_center = apply_f3_centered_alignment(point_cloud, keypoints_57)
            
            # 提取12个核心关键点
            keypoints_12 = extract_12_core_keypoints(aligned_kp57)
            
            # 数据质量检查
            if np.any(np.isnan(aligned_pc)) or np.any(np.isnan(aligned_kp57)):
                print(f"⚠️ 样本 {sample_id}: 数据包含NaN")
                processing_log.append((sample_id, "失败", "数据NaN"))
                continue
            
            # 检查关键点到点云的距离
            distances_to_surface = []
            for kp in aligned_kp57:
                dists = np.linalg.norm(aligned_pc - kp, axis=1)
                min_dist = np.min(dists)
                distances_to_surface.append(min_dist)
            
            avg_distance = np.mean(distances_to_surface)
            
            if avg_distance > 50:  # 如果平均距离太大，可能有问题
                print(f"⚠️ 样本 {sample_id}: 关键点距离点云较远 ({avg_distance:.1f}mm)")
                # 但仍然保留，因为可能是正常的解剖变异
            
            # 添加到数据集
            all_point_clouds.append(aligned_pc)
            all_keypoints_57.append(aligned_kp57)
            all_keypoints_12.append(keypoints_12)
            all_sample_ids.append(sample_id)
            processing_log.append((sample_id, "成功", f"距离: {avg_distance:.1f}mm"))
            
            if i == 0:
                print(f"\n📋 样本 {sample_id} 详细信息:")
                print(f"   点云形状: {aligned_pc.shape}")
                print(f"   57关键点形状: {aligned_kp57.shape}")
                print(f"   12关键点形状: {keypoints_12.shape}")
                print(f"   平均表面距离: {avg_distance:.2f}mm")
                print(f"   F3中心偏移: {f3_center}")
            
        except Exception as e:
            print(f"❌ 样本 {sample_id}: 处理失败 - {e}")
            processing_log.append((sample_id, "失败", str(e)))
            continue
    
    if not all_point_clouds:
        print("❌ 没有成功处理任何样本")
        return None
    
    # 转换为numpy数组
    all_point_clouds = np.array(all_point_clouds, dtype=np.float32)
    all_keypoints_57 = np.array(all_keypoints_57, dtype=np.float32)
    all_keypoints_12 = np.array(all_keypoints_12, dtype=np.float32)
    
    print(f"\n✅ 统一骨盆57点数据集构建完成:")
    print(f"   成功样本: {len(all_sample_ids)}")
    print(f"   失败样本: {len([log for log in processing_log if log[1] == '失败'])}")
    print(f"   点云形状: {all_point_clouds.shape}")
    print(f"   57关键点形状: {all_keypoints_57.shape}")
    print(f"   12关键点形状: {all_keypoints_12.shape}")
    
    return {
        'point_clouds': all_point_clouds,
        'keypoints_57': all_keypoints_57,
        'keypoints_12': all_keypoints_12,
        'sample_ids': all_sample_ids,
        'processing_log': processing_log,
        'method': 'unified_pelvis_f3_centered',
        'description': '基于F3中心的统一骨盆模型，保持F1、F2、F3的整体解剖关系'
    }

def save_unified_dataset(dataset_dict):
    """保存统一数据集"""
    
    print(f"\n💾 保存统一骨盆57点数据集...")
    
    np.savez_compressed('unified_pelvis_57_dataset.npz',
                       point_clouds=dataset_dict['point_clouds'],
                       keypoints_57=dataset_dict['keypoints_57'],
                       keypoints_12=dataset_dict['keypoints_12'],
                       sample_ids=dataset_dict['sample_ids'],
                       processing_log=dataset_dict['processing_log'],
                       method=dataset_dict['method'],
                       description=dataset_dict['description'])
    
    # 保存详细信息
    info = {
        'dataset_name': 'unified_pelvis_57_dataset',
        'method': dataset_dict['method'],
        'description': dataset_dict['description'],
        'samples': len(dataset_dict['sample_ids']),
        'point_cloud_shape': dataset_dict['point_clouds'].shape,
        'keypoints_57_shape': dataset_dict['keypoints_57'].shape,
        'keypoints_12_shape': dataset_dict['keypoints_12'].shape,
        'coordinate_system': 'f3_centered',
        'anatomical_structure': 'unified_pelvis_f1_f2_f3',
        'key_insight': '以F3为中心，保持F1、F2、F3的整体解剖关系'
    }
    
    with open('unified_pelvis_57_info.json', 'w', encoding='utf-8') as f:
        json.dump(info, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"✅ 统一数据集已保存:")
    print(f"   - unified_pelvis_57_dataset.npz (数据集)")
    print(f"   - unified_pelvis_57_info.json (详细信息)")

def main():
    """主函数"""
    
    print("🎯 统一骨盆57点系统")
    print("基于用户洞察：F3中心，F1、F2相对定位")
    print("=" * 80)
    
    # 构建统一数据集
    dataset_dict = build_unified_57_dataset()  # 处理全部样本
    
    if dataset_dict is not None:
        # 保存数据集
        save_unified_dataset(dataset_dict)
        
        print(f"\n🎉 统一骨盆57点数据集构建成功！")
        print(f"💡 关键改进:")
        print(f"   ✅ F1、F2、F3作为整体解剖结构")
        print(f"   ✅ F3中心对齐，保持空间关系")
        print(f"   ✅ 统一的坐标系，避免分离处理")
        print(f"   ✅ 保持解剖学的完整性")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 在统一数据上训练57点模型")
        print(f"   2. 验证坐标对齐质量")
        print(f"   3. 对比分离处理的性能差异")
        
    else:
        print("❌ 统一数据集构建失败")

if __name__ == "__main__":
    main()
