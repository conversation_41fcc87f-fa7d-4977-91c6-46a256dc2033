#!/usr/bin/env python3
"""
运行原始模型（修正路径）
Run Original Model with Correct Path
修正数据集路径问题，直接运行原始训练代码
"""

import sys
import os
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

# 添加原始代码路径
sys.path.insert(0, os.path.abspath("archive/old_scripts"))

# 导入原始模块
from ensemble_double_softmax_exact import ExactEnsembleDoubleSoftMaxPointNet

class ExactLoss(nn.Module):
    """精确复制的损失函数"""
    
    def __init__(self, alpha=0.8, beta=0.2):
        super(ExactLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, pred, target):
        mse_loss = F.mse_loss(pred, target)
        smooth_l1_loss = F.smooth_l1_loss(pred, target)
        total_loss = self.alpha * mse_loss + self.beta * smooth_l1_loss
        return total_loss

class HistoricalDataset(Dataset):
    """历史数据集"""
    
    def __init__(self, point_clouds, keypoints):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def calculate_metrics(predictions, targets):
    """计算评估指标"""
    
    distances = np.linalg.norm(predictions - targets, axis=2)
    mean_distance = np.mean(distances)
    
    within_5mm = np.mean(distances < 5.0) * 100
    within_7mm = np.mean(distances < 7.0) * 100
    
    return {
        'mean_distance': mean_distance,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def train_original_exact_model(seed=123):
    """训练原始精确模型"""
    
    print(f"🚀 **原始精确模型训练 (种子: {seed})**")
    print(f"🎯 **目标**: 复现5.371mm性能")
    print("=" * 80)
    
    set_seed(seed)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 加载历史数据集（修正路径）
    dataset_path = "archive/old_experiments/f3_reduced_12kp_stable.npz"
    
    print(f"📊 加载历史数据集: {dataset_path}")
    data = np.load(dataset_path, allow_pickle=True)
    
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    sample_ids = data['sample_ids']
    
    print(f"✅ 数据集加载成功:")
    print(f"   样本数: {len(sample_ids)}")
    print(f"   点云形状: {point_clouds.shape}")
    print(f"   关键点形状: {keypoints.shape}")
    
    # 精确复制的数据划分（使用原始的测试样本）
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    # 创建训练/验证划分
    train_indices = []
    val_indices = []
    
    for i, sample_id in enumerate(sample_ids):
        if str(sample_id) in test_samples:
            val_indices.append(i)
        else:
            train_indices.append(i)
    
    print(f"📋 数据划分:")
    print(f"   训练样本: {len(train_indices)}")
    print(f"   验证样本: {len(val_indices)}")
    
    # 创建数据集
    train_dataset = HistoricalDataset(point_clouds[train_indices], keypoints[train_indices])
    val_dataset = HistoricalDataset(point_clouds[val_indices], keypoints[val_indices])
    
    # 数据加载器 - 精确复制原始配置，但避免批次大小问题
    batch_size = 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0, drop_last=True)
    
    # 创建原始模型
    model = ExactEnsembleDoubleSoftMaxPointNet(num_keypoints=12, dropout_rate=0.3, num_ensembles=3).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 模型参数: {total_params:,}")
    
    # 精确复制的训练配置
    criterion = ExactLoss(alpha=0.8, beta=0.2)
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=12, min_lr=1e-6
    )
    
    num_epochs = 100
    best_val_error = float('inf')
    patience = 20
    patience_counter = 0
    history = []
    min_delta = 0.005
    
    print(f"🎯 训练配置:")
    print(f"   集成模块: 3个双Softmax")
    print(f"   损失函数: MSE(0.8) + SmoothL1(0.2)")
    print(f"   学习率: 0.0008")
    print(f"   批次大小: {batch_size}")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 40)
        
        # 训练
        model.train()
        train_loss = 0.0
        train_predictions = []
        train_targets = []
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            
            # 收集预测结果用于计算指标
            train_predictions.append(predicted.detach().cpu().numpy())
            train_targets.append(batch_kp.detach().cpu().numpy())
        
        # 计算训练指标
        train_predictions = np.concatenate(train_predictions, axis=0)
        train_targets = np.concatenate(train_targets, axis=0)
        train_metrics = calculate_metrics(train_predictions, train_targets)
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_predictions = []
        val_targets = []
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                val_loss += loss.item()
                
                val_predictions.append(predicted.cpu().numpy())
                val_targets.append(batch_kp.cpu().numpy())
        
        # 计算验证指标
        val_predictions = np.concatenate(val_predictions, axis=0)
        val_targets = np.concatenate(val_targets, axis=0)
        val_metrics = calculate_metrics(val_predictions, val_targets)
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        
        # 学习率调度
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录历史
        epoch_info = {
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'lr': current_lr
        }
        history.append(epoch_info)
        
        # 打印进度
        print(f"训练损失: {train_loss:.6f}, 验证损失: {val_loss:.6f}")
        print(f"训练误差: {train_metrics['mean_distance']:.3f}mm, 验证误差: {val_metrics['mean_distance']:.3f}mm")
        print(f"验证<5mm: {val_metrics['within_5mm_percent']:.1f}%, <7mm: {val_metrics['within_7mm_percent']:.1f}%")
        print(f"学习率: {current_lr:.2e}")
        
        # 早停检查
        if val_metrics['mean_distance'] < best_val_error - min_delta:
            best_val_error = val_metrics['mean_distance']
            patience_counter = 0
            
            # 保存最佳模型
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics,
                'history': history
            }, f'best_original_reproduction_seed{seed}.pth')
            
            print(f"✅ 新的最佳验证误差: {best_val_error:.3f}mm")
        else:
            patience_counter += 1
        
        if patience_counter >= patience:
            print(f"⏹️ 早停触发，在第 {epoch+1} 轮停止训练")
            break
        
        # 内存清理
        if epoch % 10 == 0:
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
    
    training_time = time.time() - start_time
    
    print(f"\n🎯 训练完成!")
    print(f"   最佳验证误差: {best_val_error:.3f}mm")
    print(f"   历史目标: 5.371mm")
    print(f"   差距: {best_val_error - 5.371:.3f}mm")
    print(f"   训练时间: {training_time/60:.1f}分钟")
    
    # 保存最终结果
    final_results = {
        'seed': seed,
        'best_val_error': best_val_error,
        'target_error': 5.371,
        'gap': best_val_error - 5.371,
        'training_time_minutes': training_time / 60,
        'history': history,
        'model_config': {
            'num_keypoints': 12,
            'dropout_rate': 0.3,
            'num_ensembles': 3,
            'total_params': total_params
        }
    }
    
    with open(f'original_reproduction_seed{seed}_results.json', 'w') as f:
        json.dump(final_results, f, indent=2, default=str)
    
    return best_val_error, history

def main():
    """主函数"""
    
    print("🎯 运行原始模型（修正路径）")
    print("使用正确的数据集路径运行原始训练代码")
    print("=" * 80)
    
    # 运行种子123（历史最佳）
    try:
        best_error, history = train_original_exact_model(seed=123)
        
        print(f"\n📊 最终结果:")
        print(f"   种子123最佳误差: {best_error:.3f}mm")
        print(f"   历史目标: 5.371mm")
        print(f"   差距: {best_error - 5.371:.3f}mm")
        
        if best_error < 6.0:
            print(f"🎉 成功！接近历史性能！")
            print(f"💡 证明了原始代码的有效性")
        elif best_error < 8.0:
            print(f"✅ 良好！显著改进")
        else:
            print(f"⚠️ 仍需优化")
        
        print(f"\n💡 现在我们有了:")
        print(f"   1. ✅ 原始代码可以运行")
        print(f"   2. ✅ 原始模型可以加载")
        print(f"   3. ✅ 原始数据集可以使用")
        print(f"   4. 📊 实际性能基准")
        
        print(f"\n🔍 下一步分析:")
        print(f"   1. 对比原始实现和我们的实现")
        print(f"   2. 找出关键差异")
        print(f"   3. 理解为什么原始代码能达到更好性能")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
