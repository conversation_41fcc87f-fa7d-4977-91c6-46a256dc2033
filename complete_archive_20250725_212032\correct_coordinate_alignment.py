#!/usr/bin/env python3
"""
正确的坐标系对齐
Correct coordinate system alignment based on keypoint center alignment
"""

import numpy as np
import json

def apply_keypoint_center_alignment(point_clouds, keypoints_57, keypoints_12):
    """应用关键点中心对齐 - 基于已验证的12点数据集方法"""
    
    print("🔧 应用关键点中心对齐...")
    print("基于已验证的12点数据集对齐方法")
    
    aligned_pc = []
    aligned_57kp = []
    aligned_12kp = []
    alignment_info = []
    
    for i in range(len(point_clouds)):
        pc = point_clouds[i].copy()
        kp_57 = keypoints_57[i].copy()
        kp_12 = keypoints_12[i].copy()
        
        # 计算关键点中心 - 使用12个核心关键点
        kp_center = np.mean(kp_12, axis=0)
        
        # 将坐标系原点移动到关键点中心
        pc_aligned = pc - kp_center
        kp_57_aligned = kp_57 - kp_center
        kp_12_aligned = kp_12 - kp_center
        
        aligned_pc.append(pc_aligned)
        aligned_57kp.append(kp_57_aligned)
        aligned_12kp.append(kp_12_aligned)
        
        # 记录对齐信息
        alignment_info.append({
            'sample_index': i,
            'kp_center_offset': kp_center.tolist(),
            'alignment_method': 'keypoint_center'
        })
        
        if i == 0:
            print(f"   样本 {i} 对齐:")
            print(f"     关键点中心: ({kp_center})")
            print(f"     对齐后点云中心: ({np.mean(pc_aligned, axis=0)})")
            print(f"     对齐后12关键点中心: ({np.mean(kp_12_aligned, axis=0)})")
    
    aligned_pc = np.array(aligned_pc)
    aligned_57kp = np.array(aligned_57kp)
    aligned_12kp = np.array(aligned_12kp)
    
    print(f"\n✅ 关键点中心对齐完成:")
    print(f"   点云: {aligned_pc.shape}")
    print(f"   57关键点: {aligned_57kp.shape}")
    print(f"   12关键点: {aligned_12kp.shape}")
    
    return aligned_pc, aligned_57kp, aligned_12kp, alignment_info

def validate_alignment_quality(aligned_pc, aligned_57kp, aligned_12kp):
    """验证对齐质量"""
    
    print("🔍 验证对齐质量...")
    
    # 检查关键点到点云表面的距离
    sample_idx = 0
    pc_sample = aligned_pc[sample_idx]
    kp_sample = aligned_57kp[sample_idx]
    
    distances_to_surface = []
    for kp in kp_sample:
        dists = np.linalg.norm(pc_sample - kp, axis=1)
        min_dist = np.min(dists)
        distances_to_surface.append(min_dist)
    
    avg_distance = np.mean(distances_to_surface)
    max_distance = np.max(distances_to_surface)
    
    print(f"   关键点到点云表面距离:")
    print(f"     平均距离: {avg_distance:.2f}mm")
    print(f"     最大距离: {max_distance:.2f}mm")
    
    # 检查数据中心化
    pc_centers = [np.mean(pc, axis=0) for pc in aligned_pc]
    kp_12_centers = [np.mean(kp, axis=0) for kp in aligned_12kp]
    
    avg_pc_center = np.mean(pc_centers, axis=0)
    avg_kp_center = np.mean(kp_12_centers, axis=0)
    
    print(f"   数据中心化检查:")
    print(f"     平均点云中心: ({avg_pc_center})")
    print(f"     平均12关键点中心: ({avg_kp_center})")
    print(f"     中心偏移: {np.linalg.norm(avg_kp_center):.4f}mm")
    
    # 检查数据范围
    pc_range = np.ptp(aligned_pc, axis=(0,1))
    kp_57_range = np.ptp(aligned_57kp, axis=(0,1))
    kp_12_range = np.ptp(aligned_12kp, axis=(0,1))
    
    print(f"   数据范围:")
    print(f"     点云: X={pc_range[0]:.1f}, Y={pc_range[1]:.1f}, Z={pc_range[2]:.1f}")
    print(f"     57关键点: X={kp_57_range[0]:.1f}, Y={kp_57_range[1]:.1f}, Z={kp_57_range[2]:.1f}")
    print(f"     12关键点: X={kp_12_range[0]:.1f}, Y={kp_12_range[1]:.1f}, Z={kp_12_range[2]:.1f}")
    
    # 与原始12点数据对比
    male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz', allow_pickle=True)
    orig_pc = male_data['point_clouds']
    orig_12kp = male_data['keypoints']
    
    orig_pc_range = np.ptp(orig_pc, axis=(0,1))
    orig_12kp_range = np.ptp(orig_12kp, axis=(0,1))
    
    print(f"   与原始12点数据对比:")
    print(f"     原始点云范围: X={orig_pc_range[0]:.1f}, Y={orig_pc_range[1]:.1f}, Z={orig_pc_range[2]:.1f}")
    print(f"     原始12关键点范围: X={orig_12kp_range[0]:.1f}, Y={orig_12kp_range[1]:.1f}, Z={orig_12kp_range[2]:.1f}")
    
    # 判断对齐是否成功
    success_criteria = [
        avg_distance < 20,  # 关键点到表面距离合理
        np.linalg.norm(avg_kp_center) < 1,  # 12关键点已中心化
        abs(pc_range[0] - orig_pc_range[0]) < 200,  # 数据范围相似
        abs(kp_12_range[0] - orig_12kp_range[0]) < 50  # 12关键点范围相似
    ]
    
    if all(success_criteria):
        print(f"✅ 对齐成功！数据质量良好")
        return True
    else:
        print(f"⚠️ 对齐需要进一步调整")
        print(f"   失败的标准: {[i for i, x in enumerate(success_criteria) if not x]}")
        return False

def compare_with_original_12point_data(aligned_pc, aligned_12kp):
    """与原始12点数据详细对比"""
    
    print(f"\n📊 与原始12点数据详细对比...")
    
    # 加载原始12点数据
    male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz', allow_pickle=True)
    orig_pc = male_data['point_clouds']
    orig_12kp = male_data['keypoints']
    orig_ids = male_data['sample_ids']
    
    # 加载真实数据的样本ID
    real_data = np.load('real_57_dataset_from_existing.npz', allow_pickle=True)
    real_ids = real_data['sample_ids']
    
    # 找到共同样本
    common_samples = []
    for i, real_id in enumerate(real_ids):
        for j, orig_id in enumerate(orig_ids):
            if real_id == orig_id:
                common_samples.append((i, j, real_id))
                break
    
    print(f"   共同样本数: {len(common_samples)}")
    
    if common_samples:
        # 对比前3个共同样本
        for k, (real_idx, orig_idx, sample_id) in enumerate(common_samples[:3]):
            print(f"\n   样本 {sample_id}:")
            
            # 点云统计对比
            real_pc_stats = {
                'center': np.mean(aligned_pc[real_idx], axis=0),
                'std': np.std(aligned_pc[real_idx], axis=0),
                'range': np.ptp(aligned_pc[real_idx], axis=0)
            }
            
            orig_pc_stats = {
                'center': np.mean(orig_pc[orig_idx], axis=0),
                'std': np.std(orig_pc[orig_idx], axis=0),
                'range': np.ptp(orig_pc[orig_idx], axis=0)
            }
            
            print(f"     点云中心: 对齐后{real_pc_stats['center']} vs 原始{orig_pc_stats['center']}")
            print(f"     点云标准差: 对齐后{real_pc_stats['std']} vs 原始{orig_pc_stats['std']}")
            
            # 12关键点对比
            real_kp_center = np.mean(aligned_12kp[real_idx], axis=0)
            orig_kp_center = np.mean(orig_12kp[orig_idx], axis=0)
            
            kp_distance = np.linalg.norm(real_kp_center - orig_kp_center)
            
            print(f"     12关键点中心: 对齐后{real_kp_center} vs 原始{orig_kp_center}")
            print(f"     12关键点中心距离: {kp_distance:.4f}mm")
    
    # 整体统计对比
    print(f"\n   整体统计对比:")
    
    # 对齐后数据统计
    aligned_pc_global_center = np.mean(aligned_pc, axis=(0,1))
    aligned_pc_global_std = np.std(aligned_pc, axis=(0,1))
    aligned_12kp_global_center = np.mean(aligned_12kp, axis=(0,1))
    aligned_12kp_global_std = np.std(aligned_12kp, axis=(0,1))
    
    # 原始数据统计
    orig_pc_global_center = np.mean(orig_pc, axis=(0,1))
    orig_pc_global_std = np.std(orig_pc, axis=(0,1))
    orig_12kp_global_center = np.mean(orig_12kp, axis=(0,1))
    orig_12kp_global_std = np.std(orig_12kp, axis=(0,1))
    
    print(f"     点云全局中心: 对齐后{aligned_pc_global_center} vs 原始{orig_pc_global_center}")
    print(f"     点云全局标准差: 对齐后{aligned_pc_global_std} vs 原始{orig_pc_global_std}")
    print(f"     12关键点全局中心: 对齐后{aligned_12kp_global_center} vs 原始{orig_12kp_global_center}")
    print(f"     12关键点全局标准差: 对齐后{aligned_12kp_global_std} vs 原始{orig_12kp_global_std}")

def save_aligned_data(aligned_pc, aligned_57kp, aligned_12kp, sample_ids, genders, alignment_info):
    """保存对齐后的数据"""
    
    print("💾 保存对齐后的数据...")
    
    np.savez_compressed('aligned_57_dataset.npz',
                       point_clouds=aligned_pc,
                       keypoints_57=aligned_57kp,
                       keypoints_12=aligned_12kp,
                       sample_ids=sample_ids,
                       genders=genders,
                       alignment_info=alignment_info)
    
    # 保存对齐信息
    alignment_summary = {
        'method': 'keypoint_center_alignment',
        'description': '基于12个核心关键点中心的坐标系对齐',
        'samples': len(sample_ids),
        'point_cloud_shape': aligned_pc.shape,
        'keypoints_57_shape': aligned_57kp.shape,
        'keypoints_12_shape': aligned_12kp.shape,
        'coordinate_system': 'keypoint_centered',
        'alignment_quality': 'verified'
    }
    
    with open('alignment_info.json', 'w') as f:
        json.dump(alignment_summary, f, indent=2, default=str)
    
    print(f"✅ 对齐数据已保存:")
    print(f"   - aligned_57_dataset.npz (对齐数据)")
    print(f"   - alignment_info.json (对齐信息)")

def main():
    """主函数"""
    
    print("🎯 正确的坐标系对齐")
    print("基于关键点中心对齐方法")
    print("=" * 80)
    
    # 加载真实57点数据
    print(f"📊 加载真实57点数据...")
    real_data = np.load('real_57_dataset_from_existing.npz', allow_pickle=True)
    point_clouds = real_data['point_clouds']
    keypoints_57 = real_data['keypoints_57']
    keypoints_12 = real_data['keypoints_12']
    sample_ids = real_data['sample_ids']
    genders = real_data['genders']
    
    print(f"✅ 原始数据加载完成: {len(sample_ids)} 个样本")
    
    # 应用关键点中心对齐
    aligned_pc, aligned_57kp, aligned_12kp, alignment_info = apply_keypoint_center_alignment(
        point_clouds, keypoints_57, keypoints_12
    )
    
    # 验证对齐质量
    success = validate_alignment_quality(aligned_pc, aligned_57kp, aligned_12kp)
    
    # 与原始12点数据详细对比
    compare_with_original_12point_data(aligned_pc, aligned_12kp)
    
    if success:
        # 保存对齐后的数据
        save_aligned_data(aligned_pc, aligned_57kp, aligned_12kp, sample_ids, genders, alignment_info)
        
        print(f"\n🎉 坐标系对齐成功！")
        print(f"💡 关键改进:")
        print(f"   ✅ 使用与12点数据相同的对齐方法")
        print(f"   ✅ 关键点中心对齐，保持空间关系")
        print(f"   ✅ 数据范围与原始12点数据一致")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 在对齐数据上重新训练57点模型")
        print(f"   2. 预期性能大幅提升")
        print(f"   3. 验证真实数据的优势")
        
    else:
        print(f"\n❌ 对齐仍需调整")
        print(f"💡 建议:")
        print(f"   1. 检查12点映射关系")
        print(f"   2. 分析原始12点数据的详细预处理")
        print(f"   3. 考虑其他对齐策略")

if __name__ == "__main__":
    main()
