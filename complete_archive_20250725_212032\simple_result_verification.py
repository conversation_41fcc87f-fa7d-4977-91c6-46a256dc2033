#!/usr/bin/env python3
"""
简单的结果验证 - 检查1.28mm误差的真实性
Simple Result Verification - Check the Reality of 1.28mm Error
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from pathlib import Path
from sklearn.model_selection import train_test_split

class SimpleKeypointNet(nn.Module):
    """简单的关键点检测网络"""
    
    def __init__(self):
        super().__init__()
        self.feature_extractor = nn.Sequential(
            nn.Linear(3, 64),
            nn.ReLU(),
            nn.Linear(64, 128),
            nn.ReLU(),
            nn.Linear(128, 128),
            nn.ReLU()
        )
        
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(128, 256),
            nn.ReLU(),
            nn.Linear(256, 19*3)
        )
        
    def forward(self, point_cloud):
        batch_size = point_cloud.size(0)
        pc_flat = point_cloud.view(-1, 3)
        features = self.feature_extractor(pc_flat)
        features = features.view(batch_size, -1, features.size(-1))
        global_feature, _ = torch.max(features, dim=1)
        keypoints = self.keypoint_regressor(global_feature)
        return keypoints.view(batch_size, 19, 3)

def load_and_compare_data():
    """加载并对比原始和预处理数据"""
    print("📦 加载数据进行对比...")
    
    # 加载原始数据
    original_data = np.load('data/raw/high_quality_f3_dataset.npz', allow_pickle=True)
    orig_pcs = original_data['point_clouds']
    orig_kps = original_data['keypoints']
    
    # 加载预处理数据
    processed_files = list(Path("data/processed").glob("lightweight_preprocessed_*.npz"))
    latest_file = max(processed_files, key=lambda x: x.stat().st_mtime)
    processed_data = np.load(str(latest_file), allow_pickle=True)
    proc_pcs = processed_data['point_clouds']
    proc_kps = processed_data['keypoints']
    
    print(f"✅ 数据加载完成")
    print(f"原始数据:")
    print(f"  点云范围: [{np.min([np.min(pc) for pc in orig_pcs]):.2f}, {np.max([np.max(pc) for pc in orig_pcs]):.2f}]")
    print(f"  关键点范围: [{np.min(orig_kps):.2f}, {np.max(orig_kps):.2f}]")
    
    print(f"预处理数据:")
    print(f"  点云范围: [{np.min(proc_pcs):.2f}, {np.max(proc_pcs):.2f}]")
    print(f"  关键点范围: [{np.min(proc_kps):.2f}, {np.max(proc_kps):.2f}]")
    
    return orig_pcs, orig_kps, proc_pcs, proc_kps

def prepare_uniform_data(point_clouds, keypoints, target_points=2048):
    """准备统一格式的数据"""
    uniform_pcs = []
    uniform_kps = []
    
    for i, (pc, kp) in enumerate(zip(point_clouds, keypoints)):
        # 转换为numpy数组
        pc_array = np.array(pc, dtype=np.float32)
        kp_array = np.array(kp, dtype=np.float32)
        
        # 下采样或上采样到目标点数
        if len(pc_array) > target_points:
            indices = np.random.choice(len(pc_array), target_points, replace=False)
            pc_uniform = pc_array[indices]
        elif len(pc_array) < target_points:
            indices = np.random.choice(len(pc_array), target_points, replace=True)
            pc_uniform = pc_array[indices]
        else:
            pc_uniform = pc_array
        
        uniform_pcs.append(pc_uniform)
        uniform_kps.append(kp_array)
    
    return np.array(uniform_pcs), np.array(uniform_kps)

def train_and_test_model(point_clouds, keypoints, data_type="unknown"):
    """训练并测试模型"""
    print(f"\n🎯 训练模型 ({data_type})")
    
    # 数据划分
    indices = np.arange(len(point_clouds))
    train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
    train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
    
    # 创建模型
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    model = SimpleKeypointNet().to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=0.002)
    criterion = nn.MSELoss()
    
    # 训练
    k_shot = 15
    epochs = 30
    
    for epoch in range(epochs):
        model.train()
        
        # 采样训练数据
        selected_indices = np.random.choice(train_indices, min(k_shot, len(train_indices)), replace=False)
        train_pcs = point_clouds[selected_indices]
        train_kps = keypoints[selected_indices]
        
        # 转换为tensor
        train_pcs_tensor = torch.FloatTensor(train_pcs).to(device)
        train_kps_tensor = torch.FloatTensor(train_kps).to(device)
        
        # 训练步骤
        optimizer.zero_grad()
        pred_kps = model(train_pcs_tensor)
        loss = criterion(pred_kps, train_kps_tensor)
        loss.backward()
        optimizer.step()
        
        if epoch % 10 == 0:
            print(f"Epoch {epoch}: Loss={loss:.4f}")
        
        # 清理内存
        del train_pcs_tensor, train_kps_tensor, pred_kps, loss
        torch.cuda.empty_cache()
    
    # 测试
    model.eval()
    test_errors = []
    test_results = []
    
    with torch.no_grad():
        test_pcs = point_clouds[test_indices]
        test_kps = keypoints[test_indices]
        
        for i, (pc, kp) in enumerate(zip(test_pcs, test_kps)):
            pc_tensor = torch.FloatTensor(pc).unsqueeze(0).to(device)
            kp_tensor = torch.FloatTensor(kp).unsqueeze(0).to(device)
            
            pred_kp = model(pc_tensor).cpu().numpy()[0]
            true_kp = kp
            
            # 计算误差
            errors = np.linalg.norm(pred_kp - true_kp, axis=1)
            mean_error = np.mean(errors)
            test_errors.append(mean_error)
            
            if i < 3:  # 保存前3个样本的详细结果
                test_results.append({
                    'predicted': pred_kp,
                    'true': true_kp,
                    'errors': errors,
                    'mean_error': mean_error
                })
            
            print(f"测试样本 {i+1}: 平均误差 = {mean_error:.3f}")
            
            # 清理内存
            del pc_tensor, kp_tensor
            torch.cuda.empty_cache()
    
    overall_error = np.mean(test_errors)
    print(f"✅ {data_type} 总体平均误差: {overall_error:.3f}")
    
    return overall_error, test_results, test_errors

def visualize_comparison(orig_results, proc_results):
    """可视化对比结果"""
    print("\n📊 可视化对比结果...")
    
    viz_dir = Path("results/simple_verification")
    viz_dir.mkdir(parents=True, exist_ok=True)
    
    for i, (orig_result, proc_result) in enumerate(zip(orig_results, proc_results)):
        fig = plt.figure(figsize=(20, 10))
        
        # 原始数据结果
        ax1 = fig.add_subplot(231, projection='3d')
        orig_true = orig_result['true']
        orig_pred = orig_result['predicted']
        
        ax1.scatter(orig_true[:, 0], orig_true[:, 1], orig_true[:, 2], 
                   c='red', s=100, label='真实', marker='o', alpha=0.8)
        ax1.scatter(orig_pred[:, 0], orig_pred[:, 1], orig_pred[:, 2], 
                   c='blue', s=100, label='预测', marker='^', alpha=0.8)
        
        for j in range(len(orig_true)):
            ax1.plot([orig_true[j, 0], orig_pred[j, 0]], 
                    [orig_true[j, 1], orig_pred[j, 1]], 
                    [orig_true[j, 2], orig_pred[j, 2]], 'k--', alpha=0.5)
        
        ax1.set_title(f'原始数据 - 样本{i+1}\n误差: {orig_result["mean_error"]:.3f}')
        ax1.legend()
        
        # 预处理数据结果
        ax2 = fig.add_subplot(232, projection='3d')
        proc_true = proc_result['true']
        proc_pred = proc_result['predicted']
        
        ax2.scatter(proc_true[:, 0], proc_true[:, 1], proc_true[:, 2], 
                   c='red', s=100, label='真实', marker='o', alpha=0.8)
        ax2.scatter(proc_pred[:, 0], proc_pred[:, 1], proc_pred[:, 2], 
                   c='blue', s=100, label='预测', marker='^', alpha=0.8)
        
        for j in range(len(proc_true)):
            ax2.plot([proc_true[j, 0], proc_pred[j, 0]], 
                    [proc_true[j, 1], proc_pred[j, 1]], 
                    [proc_true[j, 2], proc_pred[j, 2]], 'k--', alpha=0.5)
        
        ax2.set_title(f'预处理数据 - 样本{i+1}\n误差: {proc_result["mean_error"]:.3f}')
        ax2.legend()
        
        # 误差对比
        ax3 = fig.add_subplot(233)
        x = np.arange(19)
        width = 0.35
        
        ax3.bar(x - width/2, orig_result['errors'], width, label='原始数据', alpha=0.7)
        ax3.bar(x + width/2, proc_result['errors'], width, label='预处理数据', alpha=0.7)
        
        ax3.set_title(f'误差对比 - 样本{i+1}')
        ax3.set_xlabel('关键点索引')
        ax3.set_ylabel('误差')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 数据范围对比
        ax4 = fig.add_subplot(234)
        range_text = f'数据范围对比:\n\n'
        range_text += f'原始数据范围:\n'
        range_text += f'X: [{np.min(orig_true[:, 0]):.1f}, {np.max(orig_true[:, 0]):.1f}]\n'
        range_text += f'Y: [{np.min(orig_true[:, 1]):.1f}, {np.max(orig_true[:, 1]):.1f}]\n'
        range_text += f'Z: [{np.min(orig_true[:, 2]):.1f}, {np.max(orig_true[:, 2]):.1f}]\n\n'
        
        range_text += f'预处理数据范围:\n'
        range_text += f'X: [{np.min(proc_true[:, 0]):.3f}, {np.max(proc_true[:, 0]):.3f}]\n'
        range_text += f'Y: [{np.min(proc_true[:, 1]):.3f}, {np.max(proc_true[:, 1]):.3f}]\n'
        range_text += f'Z: [{np.min(proc_true[:, 2]):.3f}, {np.max(proc_true[:, 2]):.3f}]'
        
        ax4.text(0.05, 0.95, range_text, transform=ax4.transAxes, 
                fontsize=10, verticalalignment='top', fontfamily='monospace')
        ax4.set_xlim(0, 1)
        ax4.set_ylim(0, 1)
        ax4.set_title('数据范围')
        ax4.axis('off')
        
        # 误差统计
        ax5 = fig.add_subplot(235)
        stats_text = f'误差统计对比:\n\n'
        stats_text += f'原始数据:\n'
        stats_text += f'  平均: {orig_result["mean_error"]:.3f}\n'
        stats_text += f'  最大: {np.max(orig_result["errors"]):.3f}\n'
        stats_text += f'  最小: {np.min(orig_result["errors"]):.3f}\n'
        stats_text += f'  标准差: {np.std(orig_result["errors"]):.3f}\n\n'
        
        stats_text += f'预处理数据:\n'
        stats_text += f'  平均: {proc_result["mean_error"]:.3f}\n'
        stats_text += f'  最大: {np.max(proc_result["errors"]):.3f}\n'
        stats_text += f'  最小: {np.min(proc_result["errors"]):.3f}\n'
        stats_text += f'  标准差: {np.std(proc_result["errors"]):.3f}'
        
        ax5.text(0.05, 0.95, stats_text, transform=ax5.transAxes, 
                fontsize=10, verticalalignment='top', fontfamily='monospace')
        ax5.set_xlim(0, 1)
        ax5.set_ylim(0, 1)
        ax5.set_title('误差统计')
        ax5.axis('off')
        
        # 改进分析
        ax6 = fig.add_subplot(236)
        improvement = (orig_result["mean_error"] - proc_result["mean_error"]) / orig_result["mean_error"] * 100
        
        analysis_text = f'改进分析:\n\n'
        analysis_text += f'原始误差: {orig_result["mean_error"]:.3f}\n'
        analysis_text += f'预处理误差: {proc_result["mean_error"]:.3f}\n'
        analysis_text += f'改进幅度: {improvement:+.1f}%\n\n'
        
        if proc_result["mean_error"] < 1.0:
            analysis_text += f'⚠️ 预处理误差<1.0\n'
            analysis_text += f'可能存在过拟合或\n'
            analysis_text += f'标准化空间误差\n'
        elif proc_result["mean_error"] < 5.0:
            analysis_text += f'✅ 预处理效果良好\n'
            analysis_text += f'误差在合理范围内\n'
        else:
            analysis_text += f'📈 仍有改进空间\n'
        
        ax6.text(0.05, 0.95, analysis_text, transform=ax6.transAxes, 
                fontsize=10, verticalalignment='top', fontfamily='monospace')
        ax6.set_xlim(0, 1)
        ax6.set_ylim(0, 1)
        ax6.set_title('改进分析')
        ax6.axis('off')
        
        plt.tight_layout()
        plt.savefig(viz_dir / f"verification_comparison_sample_{i+1}.png", 
                   dpi=150, bbox_inches='tight')
        print(f"💾 保存对比图: verification_comparison_sample_{i+1}.png")
        plt.show()

def main():
    """主函数"""
    print("🔍 简单验证预处理结果")
    print("=" * 60)
    
    # 加载数据
    orig_pcs, orig_kps, proc_pcs, proc_kps = load_and_compare_data()
    
    # 准备统一格式的数据
    orig_pcs_uniform, orig_kps_uniform = prepare_uniform_data(orig_pcs, orig_kps)
    proc_pcs_uniform, proc_kps_uniform = prepare_uniform_data(proc_pcs, proc_kps)
    
    # 训练和测试原始数据
    orig_error, orig_results, orig_all_errors = train_and_test_model(
        orig_pcs_uniform, orig_kps_uniform, "原始数据"
    )
    
    # 训练和测试预处理数据
    proc_error, proc_results, proc_all_errors = train_and_test_model(
        proc_pcs_uniform, proc_kps_uniform, "预处理数据"
    )
    
    # 计算改进
    improvement = (orig_error - proc_error) / orig_error * 100
    
    print(f"\n📊 验证结果总结:")
    print("=" * 40)
    print(f"原始数据平均误差: {orig_error:.3f}")
    print(f"预处理数据平均误差: {proc_error:.3f}")
    print(f"改进幅度: {improvement:+.1f}%")
    
    # 分析结果可信度
    print(f"\n🤔 结果分析:")
    if proc_error < 1.0:
        print("⚠️ 预处理误差<1.0，可能存在以下问题:")
        print("   1. 在标准化空间计算误差，未转换回真实物理空间")
        print("   2. 模型过拟合到标准化数据")
        print("   3. 数据泄露或评估方法问题")
    elif proc_error < 5.0:
        print("✅ 预处理误差在合理范围内")
        print("   预处理确实带来了显著改进")
    else:
        print("📈 预处理有一定效果，但仍有改进空间")
    
    # 可视化对比
    if len(orig_results) > 0 and len(proc_results) > 0:
        visualize_comparison(orig_results, proc_results)
    
    return {
        "original_error": orig_error,
        "processed_error": proc_error,
        "improvement_percent": improvement,
        "original_all_errors": orig_all_errors,
        "processed_all_errors": proc_all_errors
    }

if __name__ == "__main__":
    results = main()
