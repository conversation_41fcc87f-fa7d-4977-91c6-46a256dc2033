#!/usr/bin/env python3
"""
CHaR-Inspired训练脚本
基于CHaRNet论文的条件化热图回归机制
目标: 从5.829mm突破到5.0mm医疗级精度
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random
from char_inspired_improvements import CHaRInspiredPointNet, CHaRLoss

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class ReducedKeypointsF3Dataset(Dataset):
    """12关键点F3数据集 (复用成功配置)"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points
        self.augment = augment
        self.split = split
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
    
    def __len__(self):
        return len(self.sample_ids)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        # 数据增强 (保持基线配置)
        if self.augment and self.split == 'train':
            if np.random.random() < 0.7:
                angle = np.random.uniform(-0.08, 0.08)
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                point_cloud = point_cloud @ rotation.T
                keypoints = keypoints @ rotation.T
            
            if np.random.random() < 0.6:
                translation = np.random.uniform(-0.4, 0.4, 3)
                point_cloud += translation
                keypoints += translation
            
            if np.random.random() < 0.5:
                scale = np.random.uniform(0.99, 1.01, 3)
                point_cloud *= scale
                keypoints *= scale
            
            if np.random.random() < 0.6:
                noise_level = np.random.choice([0.02, 0.03, 0.04])
                noise = np.random.normal(0, noise_level, point_cloud.shape)
                point_cloud += noise
        
        # 关键点存在性标签 (所有关键点都存在)
        presence_labels = np.ones(12, dtype=np.float32)
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'presence_labels': torch.FloatTensor(presence_labels),
            'sample_id': self.sample_ids[idx]
        }

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def train_char_inspired_model():
    """训练CHaR-Inspired模型"""
    
    print(f"🚀 **CHaR-Inspired模型训练**")
    print(f"📚 **基于CHaRNet论文的条件化热图回归机制**")
    print(f"🎯 **基础**: 5.829mm集成双Softmax成功配置")
    print(f"📈 **目标**: 突破5.0mm医疗级精度")
    print(f"🔬 **创新**: 多任务学习 (定位+存在性分类)")
    print("=" * 80)
    
    set_seed(42)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 数据集 (复用成功配置)
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    train_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'train', 
                                            num_points=4096, test_samples=test_samples, 
                                            augment=True, seed=42)
    val_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'val', 
                                          num_points=4096, test_samples=test_samples, 
                                          augment=False, seed=42)
    
    batch_size = 4  # 保持成功配置
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
    
    # 模型
    model = CHaRInspiredPointNet(num_keypoints=12, dropout_rate=0.3).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 模型参数: {total_params:,}")
    
    # CHaR损失函数
    criterion = CHaRLoss(
        lambda_reg=1.0,      # 回归损失权重
        lambda_cls=0.1,      # 分类损失权重 (较小，因为所有关键点都存在)
        lambda_heatmap=0.0   # 暂时不使用热图损失
    )
    
    # 优化器 (基于成功配置)
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
    
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=12, min_lr=1e-6
    )
    
    num_epochs = 120
    best_val_error = float('inf')
    patience = 20
    patience_counter = 0
    history = []
    min_delta = 0.005
    
    print(f"🎯 训练配置: CHaR多任务学习")
    print(f"   损失权重: 回归1.0, 分类0.1, 热图0.0")
    print(f"   学习率: 0.0008, 权重衰减: 1e-4")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 40)
        
        # 训练
        model.train()
        train_losses = {'total': 0, 'regression': 0, 'classification': 0}
        train_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        for batch in train_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            presence_labels = batch['presence_labels'].to(device)
            
            optimizer.zero_grad()
            
            try:
                # CHaR前向传播
                pred_keypoints, presence_probs, heatmaps = model(point_cloud, keypoints)
                
                # CHaR损失计算
                loss_dict = criterion(
                    pred_keypoints, keypoints,
                    presence_probs, presence_labels
                )
                
                total_loss = loss_dict['total_loss']
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                # 记录损失
                train_losses['total'] += total_loss.item()
                train_losses['regression'] += loss_dict['regression_loss'].item()
                train_losses['classification'] += loss_dict['classification_loss'].item()
                
                # 计算指标
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                        
            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue
        
        # 平均训练损失和指标
        for key in train_losses:
            train_losses[key] /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # 验证
        model.eval()
        val_losses = {'total': 0, 'regression': 0, 'classification': 0}
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                presence_labels = batch['presence_labels'].to(device)
                
                try:
                    # CHaR推理
                    pred_keypoints, presence_probs, heatmaps = model(point_cloud)
                    
                    # 损失计算
                    loss_dict = criterion(
                        pred_keypoints, keypoints,
                        presence_probs, presence_labels
                    )
                    
                    val_losses['total'] += loss_dict['total_loss'].item()
                    val_losses['regression'] += loss_dict['regression_loss'].item()
                    val_losses['classification'] += loss_dict['classification_loss'].item()
                    
                    # 计算指标
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        # 平均验证损失和指标
        for key in val_losses:
            val_losses[key] /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # 学习率调度
        scheduler.step(val_losses['total'])
        current_lr = optimizer.param_groups[0]['lr']
        
        # 打印结果
        print(f"训练: 总损失={train_losses['total']:.4f}, 回归={train_losses['regression']:.4f}, "
              f"分类={train_losses['classification']:.4f}")
        print(f"      误差={train_metrics['mean_distance']:.3f}mm, "
              f"5mm={train_metrics['within_5mm_percent']:.1f}%, 7mm={train_metrics['within_7mm_percent']:.1f}%")
        
        print(f"验证: 总损失={val_losses['total']:.4f}, 回归={val_losses['regression']:.4f}, "
              f"分类={val_losses['classification']:.4f}")
        print(f"      误差={val_metrics['mean_distance']:.3f}mm, "
              f"5mm={val_metrics['within_5mm_percent']:.1f}%, 7mm={val_metrics['within_7mm_percent']:.1f}%")
        print(f"学习率: {current_lr:.2e}")
        
        # 保存历史
        history.append({
            'epoch': epoch + 1,
            'train_losses': train_losses,
            'val_losses': val_losses,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'learning_rate': current_lr
        })
        
        # 检查改进
        current_error = val_metrics['mean_distance']
        improvement = best_val_error - current_error
        
        if improvement > min_delta:
            best_val_error = current_error
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics,
                'model_name': 'CHaR_Inspired',
                'config': 'char_conditioned_heatmap_regression'
            }, f'best_char_inspired_{best_val_error:.3f}mm.pth')
            
            print(f"🎉 新最佳! 验证误差: {best_val_error:.3f}mm (改进{improvement:.3f}mm)")
            
            if best_val_error <= 5.0:
                print(f"🏆 **突破5.0mm医疗级目标!**")
            elif best_val_error < 5.5:
                print(f"🎯 **突破5.5mm目标!** 接近医疗级精度")
            elif best_val_error < 5.829:
                print(f"✅ **超越集成双Softmax!** CHaR机制有效")
        else:
            patience_counter += 1
            print(f"⏳ 无显著改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停触发")
            break
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    
    # 保存结果
    results = {
        'method': 'CHaR-Inspired Conditioned Heatmap Regression',
        'baseline_error': 5.829,
        'best_val_error': float(best_val_error),
        'improvement': float((5.829 - best_val_error) / 5.829 * 100),
        'training_time_minutes': float(total_time / 60),
        'epochs_trained': len(history),
        'history': history,
        'char_config': {
            'lambda_reg': 1.0,
            'lambda_cls': 0.1,
            'lambda_heatmap': 0.0,
            'multi_task_learning': True,
            'conditioned_regression': True
        }
    }
    
    with open('char_inspired_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 **CHaR-Inspired训练完成!**")
    print(f"📊 集成双Softmax基线: 5.829mm")
    print(f"🎯 CHaR最佳误差: {best_val_error:.3f}mm")
    print(f"📈 改进幅度: {(5.829 - best_val_error) / 5.829 * 100:.1f}%")
    print(f"⏱️  训练时间: {total_time/60:.1f}分钟")
    
    if best_val_error < 5.0:
        print(f"🏆 **成功突破5.0mm医疗级目标!**")
    elif best_val_error < 5.5:
        print(f"🎯 **成功突破5.5mm目标!** 接近医疗级精度")
    elif best_val_error < 5.829:
        print(f"✅ **成功超越集成双Softmax!** CHaR机制有效")
    else:
        print(f"💡 **接近基线性能** CHaR参数可能需要调优")
    
    return best_val_error, results

if __name__ == "__main__":
    set_seed(42)
    best_error, results = train_char_inspired_model()
