#!/usr/bin/env python3
"""
探索更好的改进方向
基于5.959mm成功基础，系统性寻找突破5.5mm的方法
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random
from baseline_with_double_softmax import BaselineAdaptivePointNet, SimplifiedDoubleSoftMax, ReducedKeypointsF3Dataset, calculate_metrics, ImprovedLoss

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class OptimizedDoubleSoftMax(nn.Module):
    """
    优化的双Softmax机制
    基于5.959mm成功配置，进行参数优化
    """
    
    def __init__(self, threshold_ratio=0.15, temperature=2.0, weight_ratio=0.8):
        super(OptimizedDoubleSoftMax, self).__init__()
        
        self.threshold_ratio = threshold_ratio
        self.temperature = temperature
        self.weight_ratio = weight_ratio  # 距离权重比例
        
        # 改进的权重计算网络
        self.weight_net = nn.Sequential(
            nn.Linear(3, 64),  # 增加网络容量
            nn.ReLU(),
            nn.BatchNorm1d(64),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.BatchNorm1d(32),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 1)
        )
        
        print(f"🎯 优化双Softmax机制:")
        print(f"   阈值比例: {threshold_ratio}")
        print(f"   温度参数: {temperature}")
        print(f"   权重比例: {weight_ratio}:{1-weight_ratio}")
    
    def forward(self, points, predicted_keypoint):
        """优化的双Softmax权重计算"""
        # 计算相对位置
        relative_pos = points - predicted_keypoint.unsqueeze(0)
        
        # 第一个Softmax - 改进的距离权重
        distances = torch.norm(relative_pos, dim=1)
        # 使用更平滑的距离权重
        distance_weights = F.softmax(-distances**2 / (2 * self.temperature**2), dim=0)
        
        # 第二个Softmax - 改进的神经网络权重
        if len(relative_pos) > 1:
            nn_weights = self.weight_net(relative_pos).squeeze(-1)
            nn_weights = F.softmax(nn_weights / self.temperature, dim=0)
        else:
            nn_weights = torch.ones_like(distance_weights)
        
        # 自适应权重组合
        combined_weights = self.weight_ratio * distance_weights + (1 - self.weight_ratio) * nn_weights
        
        # 改进的阈值过滤
        # 使用分位数而不是平均值
        threshold = torch.quantile(combined_weights, 1 - self.threshold_ratio)
        filter_mask = combined_weights >= threshold
        
        # 确保至少保留一些点
        if filter_mask.sum() < 3:
            _, top_indices = torch.topk(combined_weights, min(5, len(combined_weights)))
            filter_mask = torch.zeros_like(combined_weights, dtype=torch.bool)
            filter_mask[top_indices] = True
        
        # 重新归一化
        filtered_weights = combined_weights * filter_mask.float()
        sum_weights = torch.sum(filtered_weights)
        
        if sum_weights > 1e-8:
            final_weights = filtered_weights / sum_weights
        else:
            final_weights = combined_weights / combined_weights.sum()
        
        # 加权平均得到精细化关键点
        refined_keypoint = torch.sum(final_weights.unsqueeze(-1) * points, dim=0)
        
        return refined_keypoint

class EnsembleDoubleSoftMax(nn.Module):
    """
    集成双Softmax机制
    使用多个不同参数的双Softmax进行集成
    """
    
    def __init__(self, num_ensembles=3):
        super(EnsembleDoubleSoftMax, self).__init__()
        
        self.num_ensembles = num_ensembles
        
        # 创建多个不同参数的双Softmax
        self.softmax_modules = nn.ModuleList([
            OptimizedDoubleSoftMax(
                threshold_ratio=0.1 + 0.1 * i,
                temperature=1.5 + 0.5 * i,
                weight_ratio=0.6 + 0.1 * i
            ) for i in range(num_ensembles)
        ])
        
        print(f"🎯 集成双Softmax机制: {num_ensembles}个模块")
    
    def forward(self, points, predicted_keypoint):
        """集成多个双Softmax的结果"""
        refined_keypoints = []
        
        for softmax_module in self.softmax_modules:
            refined_kp = softmax_module(points, predicted_keypoint)
            refined_keypoints.append(refined_kp)
        
        # 简单平均集成
        ensemble_keypoint = torch.stack(refined_keypoints).mean(dim=0)
        
        return ensemble_keypoint

class AdaptiveBaselinePointNet(nn.Module):
    """
    自适应基线PointNet
    基于成功的基线架构，添加自适应双Softmax
    """
    
    def __init__(self, num_keypoints: int, dropout_rate: float = 0.3, 
                 softmax_type: str = "optimized"):
        super(AdaptiveBaselinePointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        self.softmax_type = softmax_type
        
        # 基线架构 (完全保持)
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(dropout_rate)
        
        # 选择双Softmax类型
        if softmax_type == "optimized":
            self.double_softmax = OptimizedDoubleSoftMax(
                threshold_ratio=0.15,
                temperature=2.0,
                weight_ratio=0.8
            )
        elif softmax_type == "ensemble":
            self.double_softmax = EnsembleDoubleSoftMax(num_ensembles=3)
        else:  # original
            self.double_softmax = SimplifiedDoubleSoftMax(
                threshold_ratio=0.2,
                temperature=1.5
            )
        
        print(f"🧠 自适应基线模型: {num_keypoints}个关键点")
        print(f"   双Softmax类型: {softmax_type}")
        
    def forward(self, x):
        batch_size = x.size(0)
        x_input = x.transpose(2, 1)
        
        # 基线模型前向传播 (完全一致)
        x1 = torch.relu(self.bn1(self.conv1(x_input)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        
        x3_res = x3 + self.residual1(x1)
        
        x4 = torch.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = torch.relu(self.bn5(self.conv5(x4_res)))
        
        global_feat = torch.max(x5, 2)[0]
        
        feat = torch.relu(self.bn_fc1(self.fc1(global_feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc2(self.fc2(feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc3(self.fc3(feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc4(self.fc4(feat)))
        feat = self.dropout(feat)
        feat = self.fc5(feat)
        
        keypoints = feat.view(batch_size, self.num_keypoints, 3)
        
        # 推理时应用双Softmax精细化
        if not self.training:
            keypoints = self.apply_double_softmax_refinement(x, keypoints)
        
        return keypoints
    
    def apply_double_softmax_refinement(self, points, predicted_keypoints):
        """应用双Softmax精细化"""
        batch_size = points.shape[0]
        refined_keypoints = []
        
        for b in range(batch_size):
            batch_points = points[b]
            batch_keypoints = predicted_keypoints[b]
            
            batch_refined = []
            for k in range(self.num_keypoints):
                kp_pred = batch_keypoints[k]
                
                # 自适应候选点选择
                distances = torch.norm(batch_points - kp_pred.unsqueeze(0), dim=1)
                
                # 动态调整候选点数量
                total_points = batch_points.shape[0]
                if total_points > 1000:
                    K = 512
                elif total_points > 500:
                    K = 256
                else:
                    K = min(128, total_points)
                
                _, nearest_indices = torch.topk(distances, K, largest=False)
                candidate_points = batch_points[nearest_indices]
                
                # 应用双Softmax
                refined_kp = self.double_softmax(candidate_points, kp_pred)
                batch_refined.append(refined_kp)
            
            refined_keypoints.append(torch.stack(batch_refined))
        
        return torch.stack(refined_keypoints)

def test_improvement_approaches():
    """测试不同的改进方法"""
    
    print("🧪 **测试改进方法**")
    print("🎯 **目标**: 从5.959mm突破到5.5mm")
    print("=" * 80)
    
    set_seed(42)
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    
    # 测试配置
    improvement_configs = [
        {
            "name": "优化双Softmax参数",
            "model_class": AdaptiveBaselinePointNet,
            "model_kwargs": {"softmax_type": "optimized"},
            "config": {
                "batch_size": 4,
                "lr": 0.0008,
                "weight_decay": 1e-4,
                "dropout": 0.3,
                "epochs": 100,
                "patience": 15
            },
            "description": "优化阈值、温度和权重比例"
        },
        {
            "name": "集成双Softmax",
            "model_class": AdaptiveBaselinePointNet,
            "model_kwargs": {"softmax_type": "ensemble"},
            "config": {
                "batch_size": 4,
                "lr": 0.0008,
                "weight_decay": 1e-4,
                "dropout": 0.3,
                "epochs": 100,
                "patience": 15
            },
            "description": "多个双Softmax模块集成"
        }
    ]
    
    results = []
    
    for i, exp_config in enumerate(improvement_configs, 1):
        print(f"\n{'='*80}")
        print(f"🧪 **改进方法 {i}/{len(improvement_configs)}**: {exp_config['name']}**")
        print(f"📝 **描述**: {exp_config['description']}")
        print(f"{'='*80}")
        
        try:
            # 快速测试 (减少轮数)
            best_error = quick_test_improvement(
                exp_config["model_class"],
                exp_config["model_kwargs"],
                exp_config["config"],
                exp_config["name"]
            )
            
            result = {
                "name": exp_config["name"],
                "description": exp_config["description"],
                "best_error": float(best_error),
                "improvement_vs_baseline": float((5.959 - best_error) / 5.959 * 100),
                "status": "突破5.5mm" if best_error < 5.5 else "改进" if best_error < 5.959 else "无改进"
            }
            results.append(result)
            
            print(f"\n🎯 **{exp_config['name']}完成!**")
            print(f"   最佳误差: {best_error:.3f}mm")
            print(f"   vs 5.959mm: {(5.959 - best_error) / 5.959 * 100:+.1f}%")
            
            if best_error < 5.5:
                print(f"🏆 **突破5.5mm医疗级目标!**")
            elif best_error < 5.959:
                print(f"✅ **成功改进!**")
            
        except Exception as e:
            print(f"❌ {exp_config['name']}测试失败: {e}")
            continue
    
    # 保存结果
    improvement_results = {
        "baseline_performance": "5.959mm (基线+双Softmax)",
        "target": "5.5mm (医疗级精度)",
        "improvements_tested": results,
        "best_improvement": min(results, key=lambda x: x["best_error"]) if results else None
    }
    
    with open("improvement_exploration_results.json", "w", encoding="utf-8") as f:
        json.dump(improvement_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 **改进探索完成!**")
    print("=" * 80)
    
    if results:
        best_result = min(results, key=lambda x: x["best_error"])
        print(f"🏆 **最佳改进**: {best_result['name']}")
        print(f"🎯 **最佳误差**: {best_result['best_error']:.3f}mm")
        print(f"📈 **改进幅度**: {best_result['improvement_vs_baseline']:+.1f}%")
        
        if best_result["best_error"] < 5.5:
            print(f"🎉 **成功突破5.5mm医疗级目标!**")
        elif best_result["best_error"] < 5.959:
            print(f"✅ **成功找到更好的改进!**")
    
    return results

def quick_test_improvement(model_class, model_kwargs, config, method_name):
    """快速测试改进方法"""
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    
    # 数据集
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    train_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'train', 
                                            num_points=4096, test_samples=test_samples, 
                                            augment=True, seed=42)
    val_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'val', 
                                          num_points=4096, test_samples=test_samples, 
                                          augment=False, seed=42)
    
    train_loader = DataLoader(train_dataset, batch_size=config["batch_size"], shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=config["batch_size"], shuffle=False, num_workers=0)
    
    # 模型
    model = model_class(num_keypoints=12, dropout_rate=config["dropout"], **model_kwargs).to(device)
    
    # 训练设置
    criterion = ImprovedLoss(alpha=0.8, beta=0.2)
    optimizer = optim.AdamW(model.parameters(), lr=config["lr"], weight_decay=config["weight_decay"])
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.7, patience=8, min_lr=1e-6)
    
    best_val_error = float('inf')
    patience_counter = 0
    
    print(f"🚀 快速测试: {method_name}")
    
    for epoch in range(config["epochs"]):
        # 训练
        model.train()
        train_loss = 0.0
        
        for batch in train_loader:
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            pred_keypoints = model(point_cloud)
            loss = criterion(pred_keypoints, keypoints)
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                val_loss += loss.item()
                
                metrics = calculate_metrics(pred_keypoints, keypoints)
                val_metrics['mean_distance'] += metrics['mean_distance']
        
        val_loss /= len(val_loader)
        val_metrics['mean_distance'] /= len(val_loader)
        
        scheduler.step(val_loss)
        
        current_error = val_metrics['mean_distance']
        
        if current_error < best_val_error - 0.005:
            best_val_error = current_error
            patience_counter = 0
            
            # 保存最佳模型
            torch.save({
                'model_state_dict': model.state_dict(),
                'best_val_error': best_val_error,
                'method': method_name
            }, f'best_{method_name.lower().replace(" ", "_")}_improvement_{best_val_error:.3f}mm.pth')
            
            print(f"   Epoch {epoch+1}: {best_val_error:.3f}mm ✅")
            
            if best_val_error < 5.5:
                print(f"   🏆 突破5.5mm目标!")
                break
        else:
            patience_counter += 1
            if patience_counter >= config["patience"]:
                print(f"   早停于Epoch {epoch+1}")
                break
        
        if epoch % 10 == 0:
            print(f"   Epoch {epoch+1}: {current_error:.3f}mm")
    
    return best_val_error

if __name__ == "__main__":
    set_seed(42)
    results = test_improvement_approaches()
