import numpy as np
import cv2
import matplotlib.pyplot as plt

class PointPlusPlus:
    def __init__(self, min_line_width=2):
        self.min_line_width = min_line_width

    def thin_lines(self, image):
        """对图像进行细化处理，将线条宽度减少到1个像素"""
        kernel = np.ones((3,3), np.uint8)
        image = cv2.threshold(image, 127, 255, cv2.THRESH_BINARY)[1]
        thinned = cv2.ximgproc.thinning(image)
        return thinned

    def find_intersections(self, image):
        """查找线条交叉点"""
        # 首先对图像进行细化
        thinned = self.thin_lines(image)
        
        # 获取所有线条像素点
        y, x = np.where(thinned > 0)
        points = list(zip(x, y))
        
        singular_points = []  # 奇点
        even_points = []      # 偶点
        
        # 对每个线条像素点检查其8邻域
        for x, y in points:
            # 提取3x3邻域
            if x == 0 or y == 0 or x == image.shape[1]-1 or y == image.shape[0]-1:
                continue
                
            neighborhood = thinned[y-1:y+2, x-1:x+2]
            
            # 计算连接数（使用改进的Zhang-Suen算法）
            connections = self.count_connections(neighborhood)
            
            if connections > 2:  # 只关注交叉点
                if connections % 2 == 1:
                    singular_points.append((x, y))
                else:
                    even_points.append((x, y))
        
        # 合并距离很近的点
        singular_points = self.merge_close_points(singular_points)
        even_points = self.merge_close_points(even_points)
        
        return singular_points, even_points

    def count_connections(self, neighborhood):
        """计算中心点的连接数"""
        # Zhang-Suen细化算法中的连接数计算
        p = neighborhood.astype(np.uint8)
        p2 = p[0,1]  # P2
        p3 = p[0,2]  # P3
        p4 = p[1,2]  # P4
        p5 = p[2,2]  # P5
        p6 = p[2,1]  # P6
        p7 = p[2,0]  # P7
        p8 = p[1,0]  # P8
        p9 = p[0,0]  # P9
        
        # 计算从0到1的跳变次数
        transitions = 0
        neighbors = [p2,p3,p4,p5,p6,p7,p8,p9,p2]
        for i in range(len(neighbors)-1):
            if neighbors[i] == 0 and neighbors[i+1] == 1:
                transitions += 1
        
        return transitions

    def merge_close_points(self, points, distance_threshold=5):
        """合并距离很近的点"""
        if not points:
            return points
            
        merged_points = []
        points = np.array(points)
        
        while len(points) > 0:
            current = points[0]
            distances = np.sqrt(np.sum((points - current)**2, axis=1))
            cluster = points[distances < distance_threshold]
            merged_point = np.mean(cluster, axis=0).astype(int)
            merged_points.append(tuple(merged_point))
            points = points[distances >= distance_threshold]
            
        return merged_points

def test_point_plus_plus():
    # 创建测试图像
    image = np.zeros((200, 200), dtype=np.uint8)
    
    # 绘制正方形
    cv2.rectangle(image, (50, 50), (150, 150), 255, 2)
    # 绘制对角线
    cv2.line(image, (50, 50), (150, 150), 255, 2)
    cv2.line(image, (50, 150), (150, 50), 255, 2)
    
    # 初始化检测器
    detector = PointPlusPlus()
    
    # 检测特征点
    singular_points, even_points = detector.find_intersections(image)
    
    # 可视化结果
    plt.figure(figsize=(10, 10))
    plt.imshow(image, cmap='gray')
    
    # 绘制检测到的点
    for point in singular_points:
        plt.plot(point[0], point[1], 'ro', markersize=10, label='奇点')
    for point in even_points:
        plt.plot(point[0], point[1], 'bo', markersize=10, label='偶点')
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    plt.title('交叉点检测结果')
    handles, labels = plt.gca().get_legend_handles_labels()
    by_label = dict(zip(labels, handles))
    plt.legend(by_label.values(), by_label.keys())
    plt.show()
    
    print(f"检测到的奇点数量：{len(singular_points)}")
    print(f"检测到的偶点数量：{len(even_points)}")

if __name__ == "__main__":
    test_point_plus_plus() 