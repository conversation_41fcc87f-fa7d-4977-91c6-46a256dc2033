#!/usr/bin/env python3
"""
Train Wang2022 Model on Filtered 12-Point Medical Dataset

This script trains the Wang et al. 2022 architecture on the filtered 12-point
medical keypoint dataset, targeting medical-grade precision with improved efficiency.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import h5py
from pathlib import Path
import time
import json
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt

# Import Wang2022 components
from wang2022_complete_architecture import (
    Wang2022CompleteModel,
    AdaptiveWingLoss,
    create_gaussian_heatmaps
)

class Filtered12PointDataset(torch.utils.data.Dataset):
    """Dataset for filtered 12-point medical keypoints"""
    
    def __init__(self, data_dir: str, split: str = 'train', 
                 num_points: int = 2000, augment: bool = False):
        self.data_dir = Path(data_dir)
        self.split = split
        self.num_points = num_points
        self.augment = augment
        
        # Load sample files
        split_dir = self.data_dir / split
        self.sample_files = list(split_dir.glob('*.h5'))
        
        print(f"📂 {split} dataset: {len(self.sample_files)} samples")
        
    def __len__(self):
        return len(self.sample_files)
    
    def __getitem__(self, idx):
        sample_file = self.sample_files[idx]
        
        with h5py.File(sample_file, 'r') as f:
            # Load 12 filtered keypoints and aligned point cloud
            keypoints = f['keypoints'][:]  # (12, 3)
            point_cloud = f['point_cloud'][:].T  # (N, 3)
            
        # Sample points
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        elif len(point_cloud) < self.num_points:
            # Pad with random sampling
            indices = np.random.choice(len(point_cloud), self.num_points, replace=True)
            point_cloud = point_cloud[indices]
            
        # Data augmentation
        if self.augment and self.split == 'train':
            # Random rotation around Z-axis (medical data should preserve anatomy)
            angle = np.random.uniform(-np.pi/12, np.pi/12)  # ±15 degrees
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            point_cloud = point_cloud @ rotation_matrix.T
            keypoints = keypoints @ rotation_matrix.T
            
            # Small random translation
            translation = np.random.uniform(-2.0, 2.0, 3)
            point_cloud += translation
            keypoints += translation
            
            # Random scaling (small range for medical data)
            scale = np.random.uniform(0.95, 1.05)
            point_cloud *= scale
            keypoints *= scale
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),  # (N, 3)
            'keypoints': torch.FloatTensor(keypoints),  # (12, 3)
            'sample_id': sample_file.stem
        }

def combined_loss_function(pred_heatmaps: torch.Tensor,
                          pred_keypoints: torch.Tensor,
                          target_keypoints: torch.Tensor,
                          point_cloud: torch.Tensor,
                          mse_weight: float = 1.0,
                          wing_weight: float = 0.5,
                          smooth_l1_weight: float = 0.6) -> Tuple[torch.Tensor, Dict]:
    """Combined loss function for Wang2022 model"""

    # MSE Loss for keypoints
    mse_loss = nn.MSELoss()(pred_keypoints, target_keypoints)

    # Adaptive Wing Loss for heatmaps
    # Create target heatmaps (point_cloud is (batch, num_points, 3))
    target_heatmaps = create_gaussian_heatmaps(point_cloud, target_keypoints)

    wing_loss_fn = AdaptiveWingLoss()
    wing_loss = wing_loss_fn(pred_heatmaps, target_heatmaps)

    # Smooth L1 Loss for keypoints
    smooth_l1_loss = nn.SmoothL1Loss()(pred_keypoints, target_keypoints)

    # Combined loss
    total_loss = (mse_weight * mse_loss +
                  wing_weight * wing_loss +
                  smooth_l1_weight * smooth_l1_loss)

    loss_details = {
        'mse_loss': mse_loss.item(),
        'wing_loss': wing_loss.item(),
        'smooth_l1_loss': smooth_l1_loss.item(),
        'total_loss': total_loss.item()
    }

    return total_loss, loss_details

def calculate_metrics(pred_keypoints: torch.Tensor, 
                     target_keypoints: torch.Tensor,
                     scale_factor: float = 196.87) -> Dict:
    """Calculate evaluation metrics"""
    
    # Convert to mm
    pred_mm = pred_keypoints.cpu().numpy() * scale_factor
    target_mm = target_keypoints.cpu().numpy() * scale_factor
    
    # Calculate distances
    distances = np.linalg.norm(pred_mm - target_mm, axis=-1)  # (batch, 12)
    
    # Overall metrics
    mean_error = np.mean(distances)
    std_error = np.std(distances)
    max_error = np.max(distances)
    
    # Medical accuracy thresholds
    excellent_acc = np.mean(distances <= 2.0) * 100  # ≤2mm
    good_acc = np.mean(distances <= 5.0) * 100       # ≤5mm
    acceptable_acc = np.mean(distances <= 10.0) * 100 # ≤10mm
    
    # Region-wise analysis (F1: 0-3, F2: 4-7, F3: 8-11)
    f1_error = np.mean(distances[:, 0:4])
    f2_error = np.mean(distances[:, 4:8])
    f3_error = np.mean(distances[:, 8:12])
    
    return {
        'overall': {
            'mean_error': mean_error,
            'std_error': std_error,
            'max_error': max_error,
            'excellent_percentage': excellent_acc,
            'good_percentage': good_acc,
            'acceptable_percentage': acceptable_acc
        },
        'regions': {
            'F1_error': f1_error,
            'F2_error': f2_error,
            'F3_error': f3_error
        }
    }

def train_wang2022_12point():
    """Train Wang2022 model on 12-point dataset"""
    
    print("🚀 训练Wang2022模型 - 12点医疗数据集")
    print("🎯 目标: 医疗级精度 (<5mm)")
    print("📊 数据集: FilteredMedical12Point_Full (99样本)")
    print("=" * 80)
    
    # Device setup - 使用GPU 2避免内存冲突
    device = torch.device('cuda:2' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")

    # 清理GPU缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print(f"🧹 清理GPU缓存完成")

    # Dataset setup - 减少点云数量以节省内存
    dataset_dir = "FilteredMedical12Point_Full"
    num_points = 1000  # 减少到1000个点
    
    train_dataset = Filtered12PointDataset(dataset_dir, split='train', 
                                         num_points=num_points, augment=True)
    val_dataset = Filtered12PointDataset(dataset_dir, split='val', 
                                       num_points=num_points, augment=False)
    test_dataset = Filtered12PointDataset(dataset_dir, split='test', 
                                        num_points=num_points, augment=False)
    
    # Data loaders - 减少批量大小以节省内存
    batch_size = 2
    train_loader = DataLoader(train_dataset, batch_size=batch_size, 
                            shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, 
                          shuffle=False, num_workers=2)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, 
                           shuffle=False, num_workers=2)
    
    print(f"📊 数据集统计:")
    print(f"   训练样本: {len(train_dataset)}")
    print(f"   验证样本: {len(val_dataset)}")
    print(f"   测试样本: {len(test_dataset)}")
    print(f"   批量大小: {batch_size}")
    print(f"   关键点数: 12个")
    
    # Model setup
    model = Wang2022CompleteModel(
        num_landmarks=12,  # 12 keypoints
        num_points=num_points,
        num_neighbors=20
    ).to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 Wang2022-12点模型:")
    print(f"   总参数: {total_params:,}")
    print(f"   模型大小: {total_params * 4 / (1024 * 1024):.1f}MB")
    
    # Training setup
    optimizer = optim.Adam(model.parameters(), lr=0.0008, weight_decay=0.0001)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.75, patience=8, min_lr=1e-6, verbose=True
    )
    
    # Training configuration
    num_epochs = 120
    best_val_error = float('inf')
    patience_counter = 0
    patience = 15
    
    # Training history
    training_history = []
    
    print(f"\n🎯 开始训练 (目标: <5mm医疗级精度)")
    print(f"📈 训练轮数: {num_epochs}, 早停耐心: {patience}")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 50)
        
        # Training phase
        model.train()
        train_loss = 0.0
        train_loss_details = {'mse_loss': 0, 'wing_loss': 0, 'smooth_l1_loss': 0}
        
        for batch_idx, batch in enumerate(train_loader):
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            # Forward pass - model expects (batch, num_points, 3)
            heatmaps, pred_keypoints, transform = model(point_cloud)

            # Calculate loss
            loss, loss_details = combined_loss_function(heatmaps, pred_keypoints, keypoints, point_cloud)
            
            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            for key in train_loss_details:
                if key in loss_details:
                    train_loss_details[key] += loss_details[key]
        
        # Average training losses
        train_loss /= len(train_loader)
        for key in train_loss_details:
            train_loss_details[key] /= len(train_loader)
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        val_loss_details = {'mse_loss': 0, 'wing_loss': 0, 'smooth_l1_loss': 0}
        all_pred_keypoints = []
        all_target_keypoints = []
        
        with torch.no_grad():
            for batch in val_loader:
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                heatmaps, pred_keypoints, transform = model(point_cloud)
                loss, loss_details = combined_loss_function(heatmaps, pred_keypoints, keypoints, point_cloud)
                
                val_loss += loss.item()
                for key in val_loss_details:
                    if key in loss_details:
                        val_loss_details[key] += loss_details[key]
                
                all_pred_keypoints.append(pred_keypoints.cpu())
                all_target_keypoints.append(keypoints.cpu())
        
        # Average validation losses
        val_loss /= len(val_loader)
        for key in val_loss_details:
            val_loss_details[key] /= len(val_loader)
        
        # Calculate metrics
        all_pred = torch.cat(all_pred_keypoints, dim=0)
        all_target = torch.cat(all_target_keypoints, dim=0)
        metrics = calculate_metrics(all_pred, all_target)
        
        # Learning rate scheduling
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # Record training history
        epoch_record = {
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'val_error_mm': metrics['overall']['mean_error'],
            'learning_rate': current_lr,
            **train_loss_details,
            **{f'val_{k}': v for k, v in val_loss_details.items()}
        }
        training_history.append(epoch_record)
        
        # Print progress
        print(f"   训练损失: {train_loss:.4f}")
        print(f"   验证损失: {val_loss:.4f}")
        print(f"   验证误差: {metrics['overall']['mean_error']:.3f}mm")
        print(f"   医疗级精度: {metrics['overall']['good_percentage']:.1f}%")
        print(f"   区域误差: F1={metrics['regions']['F1_error']:.2f}, "
              f"F2={metrics['regions']['F2_error']:.2f}, "
              f"F3={metrics['regions']['F3_error']:.2f}mm")
        print(f"   学习率: {current_lr:.2e}")
        
        # Save best model
        if metrics['overall']['mean_error'] < best_val_error:
            best_val_error = metrics['overall']['mean_error']
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_error': best_val_error,
                'metrics': metrics
            }, 'best_wang2022_12point.pth')
            
            print(f"   ✅ 保存最佳模型 (误差: {best_val_error:.3f}mm)")
        else:
            patience_counter += 1
        
        # Early stopping
        if patience_counter >= patience:
            print(f"   ⏹️ 早停: {patience}轮无改进")
            break
        
        # Medical target check
        if metrics['overall']['mean_error'] <= 5.0:
            print(f"   🎯 达到医疗级目标! (≤5mm)")
            break
    
    total_time = time.time() - start_time
    
    print(f"\n🎯 Wang2022-12点模型训练完成!")
    print(f"   训练时间: {total_time/60:.1f} 分钟")
    print(f"   最佳验证误差: {best_val_error:.3f}mm")
    print(f"   医疗级目标: {'✅ 达成' if best_val_error <= 5.0 else '❌ 未达成'}")
    
    # Save training results
    results = {
        'training_completed': True,
        'best_validation_error_mm': best_val_error,
        'training_time_minutes': total_time / 60,
        'total_epochs': len(training_history),
        'dataset_samples': len(train_dataset) + len(val_dataset) + len(test_dataset),
        'model_parameters': total_params,
        'training_history': training_history
    }
    
    with open('wang2022_12point_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    return best_val_error, total_time

if __name__ == "__main__":
    try:
        best_error, training_time = train_wang2022_12point()
        print(f"\n🎉 训练成功完成!")
        print(f"🎯 最终结果: {best_error:.3f}mm 误差")
        print(f"⏱️ 训练时间: {training_time/60:.1f} 分钟")
        
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        import traceback
        traceback.print_exc()
