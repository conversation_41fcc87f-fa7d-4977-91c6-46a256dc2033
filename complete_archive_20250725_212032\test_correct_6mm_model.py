#!/usr/bin/env python3
"""
使用正确的数据处理方式测试6mm模型
Test 6mm Model with Correct Data Processing
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json
from pathlib import Path
from datetime import datetime

class AdaptivePointNet(nn.Module):
    """AdaptivePointNet架构 - 与原始训练完全一致"""
    
    def __init__(self, num_keypoints: int, dropout_rate: float = 0.4):
        super(AdaptivePointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 卷积层
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 残差连接
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        # 全连接层
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        # 批归一化
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(dropout_rate)
        
    def forward(self, x):
        batch_size = x.size(0)
        
        # 点云特征提取
        x1 = torch.relu(self.bn1(self.conv1(x)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        x4 = torch.relu(self.bn4(self.conv4(x3)))
        x5 = torch.relu(self.bn5(self.conv5(x4)))
        
        # 残差连接
        res1 = self.residual1(x1)
        x3 = x3 + res1
        
        res2 = self.residual2(x2)
        x4 = x4 + res2
        
        # 全局最大池化
        global_feature = torch.max(x5, 2)[0]
        
        # 全连接层
        x = torch.relu(self.bn_fc1(self.fc1(global_feature)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc2(self.fc2(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc3(self.fc3(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc4(self.fc4(x)))
        x = self.fc5(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

class ReducedKeypointsF3Dataset(Dataset):
    """12关键点F3数据集 - 与原始训练完全一致"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points
        self.augment = augment
        self.split = split
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
    
    def __len__(self):
        return len(self.sample_ids)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        # 不使用数据增强进行测试
        point_cloud = torch.FloatTensor(point_cloud).transpose(0, 1)  # [3, N]
        keypoints = torch.FloatTensor(keypoints)  # [12, 3]
        
        return {
            'point_cloud': point_cloud,
            'keypoints': keypoints,
            'sample_id': self.sample_ids[idx]
        }

def calculate_metrics(pred, target):
    """计算评估指标 - 与原始训练完全一致"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def test_6mm_model_correctly():
    """使用正确的方式测试6mm模型"""
    print("🎯 使用正确数据处理方式测试6mm模型")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    # 加载模型
    model_path = "archive/old_models/best_baseline_12kp_6.208mm.pth"
    model = AdaptivePointNet(num_keypoints=12, dropout_rate=0.4).to(device)
    
    try:
        checkpoint = torch.load(model_path, map_location=device)
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"✅ 从checkpoint加载模型权重")
        else:
            model.load_state_dict(checkpoint)
            print(f"✅ 直接加载模型权重")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    model.eval()
    total_params = sum(p.numel() for p in model.parameters())
    print(f"📊 模型参数: {total_params:,}")
    
    # 加载数据集 - 使用与训练时完全相同的方式
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    val_dataset = ReducedKeypointsF3Dataset(
        'data/processed/f3_reduced_12kp_stable.npz', 
        'val', 
        num_points=4096, 
        test_samples=test_samples, 
        augment=False, 
        seed=42
    )
    
    test_dataset = ReducedKeypointsF3Dataset(
        'data/processed/f3_reduced_12kp_stable.npz', 
        'test', 
        num_points=4096, 
        test_samples=test_samples, 
        augment=False, 
        seed=42
    )
    
    print(f"📊 数据集: 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    
    # 创建数据加载器
    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=4, shuffle=False, num_workers=0)
    
    # 评估验证集
    print(f"\n📏 评估验证集...")
    val_metrics = evaluate_model(model, val_loader, device)
    
    # 评估测试集
    print(f"\n📏 评估测试集...")
    test_metrics = evaluate_model(model, test_loader, device)
    
    # 显示结果
    print(f"\n📊 6mm模型性能验证:")
    print("=" * 50)
    print(f"验证集误差: {val_metrics['mean_distance']:.3f}mm")
    print(f"测试集误差: {test_metrics['mean_distance']:.3f}mm")
    print(f"验证集标准差: {val_metrics['std_distance']:.3f}mm")
    print(f"测试集标准差: {test_metrics['std_distance']:.3f}mm")
    
    print(f"\n🎯 精度分析 (验证集):")
    print(f"<1mm: {val_metrics['within_1mm_percent']:.1f}%")
    print(f"<3mm: {val_metrics['within_3mm_percent']:.1f}%")
    print(f"<5mm: {val_metrics['within_5mm_percent']:.1f}%")
    print(f"<7mm: {val_metrics['within_7mm_percent']:.1f}%")
    
    print(f"\n🎯 精度分析 (测试集):")
    print(f"<1mm: {test_metrics['within_1mm_percent']:.1f}%")
    print(f"<3mm: {test_metrics['within_3mm_percent']:.1f}%")
    print(f"<5mm: {test_metrics['within_5mm_percent']:.1f}%")
    print(f"<7mm: {test_metrics['within_7mm_percent']:.1f}%")
    
    # 验证是否与声称的6.208mm一致
    expected_error = 6.208
    actual_error = val_metrics['mean_distance']
    error_diff = abs(actual_error - expected_error)
    
    print(f"\n🔍 与声称性能对比:")
    print(f"声称验证误差: {expected_error:.3f}mm")
    print(f"实际验证误差: {actual_error:.3f}mm")
    print(f"误差差异: {error_diff:.3f}mm")
    
    if error_diff < 0.5:
        print("✅ 模型性能与声称一致！")
        status = "一致"
    elif error_diff < 2.0:
        print("⚠️ 模型性能与声称有小幅差异")
        status = "小幅差异"
    else:
        print("❌ 模型性能与声称差异较大")
        status = "差异较大"
    
    # 保存验证报告
    verification_report = {
        "verification_timestamp": datetime.now().isoformat(),
        "model_path": model_path,
        "expected_validation_error": expected_error,
        "actual_validation_error": actual_error,
        "actual_test_error": test_metrics['mean_distance'],
        "error_difference": error_diff,
        "status": status,
        "validation_metrics": val_metrics,
        "test_metrics": test_metrics,
        "dataset_config": {
            "validation_samples": len(val_dataset),
            "test_samples": len(test_dataset),
            "input_points": 4096,
            "output_keypoints": 12,
            "test_sample_ids": test_samples
        },
        "conclusion": f"模型实际验证误差{actual_error:.3f}mm，与声称的{expected_error:.3f}mm相比{status}"
    }
    
    # 保存报告
    report_dir = Path("results/6mm_model_verification")
    report_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = report_dir / f"6mm_model_verification_{timestamp}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(verification_report, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 验证报告已保存: {report_file}")
    
    return verification_report

def evaluate_model(model, data_loader, device):
    """评估模型性能"""
    model.eval()
    
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for batch in data_loader:
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            pred_keypoints = model(point_cloud)
            
            all_predictions.append(pred_keypoints)
            all_targets.append(keypoints)
    
    # 合并所有批次
    all_predictions = torch.cat(all_predictions, dim=0)
    all_targets = torch.cat(all_targets, dim=0)
    
    # 计算指标
    metrics = calculate_metrics(all_predictions, all_targets)
    
    return metrics

if __name__ == "__main__":
    report = test_6mm_model_correctly()
