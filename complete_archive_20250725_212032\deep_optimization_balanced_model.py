#!/usr/bin/env python3
"""
深度优化平衡模型
Deep Optimization for Balanced Model
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import os
from tqdm import tqdm
import torch.nn.functional as F

class AdvancedHeatmapNet(nn.Module):
    """高级Heatmap回归网络 - 内存优化版本"""

    def __init__(self, num_points=50000, num_keypoints=12):
        super(AdvancedHeatmapNet, self).__init__()

        self.num_points = num_points
        self.num_keypoints = num_keypoints

        # 多尺度特征提取 (减少通道数)
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)

        # 轻量级注意力机制 (减少embed_dim和heads)
        self.attention = nn.MultiheadAttention(embed_dim=512, num_heads=4, batch_first=True)

        # 全局特征 (减少容量)
        self.global_conv1 = nn.Conv1d(512, 512, 1)
        self.global_conv2 = nn.Conv1d(512, 1024, 1)

        # 特征融合 (简化)
        self.fusion_conv1 = nn.Conv1d(1024 + 256, 512, 1)  # 融合conv3和global
        self.fusion_conv2 = nn.Conv1d(512, 256, 1)

        # 性别感知分支 (简化)
        self.gender_branch = nn.Sequential(
            nn.Conv1d(256, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 2, 1),  # 2个性别特征
        )

        # Heatmap生成 (简化)
        self.heatmap_conv1 = nn.Conv1d(256 + 2, 128, 1)  # 加入性别特征
        self.heatmap_conv2 = nn.Conv1d(128, 64, 1)
        self.heatmap_conv3 = nn.Conv1d(64, num_keypoints, 1)

        # 残差连接
        self.residual_conv = nn.Conv1d(256, num_keypoints, 1)

        # 激活函数和正则化
        self.relu = nn.ReLU()
        self.leaky_relu = nn.LeakyReLU(0.1)
        self.dropout = nn.Dropout(0.3)

        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)

        # 权重初始化
        self._initialize_weights()
        
    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x, gender_hint=None):
        batch_size = x.size(0)
        x = x.transpose(2, 1)

        # 多尺度特征提取
        x1 = self.leaky_relu(self.bn1(self.conv1(x)))
        x2 = self.leaky_relu(self.bn2(self.conv2(x1)))
        x3 = self.leaky_relu(self.bn3(self.conv3(x2)))
        x4 = self.leaky_relu(self.bn4(self.conv4(x3)))

        # 轻量级注意力机制 (使用采样减少计算)
        # 采样点云以减少注意力计算量
        sample_indices = torch.randperm(self.num_points)[:5000]  # 采样5000个点
        x4_sampled = x4[:, :, sample_indices]

        x4_reshaped = x4_sampled.transpose(1, 2)  # (B, N_sample, C)
        attn_out, _ = self.attention(x4_reshaped, x4_reshaped, x4_reshaped)
        x4_attn_sampled = attn_out.transpose(1, 2)  # (B, C, N_sample)

        # 将注意力结果插值回原始大小
        x4_attn = torch.zeros_like(x4)
        x4_attn[:, :, sample_indices] = x4_attn_sampled

        # 全局特征
        global_feat = self.relu(self.global_conv1(x4_attn))
        global_feat = self.relu(self.global_conv2(global_feat))
        global_feat = torch.max(global_feat, 2, keepdim=True)[0]

        # 扩展全局特征
        global_feat_expanded = global_feat.repeat(1, 1, self.num_points)

        # 特征融合
        combined_feat = torch.cat([x3, global_feat_expanded], 1)

        # 特征融合
        fused = self.relu(self.fusion_conv1(combined_feat))
        fused = self.dropout(fused)
        fused = self.relu(self.fusion_conv2(fused))

        # 性别感知分支
        gender_feat = self.gender_branch(fused)
        gender_feat = torch.softmax(gender_feat, dim=1)

        # 融合性别特征
        heatmap_input = torch.cat([fused, gender_feat], 1)

        # 生成热图
        heatmap = self.relu(self.heatmap_conv1(heatmap_input))
        heatmap = self.relu(self.heatmap_conv2(heatmap))
        heatmap_main = self.heatmap_conv3(heatmap)

        # 残差连接
        heatmap_residual = self.residual_conv(fused)
        heatmap_combined = heatmap_main + heatmap_residual

        # 转置并应用softmax
        heatmap_combined = heatmap_combined.transpose(2, 1)

        # 对每个关键点的热图进行softmax
        heatmap_list = []
        for i in range(self.num_keypoints):
            hm_i = torch.softmax(heatmap_combined[:, :, i], dim=1)
            heatmap_list.append(hm_i.unsqueeze(2))

        final_heatmap = torch.cat(heatmap_list, dim=2)

        return final_heatmap, gender_feat

class FocalLoss(nn.Module):
    """Focal Loss for better hard example mining"""
    
    def __init__(self, alpha=1, gamma=2):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        
    def forward(self, pred, target):
        ce_loss = F.cross_entropy(pred, target, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1-pt)**self.gamma * ce_loss
        return focal_loss.mean()

def advanced_heatmap_loss(pred_heatmap, target_heatmap, gender_pred=None, gender_true=None):
    """高级热图损失函数"""

    # 检查并调整target_heatmap的维度
    if len(target_heatmap.shape) == 3 and target_heatmap.shape[1] == 12:
        # 如果是 (batch, 12, 50000)，转换为 (batch, 50000, 12)
        target_heatmap = target_heatmap.transpose(1, 2)

    # 主要的KL散度损失
    kl_loss = nn.KLDivLoss(reduction='batchmean')

    total_loss = 0
    batch_size, num_points, num_keypoints = pred_heatmap.shape

    # 热图损失
    for i in range(num_keypoints):
        log_pred = torch.log(pred_heatmap[:, :, i] + 1e-8)
        loss_i = kl_loss(log_pred, target_heatmap[:, :, i])
        total_loss += loss_i

    heatmap_loss = total_loss / num_keypoints

    # 性别分类损失 (如果提供)
    gender_loss = 0
    if gender_pred is not None and gender_true is not None:
        gender_loss = F.cross_entropy(gender_pred.mean(dim=2), gender_true.long())

    # 总损失
    total_loss = heatmap_loss + 0.1 * gender_loss

    return total_loss, heatmap_loss, gender_loss

def load_enhanced_dataset():
    """加载增强的数据集"""
    
    print("📊 加载增强的平衡数据集...")
    
    # 加载女性数据
    female_data = np.load("f3_reduced_12kp_female_augmented.npz", allow_pickle=True)
    female_pc = female_data['point_clouds']
    female_kp = female_data['keypoints']
    
    # 加载男性数据
    male_data = np.load("f3_reduced_12kp_male_augmented.npz", allow_pickle=True)
    male_pc = male_data['point_clouds']
    male_kp = male_data['keypoints']
    
    print(f"✅ 女性数据: {len(female_pc)}个样本")
    print(f"✅ 男性数据: {len(male_pc)}个样本")
    
    # 平衡数据量
    min_count = min(len(female_pc), len(male_pc))
    print(f"📊 平衡到: 各{min_count}个样本")
    
    # 随机采样
    female_indices = np.random.choice(len(female_pc), min_count, replace=False)
    male_indices = np.random.choice(len(male_pc), min_count, replace=False)
    
    # 合并数据
    all_pc = np.concatenate([female_pc[female_indices], male_pc[male_indices]])
    all_kp = np.concatenate([female_kp[female_indices], male_kp[male_indices]])
    
    # 性别标签
    gender_labels = np.concatenate([
        np.zeros(min_count),  # 0 = 女性
        np.ones(min_count)    # 1 = 男性
    ])
    
    # 生成热图
    print("🔥 生成高质量热图...")
    all_hm = []
    for i in tqdm(range(len(all_pc)), desc="生成热图"):
        hm = generate_enhanced_heatmap(all_kp[i], all_pc[i])
        all_hm.append(hm)
    all_hm = np.array(all_hm)
    
    print(f"📊 最终数据集:")
    print(f"   总样本: {len(all_pc)}")
    print(f"   点云形状: {all_pc.shape}")
    print(f"   关键点形状: {all_kp.shape}")
    print(f"   热图形状: {all_hm.shape}")
    
    return all_pc, all_kp, all_hm, gender_labels

def generate_enhanced_heatmap(keypoints, point_cloud, sigma=4.0):
    """生成增强的热图"""
    heatmaps = []
    
    for kp in keypoints:
        # 计算距离
        distances = np.linalg.norm(point_cloud - kp, axis=1)
        
        # 自适应sigma
        adaptive_sigma = sigma * (1 + 0.1 * np.random.randn())
        
        # 生成高斯分布
        heatmap = np.exp(-distances**2 / (2 * adaptive_sigma**2))
        
        # 归一化
        if np.sum(heatmap) > 0:
            heatmap = heatmap / np.sum(heatmap)
        
        heatmaps.append(heatmap)
    
    return np.array(heatmaps)

def train_deep_optimized_model():
    """训练深度优化模型"""
    
    print("🚀 开始深度优化训练")
    print("🎯 目标: 突破4mm，达到3mm级别")
    print("=" * 80)
    
    # 加载数据
    all_pc, all_kp, all_hm, gender_labels = load_enhanced_dataset()
    
    # 数据分割 - 更大的训练集
    n_samples = len(all_pc)
    n_train = int(n_samples * 0.75)  # 增加到75%
    n_val = int(n_samples * 0.125)   # 12.5%
    
    indices = np.random.permutation(n_samples)
    train_indices = indices[:n_train]
    val_indices = indices[n_train:n_train + n_val]
    test_indices = indices[n_train + n_val:]
    
    train_pc, train_kp, train_hm = all_pc[train_indices], all_kp[train_indices], all_hm[train_indices]
    val_pc, val_kp, val_hm = all_pc[val_indices], all_kp[val_indices], all_hm[val_indices]
    test_pc, test_kp, test_hm = all_pc[test_indices], all_kp[test_indices], all_hm[test_indices]
    
    train_gender = gender_labels[train_indices]
    val_gender = gender_labels[val_indices]
    test_gender = gender_labels[test_indices]
    
    print(f"📋 优化数据分割:")
    print(f"   训练集: {len(train_indices)}个样本 (75%)")
    print(f"   验证集: {len(val_indices)}个样本 (12.5%)")
    print(f"   测试集: {len(test_indices)}个样本 (12.5%)")
    
    # 设备设置
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ 使用设备: {device}")
    
    # 高级模型
    model = AdvancedHeatmapNet(num_points=50000, num_keypoints=12)
    model = model.to(device)
    
    print(f"🏗️ 高级模型架构:")
    print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"   特色: 注意力机制 + 性别感知 + 残差连接")
    
    # 高级优化器
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-3)
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=10, T_mult=2)
    
    # 训练参数
    num_epochs = 50  # 适中的训练轮数
    batch_size = 2   # 进一步减小batch size
    best_val_error = float('inf')
    patience = 15
    patience_counter = 0
    
    print(f"🎯 优化训练参数:")
    print(f"   训练轮数: {num_epochs}")
    print(f"   批次大小: {batch_size}")
    print(f"   初始学习率: 0.001")
    print(f"   优化器: AdamW + CosineAnnealing")
    print(f"   早停耐心: {patience}")
    
    print(f"\n🚀 开始深度优化训练...")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        n_train_batches = len(train_pc) // batch_size
        
        train_pbar = tqdm(range(n_train_batches), desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for i in train_pbar:
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(train_pc))
            
            batch_pc = torch.FloatTensor(train_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(train_kp[start_idx:end_idx]).to(device)
            batch_hm = torch.FloatTensor(train_hm[start_idx:end_idx]).to(device)
            batch_gender = torch.FloatTensor(train_gender[start_idx:end_idx]).to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            pred_hm, gender_pred = model(batch_pc)
            total_loss, hm_loss, gender_loss = advanced_heatmap_loss(
                pred_hm, batch_hm, gender_pred, batch_gender)
            
            # 反向传播
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)  # 梯度裁剪
            optimizer.step()
            
            train_loss += total_loss.item()
            train_pbar.set_postfix({
                'Loss': f'{total_loss.item():.4f}',
                'HM': f'{hm_loss:.4f}',
                'Gender': f'{gender_loss:.4f}'
            })
        
        avg_train_loss = train_loss / n_train_batches
        
        # 验证阶段
        model.eval()
        val_errors = []
        val_errors_female = []
        val_errors_male = []
        
        with torch.no_grad():
            n_val_batches = len(val_pc) // batch_size + (1 if len(val_pc) % batch_size > 0 else 0)
            
            for i in range(n_val_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(val_pc))
                
                batch_pc = torch.FloatTensor(val_pc[start_idx:end_idx]).to(device)
                batch_kp = torch.FloatTensor(val_kp[start_idx:end_idx]).to(device)
                batch_gender = val_gender[start_idx:end_idx]
                
                pred_hm, _ = model(batch_pc)
                pred_kp = extract_keypoints_from_heatmap(pred_hm.cpu(), batch_pc.cpu())
                
                for j in range(len(batch_kp)):
                    error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j].cpu(), dim=1))
                    val_errors.append(error.item())
                    
                    if batch_gender[j] == 0:  # 女性
                        val_errors_female.append(error.item())
                    else:  # 男性
                        val_errors_male.append(error.item())
        
        avg_val_error = np.mean(val_errors)
        avg_val_error_female = np.mean(val_errors_female) if val_errors_female else 0
        avg_val_error_male = np.mean(val_errors_male) if val_errors_male else 0
        
        # 学习率调度
        scheduler.step()
        
        print(f"\nEpoch {epoch+1}/{num_epochs}:")
        print(f"  训练损失: {avg_train_loss:.4f}")
        print(f"  验证误差 (总体): {avg_val_error:.2f}mm")
        print(f"  验证误差 (女性): {avg_val_error_female:.2f}mm")
        print(f"  验证误差 (男性): {avg_val_error_male:.2f}mm")
        print(f"  学习率: {scheduler.get_last_lr()[0]:.6f}")
        
        # 早停和模型保存
        if avg_val_error < best_val_error:
            best_val_error = avg_val_error
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'val_error': avg_val_error,
                'val_error_female': avg_val_error_female,
                'val_error_male': avg_val_error_male,
            }, 'best_deep_optimized_model.pth')
            print(f"  ✅ 保存最佳模型 (验证误差: {avg_val_error:.2f}mm)")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f"  ⏹️ 早停触发 (耐心: {patience})")
                break
    
    return test_pc, test_kp, test_gender

def extract_keypoints_from_heatmap(heatmap, point_cloud):
    """从热图中提取关键点坐标"""
    
    batch_size, num_points, num_keypoints = heatmap.shape
    keypoints = torch.zeros(batch_size, num_keypoints, 3)
    
    for b in range(batch_size):
        for k in range(num_keypoints):
            weights = heatmap[b, :, k]
            weighted_coords = point_cloud[b] * weights.unsqueeze(1)
            keypoint = torch.sum(weighted_coords, dim=0) / torch.sum(weights)
            keypoints[b, k] = keypoint
    
    return keypoints

def main():
    """主函数"""
    
    print("🚀 深度优化平衡模型")
    print("🎯 目标: 从4.18mm优化到3mm级别")
    print("💡 策略: 注意力机制 + 性别感知 + 高级训练")
    print("=" * 80)
    
    # 训练深度优化模型
    test_pc, test_kp, test_gender = train_deep_optimized_model()
    
    print(f"\n🧪 测试深度优化模型...")
    
    # 加载最佳模型进行测试
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    model = AdvancedHeatmapNet(num_points=50000, num_keypoints=12)
    
    checkpoint = torch.load('best_deep_optimized_model.pth')
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    # 测试
    test_errors = []
    test_errors_female = []
    test_errors_male = []
    
    batch_size = 3
    with torch.no_grad():
        n_test_batches = len(test_pc) // batch_size + (1 if len(test_pc) % batch_size > 0 else 0)
        
        for i in range(n_test_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(test_pc))
            
            batch_pc = torch.FloatTensor(test_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(test_kp[start_idx:end_idx]).to(device)
            batch_gender = test_gender[start_idx:end_idx]
            
            pred_hm, _ = model(batch_pc)
            pred_kp = extract_keypoints_from_heatmap(pred_hm.cpu(), batch_pc.cpu())
            
            for j in range(len(batch_kp)):
                error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j].cpu(), dim=1))
                test_errors.append(error.item())
                
                if batch_gender[j] == 0:  # 女性
                    test_errors_female.append(error.item())
                else:  # 男性
                    test_errors_male.append(error.item())
    
    final_test_error = np.mean(test_errors)
    final_test_error_female = np.mean(test_errors_female)
    final_test_error_male = np.mean(test_errors_male)
    
    print(f"\n🎉 深度优化结果!")
    print(f"=" * 80)
    print(f"📊 最终测试结果:")
    print(f"   测试误差 (总体): {final_test_error:.2f}mm")
    print(f"   测试误差 (女性): {final_test_error_female:.2f}mm")
    print(f"   测试误差 (男性): {final_test_error_male:.2f}mm")
    
    print(f"\n📈 优化效果对比:")
    print(f"   原始平衡模型: 4.18mm")
    print(f"   深度优化模型: {final_test_error:.2f}mm")
    improvement = (4.18 - final_test_error) / 4.18 * 100
    print(f"   性能提升: {improvement:.1f}%")
    
    if final_test_error < 3.5:
        print(f"🏆 突破3.5mm大关!")
    if final_test_error < 3.0:
        print(f"🚀 达到3mm级别!")
    
    return final_test_error

if __name__ == "__main__":
    main()
