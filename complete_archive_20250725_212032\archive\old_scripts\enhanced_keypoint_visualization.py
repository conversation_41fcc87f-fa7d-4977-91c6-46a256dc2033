#!/usr/bin/env python3
"""
增强的关键点可视化
创建清晰的点云模型，突出显示预测的关键点位置和名称
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps

# 定义关键点名称（根据医学解剖学）
KEYPOINT_NAMES = {
    0: "F3-1 (左髂前上棘)",
    1: "F3-2 (右髂前上棘)", 
    2: "F3-3 (左髂后上棘)",
    3: "F3-4 (右髂后上棘)",
    4: "F3-5 (左髂嵴)",
    5: "F3-6 (右髂嵴)",
    6: "F3-7 (骶骨岬)",
    7: "F3-8 (左骶髂关节)",
    8: "F3-9 (右骶髂关节)",
    9: "F3-10 (左坐骨棘)",
    10: "F3-11 (右坐骨棘)",
    11: "F3-12 (尾骨尖)"
}

# 简化的英文名称
KEYPOINT_NAMES_EN = {
    0: "L-ASIS",      # Left Anterior Superior Iliac Spine
    1: "R-ASIS",      # Right Anterior Superior Iliac Spine
    2: "L-PSIS",      # Left Posterior Superior Iliac Spine
    3: "R-PSIS",      # Right Posterior Superior Iliac Spine
    4: "L-IC",        # Left Iliac Crest
    5: "R-IC",        # Right Iliac Crest
    6: "SP",          # Sacral Promontory
    7: "L-SIJ",       # Left Sacroiliac Joint
    8: "R-SIJ",       # Right Sacroiliac Joint
    9: "L-IS",        # Left Ischial Spine
    10: "R-IS",       # Right Ischial Spine
    11: "CT"          # Coccyx Tip
}

def create_keypoint_color_scheme():
    """创建关键点颜色方案"""
    # 为不同类型的关键点分配不同颜色
    colors = {
        # 髂前上棘 - 红色系
        0: '#FF0000', 1: '#FF3333',
        # 髂后上棘 - 蓝色系  
        2: '#0000FF', 3: '#3333FF',
        # 髂嵴 - 绿色系
        4: '#00FF00', 5: '#33FF33',
        # 骶骨相关 - 紫色系
        6: '#8000FF', 7: '#9933FF', 8: '#B366FF',
        # 坐骨棘 - 橙色系
        9: '#FF8000', 10: '#FFB366',
        # 尾骨 - 黄色系
        11: '#FFD700'
    }
    return colors

def visualize_enhanced_keypoints(point_cloud, pred_keypoints, true_keypoints, confidences, 
                                sample_id, errors=None, save_path=None):
    """创建增强的关键点可视化"""
    
    print(f"🎨 **创建增强关键点可视化 - 样本{sample_id}**")
    
    # 创建颜色方案
    colors = create_keypoint_color_scheme()
    
    # 创建图形
    fig = plt.figure(figsize=(20, 15))
    
    # 创建多个视角的子图
    views = [
        (30, 45, "前斜视图"),
        (30, 135, "左侧视图"), 
        (30, 225, "后斜视图"),
        (30, 315, "右侧视图"),
        (90, 0, "俯视图"),
        (0, 0, "正视图")
    ]
    
    for idx, (elev, azim, view_name) in enumerate(views):
        ax = fig.add_subplot(2, 3, idx+1, projection='3d')
        
        # 绘制背景点云 - 使用统一的灰色
        ax.scatter(point_cloud[:, 0], point_cloud[:, 1], point_cloud[:, 2], 
                  c='lightgray', s=0.1, alpha=0.3, label='Point Cloud')
        
        # 绘制预测关键点
        for i in range(len(pred_keypoints)):
            color = colors[i]
            name = KEYPOINT_NAMES_EN[i]
            
            # 预测点 - 大圆圈
            ax.scatter(pred_keypoints[i, 0], pred_keypoints[i, 1], pred_keypoints[i, 2],
                      c=color, s=100, marker='o', edgecolor='black', linewidth=2,
                      alpha=0.8, label=f'Pred {name}' if idx == 0 else "")
            
            # 真实点 - 星形
            ax.scatter(true_keypoints[i, 0], true_keypoints[i, 1], true_keypoints[i, 2],
                      c='white', s=80, marker='*', edgecolor=color, linewidth=2,
                      alpha=0.9, label=f'True {name}' if idx == 0 else "")
            
            # 连接线显示误差
            ax.plot([pred_keypoints[i, 0], true_keypoints[i, 0]],
                   [pred_keypoints[i, 1], true_keypoints[i, 1]],
                   [pred_keypoints[i, 2], true_keypoints[i, 2]],
                   color=color, linestyle='--', alpha=0.6, linewidth=1)
            
            # 添加关键点标签（只在前两个视图中显示以避免拥挤）
            if idx < 2:
                # 计算标签位置（稍微偏移避免重叠）
                offset = np.array([2, 2, 2])  # 根据点云尺度调整
                label_pos = pred_keypoints[i] + offset
                
                # 添加文本标签
                ax.text(label_pos[0], label_pos[1], label_pos[2], 
                       f'{name}\n({confidences[i]:.2f})',
                       fontsize=8, ha='left', va='bottom',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.7),
                       color='white', fontweight='bold')
        
        # 设置视角
        ax.view_init(elev=elev, azim=azim)
        
        # 设置标题和标签
        ax.set_title(f'{view_name}', fontsize=12, fontweight='bold')
        ax.set_xlabel('X (mm)', fontsize=10)
        ax.set_ylabel('Y (mm)', fontsize=10)
        ax.set_zlabel('Z (mm)', fontsize=10)
        
        # 设置坐标轴范围一致
        if idx == 0:
            # 计算合适的显示范围
            all_points = np.vstack([point_cloud, pred_keypoints, true_keypoints])
            x_range = [np.min(all_points[:, 0]), np.max(all_points[:, 0])]
            y_range = [np.min(all_points[:, 1]), np.max(all_points[:, 1])]
            z_range = [np.min(all_points[:, 2]), np.max(all_points[:, 2])]
            
            # 添加一些边距
            margin = 10
            x_range = [x_range[0] - margin, x_range[1] + margin]
            y_range = [y_range[0] - margin, y_range[1] + margin]
            z_range = [z_range[0] - margin, z_range[1] + margin]
        
        ax.set_xlim(x_range)
        ax.set_ylim(y_range)
        ax.set_zlim(z_range)
        
        # 只在第一个子图显示图例
        if idx == 0:
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    
    # 添加总标题
    avg_error = np.mean(errors) if errors is not None else 0
    avg_confidence = np.mean(confidences)
    plt.suptitle(f'Enhanced Keypoint Visualization - Sample {sample_id}\n'
                f'Average Error: {avg_error:.2f}mm, Average Confidence: {avg_confidence:.3f}', 
                fontsize=16, fontweight='bold')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"   📊 增强可视化已保存: {save_path}")
    
    plt.close()

def create_keypoint_detail_view(point_cloud, pred_keypoints, true_keypoints, confidences, 
                               sample_id, errors, keypoint_idx):
    """创建单个关键点的详细视图"""
    
    print(f"🔍 **创建关键点{keypoint_idx}详细视图**")
    
    colors = create_keypoint_color_scheme()
    target_color = colors[keypoint_idx]
    name = KEYPOINT_NAMES_EN[keypoint_idx]
    full_name = KEYPOINT_NAMES[keypoint_idx]
    
    fig = plt.figure(figsize=(18, 6))
    
    # 三个不同的视角
    views = [(30, 45), (30, 135), (90, 0)]
    view_names = ["斜视图", "侧视图", "俯视图"]
    
    for i, ((elev, azim), view_name) in enumerate(zip(views, view_names)):
        ax = fig.add_subplot(1, 3, i+1, projection='3d')
        
        # 计算关键点周围的局部区域
        center = pred_keypoints[keypoint_idx]
        radius = 20  # 显示半径
        
        # 筛选局部点云
        distances = np.linalg.norm(point_cloud - center, axis=1)
        local_mask = distances < radius
        local_pc = point_cloud[local_mask]
        
        # 绘制局部点云
        ax.scatter(local_pc[:, 0], local_pc[:, 1], local_pc[:, 2], 
                  c='lightgray', s=1, alpha=0.4)
        
        # 绘制目标关键点 - 大而突出
        ax.scatter(pred_keypoints[keypoint_idx, 0], pred_keypoints[keypoint_idx, 1], 
                  pred_keypoints[keypoint_idx, 2], c=target_color, s=200, marker='o', 
                  edgecolor='black', linewidth=3, alpha=0.9, label='Predicted')
        
        ax.scatter(true_keypoints[keypoint_idx, 0], true_keypoints[keypoint_idx, 1], 
                  true_keypoints[keypoint_idx, 2], c='white', s=150, marker='*', 
                  edgecolor=target_color, linewidth=3, alpha=0.9, label='Ground Truth')
        
        # 误差连接线
        ax.plot([pred_keypoints[keypoint_idx, 0], true_keypoints[keypoint_idx, 0]],
               [pred_keypoints[keypoint_idx, 1], true_keypoints[keypoint_idx, 1]],
               [pred_keypoints[keypoint_idx, 2], true_keypoints[keypoint_idx, 2]],
               color='red', linestyle='-', linewidth=3, alpha=0.8, label='Error')
        
        # 绘制其他关键点作为参考 - 小而透明
        for j in range(len(pred_keypoints)):
            if j != keypoint_idx:
                other_center = pred_keypoints[j]
                if np.linalg.norm(other_center - center) < radius:  # 只显示附近的关键点
                    ax.scatter(pred_keypoints[j, 0], pred_keypoints[j, 1], pred_keypoints[j, 2],
                              c=colors[j], s=50, marker='o', alpha=0.5, edgecolor='gray')
                    ax.scatter(true_keypoints[j, 0], true_keypoints[j, 1], true_keypoints[j, 2],
                              c='white', s=40, marker='*', alpha=0.5, edgecolor=colors[j])
        
        # 设置视角
        ax.view_init(elev=elev, azim=azim)
        
        # 设置局部范围
        ax.set_xlim([center[0] - radius, center[0] + radius])
        ax.set_ylim([center[1] - radius, center[1] + radius])
        ax.set_zlim([center[2] - radius, center[2] + radius])
        
        ax.set_title(f'{view_name}', fontsize=12, fontweight='bold')
        ax.set_xlabel('X (mm)')
        ax.set_ylabel('Y (mm)')
        ax.set_zlabel('Z (mm)')
        
        if i == 0:
            ax.legend()
    
    # 添加详细信息
    plt.suptitle(f'Keypoint Detail View - {name}\n'
                f'{full_name}\n'
                f'Error: {errors[keypoint_idx]:.2f}mm, Confidence: {confidences[keypoint_idx]:.3f}', 
                fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    
    save_path = f'keypoint_detail_{sample_id}_kp{keypoint_idx}_{name}.png'
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"   📊 详细视图已保存: {save_path}")
    plt.close()

def create_keypoint_summary_table(pred_keypoints, true_keypoints, confidences, errors, sample_id):
    """创建关键点汇总表"""
    
    print(f"📋 **创建关键点汇总表**")
    
    fig, ax = plt.subplots(figsize=(16, 10))
    ax.axis('tight')
    ax.axis('off')
    
    # 准备表格数据
    headers = ['Index', 'Name (EN)', 'Name (CN)', 'Pred (X,Y,Z)', 'True (X,Y,Z)', 
               'Error (mm)', 'Confidence', 'Status']
    
    table_data = []
    colors = create_keypoint_color_scheme()
    
    for i in range(len(pred_keypoints)):
        name_en = KEYPOINT_NAMES_EN[i]
        name_cn = KEYPOINT_NAMES[i].split(' ')[1] if ' ' in KEYPOINT_NAMES[i] else KEYPOINT_NAMES[i]
        
        pred_str = f"({pred_keypoints[i, 0]:.1f}, {pred_keypoints[i, 1]:.1f}, {pred_keypoints[i, 2]:.1f})"
        true_str = f"({true_keypoints[i, 0]:.1f}, {true_keypoints[i, 1]:.1f}, {true_keypoints[i, 2]:.1f})"
        
        error = errors[i]
        confidence = confidences[i]
        
        # 状态评估
        if error < 3 and confidence > 0.3:
            status = "优秀"
            status_color = 'lightgreen'
        elif error < 5 and confidence > 0.2:
            status = "良好"
            status_color = 'lightyellow'
        elif error < 8:
            status = "一般"
            status_color = 'lightcoral'
        else:
            status = "需改进"
            status_color = 'lightpink'
        
        table_data.append([
            f'KP{i}', name_en, name_cn, pred_str, true_str, 
            f'{error:.2f}', f'{confidence:.3f}', status
        ])
    
    # 创建表格
    table = ax.table(cellText=table_data, colLabels=headers, cellLoc='center', loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(9)
    table.scale(1.2, 2)
    
    # 设置表格样式
    for i in range(len(headers)):
        table[(0, i)].set_facecolor('#4CAF50')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    # 根据状态设置行颜色
    for i, row_data in enumerate(table_data):
        status = row_data[-1]
        if status == "优秀":
            color = 'lightgreen'
        elif status == "良好":
            color = 'lightyellow'
        elif status == "一般":
            color = 'lightcoral'
        else:
            color = 'lightpink'
        
        for j in range(len(headers)):
            table[(i+1, j)].set_facecolor(color)
    
    # 添加统计信息
    avg_error = np.mean(errors)
    avg_confidence = np.mean(confidences)
    best_kp = np.argmin(errors)
    worst_kp = np.argmax(errors)
    
    stats_text = f"""
    样本统计信息 - {sample_id}
    
    平均误差: {avg_error:.2f} mm
    平均置信度: {avg_confidence:.3f}
    最佳关键点: KP{best_kp} ({KEYPOINT_NAMES_EN[best_kp]}) - {errors[best_kp]:.2f}mm
    最差关键点: KP{worst_kp} ({KEYPOINT_NAMES_EN[worst_kp]}) - {errors[worst_kp]:.2f}mm
    
    状态说明:
    优秀: 误差<3mm且置信度>0.3
    良好: 误差<5mm且置信度>0.2  
    一般: 误差<8mm
    需改进: 误差≥8mm
    """
    
    ax.text(0.02, 0.02, stats_text, transform=ax.transAxes, fontsize=10,
            verticalalignment='bottom', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.title(f'Keypoint Analysis Summary - Sample {sample_id}', fontsize=16, fontweight='bold', pad=20)
    
    save_path = f'keypoint_summary_table_{sample_id}.png'
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"   📊 汇总表已保存: {save_path}")
    plt.close()

def main():
    """主函数"""
    print("🎨 **增强的关键点可视化系统**")
    print("创建清晰突出的关键点标记和背景点云分离")
    print("=" * 80)
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model.eval()
        print(f"   ✅ 模型加载成功")
    except Exception as e:
        print(f"   ❌ 模型加载失败: {e}")
        return
    
    # 选择几个代表性样本
    demo_indices = [0, 5, 10]
    
    for idx in demo_indices:
        sample_id = sample_ids[idx]
        point_cloud = point_clouds[idx]
        true_keypoints = keypoints[idx]
        
        print(f"\n🎯 **处理样本: {sample_id}**")
        
        # 采样点云
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            pc_sampled = point_cloud[indices]
        else:
            pc_sampled = point_cloud
        
        # 预测
        pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
        
        with torch.no_grad():
            pred_heatmaps = model(pc_tensor)
        
        pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T
        pred_keypoints, confidences = extract_keypoints_from_heatmaps(
            pred_heatmaps_np.T, pc_sampled
        )
        
        # 计算误差
        errors = []
        for i in range(12):
            error = np.linalg.norm(pred_keypoints[i] - true_keypoints[i])
            errors.append(error)
        
        # 1. 创建增强的整体可视化
        visualize_enhanced_keypoints(
            pc_sampled, pred_keypoints, true_keypoints, confidences, 
            sample_id, errors, f'enhanced_keypoints_{sample_id}.png'
        )
        
        # 2. 创建汇总表
        create_keypoint_summary_table(
            pred_keypoints, true_keypoints, confidences, errors, sample_id
        )
        
        # 3. 为误差最大的关键点创建详细视图
        worst_kp_idx = np.argmax(errors)
        create_keypoint_detail_view(
            pc_sampled, pred_keypoints, true_keypoints, confidences, 
            sample_id, errors, worst_kp_idx
        )
        
        print(f"   平均误差: {np.mean(errors):.2f}mm")
        print(f"   平均置信度: {np.mean(confidences):.3f}")
    
    print(f"\n🎉 **增强可视化完成!**")
    print(f"✅ 清晰的背景点云 (灰色)")
    print(f"✅ 突出的关键点标记 (彩色)")
    print(f"✅ 关键点名称标签")
    print(f"✅ 多视角展示")
    print(f"✅ 详细汇总表")
    print(f"✅ 单点详细视图")

if __name__ == "__main__":
    main()
