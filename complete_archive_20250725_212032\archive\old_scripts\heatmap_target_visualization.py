#!/usr/bin/env python3
"""
热力图靶子可视化
展示模型预测的热力图分布，像靶子一样的彩虹效果
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps

# 关键点名称
KEYPOINT_NAMES = {
    0: "L-ASIS", 1: "R-ASIS", 2: "L-PSIS", 3: "R-PSIS",
    4: "L-IC", 5: "R-IC", 6: "SP", 7: "L-SIJ", 8: "R-SIJ",
    9: "L-IS", 10: "R-IS", 11: "CT"
}

def create_target_colormap():
    """创建靶子风格的彩虹色配色方案"""
    colors = [
        '#F0F0F0',  # 最外层 - 浅灰
        '#E6E6FA',  # 薰衣草色
        '#9370DB',  # 中紫色
        '#4169E1',  # 皇家蓝
        '#00BFFF',  # 深天蓝
        '#00FFFF',  # 青色
        '#00FF7F',  # 春绿色
        '#ADFF2F',  # 绿黄色
        '#FFFF00',  # 黄色
        '#FFD700',  # 金色
        '#FFA500',  # 橙色
        '#FF4500',  # 橙红色
        '#FF0000',  # 红色 - 靶心
        '#DC143C'   # 深红色 - 中心
    ]
    return LinearSegmentedColormap.from_list('target_rainbow', colors, N=256)

def visualize_heatmap_predictions(point_cloud, pred_heatmaps, true_keypoints,
                                pred_keypoints, sampled_pc, sample_id, show_keypoints=[0, 6, 11]):
    """可视化热力图预测结果"""

    print(f"🎯 Creating heatmap target visualization for sample {sample_id}")

    target_cmap = create_target_colormap()

    # 创建子图 - 显示选定的关键点
    fig = plt.figure(figsize=(24, 8))

    for i, kp_idx in enumerate(show_keypoints):
        ax = fig.add_subplot(1, 3, i+1, projection='3d')

        # 获取当前关键点的热力图 (基于采样后的点云)
        heatmap = pred_heatmaps[kp_idx]  # [sampled_N] - 每个采样点的热力图值

        # 使用采样后的点云和对应的热力图
        display_pc = sampled_pc
        display_heatmap = heatmap

        # 进一步采样用于显示
        if len(display_pc) > 5000:
            sample_indices = np.random.choice(len(display_pc), 5000, replace=False)
            display_pc = display_pc[sample_indices]
            display_heatmap = display_heatmap[sample_indices]
        
        # 归一化热力图值
        if np.max(display_heatmap) > 0:
            display_heatmap = display_heatmap / np.max(display_heatmap)
        
        # 分层显示不同置信度的点
        
        # 1. 背景点 - 低置信度 (0-0.1)
        bg_mask = display_heatmap < 0.1
        if np.any(bg_mask):
            ax.scatter(display_pc[bg_mask, 0], display_pc[bg_mask, 1], display_pc[bg_mask, 2],
                      c=display_heatmap[bg_mask], cmap=target_cmap, 
                      s=0.5, alpha=0.3, vmin=0, vmax=1)
        
        # 2. 外环 - 紫蓝色区域 (0.1-0.3)
        outer_mask = (display_heatmap >= 0.1) & (display_heatmap < 0.3)
        if np.any(outer_mask):
            ax.scatter(display_pc[outer_mask, 0], display_pc[outer_mask, 1], display_pc[outer_mask, 2],
                      c=display_heatmap[outer_mask], cmap=target_cmap, 
                      s=1.5, alpha=0.6, vmin=0, vmax=1)
        
        # 3. 中环 - 青绿色区域 (0.3-0.5)
        middle_mask = (display_heatmap >= 0.3) & (display_heatmap < 0.5)
        if np.any(middle_mask):
            ax.scatter(display_pc[middle_mask, 0], display_pc[middle_mask, 1], display_pc[middle_mask, 2],
                      c=display_heatmap[middle_mask], cmap=target_cmap, 
                      s=3, alpha=0.7, vmin=0, vmax=1)
        
        # 4. 内环 - 黄色区域 (0.5-0.7)
        inner_mask = (display_heatmap >= 0.5) & (display_heatmap < 0.7)
        if np.any(inner_mask):
            ax.scatter(display_pc[inner_mask, 0], display_pc[inner_mask, 1], display_pc[inner_mask, 2],
                      c=display_heatmap[inner_mask], cmap=target_cmap, 
                      s=6, alpha=0.8, vmin=0, vmax=1)
        
        # 5. 热区 - 橙红色区域 (0.7-0.9)
        hot_mask = (display_heatmap >= 0.7) & (display_heatmap < 0.9)
        if np.any(hot_mask):
            scatter = ax.scatter(display_pc[hot_mask, 0], display_pc[hot_mask, 1], display_pc[hot_mask, 2],
                               c=display_heatmap[hot_mask], cmap=target_cmap, 
                               s=12, alpha=0.9, vmin=0, vmax=1)
        
        # 6. 靶心 - 最高置信度区域 (0.9-1.0)
        peak_mask = display_heatmap >= 0.9
        if np.any(peak_mask):
            ax.scatter(display_pc[peak_mask, 0], display_pc[peak_mask, 1], display_pc[peak_mask, 2],
                      c='white', s=30, marker='o', 
                      edgecolor='darkred', linewidth=3, alpha=1.0, zorder=10)
        
        # 7. 标记真实和预测关键点
        true_kp = true_keypoints[kp_idx]
        pred_kp = pred_keypoints[kp_idx]
        
        # 真实关键点 - 大黑星
        ax.scatter(true_kp[0], true_kp[1], true_kp[2],
                  c='black', s=500, marker='*', 
                  edgecolor='white', linewidth=4, 
                  alpha=1.0, label='Ground Truth', zorder=15)
        
        # 预测关键点 - 大红十字
        ax.scatter(pred_kp[0], pred_kp[1], pred_kp[2],
                  c='red', s=400, marker='x', 
                  linewidth=6, alpha=1.0, label='Prediction', zorder=15)
        
        # 连接线
        ax.plot([true_kp[0], pred_kp[0]], 
                [true_kp[1], pred_kp[1]], 
                [true_kp[2], pred_kp[2]], 
                'k-', alpha=0.8, linewidth=4, zorder=12)
        
        # 计算误差和统计
        error = np.linalg.norm(pred_kp - true_kp)
        max_confidence = np.max(display_heatmap)
        high_conf_points = np.sum(display_heatmap > 0.7)
        
        # 设置标题和标签
        ax.set_title(f'{KEYPOINT_NAMES[kp_idx]}\n'
                    f'Error: {error:.1f}mm\n'
                    f'Max Conf: {max_confidence:.3f}\n'
                    f'Hot Points: {high_conf_points}',
                    fontsize=14, fontweight='bold', pad=20)
        
        ax.set_xlabel('X (mm)', fontsize=10)
        ax.set_ylabel('Y (mm)', fontsize=10)
        ax.set_zlabel('Z (mm)', fontsize=10)
        
        # 设置视角
        ax.view_init(elev=20, azim=45)
        ax.grid(True, alpha=0.3)
        
        # 添加图例（只在第一个子图）
        if i == 0:
            ax.legend(loc='upper left', fontsize=10)
        
        # 添加热力图统计
        stats_text = f'Confidence Distribution:\n'
        stats_text += f'Peak (>0.9): {np.sum(display_heatmap > 0.9)}\n'
        stats_text += f'Hot (0.7-0.9): {np.sum((display_heatmap >= 0.7) & (display_heatmap < 0.9))}\n'
        stats_text += f'Warm (0.5-0.7): {np.sum((display_heatmap >= 0.5) & (display_heatmap < 0.7))}\n'
        stats_text += f'Cool (0.3-0.5): {np.sum((display_heatmap >= 0.3) & (display_heatmap < 0.5))}\n'
        stats_text += f'Cold (<0.3): {np.sum(display_heatmap < 0.3)}'
        
        ax.text2D(0.02, 0.02, stats_text, transform=ax.transAxes, 
                 fontsize=8, verticalalignment='bottom',
                 bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # 添加颜色条
    if 'scatter' in locals():
        cbar = plt.colorbar(scatter, ax=fig.get_axes(), shrink=0.6, aspect=30)
        cbar.set_label('🎯 Heatmap Confidence\n(Gray→Purple→Blue→Cyan→Green→Yellow→Orange→Red)', 
                      fontsize=12, fontweight='bold')
    
    plt.suptitle(f'🎯 Heatmap Target Visualization 🎯\n'
                f'Sample {sample_id} - Model Prediction Confidence Distribution', 
                fontsize=18, fontweight='bold')
    
    plt.tight_layout(rect=[0, 0, 0.95, 0.9])
    
    # 保存
    filename = f'heatmap_targets_{sample_id}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"   🎯 Heatmap target visualization saved: {filename}")
    plt.close()

def create_all_keypoints_heatmap_grid(point_cloud, pred_heatmaps, true_keypoints,
                                    pred_keypoints, sampled_pc, sample_id):
    """创建所有关键点的热力图网格"""

    print(f"🌈 Creating complete heatmap grid for sample {sample_id}")

    target_cmap = create_target_colormap()

    # 创建4x3网格
    fig = plt.figure(figsize=(24, 32))

    for kp_idx in range(12):
        ax = fig.add_subplot(4, 3, kp_idx + 1, projection='3d')

        # 获取当前关键点的热力图
        heatmap = pred_heatmaps[kp_idx]

        # 使用采样后的点云
        display_pc = sampled_pc
        display_heatmap = heatmap

        # 进一步采样用于显示
        if len(display_pc) > 3000:
            sample_indices = np.random.choice(len(display_pc), 3000, replace=False)
            display_pc = display_pc[sample_indices]
            display_heatmap = display_heatmap[sample_indices]
        
        # 归一化
        if np.max(display_heatmap) > 0:
            display_heatmap = display_heatmap / np.max(display_heatmap)
        
        # 显示热力图 - 简化版本
        scatter = ax.scatter(display_pc[:, 0], display_pc[:, 1], display_pc[:, 2],
                           c=display_heatmap, cmap=target_cmap, 
                           s=2, alpha=0.7, vmin=0, vmax=1)
        
        # 标记关键点
        true_kp = true_keypoints[kp_idx]
        pred_kp = pred_keypoints[kp_idx]
        
        ax.scatter(true_kp[0], true_kp[1], true_kp[2],
                  c='black', s=200, marker='*', 
                  edgecolor='white', linewidth=2, alpha=1.0, zorder=10)
        
        ax.scatter(pred_kp[0], pred_kp[1], pred_kp[2],
                  c='red', s=150, marker='x', 
                  linewidth=4, alpha=1.0, zorder=10)
        
        # 连接线
        ax.plot([true_kp[0], pred_kp[0]], 
                [true_kp[1], pred_kp[1]], 
                [true_kp[2], pred_kp[2]], 
                'k-', alpha=0.8, linewidth=2)
        
        # 计算误差
        error = np.linalg.norm(pred_kp - true_kp)
        max_conf = np.max(display_heatmap)
        
        ax.set_title(f'{KEYPOINT_NAMES[kp_idx]}\n'
                    f'Error: {error:.1f}mm\n'
                    f'Conf: {max_conf:.3f}',
                    fontsize=12, fontweight='bold')
        
        ax.set_xlabel('X', fontsize=8)
        ax.set_ylabel('Y', fontsize=8)
        ax.set_zlabel('Z', fontsize=8)
        ax.tick_params(labelsize=6)
        ax.view_init(elev=20, azim=45)
        ax.grid(True, alpha=0.3)
    
    plt.suptitle(f'🌈 Complete Heatmap Grid 🌈\n'
                f'Sample {sample_id} - All 12 Keypoints with Confidence Distribution', 
                fontsize=20, fontweight='bold')
    
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    
    # 保存
    filename = f'complete_heatmap_grid_{sample_id}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"   🌈 Complete heatmap grid saved: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🎯 Heatmap Target Visualization")
    print("Display model predictions as rainbow target heatmaps")
    print("=" * 60)
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model.eval()
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # 选择样本进行展示
    demo_samples = [0, 1, 2]  # 展示前3个样本
    
    for sample_idx in demo_samples:
        sample_id = sample_ids[sample_idx]
        point_cloud = point_clouds[sample_idx]
        true_keypoints = keypoints[sample_idx]
        
        print(f"\n🎯 Processing sample: {sample_id}")
        
        # 采样点云用于预测
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            pc_sampled = point_cloud[indices]
        else:
            pc_sampled = point_cloud
        
        # 预测
        pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
        
        with torch.no_grad():
            pred_heatmaps = model(pc_tensor)
        
        # 获取热力图和关键点
        pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T  # [N, 12]
        pred_keypoints, confidences = extract_keypoints_from_heatmaps(
            pred_heatmaps_np.T, pc_sampled
        )
        
        print(f"   📊 Point cloud size: {len(point_cloud)}")
        print(f"   🎯 Predicted {len(pred_keypoints)} keypoints")
        print(f"   📈 Average confidence: {np.mean(confidences):.3f}")
        
        # 创建靶子可视化 - 选择3个代表性关键点
        visualize_heatmap_predictions(
            point_cloud, pred_heatmaps_np.T, true_keypoints,
            pred_keypoints, pc_sampled, sample_id, show_keypoints=[0, 6, 11]
        )

        # 创建完整网格
        create_all_keypoints_heatmap_grid(
            point_cloud, pred_heatmaps_np.T, true_keypoints,
            pred_keypoints, pc_sampled, sample_id
        )
    
    print(f"\n🎉 Heatmap Target Visualization Complete!")
    print("✅ Rainbow target effects with confidence distribution")
    print("✅ Individual keypoint analysis")
    print("✅ Complete 4x3 grid layout")
    print("✅ Statistical confidence breakdown")

if __name__ == "__main__":
    main()
