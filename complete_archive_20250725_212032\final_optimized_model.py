#!/usr/bin/env python3
"""
最终优化模型
Final Optimized Model
基于所有实验的最佳实践，追求极致性能
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import json
from tqdm import tqdm

class UltimatePointNet57(nn.Module):
    """终极PointNet57 - 基于所有验证的最佳实践"""
    
    def __init__(self, num_keypoints=57, dropout_rate=0.15):
        super(UltimatePointNet57, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 优化的特征提取 - 基于验证的最佳配置
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 精心设计的残差连接
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        # 优化的回归头 - 更深但不过度复杂
        self.fc1 = nn.Linear(1024, 768)
        self.fc2 = nn.Linear(768, 512)
        self.fc3 = nn.Linear(512, 256)
        self.fc4 = nn.Linear(256, 128)
        self.fc5 = nn.Linear(128, num_keypoints * 3)
        
        # 批归一化
        self.fc_bn1 = nn.BatchNorm1d(768)
        self.fc_bn2 = nn.BatchNorm1d(512)
        self.fc_bn3 = nn.BatchNorm1d(256)
        self.fc_bn4 = nn.BatchNorm1d(128)
        
        # 自适应Dropout
        self.dropout1 = nn.Dropout(dropout_rate)
        self.dropout2 = nn.Dropout(dropout_rate * 1.2)
        self.dropout3 = nn.Dropout(dropout_rate * 1.5)
        
        # 权重初始化
        self._initialize_weights()
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"🏗️ UltimatePointNet57: {total_params:,} 参数")
    
    def _initialize_weights(self):
        """改进的权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取 + 精心设计的残差连接
        x1 = F.relu(self.bn1(self.conv1(x)))           # [B, 64, N]
        x2 = F.relu(self.bn2(self.conv2(x1)))          # [B, 128, N]
        x3 = F.relu(self.bn3(self.conv3(x2)))          # [B, 256, N]
        x3_res = x3 + self.residual1(x1)               # 残差连接
        
        x4 = F.relu(self.bn4(self.conv4(x3_res)))      # [B, 512, N]
        x4_res = x4 + self.residual2(x2)               # 残差连接
        
        x5 = F.relu(self.bn5(self.conv5(x4_res)))      # [B, 1024, N]
        
        # 全局最大池化
        global_feat = torch.max(x5, 2)[0]              # [B, 1024]
        
        # 优化的回归头
        x = F.relu(self.fc_bn1(self.fc1(global_feat)))
        x = self.dropout1(x)
        
        x = F.relu(self.fc_bn2(self.fc2(x)))
        x = self.dropout2(x)
        
        x = F.relu(self.fc_bn3(self.fc3(x)))
        x = self.dropout3(x)
        
        x = F.relu(self.fc_bn4(self.fc4(x)))
        x = self.dropout1(x)  # 最后一层使用较小的dropout
        
        keypoints = self.fc5(x)
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

class AdaptiveLoss(nn.Module):
    """自适应损失函数"""
    
    def __init__(self, alpha=0.6, beta=0.3, gamma=0.1):
        super(AdaptiveLoss, self).__init__()
        self.alpha = alpha  # MSE权重
        self.beta = beta    # Smooth L1权重
        self.gamma = gamma  # Huber权重
        
    def forward(self, pred, target):
        mse_loss = F.mse_loss(pred, target)
        smooth_l1_loss = F.smooth_l1_loss(pred, target)
        huber_loss = F.huber_loss(pred, target, delta=0.5)
        
        return self.alpha * mse_loss + self.beta * smooth_l1_loss + self.gamma * huber_loss

class Dataset57(Dataset):
    def __init__(self, point_clouds, keypoints):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def create_ultimate_normalization(point_clouds, keypoints_57):
    """创建终极归一化策略"""
    print("🔧 执行终极归一化策略...")
    
    normalized_pc = []
    normalized_kp = []
    scalers = []
    
    for i in range(len(point_clouds)):
        pc = point_clouds[i].copy()
        kp = keypoints_57[i].copy()
        
        # 使用全局归一化，但保持F3中心的优势
        f3_center = np.mean(kp[38:57], axis=0)
        
        # 以F3中心为基准进行中心化
        pc_centered = pc - f3_center
        kp_centered = kp - f3_center
        
        # 合并数据进行统一归一化
        combined_data = np.vstack([pc_centered, kp_centered])
        
        scaler = StandardScaler()
        combined_normalized = scaler.fit_transform(combined_data)
        
        pc_normalized = combined_normalized[:len(pc)]
        kp_normalized = combined_normalized[len(pc):]
        
        normalized_pc.append(pc_normalized)
        normalized_kp.append(kp_normalized)
        scalers.append({'scaler': scaler, 'f3_center': f3_center})
    
    normalized_pc = np.array(normalized_pc)
    normalized_kp = np.array(normalized_kp)
    
    return normalized_pc, normalized_kp, scalers

def train_ultimate_model(model, train_loader, val_loader, epochs=200, device='cuda'):
    """训练终极模型"""
    
    print(f"🚀 训练终极优化模型...")
    print(f"   集成所有验证的最佳实践")
    
    model = model.to(device)
    
    # 多阶段学习率策略
    optimizer = optim.AdamW(model.parameters(), lr=0.0012, weight_decay=8e-5)
    
    # 更激进的学习率调度
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer, max_lr=0.0012, epochs=epochs, 
        steps_per_epoch=len(train_loader),
        pct_start=0.1, anneal_strategy='cos'
    )
    
    criterion = AdaptiveLoss(alpha=0.6, beta=0.3, gamma=0.1)
    
    history = {'train_loss': [], 'val_loss': [], 'train_error': [], 'val_error': []}
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 40
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            loss.backward()
            
            # 自适应梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.3)
            
            optimizer.step()
            scheduler.step()
            
            train_loss += loss.item()
            
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                val_loss += loss.item()
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_error'].append(train_error)
        history['val_error'].append(val_error)
        
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_ultimate_model.pth')
        else:
            patience_counter += 1
        
        current_lr = optimizer.param_groups[0]['lr']
        
        if epoch % 15 == 0 or epoch < 5:
            print(f"Epoch {epoch+1:3d}: "
                  f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
                  f"Train Error: {train_error:.4f}, Val Error: {val_error:.4f}, "
                  f"LR: {current_lr:.2e}")
        
        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    model.load_state_dict(torch.load('best_ultimate_model.pth'))
    return history

def test_ultimate_model(model, test_loader, scalers, test_indices, device='cuda'):
    """测试终极模型"""
    
    print("🔍 测试终极优化模型...")
    
    model = model.to(device)
    model.eval()
    
    test_predictions = []
    test_targets = []
    
    with torch.no_grad():
        for batch_pc, batch_kp in test_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            predicted = model(batch_pc)
            
            test_predictions.append(predicted.cpu().numpy())
            test_targets.append(batch_kp.cpu().numpy())
    
    test_predictions = np.vstack(test_predictions)
    test_targets = np.vstack(test_targets)
    
    # 反归一化
    real_predictions = []
    real_targets = []
    
    for i, orig_idx in enumerate(test_indices):
        if i < len(test_predictions):
            pred_norm = test_predictions[i]
            target_norm = test_targets[i]
            
            scaler_info = scalers[orig_idx]
            scaler = scaler_info['scaler']
            f3_center = scaler_info['f3_center']
            
            # 反归一化
            dummy_pc = np.zeros((50000, 3))
            
            combined_pred = np.vstack([dummy_pc, pred_norm])
            combined_pred_denorm = scaler.inverse_transform(combined_pred)
            pred_real = combined_pred_denorm[50000:] + f3_center
            
            combined_target = np.vstack([dummy_pc, target_norm])
            combined_target_denorm = scaler.inverse_transform(combined_target)
            target_real = combined_target_denorm[50000:] + f3_center
            
            real_predictions.append(pred_real)
            real_targets.append(target_real)
    
    real_predictions = np.array(real_predictions)
    real_targets = np.array(real_targets)
    
    # 计算误差
    total_error = 0.0
    region_errors = {'F1': [], 'F2': [], 'F3': []}
    all_errors = []
    
    for i in range(len(real_predictions)):
        pred = real_predictions[i]
        target = real_targets[i]
        
        distances = np.linalg.norm(pred - target, axis=1)
        total_error += np.mean(distances)
        all_errors.extend(distances)
        
        # 分区域
        region_errors['F1'].extend(distances[0:19])
        region_errors['F2'].extend(distances[19:38])
        region_errors['F3'].extend(distances[38:57])
    
    avg_error = total_error / len(real_predictions)
    
    # 计算准确率
    accuracy_5mm = np.mean(np.array(all_errors) < 5.0) * 100
    accuracy_10mm = np.mean(np.array(all_errors) < 10.0) * 100
    accuracy_8mm = np.mean(np.array(all_errors) < 8.0) * 100
    
    print(f"\n🎯 终极优化模型结果:")
    print(f"   整体平均误差: {avg_error:.2f}mm")
    
    for region, errors in region_errors.items():
        if errors:
            mean_error = np.mean(errors)
            std_error = np.std(errors)
            print(f"   {region}区域: {mean_error:.2f}±{std_error:.2f}mm")
    
    print(f"   医疗级准确率:")
    print(f"     <5mm: {accuracy_5mm:.1f}%")
    print(f"     <8mm: {accuracy_8mm:.1f}%")
    print(f"     <10mm: {accuracy_10mm:.1f}%")
    
    return avg_error, region_errors, accuracy_5mm, accuracy_8mm, accuracy_10mm

def main():
    """主函数"""
    
    print("🎯 终极优化模型")
    print("集成所有验证的最佳实践，追求极致性能")
    print("=" * 80)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载高质量数据集
    print("📊 加载高质量数据集...")
    data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
    
    point_clouds = data['point_clouds']
    keypoints_57 = data['keypoints_57']
    sample_ids = data['sample_ids']
    
    print(f"✅ 数据集加载完成: {len(sample_ids)} 个样本")
    
    # 终极归一化
    normalized_pc, normalized_kp, scalers = create_ultimate_normalization(
        point_clouds, keypoints_57
    )
    
    # 数据划分
    indices = np.arange(len(normalized_pc))
    train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
    train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
    
    # 创建数据集
    train_dataset = Dataset57(normalized_pc[train_indices], normalized_kp[train_indices])
    val_dataset = Dataset57(normalized_pc[val_indices], normalized_kp[val_indices])
    test_dataset = Dataset57(normalized_pc[test_indices], normalized_kp[test_indices])
    
    # 数据加载器
    batch_size = 8
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    
    print(f"📋 数据划分: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    
    # 创建终极模型
    model = UltimatePointNet57(dropout_rate=0.15)
    
    # 训练模型
    history = train_ultimate_model(model, train_loader, val_loader, epochs=200, device=device)
    
    # 测试模型
    avg_error, region_errors, acc_5mm, acc_8mm, acc_10mm = test_ultimate_model(
        model, test_loader, scalers, test_indices, device=device
    )
    
    print(f"\n📊 最终性能对比:")
    print(f"   Unified基线: 16.71mm")
    print(f"   高质量数据集: 15.49mm")
    print(f"   归一化优化: 11.81mm")
    print(f"   精密优化: 12.22mm")
    print(f"   终极优化: {avg_error:.2f}mm")
    
    # 计算改进幅度
    improvement_vs_best = (11.81 - avg_error) / 11.81 * 100
    improvement_vs_baseline = (16.71 - avg_error) / 16.71 * 100
    
    print(f"\n💡 改进幅度:")
    print(f"   相比最佳(11.81mm): {improvement_vs_best:+.1f}%")
    print(f"   相比基线(16.71mm): {improvement_vs_baseline:+.1f}%")
    
    # 保存结果
    results = {
        'avg_error': float(avg_error),
        'region_errors': {k: float(np.mean(v)) for k, v in region_errors.items()},
        'accuracy_5mm': float(acc_5mm),
        'accuracy_8mm': float(acc_8mm),
        'accuracy_10mm': float(acc_10mm),
        'improvement_vs_best': float(improvement_vs_best),
        'improvement_vs_baseline': float(improvement_vs_baseline),
        'training_history': history,
        'ultimate_features': [
            'f3_centered_ultimate_normalization',
            'optimized_residual_connections',
            'adaptive_dropout_strategy',
            'multi_component_loss_function',
            'one_cycle_learning_rate',
            'adaptive_gradient_clipping'
        ]
    }
    
    with open('ultimate_optimized_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    if avg_error < 10.0:
        print(f"\n🎉 终极优化模型突破10mm大关！医学级性能！")
        print(f"💡 成功集成了所有最佳实践")
    elif avg_error < 11.81:
        print(f"\n✅ 终极优化模型超越了之前的最佳结果！")
    else:
        print(f"\n⚠️ 终极优化未达预期，简单方法仍然最好")
    
    print(f"\n💾 详细结果已保存: ultimate_optimized_results.json")
    print(f"🎉 终极优化模型训练完成！")

if __name__ == "__main__":
    main()
