"""
尺度校正改进模型
基于验证结果，添加尺度校正和改进的损失函数
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class ScaleCorrectedPointNet(nn.Module):
    """尺度校正的PointNet模型"""
    
    def __init__(self, num_keypoints=57, scale_correction=True):
        super().__init__()
        self.num_keypoints = num_keypoints
        self.scale_correction = scale_correction
        
        # 基础特征提取 (保持原有的成功架构)
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        
        # 主要回归分支
        self.fc1 = nn.Linear(256, 128)
        self.fc2 = nn.Linear(128, num_keypoints * 3)
        
        # 尺度预测分支 (新增)
        if scale_correction:
            self.scale_fc1 = nn.Linear(256, 64)
            self.scale_fc2 = nn.Linear(64, 1)  # 预测尺度因子
        
        self.dropout = nn.Dropout(0.2)
        
        # 学习到的尺度校正因子 (基于验证结果)
        self.register_buffer('scale_factor', torch.tensor(1.048))  # 1/0.954
        
    def forward(self, x):
        # x: [B, N, 3] -> [B, 3, N]
        x = x.transpose(1, 2)
        
        # 特征提取
        x = torch.relu(self.bn1(self.conv1(x)))
        x = torch.relu(self.bn2(self.conv2(x)))
        x = torch.relu(self.bn3(self.conv3(x)))
        
        # 全局最大池化
        global_feat = torch.max(x, dim=2)[0]  # [B, 256]
        
        # 主要回归分支
        x = torch.relu(self.fc1(global_feat))
        x = self.dropout(x)
        keypoints = self.fc2(x)  # [B, num_keypoints * 3]
        
        # 重塑
        keypoints = keypoints.view(-1, self.num_keypoints, 3)
        
        # 尺度校正
        if self.scale_correction:
            if hasattr(self, 'scale_fc1'):
                # 动态尺度预测
                scale_feat = torch.relu(self.scale_fc1(global_feat))
                predicted_scale = torch.sigmoid(self.scale_fc2(scale_feat)) * 2.0  # [0, 2]
                
                # 计算关键点中心
                center = torch.mean(keypoints, dim=1, keepdim=True)  # [B, 1, 3]
                
                # 应用动态尺度校正
                keypoints = center + (keypoints - center) * predicted_scale.unsqueeze(-1)
                
                return keypoints, predicted_scale.squeeze(-1)
            else:
                # 固定尺度校正
                center = torch.mean(keypoints, dim=1, keepdim=True)
                keypoints = center + (keypoints - center) * self.scale_factor
        
        return keypoints

class ScaleCorrectedLoss(nn.Module):
    """尺度校正损失函数"""
    
    def __init__(self, coord_weight=1.0, scale_weight=0.1, range_weight=0.05):
        super().__init__()
        self.coord_weight = coord_weight
        self.scale_weight = scale_weight
        self.range_weight = range_weight
        
    def forward(self, pred_keypoints, gt_keypoints, pred_scale=None):
        """
        计算多项损失
        
        Args:
            pred_keypoints: [B, N, 3] 预测关键点
            gt_keypoints: [B, N, 3] 真实关键点
            pred_scale: [B] 预测尺度因子 (可选)
        """
        batch_size = pred_keypoints.size(0)
        
        # 1. 基础坐标损失
        coord_loss = F.mse_loss(pred_keypoints, gt_keypoints)
        
        total_loss = self.coord_weight * coord_loss
        loss_dict = {'coord_loss': coord_loss.item()}
        
        # 2. 尺度损失 (如果有尺度预测)
        if pred_scale is not None:
            # 计算真实尺度
            pred_std = torch.std(pred_keypoints.view(batch_size, -1), dim=1)
            gt_std = torch.std(gt_keypoints.view(batch_size, -1), dim=1)
            target_scale = gt_std / (pred_std + 1e-8)
            
            scale_loss = F.mse_loss(pred_scale, target_scale)
            total_loss += self.scale_weight * scale_loss
            loss_dict['scale_loss'] = scale_loss.item()
        
        # 3. 范围损失 (确保预测范围合理)
        pred_flat = pred_keypoints.view(batch_size, -1)
        gt_flat = gt_keypoints.view(batch_size, -1)
        pred_range = torch.max(pred_flat, dim=1)[0] - torch.min(pred_flat, dim=1)[0]
        gt_range = torch.max(gt_flat, dim=1)[0] - torch.min(gt_flat, dim=1)[0]
        range_loss = F.mse_loss(pred_range, gt_range)
        
        total_loss += self.range_weight * range_loss
        loss_dict['range_loss'] = range_loss.item()
        
        loss_dict['total_loss'] = total_loss.item()
        
        return total_loss, loss_dict

def create_scale_corrected_model(num_keypoints=57, scale_correction=True, device='cuda'):
    """创建尺度校正模型"""
    model = ScaleCorrectedPointNet(num_keypoints, scale_correction)
    return model.to(device)

def apply_post_scale_correction(predictions, scale_factor=1.048):
    """
    后处理尺度校正
    
    Args:
        predictions: [B, N, 3] 预测关键点
        scale_factor: 尺度校正因子
    
    Returns:
        corrected_predictions: 校正后的预测
    """
    # 计算中心点
    center = torch.mean(predictions, dim=1, keepdim=True)
    
    # 以中心为基准进行缩放
    corrected = center + (predictions - center) * scale_factor
    
    return corrected

class AdaptiveScaleCorrector:
    """自适应尺度校正器"""
    
    def __init__(self, window_size=100):
        self.window_size = window_size
        self.scale_history = []
        
    def update_scale_factor(self, predictions, ground_truths):
        """根据新的预测更新尺度因子"""
        # 计算当前批次的尺度比例
        pred_scales = torch.std(predictions.view(predictions.size(0), -1), dim=1)
        gt_scales = torch.std(ground_truths.view(ground_truths.size(0), -1), dim=1)
        
        scale_ratios = (gt_scales / (pred_scales + 1e-8)).detach().cpu().numpy()
        
        # 更新历史记录
        self.scale_history.extend(scale_ratios.tolist())
        
        # 保持窗口大小
        if len(self.scale_history) > self.window_size:
            self.scale_history = self.scale_history[-self.window_size:]
        
        # 计算当前最佳尺度因子
        current_scale_factor = np.mean(self.scale_history)
        
        return current_scale_factor
    
    def get_current_scale_factor(self):
        """获取当前尺度因子"""
        if len(self.scale_history) == 0:
            return 1.048  # 默认值
        return np.mean(self.scale_history)

def test_scale_correction():
    """测试尺度校正功能"""
    print("测试尺度校正模型...")
    
    # 创建测试数据
    batch_size = 2
    num_points = 512
    num_keypoints = 57
    
    point_cloud = torch.randn(batch_size, num_points, 3)
    gt_keypoints = torch.randn(batch_size, num_keypoints, 3) * 20  # 模拟真实尺度
    
    # 测试模型
    model = create_scale_corrected_model(scale_correction=True, device='cpu')
    criterion = ScaleCorrectedLoss()
    
    # 前向传播
    output = model(point_cloud)
    
    if isinstance(output, tuple):
        pred_keypoints, pred_scale = output
        loss, loss_dict = criterion(pred_keypoints, gt_keypoints, pred_scale)
        print(f"预测尺度因子: {pred_scale}")
    else:
        pred_keypoints = output
        loss, loss_dict = criterion(pred_keypoints, gt_keypoints)
    
    print(f"损失详情: {loss_dict}")
    print(f"预测关键点形状: {pred_keypoints.shape}")
    
    # 测试后处理校正
    corrected_pred = apply_post_scale_correction(pred_keypoints, scale_factor=1.048)
    print(f"校正后关键点形状: {corrected_pred.shape}")
    
    # 测试自适应校正器
    corrector = AdaptiveScaleCorrector()
    adaptive_scale = corrector.update_scale_factor(pred_keypoints, gt_keypoints)
    print(f"自适应尺度因子: {adaptive_scale:.3f}")
    
    print("✅ 尺度校正模型测试完成!")

if __name__ == "__main__":
    test_scale_correction()
