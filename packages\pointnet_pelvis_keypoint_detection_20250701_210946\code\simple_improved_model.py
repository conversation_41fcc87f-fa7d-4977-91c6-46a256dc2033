"""
简化的改进模型
回到直接坐标回归，但使用更好的架构和损失函数
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class SimpleImprovedPointNet(nn.Module):
    """简化但改进的PointNet模型"""
    
    def __init__(self, num_keypoints=57, input_dim=3):
        super().__init__()
        self.num_keypoints = num_keypoints
        self.input_dim = input_dim
        
        # 更深的特征提取
        self.conv1 = nn.Conv1d(input_dim, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 全局特征
        self.global_conv = nn.Conv1d(1024, 2048, 1)
        self.global_bn = nn.BatchNorm1d(2048)
        
        # 改进的回归头 - 输入维度是4096 (2048*2)
        self.regression_head = ImprovedRegressionHead(4096, num_keypoints)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        """
        x: [B, N, 3] - 输入点云
        返回: [B, num_keypoints, 3] - 关键点坐标
        """
        # 转换维度: [B, N, 3] -> [B, 3, N]
        x = x.transpose(1, 2)
        
        # 特征提取
        x1 = F.relu(self.bn1(self.conv1(x)))      # [B, 64, N]
        x2 = F.relu(self.bn2(self.conv2(x1)))     # [B, 128, N]
        x3 = F.relu(self.bn3(self.conv3(x2)))     # [B, 256, N]
        x4 = F.relu(self.bn4(self.conv4(x3)))     # [B, 512, N]
        x5 = F.relu(self.bn5(self.conv5(x4)))     # [B, 1024, N]
        
        # 全局特征
        global_feat = F.relu(self.global_bn(self.global_conv(x5)))  # [B, 2048, N]
        
        # 全局池化 - 使用平均池化和最大池化的组合
        avg_feat = torch.mean(global_feat, dim=2)  # [B, 2048]
        max_feat = torch.max(global_feat, dim=2)[0]  # [B, 2048]
        combined_feat = torch.cat([avg_feat, max_feat], dim=1)  # [B, 4096]
        
        # Dropout
        combined_feat = self.dropout(combined_feat)
        
        # 关键点回归
        keypoints = self.regression_head(combined_feat)  # [B, num_keypoints, 3]
        
        return keypoints

class ImprovedRegressionHead(nn.Module):
    """改进的回归头"""
    
    def __init__(self, input_dim, num_keypoints):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 更深的MLP
        self.fc1 = nn.Linear(input_dim, 1024)
        self.fc2 = nn.Linear(1024, 512)
        self.fc3 = nn.Linear(512, 256)
        self.fc4 = nn.Linear(256, 128)
        self.fc5 = nn.Linear(128, num_keypoints * 3)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(1024)
        self.bn2 = nn.BatchNorm1d(512)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(128)
        
        # Dropout
        self.dropout1 = nn.Dropout(0.4)
        self.dropout2 = nn.Dropout(0.3)
        self.dropout3 = nn.Dropout(0.2)
        
    def forward(self, x):
        """
        x: [B, input_dim]
        返回: [B, num_keypoints, 3]
        """
        x = F.relu(self.bn1(self.fc1(x)))
        x = self.dropout1(x)
        
        x = F.relu(self.bn2(self.fc2(x)))
        x = self.dropout2(x)
        
        x = F.relu(self.bn3(self.fc3(x)))
        x = self.dropout3(x)
        
        x = F.relu(self.bn4(self.fc4(x)))
        
        # 输出关键点坐标
        x = self.fc5(x)  # [B, num_keypoints * 3]
        
        # 重塑为关键点格式
        x = x.view(-1, self.num_keypoints, 3)  # [B, num_keypoints, 3]
        
        return x

class ImprovedKeypointLoss(nn.Module):
    """改进的关键点检测损失函数"""
    
    def __init__(self, smooth_l1_weight=1.0, mse_weight=0.5, geometric_weight=0.2, 
                 huber_delta=1.0):
        super().__init__()
        self.smooth_l1_weight = smooth_l1_weight
        self.mse_weight = mse_weight
        self.geometric_weight = geometric_weight
        self.huber_delta = huber_delta
        
        self.smooth_l1_loss = nn.SmoothL1Loss()
        self.mse_loss = nn.MSELoss()
        self.huber_loss = nn.HuberLoss(delta=huber_delta)
        
    def forward(self, pred_keypoints, gt_keypoints):
        """
        pred_keypoints: [B, num_keypoints, 3]
        gt_keypoints: [B, num_keypoints, 3]
        """
        # 基础回归损失
        smooth_l1 = self.smooth_l1_loss(pred_keypoints, gt_keypoints)
        mse = self.mse_loss(pred_keypoints, gt_keypoints)
        huber = self.huber_loss(pred_keypoints, gt_keypoints)
        
        # 几何一致性损失（距离保持）
        geometric_loss = self.geometric_consistency_loss(pred_keypoints, gt_keypoints)
        
        # 总损失
        total_loss = (self.smooth_l1_weight * smooth_l1 + 
                     self.mse_weight * mse + 
                     self.geometric_weight * geometric_loss +
                     0.3 * huber)  # 添加Huber损失
        
        return {
            'total_loss': total_loss,
            'smooth_l1_loss': smooth_l1,
            'mse_loss': mse,
            'huber_loss': huber,
            'geometric_loss': geometric_loss
        }
    
    def geometric_consistency_loss(self, pred_keypoints, gt_keypoints):
        """几何一致性损失 - 保持关键点之间的相对距离"""
        batch_size, num_keypoints, _ = pred_keypoints.shape
        
        if num_keypoints < 2:
            return torch.tensor(0.0, device=pred_keypoints.device)
        
        # 只计算部分关键点对的距离，避免计算量过大
        num_pairs = min(100, num_keypoints * (num_keypoints - 1) // 2)
        
        # 随机选择关键点对
        indices = torch.randperm(num_keypoints)[:min(20, num_keypoints)]
        
        if len(indices) < 2:
            return torch.tensor(0.0, device=pred_keypoints.device)
        
        # 计算选定关键点之间的距离
        pred_subset = pred_keypoints[:, indices, :]  # [B, subset, 3]
        gt_subset = gt_keypoints[:, indices, :]      # [B, subset, 3]
        
        pred_distances = self.compute_pairwise_distances(pred_subset)
        gt_distances = self.compute_pairwise_distances(gt_subset)
        
        # 距离保持损失
        distance_loss = F.mse_loss(pred_distances, gt_distances)
        
        return distance_loss
    
    def compute_pairwise_distances(self, keypoints):
        """计算关键点之间的成对距离"""
        # keypoints: [B, N, 3]
        batch_size, num_keypoints, _ = keypoints.shape
        
        # 扩展维度进行广播
        keypoints_i = keypoints.unsqueeze(2)  # [B, N, 1, 3]
        keypoints_j = keypoints.unsqueeze(1)  # [B, 1, N, 3]
        
        # 计算欧几里得距离
        distances = torch.norm(keypoints_i - keypoints_j, dim=3)  # [B, N, N]
        
        return distances

def create_simple_improved_model(num_keypoints=57, device='cuda'):
    """创建简化改进模型"""
    model = SimpleImprovedPointNet(num_keypoints=num_keypoints)
    model = model.to(device)
    return model

def create_improved_loss():
    """创建改进的损失函数"""
    return ImprovedKeypointLoss(
        smooth_l1_weight=1.0,
        mse_weight=0.5,
        geometric_weight=0.1,  # 减小几何损失权重
        huber_delta=2.0
    )

if __name__ == "__main__":
    # 测试模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    model = create_simple_improved_model(num_keypoints=57, device=device)
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    print(f"模型参数总数: {total_params:,}")
    
    # 测试输入
    batch_size = 2
    num_points = 1024
    input_points = torch.randn(batch_size, num_points, 3).to(device)
    
    # 前向传播
    with torch.no_grad():
        output = model(input_points)
        print(f"输入形状: {input_points.shape}")
        print(f"输出形状: {output.shape}")
        print(f"输出范围: [{output.min().item():.2f}, {output.max().item():.2f}]")
    
    # 测试损失函数
    loss_fn = create_improved_loss()
    gt_keypoints = torch.randn(batch_size, 57, 3).to(device) * 30
    
    loss_dict = loss_fn(output, gt_keypoints)
    print(f"\n损失测试:")
    print(f"总损失: {loss_dict['total_loss'].item():.4f}")
    print(f"Smooth L1损失: {loss_dict['smooth_l1_loss'].item():.4f}")
    print(f"MSE损失: {loss_dict['mse_loss'].item():.4f}")
    print(f"Huber损失: {loss_dict['huber_loss'].item():.4f}")
    print(f"几何损失: {loss_dict['geometric_loss'].item():.4f}")
    
    print("简化改进模型测试完成！")
