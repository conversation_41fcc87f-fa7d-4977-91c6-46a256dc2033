<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="1280" height="720" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="url(#headerGrad)"/>
  <text x="640" y="50" text-anchor="middle" fill="white" 
        font-family="Arial, sans-serif" font-size="36" font-weight="bold">
    Network Architecture Details
  </text>
  
  <!-- Top section: Multi-scale Feature Extraction -->
  <rect x="50" y="100" width="1180" height="280" rx="15" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <text x="640" y="130" text-anchor="middle" fill="#1e40af" 
        font-family="Arial, sans-serif" font-size="24" font-weight="bold">
    Multi-scale Feature Extraction Network (Stage 1)
  </text>
  <text x="640" y="155" text-anchor="middle" fill="#6b7280" 
        font-family="Arial, sans-serif" font-size="16">
    Inspired by HRNet and Res2Net architectures
  </text>
  
  <!-- Input layer -->
  <rect x="80" y="180" width="100" height="60" rx="8" fill="#dbeafe" stroke="#3b82f6" stroke-width="2"/>
  <text x="130" y="205" text-anchor="middle" fill="#1e40af" 
        font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Input Point Cloud
  </text>
  <text x="130" y="225" text-anchor="middle" fill="#1e40af" 
        font-family="Arial, sans-serif" font-size="10">
    N × 3 coordinates
  </text>
  
  <!-- FPS layer -->
  <rect x="220" y="180" width="100" height="60" rx="8" fill="#bfdbfe" stroke="#3b82f6" stroke-width="2"/>
  <text x="270" y="205" text-anchor="middle" fill="#1e40af" 
        font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    FPS Sampling
  </text>
  <text x="270" y="225" text-anchor="middle" fill="#1e40af" 
        font-family="Arial, sans-serif" font-size="10">
    Select nodes D
  </text>
  
  <!-- Multi-scale Ball Query -->
  <rect x="360" y="160" width="120" height="40" rx="5" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
  <text x="420" y="185" text-anchor="middle" fill="#d97706" 
        font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Ball Query (r₁, n₁)
  </text>
  
  <rect x="360" y="210" width="120" height="40" rx="5" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
  <text x="420" y="235" text-anchor="middle" fill="#d97706" 
        font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Ball Query (r₂, n₂)
  </text>
  
  <rect x="360" y="260" width="120" height="40" rx="5" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
  <text x="420" y="285" text-anchor="middle" fill="#d97706" 
        font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Ball Query (r₃, n₃)
  </text>
  
  <!-- Feature processing -->
  <rect x="520" y="160" width="100" height="40" rx="5" fill="#ecfdf5" stroke="#10b981" stroke-width="1"/>
  <text x="570" y="185" text-anchor="middle" fill="#059669" 
        font-family="Arial, sans-serif" font-size="10" font-weight="bold">
    BatchNorm + MaxPool
  </text>
  
  <rect x="520" y="210" width="100" height="40" rx="5" fill="#ecfdf5" stroke="#10b981" stroke-width="1"/>
  <text x="570" y="235" text-anchor="middle" fill="#059669" 
        font-family="Arial, sans-serif" font-size="10" font-weight="bold">
    BatchNorm + MaxPool
  </text>
  
  <rect x="520" y="260" width="100" height="40" rx="5" fill="#ecfdf5" stroke="#10b981" stroke-width="1"/>
  <text x="570" y="285" text-anchor="middle" fill="#059669" 
        font-family="Arial, sans-serif" font-size="10" font-weight="bold">
    BatchNorm + MaxPool
  </text>
  
  <!-- Local features -->
  <rect x="660" y="160" width="80" height="40" rx="5" fill="#fef2f2" stroke="#ef4444" stroke-width="1"/>
  <text x="700" y="185" text-anchor="middle" fill="#dc2626" 
        font-family="Arial, sans-serif" font-size="10" font-weight="bold">
    h₁
  </text>
  
  <rect x="660" y="210" width="80" height="40" rx="5" fill="#fef2f2" stroke="#ef4444" stroke-width="1"/>
  <text x="700" y="235" text-anchor="middle" fill="#dc2626" 
        font-family="Arial, sans-serif" font-size="10" font-weight="bold">
    h₂
  </text>
  
  <rect x="660" y="260" width="80" height="40" rx="5" fill="#fef2f2" stroke="#ef4444" stroke-width="1"/>
  <text x="700" y="285" text-anchor="middle" fill="#dc2626" 
        font-family="Arial, sans-serif" font-size="10" font-weight="bold">
    h₃
  </text>
  
  <!-- Cross-fusion -->
  <rect x="780" y="200" width="120" height="80" rx="8" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="2"/>
  <text x="840" y="225" text-anchor="middle" fill="#7c3aed" 
        font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Cross-fusion
  </text>
  <text x="840" y="245" text-anchor="middle" fill="#7c3aed" 
        font-family="Arial, sans-serif" font-size="10">
    h₂ = Φ(h₁ ⊕ h₂)
  </text>
  <text x="840" y="260" text-anchor="middle" fill="#7c3aed" 
        font-family="Arial, sans-serif" font-size="10">
    h₃ = Φ(h₁ ⊕ h₂ ⊕ h₃)
  </text>
  
  <!-- Global feature -->
  <rect x="940" y="200" width="120" height="80" rx="8" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="2"/>
  <text x="1000" y="225" text-anchor="middle" fill="#0c4a6e" 
        font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Global Feature G
  </text>
  <text x="1000" y="245" text-anchor="middle" fill="#0c4a6e" 
        font-family="Arial, sans-serif" font-size="10">
    G = Φ(H₁→G ⊕ ... ⊕ HₗG)
  </text>
  <text x="1000" y="265" text-anchor="middle" fill="#0c4a6e" 
        font-family="Arial, sans-serif" font-size="10">
    Aggregated features
  </text>
  
  <!-- Output -->
  <rect x="1100" y="200" width="100" height="80" rx="8" fill="#f0fdf4" stroke="#22c55e" stroke-width="2"/>
  <text x="1150" y="225" text-anchor="middle" fill="#15803d" 
        font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Segmentation
  </text>
  <text x="1150" y="245" text-anchor="middle" fill="#15803d" 
        font-family="Arial, sans-serif" font-size="10">
    Potential
  </text>
  <text x="1150" y="265" text-anchor="middle" fill="#15803d" 
        font-family="Arial, sans-serif" font-size="10">
    Regions
  </text>
  
  <!-- Arrows -->
  <defs>
    <marker id="arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#374151"/>
    </marker>
  </defs>
  
  <path d="M 180 210 L 215 210" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <path d="M 320 210 L 355 180" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <path d="M 320 210 L 355 230" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <path d="M 320 210 L 355 280" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  
  <path d="M 480 180 L 515 180" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <path d="M 480 230 L 515 230" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <path d="M 480 280 L 515 280" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  
  <path d="M 620 180 L 655 180" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <path d="M 620 230 L 655 230" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <path d="M 620 280 L 655 280" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  
  <path d="M 740 180 L 775 220" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <path d="M 740 230 L 775 240" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <path d="M 740 280 L 775 260" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  
  <path d="M 900 240 L 935 240" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <path d="M 1060 240 L 1095 240" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  
  <!-- Bottom section: Keypoint Localization Network -->
  <rect x="50" y="400" width="1180" height="280" rx="15" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <text x="640" y="430" text-anchor="middle" fill="#d97706" 
        font-family="Arial, sans-serif" font-size="24" font-weight="bold">
    Keypoint Localization Network (Stage 2)
  </text>
  <text x="640" y="455" text-anchor="middle" fill="#6b7280" 
        font-family="Arial, sans-serif" font-size="16">
    PointNet encoder with residual modules and Double SoftMax mechanism
  </text>
  
  <!-- Input regions -->
  <rect x="80" y="480" width="120" height="60" rx="8" fill="#fef3c7" stroke="#f59e0b" stroke-width="2"/>
  <text x="140" y="505" text-anchor="middle" fill="#d97706" 
        font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Potential Regions
  </text>
  <text x="140" y="525" text-anchor="middle" fill="#d97706" 
        font-family="Arial, sans-serif" font-size="10">
    {Rᵢ}ⁿᵢ₌₁ (k points each)
  </text>
  
  <!-- T-Net -->
  <rect x="240" y="480" width="100" height="60" rx="8" fill="#fef2f2" stroke="#ef4444" stroke-width="2"/>
  <text x="290" y="505" text-anchor="middle" fill="#dc2626" 
        font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    T-Net
  </text>
  <text x="290" y="525" text-anchor="middle" fill="#dc2626" 
        font-family="Arial, sans-serif" font-size="10">
    Feature Transform
  </text>
  
  <!-- PointNet Encoder -->
  <rect x="380" y="480" width="100" height="60" rx="8" fill="#dbeafe" stroke="#3b82f6" stroke-width="2"/>
  <text x="430" y="505" text-anchor="middle" fill="#1e40af" 
        font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    PointNet
  </text>
  <text x="430" y="525" text-anchor="middle" fill="#1e40af" 
        font-family="Arial, sans-serif" font-size="10">
    Feature Extraction
  </text>
  
  <!-- Residual Modules -->
  <rect x="520" y="480" width="100" height="60" rx="8" fill="#ecfdf5" stroke="#10b981" stroke-width="2"/>
  <text x="570" y="505" text-anchor="middle" fill="#059669" 
        font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Residual
  </text>
  <text x="570" y="525" text-anchor="middle" fill="#059669" 
        font-family="Arial, sans-serif" font-size="10">
    Dimensionality ↓
  </text>
  
  <!-- Double SoftMax -->
  <rect x="660" y="460" width="200" height="100" rx="8" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="2"/>
  <text x="760" y="485" text-anchor="middle" fill="#7c3aed" 
        font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Double SoftMax
  </text>
  <text x="760" y="505" text-anchor="middle" fill="#7c3aed" 
        font-family="Arial, sans-serif" font-size="11">
    1st SoftMax: ω = softmax(features)
  </text>
  <text x="760" y="525" text-anchor="middle" fill="#7c3aed" 
        font-family="Arial, sans-serif" font-size="11">
    2nd SoftMax: WS(ω) with threshold
  </text>
  <text x="760" y="545" text-anchor="middle" fill="#7c3aed" 
        font-family="Arial, sans-serif" font-size="11">
    Filter low-weight points
  </text>
  
  <!-- Weighted Average -->
  <rect x="900" y="480" width="120" height="60" rx="8" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="2"/>
  <text x="960" y="505" text-anchor="middle" fill="#0c4a6e" 
        font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Weighted Average
  </text>
  <text x="960" y="525" text-anchor="middle" fill="#0c4a6e" 
        font-family="Arial, sans-serif" font-size="10">
    Final coordinates
  </text>
  
  <!-- Final Output -->
  <rect x="1060" y="480" width="120" height="60" rx="8" fill="#f0fdf4" stroke="#22c55e" stroke-width="2"/>
  <text x="1120" y="505" text-anchor="middle" fill="#15803d" 
        font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Keypoints
  </text>
  <text x="1120" y="525" text-anchor="middle" fill="#15803d" 
        font-family="Arial, sans-serif" font-size="10">
    {kpᵢ}ⁿᵢ₌₁
  </text>
  
  <!-- Stage 2 Arrows -->
  <path d="M 200 510 L 235 510" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <path d="M 340 510 L 375 510" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <path d="M 480 510 L 515 510" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <path d="M 620 510 L 655 510" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <path d="M 860 510 L 895 510" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <path d="M 1020 510 L 1055 510" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  
  <!-- Mathematical formulations -->
  <rect x="80" y="580" width="560" height="80" rx="8" fill="#f8fafc" stroke="#64748b" stroke-width="1"/>
  <text x="360" y="605" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Key Mathematical Formulations
  </text>
  <text x="90" y="630" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    Cross-fusion: H' = Φ(H₁ ⊕ ... ⊕ Hₗ ⊕ ... ⊕ Hₗ₊ₐ) + Hₗ
  </text>
  <text x="90" y="650" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    Double SoftMax: WS(ωⱼ) = 1/(1 + exp(-M·(ωⱼ - (1/m - ε))))
  </text>
  
  <!-- Performance metrics -->
  <rect x="660" y="580" width="520" height="80" rx="8" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="920" y="605" text-anchor="middle" fill="#15803d" 
        font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Architecture Performance
  </text>
  <text x="670" y="630" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • Multi-scale features improve mIoU by 10-15%
  </text>
  <text x="670" y="650" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • Double SoftMax reduces MRE by 0.18mm compared to single SoftMax
  </text>
</svg>
