<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="pointCloudGrad" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:0.4" />
    </radialGradient>
  </defs>
  
  <rect width="1280" height="720" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="url(#headerGrad)"/>
  <text x="640" y="50" text-anchor="middle" fill="white" 
        font-family="Arial, sans-serif" font-size="36" font-weight="bold">
    Problem Definition & Challenges
  </text>
  
  <!-- Main Problem Statement -->
  <rect x="50" y="100" width="1180" height="120" rx="15" fill="white" stroke="#a855f7" stroke-width="3"/>
  <text x="640" y="135" text-anchor="middle" fill="#7c3aed" 
        font-family="Arial, sans-serif" font-size="28" font-weight="bold">
    Goal: Direct Detection of n Keypoints with Specific Semantic Information
  </text>
  <text x="640" y="170" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="20">
    in Dense, Complex Point Clouds (Medical Applications)
  </text>
  <text x="640" y="200" text-anchor="middle" fill="#6b7280" 
        font-family="Arial, sans-serif" font-size="16" font-style="italic">
    Minimize labeling effort • Provide intuitive 3D visualization • Support clinical tasks
  </text>
  
  <!-- Left side: Input Challenges -->
  <rect x="50" y="240" width="380" height="440" rx="15" fill="white" stroke="#ef4444" stroke-width="2"/>
  <text x="240" y="275" text-anchor="middle" fill="#dc2626" 
        font-family="Arial, sans-serif" font-size="22" font-weight="bold">
    Input Data Challenges
  </text>
  
  <!-- Point cloud visualization -->
  <circle cx="240" cy="340" r="60" fill="url(#pointCloudGrad)"/>
  <!-- Simulate dense points -->
  <g opacity="0.8">
    <circle cx="220" cy="320" r="2" fill="#1e40af"/>
    <circle cx="235" cy="315" r="2" fill="#1e40af"/>
    <circle cx="250" cy="325" r="2" fill="#1e40af"/>
    <circle cx="225" cy="335" r="2" fill="#1e40af"/>
    <circle cx="245" cy="340" r="2" fill="#1e40af"/>
    <circle cx="260" cy="330" r="2" fill="#1e40af"/>
    <circle cx="230" cy="350" r="2" fill="#1e40af"/>
    <circle cx="255" cy="355" r="2" fill="#1e40af"/>
    <circle cx="240" cy="360" r="2" fill="#1e40af"/>
    <circle cx="215" cy="345" r="2" fill="#1e40af"/>
    <circle cx="265" cy="345" r="2" fill="#1e40af"/>
    <circle cx="225" cy="305" r="2" fill="#1e40af"/>
    <circle cx="255" cy="310" r="2" fill="#1e40af"/>
    <circle cx="210" cy="330" r="2" fill="#1e40af"/>
    <circle cx="270" cy="335" r="2" fill="#1e40af"/>
  </g>
  
  <text x="70" y="430" fill="#374151" font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    Dense Structure:
  </text>
  <text x="70" y="455" fill="#6b7280" font-family="Arial, sans-serif" font-size="14">
    • 60K-600K points per model
  </text>
  <text x="70" y="475" fill="#6b7280" font-family="Arial, sans-serif" font-size="14">
    • Irregular & sparse distribution
  </text>
  <text x="70" y="495" fill="#6b7280" font-family="Arial, sans-serif" font-size="14">
    • Complex topological structure
  </text>
  
  <text x="70" y="530" fill="#374151" font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    Medical Complexity:
  </text>
  <text x="70" y="555" fill="#6b7280" font-family="Arial, sans-serif" font-size="14">
    • Anatomical variations
  </text>
  <text x="70" y="575" fill="#6b7280" font-family="Arial, sans-serif" font-size="14">
    • Minute detail importance
  </text>
  <text x="70" y="595" fill="#6b7280" font-family="Arial, sans-serif" font-size="14">
    • Limited training samples
  </text>
  
  <text x="70" y="630" fill="#374151" font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    Processing Challenges:
  </text>
  <text x="70" y="655" fill="#6b7280" font-family="Arial, sans-serif" font-size="14">
    • Standard CNN kernels fail
  </text>
  <text x="70" y="675" fill="#6b7280" font-family="Arial, sans-serif" font-size="14">
    • High computational cost
  </text>
  
  <!-- Middle: Core Challenges -->
  <rect x="450" y="240" width="380" height="440" rx="15" fill="white" stroke="#f59e0b" stroke-width="2"/>
  <text x="640" y="275" text-anchor="middle" fill="#d97706" 
        font-family="Arial, sans-serif" font-size="22" font-weight="bold">
    Core Technical Challenges
  </text>
  
  <!-- Challenge 1: Keypoint Matching -->
  <rect x="470" y="300" width="340" height="80" rx="8" fill="#fef3c7" stroke="#fcd34d" stroke-width="1"/>
  <text x="640" y="325" text-anchor="middle" fill="#92400e" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    Challenge 1: Keypoint-Ground Truth Matching
  </text>
  <text x="480" y="350" fill="#78350f" font-family="Arial, sans-serif" font-size="13">
    • Predicted vs. ground truth mismatch
  </text>
  <text x="480" y="370" fill="#78350f" font-family="Arial, sans-serif" font-size="13">
    • One-to-one correspondence requirement
  </text>
  
  <!-- Challenge 2: Region Detection -->
  <rect x="470" y="390" width="340" height="80" rx="8" fill="#fef3c7" stroke="#fcd34d" stroke-width="1"/>
  <text x="640" y="415" text-anchor="middle" fill="#92400e" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    Challenge 2: Potential Region Detection
  </text>
  <text x="480" y="440" fill="#78350f" font-family="Arial, sans-serif" font-size="13">
    • Class imbalance (keypoints vs. background)
  </text>
  <text x="480" y="460" fill="#78350f" font-family="Arial, sans-serif" font-size="13">
    • Small critical regions overlooked
  </text>
  
  <!-- Challenge 3: Precise Localization -->
  <rect x="470" y="480" width="340" height="80" rx="8" fill="#fef3c7" stroke="#fcd34d" stroke-width="1"/>
  <text x="640" y="505" text-anchor="middle" fill="#92400e" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    Challenge 3: Precise Localization
  </text>
  <text x="480" y="530" fill="#78350f" font-family="Arial, sans-serif" font-size="13">
    • High-density point similarity
  </text>
  <text x="480" y="550" fill="#78350f" font-family="Arial, sans-serif" font-size="13">
    • Weight distribution challenges
  </text>
  
  <!-- Mathematical formulation -->
  <rect x="470" y="570" width="340" height="100" rx="8" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="1"/>
  <text x="640" y="595" text-anchor="middle" fill="#0c4a6e" 
        font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Mathematical Formulation:
  </text>
  <text x="480" y="620" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    Input: {PCₜ}ᵐₜ₌₁ (m point cloud samples)
  </text>
  <text x="480" y="640" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    Output: {kpᵢ}ⁿᵢ₌₁ (n semantic keypoints)
  </text>
  <text x="480" y="660" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    Ground Truth: {tpᵢ}ⁿᵢ₌₁
  </text>
  
  <!-- Right side: Solution Strategy -->
  <rect x="850" y="240" width="380" height="440" rx="15" fill="white" stroke="#10b981" stroke-width="2"/>
  <text x="1040" y="275" text-anchor="middle" fill="#059669" 
        font-family="Arial, sans-serif" font-size="22" font-weight="bold">
    Our Solution Strategy
  </text>
  
  <!-- Two-stage approach -->
  <rect x="870" y="300" width="340" height="60" rx="8" fill="#d1fae5" stroke="#34d399" stroke-width="1"/>
  <text x="1040" y="325" text-anchor="middle" fill="#065f46" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    Two-Stage Coarse-to-Fine Approach
  </text>
  <text x="1040" y="350" text-anchor="middle" fill="#047857" 
        font-family="Arial, sans-serif" font-size="14">
    Divide complex problem into manageable sub-tasks
  </text>
  
  <!-- Stage 1 -->
  <rect x="870" y="370" width="340" height="100" rx="8" fill="#ecfdf5" stroke="#6ee7b7" stroke-width="1"/>
  <text x="1040" y="395" text-anchor="middle" fill="#065f46" 
        font-family="Arial, sans-serif" font-size="15" font-weight="bold">
    Stage 1: Potential Region Detection
  </text>
  <text x="880" y="420" fill="#047857" font-family="Arial, sans-serif" font-size="13">
    • Identify keypoint-related regions {Rᵢ}ⁿᵢ₌₁
  </text>
  <text x="880" y="440" fill="#047857" font-family="Arial, sans-serif" font-size="13">
    • Use K-nearest neighbors (k points)
  </text>
  <text x="880" y="460" fill="#047857" font-family="Arial, sans-serif" font-size="13">
    • Similar to point cloud segmentation
  </text>
  
  <!-- Stage 2 -->
  <rect x="870" y="480" width="340" height="100" rx="8" fill="#ecfdf5" stroke="#6ee7b7" stroke-width="1"/>
  <text x="1040" y="505" text-anchor="middle" fill="#065f46" 
        font-family="Arial, sans-serif" font-size="15" font-weight="bold">
    Stage 2: Precise Keypoint Localization
  </text>
  <text x="880" y="530" fill="#047857" font-family="Arial, sans-serif" font-size="13">
    • Compute weight matrix Weightᵢ
  </text>
  <text x="880" y="550" fill="#047857" font-family="Arial, sans-serif" font-size="13">
    • Weighted average of coordinates
  </text>
  <text x="880" y="570" fill="#047857" font-family="Arial, sans-serif" font-size="13">
    • Final keypoint prediction {kpᵢ}ⁿᵢ₌₁
  </text>
  
  <!-- Key innovations -->
  <rect x="870" y="590" width="340" height="80" rx="8" fill="#fef7ff" stroke="#c084fc" stroke-width="1"/>
  <text x="1040" y="615" text-anchor="middle" fill="#7c3aed" 
        font-family="Arial, sans-serif" font-size="15" font-weight="bold">
    Key Innovations
  </text>
  <text x="880" y="640" fill="#6b21a8" font-family="Arial, sans-serif" font-size="13">
    • Penalty Dice Loss for small regions
  </text>
  <text x="880" y="660" fill="#6b21a8" font-family="Arial, sans-serif" font-size="13">
    • Double SoftMax weighting mechanism
  </text>
</svg>
