#!/usr/bin/env python3
"""
坐标系对齐解决方案
Coordinate System Alignment Solution
解决F1、F2、F3坐标系偏移问题
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json
from pathlib import Path
from datetime import datetime

class CoordinateSystemAligner:
    """坐标系对齐器"""
    
    def __init__(self, data_path='data/raw/high_quality_f3_dataset.npz'):
        self.data_path = data_path
        self.load_data()
        
    def load_data(self):
        """加载数据"""
        print(f"📦 加载F3数据集: {self.data_path}")
        
        data = np.load(self.data_path, allow_pickle=True)
        self.sample_ids = data['sample_ids']
        self.point_clouds = data['point_clouds']
        self.keypoints = data['keypoints']
        
        print(f"✅ 数据加载完成: {len(self.sample_ids)} 样本")
        
    def analyze_coordinate_relationships(self):
        """分析坐标关系"""
        print("\n🔍 分析F1、F2、F3坐标关系...")
        
        # 分析每个样本的点云和关键点关系
        relationships = []
        
        for i, (sample_id, pc, kp) in enumerate(zip(self.sample_ids, self.point_clouds, self.keypoints)):
            pc_array = np.array(pc, dtype=np.float32)
            kp_array = np.array(kp, dtype=np.float32)
            
            # 计算点云的空间分布
            pc_center = np.mean(pc_array, axis=0)
            pc_range = np.max(pc_array, axis=0) - np.min(pc_array, axis=0)
            pc_min = np.min(pc_array, axis=0)
            pc_max = np.max(pc_array, axis=0)
            
            # 计算关键点的空间分布
            kp_center = np.mean(kp_array, axis=0)
            kp_range = np.max(kp_array, axis=0) - np.min(kp_array, axis=0)
            kp_min = np.min(kp_array, axis=0)
            kp_max = np.max(kp_array, axis=0)
            
            # 分析关键点在点云中的相对位置
            kp_relative_pos = (kp_center - pc_min) / pc_range
            
            # 检查关键点是否在点云的某个特定区域
            # F3通常在中心或特定位置
            relationships.append({
                'sample_id': sample_id,
                'pc_center': pc_center,
                'pc_range': pc_range,
                'kp_center': kp_center,
                'kp_range': kp_range,
                'kp_relative_position': kp_relative_pos,
                'center_offset': np.linalg.norm(pc_center - kp_center),
                'pc_bounds': [pc_min, pc_max],
                'kp_bounds': [kp_min, kp_max]
            })
        
        self.relationships = relationships
        
        # 统计分析
        center_offsets = [r['center_offset'] for r in relationships]
        relative_positions = np.array([r['kp_relative_position'] for r in relationships])
        
        print(f"📊 坐标关系分析:")
        print(f"   平均中心偏移: {np.mean(center_offsets):.1f}mm")
        print(f"   中心偏移范围: {np.min(center_offsets):.1f} - {np.max(center_offsets):.1f}mm")
        print(f"   关键点相对位置 (X): {np.mean(relative_positions[:, 0]):.2f} ± {np.std(relative_positions[:, 0]):.2f}")
        print(f"   关键点相对位置 (Y): {np.mean(relative_positions[:, 1]):.2f} ± {np.std(relative_positions[:, 1]):.2f}")
        print(f"   关键点相对位置 (Z): {np.mean(relative_positions[:, 2]):.2f} ± {np.std(relative_positions[:, 2]):.2f}")
        
        return relationships
    
    def identify_f3_region(self):
        """识别F3区域在点云中的位置"""
        print("\n🎯 识别F3区域位置...")
        
        # 基于关键点相对位置识别F3区域
        relative_positions = np.array([r['kp_relative_position'] for r in self.relationships])
        
        # 计算F3区域的典型位置
        f3_position = {
            'x_relative': np.mean(relative_positions[:, 0]),
            'y_relative': np.mean(relative_positions[:, 1]),
            'z_relative': np.mean(relative_positions[:, 2]),
            'x_std': np.std(relative_positions[:, 0]),
            'y_std': np.std(relative_positions[:, 1]),
            'z_std': np.std(relative_positions[:, 2])
        }
        
        print(f"📍 F3区域典型位置:")
        print(f"   X方向: {f3_position['x_relative']:.2f} ± {f3_position['x_std']:.2f} (相对位置)")
        print(f"   Y方向: {f3_position['y_relative']:.2f} ± {f3_position['y_std']:.2f} (相对位置)")
        print(f"   Z方向: {f3_position['z_relative']:.2f} ± {f3_position['z_std']:.2f} (相对位置)")
        
        # 判断F3在点云中的位置
        if f3_position['x_relative'] < 0.3:
            x_region = "左侧"
        elif f3_position['x_relative'] > 0.7:
            x_region = "右侧"
        else:
            x_region = "中央"
            
        if f3_position['y_relative'] < 0.3:
            y_region = "前部"
        elif f3_position['y_relative'] > 0.7:
            y_region = "后部"
        else:
            y_region = "中部"
            
        if f3_position['z_relative'] < 0.3:
            z_region = "下部"
        elif f3_position['z_relative'] > 0.7:
            z_region = "上部"
        else:
            z_region = "中部"
        
        print(f"🗺️ F3区域位置: {x_region}-{y_region}-{z_region}")
        
        self.f3_position = f3_position
        return f3_position
    
    def propose_alignment_strategy(self):
        """提出对齐策略"""
        print("\n🔧 提出坐标系对齐策略...")
        
        # 基于分析结果提出策略
        strategies = []
        
        # 策略1: 以关键点为中心对齐
        strategies.append({
            'name': '关键点中心对齐',
            'description': '将每个样本的坐标系原点移动到关键点中心',
            'method': 'keypoint_center',
            'pros': ['简单直接', '保持关键点相对关系', '减少坐标偏移'],
            'cons': ['可能丢失F1、F2的空间信息']
        })
        
        # 策略2: 保持完整空间信息的对齐
        strategies.append({
            'name': '完整空间对齐',
            'description': '保持F1、F2、F3的完整空间关系，只进行平移对齐',
            'method': 'full_space_alignment',
            'pros': ['保持完整解剖信息', '保留F1、F2、F3关系', '适合多区域学习'],
            'cons': ['数据量大', '计算复杂']
        })
        
        # 策略3: 区域裁剪对齐
        strategies.append({
            'name': 'F3区域裁剪对齐',
            'description': '裁剪出F3附近区域，然后进行对齐',
            'method': 'f3_region_crop',
            'pros': ['减少无关数据', '保持F3完整信息', '平衡效率和精度'],
            'cons': ['需要确定合适的裁剪范围']
        })
        
        # 策略4: 多尺度对齐
        strategies.append({
            'name': '多尺度对齐',
            'description': '同时保持全局和局部信息的多尺度对齐',
            'method': 'multi_scale_alignment',
            'pros': ['全局和局部信息并存', '适合复杂模型', '最大化信息利用'],
            'cons': ['实现复杂', '计算量大']
        })
        
        print(f"📋 提出的对齐策略:")
        for i, strategy in enumerate(strategies, 1):
            print(f"\n策略{i}: {strategy['name']}")
            print(f"   描述: {strategy['description']}")
            print(f"   优点: {', '.join(strategy['pros'])}")
            print(f"   缺点: {', '.join(strategy['cons'])}")
        
        # 推荐策略
        recommended_strategy = strategies[2]  # F3区域裁剪对齐
        
        print(f"\n🎯 推荐策略: {recommended_strategy['name']}")
        print(f"   理由: 平衡了数据效率和信息完整性，适合当前的F3关键点检测任务")
        
        self.strategies = strategies
        self.recommended_strategy = recommended_strategy
        
        return strategies, recommended_strategy
    
    def implement_f3_region_alignment(self, crop_radius_mm=80):
        """实施F3区域对齐"""
        print(f"\n🔧 实施F3区域对齐 (裁剪半径: {crop_radius_mm}mm)...")
        
        aligned_point_clouds = []
        aligned_keypoints = []
        alignment_info = []
        
        for i, (sample_id, pc, kp) in enumerate(zip(self.sample_ids, self.point_clouds, self.keypoints)):
            pc_array = np.array(pc, dtype=np.float32)
            kp_array = np.array(kp, dtype=np.float32)
            
            # 计算关键点中心
            kp_center = np.mean(kp_array, axis=0)
            
            # 裁剪点云到关键点附近区域
            distances = np.linalg.norm(pc_array - kp_center, axis=1)
            crop_mask = distances <= crop_radius_mm
            
            if np.sum(crop_mask) < 1000:  # 确保有足够的点
                # 如果裁剪后点太少，扩大半径
                crop_radius_expanded = crop_radius_mm * 1.5
                distances = np.linalg.norm(pc_array - kp_center, axis=1)
                crop_mask = distances <= crop_radius_expanded
                print(f"   样本 {sample_id}: 扩大裁剪半径到 {crop_radius_expanded:.1f}mm")
            
            cropped_pc = pc_array[crop_mask]
            
            # 坐标系对齐：以关键点中心为原点
            aligned_pc = cropped_pc - kp_center
            aligned_kp = kp_array - kp_center
            
            aligned_point_clouds.append(aligned_pc)
            aligned_keypoints.append(aligned_kp)
            
            alignment_info.append({
                'sample_id': sample_id,
                'original_pc_size': len(pc_array),
                'cropped_pc_size': len(cropped_pc),
                'crop_ratio': len(cropped_pc) / len(pc_array),
                'kp_center_offset': kp_center,
                'crop_radius_used': crop_radius_mm if np.sum(crop_mask) >= 1000 else crop_radius_mm * 1.5
            })
            
            if i < 5:  # 显示前5个样本的信息
                print(f"   样本 {sample_id}: {len(pc_array)} → {len(cropped_pc)} 点 ({len(cropped_pc)/len(pc_array)*100:.1f}%)")
        
        # 统计信息
        crop_ratios = [info['crop_ratio'] for info in alignment_info]
        pc_sizes = [len(pc) for pc in aligned_point_clouds]
        
        print(f"\n📊 对齐结果统计:")
        print(f"   平均保留比例: {np.mean(crop_ratios)*100:.1f}%")
        print(f"   点云大小范围: {np.min(pc_sizes)} - {np.max(pc_sizes)} 点")
        print(f"   平均点云大小: {np.mean(pc_sizes):.0f} 点")
        
        self.aligned_point_clouds = aligned_point_clouds
        self.aligned_keypoints = aligned_keypoints
        self.alignment_info = alignment_info
        
        return aligned_point_clouds, aligned_keypoints, alignment_info
    
    def save_aligned_dataset(self, output_path=None):
        """保存对齐后的数据集"""
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"data/processed/f3_aligned_dataset_{timestamp}.npz"
        
        output_dir = Path(output_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"\n💾 保存对齐数据集: {output_path}")
        
        # 统一点云大小
        target_points = 4096
        uniform_point_clouds = []
        
        for pc in self.aligned_point_clouds:
            if len(pc) > target_points:
                indices = np.random.choice(len(pc), target_points, replace=False)
                pc_uniform = pc[indices]
            elif len(pc) < target_points:
                indices = np.random.choice(len(pc), target_points, replace=True)
                pc_uniform = pc[indices]
            else:
                pc_uniform = pc
            
            uniform_point_clouds.append(pc_uniform)
        
        uniform_point_clouds = np.array(uniform_point_clouds)
        aligned_keypoints = np.array(self.aligned_keypoints)
        
        # 保存数据
        np.savez_compressed(
            output_path,
            sample_ids=self.sample_ids,
            point_clouds=uniform_point_clouds,
            keypoints=aligned_keypoints,
            original_point_clouds=self.point_clouds,
            original_keypoints=self.keypoints,
            alignment_info=self.alignment_info,
            preprocessing_type='f3_coordinate_alignment',
            target_points=target_points,
            coordinate_system='f3_centered'
        )
        
        print(f"✅ 对齐数据集保存完成")
        print(f"   最终点云形状: {uniform_point_clouds.shape}")
        print(f"   最终关键点形状: {aligned_keypoints.shape}")
        print(f"   坐标系: F3中心对齐")
        
        return output_path
    
    def visualize_alignment_effect(self, sample_indices=[0, 1, 2]):
        """可视化对齐效果"""
        print(f"\n📊 可视化对齐效果...")
        
        viz_dir = Path("results/coordinate_alignment_visualization")
        viz_dir.mkdir(parents=True, exist_ok=True)
        
        for idx in sample_indices:
            if idx >= len(self.sample_ids):
                continue
                
            sample_id = self.sample_ids[idx]
            
            fig = plt.figure(figsize=(20, 6))
            
            # 原始数据
            ax1 = fig.add_subplot(131, projection='3d')
            orig_pc = np.array(self.point_clouds[idx])
            orig_kp = self.keypoints[idx]
            
            # 随机采样显示
            if len(orig_pc) > 3000:
                display_indices = np.random.choice(len(orig_pc), 3000, replace=False)
                display_pc = orig_pc[display_indices]
            else:
                display_pc = orig_pc
            
            ax1.scatter(display_pc[:, 0], display_pc[:, 1], display_pc[:, 2], 
                       c='lightgray', s=1, alpha=0.3, label='Original PC')
            ax1.scatter(orig_kp[:, 0], orig_kp[:, 1], orig_kp[:, 2], 
                       c='red', s=50, label='Original KP')
            
            ax1.set_title(f'Original Data - {sample_id}')
            ax1.legend()
            ax1.set_xlabel('X (mm)')
            ax1.set_ylabel('Y (mm)')
            ax1.set_zlabel('Z (mm)')
            
            # 对齐后数据
            ax2 = fig.add_subplot(132, projection='3d')
            aligned_pc = self.aligned_point_clouds[idx]
            aligned_kp = self.aligned_keypoints[idx]
            
            # 随机采样显示
            if len(aligned_pc) > 3000:
                display_indices = np.random.choice(len(aligned_pc), 3000, replace=False)
                display_pc = aligned_pc[display_indices]
            else:
                display_pc = aligned_pc
            
            ax2.scatter(display_pc[:, 0], display_pc[:, 1], display_pc[:, 2], 
                       c='lightblue', s=1, alpha=0.3, label='Aligned PC')
            ax2.scatter(aligned_kp[:, 0], aligned_kp[:, 1], aligned_kp[:, 2], 
                       c='blue', s=50, label='Aligned KP')
            
            ax2.set_title(f'F3-Centered Aligned - {sample_id}')
            ax2.legend()
            ax2.set_xlabel('X (mm)')
            ax2.set_ylabel('Y (mm)')
            ax2.set_zlabel('Z (mm)')
            
            # 对齐信息
            ax3 = fig.add_subplot(133)
            info = self.alignment_info[idx]
            
            info_text = f'对齐信息 - {sample_id}\n\n'
            info_text += f'原始点云: {info["original_pc_size"]:,} 点\n'
            info_text += f'裁剪后: {info["cropped_pc_size"]:,} 点\n'
            info_text += f'保留比例: {info["crop_ratio"]*100:.1f}%\n'
            info_text += f'裁剪半径: {info["crop_radius_used"]:.1f}mm\n\n'
            
            info_text += f'坐标偏移:\n'
            info_text += f'X: {info["kp_center_offset"][0]:+.1f}mm\n'
            info_text += f'Y: {info["kp_center_offset"][1]:+.1f}mm\n'
            info_text += f'Z: {info["kp_center_offset"][2]:+.1f}mm\n\n'
            
            info_text += f'对齐效果:\n'
            info_text += f'• 消除了F1、F2、F3的坐标偏移\n'
            info_text += f'• 保留了F3区域的完整信息\n'
            info_text += f'• 减少了{(1-info["crop_ratio"])*100:.1f}%的无关数据\n'
            info_text += f'• 关键点现在以原点为中心'
            
            ax3.text(0.05, 0.95, info_text, transform=ax3.transAxes, 
                    fontsize=10, verticalalignment='top', fontfamily='monospace')
            ax3.set_xlim(0, 1)
            ax3.set_ylim(0, 1)
            ax3.set_title('Alignment Information')
            ax3.axis('off')
            
            plt.tight_layout()
            plt.savefig(viz_dir / f"coordinate_alignment_{sample_id}.png", 
                       dpi=150, bbox_inches='tight')
            print(f"💾 保存对齐可视化: coordinate_alignment_{sample_id}.png")
            plt.show()

def main():
    """主函数"""
    print("🔧 F3坐标系对齐解决方案")
    print("=" * 60)
    print("目标: 解决F1、F2、F3坐标系偏移问题")
    
    # 创建对齐器
    aligner = CoordinateSystemAligner()
    
    # 分析坐标关系
    relationships = aligner.analyze_coordinate_relationships()
    
    # 识别F3区域
    f3_position = aligner.identify_f3_region()
    
    # 提出对齐策略
    strategies, recommended = aligner.propose_alignment_strategy()
    
    # 实施F3区域对齐
    aligned_pcs, aligned_kps, alignment_info = aligner.implement_f3_region_alignment()
    
    # 保存对齐数据集
    output_path = aligner.save_aligned_dataset()
    
    # 可视化对齐效果
    aligner.visualize_alignment_effect()
    
    print(f"\n🎉 坐标系对齐完成!")
    print(f"📁 对齐数据集: {output_path}")
    print(f"🎯 解决了F1、F2、F3的坐标偏移问题")
    print(f"📊 数据效率提升: 平均保留{np.mean([info['crop_ratio'] for info in alignment_info])*100:.1f}%的相关数据")
    
    return aligner

if __name__ == "__main__":
    aligner = main()
