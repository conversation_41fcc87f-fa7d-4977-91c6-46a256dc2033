#!/usr/bin/env python3
"""
深入分析影响19关键点性能的根本问题
找出真正的性能瓶颈
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns

def analyze_fundamental_limitations():
    """分析根本性限制因素"""
    
    print("🔍 19关键点性能瓶颈深度分析")
    print("=" * 60)
    
    # 加载数据
    data = np.load('f3_19kp_preprocessed.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    sample_ids = data['sample_ids']
    
    print(f"📊 数据基本信息:")
    print(f"   样本数: {len(point_clouds)}")
    print(f"   关键点数: {keypoints.shape[1]}")
    print(f"   平均点云大小: {np.mean([len(pc) for pc in point_clouds]):.0f}")
    
    # 1. 数据量问题
    analyze_data_size_limitation(point_clouds, keypoints)
    
    # 2. 数据质量问题
    analyze_data_quality_issues(point_clouds, keypoints, sample_ids)
    
    # 3. 任务复杂度问题
    analyze_task_complexity(keypoints)
    
    # 4. 模型容量问题
    analyze_model_capacity_issues()
    
    # 5. 评估标准问题
    analyze_evaluation_standards()

def analyze_data_size_limitation(point_clouds, keypoints):
    """分析数据量限制"""
    
    print(f"\n📉 问题1: 数据量严重不足")
    print("-" * 40)
    
    num_samples = len(point_clouds)
    num_keypoints = keypoints.shape[1]
    num_parameters = 1395731  # 基础模型参数量
    
    # 计算数据需求
    samples_per_param = num_samples / num_parameters
    recommended_samples = num_parameters / 100  # 经验法则：每100个参数需要1个样本
    
    print(f"当前状况:")
    print(f"   训练样本: {num_samples}")
    print(f"   模型参数: {num_parameters:,}")
    print(f"   样本/参数比: {samples_per_param:.6f}")
    print(f"   推荐样本数: {recommended_samples:.0f}")
    print(f"   数据缺口: {recommended_samples - num_samples:.0f} 样本")
    
    print(f"\n影响:")
    print(f"   • 严重过拟合风险")
    print(f"   • 泛化能力差")
    print(f"   • 对噪声敏感")
    print(f"   • 无法学习复杂模式")

def analyze_data_quality_issues(point_clouds, keypoints, sample_ids):
    """分析数据质量问题"""
    
    print(f"\n🎯 问题2: 数据质量问题")
    print("-" * 40)
    
    # 2.1 标注一致性问题
    print(f"2.1 标注一致性分析:")
    
    keypoint_variations = []
    for kp_idx in range(19):
        kp_coords = keypoints[:, kp_idx, :]
        centroid = np.mean(kp_coords, axis=0)
        distances = np.linalg.norm(kp_coords - centroid, axis=1)
        variation = np.std(distances)
        keypoint_variations.append(variation)
        
        if kp_idx < 5:  # 显示前5个关键点
            print(f"   F3-{kp_idx+1}: 标注变异 {variation:.2f}mm")
    
    avg_variation = np.mean(keypoint_variations)
    max_variation = np.max(keypoint_variations)
    print(f"   平均标注变异: {avg_variation:.2f}mm")
    print(f"   最大标注变异: {max_variation:.2f}mm")
    
    # 2.2 点云质量问题
    print(f"\n2.2 点云质量分析:")
    
    pc_densities = []
    pc_noise_levels = []
    
    for i, pc in enumerate(point_clouds[:5]):  # 分析前5个样本
        # 计算点云密度
        bbox_volume = np.prod(np.max(pc, axis=0) - np.min(pc, axis=0))
        density = len(pc) / bbox_volume if bbox_volume > 0 else 0
        pc_densities.append(density)
        
        # 估计噪声水平
        if len(pc) > 100:
            # 计算每个点到最近邻的距离
            distances = []
            for point in pc[::10]:  # 采样以提高效率
                dists = np.linalg.norm(pc - point, axis=1)
                dists = dists[dists > 0]  # 排除自己
                if len(dists) > 0:
                    distances.append(np.min(dists))
            
            noise_level = np.std(distances) if distances else 0
            pc_noise_levels.append(noise_level)
        
        print(f"   样本 {sample_ids[i]}: 密度 {density:.1f} 点/mm³, 噪声 {noise_level:.3f}mm")
    
    # 2.3 坐标系一致性
    print(f"\n2.3 坐标系一致性:")
    
    # 检查不同样本的坐标范围
    x_ranges = [np.max(pc[:, 0]) - np.min(pc[:, 0]) for pc in point_clouds]
    y_ranges = [np.max(pc[:, 1]) - np.min(pc[:, 1]) for pc in point_clouds]
    z_ranges = [np.max(pc[:, 2]) - np.min(pc[:, 2]) for pc in point_clouds]
    
    print(f"   X范围变异: {np.std(x_ranges):.2f}mm (平均: {np.mean(x_ranges):.1f}mm)")
    print(f"   Y范围变异: {np.std(y_ranges):.2f}mm (平均: {np.mean(y_ranges):.1f}mm)")
    print(f"   Z范围变异: {np.std(z_ranges):.2f}mm (平均: {np.mean(z_ranges):.1f}mm)")

def analyze_task_complexity(keypoints):
    """分析任务复杂度"""
    
    print(f"\n🧩 问题3: 任务复杂度过高")
    print("-" * 40)
    
    # 3.1 关键点密度分析
    print(f"3.1 关键点空间密度:")
    
    # 计算关键点间的平均距离
    sample_kp = keypoints[0]  # 使用第一个样本
    distances = []
    
    for i in range(19):
        for j in range(i+1, 19):
            dist = np.linalg.norm(sample_kp[i] - sample_kp[j])
            distances.append(dist)
    
    avg_kp_distance = np.mean(distances)
    min_kp_distance = np.min(distances)
    
    print(f"   关键点平均间距: {avg_kp_distance:.2f}mm")
    print(f"   关键点最小间距: {min_kp_distance:.2f}mm")
    print(f"   空间密度: {19 / np.prod(np.max(sample_kp, axis=0) - np.min(sample_kp, axis=0)):.6f} 点/mm³")
    
    # 3.2 区分难度分析
    print(f"\n3.2 关键点区分难度:")
    
    # 找出最难区分的关键点对
    difficult_pairs = []
    for i in range(19):
        for j in range(i+1, 19):
            dist = np.linalg.norm(sample_kp[i] - sample_kp[j])
            if dist < 10:  # 距离小于10mm的被认为是难以区分的
                difficult_pairs.append((i, j, dist))
    
    difficult_pairs.sort(key=lambda x: x[2])
    
    print(f"   难以区分的关键点对 (<10mm):")
    for i, j, dist in difficult_pairs[:5]:
        print(f"     F3-{i+1} vs F3-{j+1}: {dist:.2f}mm")
    
    # 3.3 多尺度特征需求
    print(f"\n3.3 多尺度特征需求:")
    
    # 分析不同关键点需要的感受野大小
    bbox_size = np.max(sample_kp, axis=0) - np.min(sample_kp, axis=0)
    max_dimension = np.max(bbox_size)
    
    print(f"   整体尺度: {max_dimension:.1f}mm")
    print(f"   局部精度需求: ~1mm")
    print(f"   尺度比: {max_dimension:.0f}:1")
    print(f"   需要的尺度层数: {int(np.log2(max_dimension))}")

def analyze_model_capacity_issues():
    """分析模型容量问题"""
    
    print(f"\n🤖 问题4: 模型架构限制")
    print("-" * 40)
    
    # 4.1 PointNet架构限制
    print(f"4.1 PointNet架构分析:")
    print(f"   • 缺乏局部结构建模")
    print(f"   • 感受野增长有限")
    print(f"   • 对点云密度敏感")
    print(f"   • 难以处理多尺度特征")
    
    # 4.2 热力图回归限制
    print(f"\n4.2 热力图回归限制:")
    print(f"   • 分辨率受点云采样限制")
    print(f"   • 高斯核大小难以调优")
    print(f"   • 多个峰值时容易混淆")
    print(f"   • 边界效应明显")
    
    # 4.3 损失函数限制
    print(f"\n4.3 损失函数限制:")
    print(f"   • MSE损失对异常值敏感")
    print(f"   • 缺乏几何约束")
    print(f"   • 没有考虑关键点间关系")
    print(f"   • 忽略医学先验知识")

def analyze_evaluation_standards():
    """分析评估标准问题"""
    
    print(f"\n📏 问题5: 评估标准与医疗需求不匹配")
    print("-" * 40)
    
    # 5.1 医疗精度要求
    print(f"5.1 医疗应用精度要求:")
    print(f"   • 诊断级精度: <2mm")
    print(f"   • 手术导航: <1mm") 
    print(f"   • 治疗规划: <3mm")
    print(f"   • 当前性能: ~7.4mm ❌")
    
    # 5.2 评估指标局限性
    print(f"\n5.2 当前评估指标问题:")
    print(f"   • 平均误差掩盖了极端情况")
    print(f"   • 没有考虑临床相关性")
    print(f"   • 缺乏置信度评估")
    print(f"   • 忽略解剖学合理性")
    
    # 5.3 成功标准重新定义
    print(f"\n5.3 建议的成功标准:")
    print(f"   • 95%关键点 <5mm误差")
    print(f"   • 100%关键点 <10mm误差")
    print(f"   • 解剖学合理性检查")
    print(f"   • 临床专家验证")

def propose_fundamental_solutions():
    """提出根本性解决方案"""
    
    print(f"\n💡 根本性解决方案")
    print("=" * 60)
    
    print(f"1. 数据层面解决方案:")
    print(f"   🎯 扩大数据集:")
    print(f"     • 目标: 1000+ 样本 (当前20)")
    print(f"     • 多中心数据收集")
    print(f"     • 数据增强策略")
    print(f"     • 合成数据生成")
    
    print(f"   🎯 提高数据质量:")
    print(f"     • 标注标准化协议")
    print(f"     • 多专家标注验证")
    print(f"     • 质量控制流程")
    print(f"     • 坐标系统一")
    
    print(f"\n2. 模型层面解决方案:")
    print(f"   🎯 架构升级:")
    print(f"     • 使用Point Transformer")
    print(f"     • 引入注意力机制")
    print(f"     • 多尺度特征融合")
    print(f"     • 图神经网络")
    
    print(f"   🎯 训练策略:")
    print(f"     • 预训练 + 微调")
    print(f"     • 课程学习")
    print(f"     • 对抗训练")
    print(f"     • 知识蒸馏")
    
    print(f"\n3. 方法层面解决方案:")
    print(f"   🎯 混合方法:")
    print(f"     • 深度学习 + 传统方法")
    print(f"     • 粗定位 + 精细化")
    print(f"     • 多模态融合")
    print(f"     • 人机协作")
    
    print(f"   🎯 领域适应:")
    print(f"     • 医学先验知识")
    print(f"     • 解剖学约束")
    print(f"     • 物理约束")
    print(f"     • 临床反馈")

def create_bottleneck_visualization():
    """创建性能瓶颈可视化"""
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 数据量 vs 性能关系
    ax1 = axes[0, 0]
    
    # 模拟不同数据量下的性能
    data_sizes = [10, 20, 50, 100, 200, 500, 1000]
    performances = [15, 12, 8, 6, 4, 3, 2.5]  # 模拟的性能曲线
    
    ax1.plot(data_sizes, performances, 'bo-', linewidth=2, markersize=8)
    ax1.axvline(x=20, color='red', linestyle='--', alpha=0.7, label='Current (20 samples)')
    ax1.axhline(y=5, color='green', linestyle='--', alpha=0.7, label='Medical Target (5mm)')
    
    ax1.set_xlabel('Number of Training Samples')
    ax1.set_ylabel('Average Error (mm)')
    ax1.set_title('Data Size vs Performance')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_xscale('log')
    
    # 2. 任务复杂度分析
    ax2 = axes[0, 1]
    
    complexities = ['2D Images\n(5 points)', 'Simple 3D\n(5 points)', 
                   'Complex 3D\n(12 points)', 'Medical 3D\n(19 points)']
    difficulties = [2, 4, 6, 8]
    colors = ['green', 'yellow', 'orange', 'red']
    
    bars = ax2.bar(complexities, difficulties, color=colors, alpha=0.7)
    ax2.set_ylabel('Task Difficulty Score')
    ax2.set_title('Task Complexity Comparison')
    ax2.tick_params(axis='x', rotation=45)
    
    # 添加当前位置标记
    ax2.text(3, 8.5, 'Our Task', ha='center', fontweight='bold', 
             bbox=dict(boxstyle='round', facecolor='red', alpha=0.3))
    
    # 3. 模型容量 vs 数据需求
    ax3 = axes[0, 2]
    
    model_params = [10000, 100000, 1000000, 10000000]
    data_needs = [100, 1000, 10000, 100000]
    model_names = ['Simple\nMLP', 'Basic\nCNN', 'PointNet\n(Ours)', 'Large\nTransformer']
    
    for i, (params, data, name) in enumerate(zip(model_params, data_needs, model_names)):
        color = 'red' if i == 2 else 'blue'
        alpha = 0.8 if i == 2 else 0.5
        ax3.scatter(params, data, s=200, c=color, alpha=alpha)
        ax3.text(params, data*1.2, name, ha='center', fontsize=8)
    
    # 标记当前状态
    ax3.scatter(1395731, 20, s=300, c='red', marker='x', linewidth=3)
    ax3.text(1395731, 15, 'Current\nState', ha='center', fontweight='bold',
             bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
    
    ax3.set_xlabel('Model Parameters')
    ax3.set_ylabel('Required Training Samples')
    ax3.set_title('Model Capacity vs Data Requirements')
    ax3.set_xscale('log')
    ax3.set_yscale('log')
    ax3.grid(True, alpha=0.3)
    
    # 4. 性能瓶颈分解
    ax4 = axes[1, 0]
    
    bottlenecks = ['Data\nSize', 'Data\nQuality', 'Model\nCapacity', 'Task\nComplexity', 'Evaluation\nStandards']
    impact_scores = [9, 7, 6, 8, 4]  # 影响程度评分
    colors = ['red', 'orange', 'yellow', 'orange', 'lightblue']
    
    bars = ax4.bar(bottlenecks, impact_scores, color=colors, alpha=0.7)
    ax4.set_ylabel('Impact Score (1-10)')
    ax4.set_title('Performance Bottleneck Analysis')
    ax4.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar, score in zip(bars, impact_scores):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                f'{score}', ha='center', va='bottom', fontweight='bold')
    
    # 5. 解决方案优先级
    ax5 = axes[1, 1]
    
    solutions = ['Expand\nDataset', 'Improve\nQuality', 'Better\nModel', 'Hybrid\nMethod', 'Domain\nKnowledge']
    priorities = [10, 8, 6, 7, 9]
    feasibilities = [3, 7, 8, 6, 8]
    
    scatter = ax5.scatter(feasibilities, priorities, s=[p*20 for p in priorities], 
                         c=range(len(solutions)), cmap='viridis', alpha=0.7)
    
    for i, solution in enumerate(solutions):
        ax5.text(feasibilities[i], priorities[i], solution, ha='center', va='center', 
                fontsize=8, fontweight='bold')
    
    ax5.set_xlabel('Feasibility (1-10)')
    ax5.set_ylabel('Priority (1-10)')
    ax5.set_title('Solution Priority vs Feasibility')
    ax5.grid(True, alpha=0.3)
    
    # 6. 性能改进路线图
    ax6 = axes[1, 2]
    ax6.axis('off')
    
    roadmap_text = """
Performance Improvement Roadmap:

Phase 1 (Short-term, 3-6 months):
• Improve data quality
• Apply domain knowledge
• Optimize current model
• Target: 5-6mm average error

Phase 2 (Medium-term, 6-12 months):
• Expand dataset (100+ samples)
• Implement hybrid methods
• Add anatomical constraints
• Target: 3-4mm average error

Phase 3 (Long-term, 1-2 years):
• Large-scale data collection
• Advanced model architectures
• Multi-modal integration
• Target: <2mm medical-grade

Critical Success Factors:
✓ Data quality over quantity
✓ Domain expertise integration
✓ Realistic target setting
✓ Iterative improvement
"""
    
    ax6.text(0.05, 0.95, roadmap_text, transform=ax6.transAxes, fontsize=9,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    plt.suptitle('19-Keypoint Performance Bottleneck Analysis\n'
                'Understanding Fundamental Limitations and Solutions', 
                fontsize=16, fontweight='bold')
    plt.tight_layout(rect=[0, 0, 1, 0.93])
    
    filename = 'performance_bottleneck_analysis.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 性能瓶颈分析保存: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🔍 19关键点性能瓶颈深度分析")
    print("找出影响性能的根本问题")
    print("=" * 60)
    
    # 分析根本性限制
    analyze_fundamental_limitations()
    
    # 提出解决方案
    propose_fundamental_solutions()
    
    # 创建可视化
    create_bottleneck_visualization()
    
    print(f"\n🎯 核心结论:")
    print("影响性能的主要问题 (按重要性排序):")
    print("1. 🚨 数据量严重不足 (20样本 vs 需要1000+)")
    print("2. 🚨 任务复杂度过高 (19个密集关键点)")
    print("3. ⚠️ 数据质量问题 (标注不一致、噪声)")
    print("4. ⚠️ 模型架构限制 (PointNet局限性)")
    print("5. ⚠️ 评估标准不匹配 (医疗需求 vs 当前性能)")
    
    print(f"\n💡 最重要的改进方向:")
    print("短期: 提高数据质量 + 领域知识集成")
    print("中期: 扩大数据集 + 混合方法")
    print("长期: 大规模数据 + 先进架构")

if __name__ == "__main__":
    main()
