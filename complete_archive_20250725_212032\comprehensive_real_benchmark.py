#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面真实基准测试 - 基于57个真实关键点的完整对比
Comprehensive Real Benchmark - Complete Comparison Based on 57 Real Keypoints
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import json
import matplotlib.pyplot as plt
import pandas as pd
import time
import os

# 设置样式
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

class MedicalKeypointDataset(Dataset):
    """医疗关键点数据集"""
    
    def __init__(self, point_clouds, keypoints, num_points=50000, keypoint_indices=None):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.num_points = num_points
        self.keypoint_indices = keypoint_indices
        
        # 如果指定了关键点索引，选择对应的关键点
        if keypoint_indices is not None:
            self.keypoints = self.keypoints[:, keypoint_indices, :]
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        pc = self.point_clouds[idx].copy()
        kp = self.keypoints[idx].copy()
        
        # 重采样点云到指定数量
        if len(pc) != self.num_points:
            if len(pc) > self.num_points:
                indices = np.random.choice(len(pc), self.num_points, replace=False)
                pc = pc[indices]
            else:
                indices = np.random.choice(len(pc), self.num_points, replace=True)
                pc = pc[indices]
        
        return torch.FloatTensor(pc), torch.FloatTensor(kp)

# ==================== 主流点云模型实现 ====================

class PointNet(nn.Module):
    """PointNet模型 (Qi et al., CVPR 2017)"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(1024)
        
        # 回归头
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        
        # 全局特征
        x = torch.max(x, 2)[0]  # [B, 1024]
        
        # 回归
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

class PointNetPlusPlus(nn.Module):
    """PointNet++模型 (Qi et al., NIPS 2017)"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 多尺度特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
        # 回归头
        self.fc1 = nn.Linear(512, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.4)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 层次化特征提取
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        x = F.relu(self.bn4(self.conv4(x)))
        
        # 全局特征
        x = torch.max(x, 2)[0]  # [B, 512]
        
        # 回归
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

class DGCNN(nn.Module):
    """DGCNN模型 (Wang et al., TOG 2019) - 简化版"""
    
    def __init__(self, num_points=50000, num_keypoints=12, k=20):
        super().__init__()
        self.num_keypoints = num_keypoints
        self.k = k
        
        # 图卷积层
        self.conv1 = nn.Conv2d(6, 64, kernel_size=1, bias=False)
        self.conv2 = nn.Conv2d(128, 128, kernel_size=1, bias=False)
        self.conv3 = nn.Conv2d(256, 256, kernel_size=1, bias=False)
        
        self.bn1 = nn.BatchNorm2d(64)
        self.bn2 = nn.BatchNorm2d(128)
        self.bn3 = nn.BatchNorm2d(256)
        
        # 全局特征
        self.conv4 = nn.Conv1d(448, 1024, kernel_size=1, bias=False)
        self.bn4 = nn.BatchNorm1d(1024)
        
        # 回归头
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.5)
        
    def knn(self, x, k):
        """K近邻搜索"""
        inner = -2*torch.matmul(x.transpose(2, 1), x)
        xx = torch.sum(x**2, dim=1, keepdim=True)
        pairwise_distance = -xx - inner - xx.transpose(2, 1)
        
        idx = pairwise_distance.topk(k=k, dim=-1)[1]
        return idx
    
    def get_graph_feature(self, x, k=20, idx=None):
        """获取图特征"""
        batch_size = x.size(0)
        num_points = x.size(2)
        x = x.view(batch_size, -1, num_points)
        
        if idx is None:
            idx = self.knn(x, k=k)
        
        device = torch.device('cuda' if x.is_cuda else 'cpu')
        
        idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1)*num_points
        idx = idx + idx_base
        idx = idx.view(-1)
        
        _, num_dims, _ = x.size()
        
        x = x.transpose(2, 1).contiguous()
        feature = x.view(batch_size*num_points, -1)[idx, :]
        feature = feature.view(batch_size, num_points, k, num_dims) 
        x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)
        
        feature = torch.cat((feature-x, x), dim=3).permute(0, 3, 1, 2).contiguous()
        
        return feature
    
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # EdgeConv层
        x = self.get_graph_feature(x, k=self.k)
        x = F.relu(self.bn1(self.conv1(x)))
        x1 = x.max(dim=-1, keepdim=False)[0]
        
        x = self.get_graph_feature(x1, k=self.k)
        x = F.relu(self.bn2(self.conv2(x)))
        x2 = x.max(dim=-1, keepdim=False)[0]
        
        x = self.get_graph_feature(x2, k=self.k)
        x = F.relu(self.bn3(self.conv3(x)))
        x3 = x.max(dim=-1, keepdim=False)[0]
        
        # 特征聚合
        x = torch.cat((x1, x2, x3), dim=1)
        
        # 全局特征
        x = F.relu(self.bn4(self.conv4(x)))
        x = torch.max(x, 2)[0]
        
        # 回归
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

class PointConv(nn.Module):
    """PointConv模型 (Wu et al., CVPR 2019) - 简化版"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 连续卷积层
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 回归头
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.4)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 连续卷积特征提取
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        x = F.relu(self.bn4(self.conv4(x)))
        x = F.relu(self.bn5(self.conv5(x)))
        
        # 全局特征
        x = torch.max(x, 2)[0]  # [B, 1024]
        
        # 回归
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

class OurAdaptiveModel(nn.Module):
    """我们的自适应模型"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 根据关键点数量自适应选择架构
        if num_keypoints <= 12:
            # 轻量级架构
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(512, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, num_keypoints * 3)
            )
        elif num_keypoints <= 28:
            # 增强架构
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
                nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(1024, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 1024), nn.ReLU(), nn.Dropout(0.4),
                nn.Linear(1024, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, num_keypoints * 3)
            )
        else:
            # 深度架构
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
                nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU(),
                nn.Conv1d(1024, 2048, 1), nn.BatchNorm1d(2048), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(2048, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 1024), nn.ReLU(), nn.Dropout(0.5),
                nn.Linear(1024, 1024), nn.ReLU(), nn.Dropout(0.4),
                nn.Linear(1024, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, num_keypoints * 3)
            )
        
        # 相互辅助机制
        mutual_dim = min(256, max(64, num_keypoints * 8))
        self.mutual_assistance = nn.Sequential(
            nn.Linear(num_keypoints * 3, mutual_dim),
            nn.ReLU(), nn.Dropout(0.2),
            nn.Linear(mutual_dim, mutual_dim // 2),
            nn.ReLU(),
            nn.Linear(mutual_dim // 2, num_keypoints * 3)
        )
    
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取
        features = self.feature_extractor(x)
        global_features = self.global_conv(features)
        global_feat = torch.max(global_features, 2)[0]
        
        # 预测
        initial_kp = self.predictor(global_feat)
        assistance = self.mutual_assistance(initial_kp)
        final_kp = initial_kp + 0.3 * assistance
        final_kp = final_kp.view(batch_size, self.num_keypoints, 3)
        
        return final_kp

class ComprehensiveRealBenchmark:
    """全面真实基准测试"""
    
    def __init__(self, device='cuda:3'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        print(f"🖥️ 使用设备: {self.device}")
        
        # 主流模型配置
        self.models = {
            'PointNet': PointNet,
            'PointNet++': PointNetPlusPlus,
            'DGCNN': DGCNN,
            'PointConv': PointConv,
            'Our_Adaptive': OurAdaptiveModel,
        }
        
        # 测试配置 - 先用核心配置进行快速测试
        self.point_counts = [5000, 10000]  # 核心点云点数
        self.keypoint_configs = [
            {'count': 12, 'indices': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 56]},  # 均匀分布12点
            {'count': 28, 'indices': list(range(0, 57, 2))},  # 每隔一个选择28点
        ]
        
        # 加载数据集
        self.load_dataset()
        
        self.results = []
    
    def load_dataset(self):
        """加载数据集"""
        print("📥 加载数据集...")
        
        data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
        self.point_clouds = data['point_clouds']
        self.keypoints_57 = data['keypoints_57']
        
        # 创建数据划分
        indices = np.arange(len(self.point_clouds))
        train_indices, test_val_indices = train_test_split(indices, test_size=0.4, random_state=42)
        val_indices, test_indices = train_test_split(test_val_indices, test_size=0.5, random_state=42)
        
        self.train_indices = train_indices
        self.val_indices = val_indices
        self.test_indices = test_indices
        
        print(f"✅ 数据加载完成:")
        print(f"   总样本数: {len(self.point_clouds)}")
        print(f"   训练集: {len(train_indices)} 样本")
        print(f"   验证集: {len(val_indices)} 样本")
        print(f"   测试集: {len(test_indices)} 样本")
        print(f"   关键点数: {self.keypoints_57.shape[1]} 个")
    
    def create_datasets(self, num_points, keypoint_config):
        """创建数据集"""
        
        num_keypoints = keypoint_config['count']
        keypoint_indices = keypoint_config['indices']
        
        # 创建训练集
        train_dataset = MedicalKeypointDataset(
            self.point_clouds[self.train_indices],
            self.keypoints_57[self.train_indices],
            num_points=num_points,
            keypoint_indices=keypoint_indices
        )
        
        # 创建验证集
        val_dataset = MedicalKeypointDataset(
            self.point_clouds[self.val_indices],
            self.keypoints_57[self.val_indices],
            num_points=num_points,
            keypoint_indices=keypoint_indices
        )
        
        # 创建测试集
        test_dataset = MedicalKeypointDataset(
            self.point_clouds[self.test_indices],
            self.keypoints_57[self.test_indices],
            num_points=num_points,
            keypoint_indices=keypoint_indices
        )
        
        return train_dataset, val_dataset, test_dataset
    
    def train_model(self, model, train_loader, val_loader, epochs=30):  # 减少训练轮数
        """训练模型"""
        
        optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=8, factor=0.5)
        criterion = nn.MSELoss()
        
        best_val_loss = float('inf')
        patience = 12
        patience_counter = 0
        
        for epoch in range(epochs):
            # 训练
            model.train()
            train_loss = 0.0
            
            for batch_pc, batch_kp in train_loader:
                batch_pc = batch_pc.to(self.device)
                batch_kp = batch_kp.to(self.device)
                
                optimizer.zero_grad()
                pred_kp = model(batch_pc)
                loss = criterion(pred_kp, batch_kp)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
            
            # 验证
            model.eval()
            val_loss = 0.0
            
            with torch.no_grad():
                for batch_pc, batch_kp in val_loader:
                    batch_pc = batch_pc.to(self.device)
                    batch_kp = batch_kp.to(self.device)
                    
                    pred_kp = model(batch_pc)
                    loss = criterion(pred_kp, batch_kp)
                    val_loss += loss.item()
            
            train_loss /= len(train_loader)
            val_loss /= len(val_loader)
            
            scheduler.step(val_loss)
            
            # 早停
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                best_model_state = model.state_dict().copy()
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    print(f"    早停于epoch {epoch+1}")
                    break
            
            if (epoch + 1) % 15 == 0:
                print(f"    Epoch {epoch+1}: Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")
        
        # 加载最佳模型
        model.load_state_dict(best_model_state)
        return model
    
    def evaluate_model(self, model, test_loader):
        """评估模型"""
        
        model.eval()
        all_errors = []
        
        with torch.no_grad():
            for batch_pc, batch_kp in test_loader:
                batch_pc = batch_pc.to(self.device)
                batch_kp = batch_kp.to(self.device)
                
                pred_kp = model(batch_pc)
                
                # 计算误差
                errors = torch.norm(pred_kp - batch_kp, dim=2)  # [B, num_keypoints]
                all_errors.extend(errors.cpu().numpy().flatten())
        
        all_errors = np.array(all_errors)
        
        return {
            'avg_error': np.mean(all_errors),
            'std_error': np.std(all_errors),
            'median_error': np.median(all_errors),
            'max_error': np.max(all_errors),
            'min_error': np.min(all_errors),
            'medical_rate': np.sum(all_errors <= 10) / len(all_errors) * 100,
            'excellent_rate': np.sum(all_errors <= 5) / len(all_errors) * 100,
            'precision_1mm': np.sum(all_errors <= 1) / len(all_errors) * 100
        }
    
    def run_single_experiment(self, model_name, model_class, num_points, keypoint_config):
        """运行单个实验"""
        
        num_keypoints = keypoint_config['count']
        
        print(f"\n🔄 实验: {model_name}, {num_points}点, {num_keypoints}关键点")
        
        try:
            # 创建数据集
            train_dataset, val_dataset, test_dataset = self.create_datasets(num_points, keypoint_config)
            
            # 创建数据加载器
            batch_size = min(8, len(train_dataset))
            
            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
            test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
            
            # 创建模型
            model = model_class(num_points=num_points, num_keypoints=num_keypoints)
            model = model.to(self.device)
            
            print(f"  📊 模型参数数量: {sum(p.numel() for p in model.parameters())/1e6:.2f}M")
            
            # 训练模型
            start_time = time.time()
            model = self.train_model(model, train_loader, val_loader)
            training_time = time.time() - start_time
            
            # 评估模型
            results = self.evaluate_model(model, test_loader)
            results['training_time'] = training_time
            results['num_params'] = sum(p.numel() for p in model.parameters())
            
            print(f"  ✅ 完成: 平均误差 {results['avg_error']:.2f}mm, 训练时间 {training_time:.1f}s")
            
            return results
            
        except Exception as e:
            print(f"  ❌ 实验失败: {e}")
            return None
    
    def run_comprehensive_benchmark(self):
        """运行全面基准测试"""
        
        print("\n🚀 开始全面真实基准测试...")
        print("基于57个真实关键点的完整对比")
        print("=" * 80)
        
        total_experiments = len(self.models) * len(self.point_counts) * len(self.keypoint_configs)
        current_experiment = 0
        
        for model_name, model_class in self.models.items():
            for keypoint_config in self.keypoint_configs:
                for num_points in self.point_counts:
                    current_experiment += 1
                    print(f"\n📊 进度: {current_experiment}/{total_experiments}")
                    
                    result = self.run_single_experiment(model_name, model_class, num_points, keypoint_config)
                    
                    if result:
                        result.update({
                            'model': model_name,
                            'keypoints': keypoint_config['count'],
                            'points': num_points
                        })
                        self.results.append(result)
        
        # 保存结果
        self.save_results()
        
        print(f"\n✅ 全面真实基准测试完成！")
        return self.results
    
    def save_results(self):
        """保存结果"""
        
        # 保存为CSV
        df = pd.DataFrame(self.results)
        df.to_csv('comprehensive_real_benchmark_results.csv', index=False)
        
        # 保存为JSON
        with open('comprehensive_real_benchmark_results.json', 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        print("💾 结果已保存:")
        print("   📄 comprehensive_real_benchmark_results.json")
        print("   📊 comprehensive_real_benchmark_results.csv")

if __name__ == "__main__":
    print("🧪 全面真实基准测试")
    print("基于57个真实关键点的完整模型对比")
    print("=" * 80)
    
    # 创建基准测试框架
    benchmark = ComprehensiveRealBenchmark()
    
    # 运行全面基准测试
    results = benchmark.run_comprehensive_benchmark()
    
    print(f"\n📋 全面基准测试总结:")
    print(f"   🔬 测试模型: {len(benchmark.models)} 个")
    print(f"   📊 点数配置: {len(benchmark.point_counts)} 种")
    print(f"   🎯 关键点配置: {len(benchmark.keypoint_configs)} 种")
    print(f"   📈 总实验数: {len(benchmark.models) * len(benchmark.point_counts) * len(benchmark.keypoint_configs)} 个")
    
    if results:
        best_result = min(results, key=lambda x: x['avg_error'])
        print(f"\n🏆 最佳结果:")
        print(f"   📊 模型: {best_result['model']}")
        print(f"   📊 关键点: {best_result['keypoints']}")
        print(f"   📊 点数: {best_result['points']}")
        print(f"   📊 平均误差: {best_result['avg_error']:.2f}mm")
        print(f"   📊 医疗级达标率: {best_result['medical_rate']:.1f}%")
    
    print(f"\n💡 这个全面基准测试提供了:")
    print(f"   • 基于57个真实关键点的选择")
    print(f"   • 5个主流点云模型的对比")
    print(f"   • 6种不同点云密度的测试")
    print(f"   • 3种关键点配置的评估")
    print(f"   • 完整的性能基准数据")
