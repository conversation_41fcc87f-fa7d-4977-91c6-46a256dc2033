#!/usr/bin/env python3
"""
测试5mm精度模型
Test 5mm Precision Model
验证您的5.371mm模型在50k点12关键点数据上的性能
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json
from pathlib import Path
from datetime import datetime
from sklearn.model_selection import train_test_split

# 导入AdaptivePointNet架构
class AdaptivePointNet(nn.Module):
    """自适应PointNet架构 - 与您的5mm模型相同"""
    
    def __init__(self, num_keypoints: int, dropout_rate: float = 0.4):
        super(AdaptivePointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 卷积层
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 残差连接
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        # 全连接层
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        # 批归一化
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(dropout_rate)
        
    def forward(self, x):
        batch_size = x.size(0)
        
        # 点云特征提取
        x1 = torch.relu(self.bn1(self.conv1(x)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        x4 = torch.relu(self.bn4(self.conv4(x3)))
        x5 = torch.relu(self.bn5(self.conv5(x4)))
        
        # 残差连接
        res1 = self.residual1(x1)
        x3 = x3 + res1
        
        res2 = self.residual2(x2)
        x4 = x4 + res2
        
        # 全局最大池化
        global_feature = torch.max(x5, 2)[0]
        
        # 全连接层
        x = torch.relu(self.bn_fc1(self.fc1(global_feature)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc2(self.fc2(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc3(self.fc3(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc4(self.fc4(x)))
        x = self.fc5(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

class Model5mmTester:
    """5mm精度模型测试器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def load_5mm_model(self, model_path):
        """加载5mm精度模型"""
        print(f"🎯 加载5mm精度模型: {model_path}")
        
        # 创建模型
        model = AdaptivePointNet(num_keypoints=12, dropout_rate=0.4).to(self.device)
        
        # 加载权重
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
                print(f"✅ 从checkpoint加载模型权重")
            else:
                model.load_state_dict(checkpoint)
                print(f"✅ 直接加载模型权重")
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return None
        
        model.eval()
        total_params = sum(p.numel() for p in model.parameters())
        print(f"📊 模型参数: {total_params:,}")
        
        return model
    
    def load_12kp_data(self):
        """加载12关键点数据"""
        print(f"📦 加载12关键点数据...")
        
        data_path = 'data/processed/f3_reduced_12kp_stable.npz'
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        selected_indices = data['selected_indices']
        
        print(f"✅ 12关键点数据加载完成: {len(sample_ids)} 样本")
        print(f"   点云形状: {point_clouds.shape}")
        print(f"   关键点形状: {keypoints.shape}")
        print(f"   选择的关键点索引: {selected_indices}")
        print(f"   点云范围: [{np.min([np.min(pc) for pc in point_clouds]):.1f}, {np.max([np.max(pc) for pc in point_clouds]):.1f}]mm")
        print(f"   关键点范围: [{np.min(keypoints):.1f}, {np.max(keypoints):.1f}]mm")
        
        # 数据划分
        indices = np.arange(len(sample_ids))
        train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
        train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
        
        return {
            'sample_ids': sample_ids,
            'point_clouds': point_clouds,
            'keypoints': keypoints,
            'selected_indices': selected_indices,
            'train_indices': train_indices,
            'val_indices': val_indices,
            'test_indices': test_indices
        }
    
    def prepare_data_for_model(self, point_clouds, target_points=4096):
        """为模型准备数据 - 下采样到4096点"""
        prepared_pcs = []
        
        for pc in point_clouds:
            pc_array = np.array(pc, dtype=np.float32)
            
            # 下采样到目标点数
            if len(pc_array) > target_points:
                indices = np.random.choice(len(pc_array), target_points, replace=False)
                pc_sampled = pc_array[indices]
            elif len(pc_array) < target_points:
                indices = np.random.choice(len(pc_array), target_points, replace=True)
                pc_sampled = pc_array[indices]
            else:
                pc_sampled = pc_array
            
            prepared_pcs.append(pc_sampled)
        
        return np.array(prepared_pcs)
    
    def evaluate_5mm_model(self, model, data, split='test'):
        """评估5mm模型性能"""
        print(f"\n📏 评估5mm模型性能 ({split}集)")
        
        model.eval()
        
        # 获取数据
        if split == 'test':
            indices = data['test_indices']
        elif split == 'val':
            indices = data['val_indices']
        else:
            indices = data['train_indices']
        
        point_clouds = data['point_clouds'][indices]
        keypoints = data['keypoints'][indices]
        
        # 准备数据
        prepared_pcs = self.prepare_data_for_model(point_clouds)
        
        total_error = 0
        num_samples = 0
        detailed_results = []
        
        with torch.no_grad():
            for i, (pc, kp) in enumerate(zip(prepared_pcs, keypoints)):
                # 转换为tensor
                pc_tensor = torch.FloatTensor(pc).transpose(0, 1).unsqueeze(0).to(self.device)
                kp_tensor = torch.FloatTensor(kp).to(self.device)
                
                # 预测
                pred_kp = model(pc_tensor).squeeze(0)
                
                # 计算误差 (在真实物理空间)
                errors = torch.norm(pred_kp - kp_tensor, dim=1).cpu().numpy()
                mean_error = np.mean(errors)
                
                total_error += mean_error
                num_samples += 1
                
                # 保存详细结果
                detailed_results.append({
                    'sample_index': i,
                    'predicted': pred_kp.cpu().numpy(),
                    'true': kp,
                    'errors': errors,
                    'mean_error': mean_error
                })
                
                print(f"样本 {i+1}: 平均误差 = {mean_error:.2f}mm (范围: {np.min(errors):.2f}-{np.max(errors):.2f}mm)")
                
                # 清理内存
                del pc_tensor, kp_tensor, pred_kp
                torch.cuda.empty_cache()
        
        overall_error = total_error / num_samples if num_samples > 0 else float('inf')
        
        print(f"✅ {split}集总体平均误差: {overall_error:.3f}mm")
        
        return overall_error, detailed_results
    
    def visualize_5mm_results(self, detailed_results, max_samples=3):
        """可视化5mm模型结果"""
        print(f"\n📊 可视化5mm模型结果...")
        
        viz_dir = Path("results/5mm_model_visualization")
        viz_dir.mkdir(parents=True, exist_ok=True)
        
        for i, result in enumerate(detailed_results[:max_samples]):
            fig = plt.figure(figsize=(20, 12))
            
            true_kp = result['true']
            pred_kp = result['predicted']
            errors = result['errors']
            
            # 3D可视化
            ax1 = fig.add_subplot(231, projection='3d')
            
            ax1.scatter(true_kp[:, 0], true_kp[:, 1], true_kp[:, 2], 
                       c='red', s=100, label='True Keypoints', marker='o', alpha=0.8)
            ax1.scatter(pred_kp[:, 0], pred_kp[:, 1], pred_kp[:, 2], 
                       c='blue', s=100, label='Predicted Keypoints', marker='^', alpha=0.8)
            
            # 绘制误差线
            for j in range(len(true_kp)):
                ax1.plot([true_kp[j, 0], pred_kp[j, 0]], 
                        [true_kp[j, 1], pred_kp[j, 1]], 
                        [true_kp[j, 2], pred_kp[j, 2]], 'k--', alpha=0.5)
            
            ax1.set_title(f'5mm Model - Sample {i+1}\nMean Error: {result["mean_error"]:.2f}mm')
            ax1.legend()
            ax1.set_xlabel('X (mm)')
            ax1.set_ylabel('Y (mm)')
            ax1.set_zlabel('Z (mm)')
            
            # 误差分布
            ax2 = fig.add_subplot(232)
            bars = ax2.bar(range(len(errors)), errors, alpha=0.7, color='skyblue')
            
            # 颜色编码误差
            for j, (bar, error) in enumerate(zip(bars, errors)):
                if error < 2.0:
                    bar.set_color('green')
                elif error < 5.0:
                    bar.set_color('orange')
                else:
                    bar.set_color('red')
            
            ax2.axhline(y=2.0, color='green', linestyle='--', label='2mm (优秀)')
            ax2.axhline(y=5.0, color='orange', linestyle='--', label='5mm (医疗级)')
            ax2.axhline(y=10.0, color='red', linestyle='--', label='10mm (可接受)')
            
            ax2.set_title(f'Error Distribution - Sample {i+1}')
            ax2.set_xlabel('Keypoint Index')
            ax2.set_ylabel('Error (mm)')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # X-Y投影
            ax3 = fig.add_subplot(233)
            ax3.scatter(true_kp[:, 0], true_kp[:, 1], c='red', s=50, label='True', alpha=0.7)
            ax3.scatter(pred_kp[:, 0], pred_kp[:, 1], c='blue', s=50, label='Predicted', alpha=0.7)
            for j in range(len(true_kp)):
                ax3.plot([true_kp[j, 0], pred_kp[j, 0]], 
                        [true_kp[j, 1], pred_kp[j, 1]], 'k--', alpha=0.3)
            ax3.set_title('X-Y Projection')
            ax3.set_xlabel('X (mm)')
            ax3.set_ylabel('Y (mm)')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            
            # 性能统计
            ax4 = fig.add_subplot(234)
            stats_text = f'Sample {i+1} Performance:\n\n'
            stats_text += f'Mean Error: {result["mean_error"]:.2f} mm\n'
            stats_text += f'Max Error: {np.max(errors):.2f} mm\n'
            stats_text += f'Min Error: {np.min(errors):.2f} mm\n'
            stats_text += f'Std Error: {np.std(errors):.2f} mm\n'
            stats_text += f'Median Error: {np.median(errors):.2f} mm\n\n'
            
            stats_text += f'Performance Levels:\n'
            stats_text += f'<2mm (优秀): {np.sum(errors < 2.0)}/12 ({np.sum(errors < 2.0)/12*100:.1f}%)\n'
            stats_text += f'<5mm (医疗级): {np.sum(errors < 5.0)}/12 ({np.sum(errors < 5.0)/12*100:.1f}%)\n'
            stats_text += f'<10mm (可接受): {np.sum(errors < 10.0)}/12 ({np.sum(errors < 10.0)/12*100:.1f}%)\n\n'
            
            if result["mean_error"] < 2.0:
                stats_text += '🎉 Outstanding Performance!'
            elif result["mean_error"] < 5.0:
                stats_text += '🏆 Medical Grade Performance!'
            elif result["mean_error"] < 10.0:
                stats_text += '👍 Good Performance!'
            else:
                stats_text += '📈 Needs Improvement'
            
            ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, 
                    fontsize=10, verticalalignment='top', fontfamily='monospace')
            ax4.set_xlim(0, 1)
            ax4.set_ylim(0, 1)
            ax4.set_title('Performance Statistics')
            ax4.axis('off')
            
            # 关键点分析
            ax5 = fig.add_subplot(235)
            keypoint_names = [f'KP-{idx}' for idx in [2,3,4,5,6,7,8,9,10,11,16,17]]
            
            ax5.barh(keypoint_names, errors, alpha=0.7)
            ax5.axvline(x=2.0, color='green', linestyle='--', alpha=0.7)
            ax5.axvline(x=5.0, color='orange', linestyle='--', alpha=0.7)
            ax5.axvline(x=10.0, color='red', linestyle='--', alpha=0.7)
            
            ax5.set_title('Per-Keypoint Error Analysis')
            ax5.set_xlabel('Error (mm)')
            ax5.grid(True, alpha=0.3)
            
            # 数据范围信息
            ax6 = fig.add_subplot(236)
            range_text = f'Data Range Analysis:\n\n'
            range_text += f'True Keypoints Range:\n'
            range_text += f'X: [{np.min(true_kp[:, 0]):.1f}, {np.max(true_kp[:, 0]):.1f}] mm\n'
            range_text += f'Y: [{np.min(true_kp[:, 1]):.1f}, {np.max(true_kp[:, 1]):.1f}] mm\n'
            range_text += f'Z: [{np.min(true_kp[:, 2]):.1f}, {np.max(true_kp[:, 2]):.1f}] mm\n\n'
            
            range_text += f'Predicted Keypoints Range:\n'
            range_text += f'X: [{np.min(pred_kp[:, 0]):.1f}, {np.max(pred_kp[:, 0]):.1f}] mm\n'
            range_text += f'Y: [{np.min(pred_kp[:, 1]):.1f}, {np.max(pred_kp[:, 1]):.1f}] mm\n'
            range_text += f'Z: [{np.min(pred_kp[:, 2]):.1f}, {np.max(pred_kp[:, 2]):.1f}] mm\n\n'
            
            range_text += f'Model Configuration:\n'
            range_text += f'Architecture: AdaptivePointNet\n'
            range_text += f'Input Points: 4096 (from 50k)\n'
            range_text += f'Output Keypoints: 12\n'
            range_text += f'Target Precision: 5mm'
            
            ax6.text(0.05, 0.95, range_text, transform=ax6.transAxes, 
                    fontsize=9, verticalalignment='top', fontfamily='monospace')
            ax6.set_xlim(0, 1)
            ax6.set_ylim(0, 1)
            ax6.set_title('Data Range & Model Info')
            ax6.axis('off')
            
            plt.tight_layout()
            plt.savefig(viz_dir / f"5mm_model_result_sample_{i+1}.png", 
                       dpi=150, bbox_inches='tight')
            print(f"💾 保存可视化: 5mm_model_result_sample_{i+1}.png")
            plt.show()

def test_5mm_precision_model():
    """测试5mm精度模型"""
    print("🎯 测试5mm精度模型")
    print("=" * 60)
    print("模型: AdaptivePointNet (12关键点)")
    print("数据: 50k点云 → 4096点采样")
    print("目标: 验证5.371mm精度")
    
    tester = Model5mmTester()
    
    # 查找最佳的12关键点模型
    model_candidates = [
        "archive/old_models/best_baseline_12kp_6.208mm.pth",
        "archive/old_models/best_12kp_model_6.208mm_20250717_111327.pth",
        "archive/old_models/best_feature_pyramid_pointnet_12kp_6.384mm.pth",
        "archive/old_models/best_conservative_wing_12kp_6.871mm.pth",
        "archive/old_models/best_optimized_final_12kp_6.821mm.pth"
    ]
    
    best_model = None
    best_model_path = None
    
    for model_path in model_candidates:
        if Path(model_path).exists():
            print(f"\n🔍 尝试加载: {model_path}")
            model = tester.load_5mm_model(model_path)
            if model is not None:
                best_model = model
                best_model_path = model_path
                break
    
    if best_model is None:
        print("❌ 未找到可用的5mm精度模型")
        return
    
    print(f"\n✅ 成功加载模型: {best_model_path}")
    
    # 加载12关键点数据
    data = tester.load_12kp_data()
    
    # 评估模型性能
    test_error, test_results = tester.evaluate_5mm_model(best_model, data, 'test')
    val_error, val_results = tester.evaluate_5mm_model(best_model, data, 'val')
    
    # 计算总体统计
    all_errors = [r['mean_error'] for r in test_results]
    
    print(f"\n📊 5mm模型性能总结:")
    print("=" * 50)
    print(f"验证集误差: {val_error:.3f}mm")
    print(f"测试集误差: {test_error:.3f}mm")
    print(f"误差范围: {np.min(all_errors):.3f} - {np.max(all_errors):.3f}mm")
    print(f"标准差: {np.std(all_errors):.3f}mm")
    
    # 性能等级分析
    excellent_samples = np.sum(np.array(all_errors) < 2.0)
    medical_samples = np.sum(np.array(all_errors) < 5.0)
    acceptable_samples = np.sum(np.array(all_errors) < 10.0)
    
    print(f"\n🎯 性能等级分析:")
    print(f"优秀 (<2mm): {excellent_samples}/{len(all_errors)} ({excellent_samples/len(all_errors)*100:.1f}%)")
    print(f"医疗级 (<5mm): {medical_samples}/{len(all_errors)} ({medical_samples/len(all_errors)*100:.1f}%)")
    print(f"可接受 (<10mm): {acceptable_samples}/{len(all_errors)} ({acceptable_samples/len(all_errors)*100:.1f}%)")
    
    # 可视化结果
    tester.visualize_5mm_results(test_results, max_samples=3)
    
    # 保存测试报告
    test_report = {
        "test_timestamp": datetime.now().isoformat(),
        "model_path": best_model_path,
        "model_architecture": "AdaptivePointNet",
        "dataset": "f3_reduced_12kp_stable.npz",
        "input_points": "4096 (from 50k)",
        "output_keypoints": 12,
        "validation_error": val_error,
        "test_error": test_error,
        "error_statistics": {
            "mean": np.mean(all_errors),
            "std": np.std(all_errors),
            "min": np.min(all_errors),
            "max": np.max(all_errors),
            "median": np.median(all_errors)
        },
        "performance_levels": {
            "excellent_percent": excellent_samples/len(all_errors)*100,
            "medical_grade_percent": medical_samples/len(all_errors)*100,
            "acceptable_percent": acceptable_samples/len(all_errors)*100
        },
        "detailed_errors": all_errors,
        "conclusion": f"模型在测试集上达到{test_error:.3f}mm精度，{'达到' if test_error < 6.0 else '接近'}5mm医疗级目标"
    }
    
    # 保存报告
    report_dir = Path("results/5mm_model_tests")
    report_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = report_dir / f"5mm_model_test_report_{timestamp}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(test_report, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 测试报告已保存: {report_file}")
    
    return test_report

if __name__ == "__main__":
    report = test_5mm_precision_model()
