#!/usr/bin/env python3
"""
快速预处理效果测试 - 轻量版本
Quick Preprocessing Effect Test - Lightweight Version
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import json
from pathlib import Path
from datetime import datetime
from sklearn.model_selection import train_test_split

class LightweightKeypointNet(nn.Module):
    """轻量级关键点检测网络 - 减少内存使用"""
    
    def __init__(self, input_dim=3, hidden_dim=128, output_dim=19*3, dropout=0.2):
        super().__init__()
        
        # 简化的特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU()
        )
        
        # 关键点回归器
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(hidden_dim, 256),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, output_dim)
        )
        
    def forward(self, point_cloud):
        batch_size = point_cloud.size(0)
        
        # 点级特征提取
        pc_flat = point_cloud.view(-1, 3)  # (B*N, 3)
        features = self.feature_extractor(pc_flat)  # (B*N, hidden_dim)
        features = features.view(batch_size, -1, features.size(-1))  # (B, N, hidden_dim)
        
        # 全局最大池化
        global_feature, _ = torch.max(features, dim=1)  # (B, hidden_dim)
        
        # 预测关键点
        keypoints = self.keypoint_regressor(global_feature)  # (B, 19*3)
        return keypoints.view(batch_size, 19, 3)

class QuickTester:
    """快速测试器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def load_data(self, data_path):
        """加载数据"""
        print(f"📦 加载数据: {data_path}")
        
        data = np.load(data_path, allow_pickle=True)
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        # 下采样点云以减少内存使用
        downsampled_pcs = []
        for pc in point_clouds:
            pc_array = np.array(pc, dtype=np.float32)
            if len(pc_array) > 2048:
                indices = np.random.choice(len(pc_array), 2048, replace=False)
                pc_downsampled = pc_array[indices]
            else:
                pc_downsampled = pc_array
            downsampled_pcs.append(pc_downsampled)
        
        # 确保所有点云有相同的点数
        target_points = 2048
        uniform_pcs = []
        for pc in downsampled_pcs:
            if len(pc) < target_points:
                indices = np.random.choice(len(pc), target_points, replace=True)
                pc = pc[indices]
            uniform_pcs.append(pc)
        
        point_clouds = np.array(uniform_pcs, dtype=np.float32)
        keypoints = np.array([np.array(kp, dtype=np.float32) for kp in keypoints])
        
        print(f"✅ 数据加载完成: {len(sample_ids)} 样本")
        print(f"   点云形状: {point_clouds.shape}")
        print(f"   关键点形状: {keypoints.shape}")
        
        # 数据划分
        indices = np.arange(len(sample_ids))
        train_val_indices, test_indices = train_test_split(
            indices, test_size=0.15, random_state=42
        )
        train_indices, val_indices = train_test_split(
            train_val_indices, test_size=0.18, random_state=42
        )
        
        return {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
    
    def train_model(self, data, k_shot=15, epochs=50, lr=0.002):
        """训练轻量级模型"""
        print(f"\n🎯 训练轻量级模型 ({k_shot}-shot, {epochs} epochs)")
        
        # 创建轻量级模型
        model = LightweightKeypointNet().to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        patience = 0
        max_patience = 20
        
        for epoch in range(epochs):
            model.train()
            
            # 采样训练数据
            train_indices = np.random.choice(
                len(data['train']['point_clouds']), 
                min(k_shot, len(data['train']['point_clouds'])), 
                replace=False
            )
            
            train_pcs = data['train']['point_clouds'][train_indices]
            train_kps = data['train']['keypoints'][train_indices]
            
            # 简单数据增强
            aug_pcs = []
            aug_kps = []
            
            for pc, kp in zip(train_pcs, train_kps):
                # 原始数据
                aug_pcs.append(pc)
                aug_kps.append(kp)
                
                # 轻微旋转
                angle = np.random.uniform(-0.05, 0.05)
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], dtype=np.float32)
                
                aug_pc = pc @ rotation.T
                aug_kp = kp @ rotation.T
                aug_pcs.append(aug_pc)
                aug_kps.append(aug_kp)
                
                # 轻微噪声
                noise_pc = pc + np.random.normal(0, 0.01, pc.shape).astype(np.float32)
                aug_pcs.append(noise_pc)
                aug_kps.append(kp)
            
            # 分批处理以减少内存使用
            batch_size = 4
            total_loss = 0
            num_batches = 0
            
            for i in range(0, len(aug_pcs), batch_size):
                batch_pcs = torch.FloatTensor(aug_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(aug_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                pred_kps = model(batch_pcs)
                loss = criterion(pred_kps, batch_kps)
                loss.backward()
                
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                total_loss += loss.item()
                num_batches += 1
                
                # 清理GPU内存
                del batch_pcs, batch_kps, pred_kps, loss
                torch.cuda.empty_cache()
            
            scheduler.step()
            avg_loss = total_loss / num_batches if num_batches > 0 else 0
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_model(model, data, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                    patience = 0
                else:
                    patience += 1
                
                print(f"Epoch {epoch:2d}: Loss={avg_loss:.4f}, Val_Error={val_error:.4f}")
                
                if patience >= max_patience:
                    print(f"早停在epoch {epoch}")
                    break
        
        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def evaluate_model(self, model, data, split='test'):
        """评估模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = data[split]['point_clouds']
            kps = data[split]['keypoints']
            
            # 分批评估以减少内存使用
            batch_size = 2
            for i in range(0, len(pcs), batch_size):
                batch_pcs = torch.FloatTensor(pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(kps[i:i+batch_size]).to(self.device)
                
                pred_kps = model(batch_pcs)
                
                for j in range(len(batch_pcs)):
                    error = torch.mean(torch.norm(pred_kps[j] - batch_kps[j], dim=1))
                    total_error += error.item()
                    num_samples += 1
                
                # 清理GPU内存
                del batch_pcs, batch_kps, pred_kps
                torch.cuda.empty_cache()
        
        return total_error / num_samples if num_samples > 0 else float('inf')

def quick_comparison_test():
    """快速对比测试"""
    print("🚀 快速预处理效果对比测试")
    print("=" * 60)
    
    tester = QuickTester()
    
    # 测试原始数据
    print("\n📊 测试原始数据...")
    original_data = tester.load_data('data/raw/high_quality_f3_dataset.npz')
    original_model, original_val_error = tester.train_model(original_data, k_shot=15, epochs=40)
    original_test_error = tester.evaluate_model(original_model, original_data, 'test')
    
    print(f"✅ 原始数据结果: {original_test_error:.4f}")
    
    # 清理GPU内存
    del original_model, original_data
    torch.cuda.empty_cache()
    
    # 测试预处理数据
    print("\n📊 测试预处理数据...")
    processed_files = list(Path("data/processed").glob("lightweight_preprocessed_*.npz"))
    if not processed_files:
        print("❌ 未找到预处理数据")
        return
    
    latest_file = max(processed_files, key=lambda x: x.stat().st_mtime)
    processed_data = tester.load_data(str(latest_file))
    processed_model, processed_val_error = tester.train_model(processed_data, k_shot=15, epochs=40)
    processed_test_error = tester.evaluate_model(processed_model, processed_data, 'test')
    
    print(f"✅ 预处理数据结果: {processed_test_error:.4f}")
    
    # 计算改进
    improvement = (original_test_error - processed_test_error) / original_test_error * 100
    
    print(f"\n📊 对比结果:")
    print(f"   原始数据误差: {original_test_error:.4f}")
    print(f"   预处理数据误差: {processed_test_error:.4f}")
    print(f"   改进幅度: {improvement:+.1f}%")
    
    if improvement > 5:
        print("🎉 预处理效果显著！")
        status = "显著改进"
    elif improvement > 0:
        print("👍 预处理有一定效果")
        status = "有效改进"
    else:
        print("⚠️ 预处理效果不明显")
        status = "效果有限"
    
    # 保存结果
    results = {
        "test_timestamp": datetime.now().isoformat(),
        "original_error": original_test_error,
        "processed_error": processed_test_error,
        "improvement_percent": improvement,
        "status": status,
        "test_config": {
            "k_shot": 15,
            "epochs": 40,
            "point_cloud_size": 2048,
            "model": "LightweightKeypointNet"
        }
    }
    
    results_dir = Path("results/quick_tests")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = results_dir / f"quick_preprocessing_test_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 测试结果已保存: {results_file}")
    
    return results

if __name__ == "__main__":
    results = quick_comparison_test()
