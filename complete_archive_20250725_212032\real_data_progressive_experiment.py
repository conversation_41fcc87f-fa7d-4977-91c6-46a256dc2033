#!/usr/bin/env python3
"""
基于真实数据的渐进式关键点实验
Real Data Progressive Keypoint Experiment
使用现有的12关键点数据，通过子集和扩展来模拟不同数量的关键点
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
import json
from pathlib import Path
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 导入已验证的模型架构
from analysis_and_simple_universal_model import SimplifiedUniversalModel

class RealDataProgressiveExperiment:
    """基于真实数据的渐进式实验"""
    
    def __init__(self, device='cuda:1'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        self.experiment_results = []
        
    def load_real_12_keypoint_data(self):
        """加载真实的12关键点数据"""
        print("📥 加载真实12关键点数据")
        print("=" * 50)
        
        try:
            # 加载女性数据
            female_data = np.load('archive/old_experiments/f3_reduced_12kp_female.npz')
            female_pc = female_data['point_clouds']
            female_kp = female_data['keypoints']
            
            # 加载男性数据
            male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz')
            male_pc = male_data['point_clouds']
            male_kp = male_data['keypoints']
            
            # 合并数据
            all_pc = np.vstack([female_pc, male_pc])
            all_kp = np.vstack([female_kp, male_kp])
            
            print(f"✅ 真实数据加载成功:")
            print(f"   女性: {len(female_pc)}样本")
            print(f"   男性: {len(male_pc)}样本")
            print(f"   总计: {len(all_pc)}样本")
            print(f"   关键点: {all_kp.shape[1]}个")
            print(f"   点云形状: {all_pc.shape}")
            print(f"   关键点形状: {all_kp.shape}")
            
            return all_pc, all_kp
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return None, None
    
    def create_keypoint_subsets_from_12(self, keypoints_12):
        """从12个关键点创建不同数量的子集"""
        print("\n🔢 从12关键点创建子集")
        print("=" * 50)
        
        # 定义不同的关键点数量和对应的采样策略
        keypoint_configs = {
            3: {
                'indices': [0, 5, 11],  # 首、中、尾
                'description': '极简配置 (首中尾)'
            },
            6: {
                'indices': [0, 2, 4, 7, 9, 11],  # 均匀分布
                'description': '基础配置 (均匀分布)'
            },
            9: {
                'indices': [0, 1, 3, 4, 6, 7, 8, 10, 11],  # 密集采样
                'description': '中等配置 (密集采样)'
            },
            12: {
                'indices': list(range(12)),  # 全部关键点
                'description': '完整配置 (全部关键点)'
            }
        }
        
        keypoint_subsets = {}
        
        for num_kp, config in keypoint_configs.items():
            indices = config['indices']
            subset_kp = keypoints_12[:, indices, :]
            keypoint_subsets[num_kp] = {
                'keypoints': subset_kp,
                'indices': indices,
                'description': config['description']
            }
            
            print(f"  {num_kp}关键点: {config['description']}")
            print(f"    索引: {indices}")
            print(f"    形状: {subset_kp.shape}")
        
        return keypoint_subsets
    
    def create_adaptive_model(self, num_keypoints):
        """创建自适应模型架构"""
        
        class AdaptiveUniversalModel(nn.Module):
            """自适应通用模型"""
            
            def __init__(self, num_points=50000, num_keypoints=12):
                super().__init__()
                self.num_points = num_points
                self.num_keypoints = num_keypoints
                
                # 基础特征提取器 (固定)
                self.feature_extractor = nn.Sequential(
                    nn.Conv1d(3, 64, 1),
                    nn.BatchNorm1d(64),
                    nn.ReLU(),
                    nn.Conv1d(64, 128, 1),
                    nn.BatchNorm1d(128),
                    nn.ReLU(),
                    nn.Conv1d(128, 256, 1),
                    nn.BatchNorm1d(256),
                    nn.ReLU(),
                )
                
                # 自适应特征适配层
                self.adaptive_layer = nn.Sequential(
                    nn.Conv1d(256, 256, 1),
                    nn.BatchNorm1d(256),
                    nn.ReLU(),
                    nn.Dropout(0.1),
                )
                
                # 根据关键点数量调整网络容量
                if num_keypoints <= 3:
                    # 极简网络
                    hidden_dim = 128
                    mutual_dim = 64
                elif num_keypoints <= 6:
                    # 小型网络
                    hidden_dim = 256
                    mutual_dim = 128
                elif num_keypoints <= 9:
                    # 中型网络
                    hidden_dim = 384
                    mutual_dim = 192
                else:
                    # 完整网络
                    hidden_dim = 512
                    mutual_dim = 256
                
                # 相互辅助机制
                self.mutual_assistance = nn.Sequential(
                    nn.Linear(num_keypoints * 3, mutual_dim),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(mutual_dim, mutual_dim // 2),
                    nn.ReLU(),
                    nn.Linear(mutual_dim // 2, num_keypoints * 3)
                )
                
                # 关键点预测头
                self.keypoint_predictor = nn.Sequential(
                    nn.Linear(256, hidden_dim),
                    nn.ReLU(),
                    nn.Dropout(0.3),
                    nn.Linear(hidden_dim, hidden_dim // 2),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(hidden_dim // 2, num_keypoints * 3)
                )
                
            def forward(self, x):
                batch_size = x.size(0)
                x = x.transpose(2, 1)  # [B, 3, N]
                
                # 特征提取
                features = self.feature_extractor(x)  # [B, 256, N]
                
                # 自适应特征
                adapted_features = self.adaptive_layer(features)  # [B, 256, N]
                
                # 全局特征
                global_feat = torch.max(adapted_features, 2)[0]  # [B, 256]
                
                # 初始预测
                initial_kp = self.keypoint_predictor(global_feat)  # [B, num_keypoints*3]
                
                # 相互辅助
                assistance = self.mutual_assistance(initial_kp)  # [B, num_keypoints*3]
                
                # 最终预测 (残差连接)
                final_kp = initial_kp + 0.3 * assistance
                final_kp = final_kp.view(batch_size, self.num_keypoints, 3)
                
                return final_kp
        
        return AdaptiveUniversalModel(num_points=50000, num_keypoints=num_keypoints)
    
    def train_and_evaluate_model(self, point_clouds, keypoints, num_keypoints, config_info):
        """训练和评估模型"""
        print(f"\n🎯 训练{num_keypoints}关键点模型")
        print(f"   配置: {config_info['description']}")
        print(f"   索引: {config_info['indices']}")
        print("=" * 50)
        
        # 数据分割
        train_pc, test_pc, train_kp, test_kp = train_test_split(
            point_clouds, keypoints, test_size=0.2, random_state=42)
        
        print(f"📊 数据分割:")
        print(f"   训练: {len(train_pc)}样本")
        print(f"   测试: {len(test_pc)}样本")
        
        # 创建自适应模型
        model = self.create_adaptive_model(num_keypoints).to(self.device)
        criterion = nn.MSELoss()
        
        # 根据关键点数量调整学习率
        base_lr = 0.001
        if num_keypoints <= 3:
            lr = base_lr * 1.5  # 简单任务用更大学习率
        elif num_keypoints <= 6:
            lr = base_lr * 1.2
        else:
            lr = base_lr
        
        optimizer = optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        
        print(f"🏗️ 模型配置:")
        print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
        print(f"   学习率: {lr}")
        
        # 转换为张量
        train_pc_tensor = torch.FloatTensor(train_pc).to(self.device)
        train_kp_tensor = torch.FloatTensor(train_kp).to(self.device)
        test_pc_tensor = torch.FloatTensor(test_pc).to(self.device)
        test_kp_tensor = torch.FloatTensor(test_kp).to(self.device)
        
        # 创建数据加载器
        batch_size = max(2, min(8, len(train_pc) // 4))
        train_dataset = TensorDataset(train_pc_tensor, train_kp_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        
        # 训练循环
        model.train()
        best_loss = float('inf')
        patience = 0
        max_epochs = 80  # 固定训练轮数
        
        for epoch in range(max_epochs):
            epoch_loss = 0.0
            
            for batch_pc, batch_kp in train_loader:
                optimizer.zero_grad()
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                loss.backward()
                optimizer.step()
                epoch_loss += loss.item()
            
            avg_loss = epoch_loss / len(train_loader)
            scheduler.step(avg_loss)
            
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience = 0
                torch.save(model.state_dict(), f'best_adaptive_model_{num_keypoints}kp.pth')
            else:
                patience += 1
                if patience >= 15:
                    print(f"早停于epoch {epoch+1}")
                    break
            
            if epoch % 20 == 0:
                print(f"Epoch {epoch+1}: Loss = {avg_loss:.6f}")
        
        # 加载最佳模型并测试
        model.load_state_dict(torch.load(f'best_adaptive_model_{num_keypoints}kp.pth'))
        model.eval()
        
        with torch.no_grad():
            predicted = model(test_pc_tensor)
            test_errors = torch.norm(predicted - test_kp_tensor, dim=2)
            avg_error = torch.mean(test_errors).item()
            
            # 计算准确率
            sample_errors = torch.mean(test_errors, dim=1)
            errors_5mm = torch.sum(sample_errors <= 5.0).item()
            errors_10mm = torch.sum(sample_errors <= 10.0).item()
            errors_15mm = torch.sum(sample_errors <= 15.0).item()
            
            acc_5mm = (errors_5mm / len(test_pc)) * 100
            acc_10mm = (errors_10mm / len(test_pc)) * 100
            acc_15mm = (errors_15mm / len(test_pc)) * 100
        
        result = {
            'num_keypoints': num_keypoints,
            'description': config_info['description'],
            'keypoint_indices': config_info['indices'],
            'train_samples': len(train_pc),
            'test_samples': len(test_pc),
            'avg_error': avg_error,
            'accuracy_5mm': acc_5mm,
            'accuracy_10mm': acc_10mm,
            'accuracy_15mm': acc_15mm,
            'medical_grade': avg_error <= 10.0,
            'excellent_grade': avg_error <= 5.0,
            'parameters': sum(p.numel() for p in model.parameters()),
            'epochs_trained': epoch + 1,
            'learning_rate': lr
        }
        
        print(f"\n📊 {num_keypoints}关键点模型结果:")
        print(f"   平均误差: {result['avg_error']:.2f}mm")
        print(f"   5mm准确率: {result['accuracy_5mm']:.1f}%")
        print(f"   10mm准确率: {result['accuracy_10mm']:.1f}%")
        print(f"   医疗级达标: {'✅' if result['medical_grade'] else '❌'}")
        print(f"   优秀级达标: {'✅' if result['excellent_grade'] else '❌'}")
        
        self.experiment_results.append(result)
        return result
    
    def run_real_data_progressive_experiment(self):
        """运行基于真实数据的渐进式实验"""
        print("🚀 开始基于真实数据的渐进式关键点实验")
        print("Real Data Progressive Keypoint Experiment")
        print("=" * 70)
        
        # 加载真实12关键点数据
        point_clouds, keypoints_12 = self.load_real_12_keypoint_data()
        if point_clouds is None:
            print("❌ 数据加载失败，退出")
            return
        
        # 创建关键点子集
        keypoint_subsets = self.create_keypoint_subsets_from_12(keypoints_12)
        
        # 逐步训练不同数量的关键点模型
        for num_kp in sorted(keypoint_subsets.keys()):
            config = keypoint_subsets[num_kp]
            keypoints = config['keypoints']
            
            try:
                self.train_and_evaluate_model(point_clouds, keypoints, num_kp, config)
            except Exception as e:
                print(f"❌ {num_kp}关键点训练失败: {e}")
                continue
        
        # 保存结果和分析
        self.save_results()
        self.create_comprehensive_analysis()
        
        return self.experiment_results
    
    def save_results(self):
        """保存实验结果"""
        results = {
            'experiment_type': 'real_data_progressive_keypoint_scaling',
            'description': '基于真实12关键点数据的渐进式扩展实验',
            'base_dataset': '12关键点医疗数据集 (97样本)',
            'keypoint_configurations': [
                {
                    'num_keypoints': r['num_keypoints'],
                    'description': r['description'],
                    'indices': r['keypoint_indices'],
                    'performance': r['avg_error'],
                    'medical_grade': r['medical_grade']
                } for r in self.experiment_results
            ],
            'results': self.experiment_results,
            'summary': {
                'total_experiments': len(self.experiment_results),
                'best_performance': min([r['avg_error'] for r in self.experiment_results]) if self.experiment_results else 0,
                'medical_grade_count': len([r for r in self.experiment_results if r['medical_grade']]),
                'excellent_grade_count': len([r for r in self.experiment_results if r['excellent_grade']])
            },
            'timestamp': '2025-07-25'
        }
        
        with open('real_data_progressive_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 真实数据渐进式实验结果已保存到 real_data_progressive_results.json")
    
    def create_comprehensive_analysis(self):
        """创建综合分析"""
        print("\n📈 创建综合分析")
        print("=" * 50)
        
        if not self.experiment_results:
            print("❌ 没有实验结果可分析")
            return
        
        # 提取数据
        keypoint_counts = [r['num_keypoints'] for r in self.experiment_results]
        avg_errors = [r['avg_error'] for r in self.experiment_results]
        acc_10mm = [r['accuracy_10mm'] for r in self.experiment_results]
        parameters = [r['parameters'] for r in self.experiment_results]
        descriptions = [r['description'] for r in self.experiment_results]
        
        # 创建可视化
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 子图1: 性能 vs 关键点数量
        colors = ['red', 'orange', 'blue', 'green']
        ax1.plot(keypoint_counts, avg_errors, 'o-', linewidth=3, markersize=10, color='blue')
        ax1.axhline(y=10, color='orange', linestyle='--', linewidth=2, label='Medical Grade (10mm)')
        ax1.axhline(y=5, color='red', linestyle='--', linewidth=2, label='Excellent Grade (5mm)')
        ax1.set_xlabel('Number of Keypoints', fontsize=12)
        ax1.set_ylabel('Average Error (mm)', fontsize=12)
        ax1.set_title('Performance vs Number of Keypoints\n(Real Medical Data)', fontsize=14, fontweight='bold')
        ax1.legend(fontsize=10)
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签和配置描述
        for i, (x, y, desc) in enumerate(zip(keypoint_counts, avg_errors, descriptions)):
            ax1.annotate(f'{y:.1f}mm', (x, y), textcoords="offset points", 
                        xytext=(0,15), ha='center', fontweight='bold')
            ax1.annotate(desc, (x, y), textcoords="offset points", 
                        xytext=(0,-25), ha='center', fontsize=8, style='italic')
        
        # 子图2: 医疗级准确率
        bars = ax2.bar(range(len(keypoint_counts)), acc_10mm, 
                      color=['green' if acc >= 90 else 'orange' if acc >= 70 else 'red' for acc in acc_10mm])
        ax2.axhline(y=90, color='green', linestyle='--', linewidth=2, label='90% Target')
        ax2.set_xlabel('Configuration', fontsize=12)
        ax2.set_ylabel('10mm Accuracy (%)', fontsize=12)
        ax2.set_title('Medical Grade Accuracy by Configuration', fontsize=14, fontweight='bold')
        ax2.set_xticks(range(len(keypoint_counts)))
        ax2.set_xticklabels([f'{kp}kp' for kp in keypoint_counts])
        ax2.legend(fontsize=10)
        ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (bar, acc) in enumerate(zip(bars, acc_10mm)):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{acc:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # 子图3: 模型复杂度
        ax3.plot(keypoint_counts, [p/1000 for p in parameters], 's-', 
                linewidth=3, markersize=10, color='purple')
        ax3.set_xlabel('Number of Keypoints', fontsize=12)
        ax3.set_ylabel('Parameters (thousands)', fontsize=12)
        ax3.set_title('Model Complexity Scaling', fontsize=14, fontweight='bold')
        ax3.grid(True, alpha=0.3)
        
        # 子图4: 效率分析
        efficiency_scores = [10 / err for err in avg_errors]  # 效率分数 (越高越好)
        ax4.bar(range(len(keypoint_counts)), efficiency_scores,
               color=['darkgreen' if score >= 1.5 else 'gold' if score >= 1.0 else 'coral' for score in efficiency_scores])
        ax4.set_xlabel('Configuration', fontsize=12)
        ax4.set_ylabel('Efficiency Score (10mm/error)', fontsize=12)
        ax4.set_title('Model Efficiency by Configuration', fontsize=14, fontweight='bold')
        ax4.set_xticks(range(len(keypoint_counts)))
        ax4.set_xticklabels([f'{kp}kp' for kp in keypoint_counts])
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('real_data_progressive_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✅ 真实数据渐进式分析图表已保存为 real_data_progressive_analysis.png")
        
        # 创建详细分析报告
        analysis_report = {
            "实验总结": {
                "数据来源": "真实医疗12关键点数据集 (97样本)",
                "实验配置": len(self.experiment_results),
                "关键点范围": f"{min(keypoint_counts)}-{max(keypoint_counts)}",
                "最佳性能": f"{min(avg_errors):.2f}mm",
                "医疗级达标": f"{len([r for r in self.experiment_results if r['medical_grade']])}/{len(self.experiment_results)}"
            },
            
            "关键发现": [
                f"最佳性能在{keypoint_counts[avg_errors.index(min(avg_errors))]}关键点配置下达到{min(avg_errors):.2f}mm",
                f"所有配置均达到医疗级标准" if all(r['medical_grade'] for r in self.experiment_results) else f"{len([r for r in self.experiment_results if r['medical_grade']])}/{len(self.experiment_results)}配置达到医疗级",
                f"参数数量从{min(parameters):,}增长到{max(parameters):,}",
                "关键点数量与性能呈现非线性关系"
            ],
            
            "配置推荐": {
                "最佳性能": f"{keypoint_counts[avg_errors.index(min(avg_errors))]}关键点 ({descriptions[avg_errors.index(min(avg_errors))]})",
                "最佳效率": f"{keypoint_counts[efficiency_scores.index(max(efficiency_scores))]}关键点",
                "实用平衡": "6关键点 (基础配置)" if 6 in keypoint_counts else f"{keypoint_counts[1]}关键点"
            }
        }
        
        print(f"\n📊 实验分析总结:")
        for category, details in analysis_report["实验总结"].items():
            print(f"  {category}: {details}")
        
        print(f"\n💡 关键发现:")
        for finding in analysis_report["关键发现"]:
            print(f"  • {finding}")
        
        return analysis_report

def main():
    """主函数"""
    print("🎯 基于真实数据的渐进式关键点实验")
    print("Real Data Progressive Keypoint Experiment")
    print("=" * 60)
    
    # 创建实验器
    experimenter = RealDataProgressiveExperiment()
    
    # 运行实验
    results = experimenter.run_real_data_progressive_experiment()
    
    if results:
        print(f"\n🎉 真实数据渐进式实验完成:")
        print(f"✅ 完成{len(results)}个配置的实验")
        print(f"🎯 最佳性能: {min([r['avg_error'] for r in results]):.2f}mm")
        print(f"📊 为读者提供了基于真实数据的完整分析")
        print(f"📈 验证了不同关键点数量的实际效果")
    else:
        print("❌ 实验过程中出现问题")

if __name__ == "__main__":
    main()
