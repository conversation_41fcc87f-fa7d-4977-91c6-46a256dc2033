{"comparison_date": "2025-01-02", "training_time_results": {"mean_error": 4.063157558441162, "median_error": 3.506161689758301, "std_error": 2.5459063053131104, "accuracy_5mm": 73.7719298245614, "accuracy_10mm": 97.13450292397661, "accuracy_20mm": 99.76608187134502}, "visualization_results": {"num_samples": 5, "overall_mean_error": 4.865933418273926, "all_errors_mean": 4.865933418273926, "accuracy_5mm": 59.29824561403508, "accuracy_10mm": 95.78947368421052, "sample_details": [{"sample_id": 0, "mean_error": 6.065123081207275, "max_error": 12.153776168823242, "min_error": 1.1169120073318481}, {"sample_id": 1, "mean_error": 6.360089302062988, "max_error": 23.722082138061523, "min_error": 1.3408697843551636}, {"sample_id": 2, "mean_error": 4.425798416137695, "max_error": 9.561456680297852, "min_error": 0.7654050588607788}, {"sample_id": 3, "mean_error": 2.9448506832122803, "max_error": 7.981539249420166, "min_error": 0.19642502069473267}, {"sample_id": 4, "mean_error": 4.533805847167969, "max_error": 9.57236385345459, "min_error": 0.37862032651901245}]}, "comprehensive_validation": {"num_samples": 60, "total_keypoints": 3420, "mean_error": 4.148449420928955, "median_error": 3.663017749786377, "std_error": 2.495649814605713, "accuracy_5mm": 72.22222222222221, "accuracy_10mm": 97.36842105263158, "accuracy_20mm": 99.82456140350877, "sample_results": [{"sample_id": 0, "mean_error": 6.065123081207275, "accuracy_5mm": 33.33333333333333, "accuracy_10mm": 92.98245614035088}, {"sample_id": 1, "mean_error": 6.360089302062988, "accuracy_5mm": 52.63157894736842, "accuracy_10mm": 85.96491228070175}, {"sample_id": 2, "mean_error": 4.425798416137695, "accuracy_5mm": 59.64912280701754, "accuracy_10mm": 100.0}, {"sample_id": 3, "mean_error": 2.9448506832122803, "accuracy_5mm": 91.22807017543859, "accuracy_10mm": 100.0}, {"sample_id": 4, "mean_error": 4.533805847167969, "accuracy_5mm": 59.64912280701754, "accuracy_10mm": 100.0}, {"sample_id": 5, "mean_error": 6.202004909515381, "accuracy_5mm": 36.84210526315789, "accuracy_10mm": 85.96491228070175}, {"sample_id": 6, "mean_error": 3.1605465412139893, "accuracy_5mm": 89.47368421052632, "accuracy_10mm": 100.0}, {"sample_id": 7, "mean_error": 2.430955648422241, "accuracy_5mm": 98.24561403508771, "accuracy_10mm": 100.0}, {"sample_id": 8, "mean_error": 3.766730785369873, "accuracy_5mm": 78.94736842105263, "accuracy_10mm": 100.0}, {"sample_id": 9, "mean_error": 3.5862841606140137, "accuracy_5mm": 85.96491228070175, "accuracy_10mm": 98.24561403508771}, {"sample_id": 10, "mean_error": 3.244292736053467, "accuracy_5mm": 92.98245614035088, "accuracy_10mm": 100.0}, {"sample_id": 11, "mean_error": 4.705039024353027, "accuracy_5mm": 50.877192982456144, "accuracy_10mm": 100.0}, {"sample_id": 12, "mean_error": 3.5923256874084473, "accuracy_5mm": 77.19298245614034, "accuracy_10mm": 100.0}, {"sample_id": 13, "mean_error": 4.3589277267456055, "accuracy_5mm": 63.1578947368421, "accuracy_10mm": 100.0}, {"sample_id": 14, "mean_error": 5.129400253295898, "accuracy_5mm": 50.877192982456144, "accuracy_10mm": 98.24561403508771}, {"sample_id": 15, "mean_error": 3.078266143798828, "accuracy_5mm": 92.98245614035088, "accuracy_10mm": 100.0}, {"sample_id": 16, "mean_error": 5.313371658325195, "accuracy_5mm": 50.877192982456144, "accuracy_10mm": 100.0}, {"sample_id": 17, "mean_error": 2.956638813018799, "accuracy_5mm": 85.96491228070175, "accuracy_10mm": 100.0}, {"sample_id": 18, "mean_error": 5.320944309234619, "accuracy_5mm": 49.122807017543856, "accuracy_10mm": 92.98245614035088}, {"sample_id": 19, "mean_error": 5.267408847808838, "accuracy_5mm": 49.122807017543856, "accuracy_10mm": 94.73684210526315}, {"sample_id": 20, "mean_error": 4.370922088623047, "accuracy_5mm": 66.66666666666666, "accuracy_10mm": 98.24561403508771}, {"sample_id": 21, "mean_error": 5.336991786956787, "accuracy_5mm": 52.63157894736842, "accuracy_10mm": 98.24561403508771}, {"sample_id": 22, "mean_error": 3.1687119007110596, "accuracy_5mm": 94.73684210526315, "accuracy_10mm": 100.0}, {"sample_id": 23, "mean_error": 2.4407222270965576, "accuracy_5mm": 98.24561403508771, "accuracy_10mm": 100.0}, {"sample_id": 24, "mean_error": 3.1689977645874023, "accuracy_5mm": 85.96491228070175, "accuracy_10mm": 100.0}, {"sample_id": 25, "mean_error": 5.7848381996154785, "accuracy_5mm": 71.9298245614035, "accuracy_10mm": 87.71929824561403}, {"sample_id": 26, "mean_error": 3.4785687923431396, "accuracy_5mm": 77.19298245614034, "accuracy_10mm": 100.0}, {"sample_id": 27, "mean_error": 3.564880609512329, "accuracy_5mm": 87.71929824561403, "accuracy_10mm": 100.0}, {"sample_id": 28, "mean_error": 5.565553665161133, "accuracy_5mm": 43.859649122807014, "accuracy_10mm": 89.47368421052632}, {"sample_id": 29, "mean_error": 6.323896884918213, "accuracy_5mm": 26.31578947368421, "accuracy_10mm": 94.73684210526315}, {"sample_id": 30, "mean_error": 2.876831531524658, "accuracy_5mm": 94.73684210526315, "accuracy_10mm": 100.0}, {"sample_id": 31, "mean_error": 3.7254045009613037, "accuracy_5mm": 94.73684210526315, "accuracy_10mm": 98.24561403508771}, {"sample_id": 32, "mean_error": 3.9542086124420166, "accuracy_5mm": 80.7017543859649, "accuracy_10mm": 98.24561403508771}, {"sample_id": 33, "mean_error": 2.618129014968872, "accuracy_5mm": 96.49122807017544, "accuracy_10mm": 100.0}, {"sample_id": 34, "mean_error": 4.714623928070068, "accuracy_5mm": 56.14035087719298, "accuracy_10mm": 100.0}, {"sample_id": 35, "mean_error": 3.292393684387207, "accuracy_5mm": 82.45614035087719, "accuracy_10mm": 100.0}, {"sample_id": 36, "mean_error": 3.9127213954925537, "accuracy_5mm": 82.45614035087719, "accuracy_10mm": 98.24561403508771}, {"sample_id": 37, "mean_error": 3.03609299659729, "accuracy_5mm": 87.71929824561403, "accuracy_10mm": 100.0}, {"sample_id": 38, "mean_error": 3.410737991333008, "accuracy_5mm": 84.21052631578947, "accuracy_10mm": 94.73684210526315}, {"sample_id": 39, "mean_error": 3.7818803787231445, "accuracy_5mm": 82.45614035087719, "accuracy_10mm": 100.0}, {"sample_id": 40, "mean_error": 3.3608510494232178, "accuracy_5mm": 82.45614035087719, "accuracy_10mm": 100.0}, {"sample_id": 41, "mean_error": 6.330210208892822, "accuracy_5mm": 31.57894736842105, "accuracy_10mm": 91.22807017543859}, {"sample_id": 42, "mean_error": 3.5132570266723633, "accuracy_5mm": 89.47368421052632, "accuracy_10mm": 100.0}, {"sample_id": 43, "mean_error": 5.280263900756836, "accuracy_5mm": 47.368421052631575, "accuracy_10mm": 92.98245614035088}, {"sample_id": 44, "mean_error": 2.871638536453247, "accuracy_5mm": 91.22807017543859, "accuracy_10mm": 100.0}, {"sample_id": 45, "mean_error": 3.8055901527404785, "accuracy_5mm": 82.45614035087719, "accuracy_10mm": 100.0}, {"sample_id": 46, "mean_error": 3.858975410461426, "accuracy_5mm": 75.43859649122807, "accuracy_10mm": 96.49122807017544}, {"sample_id": 47, "mean_error": 5.39165735244751, "accuracy_5mm": 42.10526315789473, "accuracy_10mm": 94.73684210526315}, {"sample_id": 48, "mean_error": 4.5023274421691895, "accuracy_5mm": 63.1578947368421, "accuracy_10mm": 100.0}, {"sample_id": 49, "mean_error": 3.3158881664276123, "accuracy_5mm": 84.21052631578947, "accuracy_10mm": 100.0}, {"sample_id": 50, "mean_error": 4.527687072753906, "accuracy_5mm": 61.40350877192983, "accuracy_10mm": 98.24561403508771}, {"sample_id": 51, "mean_error": 5.0889716148376465, "accuracy_5mm": 61.40350877192983, "accuracy_10mm": 96.49122807017544}, {"sample_id": 52, "mean_error": 3.1728463172912598, "accuracy_5mm": 94.73684210526315, "accuracy_10mm": 100.0}, {"sample_id": 53, "mean_error": 3.8586325645446777, "accuracy_5mm": 82.45614035087719, "accuracy_10mm": 100.0}, {"sample_id": 54, "mean_error": 3.6137218475341797, "accuracy_5mm": 82.45614035087719, "accuracy_10mm": 100.0}, {"sample_id": 55, "mean_error": 3.6419179439544678, "accuracy_5mm": 77.19298245614034, "accuracy_10mm": 100.0}, {"sample_id": 56, "mean_error": 7.658858776092529, "accuracy_5mm": 42.10526315789473, "accuracy_10mm": 66.66666666666666}, {"sample_id": 57, "mean_error": 2.3174266815185547, "accuracy_5mm": 96.49122807017544, "accuracy_10mm": 100.0}, {"sample_id": 58, "mean_error": 4.342756748199463, "accuracy_5mm": 63.1578947368421, "accuracy_10mm": 98.24561403508771}, {"sample_id": 59, "mean_error": 3.483574151992798, "accuracy_5mm": 85.96491228070175, "accuracy_10mm": 100.0}]}, "analysis": {"accuracy_difference_explanation": ["训练时验证集可能与实际测试集有差异", "小样本统计导致的波动", "模型在训练数据分布上的过拟合", "不同的数据预处理或加载方式"], "recommendation": "以全面验证的结果为准，这更能反映模型的真实性能"}}