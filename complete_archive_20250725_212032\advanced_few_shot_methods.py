#!/usr/bin/env python3
"""
高级小样本学习方法
Advanced Few-Shot Learning Methods
基于Point Transformer 7.129mm验证误差的进一步优化
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pathlib import Path
from datetime import datetime
import json

class DataAugmentationPointNet(nn.Module):
    """数据增强专门PointNet"""
    
    def __init__(self, num_keypoints=19):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 鲁棒特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(), nn.Dropout(0.05),
            nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(), nn.Dropout(0.05),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.<PERSON><PERSON><PERSON>(), nn.Dropout(0.05),
            nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.<PERSON>L<PERSON>(), nn.Dropout(0.05),
            nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 关键点回归器
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(1024, 768),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(768, 512),
            nn.ReLU(),
            nn.Dropout(0.05),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, num_keypoints * 3)
        )
        
    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        # 特征提取
        features = self.feature_extractor(x)  # (B, 1024, N)
        global_feat = torch.max(features, dim=2)[0]  # (B, 1024)
        
        # 关键点预测
        keypoints = self.keypoint_regressor(global_feat)
        return keypoints.view(B, self.num_keypoints, 3)

class ConsistencyRegularizedPointNet(nn.Module):
    """一致性正则化PointNet"""
    
    def __init__(self, num_keypoints=19):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 主网络
        self.main_net = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
            nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
            nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
            nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 辅助网络 (用于一致性)
        self.aux_net = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
            nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
            nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
            nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 共享回归器
        self.regressor = nn.Sequential(
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, num_keypoints * 3)
        )
        
    def forward(self, point_cloud, return_aux=False):
        B, N, _ = point_cloud.shape
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        # 主网络
        main_features = self.main_net(x)  # (B, 1024, N)
        main_global = torch.max(main_features, dim=2)[0]  # (B, 1024)
        main_pred = self.regressor(main_global)
        main_pred = main_pred.view(B, self.num_keypoints, 3)
        
        if return_aux:
            # 辅助网络
            aux_features = self.aux_net(x)  # (B, 1024, N)
            aux_global = torch.max(aux_features, dim=2)[0]  # (B, 1024)
            aux_pred = self.regressor(aux_global)
            aux_pred = aux_pred.view(B, self.num_keypoints, 3)
            return main_pred, aux_pred
        
        return main_pred

class MixupPointNet(nn.Module):
    """Mixup增强PointNet"""
    
    def __init__(self, num_keypoints=19):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
            nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
            nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
            nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 关键点回归器
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, num_keypoints * 3)
        )
        
    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        # 特征提取
        features = self.feature_extractor(x)  # (B, 1024, N)
        global_feat = torch.max(features, dim=2)[0]  # (B, 1024)
        
        # 关键点预测
        keypoints = self.keypoint_regressor(global_feat)
        return keypoints.view(B, self.num_keypoints, 3)

class AdvancedFewShotTrainer:
    """高级小样本学习训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def load_aligned_data(self):
        """加载对齐数据"""
        print("📦 加载F3对齐数据...")
        
        aligned_files = list(Path("data/processed").glob("f3_aligned_dataset_*.npz"))
        if not aligned_files:
            raise FileNotFoundError("未找到F3对齐数据集")
        
        latest_file = max(aligned_files, key=lambda x: x.stat().st_mtime)
        data = np.load(str(latest_file), allow_pickle=True)
        
        point_clouds = np.array(data['point_clouds'], dtype=np.float32)
        keypoints = np.array(data['keypoints'], dtype=np.float32)
        
        # 数据划分
        from sklearn.model_selection import train_test_split
        indices = np.arange(len(point_clouds))
        train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
        train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
        
        print(f"✅ 数据加载完成: {point_clouds.shape}")
        print(f"   训练: {len(train_indices)}, 验证: {len(val_indices)}, 测试: {len(test_indices)}")
        
        return self.data
    
    def extreme_augmentation(self, point_clouds, keypoints, num_augmentations=20):
        """极端数据增强"""
        aug_pcs = []
        aug_kps = []
        
        for pc, kp in zip(point_clouds, keypoints):
            # 原始数据
            aug_pcs.append(pc)
            aug_kps.append(kp)
            
            # 多种增强
            for _ in range(num_augmentations):
                aug_pc = pc.copy()
                aug_kp = kp.copy()
                
                # 1. 旋转增强 (±1度)
                if np.random.random() < 0.8:
                    angle = np.random.uniform(-0.017, 0.017)  # ±1度
                    axis = np.random.choice(['x', 'y', 'z'])
                    
                    cos_a, sin_a = np.cos(angle), np.sin(angle)
                    if axis == 'x':
                        rotation = np.array([[1, 0, 0], [0, cos_a, -sin_a], [0, sin_a, cos_a]], dtype=np.float32)
                    elif axis == 'y':
                        rotation = np.array([[cos_a, 0, sin_a], [0, 1, 0], [-sin_a, 0, cos_a]], dtype=np.float32)
                    else:  # z轴
                        rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], dtype=np.float32)
                    
                    aug_pc = aug_pc @ rotation.T
                    aug_kp = aug_kp @ rotation.T
                
                # 2. 噪声增强 (0.01-0.1mm)
                if np.random.random() < 0.7:
                    noise_std = np.random.uniform(0.01, 0.1)
                    noise = np.random.normal(0, noise_std, aug_pc.shape).astype(np.float32)
                    aug_pc = aug_pc + noise
                
                # 3. 缩放增强 (±1%)
                if np.random.random() < 0.5:
                    scale = np.random.uniform(0.99, 1.01)
                    aug_pc = aug_pc * scale
                    aug_kp = aug_kp * scale
                
                # 4. 平移增强 (±0.5mm)
                if np.random.random() < 0.3:
                    translation = np.random.uniform(-0.5, 0.5, (1, 3)).astype(np.float32)
                    aug_pc = aug_pc + translation
                    aug_kp = aug_kp + translation
                
                # 5. 点云采样 (随机删除10-20%的点)
                if np.random.random() < 0.4:
                    keep_ratio = np.random.uniform(0.8, 0.9)
                    num_keep = int(len(aug_pc) * keep_ratio)
                    keep_indices = np.random.choice(len(aug_pc), num_keep, replace=False)
                    aug_pc = aug_pc[keep_indices]
                    
                    # 如果点数不够，重复采样
                    if len(aug_pc) < 4096:
                        repeat_indices = np.random.choice(len(aug_pc), 4096 - len(aug_pc), replace=True)
                        aug_pc = np.vstack([aug_pc, aug_pc[repeat_indices]])
                
                aug_pcs.append(aug_pc)
                aug_kps.append(aug_kp)
        
        return aug_pcs, aug_kps
    
    def mixup_data(self, point_clouds, keypoints, alpha=0.2):
        """Mixup数据增强"""
        if len(point_clouds) < 2:
            return point_clouds, keypoints
        
        mixed_pcs = []
        mixed_kps = []
        
        for i in range(len(point_clouds)):
            # 选择另一个样本进行混合
            j = np.random.choice([idx for idx in range(len(point_clouds)) if idx != i])
            
            # 生成混合系数
            lam = np.random.beta(alpha, alpha)
            
            # 混合点云和关键点
            mixed_pc = lam * point_clouds[i] + (1 - lam) * point_clouds[j]
            mixed_kp = lam * keypoints[i] + (1 - lam) * keypoints[j]
            
            mixed_pcs.append(mixed_pc)
            mixed_kps.append(mixed_kp)
        
        return mixed_pcs, mixed_kps
    
    def train_data_augmentation_model(self, epochs=150, lr=0.0005):
        """训练数据增强专门模型"""
        print(f"\n📈 训练数据增强专门模型")
        print(f"   策略: 极端数据增强 + 鲁棒训练")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        model = DataAugmentationPointNet(num_keypoints=19).to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=5e-5)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        
        for epoch in range(epochs):
            model.train()
            epoch_losses = []
            
            # 使用更多训练数据
            k_shot = min(25, len(self.data['train']['point_clouds']))
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                k_shot, 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 极端数据增强
            aug_pcs, aug_kps = self.extreme_augmentation(train_pcs, train_kps, num_augmentations=15)
            
            # 批次训练
            batch_size = 8
            for i in range(0, len(aug_pcs), batch_size):
                batch_pcs = torch.FloatTensor(aug_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(aug_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                pred_kps = model(batch_pcs)
                loss = criterion(pred_kps, batch_kps)
                loss.backward()
                
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_losses.append(loss.item())
                
                del batch_pcs, batch_kps, pred_kps, loss
                torch.cuda.empty_cache()
            
            scheduler.step()
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_model(model, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm, LR={optimizer.param_groups[0]['lr']:.6f}")
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def train_consistency_model(self, epochs=120, lr=0.0005):
        """训练一致性正则化模型"""
        print(f"\n🔄 训练一致性正则化模型")
        print(f"   策略: 双网络一致性 + 正则化")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        model = ConsistencyRegularizedPointNet(num_keypoints=19).to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=15, factor=0.7)
        
        mse_criterion = nn.MSELoss()
        consistency_criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        
        for epoch in range(epochs):
            model.train()
            epoch_losses = []
            
            k_shot = min(20, len(self.data['train']['point_clouds']))
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                k_shot, 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 简单增强
            aug_pcs = []
            aug_kps = []
            for pc, kp in zip(train_pcs, train_kps):
                aug_pcs.append(pc)
                aug_kps.append(kp)
                
                # 轻微旋转
                angle = np.random.uniform(-0.005, 0.005)
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], dtype=np.float32)
                
                aug_pc = pc @ rotation.T
                aug_kp = kp @ rotation.T
                aug_pcs.append(aug_pc)
                aug_kps.append(aug_kp)
            
            # 批次训练
            batch_size = 6
            for i in range(0, len(aug_pcs), batch_size):
                batch_pcs = torch.FloatTensor(aug_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(aug_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                
                # 主预测和辅助预测
                main_pred, aux_pred = model(batch_pcs, return_aux=True)
                
                # 主损失
                main_loss = mse_criterion(main_pred, batch_kps)
                
                # 一致性损失
                consistency_loss = consistency_criterion(main_pred, aux_pred)
                
                # 总损失
                total_loss = main_loss + 0.1 * consistency_loss
                
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_losses.append(total_loss.item())
                
                del batch_pcs, batch_kps, main_pred, aux_pred
                torch.cuda.empty_cache()
            
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_model(model, 'val')
                scheduler.step(val_error)
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm")
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def train_mixup_model(self, epochs=100, lr=0.0005):
        """训练Mixup模型"""
        print(f"\n🎭 训练Mixup模型")
        print(f"   策略: Mixup增强 + 稳定训练")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        model = MixupPointNet(num_keypoints=19).to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        
        for epoch in range(epochs):
            model.train()
            epoch_losses = []
            
            k_shot = min(18, len(self.data['train']['point_clouds']))
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                k_shot, 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # Mixup增强
            mixed_pcs, mixed_kps = self.mixup_data(train_pcs, train_kps, alpha=0.2)
            
            # 批次训练
            batch_size = 6
            for i in range(0, len(mixed_pcs), batch_size):
                batch_pcs = torch.FloatTensor(mixed_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(mixed_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                pred_kps = model(batch_pcs)
                loss = criterion(pred_kps, batch_kps)
                loss.backward()
                
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_losses.append(loss.item())
                
                del batch_pcs, batch_kps, pred_kps, loss
                torch.cuda.empty_cache()
            
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_model(model, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm")
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def evaluate_model(self, model, split='test'):
        """评估模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for i in range(0, len(pcs), 2):
                batch_pcs = torch.FloatTensor(pcs[i:i+2]).to(self.device)
                batch_kps = torch.FloatTensor(kps[i:i+2]).to(self.device)
                
                if hasattr(model, 'forward') and 'return_aux' in model.forward.__code__.co_varnames:
                    pred_kps = model(batch_pcs, return_aux=False)
                else:
                    pred_kps = model(batch_pcs)
                
                for j in range(len(batch_pcs)):
                    error = torch.mean(torch.norm(pred_kps[j] - batch_kps[j], dim=1))
                    total_error += error.item()
                    num_samples += 1
                
                del batch_pcs, batch_kps, pred_kps
                torch.cuda.empty_cache()
        
        return total_error / num_samples if num_samples > 0 else float('inf')

def run_advanced_few_shot_experiments():
    """运行高级小样本学习实验"""
    print("🚀 高级小样本学习方法实验")
    print("=" * 60)
    print("目标: 突破Point Transformer的7.129mm验证误差")
    print("方法:")
    print("1. 极端数据增强专门模型")
    print("2. 一致性正则化模型")
    print("3. Mixup增强模型")
    
    trainer = AdvancedFewShotTrainer()
    data = trainer.load_aligned_data()
    
    results = {}
    
    # 1. 数据增强专门模型
    print(f"\n{'='*60}")
    try:
        aug_model, aug_val_error = trainer.train_data_augmentation_model(epochs=120, lr=0.0005)
        aug_test_error = trainer.evaluate_model(aug_model, 'test')
        results['data_augmentation'] = {
            'val_error': aug_val_error,
            'test_error': aug_test_error
        }
        print(f"✅ 数据增强模型完成: 验证={aug_val_error:.3f}mm, 测试={aug_test_error:.3f}mm")
    except Exception as e:
        print(f"❌ 数据增强模型失败: {e}")
        results['data_augmentation'] = {'val_error': float('inf'), 'test_error': float('inf')}
    
    # 2. 一致性正则化模型
    print(f"\n{'='*60}")
    try:
        cons_model, cons_val_error = trainer.train_consistency_model(epochs=100, lr=0.0005)
        cons_test_error = trainer.evaluate_model(cons_model, 'test')
        results['consistency'] = {
            'val_error': cons_val_error,
            'test_error': cons_test_error
        }
        print(f"✅ 一致性模型完成: 验证={cons_val_error:.3f}mm, 测试={cons_test_error:.3f}mm")
    except Exception as e:
        print(f"❌ 一致性模型失败: {e}")
        results['consistency'] = {'val_error': float('inf'), 'test_error': float('inf')}
    
    # 3. Mixup模型
    print(f"\n{'='*60}")
    try:
        mixup_model, mixup_val_error = trainer.train_mixup_model(epochs=80, lr=0.0005)
        mixup_test_error = trainer.evaluate_model(mixup_model, 'test')
        results['mixup'] = {
            'val_error': mixup_val_error,
            'test_error': mixup_test_error
        }
        print(f"✅ Mixup模型完成: 验证={mixup_val_error:.3f}mm, 测试={mixup_test_error:.3f}mm")
    except Exception as e:
        print(f"❌ Mixup模型失败: {e}")
        results['mixup'] = {'val_error': float('inf'), 'test_error': float('inf')}
    
    # 结果汇总
    print(f"\n🏆 高级小样本学习方法对比:")
    print("=" * 60)
    
    best_method = None
    best_val_error = float('inf')
    best_test_error = float('inf')
    
    for method, result in results.items():
        val_error = result['val_error']
        test_error = result['test_error']
        
        print(f"{method:20s}: 验证={val_error:.3f}mm, 测试={test_error:.3f}mm")
        
        if val_error < best_val_error:
            best_val_error = val_error
            best_test_error = test_error
            best_method = method
    
    print(f"\n🏆 最佳方法: {best_method}")
    print(f"   最佳验证误差: {best_val_error:.3f}mm")
    print(f"   对应测试误差: {best_test_error:.3f}mm")
    
    # 与历史最佳对比
    point_transformer_val = 7.129
    point_transformer_test = 8.127
    
    print(f"\n📈 与Point Transformer对比:")
    print(f"Point Transformer:        验证={point_transformer_val:.3f}mm, 测试={point_transformer_test:.3f}mm")
    print(f"最佳高级方法:            验证={best_val_error:.3f}mm, 测试={best_test_error:.3f}mm")
    
    if best_val_error < point_transformer_val:
        val_improvement = (point_transformer_val - best_val_error) / point_transformer_val * 100
        print(f"🎉 验证误差改进: {val_improvement:.1f}%")
    
    if best_test_error < point_transformer_test:
        test_improvement = (point_transformer_test - best_test_error) / point_transformer_test * 100
        print(f"🎉 测试误差改进: {test_improvement:.1f}%")
    
    # 医疗级评估
    medical_target = 5.0
    print(f"\n🎯 医疗级精度评估:")
    print(f"医疗级目标:               {medical_target:.1f}mm")
    print(f"当前最佳验证误差:         {best_val_error:.3f}mm")
    print(f"当前最佳测试误差:         {best_test_error:.3f}mm")
    
    if best_val_error <= medical_target:
        print("🎉 验证误差达到医疗级精度！")
    elif best_val_error <= 6.0:
        remaining = best_val_error - medical_target
        print(f"🔥 验证误差非常接近医疗级！还需{remaining:.3f}mm")
    else:
        remaining = best_val_error - medical_target
        print(f"📈 验证误差距离医疗级还需{remaining:.3f}mm")
    
    # 保存结果
    experiment_results = {
        "experiment_timestamp": datetime.now().isoformat(),
        "experiment_type": "advanced_few_shot_methods",
        "target": "beat_point_transformer_7.129mm",
        "results": results,
        "best_method": best_method,
        "best_val_error": float(best_val_error),
        "best_test_error": float(best_test_error),
        "baselines": {
            "point_transformer_val": point_transformer_val,
            "point_transformer_test": point_transformer_test
        },
        "medical_target": medical_target,
        "medical_achieved": best_val_error <= medical_target
    }
    
    results_dir = Path("results/advanced_few_shot")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = results_dir / f"advanced_few_shot_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(experiment_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 实验结果已保存: {results_file}")
    
    return trainer, results

if __name__ == "__main__":
    trainer, results = run_advanced_few_shot_experiments()
