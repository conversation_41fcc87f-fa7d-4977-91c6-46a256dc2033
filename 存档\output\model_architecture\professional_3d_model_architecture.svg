<?xml version='1.0' encoding='utf-8'?>
<svg width="1800" height="1400" viewBox="0 0 1800 1400" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <linearGradient id="inputGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="modelGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcc02;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="processGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4caf50;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="outputGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffebee;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f44336;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="improvementGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2e7d32;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="3" flood-color="rgba(0,0,0,0.3)" />
    </filter>
    <style>
        .title { font-family: 'Arial', sans-serif; font-size: 32px; font-weight: bold; fill: #1a237e; }
        .subtitle { font-family: 'Arial', sans-serif; font-size: 18px; font-weight: bold; fill: #3949ab; }
        .section-title { font-family: 'Arial', sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
        .label { font-family: 'Arial', sans-serif; font-size: 14px; fill: #34495e; }
        .small-label { font-family: 'Arial', sans-serif; font-size: 12px; fill: #7f8c8d; }
        .metric-label { font-family: 'Arial', sans-serif; font-size: 13px; font-weight: bold; fill: #27ae60; }
        .error-label { font-family: 'Arial', sans-serif; font-size: 13px; font-weight: bold; fill: #e74c3c; }
        .improvement-label { font-family: 'Arial', sans-serif; font-size: 16px; font-weight: bold; fill: #2e7d32; }

        .input-box { fill: url(#inputGradient); stroke: #2196f3; stroke-width: 2; filter: url(#shadow); }
        .model-box { fill: url(#modelGradient); stroke: #ff9800; stroke-width: 2; filter: url(#shadow); }
        .process-box { fill: url(#processGradient); stroke: #4caf50; stroke-width: 2; filter: url(#shadow); }
        .output-box { fill: url(#outputGradient); stroke: #f44336; stroke-width: 2; filter: url(#shadow); }
        .improvement-box { fill: url(#improvementGradient); stroke: #2e7d32; stroke-width: 3; filter: url(#shadow); }

        .flow-arrow { stroke: #3f51b5; stroke-width: 4; fill: none; marker-end: url(#arrowhead); }
        .process-arrow { stroke: #4caf50; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
        .improvement-arrow { stroke: #2e7d32; stroke-width: 5; fill: none; marker-end: url(#bigArrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#3f51b5" />
    </marker>
    <marker id="bigArrowhead" markerWidth="16" markerHeight="10" refX="15" refY="5" orient="auto">
      <polygon points="0 0, 16 5, 0 10" fill="#2e7d32" />
    </marker>
  </defs>
  <rect width="1800" height="1400" fill="linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)" />
  <g>
    <rect x="50" y="20" width="1700" height="100" fill="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" rx="20" filter="url(#shadow)" />
    <text x="900" y="55" text-anchor="middle" class="title" fill="white">🎯 增强3D医疗关键点检测模型架构</text>
    <text x="900" y="85" text-anchor="middle" class="subtitle" fill="rgba(255,255,255,0.9)">Enhanced 3D Medical Keypoint Detection with Intelligent Post-Processing</text>
  </g>
  <g>
    <text x="150" y="170" text-anchor="middle" class="section-title">📥 输入数据</text>
    <rect x="50" y="190" width="200" height="120" class="input-box" rx="15" />
    <text x="150" y="220" text-anchor="middle" class="subtitle">🦴 骨盆点云</text>
    <text x="150" y="245" text-anchor="middle" class="label">Pelvis Point Cloud</text>
    <text x="150" y="265" text-anchor="middle" class="small-label">Shape: [N, 3]</text>
    <text x="150" y="285" text-anchor="middle" class="small-label">~10K-50K points</text>
    <rect x="50" y="330" width="200" height="120" class="input-box" rx="15" />
    <text x="150" y="360" text-anchor="middle" class="subtitle">🎯 解剖标注</text>
    <text x="150" y="385" text-anchor="middle" class="label">Anatomical Keypoints</text>
    <text x="150" y="405" text-anchor="middle" class="small-label">Shape: [57, 3]</text>
    <text x="150" y="425" text-anchor="middle" class="small-label">F1:19 + F2:19 + F3:19</text>
  </g>
  <g>
    <text x="450" y="170" text-anchor="middle" class="section-title">🤖 深度学习模型</text>
    <rect x="300" y="190" width="300" height="260" class="model-box" rx="20" />
    <text x="450" y="220" text-anchor="middle" class="subtitle">ImprovedMedicalPointNet</text>
    <text x="320" y="250" class="label">🔗 Point Embedding</text>
    <text x="320" y="265" class="small-label">点嵌入层</text>
    <text x="320" y="280" class="label">🧠 Feature Extraction</text>
    <text x="320" y="295" class="small-label">特征提取</text>
    <text x="320" y="310" class="label">🌐 Global Features</text>
    <text x="320" y="325" class="small-label">全局特征</text>
    <text x="320" y="340" class="label">🎯 Keypoint Head</text>
    <text x="320" y="355" class="small-label">关键点预测头</text>
    <text x="320" y="370" class="label">📏 Range Head</text>
    <text x="320" y="385" class="small-label">坐标范围头</text>
    <text x="320" y="400" class="label">📍 Coordinate Prediction</text>
    <text x="320" y="415" class="small-label">坐标预测</text>
    <rect x="310" y="420" width="280" height="25" fill="#ffecb3" stroke="#ffa000" rx="5" />
    <text x="450" y="437" text-anchor="middle" class="error-label">原始输出: 2.51±1.48mm, 89.6% 5mm准确率</text>
  </g>
  <g>
    <text x="1050" y="170" text-anchor="middle" class="section-title">⚡ 增强3D后处理</text>
    <rect x="700" y="190" width="700" height="500" class="process-box" rx="25" stroke-width="3" />
    <rect x="720" y="230" width="660" height="70" class="process-box" rx="12" fill="#f1f8e9" stroke="#689f38" />
    <circle cx="750" cy="265" r="20" fill="#4caf50" stroke="white" stroke-width="3" />
    <text x="750" y="270" text-anchor="middle" class="label" fill="white" font-weight="bold">1</text>
    <text x="780" y="255" class="subtitle">3D质心对齐</text>
    <text x="780" y="275" class="label">3D Centroid Alignment</text>
    <text x="780" y="290" class="small-label">解决整体偏移</text>
    <line x1="1050" y1="300" x2="1050" y2="320" class="process-arrow" />
    <rect x="720" y="320" width="660" height="70" class="process-box" rx="12" fill="#f1f8e9" stroke="#689f38" />
    <circle cx="750" cy="355" r="20" fill="#4caf50" stroke="white" stroke-width="3" />
    <text x="750" y="360" text-anchor="middle" class="label" fill="white" font-weight="bold">2</text>
    <text x="780" y="345" class="subtitle">3D尺度校正</text>
    <text x="780" y="365" class="label">3D Scale Correction</text>
    <text x="780" y="380" class="small-label">解决聚拢问题</text>
    <line x1="1050" y1="390" x2="1050" y2="410" class="process-arrow" />
    <rect x="720" y="410" width="660" height="70" class="process-box" rx="12" fill="#f1f8e9" stroke="#689f38" />
    <circle cx="750" cy="445" r="20" fill="#4caf50" stroke="white" stroke-width="3" />
    <text x="750" y="450" text-anchor="middle" class="label" fill="white" font-weight="bold">3</text>
    <text x="780" y="435" class="subtitle">解剖区域校正</text>
    <text x="780" y="455" class="label">Anatomical Region Correction</text>
    <text x="780" y="470" class="small-label">F1/F2/F3分区优化</text>
    <line x1="1050" y1="480" x2="1050" y2="500" class="process-arrow" />
    <rect x="720" y="500" width="660" height="70" class="process-box" rx="12" fill="#f1f8e9" stroke="#689f38" />
    <circle cx="750" cy="535" r="20" fill="#4caf50" stroke="white" stroke-width="3" />
    <text x="750" y="540" text-anchor="middle" class="label" fill="white" font-weight="bold">4</text>
    <text x="780" y="525" class="subtitle">3D方向性校正</text>
    <text x="780" y="545" class="label">3D Directional Correction</text>
    <text x="780" y="560" class="small-label">各轴系统性偏移</text>
    <line x1="1050" y1="570" x2="1050" y2="590" class="process-arrow" />
    <rect x="720" y="590" width="660" height="70" class="process-box" rx="12" fill="#f1f8e9" stroke="#689f38" />
    <circle cx="750" cy="625" r="20" fill="#4caf50" stroke="white" stroke-width="3" />
    <text x="750" y="630" text-anchor="middle" class="label" fill="white" font-weight="bold">5</text>
    <text x="780" y="615" class="subtitle">距离校正</text>
    <text x="780" y="635" class="label">Distance-based Correction</text>
    <text x="780" y="650" class="small-label">分层精细调整</text>
  </g>
  <g>
    <rect x="1450" y="190" width="280" height="200" class="improvement-box" rx="20" />
    <text x="1590" y="220" text-anchor="middle" class="section-title">🎉 最终结果</text>
    <text x="1590" y="250" text-anchor="middle" class="improvement-label">1.17±0.20mm</text>
    <text x="1590" y="275" text-anchor="middle" class="metric-label">99.3% 5mm准确率</text>
    <text x="1590" y="300" text-anchor="middle" class="improvement-label">改善 43.8%</text>
    <text x="1590" y="325" text-anchor="middle" class="label">医疗应用就绪</text>
    <text x="1590" y="345" text-anchor="middle" class="small-label">&lt; 1.5mm 标准</text>
  </g>
  <g>
    <line x1="250" y1="250" x2="290" y2="250" class="flow-arrow" />
    <line x1="600" y1="320" x2="690" y2="320" class="flow-arrow" />
    <line x1="1400" y1="290" x2="1440" y2="290" class="improvement-arrow" />
  </g>
</svg>
