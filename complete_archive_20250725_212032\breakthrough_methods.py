#!/usr/bin/env python3
"""
突破性改进方法
Breakthrough Methods for Medical Keypoint Detection
实现真正有潜力的突破性技术
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
import json

class EnhancedPointTransformer(nn.Module):
    """增强Point Transformer - 修复版突破性架构"""

    def __init__(self, num_keypoints=19):
        super().__init__()
        self.num_keypoints = num_keypoints

        # 输入特征提取
        self.input_transform = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU()
        )

        # 多层特征提取
        self.feature_layers = nn.ModuleList([
            nn.Sequential(
                nn.Conv1d(128, 256, 1),
                nn.BatchNorm1d(256),
                nn.<PERSON>LU()
            ),
            nn.Sequential(
                nn.Conv1d(256, 512, 1),
                nn.BatchNorm1d(512),
                nn.ReLU()
            ),
            nn.Sequential(
                nn.Conv1d(512, 1024, 1),
                nn.BatchNorm1d(1024),
                nn.ReLU()
            )
        ])

        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=1024, num_heads=16, dropout=0.1, batch_first=True
        )

        # 全局特征聚合
        self.global_mlp = nn.Sequential(
            nn.Linear(1024, 2048),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(2048, 1024),
            nn.ReLU(),
            nn.Dropout(0.2)
        )

        # 关键点回归器
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, num_keypoints * 3)
        )

    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape

        # 输入变换
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        x = self.input_transform(x)  # (B, 128, N)

        # 多层特征提取
        for layer in self.feature_layers:
            x = layer(x)  # 最终 (B, 1024, N)

        # 转换为注意力格式
        x = x.transpose(1, 2)  # (B, N, 1024)

        # 自注意力
        attended_x, _ = self.attention(x, x, x)  # (B, N, 1024)

        # 残差连接
        x = x + attended_x

        # 全局最大池化
        global_feat = torch.max(x, dim=1)[0]  # (B, 1024)

        # 全局特征处理
        global_feat = self.global_mlp(global_feat)  # (B, 1024)

        # 关键点预测
        keypoints = self.keypoint_regressor(global_feat)
        return keypoints.view(B, self.num_keypoints, 3)

class GeometryAwarePointNet(nn.Module):
    """几何感知PointNet - 突破性架构2"""
    
    def __init__(self, num_keypoints=19):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 几何特征提取器
        self.geometry_extractor = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU()
        )
        
        # 法向量估计器
        self.normal_estimator = nn.Sequential(
            nn.Conv1d(128, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 3, 1)
        )
        
        # 曲率估计器
        self.curvature_estimator = nn.Sequential(
            nn.Conv1d(128, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 1, 1)
        )
        
        # 几何增强特征
        self.enhanced_features = nn.Sequential(
            nn.Conv1d(132, 256, 1),  # 128 + 3 + 1
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Conv1d(256, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Conv1d(512, 1024, 1),
            nn.BatchNorm1d(1024),
            nn.ReLU()
        )
        
        # 关键点回归器
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, num_keypoints * 3)
        )
        
    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape
        
        # 转换为卷积格式
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        # 几何特征提取
        geo_feat = self.geometry_extractor(x)  # (B, 128, N)
        
        # 法向量估计
        normals = self.normal_estimator(geo_feat)  # (B, 3, N)
        normals = F.normalize(normals, dim=1)
        
        # 曲率估计
        curvature = self.curvature_estimator(geo_feat)  # (B, 1, N)
        
        # 几何增强特征
        enhanced_input = torch.cat([geo_feat, normals, curvature], dim=1)  # (B, 132, N)
        enhanced_feat = self.enhanced_features(enhanced_input)  # (B, 1024, N)
        
        # 全局最大池化
        global_feat = torch.max(enhanced_feat, dim=2)[0]  # (B, 1024)
        
        # 关键点预测
        keypoints = self.keypoint_regressor(global_feat)
        return keypoints.view(B, self.num_keypoints, 3)

class HierarchicalPointNet(nn.Module):
    """分层PointNet - 突破性架构3"""
    
    def __init__(self, num_keypoints=19):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 第一层: 局部特征
        self.local_conv = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU()
        )
        
        # 第二层: 区域特征
        self.regional_conv = nn.Sequential(
            nn.Conv1d(128, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Conv1d(256, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU()
        )
        
        # 第三层: 全局特征
        self.global_conv = nn.Sequential(
            nn.Conv1d(512, 1024, 1),
            nn.BatchNorm1d(1024),
            nn.ReLU(),
            nn.Conv1d(1024, 2048, 1),
            nn.BatchNorm1d(2048),
            nn.ReLU()
        )
        
        # 分层池化
        self.local_pool = nn.AdaptiveMaxPool1d(1024)
        self.regional_pool = nn.AdaptiveMaxPool1d(512)
        self.global_pool = nn.AdaptiveMaxPool1d(256)
        
        # 特征融合
        self.feature_fusion = nn.Sequential(
            nn.Linear(1792, 1024),  # 1024 + 512 + 256
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 关键点回归器
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, num_keypoints * 3)
        )
        
    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape
        
        # 转换格式
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        # 分层特征提取
        local_feat = self.local_conv(x)  # (B, 128, N)
        regional_feat = self.regional_conv(local_feat)  # (B, 512, N)
        global_feat = self.global_conv(regional_feat)  # (B, 2048, N)
        
        # 分层池化
        local_pooled = self.local_pool(local_feat).squeeze(-1)  # (B, 128)
        regional_pooled = self.regional_pool(regional_feat).squeeze(-1)  # (B, 512)
        global_pooled = self.global_pool(global_feat).squeeze(-1)  # (B, 2048)
        
        # 特征融合
        fused_feat = torch.cat([
            local_pooled, 
            regional_pooled, 
            global_pooled
        ], dim=1)  # (B, 1792)
        
        fused_feat = self.feature_fusion(fused_feat)  # (B, 512)
        
        # 关键点预测
        keypoints = self.keypoint_regressor(fused_feat)
        return keypoints.view(B, self.num_keypoints, 3)

class BreakthroughTrainer:
    """突破性方法训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def load_aligned_data(self):
        """加载对齐数据"""
        print("📦 加载F3对齐数据...")
        
        aligned_files = list(Path("data/processed").glob("f3_aligned_dataset_*.npz"))
        if not aligned_files:
            raise FileNotFoundError("未找到F3对齐数据集")
        
        latest_file = max(aligned_files, key=lambda x: x.stat().st_mtime)
        data = np.load(str(latest_file), allow_pickle=True)
        
        point_clouds = np.array(data['point_clouds'], dtype=np.float32)
        keypoints = np.array(data['keypoints'], dtype=np.float32)
        
        # 数据划分
        from sklearn.model_selection import train_test_split
        indices = np.arange(len(point_clouds))
        train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
        train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
        
        print(f"✅ 数据加载完成: {point_clouds.shape}")
        print(f"   训练: {len(train_indices)}, 验证: {len(val_indices)}, 测试: {len(test_indices)}")
        
        return self.data
    
    def medical_grade_augmentation(self, point_clouds, keypoints):
        """医疗级数据增强 - 保持几何精度"""
        aug_pcs = []
        aug_kps = []
        
        for pc, kp in zip(point_clouds, keypoints):
            # 原始数据
            aug_pcs.append(pc)
            aug_kps.append(kp)
            
            # 极小旋转 (±0.5度)
            for _ in range(2):
                angle = np.random.uniform(-0.009, 0.009)  # ±0.5度
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], dtype=np.float32)
                
                aug_pc = pc @ rotation.T
                aug_kp = kp @ rotation.T
                aug_pcs.append(aug_pc)
                aug_kps.append(aug_kp)
            
            # 极小噪声 (0.1mm)
            noise_pc = pc + np.random.normal(0, 0.1, pc.shape).astype(np.float32)
            aug_pcs.append(noise_pc)
            aug_kps.append(kp)
            
            # 极小缩放 (±0.5%)
            scale = np.random.uniform(0.995, 1.005)
            scaled_pc = pc * scale
            scaled_kp = kp * scale
            aug_pcs.append(scaled_pc)
            aug_kps.append(scaled_kp)
        
        return aug_pcs, aug_kps
    
    def train_breakthrough_model(self, model_class, model_name, epochs=80, lr=0.0005):
        """训练突破性模型"""
        print(f"\n🚀 训练{model_name}模型")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        # 创建模型
        model = model_class(num_keypoints=19).to(self.device)
        
        # 计算参数
        total_params = sum(p.numel() for p in model.parameters())
        print(f"   模型参数: {total_params:,}")
        
        # 优化器 - 使用更激进的设置
        optimizer = torch.optim.AdamW(
            model.parameters(), 
            lr=lr, 
            weight_decay=5e-5,
            betas=(0.9, 0.999)
        )
        
        # 学习率调度器
        scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer, 
            max_lr=lr*2, 
            epochs=epochs, 
            steps_per_epoch=1,
            pct_start=0.3
        )
        
        criterion = nn.MSELoss()
        
        # 训练状态
        best_val_error = float('inf')
        best_model_state = None
        patience = 0
        max_patience = 30
        
        for epoch in range(epochs):
            # 训练阶段
            model.train()
            epoch_losses = []
            
            # 使用更多训练数据
            k_shot = min(30, len(self.data['train']['point_clouds']))
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                k_shot, 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 医疗级增强
            aug_pcs, aug_kps = self.medical_grade_augmentation(train_pcs, train_kps)
            
            # 分批训练
            batch_size = 4
            for i in range(0, len(aug_pcs), batch_size):
                try:
                    batch_pcs = torch.FloatTensor(aug_pcs[i:i+batch_size]).to(self.device)
                    batch_kps = torch.FloatTensor(aug_kps[i:i+batch_size]).to(self.device)
                    
                    optimizer.zero_grad()
                    pred_kps = model(batch_pcs)
                    loss = criterion(pred_kps, batch_kps)
                    loss.backward()
                    
                    # 梯度裁剪
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    
                    optimizer.step()
                    epoch_losses.append(loss.item())
                    
                    del batch_pcs, batch_kps, pred_kps, loss
                    torch.cuda.empty_cache()
                    
                except Exception as e:
                    print(f"   训练错误: {e}")
                    torch.cuda.empty_cache()
                    continue
            
            scheduler.step()
            
            if epoch_losses:
                avg_loss = np.mean(epoch_losses)
            else:
                avg_loss = 0
            
            # 验证
            if epoch % 5 == 0:
                val_error = self.evaluate_model(model, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                    patience = 0
                else:
                    patience += 1
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm, "
                      f"LR={optimizer.param_groups[0]['lr']:.6f}, P={patience}")
                
                if patience >= max_patience:
                    print(f"早停在epoch {epoch}")
                    break
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)
            print(f"✅ 加载最佳模型 (验证误差: {best_val_error:.3f}mm)")
        
        return model, best_val_error
    
    def evaluate_model(self, model, split='test'):
        """评估模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for i in range(0, len(pcs), 2):
                try:
                    batch_pcs = torch.FloatTensor(pcs[i:i+2]).to(self.device)
                    batch_kps = torch.FloatTensor(kps[i:i+2]).to(self.device)
                    
                    pred_kps = model(batch_pcs)
                    
                    for j in range(len(batch_pcs)):
                        error = torch.mean(torch.norm(pred_kps[j] - batch_kps[j], dim=1))
                        total_error += error.item()
                        num_samples += 1
                    
                    del batch_pcs, batch_kps, pred_kps
                    torch.cuda.empty_cache()
                    
                except Exception as e:
                    print(f"   评估错误: {e}")
                    torch.cuda.empty_cache()
                    continue
        
        return total_error / num_samples if num_samples > 0 else float('inf')

def run_breakthrough_experiments():
    """运行突破性实验"""
    print("🚀 突破性方法实验")
    print("=" * 60)
    print("目标: 突破8.13mm基线，冲击医疗级5mm精度")
    
    trainer = BreakthroughTrainer()
    data = trainer.load_aligned_data()
    
    # 测试三种突破性架构
    architectures = [
        (MultiScalePointTransformer, "多尺度Point Transformer"),
        (GeometryAwarePointNet, "几何感知PointNet"),
        (HierarchicalPointNet, "分层PointNet")
    ]
    
    results = {}
    baseline_error = 8.13  # 基础Point Transformer
    
    for model_class, model_name in architectures:
        print(f"\n{'='*60}")
        
        try:
            model, val_error = trainer.train_breakthrough_model(
                model_class, model_name, epochs=80, lr=0.0005
            )
            
            test_error = trainer.evaluate_model(model, 'test')
            improvement = (baseline_error - test_error) / baseline_error * 100
            
            results[model_name] = {
                'validation_error': val_error,
                'test_error': test_error,
                'improvement_percent': improvement
            }
            
            print(f"\n📊 {model_name}结果:")
            print(f"   验证误差: {val_error:.3f}mm")
            print(f"   测试误差: {test_error:.3f}mm")
            print(f"   改进幅度: {improvement:+.1f}%")
            
            # 检查是否达到医疗级
            if test_error <= 5.0:
                print(f"🎉 {model_name}达到医疗级精度！")
            elif test_error <= 6.0:
                print(f"🎯 {model_name}接近医疗级精度！")
            elif improvement > 20:
                print(f"🚀 {model_name}取得突破性进展！")
            
        except Exception as e:
            print(f"❌ {model_name}训练失败: {e}")
            results[model_name] = {
                'validation_error': float('inf'),
                'test_error': float('inf'),
                'improvement_percent': -100
            }
    
    # 总结结果
    print(f"\n📊 突破性实验总结:")
    print("=" * 60)
    print(f"基线 (基础Point Transformer): {baseline_error:.2f}mm")
    
    best_method = None
    best_error = float('inf')
    
    for method, result in results.items():
        error = result['test_error']
        improvement = result['improvement_percent']
        
        print(f"{method}: {error:.2f}mm ({improvement:+.1f}%)")
        
        if error < best_error:
            best_error = error
            best_method = method
    
    if best_method and best_error < baseline_error:
        print(f"\n🏆 最佳方法: {best_method} ({best_error:.2f}mm)")
        
        if best_error <= 5.0:
            print("🎉 成功达到医疗级精度！")
        elif best_error <= 6.0:
            print("🎯 非常接近医疗级精度！")
        else:
            remaining = best_error - 5.0
            print(f"📈 距离医疗级还需改进{remaining:.2f}mm")
    else:
        print("⚠️ 所有方法都未能突破基线")
    
    # 保存结果
    experiment_results = {
        "experiment_timestamp": datetime.now().isoformat(),
        "experiment_type": "breakthrough_methods",
        "baseline_error": baseline_error,
        "target_error": 5.0,
        "results": results,
        "best_method": best_method,
        "best_error": best_error,
        "medical_grade_achieved": best_error <= 5.0 if best_error != float('inf') else False
    }
    
    results_dir = Path("results/breakthrough_experiments")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = results_dir / f"breakthrough_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(experiment_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 实验结果已保存: {results_file}")
    
    return trainer, experiment_results

if __name__ == "__main__":
    trainer, results = run_breakthrough_experiments()
