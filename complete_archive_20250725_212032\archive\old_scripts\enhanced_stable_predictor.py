#!/usr/bin/env python3
"""
增强稳定预测器
结合多种策略的最终预测系统
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps

class EnhancedStablePredictor:
    """增强稳定预测器"""
    
    def __init__(self, model, device):
        self.model = model
        self.device = device
        self.model.eval()
    
    def robust_predict(self, point_cloud, num_tta=8, confidence_threshold=0.3):
        """鲁棒预测 - 结合多种稳定性策略"""
        
        print(f"🔧 Running robust prediction with {num_tta} augmentations...")
        
        all_predictions = []
        all_confidences = []
        all_heatmaps = []
        
        # 1. 测试时增强 - 多次预测
        for i in range(num_tta):
            # 随机采样
            if len(point_cloud) > 8192:
                indices = np.random.choice(len(point_cloud), 8192, replace=False)
                pc_sampled = point_cloud[indices]
            else:
                pc_sampled = point_cloud.copy()
            
            # 数据增强
            if i > 0:
                pc_sampled = self._augment_point_cloud(pc_sampled, i)
            
            # 预测
            pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                pred_heatmaps = self.model(pc_tensor)
            
            pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T
            pred_keypoints, confidences = extract_keypoints_from_heatmaps(
                pred_heatmaps_np.T, pc_sampled
            )
            
            # 反向变换（如果有增强）
            if i > 0:
                pred_keypoints = self._reverse_augmentation(pred_keypoints, i)
            
            all_predictions.append(pred_keypoints)
            all_confidences.append(confidences)
            all_heatmaps.append(pred_heatmaps_np)
        
        # 2. 置信度过滤和加权
        predictions_array = np.array(all_predictions)  # [num_tta, 12, 3]
        confidences_array = np.array(all_confidences)  # [num_tta, 12]
        
        # 过滤低置信度预测
        valid_mask = confidences_array > confidence_threshold
        
        final_predictions = []
        final_confidences = []
        prediction_stds = []
        
        for kp_idx in range(12):  # 12个关键点
            kp_predictions = predictions_array[:, kp_idx, :]  # [num_tta, 3]
            kp_confidences = confidences_array[:, kp_idx]     # [num_tta]
            kp_valid = valid_mask[:, kp_idx]                  # [num_tta]
            
            if np.sum(kp_valid) > 0:
                # 使用有效预测
                valid_preds = kp_predictions[kp_valid]
                valid_confs = kp_confidences[kp_valid]
                
                # 置信度加权平均
                weights = valid_confs / np.sum(valid_confs)
                weighted_pred = np.sum(valid_preds * weights[:, np.newaxis], axis=0)
                avg_conf = np.mean(valid_confs)
                pred_std = np.std(valid_preds, axis=0)
            else:
                # 如果没有高置信度预测，使用简单平均
                weighted_pred = np.mean(kp_predictions, axis=0)
                avg_conf = np.mean(kp_confidences)
                pred_std = np.std(kp_predictions, axis=0)
            
            final_predictions.append(weighted_pred)
            final_confidences.append(avg_conf)
            prediction_stds.append(np.mean(pred_std))
        
        final_predictions = np.array(final_predictions)
        final_confidences = np.array(final_confidences)
        prediction_stds = np.array(prediction_stds)
        
        # 3. 计算预测质量指标
        consistency_score = 1.0 / (1.0 + np.mean(prediction_stds))
        confidence_score = np.mean(final_confidences)
        quality_score = (consistency_score + confidence_score) / 2
        
        return {
            'predictions': final_predictions,
            'confidences': final_confidences,
            'consistency_score': consistency_score,
            'confidence_score': confidence_score,
            'quality_score': quality_score,
            'prediction_stds': prediction_stds,
            'all_predictions': all_predictions,
            'all_confidences': all_confidences
        }
    
    def _augment_point_cloud(self, pc, aug_idx):
        """数据增强"""
        pc_aug = pc.copy()
        
        # 轻微旋转
        if aug_idx % 4 == 1:
            angle = np.random.uniform(-3, 3) * np.pi / 180
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            pc_aug = pc_aug @ rotation_matrix.T
        
        # 轻微缩放
        elif aug_idx % 4 == 2:
            scale = np.random.uniform(0.98, 1.02)
            pc_aug = pc_aug * scale
        
        # 轻微平移
        elif aug_idx % 4 == 3:
            translation = np.random.uniform(-1, 1, 3)
            pc_aug = pc_aug + translation
        
        # 添加轻微噪声
        if aug_idx > 4:
            noise = np.random.normal(0, 0.1, pc_aug.shape)
            pc_aug = pc_aug + noise
        
        return pc_aug
    
    def _reverse_augmentation(self, predictions, aug_idx):
        """反向变换（简化版，主要处理旋转）"""
        # 这里简化处理，实际应用中需要记录变换参数
        return predictions

def create_stability_comparison(point_cloud, true_keypoints, original_pred, robust_pred):
    """创建稳定性对比可视化"""
    
    fig = plt.figure(figsize=(20, 10))
    
    # 1. 原始预测
    ax1 = fig.add_subplot(1, 2, 1, projection='3d')
    
    # 采样点云
    if len(point_cloud) > 3000:
        indices = np.random.choice(len(point_cloud), 3000, replace=False)
        display_pc = point_cloud[indices]
    else:
        display_pc = point_cloud
    
    ax1.scatter(display_pc[:, 0], display_pc[:, 1], display_pc[:, 2],
               c='lightgray', s=0.5, alpha=0.3)
    
    ax1.scatter(true_keypoints[:, 0], true_keypoints[:, 1], true_keypoints[:, 2],
               c='green', s=100, marker='o', label='True', alpha=0.8)
    
    ax1.scatter(original_pred[:, 0], original_pred[:, 1], original_pred[:, 2],
               c='red', s=100, marker='x', label='Original Pred', alpha=0.8)
    
    # 连接线
    for i in range(len(true_keypoints)):
        ax1.plot([true_keypoints[i, 0], original_pred[i, 0]],
                [true_keypoints[i, 1], original_pred[i, 1]],
                [true_keypoints[i, 2], original_pred[i, 2]],
                'r--', alpha=0.6, linewidth=1)
    
    original_errors = [np.linalg.norm(original_pred[i] - true_keypoints[i]) 
                      for i in range(len(true_keypoints))]
    
    ax1.set_title(f'Original Prediction\nAvg Error: {np.mean(original_errors):.2f}mm')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 鲁棒预测
    ax2 = fig.add_subplot(1, 2, 2, projection='3d')
    
    ax2.scatter(display_pc[:, 0], display_pc[:, 1], display_pc[:, 2],
               c='lightgray', s=0.5, alpha=0.3)
    
    ax2.scatter(true_keypoints[:, 0], true_keypoints[:, 1], true_keypoints[:, 2],
               c='green', s=100, marker='o', label='True', alpha=0.8)
    
    robust_predictions = robust_pred['predictions']
    ax2.scatter(robust_predictions[:, 0], robust_predictions[:, 1], robust_predictions[:, 2],
               c='blue', s=100, marker='^', label='Robust Pred', alpha=0.8)
    
    # 连接线
    for i in range(len(true_keypoints)):
        ax2.plot([true_keypoints[i, 0], robust_predictions[i, 0]],
                [true_keypoints[i, 1], robust_predictions[i, 1]],
                [true_keypoints[i, 2], robust_predictions[i, 2]],
                'b--', alpha=0.6, linewidth=1)
    
    robust_errors = [np.linalg.norm(robust_predictions[i] - true_keypoints[i]) 
                    for i in range(len(true_keypoints))]
    
    ax2.set_title(f'Robust Prediction\nAvg Error: {np.mean(robust_errors):.2f}mm\n'
                 f'Quality Score: {robust_pred["quality_score"]:.3f}')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.suptitle('Stability Comparison: Original vs Robust Prediction', 
                fontsize=16, fontweight='bold')
    plt.tight_layout(rect=[0, 0, 1, 0.93])
    
    return fig, original_errors, robust_errors

def main():
    """主函数"""
    print("🚀 Enhanced Stable Predictor")
    print("Final robust prediction system with multiple stability strategies")
    print("=" * 70)
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # 创建增强稳定预测器
    predictor = EnhancedStablePredictor(model, device)
    
    # 测试多个样本
    total_original_errors = []
    total_robust_errors = []
    
    for i in range(min(5, len(sample_ids))):
        sample_id = sample_ids[i]
        point_cloud = point_clouds[i]
        true_keypoints = keypoints[i]
        
        print(f"\n📊 Testing sample {i+1}/5: {sample_id}")
        
        # 原始预测
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            pc_sampled = point_cloud[indices]
        else:
            pc_sampled = point_cloud
        
        pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
        
        with torch.no_grad():
            pred_heatmaps = model(pc_tensor)
        
        pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T
        original_pred, _ = extract_keypoints_from_heatmaps(
            pred_heatmaps_np.T, pc_sampled
        )
        
        # 鲁棒预测
        robust_result = predictor.robust_predict(point_cloud, num_tta=8)
        
        # 计算误差
        original_errors = [np.linalg.norm(original_pred[j] - true_keypoints[j]) 
                          for j in range(len(true_keypoints))]
        robust_errors = [np.linalg.norm(robust_result['predictions'][j] - true_keypoints[j]) 
                        for j in range(len(true_keypoints))]
        
        total_original_errors.extend(original_errors)
        total_robust_errors.extend(robust_errors)
        
        print(f"   Original: {np.mean(original_errors):.2f}mm")
        print(f"   Robust:   {np.mean(robust_errors):.2f}mm")
        print(f"   Quality:  {robust_result['quality_score']:.3f}")
        print(f"   Improvement: {np.mean(original_errors) - np.mean(robust_errors):.2f}mm")
        
        # 创建对比可视化
        fig, orig_err, rob_err = create_stability_comparison(
            point_cloud, true_keypoints, original_pred, robust_result
        )
        
        filename = f'stability_comparison_{sample_id}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"   📊 Comparison saved: {filename}")
        plt.close()
    
    # 总体统计
    print(f"\n📈 Overall Performance Summary")
    print("=" * 50)
    print(f"Original Method:")
    print(f"   Average Error: {np.mean(total_original_errors):.2f}mm")
    print(f"   Std Dev: {np.std(total_original_errors):.2f}mm")
    print(f"   Accuracy ≤5mm: {np.sum(np.array(total_original_errors) <= 5)/len(total_original_errors)*100:.1f}%")
    
    print(f"\nRobust Method:")
    print(f"   Average Error: {np.mean(total_robust_errors):.2f}mm")
    print(f"   Std Dev: {np.std(total_robust_errors):.2f}mm")
    print(f"   Accuracy ≤5mm: {np.sum(np.array(total_robust_errors) <= 5)/len(total_robust_errors)*100:.1f}%")
    
    improvement = np.mean(total_original_errors) - np.mean(total_robust_errors)
    print(f"\n🎯 Overall Improvement: {improvement:.2f}mm ({improvement/np.mean(total_original_errors)*100:.1f}%)")

if __name__ == "__main__":
    main()
