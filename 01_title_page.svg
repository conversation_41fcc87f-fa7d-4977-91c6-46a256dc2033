<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1280" height="720" fill="url(#bgGradient)"/>
  
  <!-- Decorative elements -->
  <circle cx="100" cy="100" r="50" fill="rgba(255,255,255,0.1)"/>
  <circle cx="1180" cy="620" r="80" fill="rgba(255,255,255,0.1)"/>
  <circle cx="1200" cy="150" r="30" fill="rgba(255,255,255,0.15)"/>
  
  <!-- Main title -->
  <text x="640" y="200" text-anchor="middle" fill="url(#titleGradient)" 
        font-family="Arial, sans-serif" font-size="48" font-weight="bold">
    Paying attention to the minute details:
  </text>
  
  <text x="640" y="260" text-anchor="middle" fill="url(#titleGradient)" 
        font-family="Arial, sans-serif" font-size="44" font-weight="bold">
    Supervised keypoint detection on dense,
  </text>
  
  <text x="640" y="320" text-anchor="middle" fill="url(#titleGradient)" 
        font-family="Arial, sans-serif" font-size="44" font-weight="bold">
    complex point clouds
  </text>
  
  <!-- Subtitle -->
  <text x="640" y="380" text-anchor="middle" fill="white" 
        font-family="Arial, sans-serif" font-size="24" font-style="italic">
    Engineering Applications of Artificial Intelligence 151 (2025) 110668
  </text>
  
  <!-- Authors -->
  <text x="640" y="450" text-anchor="middle" fill="white" 
        font-family="Arial, sans-serif" font-size="28" font-weight="600">
    Qiuyang Chen, Shenghui Liao, Xiaoyan Kui, Ziyang Hu, Jianda Zhou
  </text>
  
  <!-- Institutions -->
  <text x="640" y="500" text-anchor="middle" fill="rgba(255,255,255,0.9)" 
        font-family="Arial, sans-serif" font-size="20">
    School of Computer Science and Engineering, Central South University
  </text>
  
  <text x="640" y="530" text-anchor="middle" fill="rgba(255,255,255,0.9)" 
        font-family="Arial, sans-serif" font-size="20">
    The Third Xiangya Hospital, Central South University
  </text>
  
  <text x="640" y="560" text-anchor="middle" fill="rgba(255,255,255,0.8)" 
        font-family="Arial, sans-serif" font-size="18">
    Changsha, 410083, Hunan, China
  </text>
  
  <!-- Keywords box -->
  <rect x="440" y="600" width="400" height="80" rx="10" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
  <text x="640" y="625" text-anchor="middle" fill="white" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    Keywords:
  </text>
  <text x="640" y="650" text-anchor="middle" fill="rgba(255,255,255,0.9)" 
        font-family="Arial, sans-serif" font-size="14">
    Deep learning • Point cloud • Supervised 3D keypoint detection
  </text>
  <text x="640" y="670" text-anchor="middle" fill="rgba(255,255,255,0.9)" 
        font-family="Arial, sans-serif" font-size="14">
    3D landmark detection • Medical image processing
  </text>
</svg>
