#!/usr/bin/env python3
"""
精准微调优化
Precision Fine-tuning Optimization
基于7.19mm最佳基线的保守而精准的优化
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pathlib import Path
from datetime import datetime
import json

class PrecisionEnsemblePointNet(nn.Module):
    """精准集成PointNet - 基于最佳配置的微调"""
    
    def __init__(self, num_keypoints=19, num_models=3):
        super().__init__()
        self.num_keypoints = num_keypoints
        self.num_models = num_models
        
        # 保持简单集成的成功架构，只做微调
        self.models = nn.ModuleList()
        
        # 模型1: 标准配置 (保持不变)
        model1 = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(), nn.Dropout(0.1),
            nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.<PERSON>L<PERSON>(), nn.Dropout(0.1),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(), nn.Dropout(0.1),
            nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(), nn.Dropout(0.1),
            nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 模型2: 轻微调整 - 更多通道
        model2 = nn.Sequential(
            nn.Conv1d(3, 80, 1), nn.BatchNorm1d(80), nn.ReLU(), nn.Dropout(0.12),
            nn.Conv1d(80, 160, 1), nn.BatchNorm1d(160), nn.ReLU(), nn.Dropout(0.12),
            nn.Conv1d(160, 320, 1), nn.BatchNorm1d(320), nn.ReLU(), nn.Dropout(0.12),
            nn.Conv1d(320, 640, 1), nn.BatchNorm1d(640), nn.ReLU(), nn.Dropout(0.12),
            nn.Conv1d(640, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 模型3: 轻微调整 - 更深层数
        model3 = nn.Sequential(
            nn.Conv1d(3, 48, 1), nn.BatchNorm1d(48), nn.ReLU(), nn.Dropout(0.08),
            nn.Conv1d(48, 96, 1), nn.BatchNorm1d(96), nn.ReLU(), nn.Dropout(0.08),
            nn.Conv1d(96, 192, 1), nn.BatchNorm1d(192), nn.ReLU(), nn.Dropout(0.08),
            nn.Conv1d(192, 384, 1), nn.BatchNorm1d(384), nn.ReLU(), nn.Dropout(0.08),
            nn.Conv1d(384, 768, 1), nn.BatchNorm1d(768), nn.ReLU(), nn.Dropout(0.08),
            nn.Conv1d(768, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        self.models.extend([model1, model2, model3])
        
        # 回归器 - 轻微增强
        self.regressors = nn.ModuleList([
            nn.Sequential(
                nn.Linear(1024, 640),
                nn.ReLU(),
                nn.Dropout(0.15),
                nn.Linear(640, 384),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(384, 192),
                nn.ReLU(),
                nn.Dropout(0.05),
                nn.Linear(192, num_keypoints * 3)
            ) for _ in range(num_models)
        ])
        
        # 可学习的集成权重
        self.ensemble_weights = nn.Parameter(torch.ones(num_models) / num_models)
        
    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        predictions = []
        
        for i, (model, regressor) in enumerate(zip(self.models, self.regressors)):
            # 特征提取
            features = model(x)  # (B, 1024, N)
            global_feat = torch.max(features, dim=2)[0]  # (B, 1024)
            
            # 关键点预测
            pred = regressor(global_feat)  # (B, num_keypoints * 3)
            pred = pred.view(B, self.num_keypoints, 3)
            predictions.append(pred)
        
        # 加权集成
        weights = F.softmax(self.ensemble_weights, dim=0)
        ensemble_pred = sum(w * pred for w, pred in zip(weights, predictions))
        
        return ensemble_pred

class PrecisionTTA:
    """精准测试时增强"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def apply_precision_tta(self, model, point_cloud, num_augmentations=15):
        """应用精准TTA - 保守而有效"""
        model.eval()
        predictions = []
        
        with torch.no_grad():
            # 原始预测 (权重最高)
            pred = model(point_cloud.unsqueeze(0))
            predictions.append(pred[0])
            
            # 精准增强
            for i in range(num_augmentations - 1):
                aug_pc = point_cloud.clone()
                
                # 1. 极小旋转 (±0.15度)
                if np.random.random() < 0.8:
                    angle = np.random.uniform(-0.0026, 0.0026)  # ±0.15度
                    axis = np.random.choice(['z'])  # 主要Z轴旋转
                    
                    cos_a, sin_a = np.cos(angle), np.sin(angle)
                    rotation = torch.tensor([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], 
                                          dtype=torch.float32, device=self.device)
                    
                    aug_pc = aug_pc @ rotation.T
                
                # 2. 极小噪声 (0.005-0.02mm)
                if np.random.random() < 0.6:
                    noise_std = np.random.uniform(0.005, 0.02)
                    noise = torch.normal(0, noise_std, aug_pc.shape, device=self.device)
                    aug_pc = aug_pc + noise
                
                # 3. 极小缩放 (±0.05%)
                if np.random.random() < 0.3:
                    scale = np.random.uniform(0.9995, 1.0005)
                    aug_pc = aug_pc * scale
                
                # 预测
                aug_pred = model(aug_pc.unsqueeze(0))
                predictions.append(aug_pred[0])
            
            # 加权平均 (原始预测权重更高)
            weights = torch.ones(len(predictions), device=self.device)
            weights[0] = 3.0  # 原始预测权重3倍
            weights = weights / weights.sum()
            
            final_pred = torch.zeros_like(predictions[0])
            for i, pred in enumerate(predictions):
                final_pred += weights[i] * pred
            
        return final_pred

class PrecisionTrainer:
    """精准训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        self.tta = PrecisionTTA(device)
        
    def load_aligned_data(self):
        """加载对齐数据"""
        print("📦 加载F3对齐数据...")
        
        aligned_files = list(Path("data/processed").glob("f3_aligned_dataset_*.npz"))
        if not aligned_files:
            raise FileNotFoundError("未找到F3对齐数据集")
        
        latest_file = max(aligned_files, key=lambda x: x.stat().st_mtime)
        data = np.load(str(latest_file), allow_pickle=True)
        
        point_clouds = np.array(data['point_clouds'], dtype=np.float32)
        keypoints = np.array(data['keypoints'], dtype=np.float32)
        
        # 数据划分 (保持一致)
        from sklearn.model_selection import train_test_split
        indices = np.arange(len(point_clouds))
        train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
        train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
        
        print(f"✅ 数据加载完成: {point_clouds.shape}")
        print(f"   训练: {len(train_indices)}, 验证: {len(val_indices)}, 测试: {len(test_indices)}")
        
        return self.data
    
    def precision_augmentation(self, point_clouds, keypoints):
        """精准数据增强 - 保守而有效"""
        aug_pcs = []
        aug_kps = []
        
        for pc, kp in zip(point_clouds, keypoints):
            # 原始数据
            aug_pcs.append(pc)
            aug_kps.append(kp)
            
            # 精准旋转增强 (±0.1度)
            for _ in range(2):
                angle = np.random.uniform(-0.0017, 0.0017)  # ±0.1度
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], dtype=np.float32)
                
                aug_pc = pc @ rotation.T
                aug_kp = kp @ rotation.T
                aug_pcs.append(aug_pc)
                aug_kps.append(aug_kp)
            
            # 精准噪声增强 (0.005-0.015mm)
            for _ in range(2):
                noise_std = np.random.uniform(0.005, 0.015)
                noise_pc = pc + np.random.normal(0, noise_std, pc.shape).astype(np.float32)
                aug_pcs.append(noise_pc)
                aug_kps.append(kp)
            
            # 精准缩放增强 (±0.05%)
            scale = np.random.uniform(0.9995, 1.0005)
            scaled_pc = pc * scale
            scaled_kp = kp * scale
            aug_pcs.append(scaled_pc)
            aug_kps.append(scaled_kp)
        
        return aug_pcs, aug_kps
    
    def train_precision_model(self, epochs=120, lr=0.0003):
        """精准训练 - 基于最佳实践"""
        print(f"\n🎯 精准微调优化")
        print(f"   策略: 基于7.19mm基线的保守优化")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        # 创建模型
        model = PrecisionEnsemblePointNet(num_keypoints=19, num_models=3).to(self.device)
        
        # 计算参数
        total_params = sum(p.numel() for p in model.parameters())
        print(f"   模型参数: {total_params:,}")
        
        # 优化器 - 使用成功的配置
        optimizer = torch.optim.Adam(
            model.parameters(), 
            lr=lr, 
            weight_decay=1e-4,
            betas=(0.9, 0.999)
        )
        
        # 学习率调度器 - 保守设置
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.7, patience=15, 
            verbose=True, min_lr=1e-6
        )
        
        # 损失函数 - 简单有效
        criterion = nn.MSELoss()
        
        # 训练状态
        best_val_error = float('inf')
        best_model_state = None
        patience = 0
        max_patience = 40
        
        train_history = []
        val_history = []
        
        for epoch in range(epochs):
            # 训练阶段
            model.train()
            epoch_losses = []
            
            # 保守的k_shot策略
            k_shot = min(35, len(self.data['train']['point_clouds']))
            
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                k_shot, 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 精准增强
            aug_pcs, aug_kps = self.precision_augmentation(train_pcs, train_kps)
            
            # 批次训练
            batch_size = 6  # 保持成功的批次大小
            for i in range(0, len(aug_pcs), batch_size):
                batch_pcs = torch.FloatTensor(aug_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(aug_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                pred_kps = model(batch_pcs)
                loss = criterion(pred_kps, batch_kps)
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.step()
                epoch_losses.append(loss.item())
                
                del batch_pcs, batch_kps, pred_kps, loss
                torch.cuda.empty_cache()
            
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            train_history.append(avg_loss)
            
            # 验证
            if epoch % 5 == 0:
                val_error = self.evaluate_model(model, 'val')
                val_history.append(val_error)
                scheduler.step(val_error)
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                    patience = 0
                    
                    # 如果超越基线，保存检查点
                    if val_error <= 6.0:
                        self.save_checkpoint(model, val_error, epoch, "beating_baseline")
                        print(f"🎯 超越基线！验证误差: {val_error:.3f}mm")
                    
                    if val_error <= 5.5:
                        self.save_checkpoint(model, val_error, epoch, "excellent_result")
                        print(f"🔥 优秀结果！验证误差: {val_error:.3f}mm")
                        
                    if val_error <= 5.0:
                        self.save_checkpoint(model, val_error, epoch, "medical_grade")
                        print(f"🎉 医疗级精度！验证误差: {val_error:.3f}mm")
                else:
                    patience += 1
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm, "
                      f"LR={optimizer.param_groups[0]['lr']:.6f}, P={patience}")
                
                if patience >= max_patience:
                    print(f"早停在epoch {epoch}")
                    break
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)
            print(f"✅ 加载最佳模型 (验证误差: {best_val_error:.3f}mm)")
        
        self.model = model
        self.training_history = {
            'train_losses': train_history,
            'val_errors': val_history,
            'best_val_error': best_val_error
        }
        
        return model, best_val_error
    
    def evaluate_model(self, model, split='test'):
        """评估模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for i in range(0, len(pcs), 2):
                batch_pcs = torch.FloatTensor(pcs[i:i+2]).to(self.device)
                batch_kps = torch.FloatTensor(kps[i:i+2]).to(self.device)
                
                pred_kps = model(batch_pcs)
                
                for j in range(len(batch_pcs)):
                    error = torch.mean(torch.norm(pred_kps[j] - batch_kps[j], dim=1))
                    total_error += error.item()
                    num_samples += 1
                
                del batch_pcs, batch_kps, pred_kps
                torch.cuda.empty_cache()
        
        return total_error / num_samples if num_samples > 0 else float('inf')
    
    def evaluate_with_precision_tta(self, model, split='test'):
        """使用精准TTA评估"""
        print(f"\n🔬 精准TTA评估 ({split}集)...")
        
        model.eval()
        tta_errors = []
        
        pcs = self.data[split]['point_clouds']
        kps = self.data[split]['keypoints']
        
        for i, (pc, kp) in enumerate(zip(pcs, kps)):
            pc_tensor = torch.FloatTensor(pc).to(self.device)
            kp_tensor = torch.FloatTensor(kp).to(self.device)
            
            # 应用精准TTA
            tta_pred = self.tta.apply_precision_tta(model, pc_tensor, num_augmentations=15)
            
            error = torch.mean(torch.norm(tta_pred - kp_tensor, dim=1))
            tta_errors.append(error.item())
            
            if i < 3:
                print(f"   样本 {i+1}: 精准TTA误差 = {error:.3f}mm")
            
            del pc_tensor, kp_tensor, tta_pred
            torch.cuda.empty_cache()
        
        avg_error = np.mean(tta_errors)
        print(f"   精准TTA平均误差: {avg_error:.3f}mm")
        
        return avg_error, tta_errors
    
    def save_checkpoint(self, model, val_error, epoch, tag=""):
        """保存检查点"""
        output_dir = Path("trained_models/precision_optimization")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"precision_opt_{val_error:.3f}mm_epoch{epoch}_{tag}_{timestamp}.pth"
        model_path = output_dir / filename
        
        torch.save({
            'model_state_dict': model.state_dict(),
            'validation_error': val_error,
            'epoch': epoch,
            'timestamp': timestamp,
            'optimization_type': 'precision_fine_tuning',
            'baseline': '7.19mm_simple_ensemble'
        }, model_path)
        
        print(f"💾 检查点已保存: {model_path}")
        return model_path

def run_precision_optimization():
    """运行精准优化"""
    print("🎯 精准微调优化实验")
    print("=" * 60)
    print("基线: 简单集成PointNet 7.19mm")
    print("策略: 保守而精准的微调优化")
    print("目标: 突破7.19mm，冲击6.5mm")
    print()
    print("优化要点:")
    print("• 轻微架构调整 (不破坏成功配置)")
    print("• 精准数据增强 (±0.1度, 0.005-0.015mm)")
    print("• 保守训练策略 (成功的超参数)")
    print("• 精准TTA (15次保守增强)")
    print("• 早停保护 (防止过拟合)")
    
    trainer = PrecisionTrainer()
    data = trainer.load_aligned_data()
    
    # 精准训练
    model, val_error = trainer.train_precision_model(epochs=120, lr=0.0003)
    
    # 标准测试
    test_error = trainer.evaluate_model(model, 'test')
    
    # 精准TTA测试
    precision_tta_error, tta_errors = trainer.evaluate_with_precision_tta(model, 'test')
    
    # 结果分析
    baseline_error = 7.19
    
    print(f"\n🏆 精准优化结果:")
    print("=" * 50)
    print(f"基线 (简单集成):          {baseline_error:.2f}mm")
    print(f"精准优化 (验证):          {val_error:.3f}mm")
    print(f"精准优化 (测试):          {test_error:.3f}mm")
    print(f"精准优化 + TTA:           {precision_tta_error:.3f}mm")
    
    # 改进分析
    best_error = min(test_error, precision_tta_error)
    improvement = (baseline_error - best_error) / baseline_error * 100
    
    print(f"\n📈 改进分析:")
    print(f"最佳结果:                 {best_error:.3f}mm")
    print(f"vs 基线改进:              {improvement:+.1f}%")
    
    # 目标评估
    target_6_5 = 6.5
    target_6_0 = 6.0
    target_5_5 = 5.5
    target_5_0 = 5.0
    
    print(f"\n🎯 目标达成评估:")
    print(f"6.5mm目标:               {'✅ 达成' if best_error <= target_6_5 else f'❌ 还需{best_error - target_6_5:.3f}mm'}")
    print(f"6.0mm目标:               {'✅ 达成' if best_error <= target_6_0 else f'❌ 还需{best_error - target_6_0:.3f}mm'}")
    print(f"5.5mm目标:               {'✅ 达成' if best_error <= target_5_5 else f'❌ 还需{best_error - target_5_5:.3f}mm'}")
    print(f"5.0mm医疗级:             {'🎉 达成' if best_error <= target_5_0 else f'❌ 还需{best_error - target_5_0:.3f}mm'}")
    
    # 状态评估
    if best_error <= 5.0:
        status = "🎉 医疗级精度达成"
    elif best_error <= 5.5:
        status = "🔥 极其接近医疗级"
    elif best_error <= 6.0:
        status = "🎯 非常接近目标"
    elif best_error <= 6.5:
        status = "📈 良好改进"
    elif improvement > 0:
        status = "✅ 有效改进"
    else:
        status = "⚠️ 需要调整策略"
    
    print(f"\n状态: {status}")
    
    # 保存最终模型
    final_model_path = trainer.save_checkpoint(model, best_error, 120, "final_precision")
    
    return trainer, {
        'val_error': val_error,
        'test_error': test_error,
        'precision_tta_error': precision_tta_error,
        'best_error': best_error,
        'improvement_percent': improvement,
        'status': status,
        'baseline_beaten': best_error < baseline_error
    }

if __name__ == "__main__":
    trainer, results = run_precision_optimization()
