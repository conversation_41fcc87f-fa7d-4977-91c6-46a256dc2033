#!/usr/bin/env python3
"""
Training with Detailed Diagnostics for F3 Keypoint Detection

包含详细诊断信息的训练脚本，用于分析训练充分性和质量
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random
from train_precomputed_keypoint_sampling_f3 import (
    PrecomputedKeypointF3Dataset, 
    ConservativePointNet, 
    ImprovedLoss, 
    calculate_metrics,
    set_seed
)

def train_with_diagnostics(extended_epochs=100, patience=25):
    """带详细诊断的训练函数"""
    
    print("🔍 **详细诊断训练 - F3关键点检测**")
    print("🎯 **目标: 深入分析训练过程的充分性和质量**")
    print(f"📊 **配置: {extended_epochs}轮训练, {patience}轮耐心**")
    print("=" * 80)
    
    set_seed(42)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print(f"🖥️  GPU内存: {torch.cuda.get_device_properties(device).total_memory / 1e9:.1f}GB")
    
    # 数据集
    dataset_path = "high_quality_f3_dataset.npz"
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    print("🔄 创建数据集...")
    dataset_start_time = time.time()
    
    train_dataset = PrecomputedKeypointF3Dataset(dataset_path, 'train', num_points=4096, 
                                               test_samples=test_samples, augment=True, 
                                               seed=42, focus_ratio=0.7)
    val_dataset = PrecomputedKeypointF3Dataset(dataset_path, 'val', num_points=4096, 
                                             test_samples=test_samples, augment=False, 
                                             seed=42, focus_ratio=0.7)
    test_dataset = PrecomputedKeypointF3Dataset(dataset_path, 'test', num_points=4096, 
                                              test_samples=test_samples, augment=False, 
                                              seed=42, focus_ratio=0.7)
    
    dataset_creation_time = time.time() - dataset_start_time
    print(f"✅ 数据集创建完成，耗时: {dataset_creation_time:.2f}秒")
    
    batch_size = 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    print(f"📊 批次配置: 大小{batch_size}, 训练批次{len(train_loader)}, 验证批次{len(val_loader)}")
    
    # 模型
    model = ConservativePointNet(num_keypoints=19).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"🧠 模型参数: 总计{total_params:,}, 可训练{trainable_params:,}")
    
    # 训练配置
    criterion = ImprovedLoss(alpha=0.8, beta=0.2)
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
    
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer, max_lr=0.0016, epochs=extended_epochs, steps_per_epoch=len(train_loader),
        pct_start=0.1, anneal_strategy='cos', div_factor=20, final_div_factor=100
    )
    
    print(f"🎯 训练配置:")
    print(f"   损失函数: ImprovedLoss(α=0.8, β=0.2)")
    print(f"   优化器: AdamW(lr=0.0008, wd=1e-4)")
    print(f"   调度器: OneCycleLR(max_lr=0.0016)")
    print(f"   最大轮数: {extended_epochs}")
    print(f"   早停耐心: {patience}")
    
    # 训练状态跟踪
    best_val_error = float('inf')
    patience_counter = 0
    history = []
    training_start_time = time.time()
    
    # 详细的训练统计
    epoch_times = []
    batch_times = []
    gradient_norms = []
    learning_rates = []
    
    print(f"\n🚀 开始详细诊断训练")
    print("=" * 80)
    
    for epoch in range(extended_epochs):
        epoch_start_time = time.time()
        
        print(f"\n📈 Epoch {epoch+1}/{extended_epochs}")
        print("-" * 50)
        
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        epoch_batch_times = []
        epoch_gradient_norms = []
        
        for batch_idx, batch in enumerate(train_loader):
            batch_start_time = time.time()
            
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                
                # 记录梯度范数
                total_norm = 0
                for p in model.parameters():
                    if p.grad is not None:
                        param_norm = p.grad.data.norm(2)
                        total_norm += param_norm.item() ** 2
                total_norm = total_norm ** (1. / 2)
                epoch_gradient_norms.append(total_norm)
                
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                scheduler.step()
                
                train_loss += loss.item()
                
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                
                batch_end_time = time.time()
                epoch_batch_times.append(batch_end_time - batch_start_time)
                        
            except RuntimeError as e:
                print(f"❌ 训练批次{batch_idx}失败: {e}")
                continue
        
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # 记录统计信息
        epoch_end_time = time.time()
        epoch_time = epoch_end_time - epoch_start_time
        epoch_times.append(epoch_time)
        
        if epoch_batch_times:
            batch_times.extend(epoch_batch_times)
        
        if epoch_gradient_norms:
            gradient_norms.extend(epoch_gradient_norms)
        
        current_lr = optimizer.param_groups[0]['lr']
        learning_rates.append(current_lr)
        
        # 打印详细结果
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"5mm={train_metrics['within_5mm_percent']:.1f}%, 7mm={train_metrics['within_7mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"5mm={val_metrics['within_5mm_percent']:.1f}%, 7mm={val_metrics['within_7mm_percent']:.1f}%")
        print(f"时间: 轮次{epoch_time:.1f}s, 平均批次{np.mean(epoch_batch_times):.3f}s")
        print(f"梯度: 平均范数{np.mean(epoch_gradient_norms):.4f}, 学习率{current_lr:.2e}")
        
        # 保存详细历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'learning_rate': current_lr,
            'epoch_time': epoch_time,
            'avg_batch_time': np.mean(epoch_batch_times) if epoch_batch_times else 0,
            'avg_gradient_norm': np.mean(epoch_gradient_norms) if epoch_gradient_norms else 0,
            'gradient_norm_std': np.std(epoch_gradient_norms) if epoch_gradient_norms else 0
        })
        
        # 检查改进
        current_error = val_metrics['mean_distance']
        if current_error < best_val_error:
            best_val_error = current_error
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics,
                'training_stats': {
                    'epoch_times': epoch_times,
                    'batch_times': batch_times[-100:],  # 保存最近100个批次时间
                    'gradient_norms': gradient_norms[-100:],  # 保存最近100个梯度范数
                    'learning_rates': learning_rates
                }
            }, 'best_diagnostic_keypoint_f3.pth')
            
            print(f"🎉 新最佳! 验证误差: {best_val_error:.3f}mm")
            
            if best_val_error <= 5.0:
                print(f"🏆 **突破5mm目标!**")
            elif best_val_error < 7.631:
                print(f"✅ **优于保守基线!** 超越7.631mm")
            elif best_val_error < 8.543:
                print(f"✅ **优于随机基线!** 超越8.543mm")
        else:
            patience_counter += 1
            print(f"⏳ 无改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停触发")
            break
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_training_time = time.time() - training_start_time
    
    print(f"\n🎯 **详细诊断训练完成!**")
    print(f"   总训练时间: {total_training_time/60:.2f}分钟")
    print(f"   实际训练轮数: {len(history)}")
    print(f"   最佳验证误差: {best_val_error:.3f}mm")
    print(f"   平均轮次时间: {np.mean(epoch_times):.1f}秒")
    print(f"   平均批次时间: {np.mean(batch_times):.3f}秒")
    print(f"   平均梯度范数: {np.mean(gradient_norms):.4f}")
    
    return {
        'model': model,
        'test_loader': test_loader,
        'best_val_error': best_val_error,
        'total_training_time': total_training_time,
        'history': history,
        'training_stats': {
            'dataset_creation_time': dataset_creation_time,
            'total_epochs': len(history),
            'avg_epoch_time': np.mean(epoch_times),
            'avg_batch_time': np.mean(batch_times),
            'avg_gradient_norm': np.mean(gradient_norms),
            'gradient_norm_std': np.std(gradient_norms),
            'final_learning_rate': learning_rates[-1] if learning_rates else 0
        }
    }

if __name__ == "__main__":
    set_seed(42)
    
    print("🔍 **开始详细诊断训练**")
    print("🎯 **目标**: 深入分析训练过程，确保训练充分性和质量")
    
    # 运行详细诊断训练
    results = train_with_diagnostics(extended_epochs=100, patience=25)
    
    if results:
        print(f"\n💾 **保存详细结果...**")
        
        # 保存完整的诊断结果
        diagnostic_results = {
            'method': 'Detailed Diagnostic Training',
            'focus_ratio': 0.7,
            'best_val_error': float(results['best_val_error']),
            'training_time_minutes': float(results['total_training_time'] / 60),
            'history': results['history'],
            'training_stats': results['training_stats']
        }
        
        with open('detailed_diagnostic_results.json', 'w', encoding='utf-8') as f:
            json.dump(diagnostic_results, f, indent=2, ensure_ascii=False)
        
        print(f"✅ **详细诊断结果已保存**: detailed_diagnostic_results.json")
        print(f"🔍 **请运行诊断分析器进行深入分析**")
