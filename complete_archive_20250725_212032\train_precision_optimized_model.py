#!/usr/bin/env python3
"""
训练精密优化模型
Train Precision Optimized Model
使用最佳超参数和区域感知架构
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import json
from tqdm import tqdm

class RegionAwarePointNet57(nn.Module):
    """区域感知PointNet57"""
    
    def __init__(self, num_keypoints=57, dropout_rate=0.2):
        super(RegionAwarePointNet57, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 共享特征提取器
        self.shared_conv1 = nn.Conv1d(3, 64, 1)
        self.shared_conv2 = nn.Conv1d(64, 128, 1)
        self.shared_conv3 = nn.Conv1d(128, 256, 1)
        self.shared_conv4 = nn.Conv1d(256, 512, 1)
        self.shared_conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.shared_bn1 = nn.BatchNorm1d(64)
        self.shared_bn2 = nn.BatchNorm1d(128)
        self.shared_bn3 = nn.BatchNorm1d(256)
        self.shared_bn4 = nn.BatchNorm1d(512)
        self.shared_bn5 = nn.BatchNorm1d(1024)
        
        # 区域特定的回归头
        self.f1_head = self._create_region_head(1024, 19)
        self.f2_head = self._create_region_head(1024, 19)
        self.f3_head = self._create_region_head(1024, 19)
        
        # 权重初始化
        self._initialize_weights()
        
    def _create_region_head(self, input_dim, num_points):
        """创建区域特定的回归头"""
        return nn.Sequential(
            nn.Linear(input_dim, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, num_points * 3)
        )
    
    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 共享特征提取
        x = F.relu(self.shared_bn1(self.shared_conv1(x)))
        x = F.relu(self.shared_bn2(self.shared_conv2(x)))
        x = F.relu(self.shared_bn3(self.shared_conv3(x)))
        x = F.relu(self.shared_bn4(self.shared_conv4(x)))
        x = F.relu(self.shared_bn5(self.shared_conv5(x)))
        
        # 全局最大池化
        global_feat = torch.max(x, 2)[0]  # [B, 1024]
        
        # 区域特定预测
        f1_pred = self.f1_head(global_feat).view(batch_size, 19, 3)
        f2_pred = self.f2_head(global_feat).view(batch_size, 19, 3)
        f3_pred = self.f3_head(global_feat).view(batch_size, 19, 3)
        
        # 合并预测结果
        keypoints = torch.cat([f1_pred, f2_pred, f3_pred], dim=1)  # [B, 57, 3]
        
        return keypoints, (f1_pred, f2_pred, f3_pred)

class RegionWeightedLoss(nn.Module):
    """区域加权损失函数"""
    
    def __init__(self, f1_weight=1.3, f2_weight=1.3, f3_weight=0.7):
        super(RegionWeightedLoss, self).__init__()
        self.f1_weight = f1_weight
        self.f2_weight = f2_weight
        self.f3_weight = f3_weight
        
    def forward(self, pred_all, pred_regions, target):
        # 整体损失
        overall_loss = F.mse_loss(pred_all, target)
        
        # 区域特定损失
        f1_pred, f2_pred, f3_pred = pred_regions
        
        f1_loss = F.mse_loss(f1_pred, target[:, 0:19]) * self.f1_weight
        f2_loss = F.mse_loss(f2_pred, target[:, 19:38]) * self.f2_weight
        f3_loss = F.mse_loss(f3_pred, target[:, 38:57]) * self.f3_weight
        
        total_loss = overall_loss + f1_loss + f2_loss + f3_loss
        
        return total_loss

class Dataset57(Dataset):
    def __init__(self, point_clouds, keypoints):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def create_region_aware_normalization(point_clouds, keypoints_57):
    """创建区域感知归一化"""
    print("🔧 执行区域感知归一化...")
    
    normalized_pc = []
    normalized_kp = []
    region_scalers = []
    
    for i in range(len(point_clouds)):
        pc = point_clouds[i].copy()
        kp = keypoints_57[i].copy()
        
        # 分别处理每个区域
        f1_kp = kp[0:19]
        f2_kp = kp[19:38]
        f3_kp = kp[38:57]
        
        # 为每个区域创建独立的归一化
        sample_scalers = {}
        normalized_regions = []
        
        for region_name, region_kp in [('F1', f1_kp), ('F2', f2_kp), ('F3', f3_kp)]:
            # 结合点云和该区域关键点进行归一化
            combined_data = np.vstack([pc, region_kp])
            
            scaler = StandardScaler()
            combined_normalized = scaler.fit_transform(combined_data)
            
            # 只取关键点部分
            region_normalized = combined_normalized[len(pc):]
            normalized_regions.append(region_normalized)
            sample_scalers[region_name] = scaler
        
        # 合并归一化后的关键点
        kp_normalized = np.vstack(normalized_regions)
        
        # 使用F3区域的scaler处理点云（因为F3表现最好）
        pc_combined = np.vstack([pc, f3_kp])
        pc_normalized = sample_scalers['F3'].fit_transform(pc_combined)[:len(pc)]
        
        normalized_pc.append(pc_normalized)
        normalized_kp.append(kp_normalized)
        region_scalers.append(sample_scalers)
    
    normalized_pc = np.array(normalized_pc)
    normalized_kp = np.array(normalized_kp)
    
    return normalized_pc, normalized_kp, region_scalers

def train_precision_model(model, train_loader, val_loader, epochs=150, device='cuda'):
    """训练精密优化模型"""
    
    print(f"🚀 训练精密优化模型...")
    print(f"   使用最佳超参数和区域感知架构")
    
    model = model.to(device)
    
    # 使用优化后的超参数
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.0001)
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=25, T_mult=2, eta_min=1e-7
    )
    
    criterion = RegionWeightedLoss(f1_weight=1.3, f2_weight=1.3, f3_weight=0.7)
    
    history = {'train_loss': [], 'val_loss': [], 'train_error': [], 'val_error': []}
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 30
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            pred_all, pred_regions = model(batch_pc)
            loss = criterion(pred_all, pred_regions, batch_kp)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
            
            optimizer.step()
            
            train_loss += loss.item()
            
            with torch.no_grad():
                distances = torch.norm(pred_all - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                pred_all, pred_regions = model(batch_pc)
                loss = criterion(pred_all, pred_regions, batch_kp)
                
                val_loss += loss.item()
                distances = torch.norm(pred_all - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_error'].append(train_error)
        history['val_error'].append(val_error)
        
        scheduler.step()
        
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_precision_optimized_model.pth')
        else:
            patience_counter += 1
        
        current_lr = optimizer.param_groups[0]['lr']
        
        if epoch % 10 == 0 or epoch < 5:
            print(f"Epoch {epoch+1:3d}: "
                  f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
                  f"Train Error: {train_error:.4f}, Val Error: {val_error:.4f}, "
                  f"LR: {current_lr:.2e}")
        
        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    model.load_state_dict(torch.load('best_precision_optimized_model.pth'))
    return history

def test_precision_model(model, test_loader, region_scalers, test_indices, device='cuda'):
    """测试精密优化模型"""
    
    print("🔍 测试精密优化模型...")
    
    model = model.to(device)
    model.eval()
    
    test_predictions = []
    test_targets = []
    region_predictions = []
    
    with torch.no_grad():
        for batch_pc, batch_kp in test_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            pred_all, pred_regions = model(batch_pc)
            
            test_predictions.append(pred_all.cpu().numpy())
            test_targets.append(batch_kp.cpu().numpy())
            
            # 保存区域预测
            f1_pred, f2_pred, f3_pred = pred_regions
            region_predictions.append({
                'f1': f1_pred.cpu().numpy(),
                'f2': f2_pred.cpu().numpy(),
                'f3': f3_pred.cpu().numpy()
            })
    
    test_predictions = np.vstack(test_predictions)
    test_targets = np.vstack(test_targets)
    
    # 反归一化
    real_predictions = []
    real_targets = []
    
    for i, orig_idx in enumerate(test_indices):
        if i < len(test_predictions):
            pred_norm = test_predictions[i]
            target_norm = test_targets[i]
            
            scalers = region_scalers[orig_idx]
            
            # 分区域反归一化
            pred_regions = []
            target_regions = []
            
            for j, (region_name, region_scaler) in enumerate([('F1', scalers['F1']), ('F2', scalers['F2']), ('F3', scalers['F3'])]):
                start_idx = j * 19
                end_idx = (j + 1) * 19
                
                # 预测反归一化
                dummy_pc = np.zeros((50000, 3))
                combined_pred = np.vstack([dummy_pc, pred_norm[start_idx:end_idx]])
                combined_pred_denorm = region_scaler.inverse_transform(combined_pred)
                pred_region_real = combined_pred_denorm[50000:]
                
                # 目标反归一化
                combined_target = np.vstack([dummy_pc, target_norm[start_idx:end_idx]])
                combined_target_denorm = region_scaler.inverse_transform(combined_target)
                target_region_real = combined_target_denorm[50000:]
                
                pred_regions.append(pred_region_real)
                target_regions.append(target_region_real)
            
            pred_real = np.vstack(pred_regions)
            target_real = np.vstack(target_regions)
            
            real_predictions.append(pred_real)
            real_targets.append(target_real)
    
    real_predictions = np.array(real_predictions)
    real_targets = np.array(real_targets)
    
    # 计算误差
    total_error = 0.0
    region_errors = {'F1': [], 'F2': [], 'F3': []}
    all_errors = []
    
    for i in range(len(real_predictions)):
        pred = real_predictions[i]
        target = real_targets[i]
        
        distances = np.linalg.norm(pred - target, axis=1)
        total_error += np.mean(distances)
        all_errors.extend(distances)
        
        # 分区域
        region_errors['F1'].extend(distances[0:19])
        region_errors['F2'].extend(distances[19:38])
        region_errors['F3'].extend(distances[38:57])
    
    avg_error = total_error / len(real_predictions)
    
    # 计算准确率
    accuracy_5mm = np.mean(np.array(all_errors) < 5.0) * 100
    accuracy_10mm = np.mean(np.array(all_errors) < 10.0) * 100
    
    print(f"\n🎯 精密优化模型结果:")
    print(f"   整体平均误差: {avg_error:.2f}mm")
    
    for region, errors in region_errors.items():
        if errors:
            mean_error = np.mean(errors)
            std_error = np.std(errors)
            print(f"   {region}区域: {mean_error:.2f}±{std_error:.2f}mm")
    
    print(f"   医疗级准确率:")
    print(f"     <5mm: {accuracy_5mm:.1f}%")
    print(f"     <10mm: {accuracy_10mm:.1f}%")
    
    return avg_error, region_errors, accuracy_5mm, accuracy_10mm

def main():
    """主函数"""
    
    print("🎯 训练精密优化模型")
    print("使用最佳超参数和区域感知架构")
    print("=" * 80)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载高质量数据集
    print("📊 加载高质量数据集...")
    data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
    
    point_clouds = data['point_clouds']
    keypoints_57 = data['keypoints_57']
    sample_ids = data['sample_ids']
    
    print(f"✅ 数据集加载完成: {len(sample_ids)} 个样本")
    
    # 区域感知归一化
    normalized_pc, normalized_kp, region_scalers = create_region_aware_normalization(
        point_clouds, keypoints_57
    )
    
    # 数据划分
    indices = np.arange(len(normalized_pc))
    train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
    train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
    
    # 创建数据集
    train_dataset = Dataset57(normalized_pc[train_indices], normalized_kp[train_indices])
    val_dataset = Dataset57(normalized_pc[val_indices], normalized_kp[val_indices])
    test_dataset = Dataset57(normalized_pc[test_indices], normalized_kp[test_indices])
    
    # 数据加载器 - 使用最佳批次大小
    batch_size = 6
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    
    print(f"📋 数据划分: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    
    # 创建精密优化模型
    model = RegionAwarePointNet57(dropout_rate=0.2)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🏗️ RegionAwarePointNet57: {total_params:,} 参数")
    
    # 训练模型
    history = train_precision_model(model, train_loader, val_loader, epochs=150, device=device)
    
    # 测试模型
    avg_error, region_errors, acc_5mm, acc_10mm = test_precision_model(
        model, test_loader, region_scalers, test_indices, device=device
    )
    
    print(f"\n📊 性能对比总结:")
    print(f"   Unified数据集: 16.71mm")
    print(f"   高质量数据集: 15.49mm")
    print(f"   归一化优化: 11.81mm")
    print(f"   精密优化: {avg_error:.2f}mm")
    
    # 计算改进幅度
    improvement_vs_best = (11.81 - avg_error) / 11.81 * 100
    improvement_vs_baseline = (16.71 - avg_error) / 16.71 * 100
    
    print(f"\n💡 改进幅度:")
    print(f"   相比最佳(11.81mm): {improvement_vs_best:+.1f}%")
    print(f"   相比基线(16.71mm): {improvement_vs_baseline:+.1f}%")
    
    # 保存结果
    results = {
        'avg_error': float(avg_error),
        'region_errors': {k: float(np.mean(v)) for k, v in region_errors.items()},
        'accuracy_5mm': float(acc_5mm),
        'accuracy_10mm': float(acc_10mm),
        'improvement_vs_best': float(improvement_vs_best),
        'improvement_vs_baseline': float(improvement_vs_baseline),
        'training_history': history,
        'precision_features': [
            'region_aware_normalization',
            'region_specific_regression_heads',
            'region_weighted_loss',
            'optimized_hyperparameters',
            'f3_centered_strategy'
        ]
    }
    
    with open('precision_optimized_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    if avg_error < 10.0:
        print(f"\n🎉 精密优化模型效果优秀！突破10mm大关！")
        print(f"💡 证明了精细化改进策略的有效性")
    elif avg_error < 11.81:
        print(f"\n✅ 精密优化模型有进一步提升！")
    else:
        print(f"\n⚠️ 精密优化效果有限")
    
    print(f"\n💾 详细结果已保存: precision_optimized_results.json")
    print(f"🎉 精密优化模型训练完成！")

if __name__ == "__main__":
    main()
