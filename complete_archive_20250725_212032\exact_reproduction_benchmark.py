#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确复现基准测试 - 使用与您完全相同的训练方法和数据集
Exact Reproduction Benchmark - Using Your Exact Training Method and Dataset
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split
import json
import matplotlib.pyplot as plt
import pandas as pd
import time

# 设置样式
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

class AdaptiveKeypointModel(nn.Module):
    """完全相同的自适应关键点模型"""
    
    def __init__(self, num_points=50000, num_keypoints=12, architecture_type='auto'):
        super().__init__()
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        self.architecture_type = architecture_type
        
        # 根据关键点数量选择最佳架构
        if architecture_type == 'auto':
            if num_keypoints <= 6:
                self.arch_type = 'lightweight'
            elif num_keypoints <= 12:
                self.arch_type = 'balanced'
            elif num_keypoints <= 28:
                self.arch_type = 'enhanced'
            else:
                self.arch_type = 'deep'
        else:
            self.arch_type = architecture_type
        
        # 构建对应架构
        self._build_architecture()
    
    def _build_architecture(self):
        """根据类型构建架构"""
        
        if self.arch_type == 'lightweight':
            # 轻量级架构 (3-6关键点)
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(256, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, 128), nn.ReLU(), nn.Dropout(0.1),
                nn.Linear(128, self.num_keypoints * 3)
            )
            
        elif self.arch_type == 'balanced':
            # 平衡架构 (7-12关键点)
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(512, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, self.num_keypoints * 3)
            )
            
        elif self.arch_type == 'enhanced':
            # 增强架构 (13-28关键点)
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
                nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(1024, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 1024), nn.ReLU(), nn.Dropout(0.4),
                nn.Linear(1024, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, self.num_keypoints * 3)
            )
        
        else:  # deep
            # 深度架构 (29-57关键点)
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
                nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU(),
                nn.Conv1d(1024, 2048, 1), nn.BatchNorm1d(2048), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(2048, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 1024), nn.ReLU(), nn.Dropout(0.5),
                nn.Linear(1024, 1024), nn.ReLU(), nn.Dropout(0.4),
                nn.Linear(1024, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, self.num_keypoints * 3)
            )
        
        # 相互辅助机制
        mutual_dim = min(256, max(64, self.num_keypoints * 8))
        self.mutual_assistance = nn.Sequential(
            nn.Linear(self.num_keypoints * 3, mutual_dim),
            nn.ReLU(), nn.Dropout(0.2),
            nn.Linear(mutual_dim, mutual_dim // 2),
            nn.ReLU(),
            nn.Linear(mutual_dim // 2, self.num_keypoints * 3)
        )
    
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取
        features = self.feature_extractor(x)
        global_features = self.global_conv(features)
        global_feat = torch.max(global_features, 2)[0]
        
        # 预测
        initial_kp = self.predictor(global_feat)
        assistance = self.mutual_assistance(initial_kp)
        final_kp = initial_kp + 0.3 * assistance
        final_kp = final_kp.view(batch_size, self.num_keypoints, 3)
        
        return final_kp

# 主流模型实现
class PointNet(nn.Module):
    """PointNet基线模型"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(1024)
        
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        x = torch.relu(self.bn1(self.conv1(x)))
        x = torch.relu(self.bn2(self.conv2(x)))
        x = torch.relu(self.bn3(self.conv3(x)))
        
        x = torch.max(x, 2)[0]
        
        x = torch.relu(self.fc1(x))
        x = self.dropout(x)
        x = torch.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

class PointNetPlusPlus(nn.Module):
    """PointNet++模型"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
        self.fc1 = nn.Linear(512, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.4)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        x = torch.relu(self.bn1(self.conv1(x)))
        x = torch.relu(self.bn2(self.conv2(x)))
        x = torch.relu(self.bn3(self.conv3(x)))
        x = torch.relu(self.bn4(self.conv4(x)))
        
        x = torch.max(x, 2)[0]
        
        x = torch.relu(self.fc1(x))
        x = self.dropout(x)
        x = torch.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

class ExactReproductionBenchmark:
    """精确复现基准测试"""
    
    def __init__(self, device='cuda:3'):  # 使用GPU 3
        self.device = device if torch.cuda.is_available() else 'cpu'
        print(f"🖥️ 使用设备: {self.device}")
        
        # 模型配置
        self.models = {
            'Our_Adaptive': AdaptiveKeypointModel,
            'PointNet_Baseline': PointNet,
            'PointNet++_Baseline': PointNetPlusPlus,
        }
        
        # 测试配置
        self.keypoint_configs = [12, 28]  # 先测试关键配置
        
        self.results = []
    
    def load_original_data(self):
        """加载原始训练数据"""
        print("📥 加载原始训练数据...")
        
        try:
            # 加载12关键点数据
            female_data = np.load('archive/old_experiments/f3_reduced_12kp_female.npz')
            female_pc = female_data['point_clouds']
            female_kp = female_data['keypoints']
            
            male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz')
            male_pc = male_data['point_clouds']
            male_kp = male_data['keypoints']
            
            # 合并数据
            self.point_clouds = np.vstack([female_pc, male_pc])
            self.base_keypoints = np.vstack([female_kp, male_kp])
            
            print(f"✅ 原始数据加载成功:")
            print(f"   样本数: {len(self.point_clouds)}")
            print(f"   基础关键点: {self.base_keypoints.shape[1]}个")
            print(f"   点云范围: X[{self.point_clouds[:,:,0].min():.1f}, {self.point_clouds[:,:,0].max():.1f}]")
            print(f"   关键点范围: X[{self.base_keypoints[:,:,0].min():.1f}, {self.base_keypoints[:,:,0].max():.1f}]")
            
            return True
            
        except Exception as e:
            print(f"❌ 原始数据加载失败: {e}")
            print("尝试使用当前数据集...")
            
            # 备用方案
            data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
            self.point_clouds = data['point_clouds']
            keypoints_57 = data['keypoints_57']
            
            # 从57个关键点中选择12个
            indices = np.linspace(0, 56, 12, dtype=int)
            self.base_keypoints = keypoints_57[:, indices, :]
            
            print(f"✅ 使用当前数据集作为备用方案")
            return True
    
    def expand_keypoints(self, base_keypoints, num_kp):
        """扩展关键点到指定数量"""
        if num_kp <= 12:
            if num_kp == 12:
                indices = list(range(12))
            elif num_kp == 9:
                indices = [0, 1, 3, 4, 6, 7, 8, 10, 11]
            elif num_kp == 6:
                indices = [0, 2, 4, 7, 9, 11]
            elif num_kp == 3:
                indices = [0, 5, 11]
            else:
                indices = np.linspace(0, 11, num_kp, dtype=int).tolist()
            
            return base_keypoints[:, indices, :]
        else:
            # 通过插值扩展
            expanded_keypoints = []
            for i in range(len(base_keypoints)):
                base_kp = base_keypoints[i]
                expanded = np.zeros((num_kp, 3))
                for j in range(num_kp):
                    ratio = j / (num_kp - 1) * 11
                    idx = int(ratio)
                    if idx >= 11:
                        expanded[j] = base_kp[11]
                    else:
                        alpha = ratio - idx
                        expanded[j] = (1 - alpha) * base_kp[idx] + alpha * base_kp[min(idx + 1, 11)]
                expanded_keypoints.append(expanded)
            return np.array(expanded_keypoints)
    
    def train_and_evaluate_exact(self, model, train_pc, train_kp, test_pc, test_kp, model_name):
        """使用完全相同的训练和评估方法"""
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=8, factor=0.5)
        
        # 转换为张量
        train_pc_tensor = torch.FloatTensor(train_pc).to(self.device)
        train_kp_tensor = torch.FloatTensor(train_kp).to(self.device)
        test_pc_tensor = torch.FloatTensor(test_pc).to(self.device)
        test_kp_tensor = torch.FloatTensor(test_kp).to(self.device)
        
        # 数据加载器
        batch_size = max(2, min(8, len(train_pc) // 4))
        train_dataset = TensorDataset(train_pc_tensor, train_kp_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        
        # 训练
        model.train()
        best_loss = float('inf')
        patience = 0
        
        print(f"    开始训练 {model_name}...")
        
        for epoch in range(60):  # 适中的训练轮数
            epoch_loss = 0.0
            
            for batch_pc, batch_kp in train_loader:
                optimizer.zero_grad()
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                loss.backward()
                optimizer.step()
                epoch_loss += loss.item()
            
            avg_loss = epoch_loss / len(train_loader)
            scheduler.step(avg_loss)
            
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience = 0
                best_model_state = model.state_dict().copy()
            else:
                patience += 1
                if patience >= 12:
                    print(f"    早停于epoch {epoch+1}")
                    break
            
            if (epoch + 1) % 15 == 0:
                print(f"    Epoch {epoch+1}: Loss {avg_loss:.6f}")
        
        # 评估 - 使用完全相同的方法
        model.load_state_dict(best_model_state)
        model.eval()
        
        with torch.no_grad():
            predicted = model(test_pc_tensor)
            test_errors = torch.norm(predicted - test_kp_tensor, dim=2)  # [batch, num_keypoints]
            avg_error = torch.mean(test_errors).item()
            
            # 关键：使用相同的评估方法
            sample_errors = torch.mean(test_errors, dim=1)  # 每个样本的平均误差
            errors_5mm = torch.sum(sample_errors <= 5.0).item()
            errors_10mm = torch.sum(sample_errors <= 10.0).item()
            
            acc_5mm = (errors_5mm / len(test_pc)) * 100
            acc_10mm = (errors_10mm / len(test_pc)) * 100
        
        return {
            'avg_error': avg_error,
            'accuracy_5mm': acc_5mm,
            'accuracy_10mm': acc_10mm,
            'medical_grade': avg_error <= 10.0,
            'excellent_grade': avg_error <= 5.0,
            'parameters': sum(p.numel() for p in model.parameters()),
            'epochs_trained': epoch + 1
        }
    
    def run_exact_benchmark(self):
        """运行精确复现基准测试"""
        
        print("\n🚀 开始精确复现基准测试...")
        print("使用与您完全相同的训练方法和数据集")
        print("=" * 80)
        
        # 加载数据
        if not self.load_original_data():
            return
        
        for num_keypoints in self.keypoint_configs:
            print(f"\n📊 测试 {num_keypoints} 关键点配置")
            print("-" * 50)
            
            # 生成关键点配置
            keypoints = self.expand_keypoints(self.base_keypoints, num_keypoints)
            
            # 数据划分
            train_pc, test_pc, train_kp, test_kp = train_test_split(
                self.point_clouds, keypoints, test_size=0.2, random_state=42
            )
            
            print(f"   训练集: {len(train_pc)} 样本")
            print(f"   测试集: {len(test_pc)} 样本")
            
            for model_name, model_class in self.models.items():
                print(f"\n🔄 训练 {model_name}...")
                
                try:
                    # 创建模型
                    model = model_class(num_points=50000, num_keypoints=num_keypoints)
                    model = model.to(self.device)
                    
                    print(f"   参数数量: {sum(p.numel() for p in model.parameters())/1e6:.2f}M")
                    
                    # 训练和评估
                    start_time = time.time()
                    result = self.train_and_evaluate_exact(
                        model, train_pc, train_kp, test_pc, test_kp, 
                        f"{num_keypoints}kp_{model_name}"
                    )
                    training_time = time.time() - start_time
                    
                    # 添加额外信息
                    result.update({
                        'model_name': model_name,
                        'keypoints': num_keypoints,
                        'training_time': training_time
                    })
                    
                    self.results.append(result)
                    
                    print(f"   ✅ 完成: {result['avg_error']:.2f}mm, 训练时间: {training_time:.1f}s")
                    print(f"   📊 医疗级达标率: {result['accuracy_10mm']:.1f}%")
                    print(f"   📊 优秀级达标率: {result['accuracy_5mm']:.1f}%")
                    
                except Exception as e:
                    print(f"   ❌ 失败: {e}")
        
        # 保存结果
        self.save_results()
        
        print(f"\n✅ 精确复现基准测试完成！")
        return self.results
    
    def save_results(self):
        """保存结果"""
        
        # 保存为CSV
        df = pd.DataFrame(self.results)
        df.to_csv('exact_reproduction_benchmark_results.csv', index=False)
        
        # 保存为JSON
        with open('exact_reproduction_benchmark_results.json', 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        print("💾 精确复现基准测试结果已保存:")
        print("   📊 exact_reproduction_benchmark_results.csv")
        print("   📄 exact_reproduction_benchmark_results.json")

if __name__ == "__main__":
    print("🧪 精确复现基准测试")
    print("使用与您完全相同的训练代码和数据集")
    print("=" * 80)
    
    # 创建基准测试框架
    benchmark = ExactReproductionBenchmark()
    
    # 运行精确复现基准测试
    results = benchmark.run_exact_benchmark()
    
    if results:
        print(f"\n📋 精确复现基准测试总结:")
        print(f"   🔬 测试模型: {len(set(r['model_name'] for r in results))} 个")
        print(f"   📊 关键点配置: {len(set(r['keypoints'] for r in results))} 种")
        print(f"   📈 总实验数: {len(results)} 个")
        
        # 显示最佳结果
        best_result = min(results, key=lambda x: x['avg_error'])
        print(f"\n🏆 最佳结果:")
        print(f"   📊 模型: {best_result['model_name']}")
        print(f"   📊 关键点: {best_result['keypoints']}")
        print(f"   📊 平均误差: {best_result['avg_error']:.2f}mm")
        print(f"   📊 医疗级达标率: {best_result['accuracy_10mm']:.1f}%")
        
        # 对比您的方法和主流方法
        our_results = [r for r in results if 'Adaptive' in r['model_name']]
        baseline_results = [r for r in results if 'Baseline' in r['model_name']]
        
        if our_results and baseline_results:
            our_avg = np.mean([r['avg_error'] for r in our_results])
            baseline_avg = np.mean([r['avg_error'] for r in baseline_results])
            
            print(f"\n📊 方法对比:")
            print(f"   🔬 我们的自适应方法: {our_avg:.2f}mm")
            print(f"   🔬 主流基线方法: {baseline_avg:.2f}mm")
            print(f"   📈 性能提升: {((baseline_avg - our_avg) / baseline_avg * 100):.1f}%")
    
    print(f"\n💡 这个基准测试使用了:")
    print(f"   • 与您完全相同的训练代码")
    print(f"   • 相同的数据集和数据划分")
    print(f"   • 相同的评估方法")
    print(f"   • 主流模型的公平对比")
