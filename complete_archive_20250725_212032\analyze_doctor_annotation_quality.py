#!/usr/bin/env python3
"""
医生标注质量分析：评估标注的一致性和准确性
Doctor Annotation Quality Analysis: Evaluating annotation consistency and accuracy
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.cluster import DBSCAN
import os
from tqdm import tqdm

def load_clean_data():
    """加载清洁数据"""
    
    print("📊 加载数据进行标注质量分析...")
    
    # 加载女性原始数据
    female_original_path = "archive/old_experiments/f3_reduced_12kp_female.npz"
    if os.path.exists(female_original_path):
        female_data = np.load(female_original_path, allow_pickle=True)
        female_pc = female_data['point_clouds']
        female_kp = female_data['keypoints']
        print(f"✅ 女性原始数据: {len(female_pc)}个样本")
    else:
        print(f"❌ 女性原始数据不存在")
        return None
    
    # 加载男性原始数据
    male_original_path = "archive/old_experiments/f3_reduced_12kp_male.npz"
    if os.path.exists(male_original_path):
        male_data = np.load(male_original_path, allow_pickle=True)
        male_pc = male_data['point_clouds']
        male_kp = male_data['keypoints']
        print(f"✅ 男性原始数据: {len(male_pc)}个样本")
    else:
        print(f"❌ 男性原始数据不存在")
        return None
    
    return female_pc, female_kp, male_pc, male_kp

def analyze_keypoint_to_surface_distance(point_clouds, keypoints, gender_name):
    """分析关键点到表面的距离 - 评估标注准确性"""
    
    print(f"\n🎯 分析{gender_name}关键点到表面距离")
    print("=" * 60)
    
    all_distances = []
    sample_distances = []
    
    for i in range(len(point_clouds)):
        pc = point_clouds[i]
        kp = keypoints[i]
        
        sample_dist = []
        for j, keypoint in enumerate(kp):
            # 计算关键点到所有点云点的距离
            distances_to_pc = np.linalg.norm(pc - keypoint, axis=1)
            min_distance = np.min(distances_to_pc)
            
            all_distances.append(min_distance)
            sample_dist.append(min_distance)
        
        sample_distances.append(sample_dist)
    
    all_distances = np.array(all_distances)
    sample_distances = np.array(sample_distances)
    
    # 统计分析
    mean_dist = np.mean(all_distances)
    std_dist = np.std(all_distances)
    median_dist = np.median(all_distances)
    q75_dist = np.percentile(all_distances, 75)
    q95_dist = np.percentile(all_distances, 95)
    max_dist = np.max(all_distances)
    
    print(f"📏 {gender_name}关键点到表面距离统计:")
    print(f"   平均距离: {mean_dist:.3f}mm")
    print(f"   标准差: {std_dist:.3f}mm")
    print(f"   中位数: {median_dist:.3f}mm")
    print(f"   75%分位数: {q75_dist:.3f}mm")
    print(f"   95%分位数: {q95_dist:.3f}mm")
    print(f"   最大距离: {max_dist:.3f}mm")
    
    # 质量评估
    excellent_ratio = np.sum(all_distances <= 0.5) / len(all_distances)
    good_ratio = np.sum(all_distances <= 1.0) / len(all_distances)
    acceptable_ratio = np.sum(all_distances <= 2.0) / len(all_distances)
    poor_ratio = np.sum(all_distances > 5.0) / len(all_distances)
    
    print(f"\n📊 {gender_name}标注质量分布:")
    print(f"   优秀(≤0.5mm): {excellent_ratio*100:.1f}%")
    print(f"   良好(≤1.0mm): {good_ratio*100:.1f}%")
    print(f"   可接受(≤2.0mm): {acceptable_ratio*100:.1f}%")
    print(f"   较差(>5.0mm): {poor_ratio*100:.1f}%")
    
    # 按关键点分析
    print(f"\n🔍 {gender_name}各关键点标注质量:")
    keypoint_stats = []
    for kp_idx in range(12):
        kp_distances = sample_distances[:, kp_idx]
        kp_mean = np.mean(kp_distances)
        kp_std = np.std(kp_distances)
        kp_max = np.max(kp_distances)
        
        # 确定关键点所属区域
        if kp_idx < 4:
            region = "F1"
            local_idx = kp_idx + 1
        elif kp_idx < 8:
            region = "F2"
            local_idx = kp_idx - 3
        else:
            region = "F3"
            local_idx = kp_idx - 7
        
        print(f"   {region}-{local_idx}: {kp_mean:.3f}±{kp_std:.3f}mm (最大:{kp_max:.3f}mm)")
        
        keypoint_stats.append({
            'region': region,
            'local_idx': local_idx,
            'mean': kp_mean,
            'std': kp_std,
            'max': kp_max
        })
    
    return {
        'all_distances': all_distances,
        'sample_distances': sample_distances,
        'stats': {
            'mean': mean_dist,
            'std': std_dist,
            'median': median_dist,
            'q75': q75_dist,
            'q95': q95_dist,
            'max': max_dist
        },
        'quality_ratios': {
            'excellent': excellent_ratio,
            'good': good_ratio,
            'acceptable': acceptable_ratio,
            'poor': poor_ratio
        },
        'keypoint_stats': keypoint_stats
    }

def analyze_annotation_consistency(keypoints, gender_name):
    """分析标注一致性 - 评估不同样本间的标注变异性"""
    
    print(f"\n🔄 分析{gender_name}标注一致性")
    print("=" * 60)
    
    # 计算每个关键点在不同样本间的变异性
    keypoint_variations = []
    
    for kp_idx in range(12):
        # 提取所有样本的第kp_idx个关键点
        kp_coords = keypoints[:, kp_idx, :]  # [n_samples, 3]
        
        # 计算质心
        centroid = np.mean(kp_coords, axis=0)
        
        # 计算每个样本到质心的距离
        distances_to_centroid = np.linalg.norm(kp_coords - centroid, axis=1)
        
        # 统计变异性
        variation = np.std(distances_to_centroid)
        max_deviation = np.max(distances_to_centroid)
        
        keypoint_variations.append({
            'kp_idx': kp_idx,
            'variation': variation,
            'max_deviation': max_deviation,
            'centroid': centroid
        })
        
        # 确定关键点所属区域
        if kp_idx < 4:
            region = "F1"
            local_idx = kp_idx + 1
        elif kp_idx < 8:
            region = "F2"
            local_idx = kp_idx - 3
        else:
            region = "F3"
            local_idx = kp_idx - 7
        
        print(f"   {region}-{local_idx}: 变异性 {variation:.2f}mm, 最大偏差 {max_deviation:.2f}mm")
    
    # 整体一致性评估
    all_variations = [kv['variation'] for kv in keypoint_variations]
    mean_variation = np.mean(all_variations)
    max_variation = np.max(all_variations)
    
    print(f"\n📊 {gender_name}整体标注一致性:")
    print(f"   平均变异性: {mean_variation:.2f}mm")
    print(f"   最大变异性: {max_variation:.2f}mm")
    
    # 一致性等级评估
    if mean_variation < 5.0:
        consistency_level = "优秀"
    elif mean_variation < 10.0:
        consistency_level = "良好"
    elif mean_variation < 15.0:
        consistency_level = "可接受"
    else:
        consistency_level = "较差"
    
    print(f"   一致性等级: {consistency_level}")
    
    return {
        'keypoint_variations': keypoint_variations,
        'mean_variation': mean_variation,
        'max_variation': max_variation,
        'consistency_level': consistency_level
    }

def analyze_anatomical_constraints(keypoints, gender_name):
    """分析解剖学约束的遵守情况"""
    
    print(f"\n🏥 分析{gender_name}解剖学约束遵守情况")
    print("=" * 60)
    
    constraint_violations = []
    
    # F1-F2对称性约束
    print(f"🔍 F1-F2对称性分析:")
    for i in range(4):
        f1_idx = i
        f2_idx = i + 4
        
        # 计算F1和F2对应点的距离
        f1_f2_distances = []
        for sample_idx in range(len(keypoints)):
            f1_point = keypoints[sample_idx, f1_idx]
            f2_point = keypoints[sample_idx, f2_idx]
            distance = np.linalg.norm(f1_point - f2_point)
            f1_f2_distances.append(distance)
        
        mean_distance = np.mean(f1_f2_distances)
        std_distance = np.std(f1_f2_distances)
        cv = std_distance / mean_distance if mean_distance > 0 else 0
        
        print(f"   F1-{i+1} <-> F2-{i+1}: {mean_distance:.2f}±{std_distance:.2f}mm (CV:{cv:.3f})")
        
        # 检查对称性违反
        if cv > 0.2:  # 变异系数过大
            constraint_violations.append(f"F1-{i+1}/F2-{i+1}对称性变异过大")
    
    # 区域内距离约束
    print(f"\n🔍 区域内距离一致性:")
    regions = [
        ("F1", list(range(0, 4))),
        ("F2", list(range(4, 8))),
        ("F3", list(range(8, 12)))
    ]
    
    for region_name, indices in regions:
        # 计算区域内相邻点的距离
        adjacent_distances = []
        for i in range(len(indices) - 1):
            idx1, idx2 = indices[i], indices[i+1]
            
            distances = []
            for sample_idx in range(len(keypoints)):
                p1 = keypoints[sample_idx, idx1]
                p2 = keypoints[sample_idx, idx2]
                distance = np.linalg.norm(p1 - p2)
                distances.append(distance)
            
            mean_dist = np.mean(distances)
            std_dist = np.std(distances)
            cv = std_dist / mean_dist if mean_dist > 0 else 0
            
            print(f"   {region_name}-{i+1}到{region_name}-{i+2}: {mean_dist:.2f}±{std_dist:.2f}mm (CV:{cv:.3f})")
            
            if cv > 0.3:  # 变异系数过大
                constraint_violations.append(f"{region_name}区域内距离变异过大")
    
    # 跨区域距离约束
    print(f"\n🔍 跨区域距离分析:")
    cross_region_pairs = [
        ("F1-1", 0, "F3-1", 8),
        ("F2-1", 4, "F3-1", 8),
        ("F1-1", 0, "F2-1", 4)
    ]
    
    for name1, idx1, name2, idx2 in cross_region_pairs:
        distances = []
        for sample_idx in range(len(keypoints)):
            p1 = keypoints[sample_idx, idx1]
            p2 = keypoints[sample_idx, idx2]
            distance = np.linalg.norm(p1 - p2)
            distances.append(distance)
        
        mean_dist = np.mean(distances)
        std_dist = np.std(distances)
        cv = std_dist / mean_dist if mean_dist > 0 else 0
        
        print(f"   {name1} <-> {name2}: {mean_dist:.2f}±{std_dist:.2f}mm (CV:{cv:.3f})")
        
        if cv > 0.25:
            constraint_violations.append(f"{name1}-{name2}距离变异过大")
    
    print(f"\n⚠️ 约束违反情况:")
    if constraint_violations:
        for violation in constraint_violations:
            print(f"   - {violation}")
    else:
        print("   ✅ 所有解剖学约束都得到良好遵守")
    
    return {
        'violations': constraint_violations,
        'violation_count': len(constraint_violations)
    }

def detect_annotation_outliers(point_clouds, keypoints, gender_name):
    """检测标注异常值"""
    
    print(f"\n🚨 检测{gender_name}标注异常值")
    print("=" * 60)
    
    outlier_samples = []
    
    # 方法1: 基于关键点到表面距离的异常检测
    surface_distances = []
    for i in range(len(point_clouds)):
        pc = point_clouds[i]
        kp = keypoints[i]
        
        sample_distances = []
        for keypoint in kp:
            min_dist = np.min(np.linalg.norm(pc - keypoint, axis=1))
            sample_distances.append(min_dist)
        
        avg_distance = np.mean(sample_distances)
        surface_distances.append(avg_distance)
    
    surface_distances = np.array(surface_distances)
    
    # 使用Z-score检测异常值
    z_scores = np.abs(stats.zscore(surface_distances))
    surface_outliers = np.where(z_scores > 2.5)[0]
    
    print(f"📊 基于表面距离的异常检测:")
    print(f"   平均表面距离: {np.mean(surface_distances):.3f}±{np.std(surface_distances):.3f}mm")
    print(f"   异常样本数: {len(surface_outliers)}/{len(point_clouds)} ({len(surface_outliers)/len(point_clouds)*100:.1f}%)")
    
    # 方法2: 基于关键点分布的异常检测
    keypoint_outliers = []
    for kp_idx in range(12):
        kp_coords = keypoints[:, kp_idx, :]
        
        # 使用DBSCAN检测异常点
        clustering = DBSCAN(eps=10.0, min_samples=3).fit(kp_coords)
        outlier_mask = clustering.labels_ == -1
        outlier_indices = np.where(outlier_mask)[0]
        
        if len(outlier_indices) > 0:
            keypoint_outliers.extend(outlier_indices)
    
    keypoint_outliers = list(set(keypoint_outliers))
    
    print(f"📊 基于关键点分布的异常检测:")
    print(f"   异常样本数: {len(keypoint_outliers)}/{len(point_clouds)} ({len(keypoint_outliers)/len(point_clouds)*100:.1f}%)")
    
    # 综合异常样本
    all_outliers = list(set(surface_outliers.tolist() + keypoint_outliers))
    
    print(f"📊 综合异常检测结果:")
    print(f"   总异常样本数: {len(all_outliers)}/{len(point_clouds)} ({len(all_outliers)/len(point_clouds)*100:.1f}%)")
    
    if len(all_outliers) > 0:
        print(f"   异常样本索引: {all_outliers[:10]}{'...' if len(all_outliers) > 10 else ''}")
    
    return {
        'surface_outliers': surface_outliers.tolist(),
        'keypoint_outliers': keypoint_outliers,
        'all_outliers': all_outliers,
        'surface_distances': surface_distances
    }

def generate_annotation_quality_report(female_results, male_results):
    """生成标注质量综合报告"""
    
    print(f"\n" + "="*80)
    print("📋 医生标注质量综合报告")
    print("="*80)
    
    # 表面距离质量对比
    print(f"\n🎯 关键点到表面距离对比:")
    print(f"   女性平均距离: {female_results['surface']['stats']['mean']:.3f}mm")
    print(f"   男性平均距离: {male_results['surface']['stats']['mean']:.3f}mm")
    
    # 标注一致性对比
    print(f"\n🔄 标注一致性对比:")
    print(f"   女性平均变异性: {female_results['consistency']['mean_variation']:.2f}mm")
    print(f"   男性平均变异性: {male_results['consistency']['mean_variation']:.2f}mm")
    print(f"   女性一致性等级: {female_results['consistency']['consistency_level']}")
    print(f"   男性一致性等级: {male_results['consistency']['consistency_level']}")
    
    # 异常值检测对比
    print(f"\n🚨 异常值检测对比:")
    female_outlier_rate = len(female_results['outliers']['all_outliers']) / 25 * 100
    male_outlier_rate = len(male_results['outliers']['all_outliers']) / 72 * 100
    print(f"   女性异常率: {female_outlier_rate:.1f}%")
    print(f"   男性异常率: {male_outlier_rate:.1f}%")
    
    # 整体质量评估
    print(f"\n📊 整体标注质量评估:")
    
    # 女性质量评估
    female_quality_score = 100
    female_quality_score -= female_results['surface']['stats']['mean'] * 10  # 表面距离扣分
    female_quality_score -= female_results['consistency']['mean_variation'] * 2  # 一致性扣分
    female_quality_score -= female_outlier_rate * 1.5  # 异常率扣分
    female_quality_score = max(0, female_quality_score)
    
    # 男性质量评估
    male_quality_score = 100
    male_quality_score -= male_results['surface']['stats']['mean'] * 10
    male_quality_score -= male_results['consistency']['mean_variation'] * 2
    male_quality_score -= male_outlier_rate * 1.5
    male_quality_score = max(0, male_quality_score)
    
    print(f"   女性标注质量分数: {female_quality_score:.1f}/100")
    print(f"   男性标注质量分数: {male_quality_score:.1f}/100")
    
    # 质量等级
    def get_quality_level(score):
        if score >= 80:
            return "优秀"
        elif score >= 70:
            return "良好"
        elif score >= 60:
            return "可接受"
        else:
            return "需要改进"
    
    print(f"   女性标注质量等级: {get_quality_level(female_quality_score)}")
    print(f"   男性标注质量等级: {get_quality_level(male_quality_score)}")
    
    # 改进建议
    print(f"\n💡 标注质量改进建议:")
    
    if female_results['surface']['stats']['mean'] > 1.0 or male_results['surface']['stats']['mean'] > 1.0:
        print("   🔧 关键点到表面距离过大，建议:")
        print("      - 使用更高分辨率的点云数据")
        print("      - 改进标注工具的精度")
        print("      - 增加标注时的放大倍数")
    
    if female_results['consistency']['mean_variation'] > 10.0 or male_results['consistency']['mean_variation'] > 10.0:
        print("   🔧 标注一致性有待提高，建议:")
        print("      - 制定更详细的标注指南")
        print("      - 增加标注者培训")
        print("      - 实施多人标注和一致性检查")
    
    if female_outlier_rate > 15.0 or male_outlier_rate > 15.0:
        print("   🔧 异常值比例较高，建议:")
        print("      - 重新检查异常样本的标注")
        print("      - 建立标注质量控制流程")
        print("      - 考虑移除质量过差的样本")
    
    return {
        'female_quality_score': female_quality_score,
        'male_quality_score': male_quality_score,
        'overall_quality': (female_quality_score + male_quality_score) / 2
    }

def main():
    """主函数：医生标注质量分析"""
    
    print("🏥 医生标注质量分析")
    print("🎯 目标: 评估标注的准确性和一致性，排除点云变换的影响")
    print("🔧 方法: 关键点到表面距离 + 标注一致性 + 解剖学约束 + 异常值检测")
    print("=" * 80)
    
    # 加载数据
    data_result = load_clean_data()
    if data_result is None:
        return
    
    female_pc, female_kp, male_pc, male_kp = data_result
    
    results = {}
    
    # 分析女性数据
    print(f"\n👩 女性数据标注质量分析")
    print("="*80)
    
    female_surface = analyze_keypoint_to_surface_distance(female_pc, female_kp, "女性")
    female_consistency = analyze_annotation_consistency(female_kp, "女性")
    female_constraints = analyze_anatomical_constraints(female_kp, "女性")
    female_outliers = detect_annotation_outliers(female_pc, female_kp, "女性")
    
    results['female'] = {
        'surface': female_surface,
        'consistency': female_consistency,
        'constraints': female_constraints,
        'outliers': female_outliers
    }
    
    # 分析男性数据
    print(f"\n👨 男性数据标注质量分析")
    print("="*80)
    
    male_surface = analyze_keypoint_to_surface_distance(male_pc, male_kp, "男性")
    male_consistency = analyze_annotation_consistency(male_kp, "男性")
    male_constraints = analyze_anatomical_constraints(male_kp, "男性")
    male_outliers = detect_annotation_outliers(male_pc, male_kp, "男性")
    
    results['male'] = {
        'surface': male_surface,
        'consistency': male_consistency,
        'constraints': male_constraints,
        'outliers': male_outliers
    }
    
    # 生成综合报告
    quality_report = generate_annotation_quality_report(results['female'], results['male'])
    
    # 保存结果
    np.save('doctor_annotation_quality_analysis.npy', results)
    print(f"\n💾 分析结果已保存到 doctor_annotation_quality_analysis.npy")
    
    return results

if __name__ == "__main__":
    main()
