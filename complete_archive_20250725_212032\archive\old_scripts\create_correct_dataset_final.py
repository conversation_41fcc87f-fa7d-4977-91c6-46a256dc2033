#!/usr/bin/env python3
"""
Create Correct Dataset - Final Version

Use complete STL reading but with careful memory management and batch processing.
"""

import numpy as np
import pandas as pd
from pathlib import Path
import json
import struct
import gc
import os

def load_annotation_file(csv_path: str):
    """Load annotation CSV file with proper encoding"""
    try:
        df = pd.read_csv(csv_path, encoding='gbk')
    except:
        try:
            df = pd.read_csv(csv_path, encoding='utf-8')
        except:
            df = pd.read_csv(csv_path, encoding='latin-1')
    
    keypoints = df[['X', 'Y', 'Z']].values
    labels = df['label'].values.tolist()
    
    return keypoints, labels

def read_stl_binary_complete_safe(stl_path: str):
    """Read complete STL file with memory safety"""
    try:
        with open(stl_path, 'rb') as f:
            f.read(80)  # Skip header
            num_triangles = struct.unpack('<I', f.read(4))[0]
            
            # Read in chunks to manage memory
            chunk_size = 10000
            all_vertices = []
            
            for chunk_start in range(0, num_triangles, chunk_size):
                chunk_end = min(chunk_start + chunk_size, num_triangles)
                chunk_vertices = []
                
                for i in range(chunk_start, chunk_end):
                    f.read(12)  # Skip normal
                    for j in range(3):
                        x, y, z = struct.unpack('<fff', f.read(12))
                        chunk_vertices.append([x, y, z])
                    f.read(2)  # Skip attribute
                
                all_vertices.extend(chunk_vertices)
                del chunk_vertices
                gc.collect()
            
            vertices = np.array(all_vertices)
            del all_vertices
            gc.collect()
            
            # Remove duplicates to reduce memory
            unique_vertices = np.unique(vertices, axis=0)
            del vertices
            gc.collect()
            
            return unique_vertices
            
    except Exception as e:
        print(f"STL读取失败: {e}")
        return None

def separate_keypoints_by_region(keypoints, labels):
    """Separate keypoints by F1/F2/F3 regions based on labels"""
    
    f1_keypoints = []
    f2_keypoints = []
    f3_keypoints = []
    
    for i, label in enumerate(labels):
        if isinstance(label, str):
            if label.startswith('F_1') or label.startswith('F1'):
                f1_keypoints.append(keypoints[i])
            elif label.startswith('F_2') or label.startswith('F2'):
                f2_keypoints.append(keypoints[i])
            elif label.startswith('F_3') or label.startswith('F3'):
                f3_keypoints.append(keypoints[i])
    
    return {
        'F1': np.array(f1_keypoints) if f1_keypoints else np.array([]),
        'F2': np.array(f2_keypoints) if f2_keypoints else np.array([]),
        'F3': np.array(f3_keypoints) if f3_keypoints else np.array([])
    }

def process_single_sample_complete(sample_id):
    """Process single sample with complete STL reading"""
    
    print(f"🔧 处理样本 {sample_id}")
    
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    annotations_dir = data_dir / "annotations"
    stl_dir = data_dir / "stl_models"
    
    # Load annotation
    csv_file = annotations_dir / f"{sample_id}-Table-XYZ.CSV"
    
    if not csv_file.exists():
        print(f"   ❌ 标注文件不存在")
        return None
    
    try:
        keypoints, labels = load_annotation_file(str(csv_file))
        print(f"   ✅ 加载了 {len(keypoints)} 个关键点")
    except Exception as e:
        print(f"   ❌ 标注加载失败: {e}")
        return None
    
    # Separate keypoints
    regions = separate_keypoints_by_region(keypoints, labels)
    
    # Process F3 region (most reliable)
    f3_stl_file = stl_dir / f"{sample_id}-F_3.stl"
    
    if not f3_stl_file.exists():
        print(f"   ❌ F3 STL文件不存在")
        return None
    
    if len(regions['F3']) == 0:
        print(f"   ❌ F3关键点为空")
        return None
    
    # Read complete F3 STL
    print(f"   📖 读取完整F3 STL文件...")
    f3_vertices = read_stl_binary_complete_safe(str(f3_stl_file))
    
    if f3_vertices is None:
        print(f"   ❌ F3 STL读取失败")
        return None
    
    print(f"   ✅ F3 STL: {len(f3_vertices)} 个唯一顶点")
    
    # Validate alignment with complete STL
    f3_keypoints = regions['F3']
    distances = []
    
    print(f"   🎯 计算对齐质量...")
    for i, kp in enumerate(f3_keypoints):
        dists = np.linalg.norm(f3_vertices - kp, axis=1)
        min_dist = np.min(dists)
        distances.append(min_dist)
        
        if i % 5 == 0:  # Progress indicator
            print(f"      进度: {i+1}/{len(f3_keypoints)}")
    
    distances = np.array(distances)
    mean_dist = np.mean(distances)
    within_1mm = np.sum(distances <= 1.0) / len(distances) * 100
    within_5mm = np.sum(distances <= 5.0) / len(distances) * 100
    
    print(f"   📊 F3对齐质量:")
    print(f"      平均距离: {mean_dist:.2f}mm")
    print(f"      ≤1mm: {within_1mm:.1f}%")
    print(f"      ≤5mm: {within_5mm:.1f}%")
    
    # Sample point cloud for training (reduce memory)
    target_points = 4096
    if len(f3_vertices) > target_points:
        indices = np.random.choice(len(f3_vertices), target_points, replace=False)
        sampled_vertices = f3_vertices[indices].copy()
    else:
        # Duplicate if not enough points
        indices = np.random.choice(len(f3_vertices), target_points, replace=True)
        sampled_vertices = f3_vertices[indices].copy()
    
    # Simple centering (preserve scale for medical accuracy)
    kp_center = np.mean(f3_keypoints, axis=0)
    centered_pc = sampled_vertices - kp_center
    centered_kps = f3_keypoints - kp_center
    
    result = {
        'sample_id': sample_id,
        'point_cloud': centered_pc.astype(np.float32),
        'keypoints': centered_kps.astype(np.float32),
        'center': kp_center.astype(np.float32),
        'alignment_quality': {
            'mean_distance': float(mean_dist),
            'within_1mm_percent': float(within_1mm),
            'within_5mm_percent': float(within_5mm)
        }
    }
    
    # Force cleanup
    del f3_vertices, sampled_vertices, distances
    del centered_pc, centered_kps
    gc.collect()
    
    print(f"   ✅ 处理成功")
    return result

def create_f3_dataset_complete():
    """Create F3 dataset with complete STL reading"""
    
    print("🏗️ **创建F3数据集 (完整STL版本)**")
    print("=" * 60)
    
    # Get available samples (limit to avoid memory issues)
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    annotations_dir = data_dir / "annotations"
    
    xyz_files = list(annotations_dir.glob("*-Table-XYZ.CSV"))
    excluded_samples = {'600025', '600026', '600027'}
    
    valid_sample_ids = []
    for csv_file in xyz_files:
        sample_id = csv_file.stem.split('-')[0]
        if sample_id not in excluded_samples:
            valid_sample_ids.append(sample_id)
    
    # Limit to first 10 samples for testing
    test_sample_ids = valid_sample_ids[:10]
    
    print(f"📂 测试样本: {test_sample_ids}")
    
    # Process samples one by one
    processed_samples = []
    
    for i, sample_id in enumerate(test_sample_ids):
        print(f"\n📦 处理样本 {i+1}/{len(test_sample_ids)}: {sample_id}")
        
        result = process_single_sample_complete(sample_id)
        
        if result is not None:
            processed_samples.append(result)
        
        # Force cleanup after each sample
        gc.collect()
    
    print(f"\n📊 **处理结果**")
    print(f"   成功样本: {len(processed_samples)}")
    
    if processed_samples:
        # Calculate quality statistics
        qualities = [s['alignment_quality'] for s in processed_samples]
        avg_dist = np.mean([q['mean_distance'] for q in qualities])
        avg_1mm = np.mean([q['within_1mm_percent'] for q in qualities])
        avg_5mm = np.mean([q['within_5mm_percent'] for q in qualities])
        
        print(f"\n📈 **质量统计**")
        print(f"   平均对齐距离: {avg_dist:.2f}mm")
        print(f"   平均1mm精度: {avg_1mm:.1f}%")
        print(f"   平均5mm精度: {avg_5mm:.1f}%")
        
        # Save dataset
        output_data = {
            'dataset_name': 'F3_Complete_STL_Dataset',
            'creation_date': str(pd.Timestamp.now()),
            'total_samples': len(processed_samples),
            'quality_stats': {
                'avg_distance_mm': float(avg_dist),
                'avg_1mm_percent': float(avg_1mm),
                'avg_5mm_percent': float(avg_5mm)
            },
            'samples': [
                {
                    'sample_id': s['sample_id'],
                    'alignment_quality': s['alignment_quality']
                }
                for s in processed_samples
            ]
        }
        
        # Save metadata
        with open('f3_complete_dataset_info.json', 'w') as f:
            json.dump(output_data, f, indent=2)
        
        # Save training data
        np.savez_compressed('f3_complete_dataset.npz',
                           sample_ids=[s['sample_id'] for s in processed_samples],
                           point_clouds=np.stack([s['point_cloud'] for s in processed_samples]),
                           keypoints=np.stack([s['keypoints'] for s in processed_samples]),
                           centers=np.stack([s['center'] for s in processed_samples]))
        
        print(f"   ✅ 数据已保存:")
        print(f"      📄 f3_complete_dataset_info.json")
        print(f"      📦 f3_complete_dataset.npz")
        
        return processed_samples
    
    return None

if __name__ == "__main__":
    # Create complete F3 dataset
    result = create_f3_dataset_complete()
    
    if result:
        print(f"\n🎉 **F3完整数据集创建成功!**")
        print(f"💡 使用了完整STL文件，应该有更准确的对齐质量")
        print(f"🎯 下一步: 使用此数据集训练模型验证性能")
    else:
        print(f"\n❌ **数据集创建失败**")
