#!/usr/bin/env python3
"""
人类视角 vs 机器视角
展示为什么人类觉得简单的任务对机器来说很困难
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def create_human_vs_machine_visualization():
    """创建人类vs机器视角的可视化"""
    
    # 加载一个真实的F3样本
    data = np.load('f3_19kp_preprocessed.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    sample_ids = data['sample_ids']
    
    # 选择一个样本
    sample_idx = 0
    pc = point_clouds[sample_idx]
    kp = keypoints[sample_idx]
    sample_id = sample_ids[sample_idx]
    
    # F3-13 (Z最高点) 和 F3-18 (Z最低点)
    f3_13_true = kp[12]  # Z最高点
    f3_18_true = kp[17]  # Z最低点
    
    # 找到点云中的实际最高点和最低点
    z_max_idx = np.argmax(pc[:, 2])
    z_min_idx = np.argmin(pc[:, 2])
    pc_z_max = pc[z_max_idx]
    pc_z_min = pc[z_min_idx]
    
    fig = plt.figure(figsize=(20, 16))
    
    # 1. 人类视角 - 全局形状理解
    ax1 = fig.add_subplot(3, 3, 1, projection='3d')
    
    # 采样显示
    if len(pc) > 3000:
        indices = np.random.choice(len(pc), 3000, replace=False)
        display_pc = pc[indices]
    else:
        display_pc = pc
    
    # 按Z坐标着色，突出显示高度信息
    colors = display_pc[:, 2]
    scatter = ax1.scatter(display_pc[:, 0], display_pc[:, 1], display_pc[:, 2], 
                         c=colors, cmap='viridis', s=20, alpha=0.6)
    
    # 标记真实的最高点和最低点
    ax1.scatter(f3_13_true[0], f3_13_true[1], f3_13_true[2], 
               c='red', s=200, marker='*', label='F3-13 (True Z-Max)')
    ax1.scatter(f3_18_true[0], f3_18_true[1], f3_18_true[2], 
               c='blue', s=200, marker='v', label='F3-18 (True Z-Min)')
    
    # 标记点云的实际极值
    ax1.scatter(pc_z_max[0], pc_z_max[1], pc_z_max[2], 
               c='orange', s=150, marker='o', label='PC Z-Max')
    ax1.scatter(pc_z_min[0], pc_z_min[1], pc_z_min[2], 
               c='cyan', s=150, marker='s', label='PC Z-Min')
    
    ax1.set_title('Human Perspective\n"Obviously the highest/lowest points!"')
    ax1.legend()
    plt.colorbar(scatter, ax=ax1, shrink=0.5, label='Z coordinate')
    
    # 2. 机器视角 - 局部邻域
    ax2 = fig.add_subplot(3, 3, 2, projection='3d')
    
    # 模拟小感受野 - 只看F3-13周围5mm的点
    distances_13 = np.linalg.norm(pc - f3_13_true, axis=1)
    local_mask_13 = distances_13 <= 5.0
    local_pc_13 = pc[local_mask_13]
    
    ax2.scatter(local_pc_13[:, 0], local_pc_13[:, 1], local_pc_13[:, 2], 
               c='gray', s=30, alpha=0.8)
    ax2.scatter(f3_13_true[0], f3_13_true[1], f3_13_true[2], 
               c='red', s=200, marker='*')
    
    ax2.set_title('Machine Perspective (Small RF)\n"Just some local points..."')
    ax2.set_xlim(f3_13_true[0]-10, f3_13_true[0]+10)
    ax2.set_ylim(f3_13_true[1]-10, f3_13_true[1]+10)
    ax2.set_zlim(f3_13_true[2]-10, f3_13_true[2]+10)
    
    # 3. 机器视角 - 大感受野
    ax3 = fig.add_subplot(3, 3, 3, projection='3d')
    
    # 模拟大感受野 - 看F3-13周围15mm的点
    local_mask_13_large = distances_13 <= 15.0
    local_pc_13_large = pc[local_mask_13_large]
    
    colors_large = local_pc_13_large[:, 2]
    ax3.scatter(local_pc_13_large[:, 0], local_pc_13_large[:, 1], local_pc_13_large[:, 2], 
               c=colors_large, cmap='viridis', s=20, alpha=0.7)
    ax3.scatter(f3_13_true[0], f3_13_true[1], f3_13_true[2], 
               c='red', s=200, marker='*')
    
    ax3.set_title('Machine Perspective (Large RF)\n"More context, but still local"')
    
    # 4. 人类的形状理解
    ax4 = fig.add_subplot(3, 3, 4)
    
    # 2D投影显示骶骨形状
    ax4.scatter(display_pc[:, 0], display_pc[:, 2], c=colors, cmap='viridis', s=10, alpha=0.6)
    ax4.scatter(f3_13_true[0], f3_13_true[2], c='red', s=100, marker='*', label='Highest')
    ax4.scatter(f3_18_true[0], f3_18_true[2], c='blue', s=100, marker='v', label='Lowest')
    
    # 画出骶骨的大致轮廓
    hull_points = []
    for angle in np.linspace(0, 2*np.pi, 20):
        direction = np.array([np.cos(angle), np.sin(angle)])
        # 找到这个方向上最远的点
        projections = display_pc[:, [0, 2]] @ direction
        max_idx = np.argmax(projections)
        hull_points.append(display_pc[max_idx, [0, 2]])
    
    hull_points = np.array(hull_points)
    hull_points = hull_points[np.argsort(np.arctan2(hull_points[:, 1] - np.mean(hull_points[:, 1]), 
                                                   hull_points[:, 0] - np.mean(hull_points[:, 0])))]
    
    # 闭合轮廓
    hull_points = np.vstack([hull_points, hull_points[0]])
    ax4.plot(hull_points[:, 0], hull_points[:, 1], 'r--', alpha=0.7, linewidth=2, label='Shape outline')
    
    ax4.set_xlabel('X')
    ax4.set_ylabel('Z')
    ax4.set_title('Human Shape Understanding\n"Triangular/trapezoidal sacrum"')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    ax4.axis('equal')
    
    # 5. 机器的困惑
    ax5 = fig.add_subplot(3, 3, 5)
    ax5.axis('off')
    
    confusion_text = """
Machine's Confusion:

❓ What is "highest"?
• Just the point with max Z?
• Or the anatomically highest?
• What if there are multiple peaks?

❓ Local vs Global:
• Small RF: Can't see the big picture
• Large RF: Too much noise
• No shape understanding

❓ Training Issues:
• Limited samples (20)
• Inconsistent annotations
• No anatomical constraints

❓ Point Cloud Sampling:
• What if the true max point 
  is not sampled?
• Discrete vs continuous space

Human Advantages:
✓ Global shape perception
✓ Anatomical knowledge
✓ Relative comparison
✓ Context understanding
"""
    
    ax5.text(0.05, 0.95, confusion_text, transform=ax5.transAxes, fontsize=10,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    
    # 6. 距离分析
    ax6 = fig.add_subplot(3, 3, 6)
    
    # 分析F3-13标注点与实际最高点的距离
    distance_to_max = np.linalg.norm(f3_13_true - pc_z_max)
    distance_to_min = np.linalg.norm(f3_18_true - pc_z_min)
    
    # 分析Z坐标差异
    z_diff_max = f3_13_true[2] - pc_z_max[2]
    z_diff_min = f3_18_true[2] - pc_z_min[2]
    
    categories = ['F3-13\nvs PC Z-Max', 'F3-18\nvs PC Z-Min']
    distances = [distance_to_max, distance_to_min]
    z_diffs = [abs(z_diff_max), abs(z_diff_min)]
    
    x = np.arange(len(categories))
    width = 0.35
    
    bars1 = ax6.bar(x - width/2, distances, width, label='3D Distance (mm)', alpha=0.7)
    bars2 = ax6.bar(x + width/2, z_diffs, width, label='Z Difference (mm)', alpha=0.7)
    
    ax6.set_ylabel('Distance (mm)')
    ax6.set_title('Annotation vs Point Cloud Extremes')
    ax6.set_xticks(x)
    ax6.set_xticklabels(categories)
    ax6.legend()
    ax6.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, dist in zip(bars1, distances):
        ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                f'{dist:.1f}mm', ha='center', va='bottom')
    
    for bar, diff in zip(bars2, z_diffs):
        ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                f'{diff:.1f}mm', ha='center', va='bottom')
    
    # 7. 解决方案对比
    ax7 = fig.add_subplot(3, 3, 7)
    ax7.axis('off')
    
    solutions_text = """
Solution Approaches:

🤖 Pure Machine Learning:
• Large receptive field
• Attention mechanisms
• Multi-scale features
❌ Complex, overfitting risk

🧠 Human-Inspired:
• Geometric constraints
• Anatomical priors
• Shape understanding
✓ Simple, interpretable

🤝 Hybrid Approach:
• ML for local features
• Geometry for global constraints
• Best of both worlds
✅ Our chosen solution

Key Insight:
"Sometimes the simplest 
human intuition beats 
complex ML architectures"
"""
    
    ax7.text(0.05, 0.95, solutions_text, transform=ax7.transAxes, fontsize=10,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    # 8. 实际问题展示
    ax8 = fig.add_subplot(3, 3, 8, projection='3d')
    
    # 显示可能的多个局部最大值
    # 找到Z坐标前10%的点
    z_threshold = np.percentile(pc[:, 2], 90)
    high_points = pc[pc[:, 2] >= z_threshold]
    
    ax8.scatter(display_pc[:, 0], display_pc[:, 1], display_pc[:, 2], 
               c='lightgray', s=10, alpha=0.3)
    ax8.scatter(high_points[:, 0], high_points[:, 1], high_points[:, 2], 
               c='red', s=30, alpha=0.8, label='Top 10% Z points')
    ax8.scatter(f3_13_true[0], f3_13_true[1], f3_13_true[2], 
               c='blue', s=200, marker='*', label='True F3-13')
    
    ax8.set_title('The Challenge: Multiple High Points\n"Which one is the real maximum?"')
    ax8.legend()
    
    # 9. 成功案例
    ax9 = fig.add_subplot(3, 3, 9)
    ax9.axis('off')
    
    success_text = f"""
Our Success Story:

Sample: {sample_id}

Problem Analysis:
• F3-13 annotation: {f3_13_true[2]:.1f}mm (Z)
• Point cloud max: {pc_z_max[2]:.1f}mm (Z)
• Distance: {distance_to_max:.1f}mm

Solution Results:
• Before: 23.11±41.01mm error
• After: 7.80±16.33mm error
• Improvement: 66.3%

Why It Works:
✓ Combines ML prediction with 
  geometric correction
✓ Uses human intuition 
  (find the highest point)
✓ Simple and interpretable
✓ Doesn't hurt other keypoints

Lesson Learned:
"Human intuition + Machine learning
= Better than either alone"
"""
    
    ax9.text(0.05, 0.95, success_text, transform=ax9.transAxes, fontsize=9,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.suptitle(f'Human vs Machine Perspective: Why "Obvious" Tasks Are Hard\n'
                f'Sample {sample_id} - F3-13 (Z-Max) and F3-18 (Z-Min) Detection', 
                fontsize=16, fontweight='bold')
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    
    filename = 'human_vs_machine_perspective.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 人类vs机器视角分析保存: {filename}")
    plt.close()

def analyze_annotation_vs_geometry():
    """分析医生标注与几何极值的关系"""
    
    print("\n🔍 医生标注 vs 几何极值分析:")
    print("=" * 60)
    
    data = np.load('f3_19kp_preprocessed.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    sample_ids = data['sample_ids']
    
    f3_13_distances = []
    f3_18_distances = []
    z_diffs_max = []
    z_diffs_min = []
    
    for i in range(len(point_clouds)):
        pc = point_clouds[i]
        kp = keypoints[i]
        
        f3_13_true = kp[12]  # Z最高点
        f3_18_true = kp[17]  # Z最低点
        
        # 找到点云的实际极值
        z_max_idx = np.argmax(pc[:, 2])
        z_min_idx = np.argmin(pc[:, 2])
        pc_z_max = pc[z_max_idx]
        pc_z_min = pc[z_min_idx]
        
        # 计算距离
        dist_13 = np.linalg.norm(f3_13_true - pc_z_max)
        dist_18 = np.linalg.norm(f3_18_true - pc_z_min)
        
        f3_13_distances.append(dist_13)
        f3_18_distances.append(dist_18)
        
        # Z坐标差异
        z_diff_max = f3_13_true[2] - pc_z_max[2]
        z_diff_min = f3_18_true[2] - pc_z_min[2]
        
        z_diffs_max.append(z_diff_max)
        z_diffs_min.append(z_diff_min)
        
        print(f"样本 {sample_ids[i]}:")
        print(f"   F3-13距离几何最高点: {dist_13:.2f}mm (Z差: {z_diff_max:.2f}mm)")
        print(f"   F3-18距离几何最低点: {dist_18:.2f}mm (Z差: {z_diff_min:.2f}mm)")
    
    print(f"\n📊 统计结果:")
    print(f"F3-13 (Z最高点):")
    print(f"   平均距离几何最高点: {np.mean(f3_13_distances):.2f}±{np.std(f3_13_distances):.2f}mm")
    print(f"   平均Z坐标差异: {np.mean(z_diffs_max):.2f}±{np.std(z_diffs_max):.2f}mm")
    
    print(f"F3-18 (Z最低点):")
    print(f"   平均距离几何最低点: {np.mean(f3_18_distances):.2f}±{np.std(f3_18_distances):.2f}mm")
    print(f"   平均Z坐标差异: {np.mean(z_diffs_min):.2f}±{np.std(z_diffs_min):.2f}mm")
    
    # 分析为什么F3-18表现更好
    if np.mean(f3_18_distances) < np.mean(f3_13_distances):
        print(f"\n💡 发现: F3-18比F3-13更接近几何极值!")
        print(f"   这解释了为什么F3-18的模型性能更好")
        print(f"   医生标注的F3-18更符合'最低点'的几何定义")

def main():
    """主函数"""
    print("👁️ 人类视角 vs 机器视角分析")
    print("为什么'显而易见'的任务对机器来说很困难")
    print("=" * 60)
    
    # 创建可视化
    create_human_vs_machine_visualization()
    
    # 分析标注与几何的关系
    analyze_annotation_vs_geometry()
    
    print(f"\n🎯 关键洞察:")
    print("1. 人类有全局视野和形状理解能力")
    print("2. 机器只能看到局部邻域，缺乏全局概念")
    print("3. '最高点'对人类来说显而易见，对机器来说需要全局比较")
    print("4. 感受野就是机器的'视野范围'")
    print("5. 几何约束可以弥补机器的认知局限")
    
    print(f"\n💡 解决方案的智慧:")
    print("   结合机器学习的局部特征提取能力")
    print("   和人类直觉的全局几何理解")
    print("   = 最佳的混合方案")

if __name__ == "__main__":
    main()
