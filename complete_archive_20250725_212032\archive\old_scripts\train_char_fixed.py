#!/usr/bin/env python3
"""
修复版CHaR-Inspired训练脚本
修复了训练时直接使用目标关键点的问题
现在训练和推理使用相同的预测逻辑
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class SimplifiedCHaRPointNet(nn.Module):
    """
    简化版CHaR PointNet
    修复训练问题，使用更直接的实现
    """
    
    def __init__(self, num_keypoints=12, dropout_rate=0.3):
        super(SimplifiedCHaRPointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 基线架构 (保持成功配置)
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        # 关键点存在性分类头
        self.presence_classifier = nn.Sequential(
            nn.Linear(1024, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(128, num_keypoints),
            nn.Sigmoid()
        )
        
        # 关键点回归头 (基线方法)
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(128, 64),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(64, num_keypoints * 3)
        )
        
        self.dropout = nn.Dropout(dropout_rate)
        
        print(f"🧠 简化CHaR PointNet: {num_keypoints}个关键点")
        print(f"   - 基线架构 + 存在性分类")
        print(f"   - 条件化回归机制")
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [batch, 3, num_points]
        
        # 基线特征提取
        x1 = torch.relu(self.bn1(self.conv1(x)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        
        x3_res = x3 + self.residual1(x1)
        
        x4 = torch.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = torch.relu(self.bn5(self.conv5(x4_res)))
        
        # 全局特征
        global_feat = torch.max(x5, 2)[0]  # [batch, 1024]
        
        # 1. 预测关键点存在概率
        presence_probs = self.presence_classifier(global_feat)  # [batch, num_keypoints]
        
        # 2. 基础关键点回归
        keypoint_coords = self.keypoint_regressor(global_feat)  # [batch, num_keypoints * 3]
        keypoint_coords = keypoint_coords.view(batch_size, self.num_keypoints, 3)
        
        # 3. CHaR条件化机制
        # 使用存在概率调整关键点预测的置信度
        # 当存在概率低时，将关键点推向原点或点云中心
        conditioned_keypoints = []
        
        for b in range(batch_size):
            batch_conditioned = []
            for k in range(self.num_keypoints):
                presence_prob = presence_probs[b, k]
                original_kp = keypoint_coords[b, k]
                
                # 条件化：存在概率高时使用原始预测，存在概率低时趋向原点
                # 这里简化为线性插值
                conditioned_kp = presence_prob * original_kp
                batch_conditioned.append(conditioned_kp)
            
            conditioned_keypoints.append(torch.stack(batch_conditioned))
        
        conditioned_keypoints = torch.stack(conditioned_keypoints)
        
        return conditioned_keypoints, presence_probs

class ReducedKeypointsF3Dataset(Dataset):
    """12关键点F3数据集"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points
        self.augment = augment
        self.split = split
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
    
    def __len__(self):
        return len(self.sample_ids)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        # 数据增强
        if self.augment and self.split == 'train':
            if np.random.random() < 0.7:
                angle = np.random.uniform(-0.08, 0.08)
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                point_cloud = point_cloud @ rotation.T
                keypoints = keypoints @ rotation.T
            
            if np.random.random() < 0.6:
                translation = np.random.uniform(-0.4, 0.4, 3)
                point_cloud += translation
                keypoints += translation
            
            if np.random.random() < 0.5:
                scale = np.random.uniform(0.99, 1.01, 3)
                point_cloud *= scale
                keypoints *= scale
            
            if np.random.random() < 0.6:
                noise_level = np.random.choice([0.02, 0.03, 0.04])
                noise = np.random.normal(0, noise_level, point_cloud.shape)
                point_cloud += noise
        
        # 关键点存在性标签 (所有关键点都存在)
        presence_labels = np.ones(12, dtype=np.float32)
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'presence_labels': torch.FloatTensor(presence_labels),
            'sample_id': self.sample_ids[idx]
        }

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def train_simplified_char():
    """训练简化版CHaR模型"""
    
    print(f"🚀 **简化版CHaR模型训练**")
    print(f"🔧 **修复**: 解决训练时直接使用目标关键点的问题")
    print(f"🎯 **基础**: 5.829mm集成双Softmax成功配置")
    print(f"📈 **目标**: 突破5.0mm医疗级精度")
    print("=" * 80)
    
    set_seed(42)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 数据集
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    train_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'train', 
                                            num_points=4096, test_samples=test_samples, 
                                            augment=True, seed=42)
    val_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'val', 
                                          num_points=4096, test_samples=test_samples, 
                                          augment=False, seed=42)
    
    batch_size = 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
    
    # 模型
    model = SimplifiedCHaRPointNet(num_keypoints=12, dropout_rate=0.3).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 模型参数: {total_params:,}")
    
    # 损失函数
    mse_loss = nn.MSELoss()
    bce_loss = nn.BCELoss()
    
    # 优化器
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.7, patience=12, min_lr=1e-6)
    
    num_epochs = 100
    best_val_error = float('inf')
    patience = 20
    patience_counter = 0
    history = []
    min_delta = 0.005
    
    print(f"🎯 训练配置: 简化CHaR多任务学习")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 40)
        
        # 训练
        model.train()
        train_reg_loss = 0.0
        train_cls_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        for batch in train_loader:
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            presence_labels = batch['presence_labels'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints, presence_probs = model(point_cloud)
                
                # 损失计算
                reg_loss = mse_loss(pred_keypoints, keypoints)
                cls_loss = bce_loss(presence_probs, presence_labels)
                total_loss = reg_loss + 0.1 * cls_loss  # 分类损失权重较小
                
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_reg_loss += reg_loss.item()
                train_cls_loss += cls_loss.item()
                
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                        
            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue
        
        train_reg_loss /= len(train_loader)
        train_cls_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # 验证
        model.eval()
        val_reg_loss = 0.0
        val_cls_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                presence_labels = batch['presence_labels'].to(device)
                
                try:
                    pred_keypoints, presence_probs = model(point_cloud)
                    
                    reg_loss = mse_loss(pred_keypoints, keypoints)
                    cls_loss = bce_loss(presence_probs, presence_labels)
                    
                    val_reg_loss += reg_loss.item()
                    val_cls_loss += cls_loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        val_reg_loss /= len(val_loader)
        val_cls_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        scheduler.step(val_reg_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 打印结果
        print(f"训练: 回归={train_reg_loss:.4f}, 分类={train_cls_loss:.4f}, "
              f"误差={train_metrics['mean_distance']:.3f}mm")
        print(f"验证: 回归={val_reg_loss:.4f}, 分类={val_cls_loss:.4f}, "
              f"误差={val_metrics['mean_distance']:.3f}mm")
        print(f"学习率: {current_lr:.2e}")
        
        # 检查改进
        current_error = val_metrics['mean_distance']
        improvement = best_val_error - current_error
        
        if improvement > min_delta:
            best_val_error = current_error
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics
            }, f'best_simplified_char_{best_val_error:.3f}mm.pth')
            
            print(f"🎉 新最佳! 验证误差: {best_val_error:.3f}mm (改进{improvement:.3f}mm)")
            
            if best_val_error <= 5.0:
                print(f"🏆 **突破5.0mm医疗级目标!**")
            elif best_val_error < 5.5:
                print(f"🎯 **突破5.5mm目标!**")
            elif best_val_error < 5.829:
                print(f"✅ **超越集成双Softmax!**")
        else:
            patience_counter += 1
            print(f"⏳ 无显著改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停触发")
            break
    
    total_time = time.time() - start_time
    
    print(f"\n🎉 **简化CHaR训练完成!**")
    print(f"🎯 最佳误差: {best_val_error:.3f}mm")
    print(f"⏱️  训练时间: {total_time/60:.1f}分钟")
    
    return best_val_error

if __name__ == "__main__":
    set_seed(42)
    best_error = train_simplified_char()
