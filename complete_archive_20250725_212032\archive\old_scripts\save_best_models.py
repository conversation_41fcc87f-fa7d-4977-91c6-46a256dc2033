#!/usr/bin/env python3
"""
保存最佳模型并清理实验文件
保留成功的模型，清理表现不佳的实验
"""

import os
import shutil
import json
import glob
from datetime import datetime

def save_best_models():
    """保存最佳模型到专门目录"""
    
    print("🏆 **保存最佳模型**")
    print("=" * 50)
    
    # 创建最佳模型目录
    os.makedirs("models/best_models", exist_ok=True)
    
    # 最佳模型列表
    best_models = [
        {
            "source": "best_baseline_double_softmax_5.959mm.pth",
            "target": "models/best_models/best_baseline_double_softmax_5.959mm.pth",
            "description": "基线+双Softmax - 5.959mm (突破6mm目标)",
            "performance": "5.959mm",
            "status": "🥇 当前最佳"
        },
        {
            "source": "experiments/baseline_models/best_reduced_12kp_f3.pth", 
            "target": "models/best_models/best_baseline_12kp_6.208mm.pth",
            "description": "12关键点基线模型 - 6.208mm",
            "performance": "6.208mm", 
            "status": "🥈 基线最佳"
        }
    ]
    
    saved_models = []
    
    for model_info in best_models:
        source = model_info["source"]
        target = model_info["target"]
        
        if os.path.exists(source):
            shutil.copy2(source, target)
            saved_models.append(model_info)
            print(f"✅ 保存: {source} -> {target}")
            print(f"   描述: {model_info['description']}")
            print(f"   性能: {model_info['performance']}")
            print(f"   状态: {model_info['status']}")
        else:
            print(f"❌ 未找到: {source}")
    
    # 保存模型信息
    model_registry = {
        "updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "best_models": saved_models,
        "current_best": {
            "model": "baseline_double_softmax",
            "performance": "5.959mm",
            "improvement_vs_baseline": "4.0%",
            "breakthrough": "突破6mm目标"
        }
    }
    
    with open("models/best_models/model_registry.json", "w", encoding="utf-8") as f:
        json.dump(model_registry, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ 最佳模型注册表已保存: models/best_models/model_registry.json")
    
    return saved_models

def archive_poor_models():
    """存档表现不佳的模型"""
    
    print(f"\n📦 **存档表现不佳的模型**")
    print("=" * 50)
    
    # 创建存档目录
    archive_dir = "experiments/archived_models"
    os.makedirs(archive_dir, exist_ok=True)
    
    # Phase 1 改进模型 (大部分表现不佳)
    phase1_dir = "experiments/phase1_improvements"
    if os.path.exists(phase1_dir):
        phase1_files = glob.glob(f"{phase1_dir}/*.pth")
        
        # 保留最佳的几个，其余存档
        keep_threshold = 7.0  # 保留误差小于7mm的模型
        
        for pth_file in phase1_files:
            filename = os.path.basename(pth_file)
            
            # 从文件名提取误差值
            try:
                error_str = filename.split('_')[-1].replace('mm.pth', '')
                error_value = float(error_str)
                
                if error_value > keep_threshold:
                    # 存档表现不佳的模型
                    archive_path = os.path.join(archive_dir, filename)
                    shutil.move(pth_file, archive_path)
                    print(f"📦 存档: {filename} (误差: {error_value}mm)")
                else:
                    print(f"✅ 保留: {filename} (误差: {error_value}mm)")
                    
            except (ValueError, IndexError):
                # 无法解析误差值，保留
                print(f"⚠️  保留: {filename} (无法解析误差)")
    
    # Phase 2 架构模型 (大部分表现不佳)
    phase2_dir = "experiments/phase2_architectures"
    if os.path.exists(phase2_dir):
        phase2_files = glob.glob(f"{phase2_dir}/*.pth")
        
        # 保留最佳的Feature Pyramid模型，其余存档
        keep_models = [
            "best_feature_pyramid_pointnet_12kp_6.384mm.pth",
            "best_attention_pointnet_12kp_6.992mm.pth"  # 保留最佳的注意力模型
        ]
        
        for pth_file in phase2_files:
            filename = os.path.basename(pth_file)
            
            if filename in keep_models:
                print(f"✅ 保留: {filename}")
            else:
                # 存档其余模型
                archive_path = os.path.join(archive_dir, filename)
                shutil.move(pth_file, archive_path)
                print(f"📦 存档: {filename}")

def cleanup_results():
    """清理结果文件"""
    
    print(f"\n🧹 **清理结果文件**")
    print("=" * 50)
    
    results_dir = "experiments/results"
    if os.path.exists(results_dir):
        result_files = glob.glob(f"{results_dir}/*.json")
        
        # 保留重要的结果文件
        keep_results = [
            "phase2_architecture_results.json",
            "reduced_keypoints_results.json", 
            "hyperparameter_tuning_results.json"
        ]
        
        archive_results_dir = "experiments/archived_results"
        os.makedirs(archive_results_dir, exist_ok=True)
        
        for result_file in result_files:
            filename = os.path.basename(result_file)
            
            if filename in keep_results:
                print(f"✅ 保留结果: {filename}")
            else:
                # 存档其余结果
                archive_path = os.path.join(archive_results_dir, filename)
                shutil.move(result_file, archive_path)
                print(f"📦 存档结果: {filename}")

def create_cleanup_summary():
    """创建清理总结"""
    
    print(f"\n📋 **创建清理总结**")
    print("=" * 50)
    
    # 统计各目录的文件数量
    stats = {
        "best_models": len(glob.glob("models/best_models/*.pth")),
        "baseline_models": len(glob.glob("experiments/baseline_models/*.pth")),
        "phase1_remaining": len(glob.glob("experiments/phase1_improvements/*.pth")),
        "phase2_remaining": len(glob.glob("experiments/phase2_architectures/*.pth")),
        "archived_models": len(glob.glob("experiments/archived_models/*.pth")),
        "active_results": len(glob.glob("experiments/results/*.json")),
        "archived_results": len(glob.glob("experiments/archived_results/*.json"))
    }
    
    cleanup_summary = {
        "cleanup_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "action": "保存最佳模型，存档表现不佳的实验",
        "statistics": stats,
        "best_performance": {
            "current_best": "5.959mm (基线+双Softmax)",
            "baseline": "6.208mm (12关键点基线)",
            "improvement": "4.0%",
            "breakthrough": "成功突破6mm目标"
        },
        "next_steps": [
            "优化双Softmax参数",
            "探索更好的改进方向",
            "目标: 5.5mm医疗级精度"
        ]
    }
    
    with open("experiments/cleanup_summary.json", "w", encoding="utf-8") as f:
        json.dump(cleanup_summary, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 清理总结已保存: experiments/cleanup_summary.json")
    
    # 打印统计信息
    print(f"\n📊 **清理统计**:")
    print(f"   最佳模型: {stats['best_models']} 个")
    print(f"   基线模型: {stats['baseline_models']} 个") 
    print(f"   Phase1保留: {stats['phase1_remaining']} 个")
    print(f"   Phase2保留: {stats['phase2_remaining']} 个")
    print(f"   存档模型: {stats['archived_models']} 个")
    print(f"   活跃结果: {stats['active_results']} 个")
    print(f"   存档结果: {stats['archived_results']} 个")
    
    return cleanup_summary

def main():
    """主函数"""
    
    print("🧹 **模型清理和最佳模型保存**")
    print("🎯 **目标**: 保存成功模型，清理失败实验，为下一步改进做准备")
    print("=" * 80)
    
    # 保存最佳模型
    saved_models = save_best_models()
    
    # 存档表现不佳的模型
    archive_poor_models()
    
    # 清理结果文件
    cleanup_results()
    
    # 创建清理总结
    summary = create_cleanup_summary()
    
    print(f"\n🎉 **清理完成!**")
    print("=" * 50)
    print(f"🏆 当前最佳: 5.959mm (基线+双Softmax)")
    print(f"📈 突破成果: 成功突破6mm目标")
    print(f"🧹 清理状态: 保留精华，存档冗余")
    print(f"🚀 准备就绪: 可以开始寻找更好的改进")
    
    print(f"\n💡 **建议下一步**:")
    print(f"   1. 优化双Softmax参数 (阈值、温度、权重比例)")
    print(f"   2. 探索集成学习方法")
    print(f"   3. 数据质量改进")
    print(f"   4. 目标: 5.5mm医疗级精度")

if __name__ == "__main__":
    main()
