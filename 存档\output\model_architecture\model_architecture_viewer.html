
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强3D医疗关键点检测模型架构</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            text-align: center;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .svg-container {
            width: 100%;
            overflow-x: auto;
        }
        svg {
            max-width: 100%;
            height: auto;
        }
        .description {
            margin-top: 20px;
            text-align: left;
            color: #34495e;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 增强3D医疗关键点检测模型架构</h1>
        <div class="svg-container">
            <object data="enhanced_3d_model_architecture.svg" type="image/svg+xml" width="100%" height="600">
                <img src="enhanced_3d_model_architecture.svg" alt="模型架构图" style="max-width: 100%;">
            </object>
        </div>
        <div class="description">
            <h3>📊 架构说明</h3>
            <p><strong>输入层:</strong> 点云数据[N,3]和真实关键点标注[57,3]</p>
            <p><strong>模型层:</strong> ImprovedMedicalPointNet，包含点嵌入、特征提取、全局特征、关键点预测头和坐标范围预测头</p>
            <p><strong>后处理层:</strong> 五步增强3D校正流程，专门解决尺度和分布问题</p>
            <p><strong>输出层:</strong> 高精度关键点预测结果，达到医疗应用标准</p>
            
            <h3>🎯 技术亮点</h3>
            <ul>
                <li><strong>尺度感知:</strong> 专门解决预测点聚拢问题</li>
                <li><strong>解剖结构感知:</strong> 基于F1/F2/F3医学分区</li>
                <li><strong>分层优化:</strong> 五步渐进式精细校正</li>
                <li><strong>显著改善:</strong> 从2.51mm提升到1.17mm，改善43.8%</li>
            </ul>
        </div>
    </div>
</body>
</html>
