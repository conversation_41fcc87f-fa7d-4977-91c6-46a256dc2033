#!/usr/bin/env python3
"""
分析57点扩展性能下降的原因
Analyze reasons for performance degradation in 57-point expansion
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
import json
from sklearn.metrics import mean_squared_error
import seaborn as sns

def analyze_data_quality():
    """分析数据质量问题"""
    
    print("🔍 分析数据质量问题...")
    
    try:
        # 加载原始成功的12点数据
        data_12 = np.load('smart_expanded_57_dataset.npz', allow_pickle=True)
        proven_12kp = data_12['proven_12_keypoints']
        interpolated_57kp = data_12['interpolated_57_keypoints']
        
        print(f"✅ 数据加载成功:")
        print(f"   12关键点: {proven_12kp.shape}")
        print(f"   57关键点: {interpolated_57kp.shape}")
        
        # 分析1: 检查12点一致性
        mapping_12_to_57 = {
            0: 0, 1: 1, 2: 2, 3: 12, 4: 19, 5: 20,
            6: 21, 7: 31, 8: 38, 9: 52, 10: 50, 11: 51
        }
        
        extracted_12_from_57 = np.zeros_like(proven_12kp)
        for i in range(12):
            target_idx = mapping_12_to_57[i]
            extracted_12_from_57[:, i, :] = interpolated_57kp[:, target_idx, :]
        
        # 计算12点一致性误差
        consistency_errors = []
        for i in range(len(proven_12kp)):
            error = np.mean(np.linalg.norm(proven_12kp[i] - extracted_12_from_57[i], axis=1))
            consistency_errors.append(error)
        
        avg_consistency_error = np.mean(consistency_errors)
        
        print(f"\n📊 数据质量分析:")
        print(f"   12点一致性误差: {avg_consistency_error:.4f}mm")
        print(f"   最大一致性误差: {np.max(consistency_errors):.4f}mm")
        print(f"   最小一致性误差: {np.min(consistency_errors):.4f}mm")
        
        # 分析2: 检查插值质量
        region_errors = analyze_interpolation_quality(interpolated_57kp, proven_12kp)
        
        # 分析3: 检查数据分布
        analyze_data_distribution(proven_12kp, interpolated_57kp)
        
        return {
            'consistency_error': avg_consistency_error,
            'region_errors': region_errors,
            'consistency_errors_per_sample': consistency_errors
        }
        
    except Exception as e:
        print(f"❌ 数据分析失败: {e}")
        return None

def analyze_interpolation_quality(interpolated_57kp, proven_12kp):
    """分析插值质量"""
    
    print(f"\n🧬 分析插值质量...")
    
    # 定义区域
    regions = {
        'F1': list(range(0, 19)),
        'F2': list(range(19, 38)),
        'F3': list(range(38, 57))
    }
    
    region_errors = {}
    
    for region_name, indices in regions.items():
        region_points = interpolated_57kp[:, indices, :]
        
        # 计算区域内点的距离分布
        distances = []
        for sample in region_points:
            for i in range(len(sample)):
                for j in range(i+1, len(sample)):
                    dist = np.linalg.norm(sample[i] - sample[j])
                    distances.append(dist)
        
        avg_distance = np.mean(distances)
        std_distance = np.std(distances)
        
        region_errors[region_name] = {
            'avg_distance': avg_distance,
            'std_distance': std_distance,
            'num_points': len(indices)
        }
        
        print(f"   {region_name}区域:")
        print(f"     平均点间距离: {avg_distance:.2f}±{std_distance:.2f}mm")
        print(f"     点数: {len(indices)}")
    
    return region_errors

def analyze_data_distribution(proven_12kp, interpolated_57kp):
    """分析数据分布"""
    
    print(f"\n📈 分析数据分布...")
    
    # 计算12点的统计信息
    kp_12_stats = {
        'mean': np.mean(proven_12kp, axis=(0, 1)),
        'std': np.std(proven_12kp, axis=(0, 1)),
        'range': np.ptp(proven_12kp, axis=(0, 1))
    }
    
    # 计算57点的统计信息
    kp_57_stats = {
        'mean': np.mean(interpolated_57kp, axis=(0, 1)),
        'std': np.std(interpolated_57kp, axis=(0, 1)),
        'range': np.ptp(interpolated_57kp, axis=(0, 1))
    }
    
    print(f"   12点统计:")
    print(f"     坐标范围: X={kp_12_stats['range'][0]:.1f}, Y={kp_12_stats['range'][1]:.1f}, Z={kp_12_stats['range'][2]:.1f}")
    print(f"     标准差: X={kp_12_stats['std'][0]:.1f}, Y={kp_12_stats['std'][1]:.1f}, Z={kp_12_stats['std'][2]:.1f}")
    
    print(f"   57点统计:")
    print(f"     坐标范围: X={kp_57_stats['range'][0]:.1f}, Y={kp_57_stats['range'][1]:.1f}, Z={kp_57_stats['range'][2]:.1f}")
    print(f"     标准差: X={kp_57_stats['std'][0]:.1f}, Y={kp_57_stats['std'][1]:.1f}, Z={kp_57_stats['std'][2]:.1f}")
    
    return kp_12_stats, kp_57_stats

def analyze_model_complexity():
    """分析模型复杂度问题"""
    
    print(f"\n🤖 分析模型复杂度问题...")
    
    # 计算参数数量
    from end_to_end_57_system import FixedMultiModalPointNet, AnatomicalExpansionNetwork, EndToEnd57System
    
    # 12点模型
    model_12 = FixedMultiModalPointNet(num_keypoints=12)
    params_12 = sum(p.numel() for p in model_12.parameters())
    
    # 扩展网络
    expansion_net = AnatomicalExpansionNetwork()
    params_expansion = sum(p.numel() for p in expansion_net.parameters())
    
    # 端到端模型
    end_to_end = EndToEnd57System()
    params_total = sum(p.numel() for p in end_to_end.parameters())
    
    print(f"   模型参数对比:")
    print(f"     12点模型: {params_12:,} 参数")
    print(f"     扩展网络: {params_expansion:,} 参数")
    print(f"     端到端模型: {params_total:,} 参数")
    print(f"     复杂度增加: {params_total/params_12:.1f}x")
    
    # 分析数据/参数比
    num_samples = 97
    data_param_ratio_12 = (num_samples * 12 * 3) / params_12
    data_param_ratio_57 = (num_samples * 57 * 3) / params_total
    
    print(f"   数据/参数比:")
    print(f"     12点: {data_param_ratio_12:.6f}")
    print(f"     57点: {data_param_ratio_57:.6f}")
    print(f"     比值变化: {data_param_ratio_57/data_param_ratio_12:.3f}x")
    
    return {
        'params_12': params_12,
        'params_expansion': params_expansion,
        'params_total': params_total,
        'data_param_ratio_12': data_param_ratio_12,
        'data_param_ratio_57': data_param_ratio_57
    }

def analyze_training_issues():
    """分析训练问题"""
    
    print(f"\n🎯 分析训练问题...")
    
    try:
        # 加载训练历史
        with open('end_to_end_57_results.json', 'r') as f:
            results = json.load(f)
        
        history = results['training_history']
        
        # 分析收敛情况
        train_errors_57 = history['train_error_57']
        val_errors_57 = history['val_error_57']
        train_errors_12 = history['train_error_12']
        val_errors_12 = history['val_error_12']
        
        print(f"   训练收敛分析:")
        print(f"     57点训练误差: {train_errors_57[0]:.2f} → {train_errors_57[-1]:.2f}mm")
        print(f"     57点验证误差: {val_errors_57[0]:.2f} → {val_errors_57[-1]:.2f}mm")
        print(f"     12点训练误差: {train_errors_12[0]:.2f} → {train_errors_12[-1]:.2f}mm")
        print(f"     12点验证误差: {val_errors_12[0]:.2f} → {val_errors_12[-1]:.2f}mm")
        
        # 检查过拟合
        final_gap_57 = val_errors_57[-1] - train_errors_57[-1]
        final_gap_12 = val_errors_12[-1] - train_errors_12[-1]
        
        print(f"   过拟合检查:")
        print(f"     57点训练-验证差距: {final_gap_57:.2f}mm")
        print(f"     12点训练-验证差距: {final_gap_12:.2f}mm")
        
        if final_gap_57 > 3:
            print(f"     ⚠️ 57点可能存在过拟合")
        if final_gap_12 > 2:
            print(f"     ⚠️ 12点可能存在过拟合")
        
        return {
            'convergence_57': train_errors_57[-1],
            'convergence_12': train_errors_12[-1],
            'overfitting_57': final_gap_57,
            'overfitting_12': final_gap_12
        }
        
    except Exception as e:
        print(f"❌ 训练分析失败: {e}")
        return None

def identify_key_issues():
    """识别关键问题"""
    
    print(f"\n🎯 识别关键问题...")
    
    issues = []
    
    # 问题1: 数据质量
    data_analysis = analyze_data_quality()
    if data_analysis and data_analysis['consistency_error'] > 1.0:
        issues.append({
            'type': 'data_quality',
            'severity': 'high',
            'description': f"12点一致性误差过高: {data_analysis['consistency_error']:.2f}mm",
            'impact': '直接影响57点预测精度'
        })
    
    # 问题2: 模型复杂度
    complexity_analysis = analyze_model_complexity()
    if complexity_analysis['data_param_ratio_57'] < 0.001:
        issues.append({
            'type': 'model_complexity',
            'severity': 'high',
            'description': f"数据/参数比过低: {complexity_analysis['data_param_ratio_57']:.6f}",
            'impact': '容易过拟合，泛化能力差'
        })
    
    # 问题3: 训练问题
    training_analysis = analyze_training_issues()
    if training_analysis and training_analysis['overfitting_57'] > 3:
        issues.append({
            'type': 'training',
            'severity': 'medium',
            'description': f"57点过拟合: 训练-验证差距{training_analysis['overfitting_57']:.2f}mm",
            'impact': '模型泛化能力受限'
        })
    
    # 问题4: 插值策略
    if data_analysis:
        region_errors = data_analysis['region_errors']
        for region, stats in region_errors.items():
            if stats['std_distance'] > 20:  # 如果区域内点距离标准差过大
                issues.append({
                    'type': 'interpolation',
                    'severity': 'medium',
                    'description': f"{region}区域插值不稳定: 标准差{stats['std_distance']:.2f}mm",
                    'impact': '影响该区域关键点预测精度'
                })
    
    return issues

def propose_solutions(issues):
    """提出解决方案"""
    
    print(f"\n💡 解决方案建议...")
    
    solutions = []
    
    for issue in issues:
        if issue['type'] == 'data_quality':
            solutions.append({
                'for_issue': issue['type'],
                'priority': 'high',
                'solution': '改进12→57映射策略',
                'details': [
                    '使用更精确的解剖学映射',
                    '基于真实57点数据训练映射网络',
                    '添加解剖学约束损失函数'
                ]
            })
        
        elif issue['type'] == 'model_complexity':
            solutions.append({
                'for_issue': issue['type'],
                'priority': 'high',
                'solution': '简化模型架构',
                'details': [
                    '减少网络层数和参数',
                    '使用正则化技术',
                    '采用知识蒸馏方法',
                    '增加数据增强'
                ]
            })
        
        elif issue['type'] == 'training':
            solutions.append({
                'for_issue': issue['type'],
                'priority': 'medium',
                'solution': '改进训练策略',
                'details': [
                    '使用更强的正则化',
                    '早停策略优化',
                    '学习率调度改进',
                    '交叉验证'
                ]
            })
        
        elif issue['type'] == 'interpolation':
            solutions.append({
                'for_issue': issue['type'],
                'priority': 'medium',
                'solution': '改进插值算法',
                'details': [
                    '基于解剖学先验的插值',
                    '使用径向基函数插值',
                    '添加平滑约束',
                    '区域特异性插值策略'
                ]
            })
    
    return solutions

def main():
    """主函数"""
    
    print("🔍 分析57点扩展性能下降原因")
    print("=" * 80)
    
    # 步骤1: 数据质量分析
    data_analysis = analyze_data_quality()
    
    # 步骤2: 模型复杂度分析
    complexity_analysis = analyze_model_complexity()
    
    # 步骤3: 训练问题分析
    training_analysis = analyze_training_issues()
    
    # 步骤4: 识别关键问题
    issues = identify_key_issues()
    
    print(f"\n🚨 发现的关键问题:")
    for i, issue in enumerate(issues, 1):
        print(f"   {i}. [{issue['severity'].upper()}] {issue['description']}")
        print(f"      影响: {issue['impact']}")
    
    # 步骤5: 提出解决方案
    solutions = propose_solutions(issues)
    
    print(f"\n💡 解决方案:")
    for i, solution in enumerate(solutions, 1):
        print(f"   {i}. {solution['solution']} (优先级: {solution['priority']})")
        for detail in solution['details']:
            print(f"      - {detail}")
    
    # 保存分析结果
    analysis_results = {
        'data_analysis': data_analysis,
        'complexity_analysis': complexity_analysis,
        'training_analysis': training_analysis,
        'issues': issues,
        'solutions': solutions
    }
    
    with open('performance_degradation_analysis.json', 'w') as f:
        json.dump(analysis_results, f, indent=2, default=str)
    
    print(f"\n📋 分析结果已保存到: performance_degradation_analysis.json")
    
    # 总结最重要的发现
    print(f"\n🎯 核心问题总结:")
    print(f"   1. 数据质量: 插值生成的57点缺乏真实性")
    print(f"   2. 模型复杂度: 参数过多，数据过少，容易过拟合")
    print(f"   3. 训练策略: 端到端训练可能不如分阶段训练")
    print(f"   4. 评估方式: 需要更细粒度的区域性能分析")
    
    print(f"\n🚀 最优先的改进方向:")
    print(f"   1. 获取真实的57点标注数据")
    print(f"   2. 简化模型架构，减少参数")
    print(f"   3. 改进插值算法，提高数据质量")
    print(f"   4. 使用更强的正则化和数据增强")

if __name__ == "__main__":
    main()
