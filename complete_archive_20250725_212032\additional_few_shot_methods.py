#!/usr/bin/env python3
"""
更多小样本学习方法
Additional Few-Shot Learning Methods
探索更多适合医学点云的小样本学习技术
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pathlib import Path
from datetime import datetime
import json
import random

class RelationNetworkPointNet(nn.Module):
    """关系网络PointNet"""
    
    def __init__(self, num_keypoints=19, embedding_dim=256):
        super().__init__()
        self.num_keypoints = num_keypoints
        self.embedding_dim = embedding_dim
        
        # 嵌入网络
        self.embedding_net = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
            nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.<PERSON><PERSON><PERSON>(),
            nn.Conv1d(256, embedding_dim, 1), nn.BatchNorm1d(embedding_dim), nn.ReLU()
        )
        
        # 关系网络
        self.relation_net = nn.Sequential(
            nn.Linear(embedding_dim * 2, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )
        
        # 关键点预测器
        self.keypoint_predictor = nn.Sequential(
            nn.Linear(embedding_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, num_keypoints * 3)
        )
        
    def forward(self, support_pc, support_kp, query_pc):
        # 嵌入支持集和查询集
        support_embed = self.embed_point_cloud(support_pc)  # (N_support, embedding_dim)
        query_embed = self.embed_point_cloud(query_pc)      # (N_query, embedding_dim)
        
        # 计算关系分数
        relations = []
        for q_emb in query_embed:
            q_relations = []
            for s_emb in support_embed:
                # 连接查询和支持嵌入
                relation_input = torch.cat([q_emb, s_emb], dim=0)
                relation_score = self.relation_net(relation_input)
                q_relations.append(relation_score)
            relations.append(torch.stack(q_relations))
        
        relations = torch.stack(relations)  # (N_query, N_support, 1)
        relation_weights = F.softmax(relations.squeeze(-1), dim=1)  # (N_query, N_support)
        
        # 基于关系权重预测关键点
        predictions = []
        for i, q_emb in enumerate(query_embed):
            # 加权支持集关键点
            weighted_kp = torch.zeros(self.num_keypoints, 3, device=query_pc.device)
            for j, weight in enumerate(relation_weights[i]):
                weighted_kp += weight * support_kp[j]
            
            # 结合嵌入预测
            kp_adjustment = self.keypoint_predictor(q_emb)
            kp_adjustment = kp_adjustment.view(self.num_keypoints, 3)
            
            final_kp = weighted_kp + 0.1 * kp_adjustment
            predictions.append(final_kp)
        
        return torch.stack(predictions)
    
    def embed_point_cloud(self, point_cloud):
        if len(point_cloud.shape) == 2:
            point_cloud = point_cloud.unsqueeze(0)
        
        B, N, _ = point_cloud.shape
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        features = self.embedding_net(x)  # (B, embedding_dim, N)
        global_features = torch.max(features, dim=2)[0]  # (B, embedding_dim)
        
        return global_features

class MatchingNetworkPointNet(nn.Module):
    """匹配网络PointNet"""
    
    def __init__(self, num_keypoints=19, feature_dim=512):
        super().__init__()
        self.num_keypoints = num_keypoints
        self.feature_dim = feature_dim
        
        # 特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
            nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
            nn.Conv1d(256, feature_dim, 1), nn.BatchNorm1d(feature_dim), nn.ReLU()
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=feature_dim, num_heads=8, dropout=0.1, batch_first=True
        )
        
        # 关键点预测器
        self.keypoint_predictor = nn.Sequential(
            nn.Linear(feature_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, num_keypoints * 3)
        )
        
    def forward(self, support_pc, support_kp, query_pc):
        # 提取特征
        support_features = self.extract_features(support_pc)  # (N_support, feature_dim)
        query_features = self.extract_features(query_pc)      # (N_query, feature_dim)
        
        # 注意力匹配
        attended_features, attention_weights = self.attention(
            query_features.unsqueeze(0),  # (1, N_query, feature_dim)
            support_features.unsqueeze(0), # (1, N_support, feature_dim)
            support_features.unsqueeze(0)  # (1, N_support, feature_dim)
        )
        
        attended_features = attended_features.squeeze(0)  # (N_query, feature_dim)
        attention_weights = attention_weights.squeeze(0)  # (N_query, N_support)
        
        # 基于注意力权重预测关键点
        predictions = []
        for i, attn_weights in enumerate(attention_weights):
            # 加权支持集关键点
            weighted_kp = torch.zeros(self.num_keypoints, 3, device=query_pc.device)
            for j, weight in enumerate(attn_weights):
                weighted_kp += weight * support_kp[j]
            
            # 特征调整
            feature_adjustment = self.keypoint_predictor(attended_features[i])
            feature_adjustment = feature_adjustment.view(self.num_keypoints, 3)
            
            final_kp = weighted_kp + 0.05 * feature_adjustment
            predictions.append(final_kp)
        
        return torch.stack(predictions)
    
    def extract_features(self, point_cloud):
        if len(point_cloud.shape) == 2:
            point_cloud = point_cloud.unsqueeze(0)
        
        B, N, _ = point_cloud.shape
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        features = self.feature_extractor(x)  # (B, feature_dim, N)
        global_features = torch.max(features, dim=2)[0]  # (B, feature_dim)
        
        return global_features

class GradientBasedMetaLearner(nn.Module):
    """基于梯度的元学习器 (简化MAML)"""
    
    def __init__(self, num_keypoints=19):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 基础网络
        self.base_net = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
            nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
            nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
            nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 快速适应头
        self.adaptation_head = nn.Sequential(
            nn.Linear(1024, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, num_keypoints * 3)
        )
        
    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        # 特征提取
        features = self.base_net(x)  # (B, 1024, N)
        global_features = torch.max(features, dim=2)[0]  # (B, 1024)
        
        # 关键点预测
        keypoints = self.adaptation_head(global_features)
        return keypoints.view(B, self.num_keypoints, 3)
    
    def fast_adapt(self, support_pc, support_kp, adaptation_steps=3, lr=0.01):
        """快速适应"""
        # 创建临时参数副本
        temp_params = {}
        for name, param in self.named_parameters():
            temp_params[name] = param.clone()
        
        # 快速适应步骤
        for step in range(adaptation_steps):
            # 前向传播
            pred_kp = self.forward(support_pc)
            
            # 计算损失
            loss = F.mse_loss(pred_kp, support_kp)
            
            # 计算梯度
            grads = torch.autograd.grad(loss, self.parameters(), create_graph=True)
            
            # 更新临时参数
            for (name, param), grad in zip(self.named_parameters(), grads):
                temp_params[name] = param - lr * grad
        
        return temp_params

class TransferLearningPointNet(nn.Module):
    """迁移学习PointNet"""
    
    def __init__(self, num_keypoints=19, pretrained_features=True):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 预训练特征提取器 (冻结部分层)
        self.pretrained_features = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
            nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
        )
        
        # 可训练的高层特征
        self.trainable_features = nn.Sequential(
            nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
            nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 任务特定头
        self.task_head = nn.Sequential(
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, num_keypoints * 3)
        )
        
        # 冻结预训练层
        if pretrained_features:
            for param in self.pretrained_features.parameters():
                param.requires_grad = False
    
    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        # 预训练特征 (冻结)
        with torch.no_grad():
            pretrained_feat = self.pretrained_features(x)  # (B, 256, N)
        
        # 可训练特征
        trainable_feat = self.trainable_features(pretrained_feat)  # (B, 1024, N)
        global_feat = torch.max(trainable_feat, dim=2)[0]  # (B, 1024)
        
        # 任务特定预测
        keypoints = self.task_head(global_feat)
        return keypoints.view(B, self.num_keypoints, 3)

class AdditionalFewShotTrainer:
    """更多小样本学习训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def load_aligned_data(self):
        """加载对齐数据"""
        print("📦 加载F3对齐数据...")
        
        aligned_files = list(Path("data/processed").glob("f3_aligned_dataset_*.npz"))
        if not aligned_files:
            raise FileNotFoundError("未找到F3对齐数据集")
        
        latest_file = max(aligned_files, key=lambda x: x.stat().st_mtime)
        data = np.load(str(latest_file), allow_pickle=True)
        
        point_clouds = np.array(data['point_clouds'], dtype=np.float32)
        keypoints = np.array(data['keypoints'], dtype=np.float32)
        
        # 数据划分
        from sklearn.model_selection import train_test_split
        indices = np.arange(len(point_clouds))
        train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
        train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
        
        print(f"✅ 数据加载完成: {point_clouds.shape}")
        print(f"   训练: {len(train_indices)}, 验证: {len(val_indices)}, 测试: {len(test_indices)}")
        
        return self.data
    
    def train_relation_network(self, epochs=80, lr=0.001):
        """训练关系网络"""
        print(f"\n🔗 训练关系网络")
        print(f"   策略: 基于关系的小样本学习")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        model = RelationNetworkPointNet(num_keypoints=19, embedding_dim=256).to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        
        for epoch in range(epochs):
            model.train()
            epoch_losses = []
            
            # 小样本任务采样
            for _ in range(10):  # 每个epoch 10个任务
                # 采样支持集和查询集
                support_size = 3
                query_size = 2
                
                task_indices = np.random.choice(
                    len(self.data['train']['point_clouds']), 
                    support_size + query_size, 
                    replace=False
                )
                
                support_indices = task_indices[:support_size]
                query_indices = task_indices[support_size:]
                
                support_pcs = torch.FloatTensor(self.data['train']['point_clouds'][support_indices]).to(self.device)
                support_kps = torch.FloatTensor(self.data['train']['keypoints'][support_indices]).to(self.device)
                query_pcs = torch.FloatTensor(self.data['train']['point_clouds'][query_indices]).to(self.device)
                query_kps = torch.FloatTensor(self.data['train']['keypoints'][query_indices]).to(self.device)
                
                optimizer.zero_grad()
                
                # 关系网络预测
                pred_kps = model(support_pcs, support_kps, query_pcs)
                loss = criterion(pred_kps, query_kps)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_losses.append(loss.item())
                
                del support_pcs, support_kps, query_pcs, query_kps, pred_kps
                torch.cuda.empty_cache()
            
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_few_shot_model(model, 'val', model_type='relation')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm")
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def train_matching_network(self, epochs=80, lr=0.001):
        """训练匹配网络"""
        print(f"\n🎯 训练匹配网络")
        print(f"   策略: 基于注意力的匹配学习")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        model = MatchingNetworkPointNet(num_keypoints=19, feature_dim=512).to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        
        for epoch in range(epochs):
            model.train()
            epoch_losses = []
            
            # 小样本任务采样
            for _ in range(8):  # 每个epoch 8个任务
                # 采样支持集和查询集
                support_size = 4
                query_size = 2
                
                task_indices = np.random.choice(
                    len(self.data['train']['point_clouds']), 
                    support_size + query_size, 
                    replace=False
                )
                
                support_indices = task_indices[:support_size]
                query_indices = task_indices[support_size:]
                
                support_pcs = torch.FloatTensor(self.data['train']['point_clouds'][support_indices]).to(self.device)
                support_kps = torch.FloatTensor(self.data['train']['keypoints'][support_indices]).to(self.device)
                query_pcs = torch.FloatTensor(self.data['train']['point_clouds'][query_indices]).to(self.device)
                query_kps = torch.FloatTensor(self.data['train']['keypoints'][query_indices]).to(self.device)
                
                optimizer.zero_grad()
                
                # 匹配网络预测
                pred_kps = model(support_pcs, support_kps, query_pcs)
                loss = criterion(pred_kps, query_kps)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_losses.append(loss.item())
                
                del support_pcs, support_kps, query_pcs, query_kps, pred_kps
                torch.cuda.empty_cache()
            
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_few_shot_model(model, 'val', model_type='matching')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm")
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def train_gradient_meta_learner(self, epochs=60, lr=0.001):
        """训练基于梯度的元学习器"""
        print(f"\n🧠 训练基于梯度的元学习器")
        print(f"   策略: 简化MAML快速适应")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        model = GradientBasedMetaLearner(num_keypoints=19).to(self.device)
        meta_optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        
        for epoch in range(epochs):
            model.train()
            meta_losses = []
            
            # 元学习任务
            for _ in range(5):  # 每个epoch 5个元任务
                # 采样支持集和查询集
                support_size = 4
                query_size = 2
                
                task_indices = np.random.choice(
                    len(self.data['train']['point_clouds']), 
                    support_size + query_size, 
                    replace=False
                )
                
                support_indices = task_indices[:support_size]
                query_indices = task_indices[support_size:]
                
                support_pcs = torch.FloatTensor(self.data['train']['point_clouds'][support_indices]).to(self.device)
                support_kps = torch.FloatTensor(self.data['train']['keypoints'][support_indices]).to(self.device)
                query_pcs = torch.FloatTensor(self.data['train']['point_clouds'][query_indices]).to(self.device)
                query_kps = torch.FloatTensor(self.data['train']['keypoints'][query_indices]).to(self.device)
                
                # 快速适应 (简化版)
                meta_optimizer.zero_grad()
                
                # 在支持集上快速适应
                support_pred = model(support_pcs)
                support_loss = criterion(support_pred, support_kps)
                
                # 在查询集上测试
                query_pred = model(query_pcs)
                query_loss = criterion(query_pred, query_kps)
                
                # 元损失 (支持集损失 + 查询集损失)
                meta_loss = support_loss + query_loss
                
                meta_loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                meta_optimizer.step()
                
                meta_losses.append(meta_loss.item())
                
                del support_pcs, support_kps, query_pcs, query_kps
                torch.cuda.empty_cache()
            
            avg_meta_loss = np.mean(meta_losses) if meta_losses else 0
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_standard_model(model, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                
                print(f"Epoch {epoch:3d}: Meta_Loss={avg_meta_loss:.4f}, Val={val_error:.3f}mm")
            else:
                print(f"Epoch {epoch:3d}: Meta_Loss={avg_meta_loss:.4f}")
        
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def train_transfer_learning(self, epochs=100, lr=0.0005):
        """训练迁移学习模型"""
        print(f"\n🔄 训练迁移学习模型")
        print(f"   策略: 冻结预训练特征 + 微调任务头")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        model = TransferLearningPointNet(num_keypoints=19, pretrained_features=True).to(self.device)
        
        # 只优化可训练部分
        trainable_params = []
        for name, param in model.named_parameters():
            if param.requires_grad:
                trainable_params.append(param)
        
        optimizer = torch.optim.Adam(trainable_params, lr=lr, weight_decay=1e-4)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        
        for epoch in range(epochs):
            model.train()
            epoch_losses = []
            
            # 小批次训练
            k_shot = min(15, len(self.data['train']['point_clouds']))
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                k_shot, 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 简单增强
            aug_pcs = []
            aug_kps = []
            for pc, kp in zip(train_pcs, train_kps):
                aug_pcs.append(pc)
                aug_kps.append(kp)
                
                # 轻微旋转
                angle = np.random.uniform(-0.01, 0.01)
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], dtype=np.float32)
                
                aug_pc = pc @ rotation.T
                aug_kp = kp @ rotation.T
                aug_pcs.append(aug_pc)
                aug_kps.append(aug_kp)
            
            # 批次训练
            batch_size = 4
            for i in range(0, len(aug_pcs), batch_size):
                batch_pcs = torch.FloatTensor(aug_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(aug_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                pred_kps = model(batch_pcs)
                loss = criterion(pred_kps, batch_kps)
                loss.backward()
                
                torch.nn.utils.clip_grad_norm_(trainable_params, max_norm=1.0)
                optimizer.step()
                
                epoch_losses.append(loss.item())
                
                del batch_pcs, batch_kps, pred_kps, loss
                torch.cuda.empty_cache()
            
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_standard_model(model, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm")
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def evaluate_few_shot_model(self, model, split='test', model_type='relation'):
        """评估小样本模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            # 小样本评估
            for i in range(0, len(pcs), 5):
                if i + 4 >= len(pcs):
                    break
                
                # 使用前3个作为支持集，后2个作为查询集
                support_pcs = torch.FloatTensor(pcs[i:i+3]).to(self.device)
                support_kps = torch.FloatTensor(kps[i:i+3]).to(self.device)
                query_pcs = torch.FloatTensor(pcs[i+3:i+5]).to(self.device)
                query_kps = torch.FloatTensor(kps[i+3:i+5]).to(self.device)
                
                pred_kps = model(support_pcs, support_kps, query_pcs)
                
                for j in range(len(query_pcs)):
                    error = torch.mean(torch.norm(pred_kps[j] - query_kps[j], dim=1))
                    total_error += error.item()
                    num_samples += 1
                
                del support_pcs, support_kps, query_pcs, query_kps, pred_kps
                torch.cuda.empty_cache()
        
        return total_error / num_samples if num_samples > 0 else float('inf')
    
    def evaluate_standard_model(self, model, split='test'):
        """评估标准模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for i in range(0, len(pcs), 2):
                batch_pcs = torch.FloatTensor(pcs[i:i+2]).to(self.device)
                batch_kps = torch.FloatTensor(kps[i:i+2]).to(self.device)
                
                pred_kps = model(batch_pcs)
                
                for j in range(len(batch_pcs)):
                    error = torch.mean(torch.norm(pred_kps[j] - batch_kps[j], dim=1))
                    total_error += error.item()
                    num_samples += 1
                
                del batch_pcs, batch_kps, pred_kps
                torch.cuda.empty_cache()
        
        return total_error / num_samples if num_samples > 0 else float('inf')

def run_additional_few_shot_experiments():
    """运行更多小样本学习实验"""
    print("🚀 更多小样本学习方法实验")
    print("=" * 60)
    print("探索更多适合医学点云的小样本学习技术:")
    print("1. 关系网络 (Relation Network)")
    print("2. 匹配网络 (Matching Network)")
    print("3. 基于梯度的元学习 (简化MAML)")
    print("4. 迁移学习 (Transfer Learning)")
    
    trainer = AdditionalFewShotTrainer()
    data = trainer.load_aligned_data()
    
    results = {}
    
    # 1. 关系网络
    print(f"\n{'='*60}")
    try:
        relation_model, relation_val_error = trainer.train_relation_network(epochs=60, lr=0.001)
        relation_test_error = trainer.evaluate_few_shot_model(relation_model, 'test', 'relation')
        results['relation_network'] = {
            'val_error': relation_val_error,
            'test_error': relation_test_error
        }
        print(f"✅ 关系网络完成: 验证={relation_val_error:.3f}mm, 测试={relation_test_error:.3f}mm")
    except Exception as e:
        print(f"❌ 关系网络失败: {e}")
        results['relation_network'] = {'val_error': float('inf'), 'test_error': float('inf')}
    
    # 2. 匹配网络
    print(f"\n{'='*60}")
    try:
        matching_model, matching_val_error = trainer.train_matching_network(epochs=60, lr=0.001)
        matching_test_error = trainer.evaluate_few_shot_model(matching_model, 'test', 'matching')
        results['matching_network'] = {
            'val_error': matching_val_error,
            'test_error': matching_test_error
        }
        print(f"✅ 匹配网络完成: 验证={matching_val_error:.3f}mm, 测试={matching_test_error:.3f}mm")
    except Exception as e:
        print(f"❌ 匹配网络失败: {e}")
        results['matching_network'] = {'val_error': float('inf'), 'test_error': float('inf')}
    
    # 3. 基于梯度的元学习
    print(f"\n{'='*60}")
    try:
        meta_model, meta_val_error = trainer.train_gradient_meta_learner(epochs=50, lr=0.001)
        meta_test_error = trainer.evaluate_standard_model(meta_model, 'test')
        results['gradient_meta_learning'] = {
            'val_error': meta_val_error,
            'test_error': meta_test_error
        }
        print(f"✅ 梯度元学习完成: 验证={meta_val_error:.3f}mm, 测试={meta_test_error:.3f}mm")
    except Exception as e:
        print(f"❌ 梯度元学习失败: {e}")
        results['gradient_meta_learning'] = {'val_error': float('inf'), 'test_error': float('inf')}
    
    # 4. 迁移学习
    print(f"\n{'='*60}")
    try:
        transfer_model, transfer_val_error = trainer.train_transfer_learning(epochs=80, lr=0.0005)
        transfer_test_error = trainer.evaluate_standard_model(transfer_model, 'test')
        results['transfer_learning'] = {
            'val_error': transfer_val_error,
            'test_error': transfer_test_error
        }
        print(f"✅ 迁移学习完成: 验证={transfer_val_error:.3f}mm, 测试={transfer_test_error:.3f}mm")
    except Exception as e:
        print(f"❌ 迁移学习失败: {e}")
        results['transfer_learning'] = {'val_error': float('inf'), 'test_error': float('inf')}
    
    # 结果汇总
    print(f"\n🏆 更多小样本学习方法对比:")
    print("=" * 60)
    
    best_method = None
    best_val_error = float('inf')
    best_test_error = float('inf')
    
    for method, result in results.items():
        val_error = result['val_error']
        test_error = result['test_error']
        
        print(f"{method:25s}: 验证={val_error:.3f}mm, 测试={test_error:.3f}mm")
        
        if val_error < best_val_error:
            best_val_error = val_error
            best_test_error = test_error
            best_method = method
    
    print(f"\n🏆 最佳方法: {best_method}")
    print(f"   最佳验证误差: {best_val_error:.3f}mm")
    print(f"   对应测试误差: {best_test_error:.3f}mm")
    
    # 与历史最佳对比
    historical_best = {
        'point_transformer': {'val': 7.129, 'test': 8.127},
        'mixup_original': {'val': 7.041, 'test': 8.363},
        'consistency_regularization': {'val': 7.176, 'test': 8.012}
    }
    
    print(f"\n📈 与历史最佳方法对比:")
    print("历史最佳:")
    for method, errors in historical_best.items():
        print(f"  {method:25s}: 验证={errors['val']:.3f}mm, 测试={errors['test']:.3f}mm")
    
    print(f"\n当前最佳:")
    print(f"  {best_method:25s}: 验证={best_val_error:.3f}mm, 测试={best_test_error:.3f}mm")
    
    # 改进分析
    best_historical_val = min([errors['val'] for errors in historical_best.values()])
    best_historical_test = min([errors['test'] for errors in historical_best.values()])
    
    if best_val_error < best_historical_val:
        val_improvement = (best_historical_val - best_val_error) / best_historical_val * 100
        print(f"🎉 验证误差新纪录！改进: {val_improvement:.1f}%")
    
    if best_test_error < best_historical_test:
        test_improvement = (best_historical_test - best_test_error) / best_historical_test * 100
        print(f"🎉 测试误差新纪录！改进: {test_improvement:.1f}%")
    
    # 医疗级评估
    medical_target = 5.0
    print(f"\n🎯 医疗级精度评估:")
    print(f"医疗级目标:               {medical_target:.1f}mm")
    print(f"当前最佳验证误差:         {best_val_error:.3f}mm")
    print(f"当前最佳测试误差:         {best_test_error:.3f}mm")
    
    if best_val_error <= medical_target:
        print("🎉 验证误差达到医疗级精度！")
    elif best_val_error <= 6.0:
        remaining = best_val_error - medical_target
        print(f"🔥 验证误差非常接近医疗级！还需{remaining:.3f}mm")
    else:
        remaining = best_val_error - medical_target
        print(f"📈 验证误差距离医疗级还需{remaining:.3f}mm")
    
    # 保存结果
    experiment_results = {
        "experiment_timestamp": datetime.now().isoformat(),
        "experiment_type": "additional_few_shot_methods",
        "methods_tested": [
            "relation_network",
            "matching_network", 
            "gradient_meta_learning",
            "transfer_learning"
        ],
        "results": results,
        "best_method": best_method,
        "best_val_error": float(best_val_error),
        "best_test_error": float(best_test_error),
        "historical_comparison": historical_best,
        "medical_target": medical_target,
        "medical_achieved": best_val_error <= medical_target
    }
    
    results_dir = Path("results/additional_few_shot")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = results_dir / f"additional_few_shot_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(experiment_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 实验结果已保存: {results_file}")
    
    return trainer, results

if __name__ == "__main__":
    trainer, results = run_additional_few_shot_experiments()
