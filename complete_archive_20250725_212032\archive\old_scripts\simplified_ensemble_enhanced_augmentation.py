#!/usr/bin/env python3
"""
简化集成双Softmax + 增强数据增强
减少模型复杂度，优化数据增强策略
目标: 突破6.048mm，接近5.829mm
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class SimplifiedDoubleSoftMax(nn.Module):
    """简化的双Softmax机制"""
    
    def __init__(self, threshold_ratio=0.15, temperature=2.0, weight_ratio=0.75):
        super(SimplifiedDoubleSoftMax, self).__init__()
        
        self.threshold_ratio = threshold_ratio
        self.temperature = temperature
        self.weight_ratio = weight_ratio
        
        # 简化的权重网络 (减少参数)
        self.weight_net = nn.Sequential(
            nn.Linear(3, 32),
            nn.ReLU(),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 1)
        )
        
    def forward(self, points, predicted_keypoint):
        """简化的双Softmax权重计算"""
        # 计算相对位置
        relative_pos = points - predicted_keypoint.unsqueeze(0)
        
        # 第一个Softmax - 基于距离的权重
        distances = torch.norm(relative_pos, dim=1)
        distance_weights = F.softmax(-distances**2 / (2 * self.temperature**2), dim=0)
        
        # 第二个Softmax - 基于简化神经网络的权重
        if len(relative_pos) > 1:
            nn_weights = self.weight_net(relative_pos).squeeze(-1)
            nn_weights = F.softmax(nn_weights / self.temperature, dim=0)
        else:
            nn_weights = torch.ones_like(distance_weights)
        
        # 权重组合
        combined_weights = self.weight_ratio * distance_weights + (1 - self.weight_ratio) * nn_weights
        
        # 简化的阈值过滤
        threshold = torch.quantile(combined_weights, 1 - self.threshold_ratio)
        filter_mask = combined_weights >= threshold
        
        # 确保至少保留一些点
        if filter_mask.sum() < 3:
            _, top_indices = torch.topk(combined_weights, min(5, len(combined_weights)))
            filter_mask = torch.zeros_like(combined_weights, dtype=torch.bool)
            filter_mask[top_indices] = True
        
        # 重新归一化
        filtered_weights = combined_weights * filter_mask.float()
        sum_weights = torch.sum(filtered_weights)
        
        if sum_weights > 1e-8:
            final_weights = filtered_weights / sum_weights
        else:
            final_weights = combined_weights / combined_weights.sum()
        
        # 加权平均得到精细化关键点
        refined_keypoint = torch.sum(final_weights.unsqueeze(-1) * points, dim=0)
        
        return refined_keypoint

class SimplifiedEnsemblePointNet(nn.Module):
    """简化的集成双Softmax PointNet"""
    
    def __init__(self, num_keypoints: int, dropout_rate: float = 0.2, num_ensembles: int = 2):
        super(SimplifiedEnsemblePointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        self.num_ensembles = num_ensembles
        
        # 简化的基线架构 (减少参数)
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
        # 简化的全连接层
        self.fc1 = nn.Linear(512, 256)
        self.fc2 = nn.Linear(256, 128)
        self.fc3 = nn.Linear(128, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(256)
        self.bn_fc2 = nn.BatchNorm1d(128)
        
        self.dropout = nn.Dropout(dropout_rate)
        
        # 简化的集成双Softmax (只用2个模块)
        self.double_softmax_modules = nn.ModuleList([
            SimplifiedDoubleSoftMax(
                threshold_ratio=0.12 + 0.06 * i,    # 0.12, 0.18
                temperature=1.8 + 0.4 * i,          # 1.8, 2.2
                weight_ratio=0.7 + 0.1 * i          # 0.7, 0.8
            ) for i in range(num_ensembles)
        ])
        
        print(f"🧠 简化集成双Softmax PointNet: {num_keypoints}个关键点")
        print(f"   集成数量: {num_ensembles}个双Softmax模块 (简化)")
        print(f"   架构简化: 减少参数，避免过拟合")
        
    def forward(self, x):
        batch_size = x.size(0)
        x_input = x.transpose(2, 1)
        
        # 简化的前向传播
        x1 = torch.relu(self.bn1(self.conv1(x_input)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        x4 = torch.relu(self.bn4(self.conv4(x3)))
        
        global_feat = torch.max(x4, 2)[0]
        
        feat = torch.relu(self.bn_fc1(self.fc1(global_feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc2(self.fc2(feat)))
        feat = self.dropout(feat)
        feat = self.fc3(feat)
        
        keypoints = feat.view(batch_size, self.num_keypoints, 3)
        
        # 推理时应用简化的集成双Softmax精细化
        if not self.training:
            keypoints = self.apply_simplified_ensemble_refinement(x, keypoints)
        
        return keypoints
    
    def apply_simplified_ensemble_refinement(self, points, predicted_keypoints):
        """应用简化的集成双Softmax精细化"""
        batch_size = points.shape[0]
        refined_keypoints = []
        
        for b in range(batch_size):
            batch_points = points[b]
            batch_keypoints = predicted_keypoints[b]
            
            batch_refined = []
            for k in range(self.num_keypoints):
                kp_pred = batch_keypoints[k]
                
                # 减少候选点数量
                distances = torch.norm(batch_points - kp_pred.unsqueeze(0), dim=1)
                K = min(128, batch_points.shape[0])  # 减少到128个候选点
                _, nearest_indices = torch.topk(distances, K, largest=False)
                candidate_points = batch_points[nearest_indices]
                
                # 集成2个双Softmax的结果
                ensemble_results = []
                for softmax_module in self.double_softmax_modules:
                    refined_kp = softmax_module(candidate_points, kp_pred)
                    ensemble_results.append(refined_kp)
                
                # 简单平均集成
                ensemble_keypoint = torch.stack(ensemble_results).mean(dim=0)
                batch_refined.append(ensemble_keypoint)
            
            refined_keypoints.append(torch.stack(batch_refined))
        
        return torch.stack(refined_keypoints)

class EnhancedAugmentationDataset(Dataset):
    """增强数据增强的数据集"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points
        self.augment = augment
        self.split = split
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
    
    def __len__(self):
        return len(self.sample_ids)
    
    def apply_enhanced_augmentation(self, point_cloud, keypoints):
        """增强的数据增强策略"""
        
        # 1. 旋转增强 (增加概率和范围)
        if np.random.random() < 0.8:  # 增加到80%
            # 多轴旋转
            if np.random.random() < 0.5:
                # Z轴旋转 (主要)
                angle = np.random.uniform(-0.1, 0.1)  # 稍微增加范围
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
            else:
                # X或Y轴小幅旋转
                axis = np.random.choice(['x', 'y'])
                angle = np.random.uniform(-0.05, 0.05)  # 小幅旋转
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                if axis == 'x':
                    rotation = np.array([[1, 0, 0], [0, cos_a, -sin_a], [0, sin_a, cos_a]])
                else:
                    rotation = np.array([[cos_a, 0, sin_a], [0, 1, 0], [-sin_a, 0, cos_a]])
            
            point_cloud = point_cloud @ rotation.T
            keypoints = keypoints @ rotation.T
        
        # 2. 平移增强 (增加概率)
        if np.random.random() < 0.7:  # 增加到70%
            translation = np.random.uniform(-0.5, 0.5, 3)  # 稍微增加范围
            point_cloud += translation
            keypoints += translation
        
        # 3. 缩放增强 (增加概率和范围)
        if np.random.random() < 0.6:  # 增加到60%
            # 各向异性缩放
            if np.random.random() < 0.3:
                scale = np.random.uniform(0.98, 1.02, 3)  # 各轴不同缩放
            else:
                scale_factor = np.random.uniform(0.98, 1.02)
                scale = np.array([scale_factor, scale_factor, scale_factor])  # 等比缩放
            
            point_cloud *= scale
            keypoints *= scale
        
        # 4. 噪声增强 (增加多样性)
        if np.random.random() < 0.7:  # 增加到70%
            noise_type = np.random.choice(['gaussian', 'uniform'])
            noise_level = np.random.choice([0.01, 0.02, 0.03, 0.04, 0.05])  # 增加噪声选择
            
            if noise_type == 'gaussian':
                noise = np.random.normal(0, noise_level, point_cloud.shape)
            else:
                noise = np.random.uniform(-noise_level, noise_level, point_cloud.shape)
            
            point_cloud += noise
        
        # 5. 点云采样增强 (新增)
        if np.random.random() < 0.4:  # 40%概率
            # 随机丢弃一些点
            dropout_ratio = np.random.uniform(0.05, 0.15)  # 丢弃5-15%的点
            keep_ratio = 1 - dropout_ratio
            num_keep = int(len(point_cloud) * keep_ratio)
            
            if num_keep >= self.num_points:
                indices = np.random.choice(len(point_cloud), num_keep, replace=False)
                point_cloud = point_cloud[indices]
        
        # 6. 局部扰动 (新增)
        if np.random.random() < 0.3:  # 30%概率
            # 对部分点进行局部扰动
            num_perturb = int(len(point_cloud) * 0.1)  # 扰动10%的点
            perturb_indices = np.random.choice(len(point_cloud), num_perturb, replace=False)
            perturb_noise = np.random.normal(0, 0.02, (num_perturb, 3))
            point_cloud[perturb_indices] += perturb_noise
        
        return point_cloud, keypoints
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        # 数据增强 (训练时)
        if self.augment and self.split == 'train':
            point_cloud, keypoints = self.apply_enhanced_augmentation(point_cloud, keypoints)
        
        # 点云采样
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        elif len(point_cloud) < self.num_points:
            # 如果点数不够，进行重采样
            indices = np.random.choice(len(point_cloud), self.num_points, replace=True)
            point_cloud = point_cloud[indices]
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def test_simplified_model():
    """测试简化模型"""
    
    print("🧪 **测试简化集成双Softmax模型**")
    print("🎯 **简化架构 + 增强数据增强**")
    print("=" * 80)
    
    batch_size = 4
    num_points = 4096
    num_keypoints = 12
    
    # 创建测试数据
    test_input = torch.randn(batch_size, num_points, 3)
    
    print(f"📊 测试输入: {test_input.shape}")
    
    # 测试模型
    model = SimplifiedEnsemblePointNet(num_keypoints=num_keypoints, num_ensembles=2)
    
    with torch.no_grad():
        # 训练模式
        model.train()
        output_train = model(test_input)
        print(f"   训练模式输出: {output_train.shape}")
        
        # 推理模式
        model.eval()
        output_eval = model(test_input)
        print(f"   推理模式输出: {output_eval.shape}")
    
    # 参数统计
    total_params = sum(p.numel() for p in model.parameters())
    print(f"\n📊 模型参数: {total_params:,}")
    
    # 测试数据增强
    print(f"\n🔍 测试增强数据增强:")
    dataset = EnhancedAugmentationDataset('f3_reduced_12kp_stable.npz', 'train', 
                                        num_points=4096, augment=True, seed=42)
    print(f"   数据集大小: {len(dataset)}")
    
    sample = dataset[0]
    print(f"   样本形状: {sample['point_cloud'].shape}, {sample['keypoints'].shape}")
    
    print(f"\n✅ 简化模型测试通过!")
    
    return model

if __name__ == "__main__":
    set_seed(42)
    
    print("🚀 **简化集成双Softmax + 增强数据增强**")
    print("🔧 **简化**: 减少参数，避免过拟合")
    print("📈 **增强**: 更丰富的数据增强策略")
    print("🎯 **目标**: 突破6.048mm，接近5.829mm")
    print("=" * 80)
    
    # 测试模型
    model = test_simplified_model()
    
    print(f"\n🎉 **简化模型准备完成!**")
    print("=" * 50)
    print(f"🔬 核心优化:")
    print(f"   1. 模型简化: 2个集成模块，减少参数")
    print(f"   2. 数据增强: 6种增强策略，增加多样性")
    print(f"   3. 候选点减少: 128个 (vs 256个)")
    print(f"🎯 预期改进:")
    print(f"   - 减少过拟合风险")
    print(f"   - 提高泛化能力")
    print(f"   - 更稳定的训练")
