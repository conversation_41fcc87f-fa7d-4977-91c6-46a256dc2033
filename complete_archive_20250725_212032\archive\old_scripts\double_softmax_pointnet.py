#!/usr/bin/env python3
"""
双Softmax机制的PointNet实现
基于论文"Paying attention to the minute details"中的双Softmax机制
结合12关键点成功配置
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class DoubleSoftMaxMechanism(nn.Module):
    """
    双Softmax机制实现
    基于论文中的DS (Double SoftMax) weighting mechanism
    """
    
    def __init__(self, feature_dim=128, threshold_ratio=0.1, temperature=1.0):
        super(DoubleSoftMaxMechanism, self).__init__()
        
        self.feature_dim = feature_dim
        self.threshold_ratio = threshold_ratio  # 阈值比例
        self.temperature = temperature  # 温度参数
        
        # 特征提取网络
        self.feature_net = nn.Sequential(
            nn.Linear(3, 64),  # 输入点坐标
            nn.ReLU(),
            nn.Linear(64, 128),
            nn.ReLU(),
            nn.Linear(128, feature_dim)
        )
        
        # 权重计算网络
        self.weight_net = nn.Sequential(
            nn.Linear(feature_dim, 64),
            nn.ReL<PERSON>(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1)  # 输出单个权重值
        )
        
        print(f"🎯 双Softmax机制初始化:")
        print(f"   特征维度: {feature_dim}")
        print(f"   阈值比例: {threshold_ratio}")
        print(f"   温度参数: {temperature}")
    
    def forward(self, points):
        """
        双Softmax权重计算
        
        Args:
            points: [batch_size, num_points, 3] 区域内的点坐标
            
        Returns:
            weights: [batch_size, num_points] 双Softmax权重
            filtered_mask: [batch_size, num_points] 过滤掩码
        """
        batch_size, num_points, _ = points.shape
        
        # 1. 特征提取
        features = self.feature_net(points)  # [batch_size, num_points, feature_dim]
        
        # 2. 第一个Softmax - 计算初始权重
        raw_weights = self.weight_net(features).squeeze(-1)  # [batch_size, num_points]
        first_softmax_weights = F.softmax(raw_weights / self.temperature, dim=-1)
        
        # 3. 阈值过滤 - 基于权重分布动态确定阈值
        # 计算每个batch的阈值 (平均权重的一定比例)
        mean_weights = torch.mean(first_softmax_weights, dim=-1, keepdim=True)  # [batch_size, 1]
        threshold = mean_weights * self.threshold_ratio
        
        # 创建过滤掩码
        filter_mask = first_softmax_weights > threshold  # [batch_size, num_points]
        
        # 4. 第二个Softmax - 重新分配权重
        # 对过滤后的权重重新归一化
        filtered_weights = first_softmax_weights * filter_mask.float()
        
        # 避免所有权重都被过滤的情况
        sum_filtered = torch.sum(filtered_weights, dim=-1, keepdim=True)
        safe_mask = sum_filtered > 1e-8
        
        # 重新归一化
        second_softmax_weights = torch.where(
            safe_mask,
            filtered_weights / (sum_filtered + 1e-8),
            first_softmax_weights  # 如果过滤后没有权重，使用原始权重
        )
        
        return second_softmax_weights, filter_mask

class DoubleSoftMaxPointNet(nn.Module):
    """
    集成双Softmax机制的PointNet
    基于12关键点成功配置 + 双Softmax改进
    """
    
    def __init__(self, num_keypoints: int, dropout_rate: float = 0.4):
        super(DoubleSoftMaxPointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 基础PointNet特征提取 (保持成功配置)
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 残差连接
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        # 双Softmax机制 (用于精细化关键点定位)
        self.double_softmax = DoubleSoftMaxMechanism(
            feature_dim=128, 
            threshold_ratio=0.15,  # 可调参数
            temperature=2.0        # 可调参数
        )
        
        # 全局特征处理
        self.global_fc1 = nn.Linear(1024, 512)
        self.global_fc2 = nn.Linear(512, 256)
        
        # 每个关键点的特征处理
        self.keypoint_fc = nn.ModuleList([
            nn.Sequential(
                nn.Linear(256, 128),
                nn.ReLU(),
                nn.Dropout(dropout_rate),
                nn.Linear(128, 64),
                nn.ReLU(),
                nn.Dropout(dropout_rate),
                nn.Linear(64, 3)  # 输出3D坐标
            ) for _ in range(num_keypoints)
        ])
        
        self.dropout = nn.Dropout(dropout_rate)
        
        print(f"🧠 双Softmax PointNet: {num_keypoints}个关键点")
        print(f"   - 基础PointNet特征提取")
        print(f"   - 双Softmax精细化定位")
        print(f"   - 独立关键点回归头")
        
    def forward(self, x):
        batch_size = x.size(0)
        num_points = x.size(1)
        x = x.transpose(2, 1)  # [batch, 3, num_points]
        
        # 基础特征提取
        x1 = torch.relu(self.bn1(self.conv1(x)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        
        # 残差连接
        x3_res = x3 + self.residual1(x1)
        
        x4 = torch.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = torch.relu(self.bn5(self.conv5(x4_res)))
        
        # 全局特征
        global_feat = torch.max(x5, 2)[0]  # [batch, 1024]
        
        # 全局特征处理
        global_feat = torch.relu(self.global_fc1(global_feat))
        global_feat = self.dropout(global_feat)
        global_feat = torch.relu(self.global_fc2(global_feat))
        global_feat = self.dropout(global_feat)
        
        # 为每个关键点独立预测
        keypoints = []
        
        for i in range(self.num_keypoints):
            # 基础预测
            kp_feat = self.keypoint_fc[i](global_feat)  # [batch, 3]
            keypoints.append(kp_feat)
        
        # 堆叠所有关键点
        keypoints = torch.stack(keypoints, dim=1)  # [batch, num_keypoints, 3]
        
        # 可选：应用双Softmax机制进行精细化 (在推理时)
        if not self.training:
            keypoints = self.apply_double_softmax_refinement(x.transpose(2, 1), keypoints)
        
        return keypoints
    
    def apply_double_softmax_refinement(self, points, predicted_keypoints):
        """
        应用双Softmax机制进行关键点精细化
        
        Args:
            points: [batch, num_points, 3] 原始点云
            predicted_keypoints: [batch, num_keypoints, 3] 预测的关键点
            
        Returns:
            refined_keypoints: [batch, num_keypoints, 3] 精细化后的关键点
        """
        batch_size = points.shape[0]
        refined_keypoints = []
        
        for i in range(self.num_keypoints):
            kp_pred = predicted_keypoints[:, i, :]  # [batch, 3]
            
            # 为每个关键点找到邻近区域
            distances = torch.norm(points - kp_pred.unsqueeze(1), dim=2)  # [batch, num_points]
            
            # 选择最近的K个点作为候选区域
            K = min(512, points.shape[1])  # 动态调整K值
            _, nearest_indices = torch.topk(distances, K, largest=False, dim=1)
            
            # 提取候选区域的点
            batch_indices = torch.arange(batch_size).unsqueeze(1).expand(-1, K)
            candidate_points = points[batch_indices, nearest_indices]  # [batch, K, 3]
            
            # 应用双Softmax机制
            weights, filter_mask = self.double_softmax(candidate_points)
            
            # 加权平均计算精细化关键点
            refined_kp = torch.sum(weights.unsqueeze(-1) * candidate_points, dim=1)
            refined_keypoints.append(refined_kp)
        
        refined_keypoints = torch.stack(refined_keypoints, dim=1)
        return refined_keypoints

class AdaptiveDoubleSoftMaxPointNet(nn.Module):
    """
    自适应双Softmax PointNet
    动态调整双Softmax参数
    """
    
    def __init__(self, num_keypoints: int, dropout_rate: float = 0.4):
        super(AdaptiveDoubleSoftMaxPointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 基础网络 (复用成功配置)
        self.base_net = DoubleSoftMaxPointNet(num_keypoints, dropout_rate)
        
        # 参数自适应网络
        self.param_net = nn.Sequential(
            nn.Linear(1024, 256),
            nn.ReLU(),
            nn.Linear(256, 64),
            nn.ReLU(),
            nn.Linear(64, 2)  # 输出threshold_ratio和temperature
        )
        
        print(f"🔄 自适应双Softmax PointNet: {num_keypoints}个关键点")
        print(f"   - 动态参数调整")
        print(f"   - 自适应阈值和温度")
    
    def forward(self, x):
        # 先通过基础网络获取全局特征
        batch_size = x.size(0)
        x_conv = x.transpose(2, 1)
        
        # 提取全局特征用于参数预测
        x1 = torch.relu(self.base_net.bn1(self.base_net.conv1(x_conv)))
        x2 = torch.relu(self.base_net.bn2(self.base_net.conv2(x1)))
        x3 = torch.relu(self.base_net.bn3(self.base_net.conv3(x2)))
        x3_res = x3 + self.base_net.residual1(x1)
        x4 = torch.relu(self.base_net.bn4(self.base_net.conv4(x3_res)))
        x4_res = x4 + self.base_net.residual2(x2)
        x5 = torch.relu(self.base_net.bn5(self.base_net.conv5(x4_res)))
        global_feat = torch.max(x5, 2)[0]
        
        # 预测自适应参数
        adaptive_params = torch.sigmoid(self.param_net(global_feat))  # [batch, 2]
        threshold_ratio = 0.05 + 0.2 * adaptive_params[:, 0]  # 范围 [0.05, 0.25]
        temperature = 0.5 + 3.0 * adaptive_params[:, 1]       # 范围 [0.5, 3.5]
        
        # 使用自适应参数更新双Softmax机制
        for i in range(batch_size):
            self.base_net.double_softmax.threshold_ratio = threshold_ratio[i].item()
            self.base_net.double_softmax.temperature = temperature[i].item()
        
        # 前向传播
        return self.base_net(x)

def test_double_softmax_models():
    """测试双Softmax模型"""
    
    print("🧪 **测试双Softmax模型**")
    print("=" * 50)
    
    batch_size = 4
    num_points = 4096
    num_keypoints = 12
    
    # 创建测试数据
    test_input = torch.randn(batch_size, num_points, 3)
    
    print(f"📊 测试输入: {test_input.shape}")
    
    # 测试双Softmax机制
    print(f"\n🔍 测试双Softmax机制:")
    ds_mechanism = DoubleSoftMaxMechanism(feature_dim=128)
    
    # 测试小区域
    test_region = torch.randn(batch_size, 100, 3)
    with torch.no_grad():
        weights, mask = ds_mechanism(test_region)
        print(f"   区域大小: {test_region.shape}")
        print(f"   权重形状: {weights.shape}")
        print(f"   过滤比例: {mask.float().mean().item():.2%}")
        print(f"   权重和: {weights.sum(dim=1).mean().item():.4f}")
    
    # 测试双Softmax PointNet
    print(f"\n🔍 测试双Softmax PointNet:")
    ds_model = DoubleSoftMaxPointNet(num_keypoints=num_keypoints)
    
    with torch.no_grad():
        output1 = ds_model(test_input)
        print(f"   输出形状: {output1.shape}")
        print(f"   参数数量: {sum(p.numel() for p in ds_model.parameters()):,}")
    
    # 测试自适应双Softmax PointNet
    print(f"\n🔍 测试自适应双Softmax PointNet:")
    adaptive_model = AdaptiveDoubleSoftMaxPointNet(num_keypoints=num_keypoints)
    
    with torch.no_grad():
        output2 = adaptive_model(test_input)
        print(f"   输出形状: {output2.shape}")
        print(f"   参数数量: {sum(p.numel() for p in adaptive_model.parameters()):,}")
    
    print(f"\n✅ 所有双Softmax模型测试通过!")
    
    return ds_model, adaptive_model

if __name__ == "__main__":
    ds_model, adaptive_model = test_double_softmax_models()
