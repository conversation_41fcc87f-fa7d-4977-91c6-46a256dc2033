#!/usr/bin/env python3
"""
Test F3 Component Alignment

Specifically test the F3 component alignment between STL and CSV annotations
to verify if F3 uses a local coordinate system starting from origin.
"""

import numpy as np
import pandas as pd
from pathlib import Path
import struct
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def load_annotation_file(csv_path: str):
    """Load annotation CSV file with proper encoding"""
    try:
        df = pd.read_csv(csv_path, encoding='gbk')
    except:
        try:
            df = pd.read_csv(csv_path, encoding='utf-8')
        except:
            df = pd.read_csv(csv_path, encoding='latin-1')
    
    keypoints = df[['X', 'Y', 'Z']].values
    labels = df['label'].values.tolist()
    
    return keypoints, labels

def read_stl_binary_complete(stl_path: str):
    """Complete binary STL reader"""
    try:
        with open(stl_path, 'rb') as f:
            # Skip header (80 bytes)
            f.read(80)
            
            # Read number of triangles (4 bytes)
            num_triangles = struct.unpack('<I', f.read(4))[0]
            
            # Read all vertices
            vertices = []
            
            for i in range(num_triangles):
                # Skip normal vector (3 floats = 12 bytes)
                f.read(12)
                
                # Read 3 vertices (9 floats = 36 bytes)
                for j in range(3):
                    x, y, z = struct.unpack('<fff', f.read(12))
                    vertices.append([x, y, z])
                
                # Skip attribute (2 bytes)
                f.read(2)
            
            vertices = np.array(vertices)
            
            # Remove duplicate vertices
            unique_vertices = np.unique(vertices, axis=0)
            
            return unique_vertices
            
    except Exception as e:
        print(f"      ❌ STL读取失败: {e}")
        return None

def extract_f3_keypoints(keypoints, labels):
    """Extract F3 keypoints from the full annotation"""
    
    f3_indices = [i for i, label in enumerate(labels) if label.startswith('F_3')]
    f3_keypoints = keypoints[f3_indices]
    f3_labels = [labels[i] for i in f3_indices]
    
    return f3_keypoints, f3_labels, f3_indices

def test_f3_alignment(sample_id: str, coord_system: str = 'XYZ'):
    """Test F3 component alignment specifically"""
    
    print(f"\n🔍 **测试样本 {sample_id} 的F3部件对齐**")
    print("=" * 60)
    
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    annotations_dir = data_dir / "annotations"
    stl_dir = data_dir / "stl_models"
    
    # Load annotation file
    csv_file = annotations_dir / f"{sample_id}-Table-{coord_system}.CSV"
    
    if not csv_file.exists():
        print(f"   ❌ 标注文件不存在")
        return None
    
    try:
        keypoints, labels = load_annotation_file(str(csv_file))
    except Exception as e:
        print(f"   ❌ 标注加载失败: {e}")
        return None
    
    # Extract F3 keypoints
    f3_keypoints, f3_labels, f3_indices = extract_f3_keypoints(keypoints, labels)
    
    print(f"   📊 F3关键点信息:")
    print(f"      关键点数量: {len(f3_keypoints)}")
    print(f"      关键点索引: {f3_indices}")
    
    # Load F3 STL file
    f3_stl_file = stl_dir / f"{sample_id}-F_3.stl"
    
    if not f3_stl_file.exists():
        print(f"   ❌ F3 STL文件不存在")
        return None
    
    f3_stl_vertices = read_stl_binary_complete(str(f3_stl_file))
    
    if f3_stl_vertices is None:
        print(f"   ❌ F3 STL读取失败")
        return None
    
    print(f"   📊 F3 STL信息:")
    print(f"      STL顶点数: {len(f3_stl_vertices)}")
    
    # Analyze F3 keypoints
    f3_center = np.mean(f3_keypoints, axis=0)
    f3_min = np.min(f3_keypoints, axis=0)
    f3_max = np.max(f3_keypoints, axis=0)
    f3_range = np.ptp(f3_keypoints, axis=0)
    
    print(f"\n   📋 **F3关键点分析**:")
    print(f"      中心坐标: [{f3_center[0]:.1f}, {f3_center[1]:.1f}, {f3_center[2]:.1f}]")
    print(f"      最小坐标: [{f3_min[0]:.1f}, {f3_min[1]:.1f}, {f3_min[2]:.1f}]")
    print(f"      最大坐标: [{f3_max[0]:.1f}, {f3_max[1]:.1f}, {f3_max[2]:.1f}]")
    print(f"      坐标范围: [{f3_range[0]:.1f}, {f3_range[1]:.1f}, {f3_range[2]:.1f}] mm")
    
    # Analyze F3 STL
    stl_center = np.mean(f3_stl_vertices, axis=0)
    stl_min = np.min(f3_stl_vertices, axis=0)
    stl_max = np.max(f3_stl_vertices, axis=0)
    stl_range = np.ptp(f3_stl_vertices, axis=0)
    
    print(f"\n   📋 **F3 STL分析**:")
    print(f"      中心坐标: [{stl_center[0]:.1f}, {stl_center[1]:.1f}, {stl_center[2]:.1f}]")
    print(f"      最小坐标: [{stl_min[0]:.1f}, {stl_min[1]:.1f}, {stl_min[2]:.1f}]")
    print(f"      最大坐标: [{stl_max[0]:.1f}, {stl_max[1]:.1f}, {stl_max[2]:.1f}]")
    print(f"      坐标范围: [{stl_range[0]:.1f}, {stl_range[1]:.1f}, {stl_range[2]:.1f}] mm")
    
    # Check if STL starts near origin
    stl_origin_dist = np.linalg.norm(stl_min)
    print(f"      STL最小点距原点: {stl_origin_dist:.1f}mm")
    
    if stl_origin_dist < 5:
        print(f"      ✅ STL可能使用局部坐标系 (接近原点)")
    else:
        print(f"      ⚠️ STL不是从原点开始")
    
    # Compare scales
    scale_ratio = stl_range / f3_range
    scale_ratio = np.where(f3_range > 0, scale_ratio, 1.0)
    
    print(f"\n   🎯 **对齐分析**:")
    print(f"      尺度比例: [{scale_ratio[0]:.3f}, {scale_ratio[1]:.3f}, {scale_ratio[2]:.3f}]")
    
    # Check if scales are similar
    scale_consistent = np.all(np.abs(scale_ratio - 1.0) < 0.3)
    if scale_consistent:
        print(f"      ✅ 尺度基本一致")
    else:
        print(f"      ⚠️ 尺度差异较大")
    
    # Calculate surface projection quality
    distances = []
    for kp in f3_keypoints:
        dists = np.linalg.norm(f3_stl_vertices - kp, axis=1)
        min_dist = np.min(dists)
        distances.append(min_dist)
    
    distances = np.array(distances)
    mean_distance = np.mean(distances)
    within_5mm = np.sum(distances <= 5.0) / len(distances) * 100
    within_10mm = np.sum(distances <= 10.0) / len(distances) * 100
    
    print(f"\n   📊 **表面投影质量**:")
    print(f"      平均距离: {mean_distance:.2f}mm")
    print(f"      最大距离: {np.max(distances):.2f}mm")
    print(f"      ≤5mm: {within_5mm:.1f}%")
    print(f"      ≤10mm: {within_10mm:.1f}%")
    
    # Overall assessment
    print(f"\n   📋 **F3对齐评估**:")
    
    alignment_score = 0
    issues = []
    
    if stl_origin_dist < 5:
        alignment_score += 30
        print(f"      ✅ STL使用局部坐标系")
    else:
        issues.append("STL不从原点开始")
    
    if scale_consistent:
        alignment_score += 30
        print(f"      ✅ 尺度基本一致")
    else:
        issues.append("尺度不一致")
    
    if mean_distance < 20:
        alignment_score += 40
        print(f"      ✅ 表面投影可接受")
    else:
        issues.append("表面投影距离过大")
    
    print(f"      对齐得分: {alignment_score}/100")
    
    if alignment_score >= 70:
        print(f"      🎉 F3对齐质量良好，可以用于单部件测试!")
        recommendation = "proceed_with_f3"
    elif alignment_score >= 40:
        print(f"      ⚠️ F3对齐质量一般，可以尝试但需要调整")
        recommendation = "try_with_caution"
    else:
        print(f"      ❌ F3对齐质量不足")
        recommendation = "alignment_failed"
    
    if issues:
        print(f"      问题: {', '.join(issues)}")
    
    return {
        'sample_id': sample_id,
        'f3_keypoints': f3_keypoints,
        'f3_stl_vertices': f3_stl_vertices,
        'f3_labels': f3_labels,
        'alignment_score': alignment_score,
        'mean_surface_distance': mean_distance,
        'within_5mm_percent': within_5mm,
        'scale_ratio': scale_ratio.tolist(),
        'stl_origin_distance': stl_origin_dist,
        'recommendation': recommendation,
        'issues': issues
    }

def test_multiple_f3_samples():
    """Test F3 alignment for multiple samples"""
    
    print("🔍 **F3部件多样本对齐测试**")
    print("🎯 **目标: 找到最佳的F3对齐样本**")
    print("=" * 80)
    
    # Test the samples we identified as having F3 closest to origin
    test_samples = ['600100', '600050', '600114', '600076', '600001']
    
    results = []
    
    for sample_id in test_samples:
        result = test_f3_alignment(sample_id, 'XYZ')
        if result:
            results.append(result)
    
    # Find the best F3 alignment
    if results:
        print(f"\n📋 **F3对齐结果总结**")
        print("=" * 60)
        
        best_result = None
        best_score = 0
        
        for result in results:
            sample_id = result['sample_id']
            score = result['alignment_score']
            distance = result['mean_surface_distance']
            
            print(f"   样本 {sample_id}: 得分 {score}/100, 平均距离 {distance:.2f}mm")
            
            if score > best_score:
                best_score = score
                best_result = result
        
        if best_result:
            print(f"\n🏆 **最佳F3样本: {best_result['sample_id']}**")
            print(f"   对齐得分: {best_result['alignment_score']}/100")
            print(f"   表面距离: {best_result['mean_surface_distance']:.2f}mm")
            print(f"   5mm精度: {best_result['within_5mm_percent']:.1f}%")
            print(f"   建议: {best_result['recommendation']}")
            
            if best_result['recommendation'] == 'proceed_with_f3':
                print(f"\n🎯 **可以开始F3单部件训练!**")
                print(f"   1. 使用样本 {best_result['sample_id']} 验证流程")
                print(f"   2. 创建F3单部件数据集")
                print(f"   3. 训练F3关键点检测模型")
                print(f"   4. 验证F3标注质量")
        
        return results
    
    return None

def main():
    """Main F3 testing function"""
    
    # Test F3 alignment for multiple samples
    results = test_multiple_f3_samples()
    
    if results:
        # Save results
        import json
        with open('f3_alignment_test_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📁 F3对齐测试结果已保存: f3_alignment_test_results.json")
    
    print(f"\n" + "="*80)
    print(f"📋 **F3测试结论**")
    print(f"=" * 80)
    
    if results:
        good_alignments = [r for r in results if r['alignment_score'] >= 70]
        
        if good_alignments:
            print(f"\n🎉 **成功发现良好的F3对齐!**")
            print(f"   良好对齐样本: {len(good_alignments)}/{len(results)}")
            print(f"   可以开始F3单部件数据集创建和模型训练")
        else:
            print(f"\n⚠️ **F3对齐质量一般**")
            print(f"   可能需要进一步调整或寻找其他解决方案")
    else:
        print(f"\n❌ **F3测试失败**")
        print(f"   需要重新考虑策略")

if __name__ == "__main__":
    main()
