#!/usr/bin/env python3
"""
聚焦热力图可视化
保持完整点云显示，但让热力图更聚焦在关键点周围
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps

# 关键点名称
KEYPOINT_NAMES = {
    0: "L-ASIS", 1: "R-ASIS", 2: "L-PSIS", 3: "R-PSIS",
    4: "L-IC", 5: "R-IC", 6: "SP", 7: "L-SIJ", 8: "R-SIJ",
    9: "L-IS", 10: "R-IS", 11: "CT"
}

def create_medical_colormap():
    """创建医学风格的颜色映射"""
    colors = [
        '#F0F8FF',  # 浅蓝白色 (最低置信度)
        '#E6F3FF',  # 很浅蓝色
        '#CCE7FF',  # 浅蓝色
        '#99D6FF',  # 中浅蓝色
        '#66C2FF',  # 中蓝色
        '#33AAFF',  # 深蓝色
        '#0088FF',  # 更深蓝色
        '#FF6600',  # 橙色 (高置信度)
        '#FF3300',  # 红色
        '#FF0000'   # 深红色 (最高置信度)
    ]
    return LinearSegmentedColormap.from_list('medical_heatmap', colors, N=256)

def create_focused_heatmap(point_cloud, pred_keypoint, sigma=3.0):
    """创建更聚焦的热力图，基于预测关键点位置"""
    
    # 计算到预测关键点的距离
    distances = np.linalg.norm(point_cloud - pred_keypoint, axis=1)
    
    # 使用更小的sigma值创建更聚焦的高斯分布
    focused_heatmap = np.exp(-distances**2 / (2 * sigma**2))
    
    # 适度增强聚焦效果 - 保持靶子的可见性
    focused_heatmap = np.power(focused_heatmap, 1.2)  # 轻微增强，保持渐变效果
    
    # 归一化
    if np.max(focused_heatmap) > 0:
        focused_heatmap = focused_heatmap / np.max(focused_heatmap)
    
    return focused_heatmap

def create_focused_visualization(point_cloud, true_keypoint, pred_keypoint, 
                               confidence, kp_idx, sample_id):
    """创建聚焦的可视化，保持完整点云但热力图更聚焦"""
    
    print(f"🎯 Creating focused visualization for {KEYPOINT_NAMES[kp_idx]}")
    
    medical_cmap = create_medical_colormap()
    
    fig = plt.figure(figsize=(20, 6))
    
    # 三种聚焦程度 - 调整为更合理的范围
    sigma_values = [12.0, 8.0, 5.0]  # 从宽松到适度聚焦，保持靶子效果
    titles = ["Wide Target", "Medium Target", "Focused Target"]
    
    for i, (sigma, title) in enumerate(zip(sigma_values, titles)):
        ax = fig.add_subplot(1, 3, i+1, projection='3d')
        
        # 创建聚焦的热力图
        focused_heatmap = create_focused_heatmap(point_cloud, pred_keypoint, sigma)
        
        # 随机采样以减少点数，但保持完整分布
        if len(point_cloud) > 4000:
            sample_indices = np.random.choice(len(point_cloud), 4000, replace=False)
            display_pc = point_cloud[sample_indices]
            display_heatmap = focused_heatmap[sample_indices]
        else:
            display_pc = point_cloud
            display_heatmap = focused_heatmap
        
        # 分层显示 - 调整阈值以显示更好的靶子效果
        # 1. 背景点云 - 低置信度
        background_mask = display_heatmap < 0.1
        if np.any(background_mask):
            ax.scatter(display_pc[background_mask, 0],
                      display_pc[background_mask, 1],
                      display_pc[background_mask, 2],
                      c='lightgray', s=0.5, alpha=0.3, rasterized=True)

        # 2. 外环 - 低到中等置信度
        outer_mask = (display_heatmap >= 0.1) & (display_heatmap < 0.3)
        if np.any(outer_mask):
            ax.scatter(display_pc[outer_mask, 0],
                      display_pc[outer_mask, 1],
                      display_pc[outer_mask, 2],
                      c=display_heatmap[outer_mask],
                      cmap=medical_cmap, s=2, alpha=0.6, vmin=0, vmax=1)

        # 3. 中环 - 中等置信度
        middle_mask = (display_heatmap >= 0.3) & (display_heatmap < 0.6)
        if np.any(middle_mask):
            ax.scatter(display_pc[middle_mask, 0],
                      display_pc[middle_mask, 1],
                      display_pc[middle_mask, 2],
                      c=display_heatmap[middle_mask],
                      cmap=medical_cmap, s=4, alpha=0.8, vmin=0, vmax=1)

        # 4. 内环 - 高置信度区域
        inner_mask = display_heatmap >= 0.6
        if np.any(inner_mask):
            scatter = ax.scatter(display_pc[inner_mask, 0],
                               display_pc[inner_mask, 1],
                               display_pc[inner_mask, 2],
                               c=display_heatmap[inner_mask],
                               cmap=medical_cmap, s=8, alpha=0.9, vmin=0, vmax=1)

        # 5. 靶心 - 峰值点
        peak_mask = display_heatmap >= 0.9
        if np.any(peak_mask):
            ax.scatter(display_pc[peak_mask, 0],
                      display_pc[peak_mask, 1],
                      display_pc[peak_mask, 2],
                      c='red', s=30, marker='o',
                      edgecolor='white', linewidth=2, alpha=1.0)
        
        # 6. 关键点标记
        # 真实关键点 - 黑色星形
        ax.scatter(true_keypoint[0], true_keypoint[1], true_keypoint[2],
                  c='black', s=400, marker='*', edgecolor='yellow', 
                  linewidth=3, alpha=1.0, label='Ground Truth', zorder=10)
        
        # 预测关键点 - 红色十字
        ax.scatter(pred_keypoint[0], pred_keypoint[1], pred_keypoint[2],
                  c='red', s=300, marker='x', linewidth=4, 
                  alpha=1.0, label='Predicted', zorder=10)
        
        # 连接线显示误差
        ax.plot([true_keypoint[0], pred_keypoint[0]], 
                [true_keypoint[1], pred_keypoint[1]], 
                [true_keypoint[2], pred_keypoint[2]], 
                'k--', alpha=0.7, linewidth=3)
        
        # 计算误差
        error = np.linalg.norm(pred_keypoint - true_keypoint)
        
        # 设置坐标轴范围 - 显示完整点云
        pc_min = np.min(point_cloud, axis=0)
        pc_max = np.max(point_cloud, axis=0)
        margin = 5
        
        ax.set_xlim([pc_min[0] - margin, pc_max[0] + margin])
        ax.set_ylim([pc_min[1] - margin, pc_max[1] + margin])
        ax.set_zlim([pc_min[2] - margin, pc_max[2] + margin])
        
        # 设置标题
        ax.set_title(f'{title}\n{KEYPOINT_NAMES[kp_idx]}\n'
                    f'Sigma: {sigma:.1f}mm\nError: {error:.1f}mm',
                    fontsize=14, fontweight='bold', pad=20)
        
        # 坐标轴标签
        ax.set_xlabel('X (mm)', fontsize=12)
        ax.set_ylabel('Y (mm)', fontsize=12)
        ax.set_zlabel('Z (mm)', fontsize=12)
        ax.tick_params(labelsize=10)
        
        # 设置视角
        ax.view_init(elev=20, azim=45)
        ax.grid(True, alpha=0.3)
        
        # 添加图例
        if i == 0:
            ax.legend(loc='upper right', fontsize=10)
        
        # 添加统计信息
        max_conf = np.max(display_heatmap)
        high_conf_points = np.sum(display_heatmap > 0.6)  # 调整阈值
        total_points = len(display_heatmap)

        stats_text = f'Max: {max_conf:.3f}\nInner: {high_conf_points}\nTotal: {total_points}'
        ax.text2D(0.02, 0.98, stats_text, transform=ax.transAxes, 
                 fontsize=10, verticalalignment='top',
                 bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # 添加颜色条
    if 'scatter' in locals():
        cbar = plt.colorbar(scatter, ax=fig.get_axes(), shrink=0.8, aspect=30)
        cbar.set_label('Confidence Level', fontsize=12)
    
    plt.suptitle(f'Focused Heatmap Visualization - {KEYPOINT_NAMES[kp_idx]} (Sample {sample_id})\n'
                f'Complete Point Cloud with Focused Heatmap', 
                fontsize=16, fontweight='bold')
    
    plt.tight_layout(rect=[0, 0, 0.95, 0.9])
    
    # 保存
    filename = f'focused_heatmap_{sample_id}_kp{kp_idx}_{KEYPOINT_NAMES[kp_idx]}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"   📊 Focused visualization saved: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🎯 Focused Heatmap Visualization")
    print("Complete point cloud with focused heatmap targeting")
    print("=" * 80)
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model.eval()
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # 选择一个样本进行展示
    sample_idx = 0
    sample_id = sample_ids[sample_idx]
    point_cloud = point_clouds[sample_idx]
    true_keypoints = keypoints[sample_idx]
    
    print(f"\n🎯 Processing sample: {sample_id}")
    
    # 采样点云用于预测
    if len(point_cloud) > 8192:
        indices = np.random.choice(len(point_cloud), 8192, replace=False)
        pc_sampled = point_cloud[indices]
    else:
        pc_sampled = point_cloud
    
    # 预测关键点
    pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
    
    with torch.no_grad():
        pred_heatmaps = model(pc_tensor)
    
    pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T
    pred_keypoints, confidences = extract_keypoints_from_heatmaps(
        pred_heatmaps_np.T, pc_sampled
    )
    
    # 创建聚焦可视化
    demo_keypoints = [0, 6, 11]  # L-ASIS, SP, CT
    
    for kp_idx in demo_keypoints:
        create_focused_visualization(
            point_cloud,  # 使用完整点云
            true_keypoints[kp_idx], 
            pred_keypoints[kp_idx],
            confidences[kp_idx], 
            kp_idx, 
            sample_id
        )
    
    print(f"\n🎉 Focused Heatmap Visualization Complete!")
    print("✅ Complete point cloud display")
    print("✅ Focused heatmap targeting")
    print("✅ Multiple focus levels")
    print("✅ Medical-grade visualization")

if __name__ == "__main__":
    main()
