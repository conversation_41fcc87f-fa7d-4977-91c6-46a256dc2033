#!/usr/bin/env python3
"""
修正版轻量级数据预处理
Corrected Lightweight Data Preprocessing
目标: 只做必要的对齐，保持原始尺度，在真实物理空间评估
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json
from pathlib import Path
from datetime import datetime
from sklearn.decomposition import PCA

class CorrectedLightweightPreprocessor:
    """修正版轻量级预处理器 - 保持真实物理空间"""
    
    def __init__(self, data_path='data/raw/high_quality_f3_dataset.npz'):
        self.data_path = data_path
        self.load_data()
        
    def load_data(self):
        """加载原始数据"""
        print(f"📦 加载原始数据: {self.data_path}")
        
        data = np.load(self.data_path, allow_pickle=True)
        self.sample_ids = data['sample_ids']
        self.point_clouds = data['point_clouds']
        self.keypoints = data['keypoints']
        
        print(f"✅ 数据加载完成: {len(self.sample_ids)} 样本")
        print(f"   原始点云范围: [{np.min([np.min(pc) for pc in self.point_clouds]):.1f}, {np.max([np.max(pc) for pc in self.point_clouds]):.1f}]mm")
        print(f"   原始关键点范围: [{np.min(self.keypoints):.1f}, {np.max(self.keypoints):.1f}]mm")
        
    def analyze_alignment_needs(self):
        """分析对齐需求 - 只修正明显的问题"""
        print("\n🔍 分析对齐需求...")
        
        alignment_issues = []
        
        for i, (pc, kp) in enumerate(zip(self.point_clouds, self.keypoints)):
            pc_array = np.array(pc, dtype=np.float32)
            kp_array = np.array(kp, dtype=np.float32)
            
            # 计算中心
            pc_center = np.mean(pc_array, axis=0)
            kp_center = np.mean(kp_array, axis=0)
            
            # 中心偏移
            center_offset = np.linalg.norm(pc_center - kp_center)
            
            # 检查是否需要对齐
            needs_alignment = False
            issues = []
            
            # 1. 中心偏移过大 (阈值设为20mm，比之前更宽松)
            if center_offset > 20.0:
                needs_alignment = True
                issues.append(f"中心偏移: {center_offset:.1f}mm")
            
            # 2. 检查坐标范围是否合理
            pc_range = np.max(pc_array, axis=0) - np.min(pc_array, axis=0)
            kp_range = np.max(kp_array, axis=0) - np.min(kp_array, axis=0)
            
            # 如果点云范围远大于关键点范围，可能有问题
            for dim in range(3):
                if pc_range[dim] > kp_range[dim] * 5:  # 5倍阈值
                    needs_alignment = True
                    issues.append(f"{'XYZ'[dim]}轴范围异常: PC={pc_range[dim]:.1f}, KP={kp_range[dim]:.1f}")
            
            # 3. 检查是否有明显的坐标系错误 (例如Z轴翻转)
            pc_z_center = np.mean(pc_array[:, 2])
            kp_z_center = np.mean(kp_array[:, 2])
            
            if abs(pc_z_center - kp_z_center) > 50.0:  # Z轴偏移过大
                needs_alignment = True
                issues.append(f"Z轴偏移: {abs(pc_z_center - kp_z_center):.1f}mm")
            
            if needs_alignment:
                alignment_issues.append({
                    'sample_id': self.sample_ids[i],
                    'sample_index': i,
                    'center_offset': center_offset,
                    'issues': issues
                })
        
        print(f"📊 对齐需求分析:")
        print(f"   需要对齐的样本: {len(alignment_issues)}/{len(self.sample_ids)}")
        print(f"   保持原样的样本: {len(self.sample_ids) - len(alignment_issues)}")
        
        if alignment_issues:
            print(f"   主要问题:")
            for issue in alignment_issues[:5]:  # 显示前5个
                print(f"     样本 {issue['sample_id']}: {', '.join(issue['issues'])}")
        
        return alignment_issues
    
    def minimal_alignment(self, alignment_issues):
        """最小化对齐 - 只修正必要的问题"""
        print("\n🔧 执行最小化对齐...")
        
        aligned_point_clouds = []
        aligned_keypoints = []
        alignment_log = []
        
        # 创建需要对齐的样本索引集合
        alignment_indices = {issue['sample_index'] for issue in alignment_issues}
        
        for i, (pc, kp) in enumerate(zip(self.point_clouds, self.keypoints)):
            pc_array = np.array(pc, dtype=np.float32)
            kp_array = np.array(kp, dtype=np.float32)
            
            if i in alignment_indices:
                # 需要对齐的样本
                pc_center = np.mean(pc_array, axis=0)
                kp_center = np.mean(kp_array, axis=0)
                center_offset = pc_center - kp_center
                
                # 只进行中心对齐，不改变尺度
                pc_aligned = pc_array - center_offset
                kp_aligned = kp_array  # 关键点保持不变
                
                alignment_log.append({
                    'sample_id': self.sample_ids[i],
                    'action': 'center_alignment',
                    'offset': center_offset.tolist(),
                    'offset_magnitude': np.linalg.norm(center_offset)
                })
                
                print(f"   对齐样本 {self.sample_ids[i]}: 偏移 {np.linalg.norm(center_offset):.1f}mm")
                
            else:
                # 不需要对齐的样本，保持原样
                pc_aligned = pc_array
                kp_aligned = kp_array
                
                alignment_log.append({
                    'sample_id': self.sample_ids[i],
                    'action': 'no_change',
                    'offset': [0.0, 0.0, 0.0],
                    'offset_magnitude': 0.0
                })
            
            aligned_point_clouds.append(pc_aligned)
            aligned_keypoints.append(kp_aligned)
        
        self.aligned_point_clouds = aligned_point_clouds
        self.aligned_keypoints = np.array(aligned_keypoints)
        
        print(f"✅ 最小化对齐完成:")
        print(f"   对齐的样本: {len(alignment_indices)}")
        print(f"   保持原样: {len(self.sample_ids) - len(alignment_indices)}")
        
        return alignment_log
    
    def prepare_uniform_data(self, target_points=4096):
        """准备统一格式的数据 - 只统一点数，不改变坐标"""
        print(f"\n📏 统一点云大小到 {target_points} 点...")
        
        uniform_point_clouds = []
        
        for i, pc in enumerate(self.aligned_point_clouds):
            if len(pc) > target_points:
                # 随机下采样
                indices = np.random.choice(len(pc), target_points, replace=False)
                pc_uniform = pc[indices]
            elif len(pc) < target_points:
                # 重复采样
                indices = np.random.choice(len(pc), target_points, replace=True)
                pc_uniform = pc[indices]
            else:
                pc_uniform = pc
            
            uniform_point_clouds.append(pc_uniform)
        
        self.uniform_point_clouds = np.array(uniform_point_clouds)
        
        print(f"✅ 点云大小统一完成")
        print(f"   最终点云形状: {self.uniform_point_clouds.shape}")
        print(f"   最终关键点形状: {self.aligned_keypoints.shape}")
        print(f"   保持的数据范围:")
        print(f"     点云: [{np.min(self.uniform_point_clouds):.1f}, {np.max(self.uniform_point_clouds):.1f}]mm")
        print(f"     关键点: [{np.min(self.aligned_keypoints):.1f}, {np.max(self.aligned_keypoints):.1f}]mm")
        
    def quality_check(self):
        """质量检查"""
        print("\n🔍 执行质量检查...")
        
        issues = []
        
        # 检查数据完整性
        if np.any(np.isnan(self.uniform_point_clouds)) or np.any(np.isnan(self.aligned_keypoints)):
            issues.append("数据包含NaN值")
        
        if np.any(np.isinf(self.uniform_point_clouds)) or np.any(np.isinf(self.aligned_keypoints)):
            issues.append("数据包含无穷值")
        
        # 检查数据范围合理性
        pc_range = np.max(self.uniform_point_clouds) - np.min(self.uniform_point_clouds)
        kp_range = np.max(self.aligned_keypoints) - np.min(self.aligned_keypoints)
        
        if pc_range < 10 or pc_range > 1000:  # 合理的医学数据范围
            issues.append(f"点云范围异常: {pc_range:.1f}mm")
        
        if kp_range < 5 or kp_range > 500:
            issues.append(f"关键点范围异常: {kp_range:.1f}mm")
        
        # 检查点云和关键点的一致性
        for i in range(len(self.uniform_point_clouds)):
            pc = self.uniform_point_clouds[i]
            kp = self.aligned_keypoints[i]
            
            pc_center = np.mean(pc, axis=0)
            kp_center = np.mean(kp, axis=0)
            center_diff = np.linalg.norm(pc_center - kp_center)
            
            if center_diff > 30.0:  # 30mm阈值
                issues.append(f"样本{i}中心偏移过大: {center_diff:.1f}mm")
                break  # 只报告第一个问题
        
        print(f"📊 质量检查结果:")
        if issues:
            print(f"   发现问题: {len(issues)}")
            for issue in issues:
                print(f"     - {issue}")
        else:
            print(f"   ✅ 未发现问题")
        
        return issues
    
    def save_corrected_data(self, output_path=None):
        """保存修正后的数据"""
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"data/processed/corrected_lightweight_{timestamp}.npz"
        
        output_dir = Path(output_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"\n💾 保存修正数据到: {output_path}")
        
        # 保存数据 - 保持原始数据类型和范围
        np.savez_compressed(
            output_path,
            sample_ids=self.sample_ids,
            point_clouds=self.uniform_point_clouds,
            keypoints=self.aligned_keypoints,
            original_point_clouds=self.point_clouds,
            original_keypoints=self.keypoints,
            preprocessing_type='minimal_alignment_only',
            physical_space_preserved=True
        )
        
        print(f"✅ 修正数据保存完成")
        print(f"   数据类型: 真实物理空间 (mm)")
        print(f"   预处理: 仅最小化对齐")
        print(f"   尺度: 保持原始尺度")
        
        return output_path
    
    def visualize_correction_effects(self, sample_indices=[0, 1, 2], save_plots=True):
        """可视化修正效果"""
        print(f"\n📊 可视化修正效果...")
        
        viz_dir = Path("results/corrected_preprocessing_visualization")
        viz_dir.mkdir(parents=True, exist_ok=True)
        
        for idx in sample_indices:
            if idx >= len(self.sample_ids):
                continue
                
            sample_id = self.sample_ids[idx]
            
            fig = plt.figure(figsize=(20, 6))
            
            # 原始数据
            ax1 = fig.add_subplot(131, projection='3d')
            orig_pc = np.array(self.point_clouds[idx])
            orig_kp = self.keypoints[idx]
            
            # 随机采样显示点云 (避免过密)
            if len(orig_pc) > 2000:
                display_indices = np.random.choice(len(orig_pc), 2000, replace=False)
                display_pc = orig_pc[display_indices]
            else:
                display_pc = orig_pc
            
            ax1.scatter(display_pc[:, 0], display_pc[:, 1], display_pc[:, 2], 
                       c='lightgray', s=1, alpha=0.3, label='Original PC')
            ax1.scatter(orig_kp[:, 0], orig_kp[:, 1], orig_kp[:, 2], 
                       c='red', s=50, label='Original KP')
            
            ax1.set_title(f'Original Data - {sample_id}')
            ax1.legend()
            ax1.set_xlabel('X (mm)')
            ax1.set_ylabel('Y (mm)')
            ax1.set_zlabel('Z (mm)')
            
            # 对齐后数据
            ax2 = fig.add_subplot(132, projection='3d')
            aligned_pc = self.aligned_point_clouds[idx]
            aligned_kp = self.aligned_keypoints[idx]
            
            # 随机采样显示
            if len(aligned_pc) > 2000:
                display_indices = np.random.choice(len(aligned_pc), 2000, replace=False)
                display_pc = aligned_pc[display_indices]
            else:
                display_pc = aligned_pc
            
            ax2.scatter(display_pc[:, 0], display_pc[:, 1], display_pc[:, 2], 
                       c='lightblue', s=1, alpha=0.3, label='Aligned PC')
            ax2.scatter(aligned_kp[:, 0], aligned_kp[:, 1], aligned_kp[:, 2], 
                       c='blue', s=50, label='Aligned KP')
            
            ax2.set_title(f'After Minimal Alignment - {sample_id}')
            ax2.legend()
            ax2.set_xlabel('X (mm)')
            ax2.set_ylabel('Y (mm)')
            ax2.set_zlabel('Z (mm)')
            
            # 最终统一数据
            ax3 = fig.add_subplot(133, projection='3d')
            final_pc = self.uniform_point_clouds[idx]
            final_kp = self.aligned_keypoints[idx]
            
            # 随机采样显示
            display_indices = np.random.choice(len(final_pc), 2000, replace=False)
            display_pc = final_pc[display_indices]
            
            ax3.scatter(display_pc[:, 0], display_pc[:, 1], display_pc[:, 2], 
                       c='lightgreen', s=1, alpha=0.3, label='Final PC')
            ax3.scatter(final_kp[:, 0], final_kp[:, 1], final_kp[:, 2], 
                       c='green', s=50, label='Final KP')
            
            ax3.set_title(f'Final Uniform Data - {sample_id}')
            ax3.legend()
            ax3.set_xlabel('X (mm)')
            ax3.set_ylabel('Y (mm)')
            ax3.set_zlabel('Z (mm)')
            
            plt.tight_layout()
            
            if save_plots:
                plt.savefig(viz_dir / f"corrected_preprocessing_{sample_id}.png", 
                           dpi=150, bbox_inches='tight')
                print(f"💾 保存可视化: corrected_preprocessing_{sample_id}.png")
            
            plt.show()

def run_corrected_preprocessing():
    """运行修正版预处理"""
    print("🔧 修正版轻量级数据预处理")
    print("=" * 60)
    print("目标: 只做必要的对齐，保持真实物理空间")
    
    # 创建修正版预处理器
    preprocessor = CorrectedLightweightPreprocessor()
    
    # 1. 分析对齐需求
    alignment_issues = preprocessor.analyze_alignment_needs()
    
    # 2. 最小化对齐
    alignment_log = preprocessor.minimal_alignment(alignment_issues)
    
    # 3. 统一数据格式
    preprocessor.prepare_uniform_data()
    
    # 4. 质量检查
    quality_issues = preprocessor.quality_check()
    
    # 5. 保存修正数据
    output_path = preprocessor.save_corrected_data()
    
    # 6. 可视化效果
    preprocessor.visualize_correction_effects()
    
    # 7. 生成修正报告
    correction_report = {
        "preprocessing_timestamp": datetime.now().isoformat(),
        "input_data": str(preprocessor.data_path),
        "output_data": output_path,
        "total_samples": len(preprocessor.sample_ids),
        "preprocessing_type": "minimal_alignment_only",
        "physical_space_preserved": True,
        "alignment_summary": {
            "samples_needing_alignment": len(alignment_issues),
            "samples_unchanged": len(preprocessor.sample_ids) - len(alignment_issues),
            "alignment_threshold": "20mm center offset"
        },
        "data_ranges": {
            "final_point_cloud_range": [float(np.min(preprocessor.uniform_point_clouds)), 
                                      float(np.max(preprocessor.uniform_point_clouds))],
            "final_keypoint_range": [float(np.min(preprocessor.aligned_keypoints)), 
                                   float(np.max(preprocessor.aligned_keypoints))]
        },
        "quality_issues": quality_issues,
        "expected_improvement": "5-15% 真实改进 (在物理空间)",
        "evaluation_note": "所有评估必须在真实物理空间进行",
        "next_steps": [
            "使用修正数据重新训练模型",
            "确保在真实物理空间评估性能",
            "对比修正前后的真实改进效果"
        ]
    }
    
    # 保存报告
    report_dir = Path("results/corrected_preprocessing_reports")
    report_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = report_dir / f"corrected_preprocessing_report_{timestamp}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(correction_report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 修正版预处理完成!")
    print(f"📈 预期真实改进: 5-15%")
    print(f"💾 修正数据: {output_path}")
    print(f"📋 详细报告: {report_file}")
    print(f"\n🎯 关键改进:")
    print(f"   ✅ 保持真实物理空间 (mm)")
    print(f"   ✅ 仅做必要的对齐")
    print(f"   ✅ 避免过度标准化")
    print(f"   ✅ 确保正确的评估方法")
    
    return preprocessor, correction_report

if __name__ == "__main__":
    preprocessor, report = run_corrected_preprocessing()
