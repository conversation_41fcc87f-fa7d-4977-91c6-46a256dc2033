#!/usr/bin/env python3
"""
训练改进的医疗关键点检测模型
解决坐标系不一致和预测范围问题
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import h5py
from pathlib import Path
import time
import matplotlib.pyplot as plt
from tqdm import tqdm

class ImprovedMedicalPointNet(nn.Module):
    """改进的医疗PointNet模型"""
    
    def __init__(self, num_keypoints=57, num_points=2048):
        super(ImprovedMedicalPointNet, self).__init__()
        self.num_keypoints = num_keypoints
        self.num_points = num_points
        
        # 输入变换网络 (T-Net)
        self.input_transform = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReL<PERSON>(),
            nn.Conv1d(128, 1024, 1),
            nn.BatchNorm1d(1024),
            nn.ReLU(),
            nn.AdaptiveMaxPool1d(1),
            nn.Flatten(),
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Linear(256, 9)
        )
        
        # 特征变换网络
        self.feature_transform = nn.Sequential(
            nn.Conv1d(64, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 1024, 1),
            nn.BatchNorm1d(1024),
            nn.ReLU(),
            nn.AdaptiveMaxPool1d(1),
            nn.Flatten(),
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Linear(256, 64*64)
        )
        
        # 主要特征提取网络
        self.feat_conv1 = nn.Conv1d(3, 64, 1)
        self.feat_conv2 = nn.Conv1d(64, 128, 1)
        self.feat_conv3 = nn.Conv1d(128, 256, 1)
        self.feat_conv4 = nn.Conv1d(256, 512, 1)
        self.feat_conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 关键点回归头
        self.keypoint_head = nn.Sequential(
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Linear(128, num_keypoints * 3)
        )
        
        # 坐标范围预测头（新增）
        self.range_head = nn.Sequential(
            nn.Linear(1024, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Linear(128, 6)  # [x_min, x_max, y_min, y_max, z_min, z_max]
        )
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        batch_size = x.size(0)
        
        # 输入变换
        trans_input = self.input_transform(x)
        trans_input = trans_input.view(batch_size, 3, 3)
        
        # 应用输入变换
        x_transformed = torch.bmm(x.transpose(2, 1), trans_input).transpose(2, 1)
        
        # 第一层特征提取
        x = torch.relu(self.bn1(self.feat_conv1(x_transformed)))
        
        # 特征变换
        trans_feat = self.feature_transform(x)
        trans_feat = trans_feat.view(batch_size, 64, 64)
        
        # 应用特征变换
        x = torch.bmm(x.transpose(2, 1), trans_feat).transpose(2, 1)
        
        # 继续特征提取
        x = torch.relu(self.bn2(self.feat_conv2(x)))
        x = torch.relu(self.bn3(self.feat_conv3(x)))
        x = torch.relu(self.bn4(self.feat_conv4(x)))
        x = torch.relu(self.bn5(self.feat_conv5(x)))
        
        # 全局特征
        global_feat = torch.max(x, 2)[0]
        
        # 关键点预测
        keypoints = self.keypoint_head(global_feat)
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        # 坐标范围预测
        coord_range = self.range_head(global_feat)
        
        return keypoints, coord_range, global_feat

class ImprovedMedicalDataset(Dataset):
    """改进的医疗数据集"""
    
    def __init__(self, data_root, split='train', augment=True):
        self.data_root = Path(data_root)
        self.split = split
        self.augment = augment
        
        # 加载数据列表
        self.load_data_list()
        
        print(f"📊 {split}数据集: {len(self.data_list)} 个样本")
    
    def load_data_list(self):
        """加载数据列表"""
        aligned_data_dir = self.data_root / "aligned_data"
        h5_files = list(aligned_data_dir.glob("*.h5"))
        
        # 简单的训练/验证分割
        np.random.seed(42)
        indices = np.random.permutation(len(h5_files))
        split_idx = int(0.8 * len(h5_files))
        
        if self.split == 'train':
            self.data_list = [h5_files[i] for i in indices[:split_idx]]
        else:
            self.data_list = [h5_files[i] for i in indices[split_idx:]]
    
    def __len__(self):
        return len(self.data_list)
    
    def __getitem__(self, idx):
        h5_file = self.data_list[idx]
        
        try:
            with h5py.File(h5_file, 'r') as f:
                point_cloud = f['point_cloud'][:]
                keypoints = f['keypoints'][:]
            
            # 数据增强
            if self.augment and self.split == 'train':
                point_cloud, keypoints = self.apply_augmentation(point_cloud, keypoints)
            
            # 计算坐标范围
            pc_min, pc_max = point_cloud.min(axis=0), point_cloud.max(axis=0)
            coord_range = np.concatenate([pc_min, pc_max])  # [x_min, x_max, y_min, y_max, z_min, z_max]
            
            # 归一化到点云坐标系
            keypoints_normalized = self.normalize_keypoints_to_pointcloud(keypoints, point_cloud)
            
            return {
                'point_cloud': torch.FloatTensor(point_cloud.T),  # [3, N]
                'keypoints': torch.FloatTensor(keypoints_normalized),  # [57, 3]
                'coord_range': torch.FloatTensor(coord_range),  # [6]
                'patient_id': h5_file.stem
            }
            
        except Exception as e:
            print(f"❌ 加载数据失败 {h5_file}: {e}")
            # 返回一个默认样本
            return self.__getitem__((idx + 1) % len(self.data_list))
    
    def normalize_keypoints_to_pointcloud(self, keypoints, point_cloud):
        """将关键点归一化到点云坐标系"""
        pc_min, pc_max = point_cloud.min(axis=0), point_cloud.max(axis=0)
        pc_center = (pc_min + pc_max) / 2
        pc_range = pc_max - pc_min
        
        kp_min, kp_max = keypoints.min(axis=0), keypoints.max(axis=0)
        kp_center = (kp_min + kp_max) / 2
        kp_range = kp_max - kp_min
        
        # 智能缩放和平移
        scale_factors = []
        for i in range(3):
            if kp_range[i] > 0:
                # 目标是让关键点范围占点云范围的80%
                target_range = pc_range[i] * 0.8
                scale_factor = target_range / kp_range[i]
            else:
                scale_factor = 1.0
            scale_factors.append(scale_factor)
        
        scale_factors = np.array(scale_factors)
        
        # 应用变换
        normalized_keypoints = (keypoints - kp_center) * scale_factors + pc_center
        
        return normalized_keypoints
    
    def apply_augmentation(self, point_cloud, keypoints):
        """应用数据增强"""
        # 随机旋转
        if np.random.random() > 0.5:
            angle = np.random.uniform(-np.pi/12, np.pi/12)  # ±15度
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            point_cloud = point_cloud @ rotation_matrix.T
            keypoints = keypoints @ rotation_matrix.T
        
        # 随机缩放
        if np.random.random() > 0.5:
            scale = np.random.uniform(0.95, 1.05)
            point_cloud *= scale
            keypoints *= scale
        
        # 随机平移
        if np.random.random() > 0.5:
            translation = np.random.uniform(-1.0, 1.0, 3)
            point_cloud += translation
            keypoints += translation
        
        # 随机噪声
        if np.random.random() > 0.5:
            noise = np.random.normal(0, 0.01, point_cloud.shape)
            point_cloud += noise
        
        return point_cloud, keypoints

class ImprovedLoss(nn.Module):
    """改进的损失函数"""
    
    def __init__(self, alpha=1.0, beta=0.1):
        super(ImprovedLoss, self).__init__()
        self.alpha = alpha  # 关键点损失权重
        self.beta = beta    # 坐标范围损失权重
        self.mse_loss = nn.MSELoss()
        self.smooth_l1_loss = nn.SmoothL1Loss()
    
    def forward(self, pred_keypoints, pred_range, target_keypoints, target_range):
        # 关键点损失
        keypoint_loss = self.smooth_l1_loss(pred_keypoints, target_keypoints)
        
        # 坐标范围损失
        range_loss = self.mse_loss(pred_range, target_range)
        
        # 总损失
        total_loss = self.alpha * keypoint_loss + self.beta * range_loss
        
        return total_loss, keypoint_loss, range_loss

def train_improved_model():
    """训练改进的模型"""
    print("🚀 开始训练改进的医疗关键点检测模型...")
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 创建数据集
    data_root = Path("MedicalAlignedDataset")
    train_dataset = ImprovedMedicalDataset(data_root, split='train', augment=True)
    val_dataset = ImprovedMedicalDataset(data_root, split='val', augment=False)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=8, shuffle=False, num_workers=4)
    
    # 创建模型
    model = ImprovedMedicalPointNet(num_keypoints=57)
    model.to(device)
    
    # 创建优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=100, eta_min=1e-6)
    criterion = ImprovedLoss(alpha=1.0, beta=0.1)
    
    # 训练设置
    num_epochs = 100
    best_val_loss = float('inf')
    patience = 15
    patience_counter = 0
    
    # 创建输出目录
    output_dir = Path("output/improved_model_training")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 训练历史
    train_losses = []
    val_losses = []
    
    print(f"🎯 开始训练 {num_epochs} 轮...")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_keypoint_loss = 0.0
        train_range_loss = 0.0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for batch in train_pbar:
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            coord_range = batch['coord_range'].to(device)
            
            optimizer.zero_grad()
            
            pred_keypoints, pred_range, _ = model(point_cloud)
            loss, kp_loss, range_loss = criterion(pred_keypoints, pred_range, keypoints, coord_range)
            
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_keypoint_loss += kp_loss.item()
            train_range_loss += range_loss.item()
            
            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'KP': f'{kp_loss.item():.4f}',
                'Range': f'{range_loss.item():.4f}'
            })
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_keypoint_loss = 0.0
        val_range_loss = 0.0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for batch in val_pbar:
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                coord_range = batch['coord_range'].to(device)
                
                pred_keypoints, pred_range, _ = model(point_cloud)
                loss, kp_loss, range_loss = criterion(pred_keypoints, pred_range, keypoints, coord_range)
                
                val_loss += loss.item()
                val_keypoint_loss += kp_loss.item()
                val_range_loss += range_loss.item()
                
                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'KP': f'{kp_loss.item():.4f}',
                    'Range': f'{range_loss.item():.4f}'
                })
        
        # 计算平均损失
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        avg_train_kp_loss = train_keypoint_loss / len(train_loader)
        avg_val_kp_loss = val_keypoint_loss / len(val_loader)
        avg_train_range_loss = train_range_loss / len(train_loader)
        avg_val_range_loss = val_range_loss / len(val_loader)
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        # 更新学习率
        scheduler.step()
        
        print(f"Epoch {epoch+1}/{num_epochs}:")
        print(f"  Train Loss: {avg_train_loss:.4f} (KP: {avg_train_kp_loss:.4f}, Range: {avg_train_range_loss:.4f})")
        print(f"  Val Loss: {avg_val_loss:.4f} (KP: {avg_val_kp_loss:.4f}, Range: {avg_val_range_loss:.4f})")
        print(f"  LR: {scheduler.get_last_lr()[0]:.6f}")
        
        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_loss': best_val_loss,
                'train_losses': train_losses,
                'val_losses': val_losses
            }, output_dir / 'best_improved_model.pth')
            
            print(f"  ✅ 保存最佳模型 (验证损失: {best_val_loss:.4f})")
        else:
            patience_counter += 1
            print(f"  ⏳ 早停计数: {patience_counter}/{patience}")
        
        # 早停
        if patience_counter >= patience:
            print(f"🛑 早停触发，训练结束")
            break
        
        print("-" * 60)
    
    print(f"🎉 训练完成！最佳验证损失: {best_val_loss:.4f}")
    
    # 绘制训练曲线
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training History')
    plt.legend()
    plt.grid(True)
    plt.savefig(output_dir / 'training_history.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📊 训练曲线已保存: {output_dir / 'training_history.png'}")

if __name__ == "__main__":
    train_improved_model()
