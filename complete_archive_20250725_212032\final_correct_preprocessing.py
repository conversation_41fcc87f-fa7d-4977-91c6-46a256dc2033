#!/usr/bin/env python3
"""
最终正确的预处理方法
Final correct preprocessing method based on 12-point dataset analysis
"""

import numpy as np
import json

def apply_12point_style_preprocessing(point_clouds, keypoints_57, keypoints_12):
    """应用与12点数据集相同的预处理方法"""
    
    print("🔧 应用与12点数据集相同的预处理方法...")
    print("基于点云空间扩展的方法")
    
    processed_pc = []
    processed_57kp = []
    processed_12kp = []
    processing_info = []
    
    for i in range(len(point_clouds)):
        pc = point_clouds[i].copy()
        kp_57 = keypoints_57[i].copy()
        kp_12 = keypoints_12[i].copy()
        
        # 步骤1: 关键点中心对齐
        kp_center = np.mean(kp_12, axis=0)
        pc_centered = pc - kp_center
        kp_57_centered = kp_57 - kp_center
        kp_12_centered = kp_12 - kp_center
        
        # 步骤2: 计算关键点和点云的空间分布
        kp_12_std = np.std(kp_12_centered, axis=0)
        pc_std = np.std(pc_centered, axis=0)
        
        # 步骤3: 扩展点云空间分布以匹配关键点
        # 目标：让点云的标准差接近关键点的标准差
        target_std = kp_12_std
        current_std = pc_std
        
        # 计算扩展因子
        expansion_factors = target_std / (current_std + 1e-6)  # 避免除零
        
        # 限制扩展因子在合理范围内
        expansion_factors = np.clip(expansion_factors, 0.5, 10.0)
        
        # 应用扩展
        pc_expanded = pc_centered * expansion_factors
        
        # 步骤4: 重新采样点云到50000点（与12点数据集一致）
        if len(pc_expanded) < 50000:
            # 上采样
            indices = np.random.choice(len(pc_expanded), 50000, replace=True)
            pc_final = pc_expanded[indices]
        else:
            # 下采样
            indices = np.random.choice(len(pc_expanded), 50000, replace=False)
            pc_final = pc_expanded[indices]
        
        processed_pc.append(pc_final)
        processed_57kp.append(kp_57_centered)
        processed_12kp.append(kp_12_centered)
        
        # 记录处理信息
        processing_info.append({
            'sample_index': i,
            'kp_center_offset': kp_center.tolist(),
            'expansion_factors': expansion_factors.tolist(),
            'original_pc_std': pc_std.tolist(),
            'target_std': target_std.tolist(),
            'final_pc_std': np.std(pc_final, axis=0).tolist()
        })
        
        if i == 0:
            print(f"   样本 {i} 处理:")
            print(f"     关键点中心: {kp_center}")
            print(f"     原始点云标准差: {pc_std}")
            print(f"     目标标准差: {target_std}")
            print(f"     扩展因子: {expansion_factors}")
            print(f"     最终点云标准差: {np.std(pc_final, axis=0)}")
    
    processed_pc = np.array(processed_pc)
    processed_57kp = np.array(processed_57kp)
    processed_12kp = np.array(processed_12kp)
    
    print(f"\n✅ 12点风格预处理完成:")
    print(f"   点云: {processed_pc.shape}")
    print(f"   57关键点: {processed_57kp.shape}")
    print(f"   12关键点: {processed_12kp.shape}")
    
    return processed_pc, processed_57kp, processed_12kp, processing_info

def validate_12point_style_preprocessing(processed_pc, processed_57kp, processed_12kp):
    """验证12点风格预处理效果"""
    
    print("🔍 验证12点风格预处理效果...")
    
    # 检查关键点到点云表面的距离
    sample_idx = 0
    pc_sample = processed_pc[sample_idx]
    kp_sample = processed_57kp[sample_idx]
    
    distances_to_surface = []
    for kp in kp_sample:
        dists = np.linalg.norm(pc_sample - kp, axis=1)
        min_dist = np.min(dists)
        distances_to_surface.append(min_dist)
    
    avg_distance = np.mean(distances_to_surface)
    max_distance = np.max(distances_to_surface)
    
    print(f"   关键点到点云表面距离:")
    print(f"     平均距离: {avg_distance:.2f}mm")
    print(f"     最大距离: {max_distance:.2f}mm")
    
    # 检查数据中心化
    pc_centers = [np.mean(pc, axis=0) for pc in processed_pc]
    kp_12_centers = [np.mean(kp, axis=0) for kp in processed_12kp]
    
    avg_pc_center = np.mean(pc_centers, axis=0)
    avg_kp_center = np.mean(kp_12_centers, axis=0)
    
    print(f"   数据中心化检查:")
    print(f"     平均点云中心: {avg_pc_center}")
    print(f"     平均12关键点中心: {avg_kp_center}")
    print(f"     中心偏移: {np.linalg.norm(avg_kp_center):.4f}mm")
    
    # 检查空间分布匹配
    pc_global_std = np.std(processed_pc, axis=(0,1))
    kp_12_global_std = np.std(processed_12kp, axis=(0,1))
    
    print(f"   空间分布匹配:")
    print(f"     点云全局标准差: {pc_global_std}")
    print(f"     12关键点全局标准差: {kp_12_global_std}")
    print(f"     标准差比值: {pc_global_std / (kp_12_global_std + 1e-6)}")
    
    # 与原始12点数据对比
    male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz', allow_pickle=True)
    orig_pc = male_data['point_clouds']
    orig_12kp = male_data['keypoints']
    
    orig_pc_global_std = np.std(orig_pc, axis=(0,1))
    orig_12kp_global_std = np.std(orig_12kp, axis=(0,1))
    
    print(f"   与原始12点数据对比:")
    print(f"     原始点云全局标准差: {orig_pc_global_std}")
    print(f"     原始12关键点全局标准差: {orig_12kp_global_std}")
    print(f"     处理后点云全局标准差: {pc_global_std}")
    print(f"     处理后12关键点全局标准差: {kp_12_global_std}")
    
    # 判断预处理是否成功
    success_criteria = [
        avg_distance < 30,  # 关键点到表面距离合理
        np.linalg.norm(avg_kp_center) < 1,  # 12关键点已中心化
        np.allclose(pc_global_std, kp_12_global_std, rtol=0.5),  # 空间分布匹配
        np.allclose(pc_global_std, orig_pc_global_std, rtol=0.5)  # 与原始12点数据相似
    ]
    
    if all(success_criteria):
        print(f"✅ 12点风格预处理成功！")
        return True
    else:
        print(f"⚠️ 预处理需要进一步调整")
        print(f"   失败的标准: {[i for i, x in enumerate(success_criteria) if not x]}")
        return False

def save_final_preprocessed_data(processed_pc, processed_57kp, processed_12kp, sample_ids, genders, processing_info):
    """保存最终预处理数据"""
    
    print("💾 保存最终预处理数据...")
    
    np.savez_compressed('final_preprocessed_57_dataset.npz',
                       point_clouds=processed_pc,
                       keypoints_57=processed_57kp,
                       keypoints_12=processed_12kp,
                       sample_ids=sample_ids,
                       genders=genders,
                       processing_info=processing_info)
    
    # 保存预处理信息
    preprocessing_summary = {
        'method': '12point_style_preprocessing',
        'description': '基于12点数据集的完整预处理流程：中心对齐+空间扩展',
        'steps': [
            '1. 关键点中心对齐',
            '2. 计算空间分布差异',
            '3. 扩展点云空间分布以匹配关键点',
            '4. 重采样到50000点'
        ],
        'samples': len(sample_ids),
        'point_cloud_shape': processed_pc.shape,
        'keypoints_57_shape': processed_57kp.shape,
        'keypoints_12_shape': processed_12kp.shape,
        'coordinate_system': 'keypoint_centered_expanded',
        'quality': 'verified_against_12point_dataset'
    }
    
    with open('final_preprocessing_info.json', 'w') as f:
        json.dump(preprocessing_summary, f, indent=2, default=str)
    
    print(f"✅ 最终预处理数据已保存:")
    print(f"   - final_preprocessed_57_dataset.npz (预处理数据)")
    print(f"   - final_preprocessing_info.json (预处理信息)")

def main():
    """主函数"""
    
    print("🎯 最终正确的预处理方法")
    print("基于12点数据集的完整分析")
    print("=" * 80)
    
    # 加载真实57点数据
    print(f"📊 加载真实57点数据...")
    real_data = np.load('real_57_dataset_from_existing.npz', allow_pickle=True)
    point_clouds = real_data['point_clouds']
    keypoints_57 = real_data['keypoints_57']
    keypoints_12 = real_data['keypoints_12']
    sample_ids = real_data['sample_ids']
    genders = real_data['genders']
    
    print(f"✅ 原始数据加载完成: {len(sample_ids)} 个样本")
    
    # 应用12点风格预处理
    processed_pc, processed_57kp, processed_12kp, processing_info = apply_12point_style_preprocessing(
        point_clouds, keypoints_57, keypoints_12
    )
    
    # 验证预处理效果
    success = validate_12point_style_preprocessing(processed_pc, processed_57kp, processed_12kp)
    
    if success:
        # 保存最终预处理数据
        save_final_preprocessed_data(processed_pc, processed_57kp, processed_12kp, sample_ids, genders, processing_info)
        
        print(f"\n🎉 最终预处理成功！")
        print(f"💡 关键改进:")
        print(f"   ✅ 完全复制12点数据集的预处理流程")
        print(f"   ✅ 关键点中心对齐 + 点云空间扩展")
        print(f"   ✅ 空间分布完全匹配")
        print(f"   ✅ 关键点与点云正确对应")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 在最终预处理数据上训练57点模型")
        print(f"   2. 预期性能接近12点模型水平")
        print(f"   3. 验证真实数据的巨大优势")
        
    else:
        print(f"\n❌ 预处理仍需调整")
        print(f"💡 建议:")
        print(f"   1. 进一步分析12点数据集的处理细节")
        print(f"   2. 调整扩展因子的计算方法")
        print(f"   3. 检查点云重采样策略")

if __name__ == "__main__":
    main()
