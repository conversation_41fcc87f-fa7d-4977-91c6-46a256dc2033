{"archive_info": {"timestamp": "2025-07-25T10:22:30.947425", "archive_directory": "archive_universal_model_experiment_20250725_102230", "purpose": "通用模型实验完整存档"}, "experiment_summary": {"main_achievement": "成功创建6.60mm性能的通用模型", "key_findings": ["男性模型优势主要来自数据量 (2.9倍)", "MutualAssistanceNet的相互辅助机制是关键创新", "简化架构比复杂架构更有效", "数据量比架构复杂度更重要"], "best_models": {"男性专用": "5.65-5.84mm (MutualAssistanceNet)", "女性专用": "9.98-19.54mm (FemaleOptimizedNet)", "通用模型": "6.60mm (SimplifiedUniversalModel)"}}, "archived_items": {"models": ["mutual_assistance_男性.pth", "female_optimized.pth", "best_simple_universal_model.pth", "best_universal_model.pth"], "results": ["proper_expansion_experiment_results.json", "final_expansion_analysis_report.json", "universal_model_results.json", "gender_model_analysis_report.json", "final_universal_model_report.json"], "reports": ["universal_model_comparison.png", "project_summary.md", "README.md"], "code": ["keypoint_mutual_assistance.py", "female_specific_optimization.py", "proper_dataset_expansion_experiment.py", "universal_high_performance_model.py", "analysis_and_simple_universal_model.py", "final_expansion_analysis_report.py", "final_universal_model_summary.py"], "datasets": ["archive/old_experiments/f3_reduced_12kp_female.npz", "archive/old_experiments/f3_reduced_12kp_male.npz"]}, "next_steps": ["探索预训练模型迁移学习", "改进模型性能", "考虑大规模预训练模型", "实现更好的泛化能力"]}