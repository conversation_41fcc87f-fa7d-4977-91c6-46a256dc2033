#!/usr/bin/env python3
"""
快速男性数据增强
Quick Male Data Augmentation
"""

import numpy as np
import os

class QuickMaleAugmenter:
    """快速男性数据增强器"""
    
    def __init__(self, target_count=200):
        self.target_count = target_count
        print(f"🔥 快速男性增强器")
        print(f"   目标样本数: {target_count}")
    
    def rotate_3d(self, points, angles_deg):
        """3D旋转"""
        angles = np.radians(angles_deg)
        
        # 简化旋转矩阵
        cos_x, sin_x = np.cos(angles[0]), np.sin(angles[0])
        cos_y, sin_y = np.cos(angles[1]), np.sin(angles[1])
        cos_z, sin_z = np.cos(angles[2]), np.sin(angles[2])
        
        Rx = np.array([[1, 0, 0], [0, cos_x, -sin_x], [0, sin_x, cos_x]])
        Ry = np.array([[cos_y, 0, sin_y], [0, 1, 0], [-sin_y, 0, cos_y]])
        Rz = np.array([[cos_z, -sin_z, 0], [sin_z, cos_z, 0], [0, 0, 1]])
        
        R = Rz @ Ry @ Rx
        return points @ R.T
    
    def augment_male_dataset(self):
        """增强男性数据集"""
        
        # 加载男性数据
        male_path = "archive/old_experiments/f3_reduced_12kp_male.npz"
        if not os.path.exists(male_path):
            print(f"❌ 男性数据不存在: {male_path}")
            return None
        
        print(f"📊 加载男性数据...")
        data = np.load(male_path, allow_pickle=True)
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        original_count = len(sample_ids)
        augment_factor = int(np.ceil(self.target_count / original_count))
        
        print(f"✅ 原始男性数据: {original_count}个样本")
        print(f"🎯 目标数量: {self.target_count}")
        print(f"🔄 需要增强倍数: {augment_factor}x")
        
        # 存储增强结果
        all_sample_ids = []
        all_point_clouds = []
        all_keypoints = []
        all_methods = []
        
        # 添加原始数据
        for i, (sid, pc, kp) in enumerate(zip(sample_ids, point_clouds, keypoints)):
            all_sample_ids.append(f"{sid}_original")
            all_point_clouds.append(pc)
            all_keypoints.append(kp)
            all_methods.append("original")
        
        # 生成增强数据
        for aug_idx in range(augment_factor - 1):  # -1因为已经有原始数据
            print(f"生成增强轮次 {aug_idx + 1}/{augment_factor - 1}...")
            
            for i, (sid, pc, kp) in enumerate(zip(sample_ids, point_clouds, keypoints)):
                if len(all_sample_ids) >= self.target_count:
                    break
                
                # 随机旋转
                angles = np.random.uniform(-15, 15, 3)
                pc_rot = self.rotate_3d(pc, angles)
                kp_rot = self.rotate_3d(kp, angles)
                
                all_sample_ids.append(f"{sid}_aug_{aug_idx}_{i}")
                all_point_clouds.append(pc_rot)
                all_keypoints.append(kp_rot)
                all_methods.append(f"rotation_{aug_idx}")
            
            if len(all_sample_ids) >= self.target_count:
                break
        
        # 截断到目标数量
        if len(all_sample_ids) > self.target_count:
            all_sample_ids = all_sample_ids[:self.target_count]
            all_point_clouds = all_point_clouds[:self.target_count]
            all_keypoints = all_keypoints[:self.target_count]
            all_methods = all_methods[:self.target_count]
        
        # 转换为numpy数组
        all_point_clouds = np.array(all_point_clouds)
        all_keypoints = np.array(all_keypoints)
        
        print(f"\n📊 增强完成:")
        print(f"   最终样本数: {len(all_sample_ids)}")
        print(f"   点云形状: {all_point_clouds.shape}")
        print(f"   关键点形状: {all_keypoints.shape}")
        
        # 保存结果
        output_path = "f3_reduced_12kp_male_augmented.npz"
        np.savez_compressed(output_path,
                           sample_ids=all_sample_ids,
                           point_clouds=all_point_clouds,
                           keypoints=all_keypoints,
                           methods=all_methods)
        
        print(f"💾 男性增强数据已保存: {output_path}")
        
        return {
            'sample_ids': all_sample_ids,
            'point_clouds': all_point_clouds,
            'keypoints': all_keypoints,
            'methods': all_methods
        }

def check_final_balance():
    """检查最终平衡情况"""
    
    print(f"\n⚖️ 最终平衡检查:")
    print("=" * 40)
    
    # 检查女性增强数据
    female_path = "f3_reduced_12kp_female_augmented.npz"
    if os.path.exists(female_path):
        female_data = np.load(female_path, allow_pickle=True)
        female_count = len(female_data['sample_ids'])
        print(f"✅ 女性增强数据: {female_count}个")
    else:
        female_count = 0
        print(f"❌ 女性增强数据不存在")
    
    # 检查男性增强数据
    male_path = "f3_reduced_12kp_male_augmented.npz"
    if os.path.exists(male_path):
        male_data = np.load(male_path, allow_pickle=True)
        male_count = len(male_data['sample_ids'])
        print(f"✅ 男性增强数据: {male_count}个")
    else:
        male_count = 0
        print(f"❌ 男性增强数据不存在")
    
    if female_count > 0 and male_count > 0:
        total = female_count + male_count
        female_ratio = female_count / total * 100
        male_ratio = male_count / total * 100
        
        print(f"\n📊 最终比例:")
        print(f"   女性: {female_count}个 ({female_ratio:.1f}%)")
        print(f"   男性: {male_count}个 ({male_ratio:.1f}%)")
        print(f"   总计: {total}个")
        print(f"   比例: {female_count/male_count:.2f}:1")
        
        if abs(female_ratio - 50) < 10:  # 40-60%范围内
            print(f"✅ 比例相对平衡!")
        else:
            print(f"⚠️ 仍有一定不平衡")

def main():
    """主函数"""
    
    print("🔄 快速男性数据增强")
    print("🎯 目标: 平衡性别比例")
    print("=" * 60)
    
    # 询问用户意见
    print("您现在有两个选择:")
    print("1. 🚀 直接用女性增强数据训练 (250女性 vs 72男性)")
    print("2. ⚖️ 先增强男性数据平衡比例 (250女性 vs 200男性)")
    print()
    
    # 为了演示，我们先增强男性数据
    print("💡 我先帮您增强男性数据，这样您有两个选择:")
    
    # 增强男性数据
    augmenter = QuickMaleAugmenter(target_count=200)
    male_augmented = augmenter.augment_male_dataset()
    
    if male_augmented:
        # 检查最终平衡
        check_final_balance()
        
        print(f"\n🎉 现在您有了两套数据:")
        print(f"   选择A: 女性250个 + 男性72个 (不平衡但女性数据充足)")
        print(f"   选择B: 女性250个 + 男性200个 (相对平衡)")
        print(f"\n💡 建议:")
        print(f"   • 先试选择A，看女性模型能达到什么效果")
        print(f"   • 如果效果好，再用选择B训练混合模型")

if __name__ == "__main__":
    main()
