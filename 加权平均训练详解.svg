<svg width="1500" height="1000" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f87171;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="1500" height="1000" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1500" height="70" fill="url(#headerGrad)"/>
  <text x="750" y="45" text-anchor="middle" fill="white" 
        font-family="SimHei, Arial, sans-serif" font-size="32" font-weight="bold">
    加权平均如何通过训练逼近真实关键点
  </text>
  
  <!-- Main content -->
  <rect x="30" y="90" width="1440" height="880" rx="15" fill="white" stroke="#374151" stroke-width="2"/>
  
  <!-- Training iteration visualization -->
  <text x="750" y="130" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="24" font-weight="bold">
    训练过程：网络如何学会正确的权重分配
  </text>
  
  <!-- Iteration 1: Initial random weights -->
  <rect x="60" y="160" width="1380" height="200" rx="10" fill="#fef2f2" stroke="#f87171" stroke-width="2"/>
  <text x="750" y="190" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    训练初期：随机权重，预测不准确
  </text>
  
  <!-- Region points -->
  <circle cx="150" cy="250" r="30" fill="#dbeafe" stroke="#3b82f6" stroke-width="2"/>
  <circle cx="140" cy="240" r="3" fill="#1e40af"/>
  <circle cx="160" cy="260" r="3" fill="#1e40af"/>
  <circle cx="145" cy="265" r="3" fill="#1e40af"/>
  <circle cx="155" cy="235" r="3" fill="#1e40af"/>
  <circle cx="150" cy="250" r="4" fill="#dc2626"/>
  <text x="150" y="300" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    潜在区域R_i
  </text>
  <text x="150" y="315" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    红点：真实关键点
  </text>
  
  <!-- Arrow 1 -->
  <path d="M 200 250 L 280 250" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
    </marker>
  </defs>
  
  <!-- Random weights -->
  <rect x="300" y="210" width="200" height="80" rx="5" fill="#fff7ed" stroke="#f59e0b" stroke-width="1"/>
  <text x="400" y="235" text-anchor="middle" fill="#d97706" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    初始随机权重
  </text>
  <text x="400" y="255" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="12">
    W = [0.3, 0.2, 0.1, 0.4]
  </text>
  <text x="400" y="275" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    权重分配不合理
  </text>
  
  <!-- Arrow 2 -->
  <path d="M 520 250 L 600 250" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Weighted average calculation -->
  <rect x="620" y="210" width="200" height="80" rx="5" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="720" y="235" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    加权平均计算
  </text>
  <text x="720" y="255" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="12">
    kp_pred = Σ(W_i × p_i)
  </text>
  <text x="720" y="275" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="12">
    = (x₁, y₁, z₁)
  </text>
  
  <!-- Arrow 3 -->
  <path d="M 840 250 L 920 250" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Loss calculation -->
  <rect x="940" y="210" width="200" height="80" rx="5" fill="#fef2f2" stroke="#f87171" stroke-width="1"/>
  <text x="1040" y="235" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    损失计算
  </text>
  <text x="1040" y="255" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="12">
    Loss = ||kp_pred - kp_gt||₂
  </text>
  <text x="1040" y="275" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    Loss = 5.2mm (很大!)
  </text>
  
  <!-- Arrow 4 -->
  <path d="M 1160 250 L 1240 250" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Backpropagation -->
  <rect x="1260" y="210" width="160" height="80" rx="5" fill="#f0f9ff" stroke="#3b82f6" stroke-width="1"/>
  <text x="1340" y="235" text-anchor="middle" fill="#1e40af" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    反向传播
  </text>
  <text x="1340" y="255" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    更新网络参数
  </text>
  <text x="1340" y="275" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    调整权重计算
  </text>
  
  <!-- Iteration 2: Improved weights -->
  <rect x="60" y="380" width="1380" height="200" rx="10" fill="#fff7ed" stroke="#f59e0b" stroke-width="2"/>
  <text x="750" y="410" text-anchor="middle" fill="#d97706" 
        font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    训练中期：权重逐渐优化，预测改善
  </text>
  
  <!-- Same region -->
  <circle cx="150" cy="470" r="30" fill="#dbeafe" stroke="#3b82f6" stroke-width="2"/>
  <circle cx="140" cy="460" r="3" fill="#1e40af"/>
  <circle cx="160" cy="480" r="3" fill="#1e40af"/>
  <circle cx="145" cy="485" r="3" fill="#1e40af"/>
  <circle cx="155" cy="455" r="3" fill="#1e40af"/>
  <circle cx="150" cy="470" r="4" fill="#dc2626"/>
  
  <!-- Improved weights -->
  <rect x="300" y="430" width="200" height="80" rx="5" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
  <text x="400" y="455" text-anchor="middle" fill="#d97706" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    改进的权重
  </text>
  <text x="400" y="475" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="12">
    W = [0.1, 0.6, 0.05, 0.25]
  </text>
  <text x="400" y="495" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    更接近真实点的权重更大
  </text>
  
  <!-- Better prediction -->
  <rect x="620" y="430" width="200" height="80" rx="5" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="720" y="455" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    更好的预测
  </text>
  <text x="720" y="475" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="12">
    kp_pred = (x₂, y₂, z₂)
  </text>
  <text x="720" y="495" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    更接近真实关键点
  </text>
  
  <!-- Smaller loss -->
  <rect x="940" y="430" width="200" height="80" rx="5" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="1040" y="455" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    损失减小
  </text>
  <text x="1040" y="475" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="12">
    Loss = ||kp_pred - kp_gt||₂
  </text>
  <text x="1040" y="495" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    Loss = 2.1mm (改善!)
  </text>
  
  <!-- Continue training -->
  <rect x="1260" y="430" width="160" height="80" rx="5" fill="#f0f9ff" stroke="#3b82f6" stroke-width="1"/>
  <text x="1340" y="455" text-anchor="middle" fill="#1e40af" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    继续训练
  </text>
  <text x="1340" y="475" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    进一步优化
  </text>
  <text x="1340" y="495" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    权重分配
  </text>
  
  <!-- Arrows for iteration 2 -->
  <path d="M 200 470 L 280 470" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 520 470 L 600 470" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 840 470 L 920 470" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 1160 470 L 1240 470" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Final iteration: Converged -->
  <rect x="60" y="600" width="1380" height="200" rx="10" fill="#f0fdf4" stroke="#22c55e" stroke-width="2"/>
  <text x="750" y="630" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    训练后期：权重收敛，预测精确
  </text>
  
  <!-- Same region -->
  <circle cx="150" cy="690" r="30" fill="#dbeafe" stroke="#3b82f6" stroke-width="2"/>
  <circle cx="140" cy="680" r="3" fill="#1e40af"/>
  <circle cx="160" cy="700" r="3" fill="#1e40af"/>
  <circle cx="145" cy="705" r="3" fill="#1e40af"/>
  <circle cx="155" cy="675" r="3" fill="#1e40af"/>
  <circle cx="150" cy="690" r="4" fill="#dc2626"/>
  
  <!-- Optimal weights -->
  <rect x="300" y="650" width="200" height="80" rx="5" fill="#dcfce7" stroke="#22c55e" stroke-width="1"/>
  <text x="400" y="675" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    最优权重
  </text>
  <text x="400" y="695" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="12">
    W = [0.05, 0.8, 0.02, 0.13]
  </text>
  <text x="400" y="715" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    最接近真实点权重最大
  </text>
  
  <!-- Accurate prediction -->
  <rect x="620" y="650" width="200" height="80" rx="5" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="720" y="675" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    精确预测
  </text>
  <text x="720" y="695" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="12">
    kp_pred ≈ kp_gt
  </text>
  <text x="720" y="715" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    几乎重合!
  </text>
  
  <!-- Minimal loss -->
  <rect x="940" y="650" width="200" height="80" rx="5" fill="#dcfce7" stroke="#22c55e" stroke-width="1"/>
  <text x="1040" y="675" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    损失最小
  </text>
  <text x="1040" y="695" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="12">
    Loss = ||kp_pred - kp_gt||₂
  </text>
  <text x="1040" y="715" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    Loss = 0.3mm (收敛!)
  </text>
  
  <!-- Training complete -->
  <rect x="1260" y="650" width="160" height="80" rx="5" fill="#dcfce7" stroke="#22c55e" stroke-width="1"/>
  <text x="1340" y="675" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    训练完成
  </text>
  <text x="1340" y="695" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    网络学会了
  </text>
  <text x="1340" y="715" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    正确的权重分配
  </text>
  
  <!-- Arrows for final iteration -->
  <path d="M 200 690 L 280 690" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 520 690 L 600 690" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 840 690 L 920 690" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 1160 690 L 1240 690" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Key insight -->
  <rect x="60" y="820" width="1380" height="130" rx="10" fill="#f8fafc" stroke="#374151" stroke-width="1"/>
  <text x="750" y="850" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    🔑 关键洞察：网络如何学会正确的权重分配
  </text>
  
  <text x="100" y="880" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="16">
    • <tspan fill="#dc2626" font-weight="bold">梯度指导</tspan>：损失函数的梯度告诉网络如何调整权重，使预测更接近真实关键点
  </text>
  <text x="100" y="905" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="16">
    • <tspan fill="#15803d" font-weight="bold">距离敏感</tspan>：网络学会给距离真实关键点更近的点分配更大的权重
  </text>
  <text x="100" y="930" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="16">
    • <tspan fill="#7c3aed" font-weight="bold">双SoftMax优化</tspan>：通过阈值过滤和二次归一化，进一步提高权重分配的精确性
  </text>
</svg>
