#!/usr/bin/env python3
"""
小数据集救援实验执行器
Small Dataset Rescue Experiment Runner
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import random
from pathlib import Path
import json
from datetime import datetime
from sklearn.model_selection import train_test_split

class RescueKeypointNet(nn.Module):
    """救援版关键点检测网络 - 集成多种技术"""
    
    def __init__(self, input_dim=3, hidden_dim=256, output_dim=19*3, dropout=0.3):
        super().__init__()
        
        # 多尺度特征提取
        self.conv1 = nn.Conv1d(input_dim, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, hidden_dim, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(hidden_dim)
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(hidden_dim, num_heads=8, batch_first=True)
        
        # 残差连接
        self.residual_conv = nn.Conv1d(input_dim, hidden_dim, 1)
        
        # 关键点回归器
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(hidden_dim, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            
            nn.Linear(128, output_dim)
        )
        
    def forward(self, point_cloud):
        batch_size = point_cloud.size(0)
        x = point_cloud.transpose(2, 1)  # (B, 3, N)
        
        # 多尺度特征提取
        x1 = F.relu(self.bn1(self.conv1(x)))
        x2 = F.relu(self.bn2(self.conv2(x1)))
        x3 = F.relu(self.bn3(self.conv3(x2)))
        x4 = F.relu(self.bn4(self.conv4(x3)))
        
        # 残差连接
        residual = self.residual_conv(point_cloud.transpose(2, 1))
        x4 = x4 + residual
        
        # 全局特征
        global_feature = torch.max(x4, 2)[0]  # (B, hidden_dim)
        
        # 自注意力
        global_feature_expanded = global_feature.unsqueeze(1)  # (B, 1, hidden_dim)
        attended_feature, _ = self.attention(global_feature_expanded, global_feature_expanded, global_feature_expanded)
        attended_feature = attended_feature.squeeze(1)  # (B, hidden_dim)
        
        # 预测关键点
        keypoints = self.keypoint_regressor(attended_feature)
        return keypoints.view(batch_size, 19, 3)

class SuperAugmentation:
    """超级数据增强器 - 集成多种增强技术"""
    
    def __init__(self):
        self.mixup_alpha = 0.2
        self.cutmix_alpha = 1.0
        
    def mixup(self, pc1, kp1, pc2, kp2):
        """Mixup增强"""
        lam = np.random.beta(self.mixup_alpha, self.mixup_alpha)
        
        # 确保点云长度一致
        min_len = min(len(pc1), len(pc2))
        pc1_sampled = pc1[:min_len] if len(pc1) >= min_len else pc1[np.random.choice(len(pc1), min_len, replace=True)]
        pc2_sampled = pc2[:min_len] if len(pc2) >= min_len else pc2[np.random.choice(len(pc2), min_len, replace=True)]
        
        mixed_pc = lam * pc1_sampled + (1 - lam) * pc2_sampled
        mixed_kp = lam * kp1 + (1 - lam) * kp2
        
        return mixed_pc, mixed_kp
    
    def cutmix(self, pc1, kp1, pc2, kp2):
        """CutMix增强"""
        lam = np.random.beta(self.cutmix_alpha, self.cutmix_alpha)
        
        min_len = min(len(pc1), len(pc2))
        num_cut = int(min_len * (1 - lam))
        
        cut_indices = np.random.choice(min_len, num_cut, replace=False)
        
        mixed_pc = pc1[:min_len].copy()
        mixed_pc[cut_indices] = pc2[:min_len][cut_indices]
        mixed_kp = lam * kp1 + (1 - lam) * kp2
        
        return mixed_pc, mixed_kp
    
    def geometric_augment(self, pc, kp):
        """几何增强"""
        # 旋转
        if np.random.random() < 0.8:
            angle = np.random.uniform(-0.15, 0.15)  # ±8.6度
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
            pc = pc @ rotation.T
            kp = kp @ rotation.T
        
        # 缩放
        if np.random.random() < 0.7:
            scale = np.random.uniform(0.95, 1.05)
            pc *= scale
            kp *= scale
        
        # 平移
        if np.random.random() < 0.6:
            translation = np.random.uniform(-2.0, 2.0, 3)
            pc += translation
            kp += translation
        
        # 噪声
        if np.random.random() < 0.5:
            noise = np.random.normal(0, 0.5, pc.shape)
            pc += noise
        
        # 点云dropout
        if np.random.random() < 0.4:
            keep_ratio = np.random.uniform(0.8, 0.95)
            num_keep = int(len(pc) * keep_ratio)
            indices = np.random.choice(len(pc), num_keep, replace=False)
            pc_dropped = pc[indices]
            # 补齐
            if len(pc_dropped) < len(pc):
                extra_indices = np.random.choice(len(pc_dropped), len(pc) - len(pc_dropped), replace=True)
                pc = np.vstack([pc_dropped, pc_dropped[extra_indices]])
            else:
                pc = pc_dropped
        
        return pc, kp
    
    def super_augment_batch(self, point_clouds, keypoints, augment_factor=5):
        """超级批量增强"""
        augmented_pcs = []
        augmented_kps = []
        
        # 添加原始数据
        for pc, kp in zip(point_clouds, keypoints):
            augmented_pcs.append(pc)
            augmented_kps.append(kp)
        
        # 几何增强
        for pc, kp in zip(point_clouds, keypoints):
            for _ in range(augment_factor):
                aug_pc, aug_kp = self.geometric_augment(pc.copy(), kp.copy())
                augmented_pcs.append(aug_pc)
                augmented_kps.append(aug_kp)
        
        # Mixup增强
        for _ in range(len(point_clouds)):
            idx1, idx2 = np.random.choice(len(point_clouds), 2, replace=False)
            mixed_pc, mixed_kp = self.mixup(
                point_clouds[idx1], keypoints[idx1],
                point_clouds[idx2], keypoints[idx2]
            )
            augmented_pcs.append(mixed_pc)
            augmented_kps.append(mixed_kp)
        
        # CutMix增强
        for _ in range(len(point_clouds) // 2):
            idx1, idx2 = np.random.choice(len(point_clouds), 2, replace=False)
            cutmix_pc, cutmix_kp = self.cutmix(
                point_clouds[idx1], keypoints[idx1],
                point_clouds[idx2], keypoints[idx2]
            )
            augmented_pcs.append(cutmix_pc)
            augmented_kps.append(cutmix_kp)
        
        # 确保所有点云有相同的点数
        target_points = 4096
        processed_pcs = []
        for pc in augmented_pcs:
            if len(pc) > target_points:
                indices = np.random.choice(len(pc), target_points, replace=False)
                pc = pc[indices]
            elif len(pc) < target_points:
                indices = np.random.choice(len(pc), target_points, replace=True)
                pc = pc[indices]
            processed_pcs.append(pc)
        
        return np.stack(processed_pcs), np.stack(augmented_kps)

class RescueTrainer:
    """救援训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        self.augmentation = SuperAugmentation()
        
    def load_data(self, data_path='data/raw/high_quality_f3_dataset.npz'):
        """加载数据"""
        print(f"📦 加载救援数据: {data_path}")
        
        data = np.load(data_path, allow_pickle=True)
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        # 下采样
        processed_pcs = []
        for pc in point_clouds:
            if len(pc) > 4096:
                indices = np.random.choice(len(pc), 4096, replace=False)
                pc_sampled = pc[indices]
            else:
                indices = np.random.choice(len(pc), 4096, replace=True)
                pc_sampled = pc[indices]
            processed_pcs.append(pc_sampled)
        
        point_clouds = np.array(processed_pcs)
        
        # 数据划分
        indices = np.arange(len(sample_ids))
        train_val_indices, test_indices = train_test_split(
            indices, test_size=0.15, random_state=42
        )
        train_indices, val_indices = train_test_split(
            train_val_indices, test_size=0.18, random_state=42
        )
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
        
        print(f"✅ 救援数据准备完成: 训练{len(train_indices)}, 验证{len(val_indices)}, 测试{len(test_indices)}")
        return self.data
    
    def rescue_train(self, k_shot, epochs=150, lr=0.0005):
        """救援训练"""
        print(f"\n🆘 救援训练 {k_shot}-shot 模型")
        
        # 创建救援模型
        model = RescueKeypointNet().to(self.device)
        
        # 优化器和调度器
        optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-4)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=20, T_mult=2)
        
        # 损失函数 - 组合损失
        mse_loss = nn.MSELoss()
        smooth_l1_loss = nn.SmoothL1Loss()
        
        best_val_error = float('inf')
        best_model_state = None
        patience = 0
        max_patience = 40
        
        for epoch in range(epochs):
            model.train()
            
            # 采样训练数据
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                min(k_shot, len(self.data['train']['point_clouds'])), 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 超级数据增强
            aug_pcs, aug_kps = self.augmentation.super_augment_batch(
                train_pcs, train_kps, augment_factor=8
            )
            
            # 分批训练
            batch_size = 16
            total_loss = 0
            num_batches = 0
            
            for i in range(0, len(aug_pcs), batch_size):
                batch_pcs = torch.FloatTensor(aug_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(aug_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                
                pred_kps = model(batch_pcs)
                
                # 组合损失
                loss1 = mse_loss(pred_kps, batch_kps)
                loss2 = smooth_l1_loss(pred_kps, batch_kps)
                loss = 0.7 * loss1 + 0.3 * loss2
                
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.step()
                
                total_loss += loss.item()
                num_batches += 1
            
            scheduler.step()
            avg_loss = total_loss / num_batches if num_batches > 0 else 0
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_model(model, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                    patience = 0
                else:
                    patience += 1
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.3f}, Val_Error={val_error:.2f}mm, LR={optimizer.param_groups[0]['lr']:.6f}")
                
                if patience >= max_patience:
                    print(f"早停在epoch {epoch}")
                    break
        
        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def evaluate_model(self, model, split='test'):
        """评估模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for pc, kp in zip(pcs, kps):
                pc_tensor = torch.FloatTensor(pc).unsqueeze(0).to(self.device)
                kp_tensor = torch.FloatTensor(kp).unsqueeze(0).to(self.device)
                
                pred_kp = model(pc_tensor)
                error = torch.mean(torch.norm(pred_kp - kp_tensor, dim=2))
                total_error += error.item()
                num_samples += 1
        
        return total_error / num_samples if num_samples > 0 else float('inf')

def run_rescue_mission():
    """执行救援任务"""
    print("🆘 小数据集救援任务开始！")
    print("=" * 60)
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    random.seed(42)
    
    # 初始化救援训练器
    trainer = RescueTrainer()
    
    # 加载数据
    data = trainer.load_data()
    
    # 救援实验配置
    shot_configs = [3, 5, 10, 15, 20]
    rescue_results = {}
    
    for k_shot in shot_configs:
        print(f"\n{'='*60}")
        
        # 救援训练
        model, val_error = trainer.rescue_train(k_shot, epochs=100, lr=0.0005)
        
        # 测试评估
        test_error = trainer.evaluate_model(model, 'test')
        rescue_results[k_shot] = test_error
        
        print(f"🆘 {k_shot}-shot 救援结果: {test_error:.2f}mm")
    
    # 救援结果分析
    print(f"\n📊 救援任务结果")
    print("=" * 50)
    
    baseline_error = 21.26  # 之前的最佳结果
    
    for k_shot, error in rescue_results.items():
        improvement = (baseline_error - error) / baseline_error * 100
        rescue_status = "🎉 救援成功" if error < 15.0 else "⚠️ 部分改善" if error < 20.0 else "❌ 需要更多救援"
        print(f"{k_shot:2d}-shot: {error:6.2f}mm (改进: {improvement:+5.1f}%) {rescue_status}")
    
    # 找出最佳救援结果
    best_shot = min(rescue_results.keys(), key=lambda k: rescue_results[k])
    best_error = rescue_results[best_shot]
    
    print(f"\n🏆 最佳救援结果: {best_shot}-shot")
    print(f"🎯 救援后误差: {best_error:.2f}mm")
    print(f"📈 总体改进: {(baseline_error - best_error) / baseline_error * 100:+.1f}%")
    
    if best_error < 10.0:
        print("🎉 救援大成功！达到实用水平！")
    elif best_error < 15.0:
        print("👍 救援成功！显著改善！")
    elif best_error < 20.0:
        print("📈 救援有效，继续努力！")
    else:
        print("⚠️ 需要更强的救援措施！")
    
    # 保存救援结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = Path("results/rescue_mission")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    rescue_data = {
        "mission_type": "small_dataset_rescue",
        "timestamp": timestamp,
        "baseline_error": baseline_error,
        "rescue_results": rescue_results,
        "best_result": {
            "k_shot": best_shot,
            "error_mm": best_error,
            "improvement_percent": (baseline_error - best_error) / baseline_error * 100
        },
        "rescue_techniques": [
            "超级数据增强 (Mixup + CutMix + 几何变换)",
            "多尺度特征提取",
            "注意力机制",
            "残差连接",
            "组合损失函数",
            "高级优化策略"
        ]
    }
    
    with open(results_dir / f"rescue_mission_results_{timestamp}.json", 'w') as f:
        json.dump(rescue_data, f, indent=2)
    
    print(f"\n💾 救援报告已保存到: results/rescue_mission/")
    
    return rescue_results

if __name__ == "__main__":
    results = run_rescue_mission()
