<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="1280" height="720" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="url(#headerGrad)"/>
  <text x="640" y="50" text-anchor="middle" fill="white" 
        font-family="SimHei, Arial, sans-serif" font-size="36" font-weight="bold">
    核心创新：双SoftMax权重细化机制
  </text>
  
  <!-- Problem statement -->
  <rect x="50" y="100" width="1180" height="100" rx="15" fill="white" stroke="#f59e0b" stroke-width="4"/>
  <text x="640" y="130" text-anchor="middle" fill="#d97706" 
        font-family="SimHei, Arial, sans-serif" font-size="24" font-weight="bold">
    核心问题：密集点云中的"权重均匀化"困境
  </text>
  <text x="640" y="165" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="18">
    当点密度很高且相似性强时，单一SoftMax产生近似均匀权重
  </text>
  <text x="640" y="190" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    → 平均化效应 → 定位精度下降
  </text>
  
  <!-- Innovation overview -->
  <rect x="50" y="220" width="1180" height="120" rx="15" fill="white" stroke="#7c3aed" stroke-width="4"/>
  <text x="640" y="250" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="24" font-weight="bold">
    创新解决方案：两阶段权重细化
  </text>
  
  <!-- Two stages side by side -->
  <rect x="100" y="270" width="480" height="60" rx="10" fill="#fef2f2" stroke="#ef4444" stroke-width="2"/>
  <text x="340" y="295" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    第一次SoftMax：初始权重计算
  </text>
  <text x="340" y="320" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="14">
    基于特征相似性，产生近似均匀权重
  </text>
  
  <!-- Arrow -->
  <path d="M 590 300 L 690 300" stroke="#374151" stroke-width="4" fill="none" marker-end="url(#arrowhead)"/>
  <defs>
    <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#374151"/>
    </marker>
  </defs>
  
  <rect x="700" y="270" width="480" height="60" rx="10" fill="#f0fdf4" stroke="#22c55e" stroke-width="2"/>
  <text x="940" y="295" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    第二次SoftMax：权重过滤细化
  </text>
  <text x="940" y="320" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="14">
    阈值过滤，产生尖锐选择性权重
  </text>
  
  <!-- Visual comparison -->
  <rect x="50" y="360" width="1180" height="200" rx="15" fill="white" stroke="#10b981" stroke-width="3"/>
  <text x="640" y="390" text-anchor="middle" fill="#059669" 
        font-family="SimHei, Arial, sans-serif" font-size="22" font-weight="bold">
    权重分布对比：从均匀到尖锐
  </text>
  
  <!-- Before: Uniform weights -->
  <rect x="100" y="410" width="480" height="120" rx="10" fill="#fee2e2" stroke="#fca5a5" stroke-width="1"/>
  <text x="340" y="435" text-anchor="middle" fill="#7f1d1d" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    传统单一SoftMax：均匀权重分布
  </text>
  
  <!-- Uniform weight bars -->
  <rect x="120" y="450" width="360" height="40" rx="5" fill="#fef2f2" stroke="#fca5a5" stroke-width="1"/>
  <rect x="130" y="460" width="40" height="20" fill="#ef4444" opacity="0.6"/>
  <rect x="180" y="460" width="38" height="20" fill="#ef4444" opacity="0.55"/>
  <rect x="228" y="460" width="42" height="20" fill="#ef4444" opacity="0.6"/>
  <rect x="280" y="460" width="36" height="20" fill="#ef4444" opacity="0.5"/>
  <rect x="326" y="460" width="44" height="20" fill="#ef4444" opacity="0.65"/>
  <rect x="380" y="460" width="40" height="20" fill="#ef4444" opacity="0.55"/>
  <rect x="430" y="460" width="38" height="20" fill="#ef4444" opacity="0.6"/>
  
  <text x="340" y="510" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="13">
    权重差异小 → 平均化效应 → 精度损失
  </text>
  
  <!-- After: Sharp weights -->
  <rect x="700" y="410" width="480" height="120" rx="10" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="940" y="435" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    双SoftMax机制：尖锐权重分布
  </text>
  
  <!-- Sharp weight bars -->
  <rect x="720" y="450" width="360" height="40" rx="5" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <rect x="730" y="460" width="60" height="20" fill="#22c55e" opacity="0.9"/>
  <rect x="800" y="460" width="15" height="20" fill="#22c55e" opacity="0.2"/>
  <rect x="825" y="460" width="70" height="20" fill="#22c55e" opacity="0.85"/>
  <rect x="905" y="460" width="10" height="20" fill="#22c55e" opacity="0.15"/>
  <rect x="925" y="460" width="55" height="20" fill="#22c55e" opacity="0.8"/>
  <rect x="990" y="460" width="12" height="20" fill="#22c55e" opacity="0.18"/>
  <rect x="1012" y="460" width="58" height="20" fill="#22c55e" opacity="0.75"/>
  
  <text x="940" y="510" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="13">
    权重差异大 → 选择性强 → 精度提升
  </text>
  
  <!-- Key mechanism -->
  <rect x="50" y="580" width="1180" height="100" rx="15" fill="white" stroke="#8b5cf6" stroke-width="3"/>
  <text x="640" y="610" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    核心机制：阈值函数 WS(ω) = 1 / (1 + exp(-M·(ω - threshold)))
  </text>
  <text x="640" y="640" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="16">
    高于阈值的权重被放大，低于阈值的权重被抑制
  </text>
  <text x="640" y="665" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    → 突出重要点，忽略干扰点 → 精确定位
  </text>
  
  <!-- Results -->
  <rect x="50" y="690" width="1180" height="25" rx="8" fill="#fef7ff" stroke="#a855f7" stroke-width="1"/>
  <text x="640" y="708" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    效果：定位精度提升0.38mm (21%改善)，从1.81mm降至1.43mm
  </text>
</svg>
