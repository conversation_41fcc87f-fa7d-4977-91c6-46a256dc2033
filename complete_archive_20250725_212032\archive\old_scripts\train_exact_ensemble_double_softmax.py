#!/usr/bin/env python3
"""
精确集成双Softmax训练脚本
严格按照5.829mm成功配置
目标: 重现并稳定在5.829mm水平
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random
from ensemble_double_softmax_exact import ExactEnsembleDoubleSoftMaxPointNet, ExactReducedKeypointsF3Dataset, calculate_metrics

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class ExactLoss(nn.Module):
    """精确复制的损失函数"""
    
    def __init__(self, alpha=0.8, beta=0.2):
        super(ExactLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, pred, target):
        mse_loss = F.mse_loss(pred, target)
        smooth_l1_loss = F.smooth_l1_loss(pred, target)
        total_loss = self.alpha * mse_loss + self.beta * smooth_l1_loss
        return total_loss

def train_exact_ensemble_double_softmax(seed=42):
    """训练精确集成双Softmax模型"""
    
    print(f"🚀 **精确集成双Softmax训练 (种子: {seed})**")
    print(f"🔬 **精确复制**: 5.829mm成功配置")
    print(f"🎯 **目标**: 重现5.829mm或更好")
    print(f"📊 **策略**: 严格按照最佳配置")
    print("=" * 80)
    
    set_seed(seed)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 精确复制的数据集配置
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    train_dataset = ExactReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'train', 
                                                 num_points=4096, test_samples=test_samples, 
                                                 augment=True, seed=seed)
    val_dataset = ExactReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'val', 
                                               num_points=4096, test_samples=test_samples, 
                                               augment=False, seed=seed)
    
    batch_size = 4  # 精确复制
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
    
    # 精确复制的模型
    model = ExactEnsembleDoubleSoftMaxPointNet(num_keypoints=12, dropout_rate=0.3, num_ensembles=3).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 模型参数: {total_params:,}")
    
    # 精确复制的损失函数
    criterion = ExactLoss(alpha=0.8, beta=0.2)
    
    # 精确复制的优化器
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
    
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=12, min_lr=1e-6
    )
    
    num_epochs = 100  # 精确复制
    best_val_error = float('inf')
    patience = 20
    patience_counter = 0
    history = []
    min_delta = 0.005
    
    print(f"🎯 训练配置: 精确集成双Softmax")
    print(f"   集成模块: 3个 (0.10,0.15,0.20)")
    print(f"   候选点数: 256 (精确复制)")
    print(f"   损失函数: MSE(0.8) + SmoothL1(0.2)")
    print(f"   学习率: 0.0008")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 40)
        
        # 训练
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        for batch in train_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                        
            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue
        
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # 验证 (使用精确的集成双Softmax精细化)
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)  # 推理时使用精确集成双Softmax
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # 学习率调度
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 打印结果
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"5mm={train_metrics['within_5mm_percent']:.1f}%, 7mm={train_metrics['within_7mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"5mm={val_metrics['within_5mm_percent']:.1f}%, 7mm={val_metrics['within_7mm_percent']:.1f}%")
        print(f"学习率: {current_lr:.2e}")
        
        # 保存历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'learning_rate': current_lr
        })
        
        # 检查改进
        current_error = val_metrics['mean_distance']
        improvement = best_val_error - current_error
        
        if improvement > min_delta:
            best_val_error = current_error
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics,
                'model_name': 'Exact_Ensemble_Double_SoftMax',
                'config': 'exact_5829mm_configuration',
                'seed': seed
            }, f'best_exact_ensemble_seed{seed}_{best_val_error:.3f}mm.pth')
            
            print(f"🎉 新最佳! 验证误差: {best_val_error:.3f}mm (改进{improvement:.3f}mm)")
            
            if best_val_error <= 5.0:
                print(f"🏆 **突破5.0mm医疗级目标!**")
            elif best_val_error <= 5.5:
                print(f"🎯 **突破5.5mm目标!** 达到医疗级精度")
            elif best_val_error <= 5.829:
                print(f"✅ **达到或超越理论最佳!** 精确复制成功")
            elif best_val_error < 6.0:
                print(f"✅ **接近理论最佳!** 精确复制有效")
        else:
            patience_counter += 1
            print(f"⏳ 无显著改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停触发")
            break
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    
    print(f"\n🎉 **精确集成双Softmax训练完成! (种子: {seed})**")
    print(f"📊 理论最佳: 5.829mm")
    print(f"🎯 当前最佳: {best_val_error:.3f}mm")
    print(f"📈 vs理论最佳: {(best_val_error - 5.829) / 5.829 * 100:+.1f}%")
    print(f"⏱️  训练时间: {total_time/60:.1f}分钟")
    
    return best_val_error, history

def run_multiple_seeds():
    """使用多个随机种子训练"""
    
    print(f"🚀 **多种子精确集成双Softmax训练**")
    print(f"🎯 **目标**: 重现5.829mm最佳结果")
    print(f"📊 **策略**: 多个随机种子，取最佳结果")
    print("=" * 80)
    
    seeds = [42, 123, 456, 789, 999]  # 5个不同的随机种子
    results = []
    
    for i, seed in enumerate(seeds):
        print(f"\n🔄 **第{i+1}/{len(seeds)}次训练 (种子: {seed})**")
        print("=" * 60)
        
        try:
            best_error, history = train_exact_ensemble_double_softmax(seed)
            results.append({
                'seed': seed,
                'best_error': best_error,
                'history': history
            })
            
            print(f"✅ 种子{seed}完成: {best_error:.3f}mm")
            
        except Exception as e:
            print(f"❌ 种子{seed}失败: {e}")
            results.append({
                'seed': seed,
                'best_error': float('inf'),
                'error': str(e)
            })
    
    # 分析结果
    print(f"\n📊 **多种子训练结果汇总**")
    print("=" * 60)
    
    valid_results = [r for r in results if r['best_error'] != float('inf')]
    
    if valid_results:
        best_result = min(valid_results, key=lambda x: x['best_error'])
        worst_result = max(valid_results, key=lambda x: x['best_error'])
        avg_error = np.mean([r['best_error'] for r in valid_results])
        std_error = np.std([r['best_error'] for r in valid_results])
        
        print(f"🏆 最佳结果: {best_result['best_error']:.3f}mm (种子: {best_result['seed']})")
        print(f"📉 最差结果: {worst_result['best_error']:.3f}mm (种子: {worst_result['seed']})")
        print(f"📊 平均误差: {avg_error:.3f}mm ± {std_error:.3f}mm")
        print(f"📈 vs理论最佳: {(best_result['best_error'] - 5.829) / 5.829 * 100:+.1f}%")
        
        # 保存汇总结果
        summary = {
            'method': 'Exact Ensemble Double SoftMax (Multi-Seed)',
            'target_baseline': 5.829,
            'best_result': best_result['best_error'],
            'worst_result': worst_result['best_error'],
            'average_result': avg_error,
            'std_result': std_error,
            'num_seeds': len(valid_results),
            'all_results': results
        }
        
        with open('exact_ensemble_multi_seed_results.json', 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        if best_result['best_error'] <= 5.829:
            print(f"🎉 **成功重现或超越5.829mm!**")
        elif best_result['best_error'] < 6.0:
            print(f"✅ **接近理论最佳!** 精确复制基本成功")
        else:
            print(f"💡 **需要进一步调优** 或检查配置差异")
    
    else:
        print(f"❌ **所有种子都失败了** 需要检查代码或环境")
    
    return results

if __name__ == "__main__":
    # 运行多种子训练
    results = run_multiple_seeds()
