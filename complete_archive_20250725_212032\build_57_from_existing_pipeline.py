#!/usr/bin/env python3
"""
基于现有12点数据集管道构建57点数据集
Build 57-point dataset based on existing 12-point pipeline
"""

import numpy as np
import pandas as pd
import os
import glob
from pathlib import Path
from tqdm import tqdm
import json

def load_csv_annotations(csv_path, encoding='gbk'):
    """加载CSV标注文件 - 获取真实的57点"""
    
    try:
        df = pd.read_csv(csv_path, encoding=encoding)
    except:
        try:
            df = pd.read_csv(csv_path, encoding='utf-8')
        except:
            df = pd.read_csv(csv_path, encoding='latin-1')
    
    keypoints = df[['X', 'Y', 'Z']].values.astype(np.float32)
    labels = df['label'].values
    
    return keypoints, labels

def load_existing_point_cloud(sample_id):
    """加载现有的点云数据 - 基于12点数据集的方法"""
    
    pc_file = f"archive/old_experiments/{sample_id}_pointcloud.npy"
    
    if os.path.exists(pc_file):
        try:
            # 加载原始2000点的点云
            point_cloud = np.load(pc_file)
            
            # 扩展到50000点 - 与12点数据集相同的方法
            if len(point_cloud) < 50000:
                # 重复采样到50000点
                indices = np.random.choice(len(point_cloud), 50000, replace=True)
                point_cloud_expanded = point_cloud[indices]
            else:
                # 下采样到50000点
                indices = np.random.choice(len(point_cloud), 50000, replace=False)
                point_cloud_expanded = point_cloud[indices]
            
            return point_cloud_expanded, f"从{len(point_cloud)}点扩展到{len(point_cloud_expanded)}点"
            
        except Exception as e:
            return None, f"点云加载失败: {e}"
    else:
        return None, f"点云文件不存在: {pc_file}"

def extract_12_from_57(keypoints_57):
    """从57个关键点中提取12个核心关键点 - 与现有映射一致"""
    
    mapping_12_to_57 = {
        0: 0,   # F1-1
        1: 1,   # F1-2
        2: 2,   # F1-3
        3: 12,  # F1-13
        4: 19,  # F2-1
        5: 20,  # F2-2
        6: 21,  # F2-3
        7: 31,  # F2-13
        8: 38,  # F3-1
        9: 52,  # F3-15
        10: 50, # F3-13
        11: 51, # F3-14
    }
    
    keypoints_12 = np.zeros((12, 3))
    
    for i in range(12):
        original_idx = mapping_12_to_57[i]
        if original_idx < len(keypoints_57):
            keypoints_12[i] = keypoints_57[original_idx]
    
    return keypoints_12

def get_existing_sample_ids():
    """获取现有12点数据集中的样本ID"""
    
    try:
        # 加载现有的12点数据集
        male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz', allow_pickle=True)
        female_data = np.load('archive/old_experiments/f3_reduced_12kp_female.npz', allow_pickle=True)
        
        male_ids = male_data['sample_ids']
        female_ids = female_data['sample_ids']
        
        all_ids = list(male_ids) + list(female_ids)
        
        print(f"✅ 现有12点数据集样本:")
        print(f"   男性: {len(male_ids)} 个")
        print(f"   女性: {len(female_ids)} 个")
        print(f"   总计: {len(all_ids)} 个")
        
        return all_ids, male_ids, female_ids
        
    except Exception as e:
        print(f"❌ 无法加载现有数据集: {e}")
        return [], [], []

def build_57_dataset_from_existing():
    """基于现有12点数据集构建57点数据集"""
    
    print("🎯 基于现有12点数据集构建57点数据集")
    print("=" * 80)
    
    # 获取现有样本ID
    all_ids, male_ids, female_ids = get_existing_sample_ids()
    
    if not all_ids:
        print("❌ 无法获取现有样本ID")
        return None
    
    # CSV标注目录
    csv_dir = "/home/<USER>/pjc/GCN/data/Data/annotations"
    
    all_keypoints_57 = []
    all_keypoints_12 = []
    all_point_clouds = []
    all_sample_ids = []
    all_genders = []
    processing_log = []
    
    print(f"\n🔄 处理 {len(all_ids)} 个现有样本...")
    
    for sample_id in tqdm(all_ids, desc="处理样本"):
        try:
            # 确定性别
            if sample_id in male_ids:
                gender = 'male'
            elif sample_id in female_ids:
                gender = 'female'
            else:
                gender = 'unknown'
            
            # 查找对应的CSV文件
            csv_files = glob.glob(f"{csv_dir}/{sample_id}-Table-*.CSV")
            
            if not csv_files:
                print(f"⚠️ 样本 {sample_id}: 未找到CSV标注文件")
                processing_log.append((sample_id, "失败", "未找到CSV文件"))
                continue
            
            # 优先使用XYZ坐标系
            xyz_files = [f for f in csv_files if 'XYZ' in f]
            csv_file = xyz_files[0] if xyz_files else csv_files[0]
            
            # 加载57个关键点
            keypoints_57, labels = load_csv_annotations(csv_file)
            
            if len(keypoints_57) != 57:
                print(f"⚠️ 样本 {sample_id}: 关键点数量异常 {len(keypoints_57)}")
                processing_log.append((sample_id, "失败", f"关键点数量: {len(keypoints_57)}"))
                continue
            
            # 提取对应的12个关键点
            keypoints_12 = extract_12_from_57(keypoints_57)
            
            # 加载现有的点云数据
            point_cloud, pc_msg = load_existing_point_cloud(sample_id)
            
            if point_cloud is None:
                print(f"⚠️ 样本 {sample_id}: {pc_msg}")
                processing_log.append((sample_id, "失败", pc_msg))
                continue
            
            # 数据质量检查
            if np.any(np.isnan(keypoints_57)) or np.any(np.isnan(keypoints_12)):
                print(f"⚠️ 样本 {sample_id}: 关键点包含NaN")
                processing_log.append((sample_id, "失败", "关键点NaN"))
                continue
            
            if np.any(np.isnan(point_cloud)):
                print(f"⚠️ 样本 {sample_id}: 点云包含NaN")
                processing_log.append((sample_id, "失败", "点云NaN"))
                continue
            
            # 添加到数据集
            all_keypoints_57.append(keypoints_57)
            all_keypoints_12.append(keypoints_12)
            all_point_clouds.append(point_cloud)
            all_sample_ids.append(sample_id)
            all_genders.append(gender)
            processing_log.append((sample_id, "成功", f"性别: {gender}"))
            
        except Exception as e:
            print(f"❌ 样本 {sample_id}: 处理失败 - {e}")
            processing_log.append((sample_id, "失败", str(e)))
            continue
    
    if not all_keypoints_57:
        print("❌ 没有成功处理任何样本")
        return None
    
    # 转换为numpy数组
    all_keypoints_57 = np.array(all_keypoints_57, dtype=np.float32)
    all_keypoints_12 = np.array(all_keypoints_12, dtype=np.float32)
    all_point_clouds = np.array(all_point_clouds, dtype=np.float32)
    
    print(f"\n✅ 57点数据集构建完成:")
    print(f"   成功样本: {len(all_sample_ids)}")
    print(f"   失败样本: {len([log for log in processing_log if log[1] == '失败'])}")
    print(f"   点云形状: {all_point_clouds.shape}")
    print(f"   57关键点形状: {all_keypoints_57.shape}")
    print(f"   12关键点形状: {all_keypoints_12.shape}")
    
    # 按性别统计
    male_count = sum(1 for g in all_genders if g == 'male')
    female_count = sum(1 for g in all_genders if g == 'female')
    print(f"   性别分布: 男性{male_count}个, 女性{female_count}个")
    
    return {
        'point_clouds': all_point_clouds,
        'keypoints_57': all_keypoints_57,
        'keypoints_12': all_keypoints_12,
        'sample_ids': all_sample_ids,
        'genders': all_genders,
        'processing_log': processing_log
    }

def save_57_dataset(dataset_dict):
    """保存57点数据集"""
    
    print(f"\n💾 保存57点数据集...")
    
    # 保存主数据集
    np.savez_compressed('real_57_dataset_from_existing.npz',
                       point_clouds=dataset_dict['point_clouds'],
                       keypoints_57=dataset_dict['keypoints_57'],
                       keypoints_12=dataset_dict['keypoints_12'],
                       sample_ids=dataset_dict['sample_ids'],
                       genders=dataset_dict['genders'])
    
    # 按性别分离保存
    male_indices = [i for i, g in enumerate(dataset_dict['genders']) if g == 'male']
    female_indices = [i for i, g in enumerate(dataset_dict['genders']) if g == 'female']
    
    if male_indices:
        np.savez_compressed('real_57_dataset_male.npz',
                           point_clouds=dataset_dict['point_clouds'][male_indices],
                           keypoints_57=dataset_dict['keypoints_57'][male_indices],
                           keypoints_12=dataset_dict['keypoints_12'][male_indices],
                           sample_ids=[dataset_dict['sample_ids'][i] for i in male_indices])
    
    if female_indices:
        np.savez_compressed('real_57_dataset_female.npz',
                           point_clouds=dataset_dict['point_clouds'][female_indices],
                           keypoints_57=dataset_dict['keypoints_57'][female_indices],
                           keypoints_12=dataset_dict['keypoints_12'][female_indices],
                           sample_ids=[dataset_dict['sample_ids'][i] for i in female_indices])
    
    # 保存处理日志
    log_data = {
        'total_samples': len(dataset_dict['sample_ids']),
        'male_samples': len(male_indices),
        'female_samples': len(female_indices),
        'processing_log': dataset_dict['processing_log'],
        'dataset_info': {
            'point_cloud_shape': dataset_dict['point_clouds'].shape,
            'keypoints_57_shape': dataset_dict['keypoints_57'].shape,
            'keypoints_12_shape': dataset_dict['keypoints_12'].shape,
            'coordinate_system': 'XYZ',
            'point_cloud_size': 50000,
            'data_source': 'real_csv_annotations + existing_point_clouds'
        }
    }
    
    with open('real_57_dataset_log.json', 'w', encoding='utf-8') as f:
        json.dump(log_data, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"✅ 57点数据集已保存:")
    print(f"   - real_57_dataset_from_existing.npz (完整数据集)")
    print(f"   - real_57_dataset_male.npz (男性数据)")
    print(f"   - real_57_dataset_female.npz (女性数据)")
    print(f"   - real_57_dataset_log.json (处理日志)")

def compare_with_12point_dataset(dataset_dict):
    """与12点数据集对比"""
    
    print(f"\n🔍 与12点数据集对比...")
    
    try:
        # 加载原始12点数据
        male_12_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz', allow_pickle=True)
        female_12_data = np.load('archive/old_experiments/f3_reduced_12kp_female.npz', allow_pickle=True)
        
        original_12_male = len(male_12_data['sample_ids'])
        original_12_female = len(female_12_data['sample_ids'])
        
        # 新57点数据统计
        new_male = sum(1 for g in dataset_dict['genders'] if g == 'male')
        new_female = sum(1 for g in dataset_dict['genders'] if g == 'female')
        
        print(f"📊 数据集对比:")
        print(f"   原始12点数据集:")
        print(f"     男性: {original_12_male} 个")
        print(f"     女性: {original_12_female} 个")
        print(f"     总计: {original_12_male + original_12_female} 个")
        
        print(f"   新57点数据集:")
        print(f"     男性: {new_male} 个")
        print(f"     女性: {new_female} 个")
        print(f"     总计: {len(dataset_dict['sample_ids'])} 个")
        
        print(f"   数据覆盖率:")
        print(f"     男性: {new_male}/{original_12_male} ({new_male/original_12_male*100:.1f}%)")
        print(f"     女性: {new_female}/{original_12_female} ({new_female/original_12_female*100:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🎯 基于现有12点数据集管道构建57点数据集")
    print("使用真实的57点CSV标注 + 现有的点云处理方法")
    print("=" * 80)
    
    # 构建57点数据集
    dataset_dict = build_57_dataset_from_existing()
    
    if dataset_dict is not None:
        # 保存数据集
        save_57_dataset(dataset_dict)
        
        # 与12点数据集对比
        compare_with_12point_dataset(dataset_dict)
        
        print(f"\n🎉 真实57点数据集构建成功！")
        print(f"📊 最终数据集:")
        print(f"   样本数: {len(dataset_dict['sample_ids'])}")
        print(f"   点云: {dataset_dict['point_clouds'].shape}")
        print(f"   57关键点: {dataset_dict['keypoints_57'].shape}")
        print(f"   12关键点: {dataset_dict['keypoints_12'].shape}")
        
        print(f"\n💡 关键优势:")
        print(f"   ✅ 使用真实的57点CSV标注")
        print(f"   ✅ 复用已验证的点云处理管道")
        print(f"   ✅ 保持与12点数据集的一致性")
        print(f"   ✅ 完整的性别标签和样本追踪")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 在真实57点数据上训练模型")
        print(f"   2. 对比真实数据vs插值数据的性能")
        print(f"   3. 验证性能提升的原因")
        
    else:
        print("❌ 57点数据集构建失败")

if __name__ == "__main__":
    main()
