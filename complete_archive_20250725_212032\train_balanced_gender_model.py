#!/usr/bin/env python3
"""
训练平衡的男女混合模型
Train Balanced Gender Mixed Model
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import os
from tqdm import tqdm

class HeatmapRegressionNet(nn.Module):
    """Heatmap回归网络 (与之前相同)"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super(HeatmapRegressionNet, self).__init__()
        
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 点云特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        # 全局特征
        self.global_conv = nn.Conv1d(512, 1024, 1)
        
        # 特征融合
        self.fusion_conv1 = nn.Conv1d(1024 + 256, 512, 1)
        self.fusion_conv2 = nn.Conv1d(512, 256, 1)
        
        # Heatmap生成
        self.heatmap_conv1 = nn.Conv1d(256, 128, 1)
        self.heatmap_conv2 = nn.Conv1d(128, 64, 1)
        self.heatmap_conv3 = nn.Conv1d(64, num_keypoints, 1)
        
        # 激活函数和正则化
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.3)
        
        # Batch Normalization
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        # 点云特征提取
        x1 = self.relu(self.bn1(self.conv1(x)))
        x2 = self.relu(self.bn2(self.conv2(x1)))
        x3 = self.relu(self.bn3(self.conv3(x2)))
        x4 = self.relu(self.bn4(self.conv4(x3)))
        
        # 全局特征
        global_feat = self.relu(self.global_conv(x4))
        global_feat = torch.max(global_feat, 2, keepdim=True)[0]
        
        # 扩展全局特征
        global_feat_expanded = global_feat.repeat(1, 1, self.num_points)
        
        # 融合局部和全局特征
        combined_feat = torch.cat([x3, global_feat_expanded], 1)
        
        # 特征融合
        fused = self.relu(self.fusion_conv1(combined_feat))
        fused = self.dropout(fused)
        fused = self.relu(self.fusion_conv2(fused))
        
        # 生成热图
        heatmap = self.relu(self.heatmap_conv1(fused))
        heatmap = self.relu(self.heatmap_conv2(heatmap))
        heatmap = self.heatmap_conv3(heatmap)
        
        # 转置并应用softmax
        heatmap = heatmap.transpose(2, 1)
        
        # 对每个关键点的热图进行softmax
        heatmap_list = []
        for i in range(self.num_keypoints):
            hm_i = torch.softmax(heatmap[:, :, i], dim=1)
            heatmap_list.append(hm_i.unsqueeze(2))
        
        heatmap = torch.cat(heatmap_list, dim=2)
        
        return heatmap

def generate_heatmap_from_keypoints(keypoints, point_cloud, sigma=5.0):
    """从关键点生成热图"""
    heatmaps = []
    
    for kp in keypoints:
        # 计算距离
        distances = np.linalg.norm(point_cloud - kp, axis=1)
        
        # 生成高斯分布
        heatmap = np.exp(-distances**2 / (2 * sigma**2))
        
        # 归一化
        if np.sum(heatmap) > 0:
            heatmap = heatmap / np.sum(heatmap)
        
        heatmaps.append(heatmap)
    
    return np.array(heatmaps)

def enhance_male_data_to_match_female():
    """增强男性数据以匹配女性数据量"""
    
    print("🔄 增强男性数据以匹配女性数据量...")
    
    # 检查男性增强数据是否已存在
    male_aug_path = "f3_reduced_12kp_male_augmented.npz"
    if os.path.exists(male_aug_path):
        male_data = np.load(male_aug_path, allow_pickle=True)
        male_count = len(male_data['sample_ids'])
        print(f"✅ 男性增强数据已存在: {male_count}个样本")
        
        # 检查是否需要进一步增强到250个
        if male_count < 250:
            print(f"🔄 需要进一步增强男性数据: {male_count} → 250")
            # 这里可以添加进一步增强的代码
        
        return male_aug_path
    else:
        print("❌ 男性增强数据不存在，请先运行男性数据增强")
        return None

def load_balanced_dataset():
    """加载平衡的男女数据集"""
    
    print("📊 加载平衡的男女数据集...")
    
    # 加载女性增强数据
    female_path = "f3_reduced_12kp_female_augmented.npz"
    if not os.path.exists(female_path):
        print(f"❌ 女性增强数据不存在: {female_path}")
        return None
    
    female_data = np.load(female_path, allow_pickle=True)
    female_pc = female_data['point_clouds']
    female_kp = female_data['keypoints']
    
    # 如果没有热图，生成热图
    if 'heatmaps' in female_data:
        female_hm = female_data['heatmaps']
    else:
        print("🔥 为女性数据生成热图...")
        female_hm = []
        for i in tqdm(range(len(female_pc)), desc="生成女性热图"):
            hm = generate_heatmap_from_keypoints(female_kp[i], female_pc[i])
            female_hm.append(hm)
        female_hm = np.array(female_hm)
    
    print(f"✅ 女性数据: {len(female_pc)}个样本")
    
    # 加载男性增强数据
    male_path = "f3_reduced_12kp_male_augmented.npz"
    if not os.path.exists(male_path):
        print(f"❌ 男性增强数据不存在: {male_path}")
        return None
    
    male_data = np.load(male_path, allow_pickle=True)
    male_pc = male_data['point_clouds']
    male_kp = male_data['keypoints']
    
    # 如果没有热图，生成热图
    if 'heatmaps' in male_data:
        male_hm = male_data['heatmaps']
    else:
        print("🔥 为男性数据生成热图...")
        male_hm = []
        for i in tqdm(range(len(male_pc)), desc="生成男性热图"):
            hm = generate_heatmap_from_keypoints(male_kp[i], male_pc[i])
            male_hm.append(hm)
        male_hm = np.array(male_hm)
    
    print(f"✅ 男性数据: {len(male_pc)}个样本")
    
    # 平衡数据量 - 取较小的数量
    min_count = min(len(female_pc), len(male_pc))
    print(f"📊 平衡数据量: 各取{min_count}个样本")
    
    # 随机采样以平衡数据
    female_indices = np.random.choice(len(female_pc), min_count, replace=False)
    male_indices = np.random.choice(len(male_pc), min_count, replace=False)
    
    # 合并数据
    all_pc = np.concatenate([female_pc[female_indices], male_pc[male_indices]])
    all_kp = np.concatenate([female_kp[female_indices], male_kp[male_indices]])
    all_hm = np.concatenate([female_hm[female_indices], male_hm[male_indices]])
    
    # 创建性别标签
    gender_labels = np.concatenate([
        np.zeros(min_count),  # 0 = 女性
        np.ones(min_count)    # 1 = 男性
    ])
    
    print(f"📊 合并后数据集:")
    print(f"   总样本数: {len(all_pc)}")
    print(f"   女性样本: {min_count} ({min_count/len(all_pc)*100:.1f}%)")
    print(f"   男性样本: {min_count} ({min_count/len(all_pc)*100:.1f}%)")
    print(f"   点云形状: {all_pc.shape}")
    print(f"   关键点形状: {all_kp.shape}")
    print(f"   热图形状: {all_hm.shape}")
    
    return all_pc, all_kp, all_hm, gender_labels

def heatmap_loss_function(pred_heatmap, target_heatmap):
    """Heatmap损失函数"""
    
    # 检查维度
    if pred_heatmap.shape != target_heatmap.shape:
        if len(target_heatmap.shape) == 3 and target_heatmap.shape[1] == 12:
            target_heatmap = target_heatmap.transpose(1, 2)
    
    # KL散度损失
    kl_loss = nn.KLDivLoss(reduction='batchmean')
    
    total_loss = 0
    batch_size, num_points, num_keypoints = pred_heatmap.shape
    
    for i in range(num_keypoints):
        log_pred = torch.log(pred_heatmap[:, :, i] + 1e-8)
        loss_i = kl_loss(log_pred, target_heatmap[:, :, i])
        total_loss += loss_i
    
    return total_loss / num_keypoints

def extract_keypoints_from_heatmap(heatmap, point_cloud):
    """从热图中提取关键点坐标"""
    
    batch_size, num_points, num_keypoints = heatmap.shape
    keypoints = torch.zeros(batch_size, num_keypoints, 3)
    
    for b in range(batch_size):
        for k in range(num_keypoints):
            # 加权平均方法
            weights = heatmap[b, :, k]
            weighted_coords = point_cloud[b] * weights.unsqueeze(1)
            keypoint = torch.sum(weighted_coords, dim=0) / torch.sum(weights)
            keypoints[b, k] = keypoint
    
    return keypoints

def train_balanced_model():
    """训练平衡的男女混合模型"""
    
    print("🔥 开始训练平衡的男女混合Heatmap模型")
    print("⚖️ 目标: 训练一个对男女都有效的通用模型")
    print("=" * 80)
    
    # 加载平衡数据集
    data_result = load_balanced_dataset()
    if data_result is None:
        return
    
    all_pc, all_kp, all_hm, gender_labels = data_result
    
    # 数据分割
    n_samples = len(all_pc)
    n_train = int(n_samples * 0.7)
    n_val = int(n_samples * 0.15)
    
    # 随机打乱但保持性别平衡
    indices = np.random.permutation(n_samples)
    train_indices = indices[:n_train]
    val_indices = indices[n_train:n_train + n_val]
    test_indices = indices[n_train + n_val:]
    
    train_pc, train_kp, train_hm = all_pc[train_indices], all_kp[train_indices], all_hm[train_indices]
    val_pc, val_kp, val_hm = all_pc[val_indices], all_kp[val_indices], all_hm[val_indices]
    test_pc, test_kp, test_hm = all_pc[test_indices], all_kp[test_indices], all_hm[test_indices]
    
    train_gender = gender_labels[train_indices]
    val_gender = gender_labels[val_indices]
    test_gender = gender_labels[test_indices]
    
    print(f"📋 数据分割:")
    print(f"   训练集: {len(train_indices)}个样本 (女性:{np.sum(train_gender==0)}, 男性:{np.sum(train_gender==1)})")
    print(f"   验证集: {len(val_indices)}个样本 (女性:{np.sum(val_gender==0)}, 男性:{np.sum(val_gender==1)})")
    print(f"   测试集: {len(test_indices)}个样本 (女性:{np.sum(test_gender==0)}, 男性:{np.sum(test_gender==1)})")
    
    # 设备设置
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ 使用设备: {device}")
    
    # 模型初始化
    model = HeatmapRegressionNet(num_points=50000, num_keypoints=12)
    model = model.to(device)
    
    print(f"🏗️ 模型架构: 平衡男女混合HeatmapNet")
    print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 优化器
    optimizer = optim.Adam(model.parameters(), lr=0.0005, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=15, gamma=0.7)
    
    # 训练参数
    num_epochs = 40
    batch_size = 4
    best_val_error = float('inf')
    
    print(f"🎯 训练参数:")
    print(f"   训练轮数: {num_epochs}")
    print(f"   批次大小: {batch_size}")
    print(f"   学习率: 0.0005")
    
    print(f"\n🚀 开始训练...")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        n_train_batches = len(train_pc) // batch_size
        
        train_pbar = tqdm(range(n_train_batches), desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for i in train_pbar:
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(train_pc))
            
            batch_pc = torch.FloatTensor(train_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(train_kp[start_idx:end_idx]).to(device)
            batch_hm = torch.FloatTensor(train_hm[start_idx:end_idx]).to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            pred_hm = model(batch_pc)
            loss = heatmap_loss_function(pred_hm, batch_hm)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_pbar.set_postfix({'Loss': f'{loss.item():.4f}'})
        
        avg_train_loss = train_loss / n_train_batches
        
        # 验证阶段
        model.eval()
        val_errors = []
        val_errors_female = []
        val_errors_male = []
        
        with torch.no_grad():
            n_val_batches = len(val_pc) // batch_size + (1 if len(val_pc) % batch_size > 0 else 0)
            
            val_pbar = tqdm(range(n_val_batches), desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for i in val_pbar:
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(val_pc))
                
                batch_pc = torch.FloatTensor(val_pc[start_idx:end_idx]).to(device)
                batch_kp = torch.FloatTensor(val_kp[start_idx:end_idx]).to(device)
                batch_gender = val_gender[start_idx:end_idx]
                
                pred_hm = model(batch_pc)
                pred_kp = extract_keypoints_from_heatmap(pred_hm.cpu(), batch_pc.cpu())
                
                # 计算总体误差
                for j in range(len(batch_kp)):
                    error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j].cpu(), dim=1))
                    val_errors.append(error.item())
                    
                    # 按性别分类误差
                    if batch_gender[j] == 0:  # 女性
                        val_errors_female.append(error.item())
                    else:  # 男性
                        val_errors_male.append(error.item())
                
                avg_error = np.mean([e for e in val_errors[-len(batch_kp):]])
                val_pbar.set_postfix({'Error': f'{avg_error:.2f}mm'})
        
        avg_val_error = np.mean(val_errors)
        avg_val_error_female = np.mean(val_errors_female) if val_errors_female else 0
        avg_val_error_male = np.mean(val_errors_male) if val_errors_male else 0
        
        # 学习率调度
        scheduler.step()
        
        print(f"\nEpoch {epoch+1}/{num_epochs}:")
        print(f"  训练损失: {avg_train_loss:.4f}")
        print(f"  验证误差 (总体): {avg_val_error:.2f}mm")
        print(f"  验证误差 (女性): {avg_val_error_female:.2f}mm")
        print(f"  验证误差 (男性): {avg_val_error_male:.2f}mm")
        print(f"  学习率: {scheduler.get_last_lr()[0]:.6f}")
        
        # 保存最佳模型
        if avg_val_error < best_val_error:
            best_val_error = avg_val_error
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'val_error': avg_val_error,
                'val_error_female': avg_val_error_female,
                'val_error_male': avg_val_error_male,
            }, 'best_balanced_heatmap_model.pth')
            print(f"  ✅ 保存最佳模型 (验证误差: {avg_val_error:.2f}mm)")
    
    # 测试最佳模型
    print(f"\n🧪 测试最佳平衡模型...")
    checkpoint = torch.load('best_balanced_heatmap_model.pth')
    model.load_state_dict(checkpoint['model_state_dict'])
    
    model.eval()
    test_errors = []
    test_errors_female = []
    test_errors_male = []
    
    with torch.no_grad():
        n_test_batches = len(test_pc) // batch_size + (1 if len(test_pc) % batch_size > 0 else 0)
        
        test_pbar = tqdm(range(n_test_batches), desc='Testing')
        for i in test_pbar:
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(test_pc))
            
            batch_pc = torch.FloatTensor(test_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(test_kp[start_idx:end_idx]).to(device)
            batch_gender = test_gender[start_idx:end_idx]
            
            pred_hm = model(batch_pc)
            pred_kp = extract_keypoints_from_heatmap(pred_hm.cpu(), batch_pc.cpu())
            
            for j in range(len(batch_kp)):
                error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j].cpu(), dim=1))
                test_errors.append(error.item())
                
                if batch_gender[j] == 0:  # 女性
                    test_errors_female.append(error.item())
                else:  # 男性
                    test_errors_male.append(error.item())
            
            avg_error = np.mean([e for e in test_errors[-len(batch_kp):]])
            test_pbar.set_postfix({'Error': f'{avg_error:.2f}mm'})
    
    final_test_error = np.mean(test_errors)
    final_test_error_female = np.mean(test_errors_female)
    final_test_error_male = np.mean(test_errors_male)
    
    print(f"\n🎉 平衡模型训练完成!")
    print(f"=" * 80)
    print(f"📊 最终结果:")
    print(f"   测试误差 (总体): {final_test_error:.2f}mm")
    print(f"   测试误差 (女性): {final_test_error_female:.2f}mm")
    print(f"   测试误差 (男性): {final_test_error_male:.2f}mm")
    print(f"   训练样本数: {len(train_pc)} (平衡)")
    
    print(f"\n📈 性能对比:")
    print(f"   女性专用模型: 2.88mm")
    print(f"   平衡混合模型 (女性): {final_test_error_female:.2f}mm")
    print(f"   平衡混合模型 (男性): {final_test_error_male:.2f}mm")
    print(f"   平衡混合模型 (总体): {final_test_error:.2f}mm")
    
    # 分析性别差异
    gender_gap = abs(final_test_error_female - final_test_error_male)
    print(f"\n⚖️ 性别差异分析:")
    print(f"   性别间误差差异: {gender_gap:.2f}mm")
    if gender_gap < 1.0:
        print(f"   ✅ 性别差异很小，模型平衡性良好")
    elif gender_gap < 2.0:
        print(f"   ⚠️ 性别差异适中，可接受")
    else:
        print(f"   ❌ 性别差异较大，需要进一步优化")
    
    return final_test_error, final_test_error_female, final_test_error_male

def main():
    """主函数"""
    
    print("⚖️ 训练平衡的男女混合Heatmap模型")
    print("🎯 目标: 创建对男女都有效的通用模型")
    print("=" * 80)
    
    # 检查男性增强数据
    male_aug_path = enhance_male_data_to_match_female()
    if male_aug_path is None:
        print("❌ 请先运行男性数据增强")
        return
    
    # 训练平衡模型
    results = train_balanced_model()
    
    if results:
        total_error, female_error, male_error = results
        
        print(f"\n💡 实验结论:")
        print(f"✅ 成功训练了平衡的男女混合模型")
        print(f"✅ 总体性能: {total_error:.2f}mm")
        print(f"✅ 性别平衡: 女性{female_error:.2f}mm vs 男性{male_error:.2f}mm")
        
        if total_error < 5.0:
            print(f"🏆 达到医疗级精度!")
        
        print(f"\n🚀 下一步: 可视化平衡模型的性能")

if __name__ == "__main__":
    main()
