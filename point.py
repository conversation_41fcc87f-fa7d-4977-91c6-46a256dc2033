import numpy as np
from dataclasses import dataclass
import matplotlib.pyplot as plt
import cv2
import matplotlib
matplotlib.rcParams['font.family'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
matplotlib.rcParams['axes.unicode_minus'] = False

@dataclass
class Point:
    """表示图像中的一个点"""
    x: int  # x坐标
    y: int  # y坐标
    value: float  # 点的值
    
    def is_singular(self, image: np.ndarray, threshold: float = 0.2) -> bool:
        """
        判断该点是否为奇点
        
        Args:
            image: 输入图像数组
            threshold: 判断阈值
            
        Returns:
            bool: 是否为奇点
        """
        sign_changes = self._count_sign_changes(image)
        # 添加额外的条件：值的差异必须大于阈值
        neighbors = self._get_neighbors(image)
        value_diff = np.abs(neighbors - self.value).max()
        
        return sign_changes % 2 == 1 and value_diff > threshold
    
    def is_even(self, image: np.ndarray, threshold: float = 0.2) -> bool:
        """
        判断该点是否为偶点
        
        Args:
            image: 输入图像数组
            threshold: 判断阈值
            
        Returns:
            bool: 是否为偶点
        """
        sign_changes = self._count_sign_changes(image)
        # 添加额外的条件：值的差异必须大于阈值
        neighbors = self._get_neighbors(image)
        value_diff = np.abs(neighbors - self.value).max()
        
        return sign_changes % 2 == 0 and sign_changes > 2 and value_diff > threshold
    
    def _count_sign_changes(self, image: np.ndarray) -> int:
        """
        计算周围点的符号变化次数
        
        Args:
            image: 输入图像数组
            
        Returns:
            int: 符号变化的次数
        """
        neighbors = []
        for i in [-1, 0, 1]:
            for j in [-1, 0, 1]:
                if i == 0 and j == 0:
                    continue
                if 0 <= self.x + i < image.shape[0] and 0 <= self.y + j < image.shape[1]:
                    neighbors.append(image[self.x + i, self.y + j])
        
        sign_changes = 0
        neighbors = np.array(neighbors)
        diff = neighbors - self.value
        signs = np.sign(diff)
        
        for i in range(len(signs)):
            if signs[i] != signs[i-1]:
                sign_changes += 1
                
        return sign_changes
    
    def _get_neighbors(self, image: np.ndarray) -> np.ndarray:
        """
        获取点周围的8个邻居点的值
        
        Args:
            image: 输入图像数组
            
        Returns:
            np.ndarray: 邻居点的值数组
        """
        neighbors = []
        for i in [-1, 0, 1]:
            for j in [-1, 0, 1]:
                if i == 0 and j == 0:
                    continue
                if 0 <= self.x + i < image.shape[0] and 0 <= self.y + j < image.shape[1]:
                    neighbors.append(image[self.x + i, self.y + j])
        
        return np.array(neighbors)
    
    @staticmethod
    def visualize_points(image: np.ndarray, points: list['Point'], figsize=(10, 10)):
        """
        可视化图像中的奇点和偶点
        
        Args:
            image: 输入图像数组
            points: Point对象列表
            figsize: 图像大小
        """
        plt.figure(figsize=figsize)
        
        # 显示原始图像
        plt.imshow(image, cmap='gray')
        
        # 遍历所有点并标记
        for point in points:
            if point.is_singular(image):
                plt.plot(point.y, point.x, 'ro', markersize=10, label='Singular Point')
            elif point.is_even(image):
                plt.plot(point.y, point.x, 'bo', markersize=10, label='Even Point')
        
        # 去除重复的图例
        handles, labels = plt.gca().get_legend_handles_labels()
        by_label = dict(zip(labels, handles))
        plt.legend(by_label.values(), by_label.keys())
        
        plt.title('检测到的奇点(红色)和偶点(蓝色)')
        plt.axis('on')
        plt.show()
    
    @classmethod
    def detect_points(cls, image: np.ndarray, step: int = 1) -> list['Point']:
        """
        在整个图像中检测奇点和偶点
        
        Args:
            image: 输入图像数组
            step: 采样步长，可以用来控制检测密度
            
        Returns:
            list[Point]: 检测到的特征点列表
        """
        points = []
        for i in range(1, image.shape[0]-1, step):
            for j in range(1, image.shape[1]-1, step):
                point = cls(x=i, y=j, value=image[i, j])
                if point.is_singular(image) or point.is_even(image):
                    points.append(point)
        return points

if __name__ == "__main__":
    # 读取指纹图像
    test_image = cv2.imread('fingerprint.png', cv2.IMREAD_GRAYSCALE)
    if test_image is None:
        print("无法读取图像文件")
        exit()
    
    # 归一化到 [0,1] 范围
    test_image = test_image.astype(float) / 255.0
    
    # 可选：使用高斯模糊减少噪声
    test_image = cv2.GaussianBlur(test_image, (3,3), 0)
    
    # 检测特征点
    points = Point.detect_points(test_image, step=2)
    
    # 可视化结果
    Point.visualize_points(test_image, points)
