#!/usr/bin/env python3
"""
构建57关键点数据集
Build 57 keypoints dataset from original data
"""

import numpy as np
import pandas as pd
import os
from pathlib import Path
import json
from tqdm import tqdm

class Dataset57Builder:
    """57关键点数据集构建器"""
    
    def __init__(self):
        self.data_dir = Path("data/Data")
        self.annotations_dir = self.data_dir / "annotations"
        self.stl_dir = self.data_dir / "stl_models"
        
        # 12点到57点的映射关系
        self.mapping_12_to_57 = {
            0: 0,   # F1-1 -> 原始索引0
            1: 1,   # F1-2 -> 原始索引1
            2: 2,   # F1-3 -> 原始索引2
            3: 12,  # F1-13 -> 原始索引12
            4: 19,  # F2-1 -> 原始索引19
            5: 20,  # F2-2 -> 原始索引20
            6: 21,  # F2-3 -> 原始索引21
            7: 31,  # F2-13 -> 原始索引31
            8: 38,  # F3-1 -> 原始索引38
            9: 52,  # F3-15 -> 原始索引52
            10: 50, # F3-13 -> 原始索引50
            11: 51, # F3-14 -> 原始索引51
        }
        
        self.results = {
            'samples_processed': 0,
            'samples_valid': 0,
            'samples_failed': [],
            'point_clouds_57': [],
            'keypoints_57': [],
            'keypoints_12_extracted': [],
            'sample_ids': [],
            'gender_info': []
        }
    
    def load_point_cloud_combined(self, sample_id):
        """加载合并的点云数据"""
        
        try:
            # 尝试加载已处理的点云文件
            pc_file = f"{sample_id}_pointcloud.npy"
            if os.path.exists(pc_file):
                point_cloud = np.load(pc_file)
                return point_cloud
            
            # 如果没有预处理文件，尝试从STL生成
            return self.generate_point_cloud_from_stl(sample_id)
            
        except Exception as e:
            print(f"⚠️ {sample_id}: 点云加载失败 - {e}")
            return None
    
    def generate_point_cloud_from_stl(self, sample_id):
        """从STL文件生成点云"""
        
        try:
            import open3d as o3d
            
            combined_points = []
            
            # 加载F1, F2, F3三个STL文件
            for region in ['F_1', 'F_2', 'F_3']:
                stl_file = self.stl_dir / f"{sample_id}-{region}.stl"
                
                if stl_file.exists():
                    mesh = o3d.io.read_triangle_mesh(str(stl_file))
                    
                    if len(mesh.vertices) > 0:
                        # 从mesh采样点云
                        pcd = mesh.sample_points_uniformly(number_of_points=15000)
                        points = np.asarray(pcd.points)
                        combined_points.append(points)
            
            if combined_points:
                # 合并所有区域的点云
                all_points = np.vstack(combined_points)
                
                # 下采样到50000个点
                if len(all_points) > 50000:
                    indices = np.random.choice(len(all_points), 50000, replace=False)
                    all_points = all_points[indices]
                elif len(all_points) < 30000:
                    # 如果点太少，进行上采样
                    indices = np.random.choice(len(all_points), 50000, replace=True)
                    all_points = all_points[indices]
                
                return all_points
            
        except ImportError:
            print("⚠️ Open3D未安装，无法从STL生成点云")
        except Exception as e:
            print(f"⚠️ STL点云生成失败: {e}")
        
        return None
    
    def load_57_keypoints(self, sample_id):
        """加载57个关键点"""
        
        ann_file = self.annotations_dir / f"{sample_id}-Table-XYZ.CSV"
        
        if not ann_file.exists():
            return None
        
        try:
            # 尝试不同编码
            for encoding in ['gbk', 'utf-8', 'latin1']:
                try:
                    df = pd.read_csv(ann_file, encoding=encoding)
                    break
                except:
                    continue
            else:
                return None
            
            if len(df) < 57:
                return None
            
            # 提取57个关键点坐标
            keypoints_57 = []
            for idx, row in df.iterrows():
                if idx >= 57:  # 只取前57个
                    break
                x, y, z = row['X'], row['Y'], row['Z']
                keypoints_57.append([x, y, z])
            
            return np.array(keypoints_57)
            
        except Exception as e:
            print(f"⚠️ {sample_id}: 关键点加载失败 - {e}")
            return None
    
    def extract_12_from_57(self, keypoints_57):
        """从57个关键点中提取12个核心关键点"""
        
        keypoints_12 = np.zeros((12, 3))
        
        for i in range(12):
            original_idx = self.mapping_12_to_57[i]
            if original_idx < len(keypoints_57):
                keypoints_12[i] = keypoints_57[original_idx]
        
        return keypoints_12
    
    def determine_gender(self, sample_id):
        """确定样本性别（基于现有数据集）"""
        
        # 加载现有的性别分类数据
        try:
            male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz', allow_pickle=True)
            female_data = np.load('archive/old_experiments/f3_reduced_12kp_female.npz', allow_pickle=True)
            
            # 检查样本ID是否在现有数据中
            # 这里需要根据实际的样本ID匹配逻辑来实现
            # 暂时使用简单的规则
            sample_num = int(sample_id)
            
            # 基于样本编号的简单分类（需要根据实际情况调整）
            if sample_num % 3 == 0:  # 简化的分类规则
                return 'female'
            else:
                return 'male'
                
        except:
            # 如果无法确定，返回unknown
            return 'unknown'
    
    def process_single_sample(self, sample_id):
        """处理单个样本"""
        
        # 加载57个关键点
        keypoints_57 = self.load_57_keypoints(sample_id)
        if keypoints_57 is None:
            return False, "关键点加载失败"
        
        # 加载点云
        point_cloud = self.load_point_cloud_combined(sample_id)
        if point_cloud is None:
            return False, "点云加载失败"
        
        # 提取12个核心关键点
        keypoints_12 = self.extract_12_from_57(keypoints_57)
        
        # 确定性别
        gender = self.determine_gender(sample_id)
        
        # 保存到结果中
        self.results['point_clouds_57'].append(point_cloud)
        self.results['keypoints_57'].append(keypoints_57)
        self.results['keypoints_12_extracted'].append(keypoints_12)
        self.results['sample_ids'].append(sample_id)
        self.results['gender_info'].append(gender)
        
        return True, "成功"
    
    def build_dataset(self, max_samples=None):
        """构建完整的57关键点数据集"""
        
        print("🚀 开始构建57关键点数据集...")
        print("=" * 60)
        
        # 获取所有标注文件
        annotation_files = list(self.annotations_dir.glob("*-Table-XYZ.CSV"))
        print(f"📁 找到 {len(annotation_files)} 个标注文件")
        
        if max_samples:
            annotation_files = annotation_files[:max_samples]
            print(f"🎯 限制处理 {max_samples} 个样本")
        
        # 处理每个样本
        for ann_file in tqdm(annotation_files, desc="处理样本"):
            sample_id = ann_file.stem.split('-')[0]
            
            self.results['samples_processed'] += 1
            
            success, message = self.process_single_sample(sample_id)
            
            if success:
                self.results['samples_valid'] += 1
                print(f"✅ {sample_id}: {message}")
            else:
                self.results['samples_failed'].append((sample_id, message))
                print(f"❌ {sample_id}: {message}")
        
        # 转换为numpy数组
        if self.results['samples_valid'] > 0:
            self.results['point_clouds_57'] = np.array(self.results['point_clouds_57'], dtype=object)
            self.results['keypoints_57'] = np.array(self.results['keypoints_57'])
            self.results['keypoints_12_extracted'] = np.array(self.results['keypoints_12_extracted'])
            self.results['sample_ids'] = np.array(self.results['sample_ids'])
            self.results['gender_info'] = np.array(self.results['gender_info'])
        
        # 打印统计信息
        self.print_statistics()
        
        return self.results
    
    def print_statistics(self):
        """打印统计信息"""
        
        print(f"\n📊 数据集构建统计:")
        print(f"   处理样本数: {self.results['samples_processed']}")
        print(f"   有效样本数: {self.results['samples_valid']}")
        print(f"   失败样本数: {len(self.results['samples_failed'])}")
        print(f"   成功率: {self.results['samples_valid']/self.results['samples_processed']*100:.1f}%")
        
        if self.results['samples_valid'] > 0:
            print(f"\n📋 数据形状:")
            print(f"   点云数组: {len(self.results['point_clouds_57'])} 个样本")
            print(f"   57关键点: {self.results['keypoints_57'].shape}")
            print(f"   12关键点: {self.results['keypoints_12_extracted'].shape}")
            
            # 性别分布
            gender_counts = {}
            for gender in self.results['gender_info']:
                gender_counts[gender] = gender_counts.get(gender, 0) + 1
            
            print(f"\n👥 性别分布:")
            for gender, count in gender_counts.items():
                print(f"   {gender}: {count}个样本")
        
        if self.results['samples_failed']:
            print(f"\n❌ 失败样本:")
            for sample_id, reason in self.results['samples_failed'][:5]:
                print(f"   {sample_id}: {reason}")
            if len(self.results['samples_failed']) > 5:
                print(f"   ... 还有 {len(self.results['samples_failed'])-5} 个失败样本")
    
    def save_dataset(self, filename_prefix="dataset_57_keypoints"):
        """保存数据集"""
        
        if self.results['samples_valid'] == 0:
            print("❌ 没有有效样本，无法保存数据集")
            return
        
        print(f"\n💾 保存57关键点数据集...")
        
        # 保存完整数据集
        np.savez(f'{filename_prefix}_complete.npz',
                 point_clouds=self.results['point_clouds_57'],
                 keypoints_57=self.results['keypoints_57'],
                 keypoints_12=self.results['keypoints_12_extracted'],
                 sample_ids=self.results['sample_ids'],
                 gender_info=self.results['gender_info'])
        
        print(f"✅ 完整数据集已保存: {filename_prefix}_complete.npz")
        
        # 按性别分离保存
        male_indices = [i for i, gender in enumerate(self.results['gender_info']) if gender == 'male']
        female_indices = [i for i, gender in enumerate(self.results['gender_info']) if gender == 'female']
        
        if male_indices:
            np.savez(f'{filename_prefix}_male.npz',
                     point_clouds=self.results['point_clouds_57'][male_indices],
                     keypoints_57=self.results['keypoints_57'][male_indices],
                     keypoints_12=self.results['keypoints_12_extracted'][male_indices],
                     sample_ids=self.results['sample_ids'][male_indices])
            print(f"✅ 男性数据集已保存: {filename_prefix}_male.npz ({len(male_indices)}个样本)")
        
        if female_indices:
            np.savez(f'{filename_prefix}_female.npz',
                     point_clouds=self.results['point_clouds_57'][female_indices],
                     keypoints_57=self.results['keypoints_57'][female_indices],
                     keypoints_12=self.results['keypoints_12_extracted'][female_indices],
                     sample_ids=self.results['sample_ids'][female_indices])
            print(f"✅ 女性数据集已保存: {filename_prefix}_female.npz ({len(female_indices)}个样本)")
        
        # 保存映射关系
        mapping_info = {
            'mapping_12_to_57': self.mapping_12_to_57,
            'keypoint_names': self.get_keypoint_names(),
            'dataset_info': {
                'total_samples': self.results['samples_valid'],
                'male_samples': len(male_indices),
                'female_samples': len(female_indices),
                'keypoints_57': 57,
                'keypoints_12': 12,
                'point_cloud_size': 50000
            }
        }
        
        with open(f'{filename_prefix}_mapping.json', 'w', encoding='utf-8') as f:
            json.dump(mapping_info, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 映射信息已保存: {filename_prefix}_mapping.json")
    
    def get_keypoint_names(self):
        """获取关键点名称"""
        
        keypoint_names = {
            'F1': [f"F1-{i+1}" for i in range(19)],
            'F2': [f"F2-{i+1}" for i in range(19)],
            'F3': [f"F3-{i+1}" for i in range(19)]
        }
        
        return keypoint_names

def validate_dataset(dataset_file):
    """验证数据集质量"""
    
    print(f"\n🔍 验证数据集质量: {dataset_file}")
    print("=" * 50)
    
    try:
        data = np.load(dataset_file, allow_pickle=True)
        
        point_clouds = data['point_clouds']
        keypoints_57 = data['keypoints_57']
        keypoints_12 = data['keypoints_12']
        sample_ids = data['sample_ids']
        
        print(f"📊 数据集基本信息:")
        print(f"   样本数量: {len(sample_ids)}")
        print(f"   点云形状: {len(point_clouds)} 个样本")
        print(f"   57关键点形状: {keypoints_57.shape}")
        print(f"   12关键点形状: {keypoints_12.shape}")
        
        # 验证数据质量
        print(f"\n🔍 数据质量检查:")
        
        # 检查关键点范围
        kp57_ranges = {
            'X': (keypoints_57[:, :, 0].min(), keypoints_57[:, :, 0].max()),
            'Y': (keypoints_57[:, :, 1].min(), keypoints_57[:, :, 1].max()),
            'Z': (keypoints_57[:, :, 2].min(), keypoints_57[:, :, 2].max())
        }
        
        print(f"   57关键点坐标范围:")
        for axis, (min_val, max_val) in kp57_ranges.items():
            print(f"     {axis}: {min_val:.1f} ~ {max_val:.1f}")
        
        # 检查12关键点是否正确提取
        print(f"\n✅ 12关键点提取验证:")
        for i in range(min(3, len(sample_ids))):  # 检查前3个样本
            sample_id = sample_ids[i]
            kp_57 = keypoints_57[i]
            kp_12 = keypoints_12[i]
            
            print(f"   样本 {sample_id}:")
            print(f"     F1-1 (57点索引0): {kp_57[0]}")
            print(f"     F1-1 (12点索引0): {kp_12[0]}")
            print(f"     匹配: {np.allclose(kp_57[0], kp_12[0])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据集验证失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🎯 57关键点数据集构建器")
    print("从原始数据构建完整的57关键点训练数据集")
    print("=" * 80)
    
    # 创建构建器
    builder = Dataset57Builder()
    
    # 构建数据集（先处理20个样本测试）
    print("🚀 开始构建数据集（测试模式：20个样本）...")
    results = builder.build_dataset(max_samples=20)
    
    if results['samples_valid'] > 0:
        # 保存数据集
        builder.save_dataset("test_dataset_57_keypoints")
        
        # 验证数据集
        validate_dataset("test_dataset_57_keypoints_complete.npz")
        
        print(f"\n🎉 测试数据集构建完成！")
        print(f"📋 下一步建议:")
        print(f"   1. 检查测试数据集质量")
        print(f"   2. 如果满意，运行完整数据集构建")
        print(f"   3. 开始训练12→57扩展模型")
        
        # 询问是否构建完整数据集
        print(f"\n❓ 是否继续构建完整数据集？")
        print(f"   修改 max_samples=None 来处理所有样本")
        
    else:
        print(f"❌ 数据集构建失败，请检查数据路径和格式")

if __name__ == "__main__":
    main()
