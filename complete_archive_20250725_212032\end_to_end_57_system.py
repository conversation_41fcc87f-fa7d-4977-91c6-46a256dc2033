#!/usr/bin/env python3
"""
改进的端到端57关键点检测系统
Improved End-to-end 57 keypoints detection system
基于高质量数据集和验证的改进策略
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import json
import os
from tqdm import tqdm

# 导入已验证的12点模型架构
class FixedMultiModalPointNet(nn.Module):
    """已验证的12点PointNet模型"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super(FixedMultiModalPointNet, self).__init__()
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 点云特征提取器
        self.point_conv1 = nn.Conv1d(3, 64, 1)
        self.point_conv2 = nn.Conv1d(64, 128, 1)
        self.point_conv3 = nn.Conv1d(128, 256, 1)
        self.point_conv4 = nn.Conv1d(256, 512, 1)
        self.point_conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化层
        self.point_bn1 = nn.BatchNorm1d(64)
        self.point_bn2 = nn.BatchNorm1d(128)
        self.point_bn3 = nn.BatchNorm1d(256)
        self.point_bn4 = nn.BatchNorm1d(512)
        self.point_bn5 = nn.BatchNorm1d(1024)
        
        # 全局特征提取
        self.global_conv1 = nn.Conv1d(1024, 512, 1)
        self.global_conv2 = nn.Conv1d(512, 256, 1)
        self.global_bn1 = nn.BatchNorm1d(512)
        self.global_bn2 = nn.BatchNorm1d(256)
        
        # 关键点预测头
        self.keypoint_fc1 = nn.Linear(256, 512)
        self.keypoint_fc2 = nn.Linear(512, 256)
        self.keypoint_fc3 = nn.Linear(256, num_keypoints * 3)
        
        # Dropout层
        self.dropout1 = nn.Dropout(0.3)
        self.dropout2 = nn.Dropout(0.4)
        
    def forward(self, point_cloud):
        """前向传播"""
        batch_size = point_cloud.size(0)
        
        # 点云特征提取
        x = point_cloud.transpose(2, 1)  # [B, 3, N]
        
        x = torch.relu(self.point_bn1(self.point_conv1(x)))
        x = torch.relu(self.point_bn2(self.point_conv2(x)))
        x = torch.relu(self.point_bn3(self.point_conv3(x)))
        x = torch.relu(self.point_bn4(self.point_conv4(x)))
        x = torch.relu(self.point_bn5(self.point_conv5(x)))
        
        # 全局最大池化
        global_feature = torch.max(x, 2)[0]  # [B, 1024]
        
        # 全局特征处理
        x = global_feature.unsqueeze(2)  # [B, 1024, 1]
        x = torch.relu(self.global_bn1(self.global_conv1(x)))
        x = torch.relu(self.global_bn2(self.global_conv2(x)))
        x = x.squeeze(2)  # [B, 256]
        
        # 关键点预测
        x = torch.relu(self.keypoint_fc1(x))
        x = self.dropout1(x)
        x = torch.relu(self.keypoint_fc2(x))
        x = self.dropout2(x)
        keypoints = self.keypoint_fc3(x)
        
        # 重塑为关键点坐标
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

# 导入已训练的扩展网络
class AnatomicalExpansionNetwork(nn.Module):
    """解剖学扩展网络"""
    
    def __init__(self, input_dim=36, output_dim=171):
        super().__init__()
        
        # 解剖学知识编码器
        self.anatomy_encoder = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 512),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 区域特异性解码器
        self.f1_decoder = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, 19 * 3)  # F1区域19个点
        )
        
        self.f2_decoder = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, 19 * 3)  # F2区域19个点
        )
        
        self.f3_decoder = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, 19 * 3)  # F3区域19个点
        )
        
        # 12点到57点的映射
        self.mapping_12_to_57 = {
            0: 0, 1: 1, 2: 2, 3: 12,      # F1区域
            4: 19, 5: 20, 6: 21, 7: 31,   # F2区域
            8: 38, 9: 52, 10: 50, 11: 51  # F3区域
        }
        
    def forward(self, keypoints_12):
        """前向传播"""
        batch_size = keypoints_12.size(0)
        
        # 展平输入
        x = keypoints_12.view(batch_size, -1)
        
        # 解剖学特征编码
        features = self.anatomy_encoder(x)
        
        # 区域特异性解码
        f1_points = self.f1_decoder(features).view(batch_size, 19, 3)
        f2_points = self.f2_decoder(features).view(batch_size, 19, 3)
        f3_points = self.f3_decoder(features).view(batch_size, 19, 3)
        
        # 合并所有区域
        keypoints_57 = torch.cat([f1_points, f2_points, f3_points], dim=1)
        
        # 应用已知12点约束
        keypoints_57 = self.apply_known_constraints(keypoints_57, keypoints_12)
        
        return keypoints_57
    
    def apply_known_constraints(self, keypoints_57, keypoints_12):
        """应用已知12点约束"""
        
        # 将已知的12个点直接复制到对应位置
        for i in range(12):
            target_idx = self.mapping_12_to_57[i]
            keypoints_57[:, target_idx, :] = keypoints_12[:, i, :]
        
        return keypoints_57

class EndToEnd57System(nn.Module):
    """端到端57关键点检测系统"""
    
    def __init__(self):
        super().__init__()
        
        # 第一阶段: 点云 → 12关键点
        self.pointnet_12 = FixedMultiModalPointNet(num_points=50000, num_keypoints=12)
        
        # 第二阶段: 12关键点 → 57关键点
        self.expansion_net = AnatomicalExpansionNetwork()
        
        # 是否冻结第一阶段
        self.freeze_stage1 = False
        
    def forward(self, point_cloud):
        """前向传播"""
        
        # 阶段1: 点云 → 12关键点
        if self.freeze_stage1:
            with torch.no_grad():
                keypoints_12 = self.pointnet_12(point_cloud)
        else:
            keypoints_12 = self.pointnet_12(point_cloud)
        
        # 阶段2: 12关键点 → 57关键点
        keypoints_57 = self.expansion_net(keypoints_12)
        
        return keypoints_57, keypoints_12
    
    def set_freeze_stage1(self, freeze=True):
        """设置是否冻结第一阶段"""
        self.freeze_stage1 = freeze
        for param in self.pointnet_12.parameters():
            param.requires_grad = not freeze

class KeypointDataset57(Dataset):
    """57关键点数据集"""
    
    def __init__(self, point_clouds, keypoints_57):
        self.point_clouds = point_clouds
        self.keypoints_57 = keypoints_57
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return torch.FloatTensor(self.point_clouds[idx]), torch.FloatTensor(self.keypoints_57[idx])

def load_end_to_end_data():
    """加载端到端训练数据"""
    
    print("📊 加载端到端训练数据...")
    
    try:
        # 加载已验证的点云和12点数据
        data = np.load('smart_expanded_57_dataset.npz', allow_pickle=True)
        point_clouds = data['proven_point_clouds']
        keypoints_12 = data['proven_12_keypoints']
        interpolated_57 = data['interpolated_57_keypoints']
        
        print(f"✅ 数据加载成功:")
        print(f"   点云: {point_clouds.shape}")
        print(f"   12关键点: {keypoints_12.shape}")
        print(f"   57关键点: {interpolated_57.shape}")
        
        return point_clouds, interpolated_57, keypoints_12
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None, None, None

def train_end_to_end_system(model, train_loader, val_loader, epochs=30, device='cuda'):
    """训练端到端系统"""
    
    print(f"🚀 开始训练端到端57关键点系统...")
    print(f"   设备: {device}")
    print(f"   训练样本: {len(train_loader.dataset)}")
    print(f"   验证样本: {len(val_loader.dataset)}")
    print(f"   训练轮数: {epochs}")
    
    model = model.to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=0.0001, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=8, factor=0.5)
    criterion = nn.MSELoss()
    
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_error_57': [],
        'val_error_57': [],
        'train_error_12': [],
        'val_error_12': []
    }
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 15
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_error_57 = 0.0
        train_error_12 = 0.0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{epochs} [Train]')
        for batch_pc, batch_kp57 in train_pbar:
            batch_pc = batch_pc.to(device)
            batch_kp57 = batch_kp57.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            predicted_57, predicted_12 = model(batch_pc)
            
            # 计算57点损失
            loss_57 = criterion(predicted_57, batch_kp57)
            
            # 提取真实的12点用于监督
            mapping_12_to_57 = {
                0: 0, 1: 1, 2: 2, 3: 12, 4: 19, 5: 20,
                6: 21, 7: 31, 8: 38, 9: 52, 10: 50, 11: 51
            }
            
            true_12 = torch.zeros_like(predicted_12)
            for i in range(12):
                true_12[:, i, :] = batch_kp57[:, mapping_12_to_57[i], :]
            
            loss_12 = criterion(predicted_12, true_12)
            
            # 总损失
            total_loss = loss_57 + 0.5 * loss_12
            
            # 反向传播
            total_loss.backward()
            optimizer.step()
            
            train_loss += total_loss.item()
            
            # 计算误差
            with torch.no_grad():
                distances_57 = torch.norm(predicted_57 - batch_kp57, dim=2)
                distances_12 = torch.norm(predicted_12 - true_12, dim=2)
                train_error_57 += torch.mean(distances_57).item()
                train_error_12 += torch.mean(distances_12).item()
            
            train_pbar.set_postfix({
                'Loss': f'{total_loss.item():.4f}',
                'Err57': f'{torch.mean(distances_57).item():.2f}mm',
                'Err12': f'{torch.mean(distances_12).item():.2f}mm'
            })
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_error_57 = 0.0
        val_error_12 = 0.0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{epochs} [Val]')
            for batch_pc, batch_kp57 in val_pbar:
                batch_pc = batch_pc.to(device)
                batch_kp57 = batch_kp57.to(device)
                
                predicted_57, predicted_12 = model(batch_pc)
                
                loss_57 = criterion(predicted_57, batch_kp57)
                
                true_12 = torch.zeros_like(predicted_12)
                for i in range(12):
                    true_12[:, i, :] = batch_kp57[:, mapping_12_to_57[i], :]
                
                loss_12 = criterion(predicted_12, true_12)
                total_loss = loss_57 + 0.5 * loss_12
                
                val_loss += total_loss.item()
                
                distances_57 = torch.norm(predicted_57 - batch_kp57, dim=2)
                distances_12 = torch.norm(predicted_12 - true_12, dim=2)
                val_error_57 += torch.mean(distances_57).item()
                val_error_12 += torch.mean(distances_12).item()
                
                val_pbar.set_postfix({
                    'Loss': f'{total_loss.item():.4f}',
                    'Err57': f'{torch.mean(distances_57).item():.2f}mm',
                    'Err12': f'{torch.mean(distances_12).item():.2f}mm'
                })
        
        # 计算平均值
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error_57 /= len(train_loader)
        val_error_57 /= len(val_loader)
        train_error_12 /= len(train_loader)
        val_error_12 /= len(val_loader)
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_error_57'].append(train_error_57)
        history['val_error_57'].append(val_error_57)
        history['train_error_12'].append(train_error_12)
        history['val_error_12'].append(val_error_12)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_end_to_end_57.pth')
        else:
            patience_counter += 1
        
        # 打印进度
        print(f"Epoch {epoch+1:3d}: "
              f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")
        print(f"           "
              f"57点误差: {train_error_57:.2f}/{val_error_57:.2f}mm, "
              f"12点误差: {train_error_12:.2f}/{val_error_12:.2f}mm")
        
        # 早停
        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    print(f"✅ 训练完成！最佳验证损失: {best_val_loss:.6f}")
    
    # 加载最佳模型
    model.load_state_dict(torch.load('best_end_to_end_57.pth'))
    
    return history

def main():
    """主函数"""
    
    print("🎯 端到端57关键点检测系统")
    print("点云 → 12关键点 → 57关键点")
    print("=" * 80)
    
    # 检查CUDA
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载数据
    point_clouds, keypoints_57, keypoints_12 = load_end_to_end_data()
    
    if point_clouds is None:
        print("❌ 数据加载失败，退出")
        return
    
    # 数据划分
    indices = np.arange(len(point_clouds))
    train_indices, test_indices = train_test_split(
        indices, test_size=0.2, random_state=42
    )
    train_indices, val_indices = train_test_split(
        train_indices, test_size=0.2, random_state=42
    )
    
    # 创建数据集
    train_dataset = KeypointDataset57(point_clouds[train_indices], keypoints_57[train_indices])
    val_dataset = KeypointDataset57(point_clouds[val_indices], keypoints_57[val_indices])
    test_dataset = KeypointDataset57(point_clouds[test_indices], keypoints_57[test_indices])
    
    # 创建数据加载器 (确保batch_size > 1 避免BatchNorm问题)
    train_loader = DataLoader(train_dataset, batch_size=max(2, min(4, len(train_dataset))), shuffle=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=max(2, min(4, len(val_dataset))), shuffle=False, drop_last=True)
    test_loader = DataLoader(test_dataset, batch_size=max(2, min(4, len(test_dataset))), shuffle=False, drop_last=True)
    
    print(f"📋 数据划分:")
    print(f"   训练集: {len(train_dataset)} 样本")
    print(f"   验证集: {len(val_dataset)} 样本")
    print(f"   测试集: {len(test_dataset)} 样本")
    
    # 创建端到端模型
    model = EndToEnd57System()
    
    # 加载预训练的扩展网络权重
    try:
        expansion_weights = torch.load('best_anatomical_expansion.pth')
        model.expansion_net.load_state_dict(expansion_weights)
        print("✅ 已加载预训练的扩展网络权重")
    except:
        print("⚠️ 未找到预训练的扩展网络权重，将从头训练")
    
    print(f"🤖 模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练端到端系统
    history = train_end_to_end_system(model, train_loader, val_loader, epochs=25, device=device)
    
    # 测试最终性能
    model.eval()
    test_error_57 = 0.0
    test_error_12 = 0.0
    
    with torch.no_grad():
        for batch_pc, batch_kp57 in test_loader:
            batch_pc = batch_pc.to(device)
            batch_kp57 = batch_kp57.to(device)
            
            predicted_57, predicted_12 = model(batch_pc)
            
            distances_57 = torch.norm(predicted_57 - batch_kp57, dim=2)
            test_error_57 += torch.mean(distances_57).item()
            
            # 计算12点误差
            mapping_12_to_57 = {
                0: 0, 1: 1, 2: 2, 3: 12, 4: 19, 5: 20,
                6: 21, 7: 31, 8: 38, 9: 52, 10: 50, 11: 51
            }
            
            true_12 = torch.zeros_like(predicted_12)
            for i in range(12):
                true_12[:, i, :] = batch_kp57[:, mapping_12_to_57[i], :]
            
            distances_12 = torch.norm(predicted_12 - true_12, dim=2)
            test_error_12 += torch.mean(distances_12).item()
    
    test_error_57 /= len(test_loader)
    test_error_12 /= len(test_loader)
    
    print(f"\n🎯 最终测试结果:")
    print(f"   57关键点误差: {test_error_57:.2f}mm")
    print(f"   12关键点误差: {test_error_12:.2f}mm")
    
    # 保存结果
    results = {
        'test_error_57': test_error_57,
        'test_error_12': test_error_12,
        'training_history': history
    }
    
    with open('end_to_end_57_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n🎉 端到端57关键点系统训练完成！")
    print(f"📋 生成的文件:")
    print(f"   - best_end_to_end_57.pth (最佳端到端模型)")
    print(f"   - end_to_end_57_results.json (评估结果)")
    
    print(f"\n📊 性能总结:")
    print(f"   原始12点模型: 4.84-5.64mm")
    print(f"   扩展网络: 6.07mm")
    print(f"   端到端57点: {test_error_57:.2f}mm")
    
    if test_error_57 < 10:
        print(f"✅ 端到端57点系统性能优秀！")
    else:
        print(f"⚠️ 需要进一步优化")

if __name__ == "__main__":
    main()
