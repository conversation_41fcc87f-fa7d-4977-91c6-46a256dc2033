#!/usr/bin/env python3
"""
Thorough Workspace Cleanup

Remove all experimental files and keep only essential items for dataset quality research.
"""

import shutil
from pathlib import Path
import json

def identify_files_to_keep():
    """Identify essential files to keep"""
    
    essential_files = {
        # Core data and analysis
        "Data/",
        "train_wang2022_12point.py",  # User's original file
        "analyze_dataset_fundamental_issues.py",  # Current focus
        "dataset_fundamental_analysis.json",  # Key findings
        
        # Archive
        "Archive_F3_Experiments/",
        
        # Cleanup scripts
        "cleanup_and_archive.py",
        "thorough_cleanup.py"
    }
    
    return essential_files

def identify_files_to_remove():
    """Identify categories of files to remove"""
    
    remove_patterns = [
        # Model files
        "*.pth",
        
        # Training scripts (archived)
        "train_*.py",
        "retrain_*.py",
        
        # Dataset creation scripts (archived)
        "create_*.py",
        
        # Analysis scripts (archived, except current focus)
        "analyze_*.py",
        "test_*.py",
        "debug_*.py",
        "verify_*.py",
        "check_*.py",
        "investigate_*.py",
        "diagnose_*.py",
        
        # Optimization scripts (archived)
        "optimize_*.py",
        "improve_*.py",
        "enhanced_*.py",
        "advanced_*.py",
        "robust_*.py",
        
        # Visualization scripts (archived)
        "visualize_*.py",
        "showcase_*.py",
        "generate_*.py",
        
        # Model implementation files (archived)
        "wang2022_*.py",
        "medical_*.py",
        "lightweight_*.py",
        "memory_optimized_*.py",
        "paper_inspired_*.py",
        
        # Utility scripts (archived)
        "simple_*.py",
        "minimal_*.py",
        "quick_*.py",
        "final_*.py",
        "comprehensive_*.py",
        "complete_*.py",
        "focused_*.py",
        "basic_*.py",
        "standard_*.py",
        
        # Result files
        "*.json",
        "*.png",
        "*.html",
        "*.md",
        "*.log",
        "*.h5",
        "*.txt",
        
        # Directories (datasets, logs, results, etc.)
        "*Dataset*/",
        "*Split*/",
        "logs/",
        "models/",
        "results/",
        "visualizations/",
        "evaluation_plots/",
        "surface_projection_analysis/",
        "data_quality_analysis/",
        "dataset_comparison/",
        "original_data_analysis/",
        "original_vs_processed_analysis/",
        "root_cause_analysis/",
        "__pycache__/",
        "StandardDatasets/",
        "CleanedData/",
        "ProjectedData/",
        "MEDICAL_KEYPOINT_OPTIMIZATION_WORK_*/",
        "MedicalGrade_Dataset/",
        "F3SimpleDataset/"
    ]
    
    return remove_patterns

def safe_remove(path):
    """Safely remove file or directory"""
    try:
        if path.is_file():
            path.unlink()
            return True
        elif path.is_dir():
            shutil.rmtree(path)
            return True
    except Exception as e:
        print(f"   ⚠️ 无法删除 {path}: {e}")
        return False
    return False

def thorough_cleanup():
    """Perform thorough cleanup"""
    
    print("🧹 **彻底清理工作区**")
    print("🎯 **目标: 只保留数据集质量研究的核心文件**")
    print("=" * 80)
    
    # Get essential files
    essential_files = identify_files_to_keep()
    remove_patterns = identify_files_to_remove()
    
    print(f"📋 **保留的核心文件**:")
    for file in essential_files:
        print(f"   ✅ {file}")
    
    # Scan current directory
    all_items = list(Path('.').iterdir())
    
    # Categorize items
    to_keep = []
    to_remove = []
    
    for item in all_items:
        # Skip hidden files
        if item.name.startswith('.'):
            continue
            
        # Check if should keep
        should_keep = False
        for essential in essential_files:
            if essential.endswith('/'):
                # Directory
                if item.name == essential.rstrip('/'):
                    should_keep = True
                    break
            else:
                # File
                if item.name == essential:
                    should_keep = True
                    break
        
        if should_keep:
            to_keep.append(item)
        else:
            # Check if matches remove patterns
            should_remove = False
            for pattern in remove_patterns:
                if pattern.endswith('/'):
                    # Directory pattern
                    pattern_name = pattern.rstrip('/')
                    if '*' in pattern_name:
                        # Wildcard pattern
                        pattern_prefix = pattern_name.replace('*', '')
                        if pattern_prefix in item.name and item.is_dir():
                            should_remove = True
                            break
                    else:
                        # Exact directory name
                        if item.name == pattern_name and item.is_dir():
                            should_remove = True
                            break
                else:
                    # File pattern
                    if '*' in pattern:
                        # Wildcard pattern
                        if pattern.startswith('*'):
                            # Extension pattern
                            ext = pattern[1:]
                            if item.name.endswith(ext):
                                should_remove = True
                                break
                        else:
                            # Prefix pattern
                            prefix = pattern.replace('*', '')
                            if item.name.startswith(prefix):
                                should_remove = True
                                break
                    else:
                        # Exact file name
                        if item.name == pattern:
                            should_remove = True
                            break
            
            if should_remove:
                to_remove.append(item)
            else:
                # Unmatched items - ask what to do
                to_keep.append(item)
    
    print(f"\n📊 **清理统计**:")
    print(f"   保留文件/目录: {len(to_keep)}")
    print(f"   删除文件/目录: {len(to_remove)}")
    
    if to_remove:
        print(f"\n🗑️ **将要删除的项目** (前20个):")
        for i, item in enumerate(to_remove[:20]):
            item_type = "📁" if item.is_dir() else "📄"
            print(f"   {item_type} {item.name}")
        
        if len(to_remove) > 20:
            print(f"   ... 还有 {len(to_remove) - 20} 个项目")
        
        # Perform cleanup
        print(f"\n🧹 **执行清理**:")
        removed_count = 0
        failed_count = 0
        
        for item in to_remove:
            if safe_remove(item):
                removed_count += 1
            else:
                failed_count += 1
        
        print(f"   ✅ 成功删除: {removed_count}")
        print(f"   ❌ 删除失败: {failed_count}")
    
    # Show final state
    print(f"\n📁 **清理后的工作区**:")
    remaining_items = list(Path('.').iterdir())
    remaining_items = [item for item in remaining_items if not item.name.startswith('.')]
    
    for item in sorted(remaining_items):
        item_type = "📁" if item.is_dir() else "📄"
        print(f"   {item_type} {item.name}")
    
    # Create cleanup summary
    cleanup_summary = {
        "cleanup_date": str(pd.Timestamp.now()) if 'pd' in globals() else "Unknown",
        "purpose": "Thorough workspace cleanup for dataset quality research focus",
        "files_kept": len(to_keep),
        "files_removed": len(to_remove),
        "essential_files": list(essential_files),
        "remaining_files": [item.name for item in remaining_items],
        "cleanup_categories": [
            "Model files (.pth)",
            "Training scripts",
            "Dataset creation scripts", 
            "Analysis scripts (except current focus)",
            "Optimization scripts",
            "Visualization scripts",
            "Result files and logs",
            "Experimental datasets",
            "Cache directories"
        ]
    }
    
    with open('cleanup_summary.json', 'w') as f:
        json.dump(cleanup_summary, f, indent=2)
    
    print(f"\n🎉 **清理完成!**")
    print(f"📁 清理总结: cleanup_summary.json")
    print(f"🎯 工作区现在专注于数据集质量研究")
    
    print(f"\n💡 **下一步**:")
    print(f"   1. 专注分析数据集质量问题")
    print(f"   2. 研究STL-CSV对齐解决方案")
    print(f"   3. 撰写数据集质量评估论文")
    print(f"   4. 所有实验记录都在 Archive_F3_Experiments/ 中")

def main():
    """Main cleanup function"""
    
    try:
        thorough_cleanup()
        print(f"\n✅ 工作区彻底清理成功!")
        return True
    except Exception as e:
        print(f"❌ 清理过程出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n🎯 现在可以专注于数据集质量问题了!")
    else:
        print(f"\n❌ 清理失败，请检查错误")
