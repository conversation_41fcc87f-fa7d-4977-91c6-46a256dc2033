{"method": "CHaR-Inspired Conditioned Heatmap Regression", "baseline_error": 5.829, "best_val_error": 54.06261672973633, "improvement": -827.4766980568936, "training_time_minutes": 0.39644519488016766, "epochs_trained": 30, "history": [{"epoch": 1, "train_losses": {"total": 0.06228981714914827, "regression": 0.0, "classification": 0.622898156152052}, "val_losses": {"total": 1413.0143310546875, "regression": 1412.9626220703126, "classification": 0.5172823309898377}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 59.20165863037109, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 2, "train_losses": {"total": 0.03769415288287051, "regression": 0.0, "classification": 0.3769415196250467}, "val_losses": {"total": 1256.5010986328125, "regression": 1256.4643798828124, "classification": 0.36696826815605166}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 56.519483947753905, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 3, "train_losses": {"total": 0.023332531070884538, "regression": 0.0, "classification": 0.2333253043539384}, "val_losses": {"total": 1195.3612182617187, "regression": 1195.335888671875, "classification": 0.2533960103988647}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 55.26539840698242, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 4, "train_losses": {"total": 0.015308177920387071, "regression": 0.0, "classification": 0.15308177646468668}, "val_losses": {"total": 1695.33046875, "regression": 1695.3112548828126, "classification": 0.19186398684978484}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 61.76598434448242, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 5, "train_losses": {"total": 0.011005974046009429, "regression": 0.0, "classification": 0.11005973815917969}, "val_losses": {"total": 1578.2575927734374, "regression": 1578.243701171875, "classification": 0.13885197639465333}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 63.098296356201175, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 6, "train_losses": {"total": 0.008446081651045996, "regression": 0.0, "classification": 0.08446081508608426}, "val_losses": {"total": 1287.3864990234374, "regression": 1287.374755859375, "classification": 0.11726676523685456}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 57.27885437011719, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 7, "train_losses": {"total": 0.005966454963473713, "regression": 0.0, "classification": 0.05966454788165934}, "val_losses": {"total": 1312.5503662109375, "regression": 1312.5416259765625, "classification": 0.0876273587346077}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 57.4015396118164, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 8, "train_losses": {"total": 0.004506774082341615, "regression": 0.0, "classification": 0.04506774027557934}, "val_losses": {"total": 1305.4208740234376, "regression": 1305.413623046875, "classification": 0.07259542942047119}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 57.24102783203125, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 9, "train_losses": {"total": 0.0038022549740751, "regression": 0.0, "classification": 0.03802254916552235}, "val_losses": {"total": 1572.9047119140625, "regression": 1572.898291015625, "classification": 0.06442205011844634}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 62.63733978271485, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 10, "train_losses": {"total": 0.0031468273167881895, "regression": 0.0, "classification": 0.03146827264743693}, "val_losses": {"total": 1174.9999877929688, "regression": 1174.99560546875, "classification": 0.04385540783405304}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 54.06261672973633, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 11, "train_losses": {"total": 0.0030222437618409887, "regression": 0.0, "classification": 0.03022243718014044}, "val_losses": {"total": 1991.444189453125, "regression": 1991.440673828125, "classification": 0.034522741287946704}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 69.2525421142578, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 12, "train_losses": {"total": 0.0024749658837476198, "regression": 0.0, "classification": 0.024749658125288346}, "val_losses": {"total": 1498.30419921875, "regression": 1498.300927734375, "classification": 0.032733990624547}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 60.954035949707034, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 13, "train_losses": {"total": 0.0020750099176760108, "regression": 0.0, "classification": 0.02075009880697026}, "val_losses": {"total": 1363.9580810546875, "regression": 1363.955126953125, "classification": 0.029445043951272964}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 58.46522674560547, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 14, "train_losses": {"total": 0.0017958969247582205, "regression": 0.0, "classification": 0.017958968823008677}, "val_losses": {"total": 1204.4861083984374, "regression": 1204.4831176757812, "classification": 0.029877688363194467}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 55.61517105102539, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 15, "train_losses": {"total": 0.0014658758334596368, "regression": 0.0, "classification": 0.014658758142853485}, "val_losses": {"total": 1424.31064453125, "regression": 1424.3082763671875, "classification": 0.023738612607121467}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 59.80434951782227, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 16, "train_losses": {"total": 0.0014485401710878838, "regression": 0.0, "classification": 0.014485401375328793}, "val_losses": {"total": 1384.1736083984374, "regression": 1384.171337890625, "classification": 0.022604391723871232}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 58.81786346435547, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 17, "train_losses": {"total": 0.0012920012437354993, "regression": 0.0, "classification": 0.012920012177132508}, "val_losses": {"total": 1596.835205078125, "regression": 1596.833056640625, "classification": 0.02152819260954857}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 63.317684173583984, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 18, "train_losses": {"total": 0.001220217686803902, "regression": 0.0, "classification": 0.012202176594120614}, "val_losses": {"total": 1614.9086791992188, "regression": 1614.9069946289062, "classification": 0.016888689994812012}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 61.904150390625, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 19, "train_losses": {"total": 0.0009906173115322256, "regression": 0.0, "classification": 0.009906173081082456}, "val_losses": {"total": 1434.115380859375, "regression": 1434.1137939453124, "classification": 0.015888161584734918}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 59.66376647949219, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 20, "train_losses": {"total": 0.0009081733741742723, "regression": 0.0, "classification": 0.009081733604783522}, "val_losses": {"total": 1508.134521484375, "regression": 1508.1331298828125, "classification": 0.013862062618136406}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 60.78713760375977, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 21, "train_losses": {"total": 0.0008855278788627509, "regression": 0.0, "classification": 0.008855278685908108}, "val_losses": {"total": 1515.8943115234374, "regression": 1515.89306640625, "classification": 0.012628008797764777}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 60.703921508789065, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 22, "train_losses": {"total": 0.0008677452364388634, "regression": 0.0, "classification": 0.00867745214525391}, "val_losses": {"total": 1232.1350830078125, "regression": 1232.133837890625, "classification": 0.012356120720505714}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 55.51054153442383, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 23, "train_losses": {"total": 0.000770229341202032, "regression": 0.0, "classification": 0.007702293257941218}, "val_losses": {"total": 1320.1499389648438, "regression": 1320.1486206054688, "classification": 0.013275805674493314}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 57.84634628295898, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00056}, {"epoch": 24, "train_losses": {"total": 0.0006673777910798569, "regression": 0.0, "classification": 0.006673777804655188}, "val_losses": {"total": 1726.639306640625, "regression": 1726.63828125, "classification": 0.01003368068486452}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 64.87561340332032, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00056}, {"epoch": 25, "train_losses": {"total": 0.0006093204737065689, "regression": 0.0, "classification": 0.006093204582986587}, "val_losses": {"total": 1312.63330078125, "regression": 1312.6324951171875, "classification": 0.007909058500081301}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 56.95922622680664, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00056}, {"epoch": 26, "train_losses": {"total": 0.0006551524994018324, "regression": 0.0, "classification": 0.006551524870755041}, "val_losses": {"total": 1350.8486083984376, "regression": 1350.84775390625, "classification": 0.008603869006037712}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 58.72854843139648, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00056}, {"epoch": 27, "train_losses": {"total": 0.0005328215877799427, "regression": 0.0, "classification": 0.0053282158641035065}, "val_losses": {"total": 1410.675927734375, "regression": 1410.6751220703125, "classification": 0.00768378833308816}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 59.8243408203125, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00056}, {"epoch": 28, "train_losses": {"total": 0.0006281581762082436, "regression": 0.0, "classification": 0.006281581679906915}, "val_losses": {"total": 1613.012353515625, "regression": 1613.01162109375, "classification": 0.007333619240671396}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 62.51230697631836, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00056}, {"epoch": 29, "train_losses": {"total": 0.0005491701749098651, "regression": 0.0, "classification": 0.00549170166692313}, "val_losses": {"total": 1367.15869140625, "regression": 1367.1579833984374, "classification": 0.00742210429161787}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 57.53556137084961, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00056}, {"epoch": 30, "train_losses": {"total": 0.0005117585105301045, "regression": 0.0, "classification": 0.005117585047093385}, "val_losses": {"total": 1385.8144775390624, "regression": 1385.8138061523437, "classification": 0.006581442337483168}, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 100.0, "within_7mm_percent": 100.0}, "val_metrics": {"mean_distance": 58.04001312255859, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00056}], "char_config": {"lambda_reg": 1.0, "lambda_cls": 0.1, "lambda_heatmap": 0.0, "multi_task_learning": true, "conditioned_regression": true}}