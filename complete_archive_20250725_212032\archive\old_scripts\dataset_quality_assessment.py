#!/usr/bin/env python3
"""
数据集质量评估工具 - 第1阶段实施
全面评估当前数据集的质量问题，为后续改进提供依据
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.neighbors import NearestNeighbors
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
# import open3d as o3d  # 暂时注释掉，使用其他方法
import json
import os
from datetime import datetime

class DatasetQualityAssessment:
    """数据集质量评估器"""
    
    def __init__(self, dataset_path='f3_reduced_12kp_stable.npz'):
        self.dataset_path = dataset_path
        self.results = {}
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建结果目录
        self.result_dir = f"quality_assessment_{self.timestamp}"
        os.makedirs(self.result_dir, exist_ok=True)
        
        print(f"📊 数据集质量评估器初始化")
        print(f"📁 结果保存目录: {self.result_dir}")
    
    def load_dataset(self):
        """加载数据集"""
        
        print(f"\n📂 加载数据集: {self.dataset_path}")
        
        try:
            data = np.load(self.dataset_path, allow_pickle=True)
            self.sample_ids = data['sample_ids']
            self.point_clouds = data['point_clouds']
            self.keypoints = data['keypoints']
            
            print(f"✅ 数据集加载成功")
            print(f"   样本数量: {len(self.sample_ids)}")
            print(f"   点云形状: {self.point_clouds[0].shape if len(self.point_clouds) > 0 else 'N/A'}")
            print(f"   关键点形状: {self.keypoints[0].shape if len(self.keypoints) > 0 else 'N/A'}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据集加载失败: {e}")
            return False
    
    def assess_coordinate_consistency(self):
        """评估坐标系一致性"""
        
        print(f"\n🔍 评估坐标系一致性...")
        
        distances_to_surface = []
        problematic_samples = []
        
        for i, (sample_id, pc, kp) in enumerate(zip(self.sample_ids, self.point_clouds, self.keypoints)):
            try:
                # 计算关键点到最近表面点的距离
                nbrs = NearestNeighbors(n_neighbors=1, algorithm='kd_tree').fit(pc)
                distances, indices = nbrs.kneighbors(kp)
                
                avg_distance = np.mean(distances)
                max_distance = np.max(distances)
                
                distances_to_surface.append({
                    'sample_id': sample_id,
                    'avg_distance': avg_distance,
                    'max_distance': max_distance,
                    'distances': distances.flatten()
                })
                
                # 识别问题样本 (平均距离 > 5mm)
                if avg_distance > 5.0:
                    problematic_samples.append({
                        'sample_id': sample_id,
                        'avg_distance': avg_distance,
                        'max_distance': max_distance
                    })
                
            except Exception as e:
                print(f"   ⚠️ 样本 {sample_id} 处理失败: {e}")
        
        # 统计分析
        all_avg_distances = [d['avg_distance'] for d in distances_to_surface]
        all_max_distances = [d['max_distance'] for d in distances_to_surface]
        
        coord_results = {
            'total_samples': len(distances_to_surface),
            'avg_distance_mean': np.mean(all_avg_distances),
            'avg_distance_std': np.std(all_avg_distances),
            'avg_distance_median': np.median(all_avg_distances),
            'max_distance_mean': np.mean(all_max_distances),
            'max_distance_std': np.std(all_max_distances),
            'problematic_samples': len(problematic_samples),
            'problematic_ratio': len(problematic_samples) / len(distances_to_surface),
            'quality_score': 1 - (len(problematic_samples) / len(distances_to_surface))
        }
        
        print(f"📊 坐标系一致性评估结果:")
        print(f"   平均距离: {coord_results['avg_distance_mean']:.3f} ± {coord_results['avg_distance_std']:.3f}mm")
        print(f"   中位数距离: {coord_results['avg_distance_median']:.3f}mm")
        print(f"   最大距离均值: {coord_results['max_distance_mean']:.3f}mm")
        print(f"   问题样本: {coord_results['problematic_samples']}/{coord_results['total_samples']} ({coord_results['problematic_ratio']*100:.1f}%)")
        print(f"   质量评分: {coord_results['quality_score']*100:.1f}%")
        
        # 保存详细结果
        coord_results['detailed_distances'] = distances_to_surface
        coord_results['problematic_samples_list'] = problematic_samples
        
        self.results['coordinate_consistency'] = coord_results
        
        # 创建可视化
        self.visualize_coordinate_consistency(all_avg_distances, all_max_distances)
        
        return coord_results
    
    def assess_annotation_quality(self):
        """评估标注质量"""
        
        print(f"\n🔍 评估标注质量...")
        
        # 分析关键点分布
        all_keypoints = np.array(self.keypoints)  # [N, 12, 3]
        
        # 计算每个关键点的统计信息
        keypoint_stats = []
        for kp_idx in range(12):
            kp_coords = all_keypoints[:, kp_idx, :]  # [N, 3]
            
            stats = {
                'keypoint_index': kp_idx,
                'mean': np.mean(kp_coords, axis=0),
                'std': np.std(kp_coords, axis=0),
                'range': np.max(kp_coords, axis=0) - np.min(kp_coords, axis=0),
                'total_variance': np.sum(np.var(kp_coords, axis=0))
            }
            keypoint_stats.append(stats)
        
        # 分析关键点间距离的一致性
        inter_keypoint_distances = []
        for i, (pc, kp) in enumerate(zip(self.point_clouds, self.keypoints)):
            distances = []
            for j in range(12):
                for k in range(j+1, 12):
                    dist = np.linalg.norm(kp[j] - kp[k])
                    distances.append(dist)
            inter_keypoint_distances.append(distances)
        
        inter_keypoint_distances = np.array(inter_keypoint_distances)  # [N, 66]
        
        # 计算距离的变异系数 (CV)
        distance_cvs = []
        for dist_idx in range(inter_keypoint_distances.shape[1]):
            distances = inter_keypoint_distances[:, dist_idx]
            cv = np.std(distances) / np.mean(distances) if np.mean(distances) > 0 else 0
            distance_cvs.append(cv)
        
        annotation_results = {
            'keypoint_statistics': keypoint_stats,
            'inter_distance_cv_mean': np.mean(distance_cvs),
            'inter_distance_cv_std': np.std(distance_cvs),
            'high_variance_keypoints': [i for i, stats in enumerate(keypoint_stats) 
                                      if stats['total_variance'] > np.mean([s['total_variance'] for s in keypoint_stats]) * 2],
            'annotation_consistency_score': 1 - min(np.mean(distance_cvs), 1.0)
        }
        
        print(f"📊 标注质量评估结果:")
        print(f"   关键点间距离变异系数: {annotation_results['inter_distance_cv_mean']:.3f} ± {annotation_results['inter_distance_cv_std']:.3f}")
        print(f"   高变异关键点: {annotation_results['high_variance_keypoints']}")
        print(f"   标注一致性评分: {annotation_results['annotation_consistency_score']*100:.1f}%")
        
        self.results['annotation_quality'] = annotation_results
        
        # 创建可视化
        self.visualize_annotation_quality(keypoint_stats, distance_cvs)
        
        return annotation_results
    
    def assess_data_distribution(self):
        """评估数据分布"""
        
        print(f"\n🔍 评估数据分布...")
        
        # 将关键点展平作为特征
        features = self.keypoints.reshape(len(self.keypoints), -1)  # [N, 36]
        
        # PCA分析
        pca = PCA(n_components=min(10, features.shape[1]))
        features_pca = pca.fit_transform(features)
        
        # 聚类分析
        n_clusters = min(5, len(features))
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        cluster_labels = kmeans.fit_predict(features_pca[:, :3])
        
        # 计算聚类分布
        cluster_counts = np.bincount(cluster_labels)
        cluster_balance = np.std(cluster_counts) / np.mean(cluster_counts)
        
        # 计算样本间距离分布
        from sklearn.metrics.pairwise import pairwise_distances
        distances = pairwise_distances(features_pca[:, :3])
        
        # 去除对角线元素
        mask = np.triu(np.ones_like(distances, dtype=bool), k=1)
        pairwise_dists = distances[mask]
        
        distribution_results = {
            'pca_explained_variance': pca.explained_variance_ratio_[:5].tolist(),
            'cumulative_variance': np.cumsum(pca.explained_variance_ratio_[:5]).tolist(),
            'cluster_counts': cluster_counts.tolist(),
            'cluster_balance_score': 1 - min(cluster_balance, 1.0),
            'pairwise_distance_mean': np.mean(pairwise_dists),
            'pairwise_distance_std': np.std(pairwise_dists),
            'distribution_uniformity_score': 1 - (np.std(pairwise_dists) / np.mean(pairwise_dists))
        }
        
        print(f"📊 数据分布评估结果:")
        print(f"   前5个主成分解释方差: {distribution_results['pca_explained_variance']}")
        print(f"   累积解释方差: {distribution_results['cumulative_variance']}")
        print(f"   聚类分布: {distribution_results['cluster_counts']}")
        print(f"   聚类平衡评分: {distribution_results['cluster_balance_score']*100:.1f}%")
        print(f"   分布均匀性评分: {distribution_results['distribution_uniformity_score']*100:.1f}%")
        
        self.results['data_distribution'] = distribution_results
        
        # 创建可视化
        self.visualize_data_distribution(features_pca, cluster_labels, pca)
        
        return distribution_results
    
    def detect_outliers(self):
        """检测异常值"""
        
        print(f"\n🔍 检测异常值...")
        
        # 基于关键点特征检测异常
        features = self.keypoints.reshape(len(self.keypoints), -1)
        
        # 使用Isolation Forest检测异常
        from sklearn.ensemble import IsolationForest
        iso_forest = IsolationForest(contamination=0.1, random_state=42)
        outlier_labels = iso_forest.fit_predict(features)
        
        # 基于距离的异常检测
        from sklearn.neighbors import LocalOutlierFactor
        lof = LocalOutlierFactor(n_neighbors=min(10, len(features)-1))
        lof_labels = lof.fit_predict(features)
        
        # 合并异常检测结果
        outlier_indices = []
        outlier_samples = []
        
        for i, (iso_label, lof_label) in enumerate(zip(outlier_labels, lof_labels)):
            if iso_label == -1 or lof_label == -1:
                outlier_indices.append(i)
                outlier_samples.append({
                    'sample_id': self.sample_ids[i],
                    'index': i,
                    'isolation_forest': iso_label == -1,
                    'local_outlier_factor': lof_label == -1
                })
        
        outlier_results = {
            'total_outliers': len(outlier_indices),
            'outlier_ratio': len(outlier_indices) / len(features),
            'outlier_samples': outlier_samples,
            'data_quality_score': 1 - (len(outlier_indices) / len(features))
        }
        
        print(f"📊 异常值检测结果:")
        print(f"   异常样本数: {outlier_results['total_outliers']}/{len(features)} ({outlier_results['outlier_ratio']*100:.1f}%)")
        print(f"   数据质量评分: {outlier_results['data_quality_score']*100:.1f}%")
        
        if outlier_samples:
            print(f"   异常样本ID: {[s['sample_id'] for s in outlier_samples[:5]]}")
        
        self.results['outlier_detection'] = outlier_results
        
        return outlier_results
    
    def check_completeness(self):
        """检查数据完整性"""
        
        print(f"\n🔍 检查数据完整性...")
        
        completeness_issues = []
        
        # 检查数据形状一致性
        expected_pc_shape = self.point_clouds[0].shape if len(self.point_clouds) > 0 else None
        expected_kp_shape = self.keypoints[0].shape if len(self.keypoints) > 0 else None
        
        for i, (sample_id, pc, kp) in enumerate(zip(self.sample_ids, self.point_clouds, self.keypoints)):
            issues = []
            
            # 检查点云形状
            if pc.shape != expected_pc_shape:
                issues.append(f"点云形状不一致: {pc.shape} vs {expected_pc_shape}")
            
            # 检查关键点形状
            if kp.shape != expected_kp_shape:
                issues.append(f"关键点形状不一致: {kp.shape} vs {expected_kp_shape}")
            
            # 检查NaN值
            if np.isnan(pc).any():
                issues.append("点云包含NaN值")
            
            if np.isnan(kp).any():
                issues.append("关键点包含NaN值")
            
            # 检查无穷值
            if np.isinf(pc).any():
                issues.append("点云包含无穷值")
            
            if np.isinf(kp).any():
                issues.append("关键点包含无穷值")
            
            if issues:
                completeness_issues.append({
                    'sample_id': sample_id,
                    'index': i,
                    'issues': issues
                })
        
        completeness_results = {
            'total_samples': len(self.sample_ids),
            'problematic_samples': len(completeness_issues),
            'completeness_score': 1 - (len(completeness_issues) / len(self.sample_ids)),
            'issues': completeness_issues
        }
        
        print(f"📊 数据完整性检查结果:")
        print(f"   总样本数: {completeness_results['total_samples']}")
        print(f"   问题样本数: {completeness_results['problematic_samples']}")
        print(f"   完整性评分: {completeness_results['completeness_score']*100:.1f}%")
        
        self.results['completeness'] = completeness_results
        
        return completeness_results
    
    def visualize_coordinate_consistency(self, avg_distances, max_distances):
        """可视化坐标一致性"""
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 平均距离分布
        ax1.hist(avg_distances, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.axvline(np.mean(avg_distances), color='red', linestyle='--', 
                   label=f'Mean: {np.mean(avg_distances):.3f}mm')
        ax1.axvline(5.0, color='orange', linestyle='--', 
                   label='Threshold: 5.0mm')
        ax1.set_xlabel('Average Distance to Surface (mm)')
        ax1.set_ylabel('Frequency')
        ax1.set_title('Distribution of Average Distances')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 最大距离分布
        ax2.hist(max_distances, bins=20, alpha=0.7, color='lightcoral', edgecolor='black')
        ax2.axvline(np.mean(max_distances), color='red', linestyle='--', 
                   label=f'Mean: {np.mean(max_distances):.3f}mm')
        ax2.set_xlabel('Maximum Distance to Surface (mm)')
        ax2.set_ylabel('Frequency')
        ax2.set_title('Distribution of Maximum Distances')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{self.result_dir}/coordinate_consistency.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def visualize_annotation_quality(self, keypoint_stats, distance_cvs):
        """可视化标注质量"""
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 关键点变异性
        variances = [stats['total_variance'] for stats in keypoint_stats]
        ax1.bar(range(12), variances, color='lightgreen', alpha=0.7)
        ax1.set_xlabel('Keypoint Index')
        ax1.set_ylabel('Total Variance')
        ax1.set_title('Keypoint Position Variance')
        ax1.grid(True, alpha=0.3)
        
        # 距离变异系数分布
        ax2.hist(distance_cvs, bins=20, alpha=0.7, color='gold', edgecolor='black')
        ax2.axvline(np.mean(distance_cvs), color='red', linestyle='--', 
                   label=f'Mean CV: {np.mean(distance_cvs):.3f}')
        ax2.set_xlabel('Coefficient of Variation')
        ax2.set_ylabel('Frequency')
        ax2.set_title('Inter-keypoint Distance CV Distribution')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{self.result_dir}/annotation_quality.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def visualize_data_distribution(self, features_pca, cluster_labels, pca):
        """可视化数据分布"""
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # PCA散点图
        scatter = ax1.scatter(features_pca[:, 0], features_pca[:, 1], 
                            c=cluster_labels, cmap='tab10', alpha=0.7)
        ax1.set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]*100:.1f}%)')
        ax1.set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]*100:.1f}%)')
        ax1.set_title('PCA Visualization with Clusters')
        ax1.grid(True, alpha=0.3)
        
        # 解释方差比例
        ax2.bar(range(1, 6), pca.explained_variance_ratio_[:5], 
               color='purple', alpha=0.7)
        ax2.set_xlabel('Principal Component')
        ax2.set_ylabel('Explained Variance Ratio')
        ax2.set_title('PCA Explained Variance')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{self.result_dir}/data_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_quality_report(self):
        """生成质量评估报告"""
        
        print(f"\n📋 生成质量评估报告...")
        
        # 计算总体质量评分
        scores = {
            'coordinate_consistency': self.results['coordinate_consistency']['quality_score'],
            'annotation_quality': self.results['annotation_quality']['annotation_consistency_score'],
            'data_distribution': (self.results['data_distribution']['cluster_balance_score'] + 
                                self.results['data_distribution']['distribution_uniformity_score']) / 2,
            'outlier_detection': self.results['outlier_detection']['data_quality_score'],
            'completeness': self.results['completeness']['completeness_score']
        }
        
        overall_score = np.mean(list(scores.values()))
        
        # 生成报告
        report = {
            'assessment_timestamp': self.timestamp,
            'dataset_path': self.dataset_path,
            'total_samples': len(self.sample_ids),
            'overall_quality_score': overall_score,
            'individual_scores': scores,
            'detailed_results': self.results,
            'recommendations': self.generate_recommendations(scores)
        }
        
        # 保存报告
        with open(f'{self.result_dir}/quality_assessment_report.json', 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        # 打印摘要
        print(f"📊 **数据集质量评估摘要**:")
        print(f"   总体质量评分: {overall_score*100:.1f}%")
        print(f"   坐标一致性: {scores['coordinate_consistency']*100:.1f}%")
        print(f"   标注质量: {scores['annotation_quality']*100:.1f}%")
        print(f"   数据分布: {scores['data_distribution']*100:.1f}%")
        print(f"   异常检测: {scores['outlier_detection']*100:.1f}%")
        print(f"   数据完整性: {scores['completeness']*100:.1f}%")
        
        return report
    
    def generate_recommendations(self, scores):
        """生成改进建议"""
        
        recommendations = []
        
        if scores['coordinate_consistency'] < 0.8:
            recommendations.append({
                'priority': 'High',
                'issue': 'Coordinate System Inconsistency',
                'description': '关键点与表面距离过大，需要修复坐标系对齐',
                'action': '重新对齐STL文件和CSV标注的坐标系'
            })
        
        if scores['annotation_quality'] < 0.8:
            recommendations.append({
                'priority': 'High',
                'issue': 'Annotation Quality Issues',
                'description': '标注一致性不足，存在较大变异',
                'action': '建立标准化标注流程，进行多人交叉验证'
            })
        
        if scores['data_distribution'] < 0.7:
            recommendations.append({
                'priority': 'Medium',
                'issue': 'Data Distribution Imbalance',
                'description': '数据分布不均衡，某些区域覆盖不足',
                'action': '收集更多样化的数据，平衡各类别样本'
            })
        
        if scores['outlier_detection'] < 0.9:
            recommendations.append({
                'priority': 'Medium',
                'issue': 'Outlier Samples Present',
                'description': '存在异常样本，可能影响训练效果',
                'action': '审核并处理异常样本，考虑移除或修复'
            })
        
        if scores['completeness'] < 1.0:
            recommendations.append({
                'priority': 'High',
                'issue': 'Data Completeness Issues',
                'description': '存在数据完整性问题',
                'action': '修复数据完整性问题，确保所有样本完整'
            })
        
        # 总体建议
        if len(self.sample_ids) < 200:
            recommendations.append({
                'priority': 'High',
                'issue': 'Insufficient Data Volume',
                'description': f'当前样本量({len(self.sample_ids)})不足以支撑深度学习',
                'action': '扩充数据集到200-500个高质量样本'
            })
        
        return recommendations
    
    def run_full_assessment(self):
        """运行完整的质量评估"""
        
        print("🚀 **开始数据集质量全面评估**")
        print("=" * 80)
        
        # 加载数据集
        if not self.load_dataset():
            return None
        
        # 执行各项评估
        self.assess_coordinate_consistency()
        self.assess_annotation_quality()
        self.assess_data_distribution()
        self.detect_outliers()
        self.check_completeness()
        
        # 生成报告
        report = self.generate_quality_report()
        
        print(f"\n🎉 **质量评估完成!**")
        print(f"📁 详细结果保存在: {self.result_dir}/")
        print(f"📋 查看完整报告: {self.result_dir}/quality_assessment_report.json")
        
        return report

def main():
    """主函数"""
    
    print("📊 **数据集质量评估工具 - 第1阶段实施**")
    print("🎯 **目标: 全面评估当前数据集质量，识别改进方向**")
    print("=" * 80)
    
    # 创建评估器
    assessor = DatasetQualityAssessment()
    
    # 运行评估
    report = assessor.run_full_assessment()
    
    if report:
        print(f"\n💡 **下一步行动建议**:")
        for i, rec in enumerate(report['recommendations'][:3], 1):
            print(f"   {i}. [{rec['priority']}] {rec['issue']}")
            print(f"      {rec['action']}")

if __name__ == "__main__":
    main()
