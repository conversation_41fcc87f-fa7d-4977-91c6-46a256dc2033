#!/usr/bin/env python3
"""
分析医生标注的不同策略
识别哪些关键点基于几何信息，哪些基于解剖学知识
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns

# 定义F3关键点的标注策略类型
F3_ANNOTATION_STRATEGIES = {
    0: {"name": "F3-1", "strategy": "anatomical", "basis": "sacral_body_center", "consistency": "medium"},
    1: {"name": "F3-2", "strategy": "geometric", "basis": "anterior_boundary", "consistency": "high"},
    2: {"name": "F3-3", "strategy": "anatomical", "basis": "sacral_foramen", "consistency": "low"},
    3: {"name": "F3-4", "strategy": "relative", "basis": "between_landmarks", "consistency": "low"},
    4: {"name": "F3-5", "strategy": "anatomical", "basis": "sacral_ridge", "consistency": "medium"},
    5: {"name": "F3-6", "strategy": "relative", "basis": "proportional_position", "consistency": "low"},
    6: {"name": "F3-7", "strategy": "anatomical", "basis": "sacral_body", "consistency": "medium"},
    7: {"name": "F3-8", "strategy": "geometric", "basis": "central_mass", "consistency": "high"},
    8: {"name": "F3-9", "strategy": "relative", "basis": "symmetric_position", "consistency": "medium"},
    9: {"name": "F3-10", "strategy": "anatomical", "basis": "sacral_feature", "consistency": "medium"},
    10: {"name": "F3-11", "strategy": "relative", "basis": "proportional_spacing", "consistency": "low"},
    11: {"name": "F3-12", "strategy": "geometric", "basis": "posterior_boundary", "consistency": "high"},
    12: {"name": "F3-13", "strategy": "geometric", "basis": "z_maximum", "consistency": "high"},
    13: {"name": "F3-14", "strategy": "geometric", "basis": "left_boundary", "consistency": "medium"},
    14: {"name": "F3-15", "strategy": "relative", "basis": "between_boundaries", "consistency": "low"},
    15: {"name": "F3-16", "strategy": "anatomical", "basis": "sacral_feature", "consistency": "medium"},
    16: {"name": "F3-17", "strategy": "relative", "basis": "proportional_position", "consistency": "low"},
    17: {"name": "F3-18", "strategy": "anatomical", "basis": "coccyx_tip", "consistency": "medium"},
    18: {"name": "F3-19", "strategy": "geometric", "basis": "right_boundary", "consistency": "medium"}
}

def analyze_annotation_consistency():
    """分析不同标注策略的一致性"""
    
    print("🏥 医生标注策略分析")
    print("=" * 60)
    
    # 加载数据
    data = np.load('f3_19kp_preprocessed.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    sample_ids = data['sample_ids']
    
    # 计算每个关键点在不同样本间的变异性
    keypoint_variations = []
    
    for kp_idx in range(19):
        # 提取所有样本中这个关键点的坐标
        kp_coords = keypoints[:, kp_idx, :]  # [samples, 3]
        
        # 计算变异性指标
        centroid = np.mean(kp_coords, axis=0)
        distances_to_centroid = np.linalg.norm(kp_coords - centroid, axis=1)
        
        variation_stats = {
            'mean_distance': np.mean(distances_to_centroid),
            'std_distance': np.std(distances_to_centroid),
            'max_distance': np.max(distances_to_centroid),
            'cv': np.std(distances_to_centroid) / np.mean(distances_to_centroid) if np.mean(distances_to_centroid) > 0 else 0
        }
        
        keypoint_variations.append(variation_stats)
        
        strategy_info = F3_ANNOTATION_STRATEGIES[kp_idx]
        print(f"{strategy_info['name']:<6} ({strategy_info['strategy']:<10}): "
              f"变异 {variation_stats['mean_distance']:.2f}±{variation_stats['std_distance']:.2f}mm "
              f"(CV: {variation_stats['cv']:.3f})")
    
    return keypoint_variations

def categorize_by_strategy():
    """按标注策略分类分析"""
    
    print(f"\n📊 按标注策略分类:")
    print("=" * 60)
    
    strategies = {}
    for kp_idx, info in F3_ANNOTATION_STRATEGIES.items():
        strategy = info['strategy']
        if strategy not in strategies:
            strategies[strategy] = []
        strategies[strategy].append(kp_idx)
    
    for strategy, kp_indices in strategies.items():
        print(f"\n{strategy.upper()} 策略 ({len(kp_indices)} 个关键点):")
        
        if strategy == "geometric":
            print("   特点: 基于几何特征 (边界、极值、中心)")
            print("   优势: 客观、可重复、算法友好")
            print("   挑战: 可能忽略解剖学意义")
            
        elif strategy == "anatomical":
            print("   特点: 基于解剖学特征 (骨性标志、孔洞)")
            print("   优势: 医学意义明确、临床相关")
            print("   挑战: 主观性强、个体差异大")
            
        elif strategy == "relative":
            print("   特点: 基于相对位置 (比例、对称、间距)")
            print("   优势: 适应个体差异")
            print("   挑战: 依赖其他关键点、累积误差")
        
        for kp_idx in kp_indices:
            info = F3_ANNOTATION_STRATEGIES[kp_idx]
            print(f"     {info['name']}: {info['basis']} (一致性: {info['consistency']})")

def analyze_model_performance_by_strategy():
    """分析不同策略下的模型性能"""
    
    print(f"\n🤖 模型性能 vs 标注策略:")
    print("=" * 60)
    
    # 从之前的结果中获取性能数据 (模拟数据，实际应该从测试结果加载)
    # 这里使用我们之前分析的结果
    model_errors = {
        0: 6.2, 1: 5.8, 2: 7.1, 3: 15.3, 4: 4.9,  # F3-1 to F3-5
        5: 6.8, 6: 5.2, 7: 3.9, 8: 6.1, 9: 5.7,   # F3-6 to F3-10
        10: 7.3, 11: 5.9, 12: 23.1, 13: 8.4, 14: 6.5,  # F3-11 to F3-15
        15: 4.8, 16: 7.2, 17: 4.4, 18: 6.9         # F3-16 to F3-19
    }
    
    # 按策略分组分析
    strategy_performance = {}
    
    for kp_idx, info in F3_ANNOTATION_STRATEGIES.items():
        strategy = info['strategy']
        consistency = info['consistency']
        error = model_errors[kp_idx]
        
        if strategy not in strategy_performance:
            strategy_performance[strategy] = {'errors': [], 'consistencies': []}
        
        strategy_performance[strategy]['errors'].append(error)
        strategy_performance[strategy]['consistencies'].append(consistency)
    
    print(f"{'策略':<12} {'平均误差':<10} {'误差范围':<15} {'预期表现'}")
    print("-" * 55)
    
    for strategy, data in strategy_performance.items():
        errors = data['errors']
        mean_error = np.mean(errors)
        error_range = f"{np.min(errors):.1f}-{np.max(errors):.1f}mm"
        
        # 预测表现
        if strategy == "geometric":
            expected = "应该很好，但可能有异常"
        elif strategy == "anatomical":
            expected = "中等，依赖解剖清晰度"
        else:  # relative
            expected = "较差，依赖其他点"
        
        print(f"{strategy:<12} {mean_error:<10.1f} {error_range:<15} {expected}")

def create_strategy_visualization():
    """创建标注策略可视化"""
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 策略分布饼图
    ax1 = axes[0, 0]
    
    strategy_counts = {}
    for info in F3_ANNOTATION_STRATEGIES.values():
        strategy = info['strategy']
        strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
    
    colors = ['lightblue', 'lightgreen', 'lightcoral']
    wedges, texts, autotexts = ax1.pie(strategy_counts.values(), labels=strategy_counts.keys(), 
                                      autopct='%1.1f%%', colors=colors)
    ax1.set_title('Annotation Strategy Distribution')
    
    # 2. 一致性 vs 策略
    ax2 = axes[0, 1]
    
    consistency_mapping = {'high': 3, 'medium': 2, 'low': 1}
    
    strategies = []
    consistencies = []
    for info in F3_ANNOTATION_STRATEGIES.values():
        strategies.append(info['strategy'])
        consistencies.append(consistency_mapping[info['consistency']])
    
    strategy_types = ['geometric', 'anatomical', 'relative']
    consistency_by_strategy = {s: [] for s in strategy_types}
    
    for strategy, consistency in zip(strategies, consistencies):
        consistency_by_strategy[strategy].append(consistency)
    
    bp = ax2.boxplot([consistency_by_strategy[s] for s in strategy_types], 
                     labels=strategy_types, patch_artist=True)
    
    for patch, color in zip(bp['boxes'], colors):
        patch.set_facecolor(color)
    
    ax2.set_ylabel('Consistency Level')
    ax2.set_title('Consistency by Strategy')
    ax2.set_yticks([1, 2, 3])
    ax2.set_yticklabels(['Low', 'Medium', 'High'])
    ax2.grid(True, alpha=0.3)
    
    # 3. 模型性能 vs 策略
    ax3 = axes[0, 2]
    
    model_errors = {
        0: 6.2, 1: 5.8, 2: 7.1, 3: 15.3, 4: 4.9,
        5: 6.8, 6: 5.2, 7: 3.9, 8: 6.1, 9: 5.7,
        10: 7.3, 11: 5.9, 12: 23.1, 13: 8.4, 14: 6.5,
        15: 4.8, 16: 7.2, 17: 4.4, 18: 6.9
    }
    
    errors_by_strategy = {s: [] for s in strategy_types}
    
    for kp_idx, info in F3_ANNOTATION_STRATEGIES.items():
        strategy = info['strategy']
        error = model_errors[kp_idx]
        errors_by_strategy[strategy].append(error)
    
    bp = ax3.boxplot([errors_by_strategy[s] for s in strategy_types], 
                     labels=strategy_types, patch_artist=True)
    
    for patch, color in zip(bp['boxes'], colors):
        patch.set_facecolor(color)
    
    ax3.set_ylabel('Model Error (mm)')
    ax3.set_title('Model Performance by Strategy')
    ax3.grid(True, alpha=0.3)
    
    # 4. 关键点热力图
    ax4 = axes[1, 0]
    
    # 创建策略-一致性-性能矩阵
    kp_names = [F3_ANNOTATION_STRATEGIES[i]['name'] for i in range(19)]
    kp_errors = [model_errors[i] for i in range(19)]
    
    # 按误差排序
    sorted_indices = np.argsort(kp_errors)
    sorted_names = [kp_names[i] for i in sorted_indices]
    sorted_errors = [kp_errors[i] for i in sorted_indices]
    
    bars = ax4.barh(range(19), sorted_errors)
    
    # 按策略着色
    for i, (bar, kp_idx) in enumerate(zip(bars, sorted_indices)):
        strategy = F3_ANNOTATION_STRATEGIES[kp_idx]['strategy']
        if strategy == 'geometric':
            bar.set_color('lightblue')
        elif strategy == 'anatomical':
            bar.set_color('lightgreen')
        else:
            bar.set_color('lightcoral')
    
    ax4.set_yticks(range(19))
    ax4.set_yticklabels(sorted_names, fontsize=8)
    ax4.set_xlabel('Model Error (mm)')
    ax4.set_title('Keypoint Performance Ranking')
    ax4.grid(True, alpha=0.3)
    
    # 5. 策略特征分析
    ax5 = axes[1, 1]
    ax5.axis('off')
    
    analysis_text = """
Annotation Strategy Analysis:

GEOMETRIC Strategy:
• Based on: Boundaries, extremes, centers
• Characteristics: Objective, repeatable
• Examples: F3-13 (Z-max), F3-2 (anterior)
• Expected: Good performance, some outliers
• Challenge: May miss anatomical meaning

ANATOMICAL Strategy:
• Based on: Bone landmarks, foramina
• Characteristics: Medical significance
• Examples: F3-18 (coccyx), F3-3 (foramen)
• Expected: Medium performance
• Challenge: Subjective, individual variation

RELATIVE Strategy:
• Based on: Proportions, symmetry, spacing
• Characteristics: Adaptive to individuals
• Examples: F3-4 (between landmarks)
• Expected: Poor performance
• Challenge: Depends on other points

Key Insights:
• Consistency ≠ Accuracy
• Different strategies need different approaches
• Hybrid solutions work best
"""
    
    ax5.text(0.05, 0.95, analysis_text, transform=ax5.transAxes, fontsize=9,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    
    # 6. 解决方案建议
    ax6 = axes[1, 2]
    ax6.axis('off')
    
    solution_text = """
Tailored Solutions by Strategy:

GEOMETRIC Points:
✓ Use geometric constraints
✓ Post-processing with math rules
✓ Example: Z-max correction for F3-13

ANATOMICAL Points:
✓ Increase training data
✓ Anatomical priors
✓ Multi-scale features
✓ Expert knowledge integration

RELATIVE Points:
✓ Joint optimization
✓ Spatial relationship modeling
✓ Graph neural networks
✓ Consistency constraints

Mixed Approach:
1. Identify strategy for each point
2. Apply appropriate method
3. Combine predictions intelligently
4. Validate with domain experts

Success Factors:
• Understand the annotation process
• Match algorithm to annotation type
• Don't use one-size-fits-all
• Leverage human expertise
"""
    
    ax6.text(0.05, 0.95, solution_text, transform=ax6.transAxes, fontsize=9,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.suptitle('Medical Annotation Strategy Analysis\n'
                'Understanding Why Different Keypoints Need Different Approaches', 
                fontsize=16, fontweight='bold')
    plt.tight_layout(rect=[0, 0, 1, 0.93])
    
    filename = 'annotation_strategy_analysis.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 标注策略分析保存: {filename}")
    plt.close()

def propose_adaptive_solution():
    """提出自适应解决方案"""
    
    print(f"\n🎯 自适应解决方案:")
    print("=" * 60)
    
    print("基于标注策略的分层处理方法:")
    print()
    
    print("1. 几何策略关键点 (F3-13, F3-2, F3-8, F3-12, F3-14, F3-19):")
    print("   • 使用几何约束和后处理")
    print("   • 应用数学规则 (极值、边界、中心)")
    print("   • 高精度要求，低容错性")
    print()
    
    print("2. 解剖策略关键点 (F3-1, F3-3, F3-5, F3-7, F3-10, F3-16, F3-18):")
    print("   • 增加训练数据和解剖学先验")
    print("   • 使用多尺度特征和注意力机制")
    print("   • 结合医学知识库")
    print()
    
    print("3. 相对策略关键点 (F3-4, F3-6, F3-9, F3-11, F3-15, F3-17):")
    print("   • 联合优化和空间关系建模")
    print("   • 使用图神经网络")
    print("   • 一致性约束和相互验证")
    print()
    
    print("实施步骤:")
    print("1. 为每个关键点识别主要标注策略")
    print("2. 设计针对性的检测算法")
    print("3. 建立策略间的协调机制")
    print("4. 与医学专家验证和调优")

def main():
    """主函数"""
    print("🏥 医生标注策略的多样性分析")
    print("理解为什么不同关键点需要不同的方法")
    print("=" * 60)
    
    # 分析标注一致性
    variations = analyze_annotation_consistency()
    
    # 按策略分类
    categorize_by_strategy()
    
    # 分析模型性能
    analyze_model_performance_by_strategy()
    
    # 创建可视化
    create_strategy_visualization()
    
    # 提出解决方案
    propose_adaptive_solution()
    
    print(f"\n🎯 核心洞察:")
    print("1. 医生标注确实使用不同策略")
    print("2. 几何信息强的点需要几何约束")
    print("3. 解剖学点需要医学先验知识")
    print("4. 相对位置点需要联合建模")
    print("5. 一刀切的方法注定失败")
    
    print(f"\n💡 成功的关键:")
    print("   理解每个关键点的标注本质")
    print("   为不同类型设计不同算法")
    print("   建立人机协作的智能系统")

if __name__ == "__main__":
    main()
