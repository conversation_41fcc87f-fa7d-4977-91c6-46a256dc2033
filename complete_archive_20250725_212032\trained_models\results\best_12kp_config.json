{"method_name": "12关键点稳定性选择策略", "breakthrough_date": "2025-07-17 11:13:27", "performance": {"validation_error": 6.208, "improvement_vs_baseline": 27.4, "improvement_vs_conservative": 18.6, "training_time_minutes": 1.1, "epochs_to_convergence": 88, "precision_5mm": 30.0, "precision_7mm": 80.0}, "dataset_config": {"original_keypoints": 19, "selected_keypoints": 12, "reduction_percentage": 37, "selected_indices": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17], "selection_strategy": "stability_based", "dataset_file": "f3_reduced_12kp_stable.npz"}, "model_config": {"architecture": "AdaptivePointNet", "parameters": 1486692, "input_points": 4096, "output_keypoints": 12, "output_dimensions": 3}, "training_config": {"optimizer": "AdamW", "learning_rate": 0.0008, "weight_decay": 0.0001, "batch_size": 4, "max_epochs": 150, "early_stopping_patience": 20, "min_improvement": 0.005, "lr_scheduler": "ReduceLROnPlateau", "lr_factor": 0.7, "lr_patience": 12, "loss_function": "ImprovedLoss(alpha=0.8, beta=0.2)", "data_augmentation": true, "device": "cuda:1"}, "key_insights": ["减少关键点数量反而提升性能", "稳定性选择比随机选择更有效", "12个关键点是最优平衡点", "训练效率大幅提升", "避免了信息冗余和噪声干扰"], "selected_keypoints_analysis": {"keypoint_2": {"stability": 0.847, "position": [43.6, -26.7, -18.24], "region": "右侧"}, "keypoint_3": {"stability": 0.835, "position": [-43.32, -26.29, -19.32], "region": "左侧"}, "keypoint_4": {"stability": 0.592, "position": [21.84, -5.0, -3.96], "region": "右中"}, "keypoint_5": {"stability": 0.782, "position": [19.85, 17.45, 8.65], "region": "右上"}, "keypoint_6": {"stability": 0.855, "position": [18.07, 31.24, 21.98], "region": "右上"}, "keypoint_7": {"stability": 0.867, "position": [16.89, 38.01, 38.3], "region": "右顶"}, "keypoint_8": {"stability": 0.597, "position": [-21.71, -5.18, -4.11], "region": "左中"}, "keypoint_9": {"stability": 0.78, "position": [-20.01, 17.45, 8.32], "region": "左上"}, "keypoint_10": {"stability": 0.863, "position": [-18.96, 31.02, 22.02], "region": "左上"}, "keypoint_11": {"stability": 0.869, "position": [-18.19, 38.06, 38.12], "region": "左顶"}, "keypoint_16": {"stability": 0.805, "position": [-20.7, -15.81, -40.37], "region": "左下"}, "keypoint_17": {"stability": 0.809, "position": [21.54, -15.65, -40.84], "region": "右下"}}, "comparison_with_other_methods": {"random_sampling_19kp": {"error": 8.543, "improvement": "+27.4%"}, "weight_focused_19kp": {"error": 8.038, "improvement": "+22.8%"}, "knn_focused_19kp": {"error": 8.467, "improvement": "+26.7%"}, "optimized_knn_19kp": {"error": 7.474, "improvement": "+16.9%"}, "conservative_baseline_19kp": {"error": 7.631, "improvement": "+18.6%"}}}