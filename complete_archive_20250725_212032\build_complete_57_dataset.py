#!/usr/bin/env python3
"""
基于12点数据集处理方法构建完整的57点数据集
Build complete 57-point dataset based on 12-point processing method
"""

import numpy as np
import pandas as pd
import os
import glob
from pathlib import Path
from tqdm import tqdm
import json

def load_csv_annotations(csv_path, encoding='gbk'):
    """加载CSV标注文件 - 基于12点数据集的处理方法"""
    
    try:
        # 尝试GBK编码 (中文标注)
        df = pd.read_csv(csv_path, encoding=encoding)
    except:
        try:
            # 尝试UTF-8编码
            df = pd.read_csv(csv_path, encoding='utf-8')
        except:
            # 尝试latin-1编码
            df = pd.read_csv(csv_path, encoding='latin-1')
    
    # 提取坐标
    keypoints = df[['X', 'Y', 'Z']].values.astype(np.float32)
    labels = df['label'].values
    
    return keypoints, labels

def load_point_cloud_from_stl(sample_id, data_dir):
    """从STL文件生成点云 - 基于12点数据集的方法"""
    
    stl_dir = Path(data_dir) / "stl_models"
    
    try:
        import open3d as o3d
        
        combined_points = []
        regions_loaded = []
        
        # 加载F1, F2, F3三个STL文件 - 与12点数据集相同的方法
        for region in ['F_1', 'F_2', 'F_3']:
            stl_file = stl_dir / f"{sample_id}-{region}.stl"
            
            if stl_file.exists():
                try:
                    mesh = o3d.io.read_triangle_mesh(str(stl_file))
                    
                    if len(mesh.vertices) > 0:
                        # 从mesh采样点云 - 每个区域15000点
                        pcd = mesh.sample_points_uniformly(number_of_points=15000)
                        points = np.asarray(pcd.points)
                        combined_points.append(points)
                        regions_loaded.append(region)
                except Exception as e:
                    print(f"⚠️ {sample_id}-{region}: STL加载失败 - {e}")
                    continue
        
        if combined_points:
            # 合并所有区域的点云
            all_points = np.vstack(combined_points)
            
            # 调整到50000个点 - 与12点数据集相同
            if len(all_points) > 50000:
                indices = np.random.choice(len(all_points), 50000, replace=False)
                all_points = all_points[indices]
            elif len(all_points) < 30000:
                # 如果点太少，进行上采样
                indices = np.random.choice(len(all_points), 50000, replace=True)
                all_points = all_points[indices]
            
            return all_points, f"成功加载{len(regions_loaded)}个区域: {regions_loaded}"
        else:
            return None, "没有成功加载任何STL文件"
            
    except ImportError:
        return None, "Open3D未安装"
    except Exception as e:
        return None, f"点云生成失败: {e}"

def extract_12_from_57(keypoints_57):
    """从57个关键点中提取12个核心关键点 - 与12点数据集相同的映射"""
    
    # 12点到57点的映射关系 - 基于已验证的映射
    mapping_12_to_57 = {
        0: 0,   # F1-1 -> 原始索引0
        1: 1,   # F1-2 -> 原始索引1
        2: 2,   # F1-3 -> 原始索引2
        3: 12,  # F1-13 -> 原始索引12
        4: 19,  # F2-1 -> 原始索引19
        5: 20,  # F2-2 -> 原始索引20
        6: 21,  # F2-3 -> 原始索引21
        7: 31,  # F2-13 -> 原始索引31
        8: 38,  # F3-1 -> 原始索引38
        9: 52,  # F3-15 -> 原始索引52
        10: 50, # F3-13 -> 原始索引50
        11: 51, # F3-14 -> 原始索引51
    }
    
    keypoints_12 = np.zeros((12, 3))
    
    for i in range(12):
        original_idx = mapping_12_to_57[i]
        if original_idx < len(keypoints_57):
            keypoints_12[i] = keypoints_57[original_idx]
    
    return keypoints_12

def analyze_coordinate_systems(data_dir):
    """分析坐标系 - 基于12点数据集的方法"""
    
    print("🔍 分析坐标系...")
    
    annotation_dir = Path(data_dir) / "annotations"
    
    # 找到所有CSV文件
    csv_files = list(annotation_dir.glob("*.CSV"))
    
    xyz_files = [f for f in csv_files if 'XYZ' in f.name]
    lps_files = [f for f in csv_files if 'LPS' in f.name]
    
    print(f"   XYZ坐标系文件: {len(xyz_files)}")
    print(f"   LPS坐标系文件: {len(lps_files)}")
    
    # 分析一个XYZ文件
    if xyz_files:
        sample_file = xyz_files[0]
        keypoints, labels = load_csv_annotations(sample_file)
        
        print(f"\n📊 XYZ坐标系样本分析 ({sample_file.name}):")
        print(f"   关键点数量: {len(keypoints)}")
        print(f"   坐标范围:")
        print(f"     X: {np.min(keypoints[:, 0]):.2f} ~ {np.max(keypoints[:, 0]):.2f}")
        print(f"     Y: {np.min(keypoints[:, 1]):.2f} ~ {np.max(keypoints[:, 1]):.2f}")
        print(f"     Z: {np.min(keypoints[:, 2]):.2f} ~ {np.max(keypoints[:, 2]):.2f}")
        
        print(f"\n   前5个关键点:")
        for i in range(min(5, len(labels))):
            print(f"     {labels[i]}: ({keypoints[i, 0]:.2f}, {keypoints[i, 1]:.2f}, {keypoints[i, 2]:.2f})")
    
    return xyz_files, lps_files

def build_complete_57_dataset(data_dir="/home/<USER>/pjc/GCN/data/Data", max_samples=None):
    """构建完整的57点数据集 - 基于12点数据集的处理流程"""
    
    print("🎯 构建完整的57点数据集")
    print("基于已验证的12点数据集处理方法")
    print("=" * 80)
    
    data_dir = Path(data_dir)
    
    # 分析坐标系
    xyz_files, lps_files = analyze_coordinate_systems(data_dir)
    
    # 优先使用XYZ坐标系
    csv_files = xyz_files if xyz_files else lps_files
    
    if max_samples:
        csv_files = csv_files[:max_samples]
    
    print(f"\n🔄 处理 {len(csv_files)} 个样本...")
    
    all_keypoints_57 = []
    all_keypoints_12 = []
    all_point_clouds = []
    all_sample_ids = []
    processing_log = []
    
    for i, csv_file in enumerate(tqdm(csv_files, desc="处理样本")):
        sample_id = csv_file.stem.split('-')[0]
        
        print(f"\n📋 处理样本 {i+1}/{len(csv_files)}: {sample_id}")
        
        try:
            # 加载57个关键点
            keypoints_57, labels = load_csv_annotations(csv_file)
            
            # 检查关键点数量
            if len(keypoints_57) != 57:
                print(f"❌ 关键点数量异常: {len(keypoints_57)}")
                processing_log.append((sample_id, "失败", f"关键点数量: {len(keypoints_57)}"))
                continue
            
            print(f"✅ 57关键点加载成功: {keypoints_57.shape}")
            
            # 提取对应的12个关键点
            keypoints_12 = extract_12_from_57(keypoints_57)
            print(f"✅ 12关键点提取成功: {keypoints_12.shape}")
            
            # 加载点云
            point_cloud, pc_msg = load_point_cloud_from_stl(sample_id, data_dir)
            
            if point_cloud is None:
                print(f"❌ 点云加载失败: {pc_msg}")
                processing_log.append((sample_id, "失败", f"点云: {pc_msg}"))
                continue
            
            print(f"✅ 点云加载成功: {point_cloud.shape}")
            
            # 数据质量检查
            if np.any(np.isnan(keypoints_57)) or np.any(np.isnan(keypoints_12)):
                print(f"❌ 关键点包含NaN值")
                processing_log.append((sample_id, "失败", "关键点包含NaN"))
                continue
            
            if np.any(np.isnan(point_cloud)):
                print(f"❌ 点云包含NaN值")
                processing_log.append((sample_id, "失败", "点云包含NaN"))
                continue
            
            # 添加到数据集
            all_keypoints_57.append(keypoints_57)
            all_keypoints_12.append(keypoints_12)
            all_point_clouds.append(point_cloud)
            all_sample_ids.append(sample_id)
            processing_log.append((sample_id, "成功", "完整处理"))
            
            print(f"✅ 样本 {sample_id} 处理完成")
            
        except Exception as e:
            print(f"❌ 样本 {sample_id} 处理失败: {e}")
            processing_log.append((sample_id, "失败", str(e)))
            continue
    
    # 转换为numpy数组
    all_keypoints_57 = np.array(all_keypoints_57, dtype=np.float32)
    all_keypoints_12 = np.array(all_keypoints_12, dtype=np.float32)
    all_point_clouds = np.array(all_point_clouds, dtype=np.float32)
    
    print(f"\n✅ 数据集构建完成:")
    print(f"   成功样本: {len(all_sample_ids)}")
    print(f"   失败样本: {len([log for log in processing_log if log[1] == '失败'])}")
    print(f"   点云形状: {all_point_clouds.shape}")
    print(f"   57关键点形状: {all_keypoints_57.shape}")
    print(f"   12关键点形状: {all_keypoints_12.shape}")
    
    return all_point_clouds, all_keypoints_57, all_keypoints_12, all_sample_ids, processing_log

def save_complete_dataset(point_clouds, keypoints_57, keypoints_12, sample_ids, processing_log):
    """保存完整数据集"""
    
    print(f"\n💾 保存完整数据集...")
    
    # 保存主数据集
    np.savez_compressed('complete_57_dataset.npz',
                       point_clouds=point_clouds,
                       keypoints_57=keypoints_57,
                       keypoints_12=keypoints_12,
                       sample_ids=sample_ids)
    
    # 保存处理日志
    log_data = {
        'total_samples': len(sample_ids),
        'successful_samples': len([log for log in processing_log if log[1] == '成功']),
        'failed_samples': len([log for log in processing_log if log[1] == '失败']),
        'processing_log': processing_log,
        'dataset_info': {
            'point_cloud_shape': point_clouds.shape,
            'keypoints_57_shape': keypoints_57.shape,
            'keypoints_12_shape': keypoints_12.shape,
            'coordinate_system': 'XYZ',
            'point_cloud_size': point_clouds.shape[1] if len(point_clouds) > 0 else 0
        }
    }
    
    with open('complete_dataset_log.json', 'w', encoding='utf-8') as f:
        json.dump(log_data, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"✅ 完整数据集已保存:")
    print(f"   - complete_57_dataset.npz (主数据文件)")
    print(f"   - complete_dataset_log.json (处理日志)")

def compare_with_12point_dataset():
    """与12点数据集对比"""
    
    print(f"\n🔍 与12点数据集对比...")
    
    try:
        # 加载之前的12点数据
        old_data = np.load('smart_expanded_57_dataset.npz', allow_pickle=True)
        old_12kp = old_data['proven_12_keypoints']
        old_pc = old_data['proven_point_clouds']
        
        print(f"   之前12点数据:")
        print(f"     样本数: {len(old_12kp)}")
        print(f"     点云: {old_pc.shape}")
        print(f"     12关键点: {old_12kp.shape}")
        
        # 加载新的完整数据
        new_data = np.load('complete_57_dataset.npz', allow_pickle=True)
        new_pc = new_data['point_clouds']
        new_57kp = new_data['keypoints_57']
        new_12kp = new_data['keypoints_12']
        
        print(f"   新完整数据:")
        print(f"     样本数: {len(new_pc)}")
        print(f"     点云: {new_pc.shape}")
        print(f"     57关键点: {new_57kp.shape}")
        print(f"     12关键点: {new_12kp.shape}")
        
        print(f"\n📊 数据质量对比:")
        print(f"   样本数量: {len(old_12kp)} → {len(new_pc)}")
        print(f"   数据来源: 插值生成 → 真实标注")
        print(f"   关键点: 12点 → 57点")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🎯 基于12点数据集方法构建完整57点数据集")
    print("=" * 80)
    
    # 构建完整数据集
    point_clouds, keypoints_57, keypoints_12, sample_ids, processing_log = build_complete_57_dataset()
    
    if len(point_clouds) > 0:
        # 保存数据集
        save_complete_dataset(point_clouds, keypoints_57, keypoints_12, sample_ids, processing_log)
        
        # 与12点数据集对比
        compare_with_12point_dataset()
        
        print(f"\n🎉 完整57点数据集构建成功！")
        print(f"📊 最终数据集:")
        print(f"   样本数: {len(sample_ids)}")
        print(f"   点云: {point_clouds.shape}")
        print(f"   57关键点: {keypoints_57.shape}")
        print(f"   12关键点: {keypoints_12.shape}")
        
        print(f"\n💡 关键优势:")
        print(f"   ✅ 使用真实的57点标注数据")
        print(f"   ✅ 基于已验证的12点处理方法")
        print(f"   ✅ 完整的点云+关键点对应")
        print(f"   ✅ 统一的坐标系和数据格式")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 在真实57点数据上训练模型")
        print(f"   2. 对比真实数据vs插值数据的性能")
        print(f"   3. 分析性能提升的原因")
        
    else:
        print("❌ 数据集构建失败")

if __name__ == "__main__":
    main()
