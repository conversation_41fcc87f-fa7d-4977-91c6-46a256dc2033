# 技术规格文档

## 数据格式规范

### 点云数据
- **格式**: NumPy .npy
- **形状**: (N, 3) where N=50,000
- **坐标系**: 标准化到单位球
- **数据类型**: float32

### 关键点数据
- **格式**: NumPy .npy
- **形状**: (12, 3)
- **坐标系**: 与点云相同
- **数据类型**: float32

### 关键点定义
1. **F1区域** (左髂骨): 4个关键点
2. **F2区域** (右髂骨): 4个关键点  
3. **F3区域** (骶骨/尾骨): 4个关键点

## 模型架构

### 关键点相互辅助网络
- **输入**: 50,000×3 点云
- **输出**: 12×3 关键点坐标
- **特征提取**: PointNet++ backbone
- **约束模块**: 解剖学约束层
- **损失函数**: 多组件约束损失

### 训练参数
- **学习率**: 0.0002-0.00025
- **批大小**: 4-8
- **训练轮数**: 100-200
- **优化器**: Adam
- **正则化**: Dropout 0.3

## 性能基准

### 评估指标
- **主要指标**: 平均欧氏距离 (mm)
- **医疗指标**: <5mm准确率
- **一致性**: 标注变异性分析
- **对称性**: 双侧对称性评估

### 基准结果
- **男性最佳**: 4.84mm
- **女性最佳**: 5.64mm
- **标注质量**: 0.47mm表面距离
- **一致性**: 2.85-3.30mm变异

## 系统要求

### 硬件要求
- **GPU**: NVIDIA GTX 1080+ (8GB+ VRAM)
- **内存**: 16GB+ RAM
- **存储**: 10GB+ 可用空间

### 软件要求
- **Python**: 3.8+
- **PyTorch**: 1.8+
- **CUDA**: 11.0+
- **其他**: NumPy, Matplotlib, Scikit-learn

## 质量保证

### 数据质量
- **表面投影**: 99%的点在2mm内
- **标注一致性**: 优秀等级
- **对称性**: CV < 0.1
- **完整性**: 100%完整标注

### 模型质量
- **收敛性**: 稳定训练收敛
- **泛化性**: 交叉验证验证
- **鲁棒性**: 多种数据增强测试
- **可复现性**: 固定随机种子

---
**本文档提供了完整的技术规格信息**
