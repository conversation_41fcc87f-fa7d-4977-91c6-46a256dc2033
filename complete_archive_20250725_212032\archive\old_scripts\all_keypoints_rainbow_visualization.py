#!/usr/bin/env python3
"""
所有关键点彩虹靶子可视化
显示所有12个关键点的彩虹靶子效果
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps

# 关键点名称
KEYPOINT_NAMES = {
    0: "L-ASIS", 1: "R-ASIS", 2: "L-PSIS", 3: "R-PSIS",
    4: "L-IC", 5: "R-IC", 6: "SP", 7: "L-SIJ", 8: "R-SIJ",
    9: "L-IS", 10: "R-IS", 11: "CT"
}

def create_all_keypoints_rainbow_colormap():
    """创建所有关键点的彩虹色配色方案"""
    colors = [
        '#F5F5F5',  # 白烟色 (背景)
        '#E6E6FA',  # 薰衣草色
        '#9370DB',  # 中紫色
        '#4169E1',  # 皇家蓝
        '#00BFFF',  # 深天蓝
        '#00FFFF',  # 青色
        '#00FF7F',  # 春绿色
        '#ADFF2F',  # 绿黄色
        '#FFFF00',  # 黄色
        '#FFD700',  # 金色
        '#FFA500',  # 橙色
        '#FF4500',  # 橙红色
        '#FF0000',  # 红色
        '#DC143C'   # 深红色
    ]
    return LinearSegmentedColormap.from_list('all_rainbow', colors, N=256)

def create_combined_heatmap(point_cloud, pred_keypoints, sigma=10.0):
    """创建所有关键点的组合热力图"""
    
    combined_heatmap = np.zeros(len(point_cloud))
    
    # 为每个关键点计算热力图并叠加
    for kp_idx, pred_keypoint in enumerate(pred_keypoints):
        # 计算到当前关键点的距离
        distances = np.linalg.norm(point_cloud - pred_keypoint, axis=1)
        
        # 使用高斯分布
        heatmap = np.exp(-distances**2 / (2 * sigma**2))
        
        # 叠加到组合热力图
        combined_heatmap = np.maximum(combined_heatmap, heatmap)
    
    # 增强对比度
    combined_heatmap = np.power(combined_heatmap, 0.7)
    
    # 归一化
    if np.max(combined_heatmap) > 0:
        combined_heatmap = combined_heatmap / np.max(combined_heatmap)
    
    return combined_heatmap

def create_all_keypoints_visualization(point_cloud, true_keypoints, pred_keypoints, 
                                     confidences, sample_id):
    """创建所有关键点的彩虹靶子可视化"""
    
    print(f"🌈 Creating ALL keypoints rainbow visualization")
    
    rainbow_cmap = create_all_keypoints_rainbow_colormap()
    
    fig = plt.figure(figsize=(24, 8))
    
    # 三种聚焦程度
    sigma_values = [15.0, 10.0, 7.0]
    titles = ["Wide All-Target Rainbow", "Balanced All-Target Rainbow", "Focused All-Target Rainbow"]
    
    for i, (sigma, title) in enumerate(zip(sigma_values, titles)):
        ax = fig.add_subplot(1, 3, i+1, projection='3d')
        
        # 创建所有关键点的组合热力图
        combined_heatmap = create_combined_heatmap(point_cloud, pred_keypoints, sigma)
        
        # 显示更多点云
        if len(point_cloud) > 8000:
            sample_indices = np.random.choice(len(point_cloud), 8000, replace=False)
            display_pc = point_cloud[sample_indices]
            display_heatmap = combined_heatmap[sample_indices]
        else:
            display_pc = point_cloud
            display_heatmap = combined_heatmap
        
        # 显示所有点 - 使用彩虹色渐变
        
        # 1. 背景点 - 低置信度
        bg_mask = display_heatmap < 0.1
        if np.any(bg_mask):
            ax.scatter(display_pc[bg_mask, 0],
                      display_pc[bg_mask, 1],
                      display_pc[bg_mask, 2],
                      c=display_heatmap[bg_mask],
                      cmap=rainbow_cmap, s=0.5, alpha=0.3, vmin=0, vmax=1)
        
        # 2. 外环 - 紫蓝色
        outer_mask = (display_heatmap >= 0.1) & (display_heatmap < 0.3)
        if np.any(outer_mask):
            ax.scatter(display_pc[outer_mask, 0],
                      display_pc[outer_mask, 1],
                      display_pc[outer_mask, 2],
                      c=display_heatmap[outer_mask],
                      cmap=rainbow_cmap, s=1.5, alpha=0.6, vmin=0, vmax=1)
        
        # 3. 中环 - 青绿色
        middle_mask = (display_heatmap >= 0.3) & (display_heatmap < 0.6)
        if np.any(middle_mask):
            ax.scatter(display_pc[middle_mask, 0],
                      display_pc[middle_mask, 1],
                      display_pc[middle_mask, 2],
                      c=display_heatmap[middle_mask],
                      cmap=rainbow_cmap, s=3, alpha=0.8, vmin=0, vmax=1)
        
        # 4. 内环 - 黄橙色
        inner_mask = display_heatmap >= 0.6
        if np.any(inner_mask):
            scatter = ax.scatter(display_pc[inner_mask, 0],
                               display_pc[inner_mask, 1],
                               display_pc[inner_mask, 2],
                               c=display_heatmap[inner_mask],
                               cmap=rainbow_cmap, s=6, alpha=0.9, vmin=0, vmax=1)
        
        # 5. 显示所有关键点
        for kp_idx in range(len(true_keypoints)):
            true_kp = true_keypoints[kp_idx]
            pred_kp = pred_keypoints[kp_idx]
            
            # 真实关键点 - 黑色星形，不同大小
            size = 300 + kp_idx * 20  # 递增大小便于区分
            ax.scatter(true_kp[0], true_kp[1], true_kp[2],
                      c='black', s=size, marker='*', 
                      edgecolor='white', linewidth=2, 
                      alpha=0.9, zorder=10)
            
            # 预测关键点 - 彩色十字，每个点不同颜色
            colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown',
                     'pink', 'gray', 'olive', 'cyan', 'magenta', 'yellow']
            color = colors[kp_idx % len(colors)]
            
            ax.scatter(pred_kp[0], pred_kp[1], pred_kp[2],
                      c=color, s=200 + kp_idx * 15, marker='x', 
                      linewidth=4, alpha=0.9, zorder=10)
            
            # 连接线
            ax.plot([true_kp[0], pred_kp[0]], 
                    [true_kp[1], pred_kp[1]], 
                    [true_kp[2], pred_kp[2]], 
                    color=color, alpha=0.7, linewidth=2)
            
            # 添加关键点标签
            if i == 0:  # 只在第一个子图添加标签
                ax.text(true_kp[0], true_kp[1], true_kp[2] + 3, 
                       f'{kp_idx}:{KEYPOINT_NAMES[kp_idx]}', 
                       fontsize=8, ha='center')
        
        # 计算总体误差
        errors = [np.linalg.norm(pred_keypoints[i] - true_keypoints[i]) 
                 for i in range(len(true_keypoints))]
        avg_error = np.mean(errors)
        
        # 设置坐标轴范围
        pc_min = np.min(point_cloud, axis=0)
        pc_max = np.max(point_cloud, axis=0)
        margin = 15
        
        ax.set_xlim([pc_min[0] - margin, pc_max[0] + margin])
        ax.set_ylim([pc_min[1] - margin, pc_max[1] + margin])
        ax.set_zlim([pc_min[2] - margin, pc_max[2] + margin])
        
        # 设置标题
        ax.set_title(f'{title}\nAll 12 Keypoints\n'
                    f'σ: {sigma:.1f}mm | Avg Error: {avg_error:.1f}mm',
                    fontsize=14, fontweight='bold', pad=20)
        
        # 坐标轴标签
        ax.set_xlabel('X (mm)', fontsize=12)
        ax.set_ylabel('Y (mm)', fontsize=12)
        ax.set_zlabel('Z (mm)', fontsize=12)
        ax.tick_params(labelsize=10)
        
        # 设置视角
        ax.view_init(elev=25, azim=45)
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息
        high_conf_points = np.sum(display_heatmap > 0.6)
        total_points = len(display_heatmap)
        
        stats_text = f'High Conf: {high_conf_points}\nTotal: {total_points}\nKeypoints: 12'
        ax.text2D(0.02, 0.98, stats_text, transform=ax.transAxes, 
                 fontsize=10, verticalalignment='top',
                 bbox=dict(boxstyle='round', facecolor='white', alpha=0.9))
    
    # 添加颜色条
    if 'scatter' in locals():
        cbar = plt.colorbar(scatter, ax=fig.get_axes(), shrink=0.8, aspect=30)
        cbar.set_label('Combined Rainbow Confidence (All 12 Keypoints)', fontsize=12)
    
    plt.suptitle(f'🌈 ALL Keypoints Rainbow Visualization 🌈\n'
                f'Sample {sample_id} - Complete Point Cloud with All 12 Targets', 
                fontsize=16, fontweight='bold')
    
    plt.tight_layout(rect=[0, 0, 0.95, 0.9])
    
    # 保存
    filename = f'all_keypoints_rainbow_{sample_id}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"   🌈 ALL keypoints rainbow visualization saved: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🌈 ALL Keypoints Rainbow Visualization")
    print("Display ALL 12 keypoints with rainbow target effects")
    print("=" * 80)
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model.eval()
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # 选择一个样本进行展示
    sample_idx = 0
    sample_id = sample_ids[sample_idx]
    point_cloud = point_clouds[sample_idx]
    true_keypoints = keypoints[sample_idx]
    
    print(f"\n🌈 Processing sample: {sample_id}")
    print(f"📊 Point cloud size: {len(point_cloud)}")
    print(f"🎯 Number of keypoints: {len(true_keypoints)}")
    
    # 采样点云用于预测
    if len(point_cloud) > 8192:
        indices = np.random.choice(len(point_cloud), 8192, replace=False)
        pc_sampled = point_cloud[indices]
    else:
        pc_sampled = point_cloud
    
    # 预测关键点
    pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
    
    with torch.no_grad():
        pred_heatmaps = model(pc_tensor)
    
    pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T
    pred_keypoints, confidences = extract_keypoints_from_heatmaps(
        pred_heatmaps_np.T, pc_sampled
    )
    
    print(f"🎯 Predicted {len(pred_keypoints)} keypoints")
    
    # 创建所有关键点的彩虹可视化
    create_all_keypoints_visualization(
        point_cloud,  # 使用完整点云
        true_keypoints, 
        pred_keypoints,
        confidences, 
        sample_id
    )
    
    print(f"\n🎉 ALL Keypoints Rainbow Visualization Complete!")
    print("✅ ALL 12 keypoints displayed")
    print("✅ Combined rainbow target effects")
    print("✅ 8000+ points with rainbow colors")
    print("✅ Individual keypoint identification")
    print("✅ Medical-grade comprehensive view")

if __name__ == "__main__":
    main()
