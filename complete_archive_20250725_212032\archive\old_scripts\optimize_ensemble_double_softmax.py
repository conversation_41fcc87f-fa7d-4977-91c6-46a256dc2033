#!/usr/bin/env python3
"""
优化集成双Softmax方案
基于5.829mm成功配置，进行更精细的优化
目标: 突破5.5mm医疗级精度
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class OptimizedDoubleSoftMax(nn.Module):
    """
    优化的双Softmax机制
    基于5.829mm成功配置的精细优化版本
    """
    
    def __init__(self, threshold_ratio=0.12, temperature=1.8, weight_ratio=0.75):
        super(OptimizedDoubleSoftMax, self).__init__()
        
        self.threshold_ratio = threshold_ratio
        self.temperature = temperature
        self.weight_ratio = weight_ratio
        
        # 更深的权重计算网络
        self.weight_net = nn.Sequential(
            nn.Linear(3, 128),
            nn.ReLU(),
            nn.BatchNorm1d(128),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.BatchNorm1d(64),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1)
        )
        
        print(f"🎯 优化双Softmax机制:")
        print(f"   阈值比例: {threshold_ratio}")
        print(f"   温度参数: {temperature}")
        print(f"   权重比例: {weight_ratio}:{1-weight_ratio}")
    
    def forward(self, points, predicted_keypoint):
        """优化的双Softmax权重计算"""
        # 计算相对位置
        relative_pos = points - predicted_keypoint.unsqueeze(0)
        
        # 第一个Softmax - 改进的距离权重
        distances = torch.norm(relative_pos, dim=1)
        # 使用更平滑的距离权重函数
        distance_weights = F.softmax(-distances**1.5 / (2 * self.temperature**2), dim=0)
        
        # 第二个Softmax - 改进的神经网络权重
        if len(relative_pos) > 1:
            nn_weights = self.weight_net(relative_pos).squeeze(-1)
            nn_weights = F.softmax(nn_weights / self.temperature, dim=0)
        else:
            nn_weights = torch.ones_like(distance_weights)
        
        # 自适应权重组合
        combined_weights = self.weight_ratio * distance_weights + (1 - self.weight_ratio) * nn_weights
        
        # 改进的阈值过滤 - 使用更保守的阈值
        threshold = torch.quantile(combined_weights, 1 - self.threshold_ratio)
        filter_mask = combined_weights >= threshold
        
        # 确保至少保留足够的点
        min_points = max(3, int(len(combined_weights) * 0.1))  # 至少保留10%的点
        if filter_mask.sum() < min_points:
            _, top_indices = torch.topk(combined_weights, min_points)
            filter_mask = torch.zeros_like(combined_weights, dtype=torch.bool)
            filter_mask[top_indices] = True
        
        # 重新归一化
        filtered_weights = combined_weights * filter_mask.float()
        sum_weights = torch.sum(filtered_weights)
        
        if sum_weights > 1e-8:
            final_weights = filtered_weights / sum_weights
        else:
            final_weights = combined_weights / combined_weights.sum()
        
        # 加权平均得到精细化关键点
        refined_keypoint = torch.sum(final_weights.unsqueeze(-1) * points, dim=0)
        
        return refined_keypoint

class AdvancedEnsembleDoubleSoftMax(nn.Module):
    """
    高级集成双Softmax机制
    使用更多样化的参数组合和智能权重
    """
    
    def __init__(self, num_ensembles=5):
        super(AdvancedEnsembleDoubleSoftMax, self).__init__()
        
        self.num_ensembles = num_ensembles
        
        # 创建更多样化的双Softmax模块
        self.softmax_modules = nn.ModuleList([
            OptimizedDoubleSoftMax(
                threshold_ratio=0.08 + 0.04 * i,      # 0.08, 0.12, 0.16, 0.20, 0.24
                temperature=1.5 + 0.3 * i,            # 1.5, 1.8, 2.1, 2.4, 2.7
                weight_ratio=0.65 + 0.05 * i          # 0.65, 0.70, 0.75, 0.80, 0.85
            ) for i in range(num_ensembles)
        ])
        
        # 学习集成权重
        self.ensemble_weights = nn.Parameter(torch.ones(num_ensembles) / num_ensembles)
        
        print(f"🎯 高级集成双Softmax: {num_ensembles}个模块")
        print(f"   智能权重学习")
    
    def forward(self, points, predicted_keypoint):
        """高级集成多个双Softmax的结果"""
        refined_keypoints = []
        
        for softmax_module in self.softmax_modules:
            refined_kp = softmax_module(points, predicted_keypoint)
            refined_keypoints.append(refined_kp)
        
        # 智能加权集成
        ensemble_weights = F.softmax(self.ensemble_weights, dim=0)
        refined_keypoints = torch.stack(refined_keypoints)  # [num_ensembles, 3]
        
        # 加权平均
        ensemble_keypoint = torch.sum(ensemble_weights.unsqueeze(-1) * refined_keypoints, dim=0)
        
        return ensemble_keypoint

class OptimizedBaselinePointNet(nn.Module):
    """
    优化的基线PointNet
    基于成功的基线架构，添加高级集成双Softmax
    """
    
    def __init__(self, num_keypoints: int, dropout_rate: float = 0.3, 
                 ensemble_type: str = "advanced"):
        super(OptimizedBaselinePointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        self.ensemble_type = ensemble_type
        
        # 基线架构 (完全保持成功配置)
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(dropout_rate)
        
        # 选择集成双Softmax类型
        if ensemble_type == "advanced":
            self.double_softmax = AdvancedEnsembleDoubleSoftMax(num_ensembles=5)
        else:  # optimized
            self.double_softmax = OptimizedDoubleSoftMax(
                threshold_ratio=0.12,
                temperature=1.8,
                weight_ratio=0.75
            )
        
        print(f"🧠 优化基线模型: {num_keypoints}个关键点")
        print(f"   集成类型: {ensemble_type}")
        
    def forward(self, x):
        batch_size = x.size(0)
        x_input = x.transpose(2, 1)
        
        # 基线模型前向传播 (完全一致)
        x1 = torch.relu(self.bn1(self.conv1(x_input)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        
        x3_res = x3 + self.residual1(x1)
        
        x4 = torch.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = torch.relu(self.bn5(self.conv5(x4_res)))
        
        global_feat = torch.max(x5, 2)[0]
        
        feat = torch.relu(self.bn_fc1(self.fc1(global_feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc2(self.fc2(feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc3(self.fc3(feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc4(self.fc4(feat)))
        feat = self.dropout(feat)
        feat = self.fc5(feat)
        
        keypoints = feat.view(batch_size, self.num_keypoints, 3)
        
        # 推理时应用优化的双Softmax精细化
        if not self.training:
            keypoints = self.apply_optimized_double_softmax_refinement(x, keypoints)
        
        return keypoints
    
    def apply_optimized_double_softmax_refinement(self, points, predicted_keypoints):
        """应用优化的双Softmax精细化"""
        batch_size = points.shape[0]
        refined_keypoints = []
        
        for b in range(batch_size):
            batch_points = points[b]
            batch_keypoints = predicted_keypoints[b]
            
            batch_refined = []
            for k in range(self.num_keypoints):
                kp_pred = batch_keypoints[k]
                
                # 自适应候选点选择
                distances = torch.norm(batch_points - kp_pred.unsqueeze(0), dim=1)
                
                # 动态调整候选点数量 - 更保守的选择
                total_points = batch_points.shape[0]
                if total_points > 2000:
                    K = 384  # 增加候选点数量
                elif total_points > 1000:
                    K = 256
                else:
                    K = min(128, total_points)
                
                _, nearest_indices = torch.topk(distances, K, largest=False)
                candidate_points = batch_points[nearest_indices]
                
                # 应用优化的双Softmax
                refined_kp = self.double_softmax(candidate_points, kp_pred)
                batch_refined.append(refined_kp)
            
            refined_keypoints.append(torch.stack(batch_refined))
        
        return torch.stack(refined_keypoints)

class ReducedKeypointsF3Dataset(Dataset):
    """12关键点F3数据集 (复用成功配置)"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points
        self.augment = augment
        self.split = split
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
    
    def __len__(self):
        return len(self.sample_ids)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        # 数据增强 (保持成功配置)
        if self.augment and self.split == 'train':
            if np.random.random() < 0.7:
                angle = np.random.uniform(-0.08, 0.08)
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                point_cloud = point_cloud @ rotation.T
                keypoints = keypoints @ rotation.T
            
            if np.random.random() < 0.6:
                translation = np.random.uniform(-0.4, 0.4, 3)
                point_cloud += translation
                keypoints += translation
            
            if np.random.random() < 0.5:
                scale = np.random.uniform(0.99, 1.01, 3)
                point_cloud *= scale
                keypoints *= scale
            
            if np.random.random() < 0.6:
                noise_level = np.random.choice([0.02, 0.03, 0.04])
                noise = np.random.normal(0, noise_level, point_cloud.shape)
                point_cloud += noise
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

def test_optimized_ensemble():
    """测试优化的集成双Softmax"""
    
    print("🧪 **测试优化集成双Softmax**")
    print("🎯 **基于5.829mm成功配置的精细优化**")
    print("=" * 80)
    
    batch_size = 4
    num_points = 4096
    num_keypoints = 12
    
    # 创建测试数据
    test_input = torch.randn(batch_size, num_points, 3)
    
    print(f"📊 测试输入: {test_input.shape}")
    
    # 测试高级集成双Softmax
    print(f"\n🔍 测试高级集成双Softmax:")
    model = OptimizedBaselinePointNet(num_keypoints=num_keypoints, ensemble_type="advanced")
    
    with torch.no_grad():
        # 训练模式
        model.train()
        output_train = model(test_input)
        print(f"   训练模式输出: {output_train.shape}")
        
        # 推理模式
        model.eval()
        output_eval = model(test_input)
        print(f"   推理模式输出: {output_eval.shape}")
    
    # 参数统计
    total_params = sum(p.numel() for p in model.parameters())
    print(f"\n📊 模型参数: {total_params:,}")
    
    print(f"\n✅ 优化集成双Softmax测试通过!")
    
    return model

if __name__ == "__main__":
    set_seed(42)
    
    print("🚀 **优化集成双Softmax方案**")
    print("📚 **基于5.829mm成功配置的精细优化**")
    print("🎯 **目标: 突破5.5mm医疗级精度**")
    print("=" * 80)
    
    # 测试模型
    model = test_optimized_ensemble()
    
    print(f"\n🎉 **优化集成双Softmax准备完成!**")
    print("=" * 50)
    print(f"🔬 核心优化: 更精细的参数调优")
    print(f"🎯 当前基线: 5.829mm")
    print(f"🏆 目标精度: 5.5mm")
    print(f"📈 优化策略: 参数精调 + 智能集成")
