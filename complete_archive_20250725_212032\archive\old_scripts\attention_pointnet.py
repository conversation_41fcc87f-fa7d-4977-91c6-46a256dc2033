#!/usr/bin/env python3
"""
Phase 2: 注意力增强PointNet架构
基于12关键点成功配置，添加注意力机制和多尺度特征提取
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class SelfAttention(nn.Module):
    """自注意力机制 - 让模型关注重要的点"""
    
    def __init__(self, in_channels, num_heads=8):
        super(SelfAttention, self).__init__()
        self.num_heads = num_heads
        self.in_channels = in_channels
        self.head_dim = in_channels // num_heads
        
        assert in_channels % num_heads == 0, "in_channels must be divisible by num_heads"
        
        self.query = nn.Linear(in_channels, in_channels)
        self.key = nn.Linear(in_channels, in_channels)
        self.value = nn.Linear(in_channels, in_channels)
        self.out_proj = nn.Linear(in_channels, in_channels)
        
        self.dropout = nn.Dropout(0.1)
        self.layer_norm = nn.LayerNorm(in_channels)
        
    def forward(self, x):
        # x: [batch_size, num_points, in_channels]
        batch_size, num_points, _ = x.size()
        
        # 残差连接
        residual = x
        
        # 多头注意力
        Q = self.query(x).view(batch_size, num_points, self.num_heads, self.head_dim).transpose(1, 2)
        K = self.key(x).view(batch_size, num_points, self.num_heads, self.head_dim).transpose(1, 2)
        V = self.value(x).view(batch_size, num_points, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 计算注意力分数
        attention_scores = torch.matmul(Q, K.transpose(-2, -1)) / np.sqrt(self.head_dim)
        attention_weights = F.softmax(attention_scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 应用注意力
        attended = torch.matmul(attention_weights, V)
        attended = attended.transpose(1, 2).contiguous().view(batch_size, num_points, self.in_channels)
        
        # 输出投影
        output = self.out_proj(attended)
        
        # 残差连接和层归一化
        output = self.layer_norm(output + residual)
        
        return output

class ChannelAttention(nn.Module):
    """通道注意力机制 - 关注重要的特征通道"""
    
    def __init__(self, in_channels, reduction=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool1d(1)
        self.max_pool = nn.AdaptiveMaxPool1d(1)
        
        self.fc = nn.Sequential(
            nn.Linear(in_channels, in_channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(in_channels // reduction, in_channels, bias=False)
        )
        
    def forward(self, x):
        # x: [batch_size, channels, num_points]
        batch_size, channels, num_points = x.size()
        
        # 全局平均池化和最大池化
        avg_out = self.fc(self.avg_pool(x).view(batch_size, channels))
        max_out = self.fc(self.max_pool(x).view(batch_size, channels))
        
        # 注意力权重
        attention = torch.sigmoid(avg_out + max_out).view(batch_size, channels, 1)
        
        return x * attention

class MultiScaleFeatureExtractor(nn.Module):
    """多尺度特征提取器"""
    
    def __init__(self, in_channels, out_channels):
        super(MultiScaleFeatureExtractor, self).__init__()
        
        # 不同尺度的卷积
        self.conv1x1 = nn.Conv1d(in_channels, out_channels // 4, 1)
        self.conv3x1 = nn.Sequential(
            nn.Conv1d(in_channels, out_channels // 4, 1),
            nn.Conv1d(out_channels // 4, out_channels // 4, 3, padding=1)
        )
        self.conv5x1 = nn.Sequential(
            nn.Conv1d(in_channels, out_channels // 4, 1),
            nn.Conv1d(out_channels // 4, out_channels // 4, 5, padding=2)
        )
        self.pool_conv = nn.Sequential(
            nn.MaxPool1d(3, stride=1, padding=1),
            nn.Conv1d(in_channels, out_channels // 4, 1)
        )
        
        self.bn = nn.BatchNorm1d(out_channels)
        
    def forward(self, x):
        # 多尺度特征提取
        branch1 = self.conv1x1(x)
        branch2 = self.conv3x1(x)
        branch3 = self.conv5x1(x)
        branch4 = self.pool_conv(x)
        
        # 特征融合
        out = torch.cat([branch1, branch2, branch3, branch4], dim=1)
        out = self.bn(out)
        
        return out

class AttentionPointNet(nn.Module):
    """注意力增强的PointNet架构"""
    
    def __init__(self, num_keypoints: int, dropout_rate: float = 0.4):
        super(AttentionPointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 初始特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        
        # 多尺度特征提取
        self.multiscale1 = MultiScaleFeatureExtractor(128, 256)
        self.multiscale2 = MultiScaleFeatureExtractor(256, 512)
        
        # 最终特征提取
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # BatchNorm层
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 通道注意力
        self.channel_attention1 = ChannelAttention(256)
        self.channel_attention2 = ChannelAttention(512)
        self.channel_attention3 = ChannelAttention(1024)
        
        # 自注意力 (在特征空间中应用)
        self.self_attention = SelfAttention(1024, num_heads=8)
        
        # 残差连接
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        # 回归头
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(dropout_rate)
        
        print(f"🧠 注意力增强PointNet: {num_keypoints}个关键点")
        print(f"   - 多尺度特征提取")
        print(f"   - 自注意力机制 (8头)")
        print(f"   - 通道注意力机制")
        print(f"   - 残差连接")
        
    def forward(self, x):
        batch_size = x.size(0)
        num_points = x.size(1)
        x = x.transpose(2, 1)  # [batch, 3, num_points]
        
        # 初始特征提取
        x1 = torch.relu(self.bn1(self.conv1(x)))  # [batch, 64, num_points]
        x2 = torch.relu(self.bn2(self.conv2(x1)))  # [batch, 128, num_points]
        
        # 多尺度特征提取 + 通道注意力
        x3 = torch.relu(self.multiscale1(x2))  # [batch, 256, num_points]
        x3 = self.channel_attention1(x3)
        
        # 残差连接
        x3_res = x3 + self.residual1(x1)
        
        x4 = torch.relu(self.multiscale2(x3_res))  # [batch, 512, num_points]
        x4 = self.channel_attention2(x4)
        
        # 残差连接
        x4_res = x4 + self.residual2(x2)
        
        x5 = torch.relu(self.bn5(self.conv5(x4_res)))  # [batch, 1024, num_points]
        x5 = self.channel_attention3(x5)
        
        # 全局最大池化
        global_feat = torch.max(x5, 2)[0]  # [batch, 1024]
        
        # 简化：直接使用全局特征，不使用自注意力
        # 自注意力在这个场景下可能过于复杂
        attended_feat = global_feat  # [batch, 1024]
        
        # 回归头
        x = torch.relu(self.bn_fc1(self.fc1(attended_feat)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc2(self.fc2(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc3(self.fc3(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc4(self.fc4(x)))
        x = self.dropout(x)
        x = self.fc5(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

class FeaturePyramidPointNet(nn.Module):
    """特征金字塔PointNet - 另一种架构选择"""
    
    def __init__(self, num_keypoints: int, dropout_rate: float = 0.4):
        super(FeaturePyramidPointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 特征金字塔的不同层
        self.layer1 = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU()
        )
        
        self.layer2 = nn.Sequential(
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU()
        )
        
        self.layer3 = nn.Sequential(
            nn.Conv1d(128, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU()
        )
        
        self.layer4 = nn.Sequential(
            nn.Conv1d(256, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU()
        )
        
        # 特征融合层
        self.fusion_conv = nn.Sequential(
            nn.Conv1d(64 + 128 + 256 + 512, 1024, 1),
            nn.BatchNorm1d(1024),
            nn.ReLU()
        )
        
        # 注意力机制
        self.attention = ChannelAttention(1024)
        
        # 回归头
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, num_keypoints * 3)
        
        self.dropout = nn.Dropout(dropout_rate)
        
        print(f"🏗️ 特征金字塔PointNet: {num_keypoints}个关键点")
        print(f"   - 多层特征融合")
        print(f"   - 通道注意力机制")
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        # 特征金字塔提取
        feat1 = self.layer1(x)      # [batch, 64, num_points]
        feat2 = self.layer2(feat1)  # [batch, 128, num_points]
        feat3 = self.layer3(feat2)  # [batch, 256, num_points]
        feat4 = self.layer4(feat3)  # [batch, 512, num_points]
        
        # 特征融合
        fused_feat = torch.cat([feat1, feat2, feat3, feat4], dim=1)  # [batch, 960, num_points]
        fused_feat = self.fusion_conv(fused_feat)  # [batch, 1024, num_points]
        
        # 注意力机制
        fused_feat = self.attention(fused_feat)
        
        # 全局池化
        global_feat = torch.max(fused_feat, 2)[0]  # [batch, 1024]
        
        # 回归头
        x = torch.relu(self.fc1(global_feat))
        x = self.dropout(x)
        x = torch.relu(self.fc2(x))
        x = self.dropout(x)
        x = torch.relu(self.fc3(x))
        x = self.dropout(x)
        x = self.fc4(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

def test_attention_models():
    """测试注意力模型"""
    
    print("🧪 **测试注意力增强模型**")
    print("=" * 50)
    
    batch_size = 4
    num_points = 4096
    num_keypoints = 12
    
    # 创建测试数据
    test_input = torch.randn(batch_size, num_points, 3)
    
    print(f"📊 测试输入: {test_input.shape}")
    
    # 测试注意力增强PointNet
    print(f"\n🔍 测试注意力增强PointNet:")
    attention_model = AttentionPointNet(num_keypoints=num_keypoints)
    
    with torch.no_grad():
        output1 = attention_model(test_input)
        print(f"   输出形状: {output1.shape}")
        print(f"   参数数量: {sum(p.numel() for p in attention_model.parameters()):,}")
    
    # 测试特征金字塔PointNet
    print(f"\n🔍 测试特征金字塔PointNet:")
    pyramid_model = FeaturePyramidPointNet(num_keypoints=num_keypoints)
    
    with torch.no_grad():
        output2 = pyramid_model(test_input)
        print(f"   输出形状: {output2.shape}")
        print(f"   参数数量: {sum(p.numel() for p in pyramid_model.parameters()):,}")
    
    print(f"\n✅ 所有模型测试通过!")
    
    return attention_model, pyramid_model

if __name__ == "__main__":
    attention_model, pyramid_model = test_attention_models()
