{"method": "MultiModal Feature Fusion", "baseline_error": 8.509, "best_val_error": 0.0, "improvement": 100.0, "training_time_minutes": 0.6016480803489686, "epochs_trained": 21, "history": [{"epoch": 1, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 2, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 3, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 4, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 5, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 6, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 7, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 8, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 9, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 10, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 11, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 12, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 13, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 14, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00056}, {"epoch": 15, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00056}, {"epoch": 16, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00056}, {"epoch": 17, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00056}, {"epoch": 18, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00056}, {"epoch": 19, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00056}, {"epoch": 20, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00056}, {"epoch": 21, "train_loss": 0.0, "val_loss": 0.0, "train_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 0.0, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.00056}], "multimodal_config": {"geometric_features": "distance + angle", "density_features": "multi_scale_density + density_change", "curvature_features": "principal + gaussian", "pointnet_features": "baseline_features", "feature_fusion": "960_dim -> 256_dim", "weighted_loss": "difficult_keypoints_1.5x"}}