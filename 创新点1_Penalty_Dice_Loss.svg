<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="1280" height="720" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="url(#headerGrad)"/>
  <text x="640" y="50" text-anchor="middle" fill="white" 
        font-family="SimHei, Arial, sans-serif" font-size="36" font-weight="bold">
    创新点一：Penalty Dice Loss 惩罚骰子损失
  </text>
  
  <!-- Problem illustration -->
  <rect x="50" y="100" width="580" height="280" rx="15" fill="white" stroke="#ef4444" stroke-width="3"/>
  <text x="340" y="130" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="22" font-weight="bold">
    问题：类别不平衡挑战
  </text>
  
  <!-- Point cloud visualization with imbalance -->
  <circle cx="200" cy="200" r="80" fill="#f3f4f6" stroke="#9ca3af" stroke-width="2"/>
  <text x="200" y="290" text-anchor="middle" fill="#6b7280" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    背景点（多数类）
  </text>
  
  <!-- Small keypoint regions -->
  <circle cx="180" cy="180" r="8" fill="#fca5a5" stroke="#ef4444" stroke-width="2"/>
  <circle cx="220" cy="190" r="8" fill="#fca5a5" stroke="#ef4444" stroke-width="2"/>
  <circle cx="210" cy="220" r="8" fill="#fca5a5" stroke="#ef4444" stroke-width="2"/>
  
  <text x="340" y="290" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    关键点区域（少数类）
  </text>
  
  <!-- Statistics -->
  <rect x="70" y="310" width="540" height="60" rx="8" fill="#fef2f2" stroke="#fca5a5" stroke-width="1"/>
  <text x="340" y="335" text-anchor="middle" fill="#7f1d1d" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    典型不平衡比例
  </text>
  <text x="80" y="355" fill="#7f1d1d" font-family="SimHei, Arial, sans-serif" font-size="14">
    背景点：约99.5%的点 | 关键点区域：约0.5%的点
  </text>
  
  <!-- Solution -->
  <rect x="650" y="100" width="580" height="280" rx="15" fill="white" stroke="#10b981" stroke-width="3"/>
  <text x="940" y="130" text-anchor="middle" fill="#059669" 
        font-family="SimHei, Arial, sans-serif" font-size="22" font-weight="bold">
    解决方案：Penalty Dice Loss
  </text>
  
  <!-- Traditional vs Penalty Dice Loss -->
  <rect x="670" y="160" width="260" height="100" rx="8" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
  <text x="800" y="185" text-anchor="middle" fill="#92400e" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    传统交叉熵损失
  </text>
  <text x="680" y="210" fill="#78350f" font-family="SimHei, Arial, sans-serif" font-size="12">
    ❌ 专注于多数类的高准确率
  </text>
  <text x="680" y="230" fill="#78350f" font-family="SimHei, Arial, sans-serif" font-size="12">
    ❌ 忽略小的关键区域
  </text>
  <text x="680" y="250" fill="#78350f" font-family="SimHei, Arial, sans-serif" font-size="12">
    ❌ 即使遗漏关键点也有低损失
  </text>
  
  <rect x="950" y="160" width="260" height="100" rx="8" fill="#d1fae5" stroke="#34d399" stroke-width="1"/>
  <text x="1080" y="185" text-anchor="middle" fill="#065f46" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    Penalty Dice Loss
  </text>
  <text x="960" y="210" fill="#047857" font-family="SimHei, Arial, sans-serif" font-size="12">
    ✓ 强调类别间的重叠度
  </text>
  <text x="960" y="230" fill="#047857" font-family="SimHei, Arial, sans-serif" font-size="12">
    ✓ 关注小的关键区域
  </text>
  <text x="960" y="250" fill="#047857" font-family="SimHei, Arial, sans-serif" font-size="12">
    ✓ 对未检测区域进行惩罚
  </text>
  
  <!-- Mathematical formulation -->
  <rect x="670" y="280" width="540" height="90" rx="8" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="1"/>
  <text x="940" y="305" text-anchor="middle" fill="#0c4a6e" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    数学公式
  </text>
  <text x="680" y="330" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    Dice(i) = (2 × |R^pred_i ∩ R^target_i| + δ) / (|R^pred_i| + |R^target_i| + δ)
  </text>
  <text x="680" y="350" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    L = Σy_i log(ŷ_i) + (1 - ΣDice(i)) + MAX exp(α_i)
  </text>
  <text x="680" y="365" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="11">
    其中 δ = 1×10^-6 (平滑项), α_i = 未检测区域的惩罚项
  </text>
  
  <!-- Bottom section: Detailed mechanism -->
  <rect x="50" y="400" width="1180" height="280" rx="15" fill="white" stroke="#7c3aed" stroke-width="3"/>
  <text x="640" y="430" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="24" font-weight="bold">
    Penalty Dice Loss 机制详解
  </text>
  
  <!-- Three components -->
  <rect x="80" y="460" width="350" height="180" rx="10" fill="#fef2f2" stroke="#ef4444" stroke-width="2"/>
  <text x="255" y="485" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    组成部分1：交叉熵损失
  </text>
  <text x="90" y="510" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    L_CE = Σ y_i log(ŷ_i)
  </text>
  <text x="90" y="535" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 标准分类损失
  </text>
  <text x="90" y="555" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 确保基本分类准确性
  </text>
  <text x="90" y="575" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 处理表示良好的类别
  </text>
  <text x="90" y="595" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 为优化提供梯度
  </text>
  <text x="90" y="615" fill="#dc2626" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    问题：偏向多数类
  </text>
  
  <rect x="465" y="460" width="350" height="180" rx="10" fill="#f0fdf4" stroke="#22c55e" stroke-width="2"/>
  <text x="640" y="485" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    组成部分2：Dice损失
  </text>
  <text x="475" y="510" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    L_Dice = 1 - Σ Dice(i)
  </text>
  <text x="475" y="535" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 测量区域重叠相似性
  </text>
  <text x="475" y="555" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 关注空间一致性
  </text>
  <text x="475" y="575" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 更好地处理类别不平衡
  </text>
  <text x="475" y="595" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 强调少数类
  </text>
  <text x="475" y="615" fill="#15803d" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    优势：区域感知优化
  </text>
  
  <rect x="850" y="460" width="350" height="180" rx="10" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="2"/>
  <text x="1025" y="485" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    组成部分3：惩罚项
  </text>
  <text x="860" y="510" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    L_Penalty = MAX exp(α_i)
  </text>
  <text x="860" y="535" fill="#6b7280" font-family="Arial, sans-serif" font-size="12">
    • α_i = (R^pred_i ∪ R^target_i) / (R^pred_i ∩ R^target_i)
  </text>
  <text x="860" y="555" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 对遗漏区域的指数惩罚
  </text>
  <text x="860" y="575" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 强制网络关注小区域
  </text>
  <text x="860" y="595" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 防止完全区域遗漏
  </text>
  <text x="860" y="615" fill="#7c3aed" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    创新：显式未检测惩罚
  </text>
  
  <!-- Results comparison -->
  <rect x="80" y="660" width="1120" height="40" rx="8" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="1"/>
  <text x="640" y="685" text-anchor="middle" fill="#0c4a6e" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    实验结果：mIoU从62.59%提升到66.60% (k=40)，在更低k值下实现稳健检测
  </text>
</svg>
