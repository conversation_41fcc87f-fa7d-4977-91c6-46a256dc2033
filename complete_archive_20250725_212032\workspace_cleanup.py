#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工作区整理脚本 - 保留重要数据和代码，存档其他文件
Workspace Cleanup Script - Keep Important Data and Code, Archive Others
"""

import os
import shutil
import glob
from datetime import datetime

def organize_workspace():
    """整理工作区"""
    
    print("🗂️ 开始整理工作区...")
    
    # 创建存档目录
    archive_dir = f"archive_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(archive_dir, exist_ok=True)
    
    # 创建分类目录
    dirs_to_create = [
        f"{archive_dir}/old_experiments",
        f"{archive_dir}/intermediate_results", 
        f"{archive_dir}/draft_code",
        f"{archive_dir}/test_files",
        "final_results",
        "active_code"
    ]
    
    for dir_name in dirs_to_create:
        os.makedirs(dir_name, exist_ok=True)
    
    print(f"✅ 创建存档目录: {archive_dir}")
    
    # 定义文件分类规则
    file_categories = {
        # 保留的重要数据文件
        'keep_data': [
            'high_quality_pelvis_57_dataset.npz',  # 原始数据集
            'fixed_comprehensive_benchmark_results.csv',  # 最终实验结果
            'fixed_comprehensive_benchmark_results.json',
            'final_comprehensive_benchmark_summary.csv',  # 论文总结
        ],
        
        # 保留的重要可视化结果
        'keep_visualizations': [
            'detailed_36_experiments_complete.png',  # 详细36实验图
            'complete_36_experiments_table.png',  # 完整实验表格
            'fixed_comprehensive_benchmark_analysis.png',  # 修复版分析
            'benchmark_fix_comparison.png',  # 修复对比
        ],
        
        # 保留的核心代码
        'keep_code': [
            'fixed_comprehensive_benchmark.py',  # 修复版基准测试
            'detailed_36_experiments_visualization.py',  # 详细可视化
            'fixed_benchmark_analysis.py',  # 修复版分析
        ],
        
        # 存档的旧实验结果
        'archive_old_results': [
            'comprehensive_3d_benchmark_results.csv',
            'comprehensive_3d_benchmark_results.json', 
            'comprehensive_3d_paper_summary.csv',
            'memory_optimized_training_results.csv',
            'memory_optimized_training_results.json',
            'small_dataset_deep_training_results.csv',
            'small_dataset_deep_training_results.json',
        ],
        
        # 存档的旧可视化
        'archive_old_viz': [
            'comprehensive_3d_benchmark_analysis.png',
            'comprehensive_workload_table.png',
            'training_evolution_analysis.png',
        ],
        
        # 存档的草稿代码
        'archive_draft_code': [
            'comprehensive_3d_benchmark_matrix.py',
            'comprehensive_3d_analysis.py',
            'small_dataset_deep_training.py',
            'memory_optimized_training.py',
            'training_evolution_analysis.py',
        ],
        
        # 存档的测试文件
        'archive_test_files': [
            'small_dataset_training_recommendations.json',
        ]
    }
    
    # 执行文件整理
    moved_files = {'kept': [], 'archived': []}
    
    # 保留重要文件到指定目录
    for file_pattern in file_categories['keep_data']:
        files = glob.glob(file_pattern)
        for file in files:
            if os.path.exists(file):
                shutil.copy2(file, 'final_results/')
                moved_files['kept'].append(f"final_results/{file}")
                print(f"📊 保留数据: {file} → final_results/")
    
    for file_pattern in file_categories['keep_visualizations']:
        files = glob.glob(file_pattern)
        for file in files:
            if os.path.exists(file):
                shutil.copy2(file, 'final_results/')
                moved_files['kept'].append(f"final_results/{file}")
                print(f"📈 保留图表: {file} → final_results/")
    
    for file_pattern in file_categories['keep_code']:
        files = glob.glob(file_pattern)
        for file in files:
            if os.path.exists(file):
                shutil.copy2(file, 'active_code/')
                moved_files['kept'].append(f"active_code/{file}")
                print(f"💻 保留代码: {file} → active_code/")
    
    # 存档旧文件
    archive_mappings = [
        ('archive_old_results', f'{archive_dir}/old_experiments/'),
        ('archive_old_viz', f'{archive_dir}/old_experiments/'),
        ('archive_draft_code', f'{archive_dir}/draft_code/'),
        ('archive_test_files', f'{archive_dir}/test_files/')
    ]
    
    for category, dest_dir in archive_mappings:
        for file_pattern in file_categories[category]:
            files = glob.glob(file_pattern)
            for file in files:
                if os.path.exists(file):
                    shutil.move(file, dest_dir)
                    moved_files['archived'].append(f"{dest_dir}{file}")
                    print(f"📦 存档: {file} → {dest_dir}")
    
    # 创建工作区说明文件
    create_workspace_readme(moved_files)
    
    print(f"\n✅ 工作区整理完成!")
    print(f"   📊 保留文件: {len(moved_files['kept'])} 个")
    print(f"   📦 存档文件: {len(moved_files['archived'])} 个")
    print(f"   📁 存档目录: {archive_dir}")
    
    return archive_dir, moved_files

def create_workspace_readme(moved_files):
    """创建工作区说明文件"""
    
    readme_content = f"""# 医疗点云关键点检测数据集基准测试工作区

## 📊 项目概述
96样本医疗骨盆关键点检测数据集的全面基准测试系统

## 📁 目录结构

### final_results/ - 最终重要结果
- `high_quality_pelvis_57_dataset.npz` - 原始数据集
- `fixed_comprehensive_benchmark_results.csv` - 36个实验的完整结果
- `final_comprehensive_benchmark_summary.csv` - 论文级别总结
- `detailed_36_experiments_complete.png` - 详细36实验可视化
- `complete_36_experiments_table.png` - 完整实验表格
- `fixed_comprehensive_benchmark_analysis.png` - 修复版分析图
- `benchmark_fix_comparison.png` - 修复前后对比

### active_code/ - 核心代码
- `fixed_comprehensive_benchmark.py` - 修复版基准测试(100%成功率)
- `detailed_36_experiments_visualization.py` - 详细可视化代码
- `fixed_benchmark_analysis.py` - 修复版分析代码

### archive_YYYYMMDD_HHMMSS/ - 存档文件
- `old_experiments/` - 旧实验结果和图表
- `draft_code/` - 草稿代码
- `test_files/` - 测试文件

## 🏆 主要成果

### 实验规模
- **36个完整实验配置** (100%成功率)
- **4种架构** × **3种点云大小** × **3种关键点配置**
- **0个失败实验** (vs 原版19个失败)

### 最佳性能
- **最优配置**: Residual + 12关键点 + 8K点 = **15.63mm误差**
- **医疗级达标**: 32.5%达标率 (≤10mm误差)
- **参数效率**: Lightweight仅0.04M参数达到16.83mm

### 架构排名
1. **Residual**: 16.41±0.54mm (最佳)
2. **Standard**: 19.00±4.96mm
3. **Lightweight**: 23.04±5.50mm (最高效)
4. **Deep**: 29.44±1.63mm

## 🔧 技术修复
- ✅ 修复张量维度不匹配问题
- ✅ 解决GPU内存溢出问题  
- ✅ 优化训练稳定性
- ✅ 实现100%实验成功率

## 📈 数据集价值
- **挑战性适中**: 15.6-29.4mm误差范围
- **训练可行性**: 100%成功收敛
- **方法区分度**: 13.8mm性能跨度
- **医疗相关性**: 符合实际应用需求

## 🎯 使用指南

### 运行基准测试
```bash
cd active_code/
python fixed_comprehensive_benchmark.py
```

### 生成详细可视化
```bash
cd active_code/
python detailed_36_experiments_visualization.py
```

### 分析结果
```bash
cd active_code/
python fixed_benchmark_analysis.py
```

## 📊 数据文件说明
- `.csv` 文件可用Excel/Python pandas读取
- `.json` 文件包含完整的实验元数据
- `.png` 文件为高分辨率图表(300 DPI)

## 🏆 论文贡献
- 提供96样本数据集的完整基准
- 证明数据集的科学研究价值
- 建立可重现的评估方法论
- 为后续研究提供性能基线

---
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
工作区版本: 修复版 v2.0 (100%成功率)
"""
    
    with open('README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    # 创建文件清单
    file_inventory = f"""# 文件清单

## 保留的重要文件 ({len(moved_files['kept'])} 个)
"""
    
    for file in moved_files['kept']:
        file_inventory += f"- {file}\n"
    
    file_inventory += f"\n## 存档的文件 ({len(moved_files['archived'])} 个)\n"
    
    for file in moved_files['archived']:
        file_inventory += f"- {file}\n"
    
    with open('FILE_INVENTORY.md', 'w', encoding='utf-8') as f:
        f.write(file_inventory)
    
    print("📝 创建工作区说明文件: README.md")
    print("📋 创建文件清单: FILE_INVENTORY.md")

if __name__ == "__main__":
    print("🗂️ 工作区整理脚本")
    print("保留重要数据和代码，存档其他文件")
    print("=" * 60)
    
    # 执行整理
    archive_dir, moved_files = organize_workspace()
    
    print(f"\n📋 整理总结:")
    print(f"   📊 final_results/: 重要数据和图表")
    print(f"   💻 active_code/: 核心代码")
    print(f"   📦 {archive_dir}/: 存档文件")
    print(f"   📝 README.md: 工作区说明")
    print(f"   📋 FILE_INVENTORY.md: 文件清单")
    
    print(f"\n🎯 现在工作区结构清晰:")
    print(f"   • 重要结果易于访问")
    print(f"   • 核心代码独立存放") 
    print(f"   • 历史文件安全存档")
    print(f"   • 完整文档说明")
    
    print(f"\n💡 推荐使用方式:")
    print(f"   • 查看结果: final_results/")
    print(f"   • 运行代码: active_code/")
    print(f"   • 了解项目: README.md")
    print(f"   • 查找文件: FILE_INVENTORY.md")
