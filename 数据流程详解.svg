<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="1280" height="720" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="url(#headerGrad)"/>
  <text x="640" y="50" text-anchor="middle" fill="white" 
        font-family="SimHei, Arial, sans-serif" font-size="36" font-weight="bold">
    数据流程详解：从点云到关键点的完整变换过程
  </text>
  
  <!-- Step 1: Input Point Cloud -->
  <rect x="50" y="100" width="200" height="120" rx="10" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <text x="150" y="125" text-anchor="middle" fill="#1e40af" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    步骤1：输入点云
  </text>
  <text x="60" y="150" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    数据形状：N × 3
  </text>
  <text x="60" y="170" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 颅骨：60,000个点
  </text>
  <text x="60" y="190" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 胫骨：20,000个点
  </text>
  <text x="60" y="210" fill="#1e40af" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    每个点：(x, y, z)坐标
  </text>
  
  <!-- Arrow 1 -->
  <path d="M 260 160 L 290 160" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
    </marker>
  </defs>
  
  <!-- Step 2: Multi-scale Feature Extraction -->
  <rect x="300" y="100" width="200" height="120" rx="10" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <text x="400" y="125" text-anchor="middle" fill="#d97706" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    步骤2：多尺度特征提取
  </text>
  <text x="310" y="150" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    输入：N × 3 → 输出：D × C
  </text>
  <text x="310" y="170" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • FPS采样：选择D个节点
  </text>
  <text x="310" y="190" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • Ball Query：多尺度聚合
  </text>
  <text x="310" y="210" fill="#d97706" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    特征维度：C维特征向量
  </text>
  
  <!-- Arrow 2 -->
  <path d="M 510 160 L 540 160" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Step 3: Cross-fusion -->
  <rect x="550" y="100" width="200" height="120" rx="10" fill="white" stroke="#10b981" stroke-width="3"/>
  <text x="650" y="125" text-anchor="middle" fill="#059669" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    步骤3：交叉融合
  </text>
  <text x="560" y="150" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    输入：{h₁, h₂, h₃} → 输出：G
  </text>
  <text x="560" y="170" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • h₂ = Φ(h₁ ⊕ h₂)
  </text>
  <text x="560" y="190" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • h₃ = Φ(h₁ ⊕ h₂ ⊕ h₃)
  </text>
  <text x="560" y="210" fill="#059669" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    全局特征：G维向量
  </text>
  
  <!-- Arrow 3 -->
  <path d="M 760 160 L 790 160" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Step 4: Segmentation -->
  <rect x="800" y="100" width="200" height="120" rx="10" fill="white" stroke="#ef4444" stroke-width="3"/>
  <text x="900" y="125" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    步骤4：潜在区域检测
  </text>
  <text x="810" y="150" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    输入：G → 输出：{Rᵢ}ⁿᵢ₌₁
  </text>
  <text x="810" y="170" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 分割网络预测
  </text>
  <text x="810" y="190" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • K近邻选择k个点
  </text>
  <text x="810" y="210" fill="#dc2626" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    n个潜在区域，每个k个点
  </text>
  
  <!-- Arrow down -->
  <path d="M 900 230 L 900 260" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Step 5: Region Processing -->
  <rect x="800" y="270" width="200" height="120" rx="10" fill="white" stroke="#8b5cf6" stroke-width="3"/>
  <text x="900" y="295" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    步骤5：区域特征提取
  </text>
  <text x="810" y="320" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    输入：Rᵢ (k × 3) → 输出：fᵢ
  </text>
  <text x="810" y="340" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • T-Net变换
  </text>
  <text x="810" y="360" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • PointNet编码
  </text>
  <text x="810" y="380" fill="#7c3aed" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    特征向量：每个区域一个fᵢ
  </text>
  
  <!-- Arrow left -->
  <path d="M 790 330 L 760 330" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Step 6: First SoftMax -->
  <rect x="550" y="270" width="200" height="120" rx="10" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <text x="650" y="295" text-anchor="middle" fill="#d97706" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    步骤6：第一次SoftMax
  </text>
  <text x="560" y="320" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    输入：fᵢ → 输出：{ωⱼ}ᵏⱼ₌₁
  </text>
  <text x="560" y="340" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • ωⱼ = exp(fⱼ) / Σexp(fₖ)
  </text>
  <text x="560" y="360" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 基于特征相似性
  </text>
  <text x="560" y="380" fill="#d97706" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    k个权重值（近似均匀）
  </text>
  
  <!-- Arrow left -->
  <path d="M 540 330 L 510 330" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Step 7: Second SoftMax -->
  <rect x="300" y="270" width="200" height="120" rx="10" fill="white" stroke="#10b981" stroke-width="3"/>
  <text x="400" y="295" text-anchor="middle" fill="#059669" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    步骤7：第二次SoftMax
  </text>
  <text x="310" y="320" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    输入：{ωⱼ} → 输出：{WS(ωⱼ)}
  </text>
  <text x="310" y="340" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 阈值过滤函数
  </text>
  <text x="310" y="360" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 权重差异放大
  </text>
  <text x="310" y="380" fill="#059669" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    k个细化权重（尖锐分布）
  </text>
  
  <!-- Arrow left -->
  <path d="M 290 330 L 260 330" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Step 8: Weighted Average -->
  <rect x="50" y="270" width="200" height="120" rx="10" fill="white" stroke="#ef4444" stroke-width="3"/>
  <text x="150" y="295" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    步骤8：加权平均
  </text>
  <text x="60" y="320" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    输入：{WS(ωⱼ)}, {coordⱼ}
  </text>
  <text x="60" y="340" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • kpᵢ = Σ(ωⱼ·WS(ωⱼ)·coordⱼ)
  </text>
  <text x="60" y="360" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 除以权重总和
  </text>
  <text x="60" y="380" fill="#dc2626" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    输出：最终关键点坐标
  </text>
  
  <!-- Data transformation summary -->
  <rect x="50" y="420" width="1180" height="260" rx="15" fill="white" stroke="#7c3aed" stroke-width="3"/>
  <text x="640" y="450" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="24" font-weight="bold">
    数据维度变化总结
  </text>
  
  <!-- Data flow table -->
  <rect x="80" y="470" width="1120" height="190" rx="10" fill="#f8fafc" stroke="#cbd5e1" stroke-width="1"/>
  
  <!-- Table headers -->
  <rect x="80" y="470" width="140" height="30" fill="#1e40af" stroke="#1e40af" stroke-width="1"/>
  <text x="150" y="490" text-anchor="middle" fill="white" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    处理步骤
  </text>
  
  <rect x="220" y="470" width="200" height="30" fill="#1e40af" stroke="#1e40af" stroke-width="1"/>
  <text x="320" y="490" text-anchor="middle" fill="white" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    数据形状
  </text>
  
  <rect x="420" y="470" width="300" height="30" fill="#1e40af" stroke="#1e40af" stroke-width="1"/>
  <text x="570" y="490" text-anchor="middle" fill="white" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    数据含义
  </text>
  
  <rect x="720" y="470" width="200" height="30" fill="#1e40af" stroke="#1e40af" stroke-width="1"/>
  <text x="820" y="490" text-anchor="middle" fill="white" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    典型数值
  </text>
  
  <rect x="920" y="470" width="280" height="30" fill="#1e40af" stroke="#1e40af" stroke-width="1"/>
  <text x="1060" y="490" text-anchor="middle" fill="white" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    关键变化
  </text>
  
  <!-- Table rows -->
  <!-- Row 1 -->
  <rect x="80" y="500" width="140" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="150" y="517" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    原始点云
  </text>
  
  <rect x="220" y="500" width="200" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="320" y="517" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="10">
    N × 3
  </text>
  
  <rect x="420" y="500" width="300" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="570" y="517" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    N个3D坐标点
  </text>
  
  <rect x="720" y="500" width="200" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="820" y="517" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="10">
    60K × 3 (颅骨)
  </text>
  
  <rect x="920" y="500" width="280" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="1060" y="517" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    密集3D点云
  </text>
  
  <!-- Row 2 -->
  <rect x="80" y="525" width="140" height="25" fill="#f0f9ff" stroke="#cbd5e1" stroke-width="1"/>
  <text x="150" y="542" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    特征提取
  </text>
  
  <rect x="220" y="525" width="200" height="25" fill="#f0f9ff" stroke="#cbd5e1" stroke-width="1"/>
  <text x="320" y="542" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="10">
    D × C
  </text>
  
  <rect x="420" y="525" width="300" height="25" fill="#f0f9ff" stroke="#cbd5e1" stroke-width="1"/>
  <text x="570" y="542" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    D个节点，每个C维特征
  </text>
  
  <rect x="720" y="525" width="200" height="25" fill="#f0f9ff" stroke="#cbd5e1" stroke-width="1"/>
  <text x="820" y="542" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="10">
    1024 × 128
  </text>
  
  <rect x="920" y="525" width="280" height="25" fill="#f0f9ff" stroke="#cbd5e1" stroke-width="1"/>
  <text x="1060" y="542" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    降维+特征学习
  </text>
  
  <!-- Row 3 -->
  <rect x="80" y="550" width="140" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="150" y="567" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    潜在区域
  </text>
  
  <rect x="220" y="550" width="200" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="320" y="567" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="10">
    n × (k × 3)
  </text>
  
  <rect x="420" y="550" width="300" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="570" y="567" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    n个区域，每个k个点
  </text>
  
  <rect x="720" y="550" width="200" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="820" y="567" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="10">
    14 × (15 × 3)
  </text>
  
  <rect x="920" y="550" width="280" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="1060" y="567" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    区域分割+采样
  </text>
  
  <!-- Row 4 -->
  <rect x="80" y="575" width="140" height="25" fill="#f0f9ff" stroke="#cbd5e1" stroke-width="1"/>
  <text x="150" y="592" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    权重计算
  </text>
  
  <rect x="220" y="575" width="200" height="25" fill="#f0f9ff" stroke="#cbd5e1" stroke-width="1"/>
  <text x="320" y="592" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="10">
    n × k
  </text>
  
  <rect x="420" y="575" width="300" height="25" fill="#f0f9ff" stroke="#cbd5e1" stroke-width="1"/>
  <text x="570" y="592" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    每个区域k个权重值
  </text>
  
  <rect x="720" y="575" width="200" height="25" fill="#f0f9ff" stroke="#cbd5e1" stroke-width="1"/>
  <text x="820" y="592" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="10">
    14 × 15
  </text>
  
  <rect x="920" y="575" width="280" height="25" fill="#f0f9ff" stroke="#cbd5e1" stroke-width="1"/>
  <text x="1060" y="592" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    双SoftMax细化
  </text>
  
  <!-- Row 5 -->
  <rect x="80" y="600" width="140" height="25" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="150" y="617" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    最终关键点
  </text>
  
  <rect x="220" y="600" width="200" height="25" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="320" y="617" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="10">
    n × 3
  </text>
  
  <rect x="420" y="600" width="300" height="25" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="570" y="617" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    n个精确关键点坐标
  </text>
  
  <rect x="720" y="600" width="200" height="25" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="820" y="617" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="10">
    14 × 3
  </text>
  
  <rect x="920" y="600" width="280" height="25" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="1060" y="617" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="10" font-weight="bold">
    加权平均定位
  </text>
  
  <!-- Key insight -->
  <rect x="80" y="635" width="1120" height="25" rx="5" fill="#fef7ff" stroke="#a855f7" stroke-width="1"/>
  <text x="640" y="652" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    核心变化：从60K个密集点 → 14个精确关键点，实现1000+倍的信息压缩与精确定位
  </text>
</svg>
