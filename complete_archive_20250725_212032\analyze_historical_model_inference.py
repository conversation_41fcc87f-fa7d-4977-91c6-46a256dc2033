#!/usr/bin/env python3
"""
分析历史模型推理过程
Analyze Historical Model Inference
直接加载历史模型，分析其推理过程和输出格式
"""

import torch
import torch.nn as nn
import numpy as np
import os

def load_and_analyze_historical_model():
    """加载并分析历史模型"""
    
    print("🔍 加载并分析历史模型")
    print("=" * 60)
    
    historical_model_path = "archive/old_models/best_exact_ensemble_seed123_5.371mm.pth"
    
    if not os.path.exists(historical_model_path):
        print(f"❌ 历史模型文件不存在")
        return None
    
    try:
        checkpoint = torch.load(historical_model_path, map_location='cpu')
        
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
            
            print(f"📋 模型状态字典分析:")
            print(f"   总参数层数: {len(state_dict)}")
            
            # 分析输出层
            output_layers = []
            for name, param in state_dict.items():
                if 'fc5' in name or ('fc' in name and 'weight' in name):
                    output_layers.append((name, param.shape))
            
            print(f"\n🎯 输出层分析:")
            for name, shape in output_layers:
                print(f"   {name}: {shape}")
            
            # 查找最终输出
            final_output = None
            for name, param in state_dict.items():
                if name == 'fc5.weight':
                    final_output = param
                    print(f"\n🔍 最终输出层详细分析:")
                    print(f"   形状: {param.shape}")
                    print(f"   输出维度: {param.shape[0]}")
                    print(f"   输入维度: {param.shape[1]}")
                    
                    if param.shape[0] == 36:
                        print(f"   ✅ 确认12点输出 (12 * 3 = 36)")
                    else:
                        print(f"   ❓ 未知输出格式")
                    
                    # 分析权重分布
                    print(f"   权重范围: [{param.min():.4f}, {param.max():.4f}]")
                    print(f"   权重均值: {param.mean():.4f}")
                    print(f"   权重标准差: {param.std():.4f}")
            
            return state_dict
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def analyze_historical_training_details():
    """分析历史训练细节"""
    
    print(f"\n🔍 分析历史训练细节")
    print("=" * 60)
    
    # 查找可能的训练配置文件
    config_files = [
        "archive/old_experiments/training_config.json",
        "trained_models/best_performers/config.json",
        "Archive_F3_Experiments/training_logs/config.json"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ 找到配置文件: {config_file}")
            try:
                import json
                with open(config_file, 'r') as f:
                    config = json.load(f)
                
                print(f"📋 训练配置:")
                for key, value in config.items():
                    print(f"   {key}: {value}")
                
                return config
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
        else:
            print(f"❌ 未找到: {config_file}")
    
    return None

def analyze_output_format_mystery():
    """分析输出格式之谜"""
    
    print(f"\n🔍 分析输出格式之谜")
    print("=" * 60)
    
    print(f"🤔 关键问题:")
    print(f"   1. 历史模型最终输出是36维 (12 * 3)")
    print(f"   2. 但我们看到的还有64维的fc4层")
    print(f"   3. 这可能意味着什么？")
    
    print(f"\n💡 可能的解释:")
    explanations = [
        {
            "解释": "双分支输出",
            "描述": "模型可能有两个输出分支，一个36维，一个64维",
            "可能性": "高"
        },
        {
            "解释": "特征向量输出",
            "描述": "64维是特征向量，需要额外处理才能得到坐标",
            "可能性": "中"
        },
        {
            "解释": "集成输出",
            "描述": "多个子模型的输出需要融合",
            "可能性": "高"
        },
        {
            "解释": "后处理步骤",
            "描述": "原始输出需要特殊的后处理才能得到最终坐标",
            "可能性": "中"
        }
    ]
    
    for exp in explanations:
        print(f"   • {exp['解释']} ({exp['可能性']}可能性)")
        print(f"     {exp['描述']}")

def investigate_double_softmax_mystery():
    """调查双Softmax之谜"""
    
    print(f"\n🔍 调查双Softmax实现之谜")
    print("=" * 60)
    
    print(f"🔍 从历史模型参数中发现的双Softmax结构:")
    print(f"   • double_softmax_modules.0.weight_net.*")
    print(f"   • double_softmax_modules.1.weight_net.*")
    print(f"   • double_softmax_modules.2.weight_net.*")
    
    print(f"\n💡 双Softmax可能的工作方式:")
    print(f"   1. 训练时：直接输出36维坐标")
    print(f"   2. 推理时：应用双Softmax精细化")
    print(f"   3. 精细化过程可能是关键差异")
    
    print(f"\n🤔 我们的实现可能缺少:")
    missing_components = [
        "正确的双Softmax权重计算",
        "集成模块的正确融合方法",
        "推理时的精细化步骤",
        "特定的阈值和温度参数",
        "正确的点云-关键点交互机制"
    ]
    
    for i, component in enumerate(missing_components, 1):
        print(f"   {i}. {component}")

def propose_next_steps():
    """提出下一步行动"""
    
    print(f"\n🚀 下一步行动建议")
    print("=" * 60)
    
    steps = [
        {
            "步骤": "尝试直接使用历史模型推理",
            "描述": "加载历史模型，在历史数据上直接推理",
            "目的": "验证历史模型是否真的能达到5.371mm",
            "优先级": "最高"
        },
        {
            "步骤": "逆向工程双Softmax机制",
            "描述": "从历史模型参数中推断双Softmax的确切实现",
            "目的": "理解精细化过程的具体细节",
            "优先级": "高"
        },
        {
            "步骤": "分析历史评估代码",
            "描述": "寻找历史模型的评估和后处理代码",
            "目的": "确认评估方法是否一致",
            "优先级": "高"
        },
        {
            "步骤": "重新实现完整的历史架构",
            "描述": "基于分析结果，完全重新实现历史模型",
            "目的": "最终复现5.371mm性能",
            "优先级": "中"
        }
    ]
    
    for step in steps:
        print(f"🎯 {step['步骤']} ({step['优先级']}优先级)")
        print(f"   描述: {step['描述']}")
        print(f"   目的: {step['目的']}")
        print()

def main():
    """主函数"""
    
    print("🎯 分析历史模型推理过程")
    print("深入理解5.371mm模型的真实工作方式")
    print("=" * 80)
    
    # 1. 加载并分析历史模型
    state_dict = load_and_analyze_historical_model()
    
    # 2. 分析训练细节
    config = analyze_historical_training_details()
    
    # 3. 分析输出格式之谜
    analyze_output_format_mystery()
    
    # 4. 调查双Softmax之谜
    investigate_double_softmax_mystery()
    
    # 5. 提出下一步行动
    propose_next_steps()
    
    print(f"\n🎯 当前结论:")
    print(f"   ❌ 数据集不是唯一问题")
    print(f"   ❌ 我们的架构实现可能有重大缺陷")
    print(f"   ❓ 历史模型可能有特殊的推理过程")
    print(f"   🎯 需要更深入的逆向工程")
    
    print(f"\n💡 您的质疑再次被证实:")
    print(f"   ✅ 我们确实无法复现5mm性能")
    print(f"   ✅ 问题比想象的更复杂")
    print(f"   ✅ 需要更深入的分析")

if __name__ == "__main__":
    main()
