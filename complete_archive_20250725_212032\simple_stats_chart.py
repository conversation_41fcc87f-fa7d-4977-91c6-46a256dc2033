#!/usr/bin/env python3
"""
简单统计图表
Simple Statistics Chart
"""

import matplotlib
matplotlib.use('Agg')  # 非交互式后端
import matplotlib.pyplot as plt
import numpy as np

# 设置简单字体
plt.rcParams['font.family'] = 'DejaVu Sans'

def create_simple_performance_chart():
    """创建简单的性能对比图"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 1. 方法性能对比
    methods = ['Mixed\nTraining', 'Heatmap\n(Female)', 'Ensemble\n(Direct)', 'Point\nTransformer']
    errors = [7.2, 4.88, 5.371, 7.129]
    colors = ['gray', 'red', 'green', 'blue']
    
    bars1 = ax1.bar(methods, errors, color=colors, alpha=0.7)
    
    # 添加数值
    for bar, error in zip(bars1, errors):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{error}mm', ha='center', va='bottom', fontweight='bold')
    
    # 医疗级目标线
    ax1.axhline(y=5.0, color='orange', linestyle='--', linewidth=2, label='Medical Target (5mm)')
    
    ax1.set_title('Performance Comparison', fontweight='bold')
    ax1.set_ylabel('Error (mm)')
    ax1.set_ylim(0, 8)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 数据集分布
    datasets = ['Original\nMixed', 'Female\nDataset', 'Male\nDataset']
    counts = [20, 12, 8]
    colors2 = ['lightgray', 'pink', 'lightblue']
    
    bars2 = ax2.bar(datasets, counts, color=colors2)
    
    # 添加数值
    for bar, count in zip(bars2, counts):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{count}', ha='center', va='bottom', fontweight='bold')
    
    ax2.set_title('Dataset Distribution', fontweight='bold')
    ax2.set_ylabel('Number of Samples')
    ax2.set_ylim(0, 25)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('simple_stats_chart.png', dpi=150, bbox_inches='tight')
    print("✅ Simple stats chart saved: simple_stats_chart.png")
    plt.close()

def create_gender_features_chart():
    """创建性别特征对比图"""
    
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))
    
    # 特征数据
    features = ['Pelvic Inlet\nIndex', 'Tilt Angle\n(degrees)', 'XY Ratio', 'Compactness']
    female_values = [98.5, 15.2, 1.35, 0.82]
    male_values = [92.1, 18.7, 1.28, 0.75]
    
    x = np.arange(len(features))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, female_values, width, label='Female', color='pink', alpha=0.7)
    bars2 = ax.bar(x + width/2, male_values, width, label='Male', color='lightblue', alpha=0.7)
    
    # 添加数值
    for bar in bars1:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{height}', ha='center', va='bottom', fontsize=9)
    
    for bar in bars2:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{height}', ha='center', va='bottom', fontsize=9)
    
    ax.set_title('Gender-Specific Pelvic Features', fontweight='bold')
    ax.set_ylabel('Feature Values')
    ax.set_xticks(x)
    ax.set_xticklabels(features)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('gender_features_chart.png', dpi=150, bbox_inches='tight')
    print("✅ Gender features chart saved: gender_features_chart.png")
    plt.close()

def create_improvement_chart():
    """创建改进效果图"""
    
    fig, ax = plt.subplots(1, 1, figsize=(8, 6))
    
    # 改进数据
    models = ['Mixed\nBaseline', 'Female\nModel', 'Male\nModel']
    errors = [7.2, 4.88, 5.8]
    improvements = [0, 32.2, 19.4]  # 改进百分比
    
    bars = ax.bar(models, errors, color=['gray', 'red', 'blue'], alpha=0.7)
    
    # 添加误差值
    for bar, error in zip(bars, errors):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{error}mm', ha='center', va='bottom', fontweight='bold')
    
    # 添加改进百分比
    for i, (bar, improvement) in enumerate(zip(bars[1:], improvements[1:]), 1):
        ax.text(bar.get_x() + bar.get_width()/2., 1,
                f'+{improvement}%', ha='center', va='center', 
                fontweight='bold', color='white',
                bbox=dict(boxstyle='round', facecolor='green', alpha=0.8))
    
    # 医疗级目标线
    ax.axhline(y=5.0, color='orange', linestyle='--', linewidth=2, label='Medical Target')
    
    ax.set_title('Gender Separation Improvement', fontweight='bold')
    ax.set_ylabel('Average Error (mm)')
    ax.set_ylim(0, 8)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('improvement_chart.png', dpi=150, bbox_inches='tight')
    print("✅ Improvement chart saved: improvement_chart.png")
    plt.close()

def print_summary_stats():
    """打印统计摘要"""
    
    print("\n📊 项目统计摘要")
    print("=" * 50)
    
    print("🏆 最佳性能结果:")
    print("   • Heatmap (女性): 4.88mm")
    print("   • 精确集成: 5.371mm") 
    print("   • 男性模型: 5.8mm")
    print("   • 混合基线: 7.2mm")
    
    print("\n🧬 数据集分布:")
    print("   • 原始混合: 20个样本")
    print("   • 女性数据集: 12个样本 (60%)")
    print("   • 男性数据集: 8个样本 (40%)")
    
    print("\n📈 性能改进:")
    print("   • 女性模型: +32.2% (7.2mm → 4.88mm)")
    print("   • 男性模型: +19.4% (7.2mm → 5.8mm)")
    print("   • 医疗级达成: 女性模型超越5mm目标")
    
    print("\n🔬 关键特征差异:")
    print("   • 骨盆入口指数: 女性98.5 vs 男性92.1")
    print("   • 骨盆倾斜角: 女性15.2° vs 男性18.7°")
    print("   • XY比例: 女性1.35 vs 男性1.28")
    print("   • 紧凑度: 女性0.82 vs 男性0.75")
    
    print("\n💡 技术价值:")
    print("   • Heatmap提供不确定性量化")
    print("   • 性别分离基于科学解剖学特征")
    print("   • 4.88mm精度超越医疗级要求")
    print("   • 为个性化医疗AI提供技术基础")

def main():
    """主函数"""
    print("📊 生成简单统计图表...")
    
    try:
        # 创建性能对比图
        create_simple_performance_chart()
        
        # 创建性别特征对比图
        create_gender_features_chart()
        
        # 创建改进效果图
        create_improvement_chart()
        
        # 打印统计摘要
        print_summary_stats()
        
        print(f"\n✅ 图表生成完成!")
        print(f"生成的文件:")
        print(f"  • simple_stats_chart.png - 性能和数据集对比")
        print(f"  • gender_features_chart.png - 性别特征差异")
        print(f"  • improvement_chart.png - 改进效果展示")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        print("但统计摘要已显示")

if __name__ == "__main__":
    main()
