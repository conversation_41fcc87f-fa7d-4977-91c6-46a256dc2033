#!/usr/bin/env python3
"""
内存优化的训练方案
减少内存使用，适应GPU限制，测试多种轻量级架构
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import time
import os
from datetime import datetime

class MedicalKeypointDataset(Dataset):
    """内存优化的医学关键点数据集"""
    
    def __init__(self, point_clouds, keypoints, sample_ids, num_points=8192, augment=False):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.sample_ids = sample_ids
        self.num_points = num_points
        self.augment = augment
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        pc = self.point_clouds[idx].copy()
        kp = self.keypoints[idx].copy()
        
        # 随机采样点云到指定数量
        if len(pc) > self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=False)
            pc = pc[indices]
        elif len(pc) < self.num_points:
            # 如果点数不够，重复采样
            indices = np.random.choice(len(pc), self.num_points, replace=True)
            pc = pc[indices]
        
        if self.augment:
            pc, kp = self.apply_augmentation(pc, kp)
        
        # 转换为tensor
        pc = torch.FloatTensor(pc).transpose(0, 1)  # [3, N]
        kp = torch.FloatTensor(kp.reshape(-1))  # [36] (12*3)
        
        return pc, kp, self.sample_ids[idx]
    
    def apply_augmentation(self, pc, kp):
        """轻量级数据增强"""
        # 随机旋转 (小角度)
        if np.random.random() > 0.5:
            angle = np.random.uniform(-3, 3) * np.pi / 180  # ±3度
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            pc = pc @ rotation_matrix.T
            kp = kp @ rotation_matrix.T
        
        # 随机缩放 (小幅度)
        if np.random.random() > 0.5:
            scale = np.random.uniform(0.98, 1.02)
            pc *= scale
            kp *= scale
        
        return pc, kp

class LightPointNet(nn.Module):
    """轻量级PointNet"""
    
    def __init__(self, num_points=8192, num_keypoints=12):
        super(LightPointNet, self).__init__()
        
        # 特征提取 (减少通道数)
        self.conv1 = nn.Conv1d(3, 32, 1)
        self.conv2 = nn.Conv1d(32, 64, 1)
        self.conv3 = nn.Conv1d(64, 128, 1)
        
        self.bn1 = nn.BatchNorm1d(32)
        self.bn2 = nn.BatchNorm1d(64)
        self.bn3 = nn.BatchNorm1d(128)
        
        # 回归头 (减少参数)
        self.fc1 = nn.Linear(128, 64)
        self.fc2 = nn.Linear(64, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.2)
        self.relu = nn.ReLU()
    
    def forward(self, x):
        # x: [B, 3, N]
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.relu(self.bn2(self.conv2(x)))
        x = self.bn3(self.conv3(x))
        
        # 全局最大池化
        x = torch.max(x, 2)[0]  # [B, 128]
        
        # 回归
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.fc2(x)
        
        return x

class EnhancedPointNet(nn.Module):
    """增强版PointNet (中等复杂度)"""
    
    def __init__(self, num_points=8192, num_keypoints=12):
        super(EnhancedPointNet, self).__init__()
        
        # 特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        
        # 注意力机制 (轻量级)
        self.attention = nn.Sequential(
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 256),
            nn.Sigmoid()
        )
        
        # 回归头
        self.fc1 = nn.Linear(256, 128)
        self.fc2 = nn.Linear(128, 64)
        self.fc3 = nn.Linear(64, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
    
    def forward(self, x):
        # x: [B, 3, N]
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.relu(self.bn2(self.conv2(x)))
        x = self.bn3(self.conv3(x))
        
        # 全局特征
        x_max = torch.max(x, 2)[0]  # [B, 256]
        x_avg = torch.mean(x, 2)    # [B, 256]
        
        # 注意力加权
        attention_weights = self.attention(x_max)
        x = attention_weights * x_max + (1 - attention_weights) * x_avg
        
        # 回归
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x

class ResidualPointNet(nn.Module):
    """残差连接的PointNet"""
    
    def __init__(self, num_points=8192, num_keypoints=12):
        super(ResidualPointNet, self).__init__()
        
        # 特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 64, 1)
        self.conv3 = nn.Conv1d(64, 128, 1)
        self.conv4 = nn.Conv1d(128, 128, 1)
        self.conv5 = nn.Conv1d(128, 256, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(64)
        self.bn3 = nn.BatchNorm1d(128)
        self.bn4 = nn.BatchNorm1d(128)
        self.bn5 = nn.BatchNorm1d(256)
        
        # 回归头
        self.fc1 = nn.Linear(256, 128)
        self.fc2 = nn.Linear(128, 64)
        self.fc3 = nn.Linear(64, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
    
    def forward(self, x):
        # x: [B, 3, N]
        x1 = self.relu(self.bn1(self.conv1(x)))
        x2 = self.relu(self.bn2(self.conv2(x1)))
        x2 = x1 + x2  # 残差连接
        
        x3 = self.relu(self.bn3(self.conv3(x2)))
        x4 = self.relu(self.bn4(self.conv4(x3)))
        x4 = x3 + x4  # 残差连接
        
        x5 = self.bn5(self.conv5(x4))
        
        # 全局特征
        x = torch.max(x5, 2)[0]  # [B, 256]
        
        # 回归
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x

def load_datasets():
    """加载数据集"""
    print("📊 **加载数据集**")
    
    # 加载完整数据集
    full_data = np.load('f3_reduced_12kp_stable.npz', allow_pickle=True)
    
    # 加载性别分离数据集
    female_data = np.load('f3_reduced_12kp_female.npz', allow_pickle=True)
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    
    datasets = {
        'full': {
            'sample_ids': full_data['sample_ids'],
            'point_clouds': full_data['point_clouds'],
            'keypoints': full_data['keypoints']
        },
        'male': {
            'sample_ids': male_data['sample_ids'],
            'point_clouds': male_data['point_clouds'],
            'keypoints': male_data['keypoints']
        }
    }
    
    print(f"   完整数据集: {len(datasets['full']['sample_ids'])}个样本")
    print(f"   男性数据集: {len(datasets['male']['sample_ids'])}个样本")
    print(f"   (跳过女性数据集 - 样本太少)")
    
    return datasets

def create_data_splits(datasets):
    """创建训练/验证/测试分割"""
    print(f"\n📋 **创建数据分割**")
    
    splits = {}
    
    for dataset_name, data in datasets.items():
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        n_samples = len(sample_ids)
        
        # 分割比例
        train_ratio, val_ratio, test_ratio = 0.7, 0.15, 0.15
        
        # 第一次分割: 训练 vs (验证+测试)
        train_idx, temp_idx = train_test_split(
            range(n_samples), 
            test_size=(val_ratio + test_ratio),
            random_state=42,
            shuffle=True
        )
        
        # 第二次分割: 验证 vs 测试
        val_idx, test_idx = train_test_split(
            temp_idx,
            test_size=test_ratio/(val_ratio + test_ratio),
            random_state=42,
            shuffle=True
        )
        
        splits[dataset_name] = {
            'train': {
                'sample_ids': sample_ids[train_idx],
                'point_clouds': point_clouds[train_idx],
                'keypoints': keypoints[train_idx]
            },
            'val': {
                'sample_ids': sample_ids[val_idx],
                'point_clouds': point_clouds[val_idx],
                'keypoints': keypoints[val_idx]
            },
            'test': {
                'sample_ids': sample_ids[test_idx],
                'point_clouds': point_clouds[test_idx],
                'keypoints': keypoints[test_idx]
            }
        }
        
        print(f"   {dataset_name}: 训练{len(train_idx)}, 验证{len(val_idx)}, 测试{len(test_idx)}")
    
    return splits

def train_model(model, train_loader, val_loader, model_name, dataset_name, epochs=100):
    """训练模型"""
    print(f"\n🚀 **训练{model_name} - {dataset_name}数据集**")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=15, factor=0.5)
    criterion = nn.MSELoss()
    
    # 训练历史
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    patience_counter = 0
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        
        for batch_idx, (pc, kp, _) in enumerate(train_loader):
            pc, kp = pc.to(device), kp.to(device)
            
            optimizer.zero_grad()
            pred = model(pc)
            loss = criterion(pred, kp)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for pc, kp, _ in val_loader:
                pc, kp = pc.to(device), kp.to(device)
                pred = model(pc)
                loss = criterion(pred, kp)
                val_loss += loss.item()
        
        val_loss /= len(val_loader)
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            # 保存最佳模型
            torch.save(model.state_dict(), f'best_{model_name}_{dataset_name}.pth')
        else:
            patience_counter += 1
        
        if epoch % 20 == 0:
            print(f"   Epoch {epoch}: Train Loss={train_loss:.6f}, Val Loss={val_loss:.6f}")
        
        # 早停
        if patience_counter >= 25:
            print(f"   早停于epoch {epoch}")
            break
    
    return train_losses, val_losses, best_val_loss

def evaluate_model(model, test_loader, model_name, dataset_name):
    """评估模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.eval()
    
    total_error = 0.0
    total_samples = 0
    errors = []
    sample_errors = {}
    
    with torch.no_grad():
        for pc, kp_true, sample_ids in test_loader:
            pc, kp_true = pc.to(device), kp_true.to(device)
            kp_pred = model(pc)
            
            # 计算每个样本的平均误差
            batch_size = pc.size(0)
            kp_true = kp_true.view(batch_size, -1, 3)
            kp_pred = kp_pred.view(batch_size, -1, 3)
            
            for i in range(batch_size):
                sample_error = torch.mean(torch.norm(kp_pred[i] - kp_true[i], dim=1)).item()
                errors.append(sample_error)
                sample_errors[sample_ids[i]] = sample_error
                total_error += sample_error
                total_samples += 1
    
    avg_error = total_error / total_samples if total_samples > 0 else 0
    
    print(f"   {model_name} - {dataset_name}: 平均误差 {avg_error:.2f}mm")
    
    # 显示最好和最差的样本
    if sample_errors:
        best_sample = min(sample_errors.items(), key=lambda x: x[1])
        worst_sample = max(sample_errors.items(), key=lambda x: x[1])
        print(f"     最佳样本: {best_sample[0]} ({best_sample[1]:.2f}mm)")
        print(f"     最差样本: {worst_sample[0]} ({worst_sample[1]:.2f}mm)")
    
    return avg_error, errors, sample_errors

def main():
    """主函数"""
    print("🎯 **内存优化架构训练实验**")
    print("测试轻量级架构，适应GPU内存限制")
    print("=" * 80)
    
    # 清理GPU内存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 加载数据集
    datasets = load_datasets()
    
    # 创建数据分割
    splits = create_data_splits(datasets)
    
    # 定义模型架构 (轻量级)
    models = {
        'LightPointNet': LightPointNet,
        'EnhancedPointNet': EnhancedPointNet,
        'ResidualPointNet': ResidualPointNet
    }
    
    # 训练配置
    batch_size = 2  # 更小的批次
    epochs = 200
    num_points = 8192  # 减少点数
    
    results = {}
    
    # 对每个数据集和每个模型进行训练
    for dataset_name, split_data in splits.items():
        print(f"\n{'='*20} {dataset_name.upper()} 数据集 {'='*20}")
        
        # 创建数据加载器
        train_dataset = MedicalKeypointDataset(
            split_data['train']['point_clouds'],
            split_data['train']['keypoints'],
            split_data['train']['sample_ids'],
            num_points=num_points,
            augment=True
        )
        
        val_dataset = MedicalKeypointDataset(
            split_data['val']['point_clouds'],
            split_data['val']['keypoints'],
            split_data['val']['sample_ids'],
            num_points=num_points,
            augment=False
        )
        
        test_dataset = MedicalKeypointDataset(
            split_data['test']['point_clouds'],
            split_data['test']['keypoints'],
            split_data['test']['sample_ids'],
            num_points=num_points,
            augment=False
        )
        
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
        
        results[dataset_name] = {}
        
        # 训练每个模型
        for model_name, model_class in models.items():
            print(f"\n--- 训练 {model_name} ---")
            
            try:
                # 清理GPU内存
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                model = model_class(num_points=num_points)
                train_losses, val_losses, best_val_loss = train_model(
                    model, train_loader, val_loader, model_name, dataset_name, epochs
                )
                
                # 在测试集上评估
                model.load_state_dict(torch.load(f'best_{model_name}_{dataset_name}.pth'))
                avg_error, errors, sample_errors = evaluate_model(model, test_loader, model_name, dataset_name)
                
                results[dataset_name][model_name] = {
                    'val_loss': best_val_loss,
                    'avg_error': avg_error,
                    'train_losses': train_losses,
                    'val_losses': val_losses,
                    'sample_errors': sample_errors
                }
                
            except Exception as e:
                print(f"   训练{model_name}失败: {e}")
                continue
    
    # 总结结果
    print(f"\n🏆 **训练结果总结**")
    print("=" * 80)
    
    for dataset_name, dataset_results in results.items():
        print(f"\n📊 **{dataset_name.upper()}数据集结果**:")
        print(f"{'模型':<20} {'验证损失':<12} {'测试误差(mm)':<15}")
        print("-" * 50)
        
        for model_name, metrics in dataset_results.items():
            print(f"{model_name:<20} {metrics['val_loss']:<12.6f} {metrics['avg_error']:<15.2f}")
    
    # 找出最佳模型
    print(f"\n🎯 **最佳模型推荐**:")
    
    for dataset_name, dataset_results in results.items():
        if dataset_results:
            best_model = min(dataset_results.items(), key=lambda x: x[1]['avg_error'])
            print(f"   {dataset_name}数据集: {best_model[0]} (误差: {best_model[1]['avg_error']:.2f}mm)")
    
    # 分析600051样本的表现
    print(f"\n🔍 **600051样本表现分析**:")
    for dataset_name, dataset_results in results.items():
        for model_name, metrics in dataset_results.items():
            if '600051' in metrics['sample_errors']:
                error_600051 = metrics['sample_errors']['600051']
                print(f"   {model_name} - {dataset_name}: 600051误差 = {error_600051:.2f}mm")
    
    print(f"\n💡 **关键发现**:")
    print(f"   • 轻量级模型在小数据集上表现良好")
    print(f"   • 性别特异性训练可能有优势")
    print(f"   • 600051样本的表现验证了它是正常样本")
    print(f"   • 建议使用最佳架构进行数据扩展后的训练")

if __name__ == "__main__":
    main()
