#!/usr/bin/env python3
"""
分析真实F3 19关键点模型性能
识别哪些关键点识别效果最好/最差
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns
from train_real_f3_19keypoints import RealF3HeatmapPointNet, F3_KEYPOINT_INFO

def extract_keypoints_from_heatmaps_f3(heatmaps, point_cloud):
    """从F3热力图中提取关键点位置"""
    num_keypoints = heatmaps.shape[0]
    keypoints = []
    confidences = []
    
    for kp_idx in range(num_keypoints):
        heatmap = heatmaps[kp_idx]
        
        # 找到最大值位置
        max_idx = np.argmax(heatmap)
        max_confidence = heatmap[max_idx]
        
        # 提取对应的3D坐标
        keypoint_3d = point_cloud[max_idx]
        
        keypoints.append(keypoint_3d)
        confidences.append(max_confidence)
    
    return np.array(keypoints), np.array(confidences)

def analyze_f3_keypoint_performance(model, point_clouds, keypoints, sample_ids, device):
    """分析F3关键点性能"""
    
    print(f"🔍 分析F3关键点性能...")
    
    all_errors = []
    all_confidences = []
    all_sample_results = []
    
    for i in range(len(point_clouds)):
        sample_id = sample_ids[i]
        point_cloud = point_clouds[i]
        true_keypoints = keypoints[i]
        
        # 采样点云
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            pc_sampled = point_cloud[indices]
        else:
            pc_sampled = point_cloud
        
        pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
        
        # 预测
        with torch.no_grad():
            pred_heatmaps = model(pc_tensor)
        
        pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze()
        pred_keypoints, confidences = extract_keypoints_from_heatmaps_f3(pred_heatmaps_np, pc_sampled)
        
        # 计算每个关键点的误差
        sample_errors = []
        for j in range(19):
            error = np.linalg.norm(pred_keypoints[j] - true_keypoints[j])
            sample_errors.append(error)
        
        all_errors.append(sample_errors)
        all_confidences.append(confidences)
        
        all_sample_results.append({
            'sample_id': sample_id,
            'errors': sample_errors,
            'confidences': confidences,
            'avg_error': np.mean(sample_errors),
            'pred_keypoints': pred_keypoints,
            'true_keypoints': true_keypoints,
            'point_cloud': point_cloud
        })
        
        print(f"   样本 {sample_id}: 平均误差 {np.mean(sample_errors):.2f}mm")
    
    # 转换为numpy数组
    all_errors = np.array(all_errors)  # [num_samples, 19]
    all_confidences = np.array(all_confidences)  # [num_samples, 19]
    
    return all_errors, all_confidences, all_sample_results

def identify_best_worst_keypoints(all_errors, all_confidences):
    """识别表现最好和最差的关键点"""
    
    print(f"\n📊 F3关键点性能排名:")
    print("=" * 80)
    
    # 计算每个关键点的统计信息
    keypoint_stats = {}
    
    for kp_idx in range(19):
        errors = all_errors[:, kp_idx]
        confidences = all_confidences[:, kp_idx]
        
        stats = {
            'mean_error': np.mean(errors),
            'std_error': np.std(errors),
            'max_error': np.max(errors),
            'min_error': np.min(errors),
            'median_error': np.median(errors),
            'mean_confidence': np.mean(confidences),
            'std_confidence': np.std(confidences),
            'info': F3_KEYPOINT_INFO[kp_idx]
        }
        
        keypoint_stats[kp_idx] = stats
    
    # 按平均误差排序
    sorted_by_error = sorted(keypoint_stats.items(), key=lambda x: x[1]['mean_error'])
    
    print(f"{'Rank':<4} {'Name':<8} {'Anatomy':<15} {'Error (mm)':<12} {'Confidence':<12} {'Status'}")
    print("-" * 80)
    
    best_keypoints = []
    worst_keypoints = []
    
    for rank, (kp_idx, stats) in enumerate(sorted_by_error):
        info = stats['info']
        
        # 判断表现等级
        if rank < 5:
            status = "🏆 最佳"
            best_keypoints.append(kp_idx)
        elif rank >= 14:
            status = "🚨 最差"
            worst_keypoints.append(kp_idx)
        else:
            status = "✅ 良好"
        
        print(f"{rank+1:<4} {info['name']:<8} {info['anatomy']:<15} "
              f"{stats['mean_error']:.2f}±{stats['std_error']:.2f} "
              f"{stats['mean_confidence']:.3f}±{stats['std_confidence']:.3f} {status}")
    
    return keypoint_stats, best_keypoints, worst_keypoints

def analyze_anatomical_patterns(keypoint_stats, all_errors):
    """分析解剖模式"""
    
    print(f"\n🔍 解剖模式分析:")
    print("=" * 50)
    
    # 按解剖类型分组
    anatomy_groups = {}
    for kp_idx, stats in keypoint_stats.items():
        anatomy_type = stats['info']['anatomy']
        if anatomy_type not in anatomy_groups:
            anatomy_groups[anatomy_type] = []
        anatomy_groups[anatomy_type].extend(all_errors[:, kp_idx])
    
    print(f"按解剖类型分析:")
    for anatomy_type, errors in anatomy_groups.items():
        print(f"   {anatomy_type}: {np.mean(errors):.2f}±{np.std(errors):.2f}mm")
    
    # 按重要性分组
    importance_groups = {}
    for kp_idx, stats in keypoint_stats.items():
        importance = stats['info']['importance']
        if importance not in importance_groups:
            importance_groups[importance] = []
        importance_groups[importance].extend(all_errors[:, kp_idx])
    
    print(f"\n按重要性分析:")
    for importance, errors in importance_groups.items():
        print(f"   {importance}: {np.mean(errors):.2f}±{np.std(errors):.2f}mm")

def create_f3_performance_visualization(keypoint_stats, all_errors, all_confidences, sample_results):
    """创建F3性能可视化"""
    
    fig, axes = plt.subplots(3, 2, figsize=(16, 18))
    
    # 1. 关键点误差排序
    ax1 = axes[0, 0]
    
    kp_indices = list(range(19))
    mean_errors = [keypoint_stats[i]['mean_error'] for i in kp_indices]
    kp_names = [F3_KEYPOINT_INFO[i]['name'] for i in kp_indices]
    anatomy_types = [F3_KEYPOINT_INFO[i]['anatomy'] for i in kp_indices]
    
    # 按误差排序
    sorted_indices = np.argsort(mean_errors)
    sorted_errors = [mean_errors[i] for i in sorted_indices]
    sorted_names = [kp_names[i] for i in sorted_indices]
    sorted_anatomy = [anatomy_types[i] for i in sorted_indices]
    
    # 颜色编码
    color_map = {
        'z_maximum': 'red',
        'z_minimum': 'blue', 
        'left_boundary': 'green',
        'right_boundary': 'orange',
        'anterior_boundary': 'purple',
        'posterior_boundary': 'brown',
        'sacral_region': 'gray'
    }
    colors = [color_map.get(anatomy, 'gray') for anatomy in sorted_anatomy]
    
    bars = ax1.barh(range(19), sorted_errors, color=colors, alpha=0.7)
    ax1.set_yticks(range(19))
    ax1.set_yticklabels(sorted_names, fontsize=8)
    ax1.set_xlabel('Mean Error (mm)')
    ax1.set_title('F3 Keypoint Error Ranking')
    ax1.grid(True, alpha=0.3)
    
    # 2. 解剖类型箱线图
    ax2 = axes[0, 1]
    
    anatomy_types_unique = ['z_maximum', 'z_minimum', 'left_boundary', 'right_boundary', 
                           'anterior_boundary', 'posterior_boundary', 'sacral_region']
    anatomy_data = []
    anatomy_labels = []
    
    for anatomy_type in anatomy_types_unique:
        type_errors = []
        for kp_idx in range(19):
            if F3_KEYPOINT_INFO[kp_idx]['anatomy'] == anatomy_type:
                type_errors.extend(all_errors[:, kp_idx])
        
        if type_errors:
            anatomy_data.append(type_errors)
            count = len([i for i in range(19) if F3_KEYPOINT_INFO[i]['anatomy'] == anatomy_type])
            anatomy_labels.append(f'{anatomy_type}\n({count})')
    
    bp = ax2.boxplot(anatomy_data, labels=anatomy_labels, patch_artist=True)
    
    # 设置颜色
    for patch, anatomy_type in zip(bp['boxes'], anatomy_types_unique[:len(bp['boxes'])]):
        patch.set_facecolor(color_map.get(anatomy_type, 'gray'))
        patch.set_alpha(0.7)
    
    ax2.set_ylabel('Error (mm)')
    ax2.set_title('Error by Anatomy Type')
    ax2.tick_params(axis='x', rotation=45)
    ax2.grid(True, alpha=0.3)
    
    # 3. 置信度 vs 误差散点图
    ax3 = axes[1, 0]
    
    for kp_idx in range(19):
        errors = all_errors[:, kp_idx]
        confidences = all_confidences[:, kp_idx]
        anatomy = F3_KEYPOINT_INFO[kp_idx]['anatomy']
        color = color_map.get(anatomy, 'gray')
        
        ax3.scatter(confidences, errors, c=color, alpha=0.6, s=30, 
                   label=anatomy if kp_idx == 0 or anatomy not in [F3_KEYPOINT_INFO[j]['anatomy'] for j in range(kp_idx)] else "")
    
    ax3.set_xlabel('Confidence')
    ax3.set_ylabel('Error (mm)')
    ax3.set_title('Confidence vs Error')
    ax3.grid(True, alpha=0.3)
    ax3.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    
    # 4. 重要性分析
    ax4 = axes[1, 1]
    
    importance_levels = ['critical', 'high', 'medium']
    importance_data = []
    importance_labels = []
    
    for importance in importance_levels:
        imp_errors = []
        for kp_idx in range(19):
            if F3_KEYPOINT_INFO[kp_idx]['importance'] == importance:
                imp_errors.extend(all_errors[:, kp_idx])
        
        if imp_errors:
            importance_data.append(imp_errors)
            count = len([i for i in range(19) if F3_KEYPOINT_INFO[i]['importance'] == importance])
            importance_labels.append(f'{importance}\n({count})')
    
    ax4.boxplot(importance_data, labels=importance_labels)
    ax4.set_ylabel('Error (mm)')
    ax4.set_title('Error by Importance Level')
    ax4.grid(True, alpha=0.3)
    
    # 5. 最佳和最差关键点对比
    ax5 = axes[2, 0]
    
    # 找出最佳和最差的各3个关键点
    sorted_kp_by_error = sorted(range(19), key=lambda x: keypoint_stats[x]['mean_error'])
    best_3 = sorted_kp_by_error[:3]
    worst_3 = sorted_kp_by_error[-3:]
    
    best_names = [F3_KEYPOINT_INFO[i]['name'] for i in best_3]
    best_errors = [keypoint_stats[i]['mean_error'] for i in best_3]
    worst_names = [F3_KEYPOINT_INFO[i]['name'] for i in worst_3]
    worst_errors = [keypoint_stats[i]['mean_error'] for i in worst_3]
    
    x_pos = np.arange(3)
    width = 0.35
    
    ax5.bar(x_pos - width/2, best_errors, width, label='Best 3', color='green', alpha=0.7)
    ax5.bar(x_pos + width/2, worst_errors, width, label='Worst 3', color='red', alpha=0.7)
    
    ax5.set_xlabel('Keypoint Rank')
    ax5.set_ylabel('Mean Error (mm)')
    ax5.set_title('Best vs Worst Keypoints')
    ax5.set_xticks(x_pos)
    ax5.set_xticklabels(['1st', '2nd', '3rd'])
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, (best_err, worst_err) in enumerate(zip(best_errors, worst_errors)):
        ax5.text(i - width/2, best_err + 0.1, f'{best_err:.1f}mm\n{best_names[i]}', 
                ha='center', va='bottom', fontsize=8)
        ax5.text(i + width/2, worst_err + 0.1, f'{worst_err:.1f}mm\n{worst_names[i]}', 
                ha='center', va='bottom', fontsize=8)
    
    # 6. 样本间变异性
    ax6 = axes[2, 1]
    
    sample_avg_errors = [result['avg_error'] for result in sample_results]
    sample_ids = [result['sample_id'] for result in sample_results]
    
    bars = ax6.bar(range(len(sample_avg_errors)), sample_avg_errors, alpha=0.7)
    ax6.set_xlabel('Sample Index')
    ax6.set_ylabel('Average Error (mm)')
    ax6.set_title('Error Across Samples')
    ax6.set_xticks(range(len(sample_ids)))
    ax6.set_xticklabels([sid[-3:] for sid in sample_ids], rotation=45)
    ax6.grid(True, alpha=0.3)
    
    # 添加平均线
    overall_avg = np.mean(sample_avg_errors)
    ax6.axhline(y=overall_avg, color='red', linestyle='--', alpha=0.7, 
               label=f'Overall Avg: {overall_avg:.1f}mm')
    ax6.legend()
    
    plt.suptitle('F3 19-Keypoint Performance Analysis\nReal Medical Annotations', 
                fontsize=16, fontweight='bold')
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    
    filename = 'f3_19kp_performance_analysis.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 F3性能分析保存: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🔍 真实F3 19关键点性能分析")
    print("识别表现最好和最差的关键点")
    print("=" * 60)
    
    # 加载数据和模型
    data = np.load('f3_19kp_real.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    sample_ids = data['sample_ids']
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = RealF3HeatmapPointNet(input_dim=3, num_keypoints=19).to(device)
    
    try:
        model.load_state_dict(torch.load('best_real_f3_19kp_model.pth', map_location=device))
        model.eval()
        print("✅ 真实F3模型加载成功")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 分析关键点性能
    all_errors, all_confidences, sample_results = analyze_f3_keypoint_performance(
        model, point_clouds, keypoints, sample_ids, device
    )
    
    # 识别最佳和最差关键点
    keypoint_stats, best_keypoints, worst_keypoints = identify_best_worst_keypoints(
        all_errors, all_confidences
    )
    
    # 分析解剖模式
    analyze_anatomical_patterns(keypoint_stats, all_errors)
    
    # 创建可视化
    create_f3_performance_visualization(keypoint_stats, all_errors, all_confidences, sample_results)
    
    # 总结发现
    print(f"\n🎯 关键发现:")
    print("=" * 50)
    
    overall_avg = np.mean(all_errors)
    print(f"整体平均误差: {overall_avg:.2f}mm")
    
    print(f"\n🏆 表现最佳的关键点:")
    for kp_idx in best_keypoints:
        info = F3_KEYPOINT_INFO[kp_idx]
        stats = keypoint_stats[kp_idx]
        print(f"   {info['name']} ({info['anatomy']}): {stats['mean_error']:.2f}mm")
    
    print(f"\n🚨 表现最差的关键点:")
    for kp_idx in worst_keypoints:
        info = F3_KEYPOINT_INFO[kp_idx]
        stats = keypoint_stats[kp_idx]
        print(f"   {info['name']} ({info['anatomy']}): {stats['mean_error']:.2f}mm")
    
    print(f"\n💡 改进建议:")
    print("1. 重点优化表现最差的关键点")
    print("2. 分析最佳关键点的成功因素")
    print("3. 考虑解剖特征对性能的影响")
    print("4. 针对特定解剖类型优化模型")

if __name__ == "__main__":
    main()
