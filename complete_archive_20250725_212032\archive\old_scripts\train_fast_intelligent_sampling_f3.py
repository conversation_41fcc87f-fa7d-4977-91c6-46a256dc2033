#!/usr/bin/env python3
"""
Fast Intelligent Sampling Strategy for F3 Keypoint Detection

简化版智能采样，避免复杂计算导致的训练卡顿
基于7.631mm成功基线，通过快速智能采样策略充分利用50K高质量点云
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import time
import json
import gc
import random

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class FastIntelligentSampler:
    """快速智能采样器 - 简化版本，避免复杂计算"""
    
    def __init__(self, target_points=4096):
        self.target_points = target_points
        
    def fast_sample_near_keypoints(self, points, keypoints, num_samples):
        """快速关键点附近采样"""
        if num_samples <= 0:
            return np.array([], dtype=int)
        
        # 简化距离计算 - 只计算到质心的距离
        keypoint_center = np.mean(keypoints, axis=0)
        distances_to_center = np.linalg.norm(points - keypoint_center, axis=1)
        
        # 距离越近，权重越高
        weights = 1.0 / (distances_to_center + 0.1)
        weights = weights / np.sum(weights)
        
        # 加权采样
        selected_indices = np.random.choice(
            len(points), 
            size=min(num_samples, len(points)), 
            replace=False, 
            p=weights
        )
        
        return selected_indices
    
    def fast_sample_surface_points(self, points, num_samples):
        """快速表面点采样 - 基于坐标方差"""
        if num_samples <= 0:
            return np.array([], dtype=int)
        
        # 计算每个点的局部方差（简化的表面特征）
        # 使用滑动窗口计算局部方差
        n_neighbors = min(50, len(points) // 10)
        
        variances = []
        for i in range(len(points)):
            # 简单的邻域选择 - 基于索引距离
            start_idx = max(0, i - n_neighbors // 2)
            end_idx = min(len(points), i + n_neighbors // 2)
            neighbors = points[start_idx:end_idx]
            
            # 计算局部方差
            if len(neighbors) > 1:
                variance = np.var(neighbors, axis=0).sum()
            else:
                variance = 0.0
            variances.append(variance)
        
        variances = np.array(variances)
        
        # 方差越大，被选中概率越高
        weights = variances + 1e-8
        weights = weights / np.sum(weights)
        
        selected_indices = np.random.choice(
            len(points), 
            size=min(num_samples, len(points)), 
            replace=False, 
            p=weights
        )
        
        return selected_indices
    
    def fast_uniform_sample(self, points, num_samples):
        """快速均匀采样 - 简化的网格采样"""
        if num_samples <= 0:
            return np.array([], dtype=int)
        
        if num_samples >= len(points):
            return np.arange(len(points))
        
        # 简单的均匀间隔采样
        step = len(points) // num_samples
        indices = np.arange(0, len(points), step)[:num_samples]
        
        # 如果不够，随机补充
        if len(indices) < num_samples:
            remaining = num_samples - len(indices)
            all_indices = set(range(len(points)))
            used_indices = set(indices)
            available_indices = list(all_indices - used_indices)
            
            if available_indices:
                additional = np.random.choice(
                    available_indices, 
                    size=min(remaining, len(available_indices)), 
                    replace=False
                )
                indices = np.concatenate([indices, additional])
        
        return indices
    
    def fast_intelligent_sample(self, points, keypoints):
        """快速智能采样主函数"""
        total_points = len(points)
        
        if total_points <= self.target_points:
            return np.arange(total_points)
        
        # 分配采样数量
        keypoint_samples = int(0.4 * self.target_points)  # 40% 关键点附近
        surface_samples = int(0.3 * self.target_points)   # 30% 表面特征点
        uniform_samples = self.target_points - keypoint_samples - surface_samples  # 30% 均匀分布
        
        print(f"🔍 采样分配: 关键点{keypoint_samples}, 表面{surface_samples}, 均匀{uniform_samples}")
        
        # 执行快速分层采样
        keypoint_indices = self.fast_sample_near_keypoints(points, keypoints, keypoint_samples)
        
        # 排除已选择的点
        remaining_mask = np.ones(total_points, dtype=bool)
        remaining_mask[keypoint_indices] = False
        remaining_points = points[remaining_mask]
        remaining_original_indices = np.where(remaining_mask)[0]
        
        if len(remaining_points) > 0:
            surface_indices_local = self.fast_sample_surface_points(remaining_points, surface_samples)
            surface_indices = remaining_original_indices[surface_indices_local]
            
            # 再次排除已选择的点
            remaining_mask[surface_indices] = False
            remaining_points = points[remaining_mask]
            remaining_original_indices = np.where(remaining_mask)[0]
            
            if len(remaining_points) > 0:
                uniform_indices_local = self.fast_uniform_sample(remaining_points, uniform_samples)
                uniform_indices = remaining_original_indices[uniform_indices_local]
            else:
                uniform_indices = np.array([], dtype=int)
        else:
            surface_indices = np.array([], dtype=int)
            uniform_indices = np.array([], dtype=int)
        
        # 合并所有采样结果
        all_indices = np.concatenate([keypoint_indices, surface_indices, uniform_indices])
        
        # 确保数量正确
        if len(all_indices) < self.target_points:
            remaining_mask = np.ones(total_points, dtype=bool)
            remaining_mask[all_indices] = False
            remaining_indices = np.where(remaining_mask)[0]
            
            if len(remaining_indices) > 0:
                additional_needed = self.target_points - len(all_indices)
                additional_indices = np.random.choice(
                    remaining_indices, 
                    size=min(additional_needed, len(remaining_indices)), 
                    replace=False
                )
                all_indices = np.concatenate([all_indices, additional_indices])
        
        return all_indices[:self.target_points]

class FastIntelligentF3Dataset(Dataset):
    """使用快速智能采样的F3数据集"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points
        self.augment = augment
        self.split = split
        
        # 初始化快速智能采样器
        self.sampler = FastIntelligentSampler(target_points=num_points)
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        print(f"📊 原始数据: {len(sample_ids)} 样本")
        print(f"📊 点云密度: {len(point_clouds[0])} 点 (50K高质量)")
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
        
        print(f"   {split}: {len(self.sample_ids)} 样本")
    
    def __len__(self):
        return len(self.sample_ids)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()  # 50K点云
        keypoints = self.keypoints[idx].copy()
        
        # 快速智能采样到目标点数
        if len(point_cloud) > self.num_points:
            selected_indices = self.sampler.fast_intelligent_sample(point_cloud, keypoints)
            point_cloud = point_cloud[selected_indices]
        
        # 保守的数据增强
        if self.augment and self.split == 'train':
            # 轻微旋转
            if np.random.random() < 0.7:
                angle = np.random.uniform(-0.08, 0.08)  # ±4.6度
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                point_cloud = point_cloud @ rotation.T
                keypoints = keypoints @ rotation.T
            
            # 小幅平移
            if np.random.random() < 0.6:
                translation = np.random.uniform(-0.4, 0.4, 3)  # ±0.4mm
                point_cloud += translation
                keypoints += translation
            
            # 轻微缩放
            if np.random.random() < 0.5:
                scale = np.random.uniform(0.99, 1.01, 3)  # ±1%
                point_cloud *= scale
                keypoints *= scale
            
            # 轻微噪声
            if np.random.random() < 0.6:
                noise_level = np.random.choice([0.02, 0.03, 0.04])
                noise = np.random.normal(0, noise_level, point_cloud.shape)
                point_cloud += noise
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

# 使用之前成功的保守PointNet架构
class ConservativePointNet(nn.Module):
    """保守优化PointNet - 保持成功架构不变"""
    
    def __init__(self, num_keypoints: int = 19):
        super(ConservativePointNet, self).__init__()
        
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        x1 = torch.relu(self.bn1(self.conv1(x)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        
        x3_res = x3 + self.residual1(x1)
        
        x4 = torch.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = torch.relu(self.bn5(self.conv5(x4_res)))
        
        global_feat = torch.max(x5, 2)[0]
        
        x = torch.relu(self.bn_fc1(self.fc1(global_feat)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc2(self.fc2(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc3(self.fc3(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc4(self.fc4(x)))
        x = self.dropout(x)
        x = self.fc5(x)
        
        return x.view(batch_size, 19, 3)

class ImprovedLoss(nn.Module):
    """改进损失函数"""
    
    def __init__(self, alpha=0.8, beta=0.2):
        super(ImprovedLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, pred, target):
        mse_loss = F.mse_loss(pred, target)
        smooth_l1_loss = F.smooth_l1_loss(pred, target)
        total_loss = self.alpha * mse_loss + self.beta * smooth_l1_loss
        return total_loss

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def train_fast_intelligent_sampling():
    """训练快速智能采样模型"""

    print("🚀 **快速智能采样训练 - F3关键点检测**")
    print("⚡ **优化: 简化采样算法，避免复杂计算导致的卡顿**")
    print("🎯 **策略: 关键点导向(40%) + 表面特征(30%) + 均匀分布(30%)**")
    print("📊 **基线: 7.631mm → 目标: <5mm**")
    print("=" * 80)

    set_seed(42)

    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")

    if torch.cuda.is_available():
        torch.cuda.empty_cache()

    # 数据集
    dataset_path = "high_quality_f3_dataset.npz"
    test_samples = ['600114', '600115', '600116', '600117', '600118',
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']

    print(f"🔍 **快速智能采样策略**")

    train_dataset = FastIntelligentF3Dataset(dataset_path, 'train', num_points=4096,
                                           test_samples=test_samples, augment=True, seed=42)
    val_dataset = FastIntelligentF3Dataset(dataset_path, 'val', num_points=4096,
                                         test_samples=test_samples, augment=False, seed=42)
    test_dataset = FastIntelligentF3Dataset(dataset_path, 'test', num_points=4096,
                                          test_samples=test_samples, augment=False, seed=42)

    batch_size = 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)

    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")

    # 模型
    model = ConservativePointNet(num_keypoints=19).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 快速智能采样PointNet参数: {total_params:,}")

    # 训练配置
    criterion = ImprovedLoss(alpha=0.8, beta=0.2)
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)

    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer, max_lr=0.0016, epochs=80, steps_per_epoch=len(train_loader),
        pct_start=0.1, anneal_strategy='cos', div_factor=20, final_div_factor=100
    )

    num_epochs = 80  # 减少训练轮数，快速验证效果
    best_val_error = float('inf')
    patience = 15
    patience_counter = 0
    history = []

    print(f"🎯 开始快速智能采样训练")
    start_time = time.time()

    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 40)

        # 训练
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_5mm_percent': 0}

        for batch_idx, batch in enumerate(train_loader):
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)

            optimizer.zero_grad()

            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)

                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                scheduler.step()

                train_loss += loss.item()

                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]

                # 打印进度
                if batch_idx == 0:
                    print(f"   批次 {batch_idx+1}/{len(train_loader)} 完成")

            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue

        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)

        # 验证
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0}

        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)

                try:
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()

                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]

                except RuntimeError as e:
                    continue

        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)

        # 打印结果
        current_lr = optimizer.param_groups[0]['lr']
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"5mm={train_metrics['within_5mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"5mm={val_metrics['within_5mm_percent']:.1f}%")
        print(f"学习率: {current_lr:.2e}")

        # 保存历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'learning_rate': current_lr
        })

        # 检查改进
        current_error = val_metrics['mean_distance']
        if current_error < best_val_error:
            best_val_error = current_error
            patience_counter = 0

            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics
            }, 'best_fast_intelligent_sampling_f3.pth')

            print(f"🎉 新最佳! 验证误差: {best_val_error:.3f}mm")

            if best_val_error <= 5.0:
                print(f"🏆 **突破5mm目标!**")
            elif best_val_error < 7.631:
                print(f"✅ **优于基线!** 超越7.631mm")
        else:
            patience_counter += 1
            print(f"⏳ 无改善 ({patience_counter}/{patience})")

        if patience_counter >= patience:
            print("🛑 早停")
            break

        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

    total_time = time.time() - start_time

    return model, test_loader, best_val_error, total_time, history

def main():
    """主函数"""

    try:
        # 训练快速智能采样模型
        model, test_loader, best_val_error, training_time, history = train_fast_intelligent_sampling()

        print(f"\n🎯 **快速智能采样训练完成!**")
        print(f"   最佳验证误差: {best_val_error:.3f}mm")
        print(f"   训练时间: {training_time/60:.1f}分钟")

        # 简化测试评估
        device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')

        checkpoint = torch.load('best_fast_intelligent_sampling_f3.pth')
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()

        all_distances = []

        with torch.no_grad():
            for batch in test_loader:
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)

                try:
                    pred_keypoints = model(point_cloud)

                    for i in range(len(batch['sample_id'])):
                        pred_single = pred_keypoints[i:i+1]
                        target_single = keypoints[i:i+1]

                        metrics = calculate_metrics(pred_single, target_single)
                        all_distances.append(metrics['mean_distance'])

                except RuntimeError as e:
                    continue

        all_distances = np.array(all_distances)
        test_error = np.mean(all_distances)

        print(f"\n📊 **快速智能采样测试结果**")
        print(f"   测试误差: {test_error:.3f}±{np.std(all_distances):.3f}mm")
        print(f"   最小误差: {np.min(all_distances):.3f}mm")
        print(f"   最大误差: {np.max(all_distances):.3f}mm")

        # 与基线对比
        conservative_error = 7.631
        improvement = (conservative_error - test_error) / conservative_error * 100

        print(f"\n📈 **性能对比**")
        print(f"   保守基线: {conservative_error:.3f}mm")
        print(f"   快速智能采样: {test_error:.3f}mm")
        print(f"   改进幅度: {improvement:.1f}%")

        if test_error <= 5.0:
            print(f"\n🏆 **突破5mm目标!** 快速智能采样成功!")
        elif test_error < conservative_error:
            print(f"\n✅ **采样策略有效!** 优于保守基线")
        else:
            print(f"\n💡 **需要调整**: 采样策略需要优化")

        # 保存结果
        results = {
            'model_name': 'Fast_Intelligent_Sampling_PointNet_F3',
            'test_error_mm': float(test_error),
            'best_validation_error_mm': float(best_val_error),
            'training_time_minutes': float(training_time / 60),
            'improvement_vs_conservative_percent': float(improvement),
            'all_distances': all_distances.tolist()
        }

        with open('fast_intelligent_sampling_f3_results.json', 'w') as f:
            json.dump(results, f, indent=2)

        print(f"\n💾 **结果已保存**: fast_intelligent_sampling_f3_results.json")

        return results

    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    set_seed(42)

    print("🚀 **开始快速智能采样PointNet训练**")
    print("⚡ **核心改进**: 简化采样算法，避免复杂计算")
    print("📊 **采样策略**: 关键点导向(40%) + 表面特征(30%) + 均匀分布(30%)")
    print("🎯 **目标**: 验证智能采样效果，突破7.631mm基线")

    results = main()
