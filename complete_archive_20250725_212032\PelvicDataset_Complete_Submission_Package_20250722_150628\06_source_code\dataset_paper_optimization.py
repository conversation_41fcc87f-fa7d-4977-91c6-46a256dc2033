#!/usr/bin/env python3
"""
数据集论文优化：快速修复女性模型 + 建立可靠基准
Dataset Paper Optimization: Quick Fix for Female Model + Establish Reliable Baseline
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import os
from tqdm import tqdm

class SimpleHeatmapRegressionNet(nn.Module):
    """简化的Heatmap回归网络 (专为小数据集优化)"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super(SimpleHeatmapRegressionNet, self).__init__()
        
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 简化的点云特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        
        # 全局特征
        self.global_conv = nn.Conv1d(256, 512, 1)
        
        # 特征融合 (简化)
        self.fusion_conv1 = nn.Conv1d(512 + 128, 256, 1)
        self.fusion_conv2 = nn.Conv1d(256, 128, 1)
        
        # Heatmap生成
        self.heatmap_conv1 = nn.Conv1d(128, 64, 1)
        self.heatmap_conv2 = nn.Conv1d(64, num_keypoints, 1)
        
        # 激活函数和正则化
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.2)  # 减少dropout
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        # 简化的特征提取
        x1 = self.relu(self.bn1(self.conv1(x)))
        x2 = self.relu(self.bn2(self.conv2(x1)))
        x3 = self.relu(self.bn3(self.conv3(x2)))
        
        # 全局特征
        global_feat = self.relu(self.global_conv(x3))
        global_feat = torch.max(global_feat, 2, keepdim=True)[0]
        
        # 扩展全局特征
        global_feat_expanded = global_feat.repeat(1, 1, self.num_points)
        
        # 融合局部和全局特征 (使用x2而不是x3)
        combined_feat = torch.cat([x2, global_feat_expanded], 1)
        
        # 特征融合
        fused = self.relu(self.fusion_conv1(combined_feat))
        fused = self.dropout(fused)
        fused = self.relu(self.fusion_conv2(fused))
        
        # 生成热图
        heatmap = self.relu(self.heatmap_conv1(fused))
        heatmap = self.heatmap_conv2(heatmap)
        
        # 转置并应用softmax
        heatmap = heatmap.transpose(2, 1)
        
        # 对每个关键点的热图进行softmax
        heatmap_list = []
        for i in range(self.num_keypoints):
            hm_i = torch.softmax(heatmap[:, :, i], dim=1)
            heatmap_list.append(hm_i.unsqueeze(2))
        
        final_heatmap = torch.cat(heatmap_list, dim=2)
        
        return final_heatmap

def load_clean_data():
    """加载清洁数据"""
    
    print("📊 加载清洁数据...")
    
    # 加载女性原始数据
    female_original_path = "archive/old_experiments/f3_reduced_12kp_female.npz"
    if os.path.exists(female_original_path):
        female_data = np.load(female_original_path, allow_pickle=True)
        female_pc = female_data['point_clouds']
        female_kp = female_data['keypoints']
        print(f"✅ 女性原始数据: {len(female_pc)}个样本")
    else:
        print(f"❌ 女性原始数据不存在")
        return None
    
    # 加载男性原始数据
    male_original_path = "archive/old_experiments/f3_reduced_12kp_male.npz"
    if os.path.exists(male_original_path):
        male_data = np.load(male_original_path, allow_pickle=True)
        male_pc = male_data['point_clouds']
        male_kp = male_data['keypoints']
        print(f"✅ 男性原始数据: {len(male_pc)}个样本")
    else:
        print(f"❌ 男性原始数据不存在")
        return None
    
    return female_pc, female_kp, male_pc, male_kp

def analyze_data_quality(pc_data, kp_data, gender_name):
    """分析数据质量"""
    
    quality_scores = []
    
    for i in range(len(pc_data)):
        pc = pc_data[i]
        kp = kp_data[i]
        
        # 计算质量指标
        quality_metrics = {}
        
        # 1. 点云密度一致性
        pc_std = np.std(np.linalg.norm(pc, axis=1))
        quality_metrics['pc_density'] = 1.0 / (1.0 + pc_std)
        
        # 2. 关键点到点云的距离
        distances = []
        for kp_point in kp:
            dist_to_pc = np.min(np.linalg.norm(pc - kp_point, axis=1))
            distances.append(dist_to_pc)
        avg_distance = np.mean(distances)
        quality_metrics['kp_to_pc_distance'] = 1.0 / (1.0 + avg_distance)
        
        # 3. 关键点分布合理性
        kp_center = np.mean(kp, axis=0)
        kp_spread = np.std(np.linalg.norm(kp - kp_center, axis=1))
        quality_metrics['kp_spread'] = 1.0 / (1.0 + abs(kp_spread - 50))
        
        # 4. 点云完整性
        pc_center = np.mean(pc, axis=0)
        pc_coverage = np.std(np.linalg.norm(pc - pc_center, axis=1))
        quality_metrics['pc_coverage'] = min(1.0, pc_coverage / 100)
        
        # 综合质量分数
        overall_quality = np.mean(list(quality_metrics.values()))
        quality_scores.append({
            'index': i,
            'overall_quality': overall_quality,
            'metrics': quality_metrics,
            'avg_kp_distance': avg_distance
        })
    
    # 排序
    quality_scores.sort(key=lambda x: x['overall_quality'])
    
    return quality_scores

def remove_outliers(pc_data, kp_data, quality_scores, removal_ratio=0.05):
    """移除异常样本 (减少移除比例)"""
    
    n_samples = len(pc_data)
    n_remove = int(n_samples * removal_ratio)
    
    # 获取要移除的样本索引
    outlier_indices = set([score['index'] for score in quality_scores[:n_remove]])
    
    # 创建清洁数据
    clean_pc = []
    clean_kp = []
    
    for i in range(n_samples):
        if i not in outlier_indices:
            clean_pc.append(pc_data[i])
            clean_kp.append(kp_data[i])
    
    clean_pc = np.array(clean_pc)
    clean_kp = np.array(clean_kp)
    
    return clean_pc, clean_kp, outlier_indices

def split_clean_data(pc, kp, gender_name):
    """分割清洁数据"""
    
    n_samples = len(pc)
    n_train = int(n_samples * 0.7)
    n_val = int(n_samples * 0.15)
    n_test = n_samples - n_train - n_val
    
    # 随机打乱
    indices = np.random.permutation(n_samples)
    
    train_indices = indices[:n_train]
    val_indices = indices[n_train:n_train + n_val]
    test_indices = indices[n_train + n_val:]
    
    # 分割数据
    train_pc = pc[train_indices]
    train_kp = kp[train_indices]
    
    val_pc = pc[val_indices]
    val_kp = kp[val_indices]
    
    test_pc = pc[test_indices]
    test_kp = kp[test_indices]
    
    return (train_pc, train_kp), (val_pc, val_kp), (test_pc, test_kp)

def augment_conservative_training_data(train_pc, train_kp, target_size=None):
    """保守的训练数据增强"""
    
    if target_size is None:
        target_size = len(train_pc) * 8  # 减少增强倍数
    
    augmented_pc = []
    augmented_kp = []
    
    # 保留原始数据
    for i in range(len(train_pc)):
        augmented_pc.append(train_pc[i])
        augmented_kp.append(train_kp[i])
    
    # 生成增强数据
    n_augment_needed = target_size - len(train_pc)
    
    for i in tqdm(range(n_augment_needed), desc="保守增强数据"):
        # 随机选择一个原始样本
        idx = np.random.randint(0, len(train_pc))
        original_pc = train_pc[idx].copy()
        original_kp = train_kp[idx].copy()
        
        # 随机选择增强方法 (更保守的参数)
        aug_type = np.random.choice(['rotation', 'noise', 'scale'])
        
        if aug_type == 'rotation':
            # 3D旋转增强 (减小角度)
            angles = np.random.uniform(-5, 5, 3)
            aug_pc, aug_kp = apply_3d_rotation(original_pc, original_kp, angles)
        elif aug_type == 'noise':
            # 噪声增强 (减小噪声)
            noise_pc = np.random.normal(0, 0.5, original_pc.shape)
            noise_kp = np.random.normal(0, 0.8, original_kp.shape)
            aug_pc = original_pc + noise_pc
            aug_kp = original_kp + noise_kp
        else:  # scale
            # 尺度增强 (减小变化)
            scale_factor = np.random.uniform(0.98, 1.02)
            aug_pc = original_pc * scale_factor
            aug_kp = original_kp * scale_factor
        
        augmented_pc.append(aug_pc)
        augmented_kp.append(aug_kp)
    
    augmented_pc = np.array(augmented_pc)
    augmented_kp = np.array(augmented_kp)
    
    return augmented_pc, augmented_kp

def apply_3d_rotation(point_cloud, keypoints, angles_deg):
    """应用3D旋转"""
    
    angles = np.radians(angles_deg)
    
    # 旋转矩阵
    cos_x, sin_x = np.cos(angles[0]), np.sin(angles[0])
    cos_y, sin_y = np.cos(angles[1]), np.sin(angles[1])
    cos_z, sin_z = np.cos(angles[2]), np.sin(angles[2])
    
    # X轴旋转
    Rx = np.array([[1, 0, 0],
                   [0, cos_x, -sin_x],
                   [0, sin_x, cos_x]])
    
    # Y轴旋转
    Ry = np.array([[cos_y, 0, sin_y],
                   [0, 1, 0],
                   [-sin_y, 0, cos_y]])
    
    # Z轴旋转
    Rz = np.array([[cos_z, -sin_z, 0],
                   [sin_z, cos_z, 0],
                   [0, 0, 1]])
    
    # 组合旋转
    R = Rz @ Ry @ Rx
    
    # 应用旋转
    rotated_pc = point_cloud @ R.T
    rotated_kp = keypoints @ R.T
    
    return rotated_pc, rotated_kp

def generate_heatmap_from_keypoints(keypoints, point_cloud, sigma=5.0):
    """从关键点生成热图"""
    heatmaps = []
    
    for kp in keypoints:
        distances = np.linalg.norm(point_cloud - kp, axis=1)
        heatmap = np.exp(-distances**2 / (2 * sigma**2))
        
        if np.sum(heatmap) > 0:
            heatmap = heatmap / np.sum(heatmap)
        
        heatmaps.append(heatmap)
    
    return np.array(heatmaps)

def heatmap_loss_function(pred_heatmap, target_heatmap):
    """热图损失函数"""
    
    # 检查并调整target_heatmap的维度
    if len(target_heatmap.shape) == 3 and target_heatmap.shape[1] == 12:
        target_heatmap = target_heatmap.transpose(1, 2)
    
    # KL散度损失
    kl_loss = nn.KLDivLoss(reduction='batchmean')
    
    total_loss = 0
    batch_size, num_points, num_keypoints = pred_heatmap.shape
    
    for i in range(num_keypoints):
        log_pred = torch.log(pred_heatmap[:, :, i] + 1e-8)
        loss_i = kl_loss(log_pred, target_heatmap[:, :, i])
        total_loss += loss_i
    
    return total_loss / num_keypoints

def extract_keypoints_from_heatmap(heatmap, point_cloud):
    """从热图中提取关键点坐标"""
    
    batch_size, num_points, num_keypoints = heatmap.shape
    keypoints = torch.zeros(batch_size, num_keypoints, 3)
    
    for b in range(batch_size):
        for k in range(num_keypoints):
            weights = heatmap[b, :, k]
            weighted_coords = point_cloud[b] * weights.unsqueeze(1)
            keypoint = torch.sum(weighted_coords, dim=0) / torch.sum(weights)
            keypoints[b, k] = keypoint
    
    return keypoints

def train_dataset_paper_model(train_pc, train_kp, val_pc, val_kp, gender_name, model_type="simple"):
    """训练数据集论文模型"""
    
    print(f"\n🚀 训练{gender_name}数据集论文模型 ({model_type})")
    print("=" * 60)
    
    # 生成训练数据的热图
    print("🔥 生成训练数据热图...")
    train_hm = []
    for i in tqdm(range(len(train_pc)), desc="训练热图"):
        hm = generate_heatmap_from_keypoints(train_kp[i], train_pc[i])
        train_hm.append(hm)
    train_hm = np.array(train_hm)
    
    # 生成验证数据的热图
    print("🔥 生成验证数据热图...")
    val_hm = []
    for i in tqdm(range(len(val_pc)), desc="验证热图"):
        hm = generate_heatmap_from_keypoints(val_kp[i], val_pc[i])
        val_hm.append(hm)
    val_hm = np.array(val_hm)
    
    # 设备设置
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    
    # 模型初始化
    torch.manual_seed(42)
    if model_type == "simple":
        model = SimpleHeatmapRegressionNet(num_points=50000, num_keypoints=12)
    else:
        # 使用第三阶段的复杂模型 (男性数据)
        from stage3_attention_multiscale import EnhancedHeatmapRegressionNet
        model = EnhancedHeatmapRegressionNet(num_points=50000, num_keypoints=12)
    
    model = model.to(device)
    
    print(f"🏗️ {gender_name}数据集论文模型:")
    print(f"   模型类型: {model_type}")
    print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"   训练样本: {len(train_pc)}")
    print(f"   验证样本: {len(val_pc)}")
    
    # 优化器设置
    if model_type == "simple":
        lr = 0.0003
        batch_size = 4
        num_epochs = 80
    else:
        lr = 0.00025
        batch_size = 3
        num_epochs = 60
    
    optimizer = optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs//2, eta_min=1e-6)
    
    # 训练参数
    best_val_error = float('inf')
    patience = 25
    patience_counter = 0
    
    print(f"🎯 训练参数:")
    print(f"   训练轮数: {num_epochs}")
    print(f"   批次大小: {batch_size}")
    print(f"   学习率: {lr}")
    print(f"   早停耐心: {patience}")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        n_train_batches = len(train_pc) // batch_size
        
        for i in range(n_train_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(train_pc))
            
            batch_pc = torch.FloatTensor(train_pc[start_idx:end_idx]).to(device)
            batch_hm = torch.FloatTensor(train_hm[start_idx:end_idx]).to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            pred_hm = model(batch_pc)
            loss = heatmap_loss_function(pred_hm, batch_hm)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        avg_train_loss = train_loss / n_train_batches
        
        # 验证阶段
        model.eval()
        val_errors = []
        
        with torch.no_grad():
            n_val_batches = len(val_pc) // batch_size + (1 if len(val_pc) % batch_size > 0 else 0)
            
            for i in range(n_val_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(val_pc))
                
                batch_pc = torch.FloatTensor(val_pc[start_idx:end_idx]).to(device)
                batch_kp = torch.FloatTensor(val_kp[start_idx:end_idx]).to(device)
                
                pred_hm = model(batch_pc)
                pred_kp = extract_keypoints_from_heatmap(pred_hm.cpu(), batch_pc.cpu())
                
                for j in range(len(batch_kp)):
                    error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j].cpu(), dim=1))
                    val_errors.append(error.item())
        
        avg_val_error = np.mean(val_errors)
        
        # 学习率调度
        scheduler.step()
        
        if epoch % 10 == 0:
            print(f"Epoch {epoch+1}/{num_epochs}: Loss={avg_train_loss:.4f}, Val={avg_val_error:.2f}mm")
        
        # 早停和模型保存
        if avg_val_error < best_val_error:
            best_val_error = avg_val_error
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'val_error': avg_val_error,
                'model_type': model_type
            }, f'dataset_paper_{gender_name}_{model_type}.pth')
        else:
            patience_counter += 1
            if patience_counter >= patience:
                break
    
    print(f"✅ {gender_name}数据集论文模型训练完成，最佳验证误差: {best_val_error:.2f}mm")
    
    return model, best_val_error

def test_dataset_paper_model(model, test_pc, test_kp, gender_name, model_type):
    """测试数据集论文模型"""
    
    print(f"\n🧪 测试{gender_name}数据集论文模型 ({model_type})")
    print("=" * 60)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    model.eval()
    
    test_errors = []
    batch_size = 4 if model_type == "simple" else 3
    
    with torch.no_grad():
        n_test_batches = len(test_pc) // batch_size + (1 if len(test_pc) % batch_size > 0 else 0)
        
        for i in range(n_test_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(test_pc))
            
            batch_pc = torch.FloatTensor(test_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(test_kp[start_idx:end_idx]).to(device)
            
            pred_hm = model(batch_pc)
            pred_kp = extract_keypoints_from_heatmap(pred_hm.cpu(), batch_pc.cpu())
            
            for j in range(len(batch_kp)):
                error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j].cpu(), dim=1))
                test_errors.append(error.item())
    
    test_error = np.mean(test_errors)
    
    print(f"📊 {gender_name}数据集论文模型测试结果:")
    print(f"   模型类型: {model_type}")
    print(f"   测试样本数: {len(test_pc)}")
    print(f"   测试误差: {test_error:.2f}mm")
    print(f"   医疗级状态: {'✅ 达标' if test_error < 5.0 else '❌ 未达标'}")
    
    return test_error

def main():
    """主函数"""
    
    print("📝 数据集论文优化：建立可靠的性能基准")
    print("🎯 目标: 女性<5mm, 男性<5mm, 整体医疗级精度")
    print("🔧 策略: 女性简化模型 + 男性复杂模型")
    print("=" * 80)
    
    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 加载数据
    data_result = load_clean_data()
    if data_result is None:
        return
    
    female_pc, female_kp, male_pc, male_kp = data_result
    
    results = {}
    
    # 处理女性数据 (使用简化模型)
    print(f"\n" + "="*80)
    print("👩 处理女性数据 (简化模型)")
    print("="*80)
    
    # 分析数据质量并清洁
    female_quality = analyze_data_quality(female_pc, female_kp, "女性")
    female_clean_pc, female_clean_kp, female_outliers = remove_outliers(
        female_pc, female_kp, female_quality, removal_ratio=0.05)
    
    # 分割清洁数据
    (female_train_pc, female_train_kp), (female_val_pc, female_val_kp), (female_test_pc, female_test_kp) = \
        split_clean_data(female_clean_pc, female_clean_kp, "女性")
    
    # 保守增强女性训练数据
    female_aug_train_pc, female_aug_train_kp = augment_conservative_training_data(
        female_train_pc, female_train_kp, target_size=140)
    
    # 训练女性简化模型
    female_model, female_val_error = train_dataset_paper_model(
        female_aug_train_pc, female_aug_train_kp,
        female_val_pc, female_val_kp, "女性", "simple")
    
    # 测试女性模型
    female_test_error = test_dataset_paper_model(
        female_model, female_test_pc, female_test_kp, "女性", "simple")
    
    results['female'] = {
        'test_error': female_test_error,
        'val_error': female_val_error,
        'test_samples': len(female_test_pc),
        'model_type': 'simple'
    }
    
    # 处理男性数据 (使用复杂模型)
    print(f"\n" + "="*80)
    print("👨 处理男性数据 (复杂模型)")
    print("="*80)
    
    # 分析数据质量并清洁
    male_quality = analyze_data_quality(male_pc, male_kp, "男性")
    male_clean_pc, male_clean_kp, male_outliers = remove_outliers(
        male_pc, male_kp, male_quality, removal_ratio=0.05)
    
    # 分割清洁数据
    (male_train_pc, male_train_kp), (male_val_pc, male_val_kp), (male_test_pc, male_test_kp) = \
        split_clean_data(male_clean_pc, male_clean_kp, "男性")
    
    # 增强男性训练数据
    male_aug_train_pc, male_aug_train_kp = augment_conservative_training_data(
        male_train_pc, male_train_kp, target_size=500)
    
    # 训练男性复杂模型
    male_model, male_val_error = train_dataset_paper_model(
        male_aug_train_pc, male_aug_train_kp,
        male_val_pc, male_val_kp, "男性", "enhanced")
    
    # 测试男性模型
    male_test_error = test_dataset_paper_model(
        male_model, male_test_pc, male_test_kp, "男性", "enhanced")
    
    results['male'] = {
        'test_error': male_test_error,
        'val_error': male_val_error,
        'test_samples': len(male_test_pc),
        'model_type': 'enhanced'
    }
    
    # 总结结果
    print(f"\n" + "="*80)
    print("📝 数据集论文性能基准总结")
    print("="*80)
    
    print(f"📊 女性数据集基准:")
    print(f"   模型类型: 简化模型 ({sum(p.numel() for p in female_model.parameters()):,}参数)")
    print(f"   测试误差: {results['female']['test_error']:.2f}mm")
    print(f"   验证误差: {results['female']['val_error']:.2f}mm")
    print(f"   测试样本: {results['female']['test_samples']}个")
    print(f"   医疗级状态: {'✅ 达标' if results['female']['test_error'] < 5.0 else '❌ 未达标'}")
    
    print(f"\n📊 男性数据集基准:")
    print(f"   模型类型: 增强模型 ({sum(p.numel() for p in male_model.parameters()):,}参数)")
    print(f"   测试误差: {results['male']['test_error']:.2f}mm")
    print(f"   验证误差: {results['male']['val_error']:.2f}mm")
    print(f"   测试样本: {results['male']['test_samples']}个")
    print(f"   医疗级状态: {'✅ 达标' if results['male']['test_error'] < 5.0 else '❌ 未达标'}")
    
    # 整体评估
    avg_error = (results['female']['test_error'] + results['male']['test_error']) / 2
    both_medical_grade = results['female']['test_error'] < 5.0 and results['male']['test_error'] < 5.0
    
    print(f"\n🎯 数据集整体评估:")
    print(f"   平均测试误差: {avg_error:.2f}mm")
    print(f"   医疗级精度: {'✅ 整体达标' if both_medical_grade else '❌ 部分达标'}")
    print(f"   数据集可用性: {'✅ 适合发表' if both_medical_grade else '⚠️ 需要改进'}")
    
    # 数据集论文建议
    print(f"\n📝 数据集论文建议:")
    if both_medical_grade:
        print("✅ 数据集质量良好，可以支撑高质量论文发表")
        print("✅ 建议重点展示数据集的医疗应用价值")
        print("✅ 可以建立多个性能基准供后续研究比较")
    else:
        print("⚠️ 数据集需要进一步优化才能支撑论文发表")
        print("⚠️ 建议分析数据质量问题并提出改进方案")
        print("⚠️ 考虑增加数据样本或改进标注质量")
    
    return results

if __name__ == "__main__":
    main()
