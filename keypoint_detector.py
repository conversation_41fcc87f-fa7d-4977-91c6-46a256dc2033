import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import cv2
import matplotlib.pyplot as plt
from typing import List, Tuple

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class KeypointCNN(nn.Module):
    def __init__(self):
        super(KeypointCNN, self).__init__()
        # 特征提取
        self.conv1 = nn.Conv2d(1, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, padding=1)
        
        # 关键点检测头
        self.conv_keypoints = nn.Conv2d(128, 3, kernel_size=1)  # 3通道：背景、奇点、偶点
        
    def forward(self, x):
        x = F.relu(F.max_pool2d(self.conv1(x), 2))
        x = F.relu(F.max_pool2d(self.conv2(x), 2))
        x = F.relu(self.conv3(x))
        
        # 上采样回原始大小
        keypoints = self.conv_keypoints(x)
        keypoints = F.interpolate(keypoints, scale_factor=4, mode='bilinear')
        return keypoints

class SingularityDataset(Dataset):
    def __init__(self, images: List[np.ndarray], labels: List[np.ndarray]):
        self.images = images
        self.labels = labels
        
    def __len__(self):
        return len(self.images)
    
    def __getitem__(self, idx):
        image = torch.FloatTensor(self.images[idx]).unsqueeze(0)  # 添加通道维度
        label = torch.LongTensor(self.labels[idx])
        return image, label

def generate_synthetic_data(num_samples: int = 1000) -> Tuple[List[np.ndarray], List[np.ndarray]]:
    """生成合成训练数据"""
    images = []
    labels = []
    
    for _ in range(num_samples):
        # 创建空白图像
        image = np.zeros((64, 64), dtype=np.float32)
        label = np.zeros((64, 64), dtype=np.int64)
        
        # 随机生成线条
        num_lines = np.random.randint(2, 6)
        for _ in range(num_lines):
            pt1 = (np.random.randint(0, 64), np.random.randint(0, 64))
            pt2 = (np.random.randint(0, 64), np.random.randint(0, 64))
            cv2.line(image, pt1, pt2, 1.0, 2)
        
        # 标记交叉点
        for i in range(1, 63):
            for j in range(1, 63):
                if image[i, j] > 0:
                    # 计算连接线数量
                    neighbors = image[i-1:i+2, j-1:j+2].flatten()
                    line_count = np.sum(neighbors > 0)
                    if line_count > 2:  # 只考虑交叉点
                        if line_count % 2 == 1:
                            label[i, j] = 1  # 奇点
                        else:
                            label[i, j] = 2  # 偶点
        
        images.append(image)
        labels.append(label)
    
    return images, labels

def train_model(model, train_loader, num_epochs=10):
    """训练模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    optimizer = torch.optim.Adam(model.parameters())
    criterion = nn.CrossEntropyLoss()
    
    for epoch in range(num_epochs):
        model.train()
        total_loss = 0
        
        for images, labels in train_loader:
            images = images.to(device)
            labels = labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        print(f"轮次 {epoch+1}/{num_epochs}, 损失: {total_loss/len(train_loader):.4f}")

def detect_points(model, image: np.ndarray) -> Tuple[List[Tuple[int, int]], List[Tuple[int, int]]]:
    """检测图像中的奇点和偶点"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.eval()
    
    # 预处理像
    image = cv2.resize(image, (64, 64))
    image = torch.FloatTensor(image).unsqueeze(0).unsqueeze(0).to(device)
    
    with torch.no_grad():
        output = model(image)
        pred = output.argmax(dim=1).cpu().numpy()[0]
    
    # 获取奇点和偶点坐标
    singular_points = []
    even_points = []
    
    for i in range(pred.shape[0]):
        for j in range(pred.shape[1]):
            if pred[i, j] == 1:
                singular_points.append((i, j))
            elif pred[i, j] == 2:
                even_points.append((i, j))
    
    return singular_points, even_points

if __name__ == "__main__":
    # 生成训练数据
    images, labels = generate_synthetic_data(1000)
    dataset = SingularityDataset(images, labels)
    train_loader = DataLoader(dataset, batch_size=32, shuffle=True)
    
    # 创建和训练模型
    model = KeypointCNN()
    train_model(model, train_loader)
    
    # 保存模型
    torch.save(model.state_dict(), 'keypoint_model.pth')
    
    # 生成一个测试图像
    test_image = np.zeros((64, 64), dtype=np.float32)
    
    # 添加一些测试线条
    cv2.line(test_image, (10, 10), (50, 50), 1.0, 2)  # 对角线
    cv2.line(test_image, (10, 50), (50, 10), 1.0, 2)  # 交叉的对角线
    cv2.line(test_image, (30, 0), (30, 60), 1.0, 2)   # 垂直线
    
    # 检测特征点
    singular_points, even_points = detect_points(model, test_image)
    
    # 可视化结果
    plt.figure(figsize=(10, 10))
    plt.imshow(test_image, cmap='gray')
    
    for point in singular_points:
        plt.plot(point[1], point[0], 'ro', markersize=10)
    for point in even_points:
        plt.plot(point[1], point[0], 'bo', markersize=10)
    
    plt.title('检测到的奇点(红色)和偶点(蓝色)')
    plt.show()
    
    # 打印检测到的点数
    print(f"检测到的奇点数量：{len(singular_points)}")
    print(f"检测到的偶点数量：{len(even_points)}") 