#!/usr/bin/env python3
"""
训练CHaR启发的19关键点系统
完整的训练流程，支持热力图回归和区域分类
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from charnet_inspired_system import CHaR<PERSON>Inspired, CHaRLoss, create_heatmap_targets, create_region_labels
import os
from sklearn.model_selection import train_test_split

class PelvisKeypointDataset19(Dataset):
    """19关键点骨盆数据集"""
    
    def __init__(self, point_clouds, keypoints, sample_ids, num_points=8192, 
                 sigma=8.0, augment=False, add_null_point=True):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.sample_ids = sample_ids
        self.num_points = num_points
        self.sigma = sigma
        self.augment = augment
        self.add_null_point = add_null_point
        
        print(f"📊 Dataset initialized:")
        print(f"   Samples: {len(self.point_clouds)}")
        print(f"   Keypoints per sample: {len(self.keypoints[0]) if len(self.keypoints) > 0 else 0}")
        print(f"   Augmentation: {self.augment}")
        print(f"   Null point: {self.add_null_point}")
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        pc = self.point_clouds[idx].copy()
        kp = self.keypoints[idx].copy()
        sample_id = self.sample_ids[idx]
        
        # 数据增强
        if self.augment:
            pc, kp = self.apply_augmentation(pc, kp)
        
        # 采样点云
        if len(pc) > self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=False)
            pc = pc[indices]
        elif len(pc) < self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=True)
            pc = pc[indices]
        
        # 添加空点（用于处理缺失区域）
        null_point_idx = -1
        if self.add_null_point:
            # 在点云边界外添加空点
            pc_center = np.mean(pc, axis=0)
            pc_range = np.max(pc, axis=0) - np.min(pc, axis=0)
            null_point = pc_center + pc_range * 2  # 放在边界外
            
            pc = np.vstack([pc, null_point])
            null_point_idx = len(pc) - 1
        
        # 生成热力图目标
        heatmaps = create_heatmap_targets(kp, pc, self.sigma)
        
        # 生成区域标签（简化版本 - 假设所有区域都存在）
        region_labels = create_region_labels(kp, num_regions=3)
        
        return {
            'point_cloud': torch.FloatTensor(pc).transpose(0, 1),  # [3, N]
            'heatmaps': torch.FloatTensor(heatmaps),  # [num_keypoints, N]
            'region_labels': torch.FloatTensor(region_labels),  # [3]
            'keypoints': torch.FloatTensor(kp),  # [num_keypoints, 3]
            'sample_id': sample_id,
            'null_point_idx': null_point_idx
        }
    
    def apply_augmentation(self, pc, kp):
        """数据增强"""
        # 随机旋转
        if np.random.random() < 0.5:
            angle = np.random.uniform(-15, 15) * np.pi / 180
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            pc = pc @ rotation_matrix.T
            kp = kp @ rotation_matrix.T
        
        # 随机缩放
        if np.random.random() < 0.3:
            scale = np.random.uniform(0.9, 1.1)
            pc = pc * scale
            kp = kp * scale
        
        # 随机平移
        if np.random.random() < 0.3:
            translation = np.random.uniform(-5, 5, 3)
            pc = pc + translation
            kp = kp + translation
        
        # 添加噪声
        if np.random.random() < 0.2:
            noise = np.random.normal(0, 0.5, pc.shape)
            pc = pc + noise
        
        return pc, kp

def load_19keypoint_data():
    """加载19关键点数据"""
    
    # 检查是否有19关键点数据
    data_files_19 = [
        'f1_19kp_male.npz', 'f2_19kp_male.npz', 'f3_19kp_male.npz',
        'f1_19kp_female.npz', 'f2_19kp_female.npz', 'f3_19kp_female.npz'
    ]
    
    available_files = [f for f in data_files_19 if os.path.exists(f)]
    
    if not available_files:
        print("⚠️ No 19-keypoint data found. Using 12-keypoint data for demonstration...")
        # 使用现有的12关键点数据，扩展到57个关键点
        male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
        
        point_clouds = male_data['point_clouds']
        keypoints_12 = male_data['keypoints']
        sample_ids = male_data['sample_ids']
        
        # 扩展12个关键点到57个关键点（模拟数据）
        keypoints_57 = []
        for kp_12 in keypoints_12:
            # 为每个区域创建19个关键点
            kp_57 = np.zeros((57, 3))
            
            # F1区域 (0-18): 基于前4个关键点插值
            base_f1 = kp_12[:4]  # 假设前4个是F1
            for i in range(19):
                # 在F1区域内插值生成19个点
                t = i / 18.0
                if i < 4:
                    kp_57[i] = base_f1[i]
                else:
                    # 插值生成其他点
                    idx1 = (i - 4) % 4
                    idx2 = (idx1 + 1) % 4
                    alpha = ((i - 4) % 4) / 4.0
                    kp_57[i] = base_f1[idx1] * (1 - alpha) + base_f1[idx2] * alpha
                    # 添加一些随机偏移
                    kp_57[i] += np.random.normal(0, 2, 3)
            
            # F2区域 (19-37): 基于中间4个关键点
            base_f2 = kp_12[4:8]
            for i in range(19):
                idx = 19 + i
                if i < 4:
                    kp_57[idx] = base_f2[i]
                else:
                    idx1 = (i - 4) % 4
                    idx2 = (idx1 + 1) % 4
                    alpha = ((i - 4) % 4) / 4.0
                    kp_57[idx] = base_f2[idx1] * (1 - alpha) + base_f2[idx2] * alpha
                    kp_57[idx] += np.random.normal(0, 2, 3)
            
            # F3区域 (38-56): 基于后4个关键点
            base_f3 = kp_12[8:12]
            for i in range(19):
                idx = 38 + i
                if i < 4:
                    kp_57[idx] = base_f3[i]
                else:
                    idx1 = (i - 4) % 4
                    idx2 = (idx1 + 1) % 4
                    alpha = ((i - 4) % 4) / 4.0
                    kp_57[idx] = base_f3[idx1] * (1 - alpha) + base_f3[idx2] * alpha
                    kp_57[idx] += np.random.normal(0, 2, 3)
            
            keypoints_57.append(kp_57)
        
        keypoints_57 = np.array(keypoints_57)
        
        print(f"✅ Simulated 57-keypoint data created from 12-keypoint data")
        print(f"   Samples: {len(point_clouds)}")
        print(f"   Keypoints: {keypoints_57.shape}")
        
        return point_clouds, keypoints_57, sample_ids
    
    else:
        print(f"✅ Found {len(available_files)} 19-keypoint data files")
        # 加载真实的19关键点数据
        all_point_clouds = []
        all_keypoints = []
        all_sample_ids = []
        
        for file in available_files:
            data = np.load(file, allow_pickle=True)
            all_point_clouds.extend(data['point_clouds'])
            all_keypoints.extend(data['keypoints'])
            all_sample_ids.extend(data['sample_ids'])
        
        return np.array(all_point_clouds), np.array(all_keypoints), np.array(all_sample_ids)

def train_char_model(train_loader, val_loader, device, num_epochs=50):
    """训练CHaR模型"""
    
    # 创建模型
    model = CHaRNetInspired(input_dim=3, feature_dim=1024, num_keypoints=57).to(device)
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=10, factor=0.5)
    criterion = CHaRLoss(lambda_heatmap=1.0, lambda_region=0.1)
    
    # 训练历史
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    
    print(f"🚀 Starting CHaR training for {num_epochs} epochs")
    print(f"📊 Model parameters: {sum(p.numel() for p in model.parameters())}")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0
        train_heatmap_loss = 0
        train_region_loss = 0
        
        for batch_idx, batch in enumerate(train_loader):
            point_clouds = batch['point_cloud'].to(device)  # [B, 3, N]
            heatmap_targets = batch['heatmaps'].to(device)  # [B, num_keypoints, N]
            region_targets = batch['region_labels'].to(device)  # [B, 3]
            null_point_indices = batch['null_point_idx']
            
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(point_clouds, null_point_idx=null_point_indices[0].item())
            
            # 计算损失
            targets = {
                'heatmaps': heatmap_targets,
                'region_labels': region_targets
            }
            
            losses = criterion(outputs, targets)
            total_loss = losses['total_loss']
            
            # 反向传播
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += total_loss.item()
            train_heatmap_loss += losses['heatmap_loss'].item()
            train_region_loss += losses['region_loss'].item()
        
        # 验证阶段
        model.eval()
        val_loss = 0
        val_heatmap_loss = 0
        val_region_loss = 0
        
        with torch.no_grad():
            for batch in val_loader:
                point_clouds = batch['point_cloud'].to(device)
                heatmap_targets = batch['heatmaps'].to(device)
                region_targets = batch['region_labels'].to(device)
                null_point_indices = batch['null_point_idx']
                
                outputs = model(point_clouds, null_point_idx=null_point_indices[0].item())
                
                targets = {
                    'heatmaps': heatmap_targets,
                    'region_labels': region_targets
                }
                
                losses = criterion(outputs, targets)
                
                val_loss += losses['total_loss'].item()
                val_heatmap_loss += losses['heatmap_loss'].item()
                val_region_loss += losses['region_loss'].item()
        
        # 计算平均损失
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            torch.save(model.state_dict(), 'best_char_model_57kp.pth')
            print(f"✅ Saved best model at epoch {epoch+1}")
        
        # 打印进度
        if (epoch + 1) % 5 == 0:
            print(f"Epoch {epoch+1}/{num_epochs}:")
            print(f"  Train Loss: {avg_train_loss:.4f} (Heatmap: {train_heatmap_loss/len(train_loader):.4f}, Region: {train_region_loss/len(train_loader):.4f})")
            print(f"  Val Loss: {avg_val_loss:.4f} (Heatmap: {val_heatmap_loss/len(val_loader):.4f}, Region: {val_region_loss/len(val_loader):.4f})")
            print(f"  LR: {optimizer.param_groups[0]['lr']:.6f}")
    
    return model, train_losses, val_losses

def main():
    """主函数"""
    print("🚀 Training CHaRNet-Inspired 19-Keypoint System")
    print("Heatmap regression with region conditioning")
    print("=" * 60)
    
    # 加载数据
    point_clouds, keypoints, sample_ids = load_19keypoint_data()
    
    print(f"📊 Dataset loaded:")
    print(f"   Samples: {len(point_clouds)}")
    print(f"   Keypoints shape: {keypoints.shape}")
    print(f"   Point clouds shape: {point_clouds.shape}")
    
    # 数据分割
    train_indices, val_indices = train_test_split(
        range(len(point_clouds)), test_size=0.2, random_state=42
    )
    
    # 创建数据集
    train_dataset = PelvisKeypointDataset19(
        point_clouds[train_indices], keypoints[train_indices], 
        sample_ids[train_indices], augment=True
    )
    
    val_dataset = PelvisKeypointDataset19(
        point_clouds[val_indices], keypoints[val_indices], 
        sample_ids[val_indices], augment=False
    )
    
    # 创建数据加载器 - 使用更大的batch size避免BatchNorm问题
    train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=8, shuffle=False, num_workers=2)
    
    print(f"📊 Data split:")
    print(f"   Training: {len(train_dataset)} samples")
    print(f"   Validation: {len(val_dataset)} samples")
    
    # 训练模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 Using device: {device}")
    
    model, train_losses, val_losses = train_char_model(
        train_loader, val_loader, device, num_epochs=30
    )
    
    # 绘制训练曲线
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='Train Loss', alpha=0.8)
    plt.plot(val_losses, label='Validation Loss', alpha=0.8)
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('CHaR Training Progress')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 2, 2)
    plt.plot(train_losses[-20:], label='Train Loss (Last 20)', alpha=0.8)
    plt.plot(val_losses[-20:], label='Val Loss (Last 20)', alpha=0.8)
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Recent Training Progress')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('char_training_progress.png', dpi=300, bbox_inches='tight')
    print("📊 Training progress saved: char_training_progress.png")
    plt.close()
    
    print(f"\n🎉 CHaR Training Complete!")
    print("✅ Best model saved as: best_char_model_57kp.pth")
    print("💡 Key improvements over basic heatmap regression:")
    print("   1. Region-aware conditioning (F1/F2/F3)")
    print("   2. Robust to missing anatomical regions")
    print("   3. End-to-end heatmap regression")
    print("   4. Inspired by state-of-the-art CHaRNet")
    print("   5. Support for 57 keypoints (19 per region)")

if __name__ == "__main__":
    main()
