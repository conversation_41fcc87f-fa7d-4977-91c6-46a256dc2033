<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
 <!-- 背景 -->
 <!-- 标题 -->
 <!-- 输入数据部分 -->
 <!-- 数据增强部分 -->
 <!-- PointNet 特征提取器 (原始部分) -->
 <!-- 改进：特征增强模块 -->
 <!-- 改进：双头预测结构 -->
 <!-- 改进：多任务损失函数 -->
 <!-- 后处理校正部分 -->
 <!-- 最终输出 -->
 <!-- 性能对比 -->
 <!-- 图例 -->
 <!-- 箭头定义 -->
 <defs>
  <marker orient="auto" refY="3.5" refX="9" markerHeight="7" markerWidth="10" id="arrowhead">
   <polygon id="svg_1" fill="#34495e" points="0 0, 10 3.5, 0 7"/>
  </marker>
  <marker orient="auto" refY="3.5" refX="9" markerHeight="7" markerWidth="10" id="whitearrow">
   <polygon id="svg_2" fill="white" points="0 0, 10 3.5, 0 7"/>
  </marker>
 </defs>
 <!-- 连接线 -->
 <g>
  <title>Layer 1</title>
  <rect x="0" y="1.17647" id="svg_3" fill="#f8f9fa" height="800" width="1200"/>
  <g id="input-section">
   <!-- 原始数据 -->
   <rect id="svg_5" rx="5" stroke-width="2" stroke="#95a5a6" fill="#bdc3c7" height="60" width="120" y="80" x="50"/>
   <text id="svg_6" fill="#2c3e50" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="105" x="110">STL文件</text>
   <text id="svg_7" fill="#2c3e50" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="120" x="110">点云数据</text>
   <rect id="svg_8" rx="5" stroke-width="2" stroke="#95a5a6" fill="#bdc3c7" height="60" width="120" y="160" x="50"/>
   <text id="svg_9" fill="#2c3e50" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="185" x="110">CSV文件</text>
   <text id="svg_10" fill="#2c3e50" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="200" x="110">关键点标注</text>
   <!-- 改进：坐标系对齐 -->
   <rect id="svg_11" rx="5" stroke-width="3" stroke="#2980b9" fill="#3498db" height="80" width="140" y="120" x="220"/>
   <text id="svg_12" fill="white" font-weight="bold" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="140" x="290">坐标系</text>
   <text id="svg_13" fill="white" font-weight="bold" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="155" x="290">一致性对齐</text>
   <text id="svg_14" fill="#ecf0f1" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="175" x="290">✓ 解剖参考点</text>
   <text id="svg_15" fill="#ecf0f1" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="190" x="290">✓ 智能归一化</text>
   <!-- 箭头 -->
   <path id="svg_16" marker-end="url(#arrowhead)" stroke-width="2" stroke="#34495e" d="m170,110l40,30"/>
   <path id="svg_17" marker-end="url(#arrowhead)" stroke-width="2" stroke="#34495e" d="m170,190l40,-20"/>
  </g>
  <g id="augmentation-section">
   <rect id="svg_18" rx="5" stroke-width="3" stroke="#c0392b" fill="#e74c3c" height="80" width="140" y="120" x="400"/>
   <text id="svg_19" fill="white" font-weight="bold" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="140" x="470">增强数据</text>
   <text id="svg_20" fill="white" font-weight="bold" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="155" x="470">增广策略</text>
   <text id="svg_21" fill="#ecf0f1" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="175" x="470">✓ 4倍数据扩增</text>
   <text id="svg_22" fill="#ecf0f1" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="190" x="470">✓ 医疗场景模拟</text>
   <!-- 箭头 -->
   <path id="svg_23" marker-end="url(#arrowhead)" stroke-width="2" stroke="#34495e" d="m360,160l30,0"/>
  </g>
  <g id="pointnet-backbone">
   <rect id="svg_24" rx="5" stroke-width="2" stroke="#7f8c8d" fill="#95a5a6" height="120" width="160" y="80" x="580"/>
   <text id="svg_25" fill="white" font-weight="bold" font-size="14" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="105" x="660">PointNet骨干网络</text>
   <text id="svg_26" fill="#ecf0f1" font-size="11" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="125" x="660">(原始架构)</text>
   <!-- 内部结构 -->
   <rect id="svg_27" rx="2" fill="#7f8c8d" height="20" width="35" y="140" x="590"/>
   <text id="svg_28" fill="white" font-size="9" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="153" x="607">卷积</text>
   <rect id="svg_29" rx="2" fill="#7f8c8d" height="20" width="35" y="140" x="635"/>
   <text id="svg_30" fill="white" font-size="9" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="153" x="652">卷积</text>
   <rect id="svg_31" rx="2" fill="#7f8c8d" height="20" width="35" y="140" x="680"/>
   <text id="svg_32" fill="white" font-size="9" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="153" x="697">池化</text>
   <rect id="svg_33" rx="2" fill="#7f8c8d" height="20" width="80" y="170" x="610"/>
   <text id="svg_34" fill="white" font-size="9" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="183" x="650">全局特征</text>
   <!-- 箭头 -->
   <path id="svg_35" marker-end="url(#arrowhead)" stroke-width="2" stroke="#34495e" d="m540,160l30,0"/>
  </g>
  <g id="feature-enhancement">
   <rect id="svg_36" rx="5" stroke-width="3" stroke="#8e44ad" fill="#9b59b6" height="60" width="160" y="210" x="580"/>
   <text id="svg_37" fill="white" font-weight="bold" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="230" x="660">特征增强模块</text>
   <text id="svg_38" fill="#ecf0f1" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="250" x="660">✓ 多尺度特征</text>
   <text id="svg_39" fill="#ecf0f1" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="265" x="660">✓ 解剖注意力</text>
  </g>
  <g id="dual-head">
   <!-- 关键点预测头 -->
   <rect id="svg_40" rx="5" stroke-width="3" stroke="#229954" fill="#27ae60" height="80" width="140" y="88.23529" x="801.17647"/>
   <text id="svg_41" fill="white" font-weight="bold" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="113.23529" x="871.17647">关键点预测头</text>
   <text id="svg_42" fill="#ecf0f1" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="133.23529" x="871.17647">✓ 57个关键点坐标</text>
   <text id="svg_43" fill="#ecf0f1" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="148.23529" x="871.17647">✓ 医疗重要性加权</text>
   <text id="svg_44" fill="#ecf0f1" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="163.23529" x="871.17647">输出: [57, 3]</text>
   <!-- 坐标范围预测头 -->
   <rect id="svg_45" rx="5" stroke-width="3" stroke="#e67e22" fill="#f39c12" height="80" width="140" y="188.23529" x="801.17647"/>
   <text id="svg_46" fill="white" font-weight="bold" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="213.23529" x="871.17647">范围预测头</text>
   <text id="svg_47" fill="#ecf0f1" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="233.23529" x="871.17647">✓ 空间约束</text>
   <text id="svg_48" fill="#ecf0f1" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="248.23529" x="871.17647">✓ 边界预测</text>
   <text id="svg_49" fill="#ecf0f1" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="263.23529" x="871.17647">输出: [6]</text>
   <!-- 箭头 -->
   <path id="svg_50" marker-end="url(#arrowhead)" stroke-width="2" stroke="#34495e" d="m741.17647,128.23529l50,0"/>
   <path id="svg_51" marker-end="url(#arrowhead)" stroke-width="2" stroke="#34495e" d="m741.17647,228.23529l50,0"/>
  </g>
  <g id="loss-function">
   <rect id="svg_52" rx="5" stroke-width="3" stroke="#d35400" fill="#e67e22" height="100" width="160" y="120" x="980"/>
   <text id="svg_53" fill="white" font-weight="bold" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="145" x="1060">多任务损失函数</text>
   <!-- 损失组件 -->
   <rect id="svg_54" rx="2" fill="#d35400" height="18" width="65" y="160" x="990"/>
   <text id="svg_55" fill="white" font-size="8" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="172" x="1022">关键点损失</text>
   <rect id="svg_56" rx="2" fill="#d35400" height="18" width="65" y="160" x="1065"/>
   <text id="svg_57" fill="white" font-size="8" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="172" x="1097">范围损失</text>
   <rect id="svg_58" rx="2" fill="#d35400" height="18" width="65" y="185" x="990"/>
   <text id="svg_59" fill="white" font-size="8" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="197" x="1022">一致性损失</text>
   <rect id="svg_60" rx="2" fill="#d35400" height="18" width="65" y="185" x="1065"/>
   <text id="svg_61" fill="white" font-size="8" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="197" x="1097">医疗加权</text>
   <!-- 箭头 -->
   <path id="svg_62" marker-end="url(#arrowhead)" stroke-width="2" stroke="#34495e" d="m940,130l30,10"/>
   <path id="svg_63" marker-end="url(#arrowhead)" stroke-width="2" stroke="#34495e" d="m940,230l30,-30"/>
  </g>
  <g id="post-processing">
   <rect id="svg_64" rx="5" stroke-width="3" stroke="#16a085" fill="#1abc9c" height="120" width="700" y="350" x="400"/>
   <text id="svg_65" fill="white" font-weight="bold" font-size="16" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="375" x="750">综合对齐校正</text>
   <!-- 四步校正 -->
   <rect id="svg_66" rx="3" fill="#16a085" height="60" width="130" y="390" x="420"/>
   <text id="svg_67" fill="white" font-weight="bold" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="410" x="485">步骤1: 全局</text>
   <text id="svg_68" fill="white" font-weight="bold" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="425" x="485">质心对齐</text>
   <text id="svg_69" fill="#ecf0f1" font-size="9" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="440" x="485">改善16%</text>
   <rect id="svg_70" rx="3" fill="#16a085" height="60" width="130" y="390" x="570"/>
   <text id="svg_71" fill="white" font-weight="bold" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="410" x="635">步骤2: 对称</text>
   <text id="svg_72" fill="white" font-weight="bold" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="425" x="635">区域校正</text>
   <text id="svg_73" fill="#ecf0f1" font-size="9" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="440" x="635">改善26%</text>
   <rect id="svg_74" rx="3" fill="#16a085" height="60" width="130" y="390" x="720"/>
   <text id="svg_75" fill="white" font-weight="bold" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="410" x="785">步骤3: 边缘-中心</text>
   <text id="svg_76" fill="white" font-weight="bold" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="425" x="785">对齐</text>
   <text id="svg_77" fill="#ecf0f1" font-size="9" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="440" x="785">改善19%</text>
   <rect id="svg_78" rx="3" fill="#16a085" height="60" width="130" y="390" x="870"/>
   <text id="svg_79" fill="white" font-weight="bold" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="410" x="935">步骤4: 精细</text>
   <text id="svg_80" fill="white" font-weight="bold" font-size="10" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="425" x="935">调整</text>
   <text id="svg_81" fill="#ecf0f1" font-size="9" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="440" x="935">最终优化</text>
   <!-- 连接箭头 -->
   <path id="svg_82" marker-end="url(#whitearrow)" stroke-width="2" stroke="white" d="m550,420l10,0"/>
   <path id="svg_83" marker-end="url(#whitearrow)" stroke-width="2" stroke="white" d="m700,420l10,0"/>
   <path id="svg_84" marker-end="url(#whitearrow)" stroke-width="2" stroke="white" d="m850,420l10,0"/>
  </g>
  <g id="final-output">
   <rect id="svg_85" rx="5" stroke-width="2" stroke="#34495e" fill="#2c3e50" height="80" width="300" y="520" x="450"/>
   <text id="svg_86" fill="white" font-weight="bold" font-size="16" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="545" x="600">最终输出</text>
   <text id="svg_87" fill="#ecf0f1" font-size="14" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="570" x="600">57个高精度关键点</text>
   <text id="svg_88" fill="#1abc9c" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="590" x="600">平均误差0.63mm</text>
  </g>
  <g id="performance-comparison">
   <rect id="svg_89" rx="5" stroke-width="2" stroke="#bdc3c7" fill="#ecf0f1" height="120" width="1100" y="650" x="50"/>
   <text id="svg_90" fill="#2c3e50" font-weight="bold" font-size="16" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="675" x="600">性能演进</text>
   <!-- 性能条 -->
   <rect id="svg_91" rx="3" fill="#e74c3c" height="25" width="200" y="690" x="80"/>
   <text id="svg_92" fill="white" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="707" x="180">基线: 4.5mm</text>
   <rect id="svg_93" rx="3" fill="#f39c12" height="25" width="160" y="690" x="300"/>
   <text id="svg_94" fill="white" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="707" x="380">+坐标系: 3.72mm</text>
   <rect id="svg_95" rx="3" fill="#27ae60" height="25" width="140" y="690" x="480"/>
   <text id="svg_96" fill="white" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="707" x="550">+架构: 3.21mm</text>
   <rect id="svg_97" rx="3" fill="#3498db" height="25" width="120" y="690" x="640"/>
   <text id="svg_98" fill="white" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="707" x="700">+增强: 2.02mm</text>
   <rect id="svg_99" rx="3" fill="#1abc9c" height="25" width="100" y="690" x="780"/>
   <text id="svg_100" fill="white" font-size="12" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="707" x="830">+校正: 0.63mm</text>
   <!-- 改善百分比 -->
   <text id="svg_101" fill="#27ae60" font-size="11" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="735" x="380">↓17%</text>
   <text id="svg_102" fill="#27ae60" font-size="11" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="735" x="550">↓14%</text>
   <text id="svg_103" fill="#27ae60" font-size="11" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="735" x="700">↓37%</text>
   <text id="svg_104" fill="#27ae60" font-size="11" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="735" x="830">↓69%</text>
   <text id="svg_105" fill="#27ae60" font-weight="bold" font-size="14" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="707" x="1000">总体改善86%</text>
  </g>
  <g id="legend">
   <rect id="svg_106" rx="5" stroke="#bdc3c7" fill="white" height="120" width="300" y="300" x="50"/>
   <text id="svg_107" fill="#2c3e50" font-weight="bold" font-size="14" font-family="SimHei, Arial, sans-serif" text-anchor="middle" y="320" x="200">图例</text>
   <rect id="svg_108" fill="#95a5a6" height="15" width="15" y="330" x="70"/>
   <text id="svg_109" fill="#2c3e50" font-size="11" font-family="SimHei, Arial, sans-serif" y="342" x="95">原始PointNet组件</text>
   <rect id="svg_110" fill="#3498db" height="15" width="15" y="350" x="70"/>
   <text id="svg_111" fill="#2c3e50" font-size="11" font-family="SimHei, Arial, sans-serif" y="362" x="95">坐标系改进</text>
   <rect id="svg_112" fill="#27ae60" height="15" width="15" y="370" x="70"/>
   <text id="svg_113" fill="#2c3e50" font-size="11" font-family="SimHei, Arial, sans-serif" y="382" x="95">架构增强</text>
   <rect id="svg_114" fill="#1abc9c" height="15" width="15" y="390" x="70"/>
   <text id="svg_115" fill="#2c3e50" font-size="11" font-family="SimHei, Arial, sans-serif" y="402" x="95">后处理校正</text>
  </g>
  <path id="svg_116" marker-end="url(#arrowhead)" stroke-width="3" stroke="#34495e" d="m750,290l0,50"/>
  <path id="svg_117" marker-end="url(#arrowhead)" stroke-width="3" stroke="#34495e" d="m750,470l-150,40"/>
 </g>
</svg>