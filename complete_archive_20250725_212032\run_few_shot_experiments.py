#!/usr/bin/env python3
"""
小样本学习实验启动脚本
Few-Shot Learning Experiment Launcher
"""

import argparse
import torch
import numpy as np
import json
from pathlib import Path
import time
from datetime import datetime

def check_environment():
    """检查实验环境"""
    print("🔍 检查实验环境...")
    
    # 检查CUDA
    if torch.cuda.is_available():
        print(f"✅ CUDA可用: {torch.cuda.get_device_name()}")
        print(f"   GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    else:
        print("⚠️  CUDA不可用，将使用CPU")
    
    # 检查数据文件
    data_path = Path("data/raw/high_quality_f3_dataset.npz")
    if data_path.exists():
        data = np.load(data_path, allow_pickle=True)
        print(f"✅ 数据文件存在: {len(data['sample_ids'])} 样本")
    else:
        print(f"❌ 数据文件不存在: {data_path}")
        return False
    
    return True

def run_basic_experiment():
    """运行基础小样本学习实验"""
    print("\n🚀 运行基础小样本学习实验")
    print("=" * 50)
    
    try:
        from few_shot_97_samples_experiment import main as basic_main
        results = basic_main()
        return results
    except Exception as e:
        print(f"❌ 基础实验失败: {e}")
        return None

def run_advanced_experiment():
    """运行高级小样本学习实验"""
    print("\n🚀 运行高级小样本学习实验")
    print("=" * 50)
    
    try:
        from advanced_few_shot_97_samples import run_advanced_experiment
        results_with, results_without = run_advanced_experiment()
        return results_with, results_without
    except Exception as e:
        print(f"❌ 高级实验失败: {e}")
        return None, None

def save_results(results, experiment_type="basic"):
    """保存实验结果"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = Path("results/few_shot_experiments")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    filename = f"{experiment_type}_few_shot_results_{timestamp}.json"
    filepath = results_dir / filename
    
    # 准备保存的数据
    save_data = {
        "experiment_type": experiment_type,
        "timestamp": timestamp,
        "results": results,
        "dataset_info": {
            "total_samples": 97,
            "data_source": "high_quality_f3_dataset.npz",
            "keypoints": 19,
            "point_cloud_size": "50000 -> 4096 (downsampled)"
        }
    }
    
    with open(filepath, 'w') as f:
        json.dump(save_data, f, indent=2)
    
    print(f"💾 结果已保存: {filepath}")
    return filepath

def analyze_results(results):
    """分析实验结果"""
    print("\n📊 结果分析")
    print("=" * 40)
    
    if not results:
        print("❌ 没有可分析的结果")
        return
    
    # 计算改进情况
    baseline_error = 5.0  # 假设当前基线误差为5mm
    
    print(f"{'Shot':<6} {'误差(mm)':<10} {'vs基线':<10} {'医疗级':<10}")
    print("-" * 40)
    
    for k_shot, error in results.items():
        improvement = (baseline_error - error) / baseline_error * 100
        medical_grade = "✅" if error < 2.0 else "❌"
        
        print(f"{k_shot:<6} {error:<10.2f} {improvement:>+6.1f}% {medical_grade:<10}")
    
    # 找出最佳配置
    best_shot = min(results.keys(), key=lambda k: results[k])
    best_error = results[best_shot]
    
    print(f"\n🏆 最佳配置: {best_shot}-shot, 误差: {best_error:.2f}mm")
    
    if best_error < 2.0:
        print("🎉 恭喜！达到医疗级精度 (<2mm)")
    elif best_error < 3.0:
        print("👍 接近医疗级精度，继续优化")
    else:
        print("📈 需要进一步改进")

def generate_recommendations(results):
    """生成改进建议"""
    print("\n💡 改进建议")
    print("=" * 30)
    
    if not results:
        return
    
    best_error = min(results.values())
    
    if best_error > 3.0:
        print("🔧 建议优先尝试:")
        print("   1. 增加数据增强强度")
        print("   2. 调整学习率和训练轮数")
        print("   3. 尝试不同的网络架构")
        print("   4. 增加自监督预训练轮数")
    elif best_error > 2.0:
        print("🎯 接近目标，建议:")
        print("   1. 精细调整超参数")
        print("   2. 增加模型集成")
        print("   3. 使用更复杂的数据增强")
        print("   4. 尝试知识蒸馏")
    else:
        print("🎉 已达到医疗级精度！")
        print("   建议进行更多验证实验")
        print("   考虑在更大数据集上测试")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="小样本学习实验")
    parser.add_argument("--experiment", choices=["basic", "advanced", "both"], 
                       default="both", help="实验类型")
    parser.add_argument("--save", action="store_true", help="保存结果")
    
    args = parser.parse_args()
    
    print("🎯 97样本医疗关键点检测 - 小样本学习实验")
    print("=" * 60)
    
    # 检查环境
    if not check_environment():
        return
    
    # 运行实验
    if args.experiment in ["basic", "both"]:
        print("\n" + "="*60)
        basic_results = run_basic_experiment()
        
        if basic_results:
            analyze_results(basic_results)
            generate_recommendations(basic_results)
            
            if args.save:
                save_results(basic_results, "basic")
    
    if args.experiment in ["advanced", "both"]:
        print("\n" + "="*60)
        advanced_with, advanced_without = run_advanced_experiment()
        
        if advanced_with and advanced_without:
            print("\n📊 高级实验结果分析")
            print("有预训练结果:")
            analyze_results(advanced_with)
            
            print("\n无预训练结果:")
            analyze_results(advanced_without)
            
            if args.save:
                save_results({
                    "with_pretraining": advanced_with,
                    "without_pretraining": advanced_without
                }, "advanced")
    
    print("\n🎉 实验完成！")
    print("💡 提示: 使用 --save 参数保存详细结果")

if __name__ == "__main__":
    main()
