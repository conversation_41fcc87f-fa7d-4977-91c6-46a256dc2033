#!/usr/bin/env python3
"""
正确的评估方法 - 避免数据泄露
Proper Evaluation Method - Avoiding Data Leakage
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import os
from tqdm import tqdm

class HeatmapRegressionNet(nn.Module):
    """Heatmap回归网络"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super(HeatmapRegressionNet, self).__init__()
        
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 点云特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        # 全局特征
        self.global_conv = nn.Conv1d(512, 1024, 1)
        
        # 特征融合
        self.fusion_conv1 = nn.Conv1d(1024 + 256, 512, 1)
        self.fusion_conv2 = nn.Conv1d(512, 256, 1)
        
        # Heatmap生成
        self.heatmap_conv1 = nn.Conv1d(256, 128, 1)
        self.heatmap_conv2 = nn.Conv1d(128, 64, 1)
        self.heatmap_conv3 = nn.Conv1d(64, num_keypoints, 1)
        
        # 激活函数和正则化
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.3)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        # 点云特征提取
        x1 = self.relu(self.bn1(self.conv1(x)))
        x2 = self.relu(self.bn2(self.conv2(x1)))
        x3 = self.relu(self.bn3(self.conv3(x2)))
        x4 = self.relu(self.bn4(self.conv4(x3)))
        
        # 全局特征
        global_feat = self.relu(self.global_conv(x4))
        global_feat = torch.max(global_feat, 2, keepdim=True)[0]
        
        # 扩展全局特征
        global_feat_expanded = global_feat.repeat(1, 1, self.num_points)
        
        # 融合局部和全局特征
        combined_feat = torch.cat([x3, global_feat_expanded], 1)
        
        # 特征融合
        fused = self.relu(self.fusion_conv1(combined_feat))
        fused = self.dropout(fused)
        fused = self.relu(self.fusion_conv2(fused))
        
        # 生成热图
        heatmap = self.relu(self.heatmap_conv1(fused))
        heatmap = self.relu(self.heatmap_conv2(heatmap))
        heatmap = self.heatmap_conv3(heatmap)
        
        # 转置并应用softmax
        heatmap = heatmap.transpose(2, 1)
        
        # 对每个关键点的热图进行softmax
        heatmap_list = []
        for i in range(self.num_keypoints):
            hm_i = torch.softmax(heatmap[:, :, i], dim=1)
            heatmap_list.append(hm_i.unsqueeze(2))
        
        final_heatmap = torch.cat(heatmap_list, dim=2)
        
        return final_heatmap

def load_original_data():
    """加载原始数据（未增强的）"""
    
    print("📊 加载原始数据...")
    
    # 加载女性原始数据
    female_original_path = "archive/old_experiments/f3_reduced_12kp_female.npz"
    if os.path.exists(female_original_path):
        female_data = np.load(female_original_path, allow_pickle=True)
        female_pc = female_data['point_clouds']
        female_kp = female_data['keypoints']
        print(f"✅ 女性原始数据: {len(female_pc)}个样本")
    else:
        print(f"❌ 女性原始数据不存在: {female_original_path}")
        return None
    
    # 加载男性原始数据
    male_original_path = "archive/old_experiments/f3_reduced_12kp_male.npz"
    if os.path.exists(male_original_path):
        male_data = np.load(male_original_path, allow_pickle=True)
        male_pc = male_data['point_clouds']
        male_kp = male_data['keypoints']
        print(f"✅ 男性原始数据: {len(male_pc)}个样本")
    else:
        print(f"❌ 男性原始数据不存在: {male_original_path}")
        return None
    
    return female_pc, female_kp, male_pc, male_kp

def split_original_data(pc, kp, gender_name):
    """正确分割原始数据"""
    
    print(f"\n📋 分割{gender_name}原始数据...")
    
    n_samples = len(pc)
    print(f"   总样本数: {n_samples}")
    
    # 计算分割点
    n_train = int(n_samples * 0.7)
    n_val = int(n_samples * 0.15)
    n_test = n_samples - n_train - n_val
    
    print(f"   训练集: {n_train}个样本 (70%)")
    print(f"   验证集: {n_val}个样本 (15%)")
    print(f"   测试集: {n_test}个样本 (15%)")
    
    # 随机打乱
    indices = np.random.permutation(n_samples)
    
    train_indices = indices[:n_train]
    val_indices = indices[n_train:n_train + n_val]
    test_indices = indices[n_train + n_val:]
    
    # 分割数据
    train_pc = pc[train_indices]
    train_kp = kp[train_indices]
    
    val_pc = pc[val_indices]
    val_kp = kp[val_indices]
    
    test_pc = pc[test_indices]
    test_kp = kp[test_indices]
    
    return (train_pc, train_kp), (val_pc, val_kp), (test_pc, test_kp)

def augment_training_data(train_pc, train_kp, target_size=None):
    """只对训练数据进行增强"""
    
    print(f"🔄 对训练数据进行增强...")
    print(f"   原始训练样本: {len(train_pc)}")
    
    if target_size is None:
        target_size = len(train_pc) * 10  # 默认10倍增强
    
    augmented_pc = []
    augmented_kp = []
    
    # 保留原始数据
    for i in range(len(train_pc)):
        augmented_pc.append(train_pc[i])
        augmented_kp.append(train_kp[i])
    
    # 生成增强数据
    n_augment_needed = target_size - len(train_pc)
    
    for i in tqdm(range(n_augment_needed), desc="生成增强数据"):
        # 随机选择一个原始样本
        idx = np.random.randint(0, len(train_pc))
        original_pc = train_pc[idx].copy()
        original_kp = train_kp[idx].copy()
        
        # 随机选择增强方法
        aug_type = np.random.choice(['rotation', 'gaussian', 'uncertainty'])
        
        if aug_type == 'rotation':
            # 3D旋转增强
            angles = np.random.uniform(-15, 15, 3)
            aug_pc, aug_kp = apply_3d_rotation(original_pc, original_kp, angles)
        elif aug_type == 'gaussian':
            # 高斯核调整（通过重新生成热图实现）
            sigma_factor = np.random.uniform(0.8, 1.2)
            aug_pc = original_pc.copy()
            aug_kp = original_kp.copy()
        else:  # uncertainty
            # 不确定性增强
            noise = np.random.normal(0, 2.0, original_kp.shape)
            aug_pc = original_pc.copy()
            aug_kp = original_kp + noise
        
        augmented_pc.append(aug_pc)
        augmented_kp.append(aug_kp)
    
    augmented_pc = np.array(augmented_pc)
    augmented_kp = np.array(augmented_kp)
    
    print(f"   增强后训练样本: {len(augmented_pc)}")
    print(f"   增强倍数: {len(augmented_pc) / len(train_pc):.1f}x")
    
    return augmented_pc, augmented_kp

def apply_3d_rotation(point_cloud, keypoints, angles_deg):
    """应用3D旋转"""
    
    angles = np.radians(angles_deg)
    
    # 旋转矩阵
    cos_x, sin_x = np.cos(angles[0]), np.sin(angles[0])
    cos_y, sin_y = np.cos(angles[1]), np.sin(angles[1])
    cos_z, sin_z = np.cos(angles[2]), np.sin(angles[2])
    
    # X轴旋转
    Rx = np.array([[1, 0, 0],
                   [0, cos_x, -sin_x],
                   [0, sin_x, cos_x]])
    
    # Y轴旋转
    Ry = np.array([[cos_y, 0, sin_y],
                   [0, 1, 0],
                   [-sin_y, 0, cos_y]])
    
    # Z轴旋转
    Rz = np.array([[cos_z, -sin_z, 0],
                   [sin_z, cos_z, 0],
                   [0, 0, 1]])
    
    # 组合旋转
    R = Rz @ Ry @ Rx
    
    # 应用旋转
    rotated_pc = point_cloud @ R.T
    rotated_kp = keypoints @ R.T
    
    return rotated_pc, rotated_kp

def generate_heatmap_from_keypoints(keypoints, point_cloud, sigma=5.0):
    """从关键点生成热图"""
    heatmaps = []
    
    for kp in keypoints:
        distances = np.linalg.norm(point_cloud - kp, axis=1)
        heatmap = np.exp(-distances**2 / (2 * sigma**2))
        
        if np.sum(heatmap) > 0:
            heatmap = heatmap / np.sum(heatmap)
        
        heatmaps.append(heatmap)
    
    return np.array(heatmaps)

def heatmap_loss_function(pred_heatmap, target_heatmap):
    """热图损失函数"""
    
    # 检查并调整target_heatmap的维度
    if len(target_heatmap.shape) == 3 and target_heatmap.shape[1] == 12:
        target_heatmap = target_heatmap.transpose(1, 2)
    
    # KL散度损失
    kl_loss = nn.KLDivLoss(reduction='batchmean')
    
    total_loss = 0
    batch_size, num_points, num_keypoints = pred_heatmap.shape
    
    for i in range(num_keypoints):
        log_pred = torch.log(pred_heatmap[:, :, i] + 1e-8)
        loss_i = kl_loss(log_pred, target_heatmap[:, :, i])
        total_loss += loss_i
    
    return total_loss / num_keypoints

def extract_keypoints_from_heatmap(heatmap, point_cloud):
    """从热图中提取关键点坐标"""
    
    batch_size, num_points, num_keypoints = heatmap.shape
    keypoints = torch.zeros(batch_size, num_keypoints, 3)
    
    for b in range(batch_size):
        for k in range(num_keypoints):
            weights = heatmap[b, :, k]
            weighted_coords = point_cloud[b] * weights.unsqueeze(1)
            keypoint = torch.sum(weighted_coords, dim=0) / torch.sum(weights)
            keypoints[b, k] = keypoint
    
    return keypoints

def train_model_properly(train_pc, train_kp, val_pc, val_kp, gender_name):
    """正确训练模型"""
    
    print(f"\n🚀 训练{gender_name}专用模型 (正确方法)")
    print("=" * 60)
    
    # 生成训练数据的热图
    print("🔥 生成训练数据热图...")
    train_hm = []
    for i in tqdm(range(len(train_pc)), desc="训练热图"):
        hm = generate_heatmap_from_keypoints(train_kp[i], train_pc[i])
        train_hm.append(hm)
    train_hm = np.array(train_hm)
    
    # 生成验证数据的热图
    print("🔥 生成验证数据热图...")
    val_hm = []
    for i in tqdm(range(len(val_pc)), desc="验证热图"):
        hm = generate_heatmap_from_keypoints(val_kp[i], val_pc[i])
        val_hm.append(hm)
    val_hm = np.array(val_hm)
    
    # 设备设置
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ 使用设备: {device}")
    
    # 模型初始化
    model = HeatmapRegressionNet(num_points=50000, num_keypoints=12)
    model = model.to(device)
    
    print(f"🏗️ {gender_name}专用模型:")
    print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"   训练样本: {len(train_pc)}")
    print(f"   验证样本: {len(val_pc)} (原始数据)")
    
    # 优化器
    optimizer = optim.Adam(model.parameters(), lr=0.0005, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=15, gamma=0.7)
    
    # 训练参数
    num_epochs = 40
    batch_size = 4
    best_val_error = float('inf')
    patience = 10
    patience_counter = 0
    
    print(f"🎯 训练参数:")
    print(f"   训练轮数: {num_epochs}")
    print(f"   批次大小: {batch_size}")
    print(f"   学习率: 0.0005")
    
    print(f"\n🚀 开始训练...")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        n_train_batches = len(train_pc) // batch_size
        
        train_pbar = tqdm(range(n_train_batches), desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for i in train_pbar:
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(train_pc))
            
            batch_pc = torch.FloatTensor(train_pc[start_idx:end_idx]).to(device)
            batch_hm = torch.FloatTensor(train_hm[start_idx:end_idx]).to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            pred_hm = model(batch_pc)
            loss = heatmap_loss_function(pred_hm, batch_hm)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_pbar.set_postfix({'Loss': f'{loss.item():.4f}'})
        
        avg_train_loss = train_loss / n_train_batches
        
        # 验证阶段 (在原始数据上)
        model.eval()
        val_errors = []
        
        with torch.no_grad():
            n_val_batches = len(val_pc) // batch_size + (1 if len(val_pc) % batch_size > 0 else 0)
            
            for i in range(n_val_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(val_pc))
                
                batch_pc = torch.FloatTensor(val_pc[start_idx:end_idx]).to(device)
                batch_kp = torch.FloatTensor(val_kp[start_idx:end_idx]).to(device)
                
                pred_hm = model(batch_pc)
                pred_kp = extract_keypoints_from_heatmap(pred_hm.cpu(), batch_pc.cpu())
                
                for j in range(len(batch_kp)):
                    error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j].cpu(), dim=1))
                    val_errors.append(error.item())
        
        avg_val_error = np.mean(val_errors)
        
        # 学习率调度
        scheduler.step()
        
        print(f"\nEpoch {epoch+1}/{num_epochs}:")
        print(f"  训练损失: {avg_train_loss:.4f}")
        print(f"  验证误差: {avg_val_error:.2f}mm (原始数据)")
        print(f"  学习率: {scheduler.get_last_lr()[0]:.6f}")
        
        # 早停和模型保存
        if avg_val_error < best_val_error:
            best_val_error = avg_val_error
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'val_error': avg_val_error,
            }, f'best_{gender_name}_proper_model.pth')
            print(f"  ✅ 保存最佳模型 (验证误差: {avg_val_error:.2f}mm)")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f"  ⏹️ 早停触发 (耐心: {patience})")
                break
    
    return model, best_val_error

def test_model_properly(model, test_pc, test_kp, gender_name):
    """在原始测试数据上测试模型"""
    
    print(f"\n🧪 测试{gender_name}专用模型 (原始数据)")
    print("=" * 60)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    model.eval()
    
    test_errors = []
    
    batch_size = 4
    with torch.no_grad():
        n_test_batches = len(test_pc) // batch_size + (1 if len(test_pc) % batch_size > 0 else 0)
        
        for i in range(n_test_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(test_pc))
            
            batch_pc = torch.FloatTensor(test_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(test_kp[start_idx:end_idx]).to(device)
            
            pred_hm = model(batch_pc)
            pred_kp = extract_keypoints_from_heatmap(pred_hm.cpu(), batch_pc.cpu())
            
            for j in range(len(batch_kp)):
                error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j].cpu(), dim=1))
                test_errors.append(error.item())
    
    final_test_error = np.mean(test_errors)
    
    print(f"📊 {gender_name}专用模型测试结果:")
    print(f"   测试样本数: {len(test_pc)} (原始数据)")
    print(f"   测试误差: {final_test_error:.2f}mm")
    print(f"   医疗级状态: {'✅ 达标' if final_test_error < 5.0 else '❌ 未达标'}")
    
    return final_test_error

def main():
    """主函数"""
    
    print("🔧 正确的评估方法 - 避免数据泄露")
    print("🎯 目标: 获得真实可信的模型性能")
    print("=" * 80)
    
    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 加载原始数据
    data_result = load_original_data()
    if data_result is None:
        return
    
    female_pc, female_kp, male_pc, male_kp = data_result
    
    results = {}
    
    # 处理女性数据
    print(f"\n" + "="*80)
    print("👩 处理女性数据")
    print("="*80)
    
    (female_train_pc, female_train_kp), (female_val_pc, female_val_kp), (female_test_pc, female_test_kp) = \
        split_original_data(female_pc, female_kp, "女性")
    
    # 增强女性训练数据
    female_aug_train_pc, female_aug_train_kp = augment_training_data(
        female_train_pc, female_train_kp, target_size=170)
    
    # 训练女性模型
    female_model, female_val_error = train_model_properly(
        female_aug_train_pc, female_aug_train_kp, 
        female_val_pc, female_val_kp, "女性")
    
    # 测试女性模型
    female_test_error = test_model_properly(
        female_model, female_test_pc, female_test_kp, "女性")
    
    results['female'] = {
        'val_error': female_val_error,
        'test_error': female_test_error,
        'test_samples': len(female_test_pc)
    }
    
    # 处理男性数据
    print(f"\n" + "="*80)
    print("👨 处理男性数据")
    print("="*80)
    
    (male_train_pc, male_train_kp), (male_val_pc, male_val_kp), (male_test_pc, male_test_kp) = \
        split_original_data(male_pc, male_kp, "男性")
    
    # 增强男性训练数据
    male_aug_train_pc, male_aug_train_kp = augment_training_data(
        male_train_pc, male_train_kp, target_size=500)
    
    # 训练男性模型
    male_model, male_val_error = train_model_properly(
        male_aug_train_pc, male_aug_train_kp, 
        male_val_pc, male_val_kp, "男性")
    
    # 测试男性模型
    male_test_error = test_model_properly(
        male_model, male_test_pc, male_test_kp, "男性")
    
    results['male'] = {
        'val_error': male_val_error,
        'test_error': male_test_error,
        'test_samples': len(male_test_pc)
    }
    
    # 总结结果
    print(f"\n" + "="*80)
    print("🎉 正确评估结果总结")
    print("="*80)
    
    print(f"📊 女性专用模型 (正确评估):")
    print(f"   验证误差: {results['female']['val_error']:.2f}mm")
    print(f"   测试误差: {results['female']['test_error']:.2f}mm")
    print(f"   测试样本: {results['female']['test_samples']}个 (原始数据)")
    
    print(f"\n📊 男性专用模型 (正确评估):")
    print(f"   验证误差: {results['male']['val_error']:.2f}mm")
    print(f"   测试误差: {results['male']['test_error']:.2f}mm")
    print(f"   测试样本: {results['male']['test_samples']}个 (原始数据)")
    
    print(f"\n📈 与之前结果对比:")
    print(f"   女性模型: {results['female']['test_error']:.2f}mm vs 之前2.88mm")
    print(f"   男性模型: {results['male']['test_error']:.2f}mm vs 之前4.50mm")
    
    gender_gap = abs(results['female']['test_error'] - results['male']['test_error'])
    print(f"   性别差异: {gender_gap:.2f}mm")
    
    print(f"\n✅ 评估质量:")
    print(f"   • 无数据泄露: 测试集完全是原始数据")
    print(f"   • 严格分割: 训练/验证/测试完全独立")
    print(f"   • 可信结果: 基于真实未见过的数据")
    
    return results

if __name__ == "__main__":
    main()
