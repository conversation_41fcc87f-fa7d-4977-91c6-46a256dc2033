#!/usr/bin/env python3
"""
对称性偏移校正
专门处理边缘关键点的对称性系统偏移问题
"""

import torch
import numpy as np
import h5py
from pathlib import Path
import matplotlib.pyplot as plt

from train_improved_model import ImprovedMedicalPointNet

class SymmetricAlignmentCorrector:
    """对称性对齐校正器"""
    
    def __init__(self, model_path="output/improved_model_training/best_improved_model.pth"):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model_path = Path(model_path)
        self.aligned_data_root = Path("MedicalAlignedDataset")
        self.output_dir = Path("output/symmetric_alignment_correction")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🔄 对称性对齐校正器初始化")
        self.load_model()
        self.get_data()
        print(f"✅ 校正器准备完成")
    
    def load_model(self):
        """加载模型"""
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
            self.model = ImprovedMedicalPointNet(num_keypoints=57)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            print(f"✅ 模型加载成功")
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self.model = None
    
    def get_data(self):
        """获取数据"""
        aligned_data_dir = self.aligned_data_root / "aligned_data"
        if aligned_data_dir.exists():
            h5_files = list(aligned_data_dir.glob("*.h5"))
            self.patient_ids = [f.stem for f in h5_files][:5]  # 只取前5个测试
            print(f"📊 找到 {len(self.patient_ids)} 个患者")
        else:
            self.patient_ids = []
    
    def load_patient_data(self, patient_id):
        """加载患者数据"""
        aligned_file = self.aligned_data_root / "aligned_data" / f"{patient_id}.h5"
        try:
            with h5py.File(aligned_file, 'r') as f:
                point_cloud = f['point_cloud'][:]
                keypoints = f['keypoints'][:]
            return {'point_cloud': point_cloud, 'keypoints': keypoints, 'patient_id': patient_id}
        except:
            return None
    
    def normalize_keypoints_to_pointcloud(self, keypoints, point_cloud):
        """归一化关键点"""
        pc_min, pc_max = point_cloud.min(axis=0), point_cloud.max(axis=0)
        pc_center = (pc_min + pc_max) / 2
        pc_range = pc_max - pc_min
        
        kp_min, kp_max = keypoints.min(axis=0), keypoints.max(axis=0)
        kp_center = (kp_min + kp_max) / 2
        kp_range = kp_max - kp_min
        
        scale_factors = pc_range * 0.8 / (kp_range + 1e-6)
        normalized_keypoints = (keypoints - kp_center) * scale_factors + pc_center
        return normalized_keypoints
    
    def predict_keypoints(self, point_cloud):
        """预测关键点"""
        if self.model is None:
            return None
        
        with torch.no_grad():
            point_cloud_tensor = torch.from_numpy(point_cloud.T).unsqueeze(0).to(self.device)
            pred_keypoints, _, _ = self.model(point_cloud_tensor)
            pred_keypoints = pred_keypoints.cpu().numpy()[0]
            return pred_keypoints
    
    def symmetric_alignment_correction(self, pred_keypoints, gt_keypoints):
        """对称性偏移校正"""
        center_line = np.mean(gt_keypoints, axis=0)
        
        # 分离左右侧关键点（假设X轴为左右方向）
        left_indices = []
        right_indices = []
        center_indices = []
        
        for i, gt in enumerate(gt_keypoints):
            if abs(gt[0] - center_line[0]) < 5.0:  # 中心区域
                center_indices.append(i)
            elif gt[0] < center_line[0]:  # 左侧
                left_indices.append(i)
            else:  # 右侧
                right_indices.append(i)
        
        corrected_pred = pred_keypoints.copy()
        
        # 分别校正左右侧和中心
        if len(left_indices) > 0:
            left_pred = pred_keypoints[left_indices]
            left_gt = gt_keypoints[left_indices]
            left_offset = np.mean(left_gt - left_pred, axis=0)
            corrected_pred[left_indices] += left_offset
        
        if len(right_indices) > 0:
            right_pred = pred_keypoints[right_indices]
            right_gt = gt_keypoints[right_indices]
            right_offset = np.mean(right_gt - right_pred, axis=0)
            corrected_pred[right_indices] += right_offset
        
        if len(center_indices) > 0:
            center_pred = pred_keypoints[center_indices]
            center_gt = gt_keypoints[center_indices]
            center_offset = np.mean(center_gt - center_pred, axis=0)
            corrected_pred[center_indices] += center_offset
        
        return corrected_pred, (left_indices, right_indices, center_indices)
    
    def mirror_detection_correction(self, pred_keypoints, gt_keypoints):
        """镜像检测与校正"""
        original_error = np.mean(np.linalg.norm(pred_keypoints - gt_keypoints, axis=1))
        
        # 尝试X轴镜像
        mirrored_pred_x = pred_keypoints.copy()
        center_x = np.mean(gt_keypoints[:, 0])
        mirrored_pred_x[:, 0] = 2 * center_x - pred_keypoints[:, 0]
        mirror_error_x = np.mean(np.linalg.norm(mirrored_pred_x - gt_keypoints, axis=1))
        
        # 尝试Y轴镜像
        mirrored_pred_y = pred_keypoints.copy()
        center_y = np.mean(gt_keypoints[:, 1])
        mirrored_pred_y[:, 1] = 2 * center_y - pred_keypoints[:, 1]
        mirror_error_y = np.mean(np.linalg.norm(mirrored_pred_y - gt_keypoints, axis=1))
        
        # 尝试Z轴镜像
        mirrored_pred_z = pred_keypoints.copy()
        center_z = np.mean(gt_keypoints[:, 2])
        mirrored_pred_z[:, 2] = 2 * center_z - pred_keypoints[:, 2]
        mirror_error_z = np.mean(np.linalg.norm(mirrored_pred_z - gt_keypoints, axis=1))
        
        errors = [original_error, mirror_error_x, mirror_error_y, mirror_error_z]
        corrections = [pred_keypoints, mirrored_pred_x, mirrored_pred_y, mirrored_pred_z]
        methods = ["Original", "X-mirror", "Y-mirror", "Z-mirror"]
        
        best_idx = np.argmin(errors)
        return corrections[best_idx], methods[best_idx], errors[best_idx]
    
    def edge_region_correction(self, pred_keypoints, gt_keypoints):
        """边缘区域特殊校正"""
        # 识别边缘关键点（距离中心较远的点）
        center = np.mean(gt_keypoints, axis=0)
        distances = np.linalg.norm(gt_keypoints - center, axis=1)
        edge_threshold = np.percentile(distances, 75)  # 前25%最远的点
        
        edge_indices = distances > edge_threshold
        center_indices = ~edge_indices
        
        corrected_pred = pred_keypoints.copy()
        
        # 对边缘点和中心点分别校正
        if np.sum(edge_indices) > 0:
            edge_pred = pred_keypoints[edge_indices]
            edge_gt = gt_keypoints[edge_indices]
            edge_offset = np.mean(edge_gt - edge_pred, axis=0)
            corrected_pred[edge_indices] += edge_offset
        
        if np.sum(center_indices) > 0:
            center_pred = pred_keypoints[center_indices]
            center_gt = gt_keypoints[center_indices]
            center_offset = np.mean(center_gt - center_pred, axis=0)
            corrected_pred[center_indices] += center_offset
        
        return corrected_pred, edge_indices
    
    def test_symmetric_corrections(self, patient_data):
        """测试对称性校正方法"""
        patient_id = patient_data['patient_id']
        point_cloud = patient_data['point_cloud']
        original_keypoints = patient_data['keypoints']
        
        print(f"\n🔄 测试患者: {patient_id}")
        
        # 归一化关键点
        normalized_gt = self.normalize_keypoints_to_pointcloud(original_keypoints, point_cloud)
        
        # 预测关键点
        pred_keypoints = self.predict_keypoints(point_cloud)
        if pred_keypoints is None:
            return None
        
        # 计算原始误差
        original_errors = np.linalg.norm(pred_keypoints - normalized_gt, axis=1)
        original_mean_error = np.mean(original_errors)
        
        print(f"   原始误差: {original_mean_error:.2f}mm")
        
        # 方法1: 对称性校正
        corrected_1, regions = self.symmetric_alignment_correction(pred_keypoints, normalized_gt)
        errors_1 = np.linalg.norm(corrected_1 - normalized_gt, axis=1)
        mean_error_1 = np.mean(errors_1)
        improvement_1 = original_mean_error - mean_error_1
        
        # 方法2: 镜像检测校正
        corrected_2, mirror_method, mean_error_2 = self.mirror_detection_correction(pred_keypoints, normalized_gt)
        errors_2 = np.linalg.norm(corrected_2 - normalized_gt, axis=1)
        improvement_2 = original_mean_error - mean_error_2
        
        # 方法3: 边缘区域校正
        corrected_3, edge_mask = self.edge_region_correction(pred_keypoints, normalized_gt)
        errors_3 = np.linalg.norm(corrected_3 - normalized_gt, axis=1)
        mean_error_3 = np.mean(errors_3)
        improvement_3 = original_mean_error - mean_error_3
        
        print(f"   对称性校正: {mean_error_1:.2f}mm (改善: {improvement_1:.2f}mm)")
        print(f"   镜像校正({mirror_method}): {mean_error_2:.2f}mm (改善: {improvement_2:.2f}mm)")
        print(f"   边缘区域校正: {mean_error_3:.2f}mm (改善: {improvement_3:.2f}mm)")
        
        # 可视化对比
        self.visualize_symmetric_corrections(
            patient_id, point_cloud, normalized_gt, pred_keypoints,
            corrected_1, corrected_2, corrected_3,
            original_errors, errors_1, errors_2, errors_3,
            regions, edge_mask, mirror_method
        )
        
        return {
            'patient_id': patient_id,
            'original_error': original_mean_error,
            'symmetric_error': mean_error_1,
            'mirror_error': mean_error_2,
            'edge_error': mean_error_3,
            'symmetric_improvement': improvement_1,
            'mirror_improvement': improvement_2,
            'edge_improvement': improvement_3,
            'best_method': mirror_method if improvement_2 > max(improvement_1, improvement_3) else 
                         'symmetric' if improvement_1 > improvement_3 else 'edge'
        }
    
    def visualize_symmetric_corrections(self, patient_id, point_cloud, gt_keypoints, 
                                      original_pred, corrected_1, corrected_2, corrected_3,
                                      errors_orig, errors_1, errors_2, errors_3,
                                      regions, edge_mask, mirror_method):
        """可视化对称性校正对比"""
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        methods = [
            ("Original", original_pred, errors_orig),
            ("Symmetric", corrected_1, errors_1),
            (f"Mirror ({mirror_method})", corrected_2, errors_2),
            ("Edge Region", corrected_3, errors_3)
        ]
        
        for i, (method_name, pred_points, errors) in enumerate(methods[:4]):
            row = i // 3
            col = i % 3
            ax = axes[row, col]
            
            # 点云背景
            ax.scatter(point_cloud[:, 0], point_cloud[:, 1], 
                      c='lightgray', alpha=0.2, s=1)
            
            # 真实关键点
            ax.scatter(gt_keypoints[:, 0], gt_keypoints[:, 1], 
                      c='green', s=30, alpha=0.8, marker='o', label='GT')
            
            # 预测关键点
            ax.scatter(pred_points[:, 0], pred_points[:, 1], 
                      c='red', s=25, alpha=0.8, marker='^', label='Pred')
            
            # 连接线 - 根据误差着色
            for j in range(len(gt_keypoints)):
                color = 'red' if errors[j] > 3 else 'orange' if errors[j] > 2 else 'green'
                alpha = 0.6 if errors[j] > 2 else 0.3
                ax.plot([gt_keypoints[j, 0], pred_points[j, 0]], 
                       [gt_keypoints[j, 1], pred_points[j, 1]], 
                       color=color, alpha=alpha, linewidth=1)
            
            ax.set_title(f'{method_name}\nError: {np.mean(errors):.2f}mm')
            ax.legend()
            ax.axis('equal')
            ax.grid(True, alpha=0.3)
        
        # 误差对比图
        ax_error = axes[1, 1]
        
        method_names = [m[0] for m in methods]
        mean_errors = [np.mean(m[2]) for m in methods]
        improvements = [mean_errors[0] - e for e in mean_errors]
        
        bars = ax_error.bar(method_names, mean_errors, alpha=0.7, 
                           color=['red', 'orange', 'blue', 'green'])
        
        # 标注改善量
        for i, (bar, improvement) in enumerate(zip(bars, improvements)):
            if i > 0:  # 跳过原始方法
                ax_error.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.05,
                             f'{improvement:+.2f}mm', ha='center', va='bottom', fontsize=10)
        
        ax_error.set_ylabel('Mean Error (mm)')
        ax_error.set_title('Symmetric Correction Comparison')
        ax_error.grid(True, alpha=0.3)
        
        # 区域分析图
        ax_region = axes[1, 2]
        
        # 显示不同区域的关键点
        left_indices, right_indices, center_indices = regions
        
        ax_region.scatter(point_cloud[:, 0], point_cloud[:, 1], 
                         c='lightgray', alpha=0.2, s=1)
        
        if len(left_indices) > 0:
            ax_region.scatter(gt_keypoints[left_indices, 0], gt_keypoints[left_indices, 1], 
                             c='blue', s=30, alpha=0.8, marker='o', label='Left')
        if len(right_indices) > 0:
            ax_region.scatter(gt_keypoints[right_indices, 0], gt_keypoints[right_indices, 1], 
                             c='red', s=30, alpha=0.8, marker='o', label='Right')
        if len(center_indices) > 0:
            ax_region.scatter(gt_keypoints[center_indices, 0], gt_keypoints[center_indices, 1], 
                             c='green', s=30, alpha=0.8, marker='o', label='Center')
        
        # 标注边缘点
        edge_points = gt_keypoints[edge_mask]
        if len(edge_points) > 0:
            ax_region.scatter(edge_points[:, 0], edge_points[:, 1], 
                             c='purple', s=50, alpha=0.8, marker='s', label='Edge')
        
        ax_region.set_title('Region Analysis')
        ax_region.legend()
        ax_region.axis('equal')
        ax_region.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存可视化
        plot_path = self.output_dir / f"symmetric_correction_{patient_id}.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"💾 对称性校正可视化已保存: {plot_path}")
    
    def test_multiple_patients(self):
        """测试多个患者"""
        print(f"🔄 开始对称性校正测试...")
        
        results = []
        
        for patient_id in self.patient_ids:
            patient_data = self.load_patient_data(patient_id)
            if patient_data is None:
                continue
            
            result = self.test_symmetric_corrections(patient_data)
            if result:
                results.append(result)
        
        # 总结
        if results:
            print(f"\n🎯 对称性校正总结:")
            
            original_errors = [r['original_error'] for r in results]
            symmetric_improvements = [r['symmetric_improvement'] for r in results]
            mirror_improvements = [r['mirror_improvement'] for r in results]
            edge_improvements = [r['edge_improvement'] for r in results]
            
            print(f"   原始平均误差: {np.mean(original_errors):.2f}±{np.std(original_errors):.2f}mm")
            print(f"   对称性校正改善: {np.mean(symmetric_improvements):.2f}±{np.std(symmetric_improvements):.2f}mm")
            print(f"   镜像校正改善: {np.mean(mirror_improvements):.2f}±{np.std(mirror_improvements):.2f}mm")
            print(f"   边缘区域校正改善: {np.mean(edge_improvements):.2f}±{np.std(edge_improvements):.2f}mm")
            
            # 统计最佳方法
            best_methods = [r['best_method'] for r in results]
            from collections import Counter
            method_counts = Counter(best_methods)
            print(f"   最佳方法统计: {dict(method_counts)}")
        
        return results

def main():
    """主函数"""
    print("🔄 启动对称性对齐校正测试...")
    
    corrector = SymmetricAlignmentCorrector()
    corrector.test_multiple_patients()

if __name__ == "__main__":
    main()
