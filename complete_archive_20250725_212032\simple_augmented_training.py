#!/usr/bin/env python3
"""
简化版增强数据训练
Simple Augmented Data Training
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import os
from tqdm import tqdm

class SimplePointNet(nn.Module):
    """简化的PointNet用于关键点检测"""
    
    def __init__(self, num_keypoints=12):
        super(SimplePointNet, self).__init__()
        
        # 点云特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        
        # 全局特征
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        # 关键点回归
        self.fc1 = nn.Linear(512, 256)
        self.fc2 = nn.Linear(256, 128)
        self.fc3 = nn.Linear(128, num_keypoints * 3)
        
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        # x: (batch_size, num_points, 3)
        x = x.transpose(2, 1)  # (batch_size, 3, num_points)
        
        # 特征提取
        x = self.relu(self.conv1(x))
        x = self.relu(self.conv2(x))
        x = self.relu(self.conv3(x))
        x = self.relu(self.conv4(x))
        
        # 全局最大池化
        x = torch.max(x, 2)[0]  # (batch_size, 512)
        
        # 全连接层
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.fc3(x)
        
        # 重塑为关键点坐标
        x = x.view(-1, 12, 3)
        
        return x

def load_augmented_data():
    """加载增强数据"""
    
    print("📊 加载增强数据...")
    
    data_path = "f3_reduced_12kp_female_augmented.npz"
    if not os.path.exists(data_path):
        print(f"❌ 数据文件不存在: {data_path}")
        return None, None, None
    
    data = np.load(data_path, allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    
    print(f"✅ 数据加载成功:")
    print(f"   样本数: {len(point_clouds)}")
    print(f"   点云形状: {point_clouds.shape}")
    print(f"   关键点形状: {keypoints.shape}")
    
    # 数据分割
    n_samples = len(point_clouds)
    n_train = int(n_samples * 0.7)
    n_val = int(n_samples * 0.15)
    
    # 随机打乱
    indices = np.random.permutation(n_samples)
    
    train_indices = indices[:n_train]
    val_indices = indices[n_train:n_train + n_val]
    test_indices = indices[n_train + n_val:]
    
    train_data = (point_clouds[train_indices], keypoints[train_indices])
    val_data = (point_clouds[val_indices], keypoints[val_indices])
    test_data = (point_clouds[test_indices], keypoints[test_indices])
    
    print(f"📋 数据分割:")
    print(f"   训练集: {len(train_indices)}个样本")
    print(f"   验证集: {len(val_indices)}个样本")
    print(f"   测试集: {len(test_indices)}个样本")
    
    return train_data, val_data, test_data

def train_simple_model():
    """训练简化模型"""
    
    print("🔥 开始训练简化版增强模型")
    print("=" * 60)
    
    # 加载数据
    train_data, val_data, test_data = load_augmented_data()
    if train_data is None:
        return
    
    # 设备设置
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ 使用设备: {device}")
    
    # 模型初始化
    model = SimplePointNet(num_keypoints=12)
    model = model.to(device)
    
    # 优化器
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    criterion = nn.MSELoss()
    
    # 训练参数
    num_epochs = 30
    batch_size = 8
    best_val_error = float('inf')
    
    print(f"🎯 训练参数:")
    print(f"   训练轮数: {num_epochs}")
    print(f"   批次大小: {batch_size}")
    print(f"   学习率: 0.001")
    
    # 准备数据
    train_pc, train_kp = train_data
    val_pc, val_kp = val_data
    test_pc, test_kp = test_data
    
    print(f"\n🚀 开始训练...")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        n_train_batches = len(train_pc) // batch_size
        
        for i in range(n_train_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(train_pc))
            
            batch_pc = torch.FloatTensor(train_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(train_kp[start_idx:end_idx]).to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            pred_kp = model(batch_pc)
            loss = criterion(pred_kp, batch_kp)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        avg_train_loss = train_loss / n_train_batches
        
        # 验证阶段
        model.eval()
        val_errors = []
        
        with torch.no_grad():
            n_val_batches = len(val_pc) // batch_size + (1 if len(val_pc) % batch_size > 0 else 0)
            
            for i in range(n_val_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(val_pc))
                
                batch_pc = torch.FloatTensor(val_pc[start_idx:end_idx]).to(device)
                batch_kp = torch.FloatTensor(val_kp[start_idx:end_idx]).to(device)
                
                pred_kp = model(batch_pc)
                
                # 计算误差 (mm)
                error = torch.mean(torch.norm(pred_kp - batch_kp, dim=2))
                val_errors.append(error.item())
        
        avg_val_error = np.mean(val_errors)
        
        print(f"Epoch {epoch+1}/{num_epochs}:")
        print(f"  训练损失: {avg_train_loss:.4f}")
        print(f"  验证误差: {avg_val_error:.2f}mm")
        
        # 保存最佳模型
        if avg_val_error < best_val_error:
            best_val_error = avg_val_error
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'val_error': avg_val_error,
            }, 'best_simple_augmented_model.pth')
            print(f"  ✅ 保存最佳模型 (验证误差: {avg_val_error:.2f}mm)")
    
    # 测试最佳模型
    print(f"\n🧪 测试最佳模型...")
    checkpoint = torch.load('best_simple_augmented_model.pth')
    model.load_state_dict(checkpoint['model_state_dict'])
    
    model.eval()
    test_errors = []
    
    with torch.no_grad():
        n_test_batches = len(test_pc) // batch_size + (1 if len(test_pc) % batch_size > 0 else 0)
        
        for i in range(n_test_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(test_pc))
            
            batch_pc = torch.FloatTensor(test_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(test_kp[start_idx:end_idx]).to(device)
            
            pred_kp = model(batch_pc)
            error = torch.mean(torch.norm(pred_kp - batch_kp, dim=2))
            test_errors.append(error.item())
    
    final_test_error = np.mean(test_errors)
    
    print(f"\n🎉 训练完成!")
    print(f"=" * 60)
    print(f"📊 最终结果:")
    print(f"   最佳验证误差: {best_val_error:.2f}mm")
    print(f"   最终测试误差: {final_test_error:.2f}mm")
    print(f"   训练样本数: {len(train_pc)}")
    
    # 与之前结果对比
    print(f"\n📈 性能对比:")
    print(f"   原始女性模型 (25样本): 4.88mm")
    print(f"   增强女性模型 (250样本): {final_test_error:.2f}mm")
    
    if final_test_error < 4.88:
        improvement = ((4.88 - final_test_error) / 4.88) * 100
        print(f"   🎉 性能提升: {improvement:.1f}%")
        if final_test_error < 4.0:
            print(f"   🏆 突破4mm大关!")
        if final_test_error < 3.5:
            print(f"   🚀 达到3.5mm级别!")
    else:
        print(f"   ⚠️ 性能未提升，可能需要调整参数")
    
    return final_test_error

def compare_with_original():
    """与原始结果对比"""
    
    print(f"\n📊 完整性能对比:")
    print("=" * 60)
    print("历史最佳结果:")
    print("   • Heatmap回归 (25女性样本): 4.88mm")
    print("   • 精确集成方法: 5.371mm")
    print("   • Point Transformer: 7.129mm")
    print("   • 医疗级目标: 5.0mm")
    print()
    print("增强数据的意义:")
    print("   • 数据量: 25个 → 250个 (10倍增长)")
    print("   • 过拟合风险: 大幅降低")
    print("   • 泛化能力: 显著提升")
    print("   • 医学合理性: 保持解剖学约束")

def main():
    """主函数"""
    
    print("🔥 增强数据训练实验")
    print("🎯 目标: 验证10倍数据增强的效果")
    print("=" * 80)
    
    # 训练模型
    final_error = train_simple_model()
    
    # 对比分析
    compare_with_original()
    
    print(f"\n💡 实验结论:")
    if final_error and final_error < 4.88:
        print(f"✅ 数据增强成功! 性能从4.88mm提升到{final_error:.2f}mm")
        print(f"✅ 证明了数据增强对医学AI的重要价值")
    else:
        print(f"⚠️ 需要进一步优化模型架构或训练策略")
    
    print(f"\n🚀 下一步建议:")
    print(f"   1. 如果效果好，可以尝试更复杂的模型")
    print(f"   2. 考虑增强男性数据并训练混合模型")
    print(f"   3. 探索更高级的增强技术")

if __name__ == "__main__":
    main()
