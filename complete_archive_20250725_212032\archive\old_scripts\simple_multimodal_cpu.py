#!/usr/bin/env python3
"""
简化版多模态特征融合 - CPU版本
先验证核心思想，然后再解决GPU问题
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)

class SimpleDensityFeatureExtractor(nn.Module):
    """简化的密度特征提取器"""
    
    def __init__(self, radius_list=[1.0, 2.0, 4.0]):
        super(SimpleDensityFeatureExtractor, self).__init__()
        
        self.radius_list = radius_list
        
    def compute_density(self, xyz, radius):
        """计算指定半径内的点密度"""
        B, N, _ = xyz.shape
        
        # 计算距离矩阵
        distances = torch.cdist(xyz, xyz)  # [B, N, N]
        
        # 统计半径内的点数
        within_radius = (distances < radius).float()
        density = torch.sum(within_radius, dim=2, keepdim=True)  # [B, N, 1]
        
        return density
    
    def forward(self, xyz):
        """提取多尺度密度特征"""
        density_features = []
        
        for radius in self.radius_list:
            density = self.compute_density(xyz, radius)  # [B, N, 1]
            density_features.append(density)
        
        # 拼接所有密度特征
        all_densities = torch.cat(density_features, dim=2)  # [B, N, len(radius_list)]
        
        return all_densities.transpose(2, 1)  # [B, len(radius_list), N]

class SimpleGeometricFeatureExtractor(nn.Module):
    """简化的几何特征提取器"""
    
    def __init__(self, k=16):
        super(SimpleGeometricFeatureExtractor, self).__init__()
        
        self.k = k
        
    def forward(self, xyz):
        """提取几何特征"""
        B, N, _ = xyz.shape
        
        # 计算距离矩阵
        distances = torch.cdist(xyz, xyz)  # [B, N, N]
        
        # K近邻
        _, idx = torch.topk(distances, k=self.k, dim=-1, largest=False)  # [B, N, k]
        
        # 获取邻居点
        neighbors = torch.gather(xyz.unsqueeze(2).expand(-1, -1, self.k, -1), 
                                1, idx.unsqueeze(-1).expand(-1, -1, -1, 3))  # [B, N, k, 3]
        
        center = xyz.unsqueeze(2)  # [B, N, 1, 3]
        
        # 相对位置
        relative_pos = neighbors - center  # [B, N, k, 3]
        
        # 距离特征
        dist_features = torch.norm(relative_pos, dim=3)  # [B, N, k]
        
        # 统计特征
        mean_dist = torch.mean(dist_features, dim=2, keepdim=True)  # [B, N, 1]
        std_dist = torch.std(dist_features, dim=2, keepdim=True)   # [B, N, 1]
        
        geometric_features = torch.cat([mean_dist, std_dist], dim=2)  # [B, N, 2]
        
        return geometric_features.transpose(2, 1)  # [B, 2, N]

class SimpleMultiModalPointNet(nn.Module):
    """简化版多模态PointNet"""
    
    def __init__(self, num_keypoints=12):
        super(SimpleMultiModalPointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 密度特征提取器
        self.density_extractor = SimpleDensityFeatureExtractor(radius_list=[1.0, 2.0, 4.0])
        
        # 几何特征提取器
        self.geometric_extractor = SimpleGeometricFeatureExtractor(k=16)
        
        # 基础PointNet特征
        self.pointnet_conv1 = nn.Conv1d(3, 64, 1)
        self.pointnet_conv2 = nn.Conv1d(64, 128, 1)
        self.pointnet_conv3 = nn.Conv1d(128, 256, 1)
        
        self.pointnet_bn1 = nn.BatchNorm1d(64)
        self.pointnet_bn2 = nn.BatchNorm1d(128)
        self.pointnet_bn3 = nn.BatchNorm1d(256)
        
        # 特征融合
        # 密度(3) + 几何(2) + PointNet(256) = 261
        self.feature_fusion = nn.Sequential(
            nn.Conv1d(261, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Conv1d(512, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 关键点预测
        self.keypoint_head = nn.Sequential(
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(64, num_keypoints * 3)
        )
        
        print(f"🧠 简化多模态PointNet: {num_keypoints}个关键点")
        print(f"   - 密度特征: 3个尺度")
        print(f"   - 几何特征: 距离统计")
        print(f"   - PointNet特征: 基础特征")
        
    def forward(self, x):
        """
        Args:
            x: [B, N, 3] 输入点云
        """
        batch_size = x.size(0)
        xyz = x  # [B, N, 3]
        
        # 1. 密度特征
        density_features = self.density_extractor(xyz)  # [B, 3, N]
        
        # 2. 几何特征
        geometric_features = self.geometric_extractor(xyz)  # [B, 2, N]
        
        # 3. PointNet特征
        pointnet_input = xyz.transpose(2, 1)  # [B, 3, N]
        pn_feat1 = F.relu(self.pointnet_bn1(self.pointnet_conv1(pointnet_input)))
        pn_feat2 = F.relu(self.pointnet_bn2(self.pointnet_conv2(pn_feat1)))
        pn_feat3 = F.relu(self.pointnet_bn3(self.pointnet_conv3(pn_feat2)))
        
        # 4. 特征融合
        all_features = torch.cat([
            density_features,     # [B, 3, N]
            geometric_features,   # [B, 2, N]
            pn_feat3             # [B, 256, N]
        ], dim=1)  # [B, 261, N]
        
        fused_features = self.feature_fusion(all_features)  # [B, 256, N]
        
        # 5. 全局特征
        global_features = fused_features.max(dim=-1)[0]  # [B, 256]
        
        # 6. 关键点预测
        keypoints = self.keypoint_head(global_features)  # [B, num_keypoints * 3]
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

class ReducedKeypointsF3Dataset(Dataset):
    """12关键点F3数据集"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points
        self.augment = augment
        self.split = split
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
    
    def __len__(self):
        return len(self.sample_ids)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        # 数据增强
        if self.augment and self.split == 'train':
            if np.random.random() < 0.7:
                angle = np.random.uniform(-0.08, 0.08)
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                point_cloud = point_cloud @ rotation.T
                keypoints = keypoints @ rotation.T
            
            if np.random.random() < 0.6:
                translation = np.random.uniform(-0.4, 0.4, 3)
                point_cloud += translation
                keypoints += translation
            
            if np.random.random() < 0.5:
                scale = np.random.uniform(0.99, 1.01, 3)
                point_cloud *= scale
                keypoints *= scale
            
            if np.random.random() < 0.6:
                noise_level = np.random.choice([0.02, 0.03, 0.04])
                noise = np.random.normal(0, noise_level, point_cloud.shape)
                point_cloud += noise
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def train_simple_multimodal():
    """训练简化版多模态模型"""
    
    print(f"🚀 **简化版多模态特征融合训练**")
    print(f"🔧 **CPU版本**: 先验证核心思想")
    print(f"🎯 **目标**: 验证密度+几何特征的效果")
    print("=" * 80)
    
    set_seed(42)
    
    device = torch.device('cpu')  # 强制使用CPU
    print(f"🖥️  设备: {device}")
    
    # 数据集
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    train_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'train', 
                                            num_points=1024, test_samples=test_samples,  # 减少点数
                                            augment=True, seed=42)
    val_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'val', 
                                          num_points=1024, test_samples=test_samples, 
                                          augment=False, seed=42)
    
    batch_size = 2  # 减少批次大小
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
    
    # 模型
    model = SimpleMultiModalPointNet(num_keypoints=12).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 模型参数: {total_params:,}")
    
    # 损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
    
    num_epochs = 20  # 减少训练轮数
    best_val_error = float('inf')
    
    print(f"🎯 训练配置: 简化多模态特征融合")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 40)
        
        # 训练
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        for batch in train_loader:
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                        
            except Exception as e:
                print(f"❌ 训练批次失败: {e}")
                continue
        
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except Exception as e:
                    continue
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # 打印结果
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm")
        
        # 检查改进
        current_error = val_metrics['mean_distance']
        if current_error < best_val_error:
            best_val_error = current_error
            print(f"🎉 新最佳! 验证误差: {best_val_error:.3f}mm")
    
    total_time = time.time() - start_time
    
    print(f"\n🎉 **简化多模态训练完成!**")
    print(f"🎯 最佳误差: {best_val_error:.3f}mm")
    print(f"⏱️  训练时间: {total_time/60:.1f}分钟")
    
    return best_val_error

if __name__ == "__main__":
    set_seed(42)
    best_error = train_simple_multimodal()
