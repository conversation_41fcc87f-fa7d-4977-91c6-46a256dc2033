#!/usr/bin/env python3
"""
简单12点测试
Simple 12-point Test
快速验证12点子集的性能
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import json

class Simple12PointNet(nn.Module):
    """简单的12点PointNet"""
    
    def __init__(self, num_keypoints=12):
        super(Simple12PointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 回归头
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, num_keypoints * 3)
        
        self.fc_bn1 = nn.BatchNorm1d(512)
        self.fc_bn2 = nn.BatchNorm1d(256)
        
        self.dropout = nn.Dropout(0.1)
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"🏗️ Simple12PointNet: {total_params:,} 参数")
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        x = F.relu(self.bn4(self.conv4(x)))
        x = F.relu(self.bn5(self.conv5(x)))
        
        # 全局最大池化
        global_feat = torch.max(x, 2)[0]  # [B, 1024]
        
        # 回归
        x = F.relu(self.fc_bn1(self.fc1(global_feat)))
        x = self.dropout(x)
        x = F.relu(self.fc_bn2(self.fc2(x)))
        x = self.dropout(x)
        keypoints = self.fc3(x)
        
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

class Simple12Dataset(Dataset):
    def __init__(self, point_clouds, keypoints_12):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints_12)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def create_simple_12point_subset():
    """创建简单的12点子集"""
    # 选择最容易预测的12个点（基于之前的分析）
    easy_12_indices = [13, 3, 7, 2, 32, 25, 26, 34, 45, 49, 44, 48]
    
    print(f"🎯 选择最容易预测的12点:")
    print(f"   索引: {easy_12_indices}")
    
    return easy_12_indices

def simple_normalization(point_clouds, keypoints_12):
    """简单归一化"""
    print("🔧 执行简单归一化...")
    
    normalized_pc = []
    normalized_kp = []
    
    for i in range(len(point_clouds)):
        pc = point_clouds[i].copy()
        kp = keypoints_12[i].copy()
        
        # 简单的中心化和缩放
        pc_center = np.mean(pc, axis=0)
        kp_center = np.mean(kp, axis=0)
        
        pc_centered = pc - pc_center
        kp_centered = kp - kp_center
        
        # 缩放到单位方差
        pc_scale = np.std(pc_centered)
        kp_scale = np.std(kp_centered)
        
        if pc_scale > 0:
            pc_normalized = pc_centered / pc_scale
        else:
            pc_normalized = pc_centered
            
        if kp_scale > 0:
            kp_normalized = kp_centered / kp_scale
        else:
            kp_normalized = kp_centered
        
        normalized_pc.append(pc_normalized)
        normalized_kp.append(kp_normalized)
    
    return np.array(normalized_pc), np.array(normalized_kp)

def train_simple_model(model, train_loader, val_loader, epochs=100, device='cuda'):
    """训练简单模型"""
    
    print(f"🚀 训练简单12点模型...")
    
    model = model.to(device)
    
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=25, gamma=0.8)
    criterion = nn.MSELoss()
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 20
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                val_loss += loss.item()
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        scheduler.step()
        
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_simple_12point_model.pth')
        else:
            patience_counter += 1
        
        if epoch % 10 == 0 or epoch < 3:
            current_lr = optimizer.param_groups[0]['lr']
            print(f"Epoch {epoch+1:3d}: "
                  f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
                  f"Train Error: {train_error:.4f}, Val Error: {val_error:.4f}, "
                  f"LR: {current_lr:.2e}")
        
        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    model.load_state_dict(torch.load('best_simple_12point_model.pth'))
    return model

def test_simple_model(model, test_loader, device='cuda'):
    """测试简单模型"""
    
    print("🔍 测试简单12点模型...")
    
    model = model.to(device)
    model.eval()
    
    all_errors = []
    
    with torch.no_grad():
        for batch_pc, batch_kp in test_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            predicted = model(batch_pc)
            
            # 计算误差（归一化空间）
            distances = torch.norm(predicted - batch_kp, dim=2)
            all_errors.extend(distances.cpu().numpy().flatten())
    
    avg_error = np.mean(all_errors)
    
    # 计算准确率（在归一化空间）
    accuracy_01 = np.mean(np.array(all_errors) < 0.1) * 100
    accuracy_02 = np.mean(np.array(all_errors) < 0.2) * 100
    accuracy_05 = np.mean(np.array(all_errors) < 0.5) * 100
    
    return avg_error, accuracy_01, accuracy_02, accuracy_05

def main():
    """主函数"""
    
    print("🎯 简单12点测试")
    print("快速验证12点子集的性能")
    print("=" * 60)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载数据集
    print("📊 加载高质量数据集...")
    data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints_57 = data['keypoints_57']
    sample_ids = data['sample_ids']
    
    # 创建12点子集
    subset_indices = create_simple_12point_subset()
    keypoints_12 = keypoints_57[:, subset_indices, :]
    
    print(f"✅ 数据集: {len(sample_ids)} 个样本")
    print(f"   点云形状: {point_clouds.shape}")
    print(f"   12点关键点形状: {keypoints_12.shape}")
    
    # 简单归一化
    normalized_pc, normalized_kp = simple_normalization(point_clouds, keypoints_12)
    
    # 数据划分
    indices = np.arange(len(normalized_pc))
    train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
    train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
    
    # 创建数据集
    train_dataset = Simple12Dataset(normalized_pc[train_indices], normalized_kp[train_indices])
    val_dataset = Simple12Dataset(normalized_pc[val_indices], normalized_kp[val_indices])
    test_dataset = Simple12Dataset(normalized_pc[test_indices], normalized_kp[test_indices])
    
    # 数据加载器
    batch_size = 8
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    
    print(f"📋 数据划分: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    
    # 创建模型
    model = Simple12PointNet(num_keypoints=12)
    
    # 训练模型
    model = train_simple_model(model, train_loader, val_loader, epochs=80, device=device)
    
    # 测试模型
    avg_error, acc_01, acc_02, acc_05 = test_simple_model(model, test_loader, device=device)
    
    print(f"\n🎯 简单12点模型结果 (归一化空间):")
    print(f"   平均误差: {avg_error:.4f}")
    print(f"   <0.1准确率: {acc_01:.1f}%")
    print(f"   <0.2准确率: {acc_02:.1f}%")
    print(f"   <0.5准确率: {acc_05:.1f}%")
    
    # 估算真实空间误差
    # 假设归一化因子约为100-200mm
    estimated_real_error = avg_error * 150  # 粗略估算
    
    print(f"\n📊 估算真实空间性能:")
    print(f"   估算误差: ~{estimated_real_error:.1f}mm")
    
    if estimated_real_error < 8.0:
        print(f"\n🎉 优秀！12点子集显著优于57点模型！")
        print(f"💡 证明了关键点筛选的有效性")
    elif estimated_real_error < 12.0:
        print(f"\n✅ 良好！12点子集有一定优势")
    else:
        print(f"\n⚠️ 12点子集性能有限")
    
    # 保存结果
    results = {
        'normalized_avg_error': float(avg_error),
        'normalized_accuracy_01': float(acc_01),
        'normalized_accuracy_02': float(acc_02),
        'normalized_accuracy_05': float(acc_05),
        'estimated_real_error_mm': float(estimated_real_error),
        'subset_indices': subset_indices,
        'comparison_with_57points': {
            '57_points_error': 10.89,
            'estimated_12_points_error': float(estimated_real_error),
            'improvement_estimate': f"{((10.89 - estimated_real_error) / 10.89 * 100):+.1f}%"
        }
    }
    
    with open('simple_12point_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 详细结果已保存: simple_12point_test_results.json")
    print(f"🎉 简单12点测试完成！")

if __name__ == "__main__":
    main()
