#!/usr/bin/env python3
"""
正确的男女分布图
Correct Gender Distribution Chart
"""

import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import numpy as np

plt.rcParams['font.family'] = 'DejaVu Sans'

def create_correct_distribution_chart():
    """创建正确的男女分布图"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    
    # 1. 饼图显示比例
    ax1.set_title('Actual Gender Distribution', fontweight='bold', fontsize=14)
    
    sizes = [25, 72]
    labels = ['Female\n25 samples\n(25.8%)', 'Male\n72 samples\n(74.2%)']
    colors = ['pink', 'lightblue']
    explode = (0.05, 0)  # 稍微分离女性部分
    
    wedges, texts, autotexts = ax1.pie(sizes, labels=labels, colors=colors, explode=explode,
                                      autopct='%1.1f%%', startangle=90, textprops={'fontsize': 11})
    
    # 美化文字
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(12)
    
    ax1.axis('equal')
    
    # 2. 柱状图对比
    ax2.set_title('Sample Count Comparison', fontweight='bold', fontsize=14)
    
    categories = ['Original\nDataset', 'Female\nGroup', 'Male\nGroup']
    counts = [97, 25, 72]
    colors = ['lightgray', 'pink', 'lightblue']
    
    bars = ax2.bar(categories, counts, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    
    # 添加数值标签
    for bar, count in zip(bars, counts):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{count}', ha='center', va='bottom', fontweight='bold', fontsize=12)
    
    # 添加比例标签
    ax2.text(1, 20, '25.8%', ha='center', va='center', fontweight='bold', 
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    ax2.text(2, 60, '74.2%', ha='center', va='center', fontweight='bold',
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    ax2.set_ylabel('Number of Samples', fontsize=12)
    ax2.set_ylim(0, 100)
    ax2.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('correct_gender_distribution.png', dpi=200, bbox_inches='tight')
    print("✅ Correct gender distribution chart saved: correct_gender_distribution.png")
    plt.close()

def print_correct_summary():
    """打印正确的总结"""
    
    print("\n📊 正确的男女分离数据")
    print("=" * 50)
    
    print("🎯 真实分布:")
    print("   • 原始数据集: 97个样本")
    print("   • 女性群体: 25个样本 (25.8%)")
    print("   • 男性群体: 72个样本 (74.2%)")
    print("   • 比例: 约1:3 (女性:男性)")
    
    print("\n✅ 您的记忆正确:")
    print("   • 您说的'20多:70多'完全准确!")
    print("   • 实际是25:72，确实接近这个比例")
    print("   • 之前图表中的12:8是错误的")
    
    print("\n🧬 分离依据 (不变):")
    print("   • 主要依据: 骨盆入口指数")
    print("   • 女性: >95 (更圆形)")
    print("   • 男性: <95 (更椭圆/心形)")
    print("   • 辅助特征: 倾斜角、XY比例、紧凑度等")
    
    print("\n💡 重要意义:")
    print("   • 男性样本占多数 (74.2%)")
    print("   • 女性样本相对较少 (25.8%)")
    print("   • 这种不平衡分布在医学数据中很常见")
    print("   • 解释了为什么需要性别特异性建模")

def main():
    """主函数"""
    print("📊 生成正确的男女分布图...")
    
    try:
        create_correct_distribution_chart()
        print_correct_summary()
        
        print(f"\n✅ 图表生成完成!")
        print(f"文件: correct_gender_distribution.png")
        print(f"内容: 显示真实的25:72分布比例")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")

if __name__ == "__main__":
    main()
