
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PointNet骨盆关键点检测 - 医生可解释性界面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #007bff;
        }
        
        .header h1 {
            color: #007bff;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .header .subtitle {
            color: #6c757d;
            font-size: 1.2em;
        }
        
        .performance-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .metric-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .metric-description {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 8px;
        }
        
        .clinical-assessment {
            background: #e8f5e8;
            border-left: 5px solid #28a745;
            padding: 20px;
            margin: 30px 0;
            border-radius: 8px;
        }
        
        .clinical-assessment h3 {
            color: #155724;
            margin-top: 0;
        }
        
        .samples-section {
            margin-top: 40px;
        }
        
        .sample-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }
        
        .sample-card {
            border: 2px solid #dee2e6;
            border-radius: 12px;
            padding: 20px;
            background-color: #fff;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .sample-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #007bff;
        }
        
        .sample-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .sample-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #007bff;
        }
        
        .quality-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .quality-excellent {
            background-color: #d4edda;
            color: #155724;
        }
        
        .quality-good {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .quality-fair {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .sample-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .metric {
            text-align: center;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        
        .metric-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }
        
        .metric-text {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .clinical-notes {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .clinical-notes h4 {
            margin-top: 0;
            color: #007bff;
        }
        
        .view-details {
            display: inline-block;
            margin-top: 15px;
            padding: 10px 20px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .view-details:hover {
            transform: scale(1.05);
            text-decoration: none;
            color: white;
        }
        
        .interpretation-guide {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .interpretation-guide h3 {
            color: #856404;
            margin-top: 0;
        }
        
        .guide-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .guide-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
        }
        
        .footer {
            margin-top: 50px;
            padding-top: 30px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 PointNet骨盆关键点检测</h1>
            <div class="subtitle">AI决策可解释性分析 - 医生专用界面</div>
        </div>
        
        <div class="performance-overview">
            <div class="metric-card">
                <div class="metric-value">2.93mm</div>
                <div class="metric-label">平均预测误差</div>
                <div class="metric-description">标准差: ±0.71mm</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">90.5%</div>
                <div class="metric-label">5mm准确率</div>
                <div class="metric-description">临床可接受标准</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">5</div>
                <div class="metric-label">分析样本数</div>
                <div class="metric-description">代表性测试案例</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">57</div>
                <div class="metric-label">关键点数量</div>
                <div class="metric-description">完整骨盆标志点</div>
            </div>
        </div>
        
        <div class="clinical-assessment">
            <h3>🩺 临床评估</h3>
            <p><strong>整体性能评价:</strong> 
            优秀 - 达到临床应用标准
            </p>
            <p><strong>可靠性:</strong> 模型在不同样本上表现稳定，预测一致性良好</p>
            <p><strong>临床适用性:</strong> 可用于辅助诊断和治疗规划，建议与医生判断结合使用</p>
        </div>
        
        <div class="interpretation-guide">
            <h3>📖 AI决策解释指南</h3>
            <div class="guide-grid">
                <div class="guide-item">
                    <h4>🎯 预测精度理解</h4>
                    <p>• <strong>平均误差 < 3mm:</strong> 优秀精度，可直接用于临床</p>
                    <p>• <strong>5mm准确率 > 90%:</strong> 符合临床应用标准</p>
                    <p>• <strong>误差分布:</strong> 大部分关键点误差在2-3mm范围内</p>
                </div>
                <div class="guide-item">
                    <h4>🔍 模型关注区域</h4>
                    <p>• AI主要关注骨盆边缘和角点区域</p>
                    <p>• 重要解剖标志点获得更多关注</p>
                    <p>• 关注模式符合解剖学知识</p>
                </div>
                <div class="guide-item">
                    <h4>⚠️ 注意事项</h4>
                    <p>• 边缘关键点可能误差较大</p>
                    <p>• 异常解剖结构需要特别验证</p>
                    <p>• 建议结合医生专业判断</p>
                </div>
                <div class="guide-item">
                    <h4>✅ 质量控制</h4>
                    <p>• 检查预测结果的解剖学合理性</p>
                    <p>• 关注异常高误差的关键点</p>
                    <p>• 验证整体几何结构的一致性</p>
                </div>
            </div>
        </div>
        
        <div class="samples-section">
            <h2>📊 样本详细分析</h2>
            <div class="sample-grid">

                <div class="sample-card">
                    <div class="sample-header">
                        <div class="sample-title">样本 1</div>
                        <div class="quality-badge quality-fair">一般</div>
                    </div>
                    
                    <div class="sample-metrics">
                        <div class="metric">
                            <div class="metric-number">3.83mm</div>
                            <div class="metric-text">平均误差</div>
                        </div>
                        <div class="metric">
                            <div class="metric-number">78.9%</div>
                            <div class="metric-text">5mm准确率</div>
                        </div>
                    </div>
                    
                    <div class="clinical-notes">
                        <h4>🩺 临床建议</h4>
                        <p>预测质量一般，需要医生仔细验证结果。</p>
                        
                        <h4>📐 几何分析</h4>
                        <p>• 预测中心偏移: 1.08mm</p>
                        <p>• 最大单点误差: 7.41mm</p>
                        <p>• 误差标准差: 1.34mm</p>
                    </div>
                    
                    <a href="sample_1_analysis.png" class="view-details" target="_blank">
                        📈 查看详细分析图表
                    </a>
                </div>

                <div class="sample-card">
                    <div class="sample-header">
                        <div class="sample-title">样本 2</div>
                        <div class="quality-badge quality-excellent">优秀</div>
                    </div>
                    
                    <div class="sample-metrics">
                        <div class="metric">
                            <div class="metric-number">2.32mm</div>
                            <div class="metric-text">平均误差</div>
                        </div>
                        <div class="metric">
                            <div class="metric-number">96.5%</div>
                            <div class="metric-text">5mm准确率</div>
                        </div>
                    </div>
                    
                    <div class="clinical-notes">
                        <h4>🩺 临床建议</h4>
                        <p>预测精度优秀，可直接用于临床决策支持。</p>
                        
                        <h4>📐 几何分析</h4>
                        <p>• 预测中心偏移: 0.50mm</p>
                        <p>• 最大单点误差: 7.04mm</p>
                        <p>• 误差标准差: 1.18mm</p>
                    </div>
                    
                    <a href="sample_2_analysis.png" class="view-details" target="_blank">
                        📈 查看详细分析图表
                    </a>
                </div>

                <div class="sample-card">
                    <div class="sample-header">
                        <div class="sample-title">样本 3</div>
                        <div class="quality-badge quality-good">良好</div>
                    </div>
                    
                    <div class="sample-metrics">
                        <div class="metric">
                            <div class="metric-number">3.68mm</div>
                            <div class="metric-text">平均误差</div>
                        </div>
                        <div class="metric">
                            <div class="metric-number">87.7%</div>
                            <div class="metric-text">5mm准确率</div>
                        </div>
                    </div>
                    
                    <div class="clinical-notes">
                        <h4>🩺 临床建议</h4>
                        <p>预测质量一般，需要医生仔细验证结果。</p>
                        
                        <h4>📐 几何分析</h4>
                        <p>• 预测中心偏移: 1.85mm</p>
                        <p>• 最大单点误差: 10.06mm</p>
                        <p>• 误差标准差: 1.75mm</p>
                    </div>
                    
                    <a href="sample_3_analysis.png" class="view-details" target="_blank">
                        📈 查看详细分析图表
                    </a>
                </div>

                <div class="sample-card">
                    <div class="sample-header">
                        <div class="sample-title">样本 4</div>
                        <div class="quality-badge quality-excellent">优秀</div>
                    </div>
                    
                    <div class="sample-metrics">
                        <div class="metric">
                            <div class="metric-number">2.07mm</div>
                            <div class="metric-text">平均误差</div>
                        </div>
                        <div class="metric">
                            <div class="metric-number">96.5%</div>
                            <div class="metric-text">5mm准确率</div>
                        </div>
                    </div>
                    
                    <div class="clinical-notes">
                        <h4>🩺 临床建议</h4>
                        <p>预测精度优秀，可直接用于临床决策支持。</p>
                        
                        <h4>📐 几何分析</h4>
                        <p>• 预测中心偏移: 0.70mm</p>
                        <p>• 最大单点误差: 6.12mm</p>
                        <p>• 误差标准差: 0.99mm</p>
                    </div>
                    
                    <a href="sample_4_analysis.png" class="view-details" target="_blank">
                        📈 查看详细分析图表
                    </a>
                </div>

                <div class="sample-card">
                    <div class="sample-header">
                        <div class="sample-title">样本 5</div>
                        <div class="quality-badge quality-good">良好</div>
                    </div>
                    
                    <div class="sample-metrics">
                        <div class="metric">
                            <div class="metric-number">2.72mm</div>
                            <div class="metric-text">平均误差</div>
                        </div>
                        <div class="metric">
                            <div class="metric-number">93.0%</div>
                            <div class="metric-text">5mm准确率</div>
                        </div>
                    </div>
                    
                    <div class="clinical-notes">
                        <h4>🩺 临床建议</h4>
                        <p>预测质量良好，建议结合医生判断使用。</p>
                        
                        <h4>📐 几何分析</h4>
                        <p>• 预测中心偏移: 0.26mm</p>
                        <p>• 最大单点误差: 6.90mm</p>
                        <p>• 误差标准差: 1.24mm</p>
                    </div>
                    
                    <a href="sample_5_analysis.png" class="view-details" target="_blank">
                        📈 查看详细分析图表
                    </a>
                </div>

            </div>
        </div>
        
        <div class="footer">
            <p>🤖 PointNet AI骨盆关键点检测系统</p>
            <p>📅 分析时间: /data1/home/<USER>/pjc/GCN/interpretability | 🔬 可解释性分析工具</p>
            <p>⚠️ 本系统仅供医疗辅助参考，最终诊断请以医生专业判断为准</p>
        </div>
    </div>
    
    <script>
        // 添加交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 样本卡片点击效果
            const sampleCards = document.querySelectorAll('.sample-card');
            sampleCards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 200);
                });
            });
            
            // 性能指标动画
            const metricValues = document.querySelectorAll('.metric-value');
            metricValues.forEach(metric => {
                const finalValue = metric.textContent;
                metric.textContent = '0';
                
                let current = 0;
                const target = parseFloat(finalValue);
                const increment = target / 50;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        metric.textContent = finalValue;
                        clearInterval(timer);
                    } else {
                        metric.textContent = current.toFixed(1);
                    }
                }, 20);
            });
        });
    </script>
</body>
</html>
