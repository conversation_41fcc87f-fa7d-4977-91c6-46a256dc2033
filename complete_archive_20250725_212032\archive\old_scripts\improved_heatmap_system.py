#!/usr/bin/env python3
"""
改进的热图关键点预测系统
修复热图可视化问题，提供更好的概率分布
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap

class ImprovedHeatmapDataset(Dataset):
    """改进的热图数据集"""
    
    def __init__(self, point_clouds, keypoints, sample_ids, num_points=8192, sigma=5.0, augment=False):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.sample_ids = sample_ids
        self.num_points = num_points
        self.sigma = sigma  # 增大sigma值
        self.augment = augment
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        pc = self.point_clouds[idx].copy()
        kp = self.keypoints[idx].copy()
        
        # 随机采样点云
        if len(pc) > self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=False)
            pc = pc[indices]
        elif len(pc) < self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=True)
            pc = pc[indices]
        
        if self.augment:
            pc, kp = self.apply_augmentation(pc, kp)
        
        # 生成改进的热图标签
        heatmaps = self.generate_improved_heatmaps(pc, kp)
        
        # 转换为tensor
        pc = torch.FloatTensor(pc).transpose(0, 1)  # [3, N]
        heatmaps = torch.FloatTensor(heatmaps).transpose(0, 1)  # [12, N]
        
        return pc, heatmaps, self.sample_ids[idx]
    
    def apply_augmentation(self, pc, kp):
        """数据增强"""
        if np.random.random() > 0.5:
            angle = np.random.uniform(-5, 5) * np.pi / 180
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            pc = pc @ rotation_matrix.T
            kp = kp @ rotation_matrix.T
        
        if np.random.random() > 0.5:
            scale = np.random.uniform(0.95, 1.05)
            pc *= scale
            kp *= scale
        
        return pc, kp
    
    def generate_improved_heatmaps(self, pc, kp):
        """生成改进的高斯热图标签"""
        num_points = len(pc)
        num_keypoints = len(kp)
        heatmaps = np.zeros((num_points, num_keypoints))
        
        for i, keypoint in enumerate(kp):
            # 计算每个点到关键点的距离
            distances = np.linalg.norm(pc - keypoint, axis=1)
            
            # 生成高斯分布
            heatmap = np.exp(-distances**2 / (2 * self.sigma**2))
            
            # 增强对比度 - 使用幂函数
            heatmap = np.power(heatmap, 0.5)  # 平方根增强
            
            # 确保有明显的峰值
            if np.max(heatmap) > 0:
                heatmap = heatmap / np.max(heatmap)
                # 进一步增强对比度
                heatmap = np.where(heatmap > 0.1, heatmap, heatmap * 0.1)
            
            heatmaps[:, i] = heatmap
        
        return heatmaps

class ImprovedHeatmapPointNet(nn.Module):
    """改进的热图预测PointNet"""
    
    def __init__(self, num_points=8192, num_keypoints=12):
        super(ImprovedHeatmapPointNet, self).__init__()
        
        # 特征提取层
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        
        # 全局特征
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.bn4 = nn.BatchNorm1d(512)
        
        # 热图预测头 - 改进架构
        self.conv5 = nn.Conv1d(512 + 256, 256, 1)
        self.conv6 = nn.Conv1d(256, 128, 1)
        self.conv7 = nn.Conv1d(128, 64, 1)
        self.conv8 = nn.Conv1d(64, num_keypoints, 1)  # 最终输出层
        
        self.bn5 = nn.BatchNorm1d(256)
        self.bn6 = nn.BatchNorm1d(128)
        self.bn7 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(0.2)  # 减少dropout
        self.relu = nn.ReLU()
        
        # 使用更强的激活函数组合
        self.final_activation = nn.Sequential(
            nn.Sigmoid(),
            nn.Softmax(dim=1)  # 在关键点维度上softmax，增强竞争
        )
    
    def forward(self, x):
        # x: [B, 3, N]
        batch_size, _, num_points = x.size()
        
        # 局部特征提取
        x1 = self.relu(self.bn1(self.conv1(x)))
        x2 = self.relu(self.bn2(self.conv2(x1)))
        x3 = self.relu(self.bn3(self.conv3(x2)))
        
        # 全局特征
        x4 = self.relu(self.bn4(self.conv4(x3)))
        global_feat = torch.max(x4, 2)[0]  # [B, 512]
        
        # 扩展全局特征到每个点
        global_feat_expanded = global_feat.unsqueeze(2).repeat(1, 1, num_points)  # [B, 512, N]
        
        # 融合全局和局部特征
        combined_feat = torch.cat([x3, global_feat_expanded], dim=1)  # [B, 768, N]
        
        # 热图预测 - 更深的网络
        x5 = self.relu(self.bn5(self.conv5(combined_feat)))
        x5 = self.dropout(x5)
        x6 = self.relu(self.bn6(self.conv6(x5)))
        x6 = self.dropout(x6)
        x7 = self.relu(self.bn7(self.conv7(x6)))
        
        # 最终输出
        heatmaps = self.conv8(x7)  # [B, 12, N]
        
        # 应用sigmoid激活
        heatmaps = torch.sigmoid(heatmaps)
        
        # 增强对比度 - 在每个关键点内部进行softmax
        heatmaps_enhanced = torch.zeros_like(heatmaps)
        for i in range(heatmaps.size(1)):  # 对每个关键点
            kp_heatmap = heatmaps[:, i:i+1, :]  # [B, 1, N]
            # 应用温度缩放的softmax
            temperature = 0.1  # 低温度增强峰值
            kp_heatmap_scaled = kp_heatmap / temperature
            kp_heatmap_softmax = torch.softmax(kp_heatmap_scaled.view(batch_size, -1), dim=1)
            heatmaps_enhanced[:, i, :] = kp_heatmap_softmax.view(batch_size, num_points)
        
        return heatmaps_enhanced

class WeightedFocalLoss(nn.Module):
    """加权Focal Loss"""
    
    def __init__(self, alpha=2, gamma=2, pos_weight=10.0):
        super(WeightedFocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.pos_weight = pos_weight
    
    def forward(self, pred, target):
        # pred: [B, 12, N], target: [B, 12, N]
        
        # 计算加权BCE loss
        bce_loss = nn.functional.binary_cross_entropy(pred, target, reduction='none')
        
        # 计算focal weight
        pt = torch.where(target > 0.5, pred, 1 - pred)
        focal_weight = self.alpha * (1 - pt) ** self.gamma
        
        # 对正样本加权
        pos_weight = torch.where(target > 0.5, self.pos_weight, 1.0)
        
        focal_loss = focal_weight * pos_weight * bce_loss
        
        return focal_loss.mean()

def train_improved_model(train_data, val_data, epochs=200, batch_size=2):
    """训练改进的模型"""
    print(f"\n🚀 **训练改进的热图预测模型**")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"   使用设备: {device}")
    
    # 创建数据集
    train_dataset = ImprovedHeatmapDataset(
        train_data['point_clouds'],
        train_data['keypoints'],
        train_data['sample_ids'],
        sigma=5.0,  # 增大sigma
        augment=True
    )
    
    val_dataset = ImprovedHeatmapDataset(
        val_data['point_clouds'],
        val_data['keypoints'],
        val_data['sample_ids'],
        sigma=5.0,
        augment=False
    )
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    # 创建改进的模型
    model = ImprovedHeatmapPointNet().to(device)
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=0.0005, weight_decay=1e-4)  # 降低学习率
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=15, factor=0.5)
    
    # 使用改进的损失函数
    criterion = WeightedFocalLoss(alpha=2, gamma=2, pos_weight=20.0)
    
    # 训练历史
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    patience_counter = 0
    
    print(f"   开始训练...")
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        
        for batch_idx, (pc, heatmaps, _) in enumerate(train_loader):
            pc, heatmaps = pc.to(device), heatmaps.to(device)
            
            optimizer.zero_grad()
            pred_heatmaps = model(pc)
            loss = criterion(pred_heatmaps, heatmaps)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for pc, heatmaps, _ in val_loader:
                pc, heatmaps = pc.to(device), heatmaps.to(device)
                pred_heatmaps = model(pc)
                loss = criterion(pred_heatmaps, heatmaps)
                val_loss += loss.item()
        
        val_loss /= len(val_loader)
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_improved_heatmap_model.pth')
        else:
            patience_counter += 1
        
        if epoch % 20 == 0:
            print(f"   Epoch {epoch}: Train Loss={train_loss:.6f}, Val Loss={val_loss:.6f}")
        
        # 早停
        if patience_counter >= 40:
            print(f"   早停于epoch {epoch}")
            break
    
    print(f"   改进模型训练完成! 最佳验证损失: {best_val_loss:.6f}")
    
    return model, train_losses, val_losses

def extract_improved_keypoints(heatmaps, point_cloud, threshold=0.1):
    """从改进的热图中提取关键点"""
    # heatmaps: [12, N], point_cloud: [N, 3]
    keypoints = []
    confidences = []
    
    for i in range(heatmaps.shape[0]):
        heatmap = heatmaps[i]
        
        # 找到最高概率的点作为关键点
        max_idx = np.argmax(heatmap)
        keypoint = point_cloud[max_idx]
        confidence = heatmap[max_idx]
        
        # 也可以使用加权平均（如果需要更平滑的结果）
        if confidence < threshold:
            # 如果最高概率太低，使用加权平均
            high_conf_mask = heatmap > (threshold * 0.5)
            if np.sum(high_conf_mask) > 0:
                weights = heatmap[high_conf_mask]
                points = point_cloud[high_conf_mask]
                keypoint = np.average(points, axis=0, weights=weights)
        
        keypoints.append(keypoint)
        confidences.append(confidence)
    
    return np.array(keypoints), np.array(confidences)

def test_improved_visualization():
    """测试改进的可视化"""
    print(f"\n📊 **测试改进的可视化**")
    
    # 加载数据
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    # 选择一个样本
    sample_idx = 0
    sample_id = sample_ids[sample_idx]
    point_cloud = point_clouds[sample_idx]
    true_keypoints = keypoints[sample_idx]
    
    # 采样点云
    if len(point_cloud) > 8192:
        indices = np.random.choice(len(point_cloud), 8192, replace=False)
        pc_sampled = point_cloud[indices]
    else:
        pc_sampled = point_cloud
    
    # 加载改进的模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = ImprovedHeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_improved_heatmap_model.pth', map_location=device))
        model.eval()
        print(f"   ✅ 改进模型加载成功")
    except Exception as e:
        print(f"   ❌ 改进模型加载失败: {e}")
        return
    
    # 预测热图
    pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
    
    with torch.no_grad():
        pred_heatmaps = model(pc_tensor)
    
    pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T  # [N, 12]
    
    # 分析改进后的热图
    print(f"   改进后热图统计:")
    for i in range(3):  # 只显示前3个关键点
        heatmap = pred_heatmaps_np[:, i]
        print(f"     关键点{i}: min={np.min(heatmap):.6f}, max={np.max(heatmap):.6f}, "
              f"mean={np.mean(heatmap):.6f}, std={np.std(heatmap):.6f}")
    
    # 创建对比可视化
    fig, axes = plt.subplots(2, 3, figsize=(18, 12), subplot_kw={'projection': '3d'})
    
    for i in range(3):
        heatmap = pred_heatmaps_np[:, i]
        
        # 原始热图
        ax1 = axes[0, i]
        scatter1 = ax1.scatter(pc_sampled[:, 0], pc_sampled[:, 1], pc_sampled[:, 2], 
                              c=heatmap, cmap='hot', s=1, alpha=0.8, vmin=0, vmax=np.max(heatmap))
        ax1.set_title(f'Improved Heatmap KP{i}')
        plt.colorbar(scatter1, ax=ax1, shrink=0.6)
        
        # 增强对比度版本
        ax2 = axes[1, i]
        heatmap_enhanced = np.power(heatmap, 0.3)  # 进一步增强
        scatter2 = ax2.scatter(pc_sampled[:, 0], pc_sampled[:, 1], pc_sampled[:, 2], 
                              c=heatmap_enhanced, cmap='plasma', s=1, alpha=0.8)
        ax2.set_title(f'Enhanced Contrast KP{i}')
        plt.colorbar(scatter2, ax=ax2, shrink=0.6)
    
    plt.tight_layout()
    plt.savefig('improved_heatmap_test.png', dpi=300, bbox_inches='tight')
    print(f"   📊 改进热图测试已保存: improved_heatmap_test.png")
    plt.close()

def main():
    """主函数"""
    print("🎯 **改进的3D医学点云热图关键点预测系统**")
    print("修复可视化问题，提供更好的概率分布")
    print("=" * 80)
    
    # 加载数据
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    # 创建数据分割
    n_samples = len(sample_ids)
    train_idx, val_idx = train_test_split(range(n_samples), test_size=0.2, random_state=42)
    
    train_data = {
        'sample_ids': sample_ids[train_idx],
        'point_clouds': point_clouds[train_idx],
        'keypoints': keypoints[train_idx]
    }
    
    val_data = {
        'sample_ids': sample_ids[val_idx],
        'point_clouds': point_clouds[val_idx],
        'keypoints': keypoints[val_idx]
    }
    
    # 训练改进的模型
    model, train_losses, val_losses = train_improved_model(train_data, val_data)
    
    # 测试改进的可视化
    test_improved_visualization()
    
    print(f"\n🎉 **改进的热图系统完成!**")
    print(f"✅ 使用更大的sigma值 (5.0)")
    print(f"✅ 改进的网络架构")
    print(f"✅ 加权Focal Loss")
    print(f"✅ 增强的对比度处理")
    print(f"✅ 更好的可视化效果")

if __name__ == "__main__":
    main()
