#!/usr/bin/env python3
"""
热图方法 vs 传统方法对比分析
比较基于热图的概率预测与传统单点预测的性能差异
"""

import numpy as np
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from torch.utils.data import Dataset, DataLoader
from heatmap_keypoint_system import HeatmapPointNet, HeatmapKeypointDataset, extract_keypoints_from_heatmaps

class TraditionalPointNet(nn.Module):
    """传统的单点预测PointNet"""
    
    def __init__(self, num_points=8192, num_keypoints=12):
        super(TraditionalPointNet, self).__init__()
        
        # 特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        
        # 回归头
        self.fc1 = nn.Linear(256, 128)
        self.fc2 = nn.Linear(128, 64)
        self.fc3 = nn.Linear(64, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
    
    def forward(self, x):
        # x: [B, 3, N]
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.relu(self.bn2(self.conv2(x)))
        x = self.bn3(self.conv3(x))
        
        # 全局最大池化
        x = torch.max(x, 2)[0]  # [B, 256]
        
        # 回归
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x

class TraditionalKeypointDataset(Dataset):
    """传统关键点数据集"""
    
    def __init__(self, point_clouds, keypoints, sample_ids, num_points=8192, augment=False):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.sample_ids = sample_ids
        self.num_points = num_points
        self.augment = augment
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        pc = self.point_clouds[idx].copy()
        kp = self.keypoints[idx].copy()
        
        # 随机采样点云
        if len(pc) > self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=False)
            pc = pc[indices]
        elif len(pc) < self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=True)
            pc = pc[indices]
        
        if self.augment:
            pc, kp = self.apply_augmentation(pc, kp)
        
        # 转换为tensor
        pc = torch.FloatTensor(pc).transpose(0, 1)  # [3, N]
        kp = torch.FloatTensor(kp.reshape(-1))  # [36] (12*3)
        
        return pc, kp, self.sample_ids[idx]
    
    def apply_augmentation(self, pc, kp):
        """数据增强"""
        # 随机旋转
        if np.random.random() > 0.5:
            angle = np.random.uniform(-5, 5) * np.pi / 180
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            pc = pc @ rotation_matrix.T
            kp = kp @ rotation_matrix.T
        
        # 随机缩放
        if np.random.random() > 0.5:
            scale = np.random.uniform(0.95, 1.05)
            pc *= scale
            kp *= scale
        
        return pc, kp

def train_traditional_model(train_data, val_data, epochs=150, batch_size=2):
    """训练传统模型"""
    print(f"\n🚀 **训练传统单点预测模型**")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建数据集
    train_dataset = TraditionalKeypointDataset(
        train_data['point_clouds'],
        train_data['keypoints'],
        train_data['sample_ids'],
        augment=True
    )
    
    val_dataset = TraditionalKeypointDataset(
        val_data['point_clouds'],
        val_data['keypoints'],
        val_data['sample_ids'],
        augment=False
    )
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    # 创建模型
    model = TraditionalPointNet().to(device)
    
    # 优化器和损失函数
    optimizer = torch.optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=20, factor=0.5)
    criterion = nn.MSELoss()
    
    # 训练历史
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    patience_counter = 0
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        
        for pc, kp, _ in train_loader:
            pc, kp = pc.to(device), kp.to(device)
            
            optimizer.zero_grad()
            pred = model(pc)
            loss = criterion(pred, kp)
            loss.backward()
            
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for pc, kp, _ in val_loader:
                pc, kp = pc.to(device), kp.to(device)
                pred = model(pc)
                loss = criterion(pred, kp)
                val_loss += loss.item()
        
        val_loss /= len(val_loader)
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        
        scheduler.step(val_loss)
        
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_traditional_model.pth')
        else:
            patience_counter += 1
        
        if epoch % 20 == 0:
            print(f"   Epoch {epoch}: Train Loss={train_loss:.6f}, Val Loss={val_loss:.6f}")
        
        if patience_counter >= 30:
            print(f"   早停于epoch {epoch}")
            break
    
    print(f"   传统模型训练完成! 最佳验证损失: {best_val_loss:.6f}")
    return model, train_losses, val_losses

def evaluate_traditional_model(model, val_data, device):
    """评估传统模型"""
    print(f"\n📊 **评估传统模型**")
    
    model.eval()
    
    val_dataset = TraditionalKeypointDataset(
        val_data['point_clouds'],
        val_data['keypoints'],
        val_data['sample_ids'],
        augment=False
    )
    
    val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False)
    
    total_error = 0.0
    total_samples = 0
    sample_results = {}
    
    with torch.no_grad():
        for pc, kp_true, sample_id in val_loader:
            pc, kp_true = pc.to(device), kp_true.to(device)
            kp_pred = model(pc)
            
            # 转换为numpy
            kp_true_np = kp_true.cpu().numpy().reshape(12, 3)
            kp_pred_np = kp_pred.cpu().numpy().reshape(12, 3)
            
            # 计算误差
            errors = []
            for i in range(12):
                error = np.linalg.norm(kp_pred_np[i] - kp_true_np[i])
                errors.append(error)
            
            avg_error = np.mean(errors)
            
            sample_results[sample_id[0]] = {
                'avg_error': avg_error,
                'errors': errors
            }
            
            total_error += avg_error
            total_samples += 1
    
    overall_avg_error = total_error / total_samples
    print(f"   传统模型平均误差: {overall_avg_error:.2f}mm")
    
    return sample_results, overall_avg_error

def compare_methods_performance(heatmap_results, traditional_results):
    """比较两种方法的性能"""
    print(f"\n📊 **方法性能对比**")
    
    # 提取共同样本的结果
    common_samples = set(heatmap_results.keys()) & set(traditional_results.keys())
    
    heatmap_errors = []
    traditional_errors = []
    heatmap_confidences = []
    
    for sample_id in common_samples:
        heatmap_errors.append(heatmap_results[sample_id]['avg_error'])
        traditional_errors.append(traditional_results[sample_id]['avg_error'])
        heatmap_confidences.append(heatmap_results[sample_id]['avg_confidence'])
    
    heatmap_errors = np.array(heatmap_errors)
    traditional_errors = np.array(traditional_errors)
    heatmap_confidences = np.array(heatmap_confidences)
    
    # 统计分析
    print(f"   对比样本数: {len(common_samples)}")
    print(f"   热图方法:")
    print(f"     平均误差: {np.mean(heatmap_errors):.2f}±{np.std(heatmap_errors):.2f}mm")
    print(f"     平均置信度: {np.mean(heatmap_confidences):.3f}±{np.std(heatmap_confidences):.3f}")
    print(f"   传统方法:")
    print(f"     平均误差: {np.mean(traditional_errors):.2f}±{np.std(traditional_errors):.2f}mm")
    
    # 配对t检验
    from scipy import stats
    t_stat, p_value = stats.ttest_rel(heatmap_errors, traditional_errors)
    print(f"   配对t检验: t={t_stat:.3f}, p={p_value:.3f}")
    
    if p_value < 0.05:
        if np.mean(heatmap_errors) < np.mean(traditional_errors):
            print(f"   ✅ 热图方法显著优于传统方法")
        else:
            print(f"   ❌ 传统方法显著优于热图方法")
    else:
        print(f"   ➡️ 两种方法无显著差异")
    
    return heatmap_errors, traditional_errors, heatmap_confidences

def create_comparison_visualizations(heatmap_errors, traditional_errors, heatmap_confidences):
    """创建对比可视化"""
    print(f"\n📊 **创建对比可视化**")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 误差分布对比
    ax1 = axes[0, 0]
    ax1.hist(heatmap_errors, bins=15, alpha=0.7, label='Heatmap Method', color='blue', density=True)
    ax1.hist(traditional_errors, bins=15, alpha=0.7, label='Traditional Method', color='red', density=True)
    ax1.axvline(np.mean(heatmap_errors), color='blue', linestyle='--', 
               label=f'Heatmap Mean: {np.mean(heatmap_errors):.2f}mm')
    ax1.axvline(np.mean(traditional_errors), color='red', linestyle='--', 
               label=f'Traditional Mean: {np.mean(traditional_errors):.2f}mm')
    ax1.set_xlabel('Average Error (mm)')
    ax1.set_ylabel('Density')
    ax1.set_title('Error Distribution Comparison')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 散点图对比
    ax2 = axes[0, 1]
    ax2.scatter(traditional_errors, heatmap_errors, alpha=0.7, s=50)
    
    # 添加对角线
    min_error = min(np.min(heatmap_errors), np.min(traditional_errors))
    max_error = max(np.max(heatmap_errors), np.max(traditional_errors))
    ax2.plot([min_error, max_error], [min_error, max_error], 'r--', alpha=0.8, label='Equal Performance')
    
    ax2.set_xlabel('Traditional Method Error (mm)')
    ax2.set_ylabel('Heatmap Method Error (mm)')
    ax2.set_title('Method Performance Scatter Plot')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 置信度 vs 误差关系
    ax3 = axes[1, 0]
    ax3.scatter(heatmap_confidences, heatmap_errors, alpha=0.7, s=50, color='green')
    
    # 计算相关系数
    correlation = np.corrcoef(heatmap_confidences, heatmap_errors)[0, 1]
    ax3.set_xlabel('Average Confidence')
    ax3.set_ylabel('Heatmap Method Error (mm)')
    ax3.set_title(f'Confidence vs Error (r={correlation:.3f})')
    ax3.grid(True, alpha=0.3)
    
    # 添加趋势线
    z = np.polyfit(heatmap_confidences, heatmap_errors, 1)
    p = np.poly1d(z)
    ax3.plot(heatmap_confidences, p(heatmap_confidences), "r--", alpha=0.8)
    
    # 4. 方法优势分析
    ax4 = axes[1, 1]
    
    # 计算每个样本的改进
    improvements = traditional_errors - heatmap_errors
    better_count = np.sum(improvements > 0)
    worse_count = np.sum(improvements < 0)
    equal_count = np.sum(improvements == 0)
    
    categories = ['Heatmap Better', 'Traditional Better', 'Equal']
    counts = [better_count, worse_count, equal_count]
    colors = ['green', 'red', 'gray']
    
    bars = ax4.bar(categories, counts, color=colors, alpha=0.7)
    ax4.set_ylabel('Number of Samples')
    ax4.set_title('Method Advantage Distribution')
    
    # 添加数值标签
    for bar, count in zip(bars, counts):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                str(count), ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('heatmap_vs_traditional_comparison.png', dpi=300, bbox_inches='tight')
    print(f"   📊 对比分析图已保存: heatmap_vs_traditional_comparison.png")
    plt.close()

def analyze_uncertainty_benefits(heatmap_results):
    """分析不确定性量化的好处"""
    print(f"\n🔍 **不确定性量化分析**")
    
    # 提取置信度和误差
    confidences = []
    errors = []
    
    for sample_id, result in heatmap_results.items():
        confidences.extend(result['confidences'])
        errors.extend(result['errors'])
    
    confidences = np.array(confidences)
    errors = np.array(errors)
    
    # 分析置信度与误差的关系
    correlation = np.corrcoef(confidences, errors)[0, 1]
    print(f"   置信度与误差相关性: {correlation:.3f}")
    
    # 按置信度分组分析
    high_conf_mask = confidences > 0.8
    medium_conf_mask = (confidences >= 0.5) & (confidences <= 0.8)
    low_conf_mask = confidences < 0.5
    
    print(f"   高置信度组 (>0.8): {np.sum(high_conf_mask)}个关键点")
    print(f"     平均误差: {np.mean(errors[high_conf_mask]):.2f}mm")
    print(f"   中置信度组 (0.5-0.8): {np.sum(medium_conf_mask)}个关键点")
    print(f"     平均误差: {np.mean(errors[medium_conf_mask]):.2f}mm")
    print(f"   低置信度组 (<0.5): {np.sum(low_conf_mask)}个关键点")
    print(f"     平均误差: {np.mean(errors[low_conf_mask]):.2f}mm")
    
    # 医学应用建议
    print(f"\n💡 **医学应用建议**:")
    print(f"   • 高置信度预测可直接使用")
    print(f"   • 中置信度预测需要专家审核")
    print(f"   • 低置信度预测建议人工重新标注")

def main():
    """主函数"""
    print("🔬 **热图方法 vs 传统方法对比分析**")
    print("比较基于热图的概率预测与传统单点预测")
    print("=" * 80)
    
    # 加载数据
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    # 创建数据分割
    n_samples = len(sample_ids)
    train_idx, val_idx = train_test_split(range(n_samples), test_size=0.2, random_state=42)
    
    train_data = {
        'sample_ids': sample_ids[train_idx],
        'point_clouds': point_clouds[train_idx],
        'keypoints': keypoints[train_idx]
    }
    
    val_data = {
        'sample_ids': sample_ids[val_idx],
        'point_clouds': point_clouds[val_idx],
        'keypoints': keypoints[val_idx]
    }
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 训练传统模型
    traditional_model, _, _ = train_traditional_model(train_data, val_data)
    traditional_model.load_state_dict(torch.load('best_traditional_model.pth'))
    
    # 评估传统模型
    traditional_results, traditional_avg_error = evaluate_traditional_model(
        traditional_model, val_data, device
    )
    
    # 加载热图模型结果（假设已经训练过）
    try:
        # 这里需要重新运行热图模型评估
        from heatmap_keypoint_system import HeatmapPointNet, evaluate_heatmap_model
        
        heatmap_model = HeatmapPointNet().to(device)
        heatmap_model.load_state_dict(torch.load('best_heatmap_model.pth'))
        
        heatmap_results, heatmap_avg_error = evaluate_heatmap_model(
            heatmap_model, val_data, device
        )
        
        # 性能对比
        heatmap_errors, traditional_errors, heatmap_confidences = compare_methods_performance(
            heatmap_results, traditional_results
        )
        
        # 创建可视化
        create_comparison_visualizations(heatmap_errors, traditional_errors, heatmap_confidences)
        
        # 不确定性分析
        analyze_uncertainty_benefits(heatmap_results)
        
        print(f"\n🎉 **对比分析完成!**")
        print(f"✅ 热图方法平均误差: {heatmap_avg_error:.2f}mm")
        print(f"✅ 传统方法平均误差: {traditional_avg_error:.2f}mm")
        print(f"✅ 热图方法提供了不确定性量化")
        print(f"✅ 生成了详细的对比分析")
        
    except Exception as e:
        print(f"❌ 热图模型加载失败: {e}")
        print(f"请先运行 heatmap_keypoint_system.py 训练热图模型")

if __name__ == "__main__":
    main()
