#!/usr/bin/env python3
"""
优化集成PointNet
Optimized Ensemble PointNet
专门针对医疗级5mm精度的集成优化
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pathlib import Path
from datetime import datetime
import json

class DiversePointNet(nn.Module):
    """多样化PointNet子网络"""
    
    def __init__(self, architecture_type, num_keypoints=19):
        super().__init__()
        self.num_keypoints = num_keypoints
        self.architecture_type = architecture_type
        
        if architecture_type == 'wide':
            # 宽网络：更多通道，较少层
            self.features = nn.Sequential(
                nn.Conv1d(3, 128, 1), nn.BatchNorm1d(128), nn.ReLU(), nn.Dropout(0.1),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(), nn.Dropout(0.1),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.<PERSON><PERSON>(), nn.Dropout(0.1),
                nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
            )
            
        elif architecture_type == 'deep':
            # 深网络：更多层，较少通道
            self.features = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(), nn.Dropout(0.05),
                nn.Conv1d(64, 64, 1), nn.BatchNorm1d(64), nn.ReLU(), nn.Dropout(0.05),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(), nn.Dropout(0.05),
                nn.Conv1d(128, 128, 1), nn.BatchNorm1d(128), nn.ReLU(), nn.Dropout(0.05),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(), nn.Dropout(0.05),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(), nn.Dropout(0.05),
                nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
            )
            
        elif architecture_type == 'residual':
            # 残差网络：带跳跃连接
            self.conv1 = nn.Sequential(nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU())
            self.conv2 = nn.Sequential(nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU())
            self.conv3 = nn.Sequential(nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU())
            self.conv4 = nn.Sequential(nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU())
            self.conv5 = nn.Sequential(nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU())
            
            # 跳跃连接的投影层
            self.skip1 = nn.Conv1d(64, 128, 1)
            self.skip2 = nn.Conv1d(128, 256, 1)
            self.skip3 = nn.Conv1d(256, 512, 1)
            
        elif architecture_type == 'attention':
            # 注意力网络
            self.conv_layers = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU()
            )
            
            # 自注意力层
            self.attention = nn.MultiheadAttention(
                embed_dim=512, num_heads=8, dropout=0.1, batch_first=True
            )
            
            self.final_conv = nn.Sequential(
                nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
            )
            
        else:  # 'standard'
            # 标准网络
            self.features = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(), nn.Dropout(0.1),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(), nn.Dropout(0.1),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(), nn.Dropout(0.1),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(), nn.Dropout(0.1),
                nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
            )
        
        # 回归器
        self.regressor = nn.Sequential(
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, num_keypoints * 3)
        )
        
    def forward(self, x):
        B, N, _ = x.shape
        x = x.transpose(1, 2)  # (B, 3, N)
        
        if self.architecture_type == 'residual':
            # 残差前向传播
            x1 = self.conv1(x)  # (B, 64, N)
            x2 = self.conv2(x1) + self.skip1(x1)  # (B, 128, N)
            x3 = self.conv3(x2) + self.skip2(x2)  # (B, 256, N)
            x4 = self.conv4(x3) + self.skip3(x3)  # (B, 512, N)
            features = self.conv5(x4)  # (B, 1024, N)
            
        elif self.architecture_type == 'attention':
            # 注意力前向传播
            x = self.conv_layers(x)  # (B, 512, N)
            x = x.transpose(1, 2)  # (B, N, 512)
            
            # 自注意力
            attended_x, _ = self.attention(x, x, x)
            x = x + attended_x  # 残差连接
            
            x = x.transpose(1, 2)  # (B, 512, N)
            features = self.final_conv(x)  # (B, 1024, N)
            
        else:
            # 标准前向传播
            features = self.features(x)  # (B, 1024, N)
        
        # 全局最大池化
        global_feat = torch.max(features, dim=2)[0]  # (B, 1024)
        
        # 关键点预测
        keypoints = self.regressor(global_feat)
        return keypoints.view(B, self.num_keypoints, 3)

class SmartEnsemblePointNet(nn.Module):
    """智能集成PointNet"""
    
    def __init__(self, num_keypoints=19, num_models=5):
        super().__init__()
        self.num_keypoints = num_keypoints
        self.num_models = num_models
        
        # 创建多样化的子网络
        architectures = ['standard', 'wide', 'deep', 'residual', 'attention']
        self.models = nn.ModuleList()
        
        for i in range(num_models):
            arch_type = architectures[i % len(architectures)]
            model = DiversePointNet(arch_type, num_keypoints)
            self.models.append(model)
        
        # 智能权重网络
        self.weight_network = nn.Sequential(
            nn.Linear(1024 * num_models, 512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, 128),
            nn.ReLU(),
            nn.Linear(128, num_models),
            nn.Softmax(dim=1)
        )
        
        # 特征提取器（用于权重计算）
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
            nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
            nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
            nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape
        
        # 获取所有模型的预测和特征
        predictions = []
        model_features = []
        
        for model in self.models:
            pred = model(point_cloud)
            predictions.append(pred)
            
            # 提取特征用于权重计算
            x = point_cloud.transpose(1, 2)
            feat = self.feature_extractor(x)
            global_feat = torch.max(feat, dim=2)[0]
            model_features.append(global_feat)
        
        # 计算智能权重
        combined_features = torch.cat(model_features, dim=1)  # (B, 1024*num_models)
        weights = self.weight_network(combined_features)  # (B, num_models)
        
        # 加权集成
        ensemble_pred = torch.zeros_like(predictions[0])
        for i, pred in enumerate(predictions):
            ensemble_pred += weights[:, i:i+1].unsqueeze(-1) * pred
        
        return ensemble_pred

class MedicalGradeTrainer:
    """医疗级训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def load_aligned_data(self):
        """加载对齐数据"""
        print("📦 加载F3对齐数据...")
        
        aligned_files = list(Path("data/processed").glob("f3_aligned_dataset_*.npz"))
        if not aligned_files:
            raise FileNotFoundError("未找到F3对齐数据集")
        
        latest_file = max(aligned_files, key=lambda x: x.stat().st_mtime)
        data = np.load(str(latest_file), allow_pickle=True)
        
        point_clouds = np.array(data['point_clouds'], dtype=np.float32)
        keypoints = np.array(data['keypoints'], dtype=np.float32)
        
        # 数据划分
        from sklearn.model_selection import train_test_split
        indices = np.arange(len(point_clouds))
        train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
        train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
        
        print(f"✅ 数据加载完成: {point_clouds.shape}")
        print(f"   训练: {len(train_indices)}, 验证: {len(val_indices)}, 测试: {len(test_indices)}")
        
        return self.data
    
    def medical_grade_augmentation(self, point_clouds, keypoints):
        """医疗级数据增强"""
        aug_pcs = []
        aug_kps = []
        
        for pc, kp in zip(point_clouds, keypoints):
            # 原始数据
            aug_pcs.append(pc)
            aug_kps.append(kp)
            
            # 极小旋转 (±0.2度)
            for _ in range(3):
                angle = np.random.uniform(-0.0035, 0.0035)  # ±0.2度
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                
                # 随机选择旋转轴
                axis = np.random.choice(['x', 'y', 'z'])
                if axis == 'x':
                    rotation = np.array([[1, 0, 0], [0, cos_a, -sin_a], [0, sin_a, cos_a]], dtype=np.float32)
                elif axis == 'y':
                    rotation = np.array([[cos_a, 0, sin_a], [0, 1, 0], [-sin_a, 0, cos_a]], dtype=np.float32)
                else:  # z轴
                    rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], dtype=np.float32)
                
                aug_pc = pc @ rotation.T
                aug_kp = kp @ rotation.T
                aug_pcs.append(aug_pc)
                aug_kps.append(aug_kp)
            
            # 极小噪声 (0.03mm)
            for _ in range(2):
                noise_pc = pc + np.random.normal(0, 0.03, pc.shape).astype(np.float32)
                aug_pcs.append(noise_pc)
                aug_kps.append(kp)
            
            # 极小缩放 (±0.3%)
            scale = np.random.uniform(0.997, 1.003)
            scaled_pc = pc * scale
            scaled_kp = kp * scale
            aug_pcs.append(scaled_pc)
            aug_kps.append(scaled_kp)
        
        return aug_pcs, aug_kps
    
    def train_medical_grade_ensemble(self, epochs=150, lr=0.0002):
        """训练医疗级集成模型"""
        print(f"\n🚀 训练医疗级智能集成PointNet")
        print(f"   目标: 冲击医疗级5mm精度")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        # 创建模型
        model = SmartEnsemblePointNet(num_keypoints=19, num_models=5).to(self.device)
        
        # 计算参数
        total_params = sum(p.numel() for p in model.parameters())
        print(f"   模型参数: {total_params:,}")
        
        # 优化器 - 更保守的设置
        optimizer = torch.optim.Adam(
            model.parameters(), 
            lr=lr, 
            weight_decay=5e-5,
            betas=(0.9, 0.999)
        )
        
        # 学习率调度器 - 更精细的调度
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.8, patience=20, 
            verbose=True, min_lr=1e-6
        )
        
        criterion = nn.MSELoss()
        
        # 训练状态
        best_val_error = float('inf')
        best_model_state = None
        patience = 0
        max_patience = 50
        
        train_history = []
        val_history = []
        
        for epoch in range(epochs):
            # 训练阶段
            model.train()
            epoch_losses = []
            
            # 使用更多训练数据
            k_shot = min(40, len(self.data['train']['point_clouds']))
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                k_shot, 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 医疗级增强
            aug_pcs, aug_kps = self.medical_grade_augmentation(train_pcs, train_kps)
            
            # 分批训练
            batch_size = 4  # 智能集成需要更多内存
            for i in range(0, len(aug_pcs), batch_size):
                batch_pcs = torch.FloatTensor(aug_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(aug_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                pred_kps = model(batch_pcs)
                loss = criterion(pred_kps, batch_kps)
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.step()
                epoch_losses.append(loss.item())
                
                del batch_pcs, batch_kps, pred_kps, loss
                torch.cuda.empty_cache()
            
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            train_history.append(avg_loss)
            
            # 验证
            if epoch % 3 == 0:  # 更频繁的验证
                val_error = self.evaluate_model(model, 'val')
                val_history.append(val_error)
                scheduler.step(val_error)
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                    patience = 0
                    
                    # 如果达到医疗级精度，保存检查点
                    if val_error <= 5.0:
                        self.save_checkpoint(model, val_error, epoch, "medical_grade_achieved")
                        print(f"🎉 医疗级精度达成！验证误差: {val_error:.3f}mm")
                else:
                    patience += 1
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm, "
                      f"LR={optimizer.param_groups[0]['lr']:.6f}, P={patience}")
                
                if patience >= max_patience:
                    print(f"早停在epoch {epoch}")
                    break
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)
            print(f"✅ 加载最佳模型 (验证误差: {best_val_error:.3f}mm)")
        
        self.model = model
        self.training_history = {
            'train_losses': train_history,
            'val_errors': val_history,
            'best_val_error': best_val_error
        }
        
        return model, best_val_error
    
    def evaluate_model(self, model, split='test'):
        """评估模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for i in range(0, len(pcs), 2):
                batch_pcs = torch.FloatTensor(pcs[i:i+2]).to(self.device)
                batch_kps = torch.FloatTensor(kps[i:i+2]).to(self.device)
                
                pred_kps = model(batch_pcs)
                
                for j in range(len(batch_pcs)):
                    error = torch.mean(torch.norm(pred_kps[j] - batch_kps[j], dim=1))
                    total_error += error.item()
                    num_samples += 1
                
                del batch_pcs, batch_kps, pred_kps
                torch.cuda.empty_cache()
        
        return total_error / num_samples if num_samples > 0 else float('inf')
    
    def test_time_augmentation(self, model, point_cloud, num_augmentations=10):
        """测试时增强"""
        model.eval()
        predictions = []
        
        with torch.no_grad():
            # 原始预测
            pred = model(point_cloud)
            predictions.append(pred)
            
            # 增强预测
            for _ in range(num_augmentations):
                # 极小旋转
                angle = np.random.uniform(-0.002, 0.002)  # ±0.1度
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = torch.tensor([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], 
                                      dtype=torch.float32, device=self.device)
                
                aug_pc = point_cloud @ rotation.T
                aug_pred = model(aug_pc)
                
                # 反向旋转预测结果
                aug_pred = aug_pred @ rotation
                predictions.append(aug_pred)
        
        # 平均预测
        ensemble_pred = torch.mean(torch.stack(predictions), dim=0)
        return ensemble_pred
    
    def save_checkpoint(self, model, val_error, epoch, tag=""):
        """保存检查点"""
        output_dir = Path("trained_models/medical_grade_ensemble")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"smart_ensemble_{val_error:.3f}mm_epoch{epoch}_{tag}_{timestamp}.pth"
        model_path = output_dir / filename
        
        torch.save({
            'model_state_dict': model.state_dict(),
            'validation_error': val_error,
            'epoch': epoch,
            'timestamp': timestamp,
            'model_config': {
                'num_keypoints': 19,
                'num_models': 5,
                'architecture': 'SmartEnsemblePointNet'
            }
        }, model_path)
        
        print(f"💾 检查点已保存: {model_path}")
        return model_path

def run_medical_grade_optimization():
    """运行医疗级优化实验"""
    print("🎯 医疗级智能集成PointNet优化")
    print("=" * 60)
    print("目标: 冲击医疗级5mm精度")
    print("策略: 智能集成 + 医疗级训练 + 测试时增强")
    
    trainer = MedicalGradeTrainer()
    data = trainer.load_aligned_data()
    
    # 训练智能集成模型
    model, val_error = trainer.train_medical_grade_ensemble(epochs=150, lr=0.0002)
    
    # 标准测试
    test_error = trainer.evaluate_model(model, 'test')
    
    # 测试时增强评估
    print("\n🔬 测试时增强评估...")
    model.eval()
    tta_errors = []
    
    with torch.no_grad():
        test_pcs = trainer.data['test']['point_clouds']
        test_kps = trainer.data['test']['keypoints']
        
        for i, (pc, kp) in enumerate(zip(test_pcs, test_kps)):
            pc_tensor = torch.FloatTensor(pc).unsqueeze(0).to(trainer.device)
            kp_tensor = torch.FloatTensor(kp).unsqueeze(0).to(trainer.device)
            
            # 测试时增强预测
            tta_pred = trainer.test_time_augmentation(model, pc_tensor, num_augmentations=15)
            
            error = torch.mean(torch.norm(tta_pred[0] - kp_tensor[0], dim=1))
            tta_errors.append(error.item())
            
            del pc_tensor, kp_tensor, tta_pred
            torch.cuda.empty_cache()
    
    tta_error = np.mean(tta_errors)
    
    # 结果分析
    baseline_error = 8.13
    simple_ensemble_error = 7.19
    
    print(f"\n📊 医疗级优化结果:")
    print("=" * 50)
    print(f"基线Point Transformer:     {baseline_error:.2f}mm")
    print(f"简单集成PointNet:         {simple_ensemble_error:.2f}mm")
    print(f"智能集成PointNet (验证):   {val_error:.2f}mm")
    print(f"智能集成PointNet (测试):   {test_error:.2f}mm")
    print(f"测试时增强结果:           {tta_error:.2f}mm")
    
    # 改进分析
    improvement_vs_baseline = (baseline_error - test_error) / baseline_error * 100
    improvement_vs_simple = (simple_ensemble_error - test_error) / simple_ensemble_error * 100
    tta_improvement = (test_error - tta_error) / test_error * 100
    
    print(f"\n📈 改进分析:")
    print(f"vs 基线改进:              {improvement_vs_baseline:+.1f}%")
    print(f"vs 简单集成改进:          {improvement_vs_simple:+.1f}%")
    print(f"测试时增强额外改进:       {tta_improvement:+.1f}%")
    
    # 医疗级精度评估
    best_error = min(test_error, tta_error)
    medical_target = 5.0
    
    print(f"\n🎯 医疗级精度评估:")
    print(f"医疗级目标:               {medical_target:.1f}mm")
    print(f"当前最佳结果:             {best_error:.2f}mm")
    
    if best_error <= medical_target:
        print("🎉 成功达到医疗级精度！")
        status = "医疗级精度达成"
    elif best_error <= 6.0:
        remaining = best_error - medical_target
        print(f"🎯 非常接近医疗级！还需改进{remaining:.2f}mm")
        status = f"接近医疗级，还需{remaining:.2f}mm"
    else:
        remaining = best_error - medical_target
        print(f"📈 距离医疗级还需改进{remaining:.2f}mm")
        status = f"需要改进{remaining:.2f}mm"
    
    # 保存最终模型
    final_model_path = trainer.save_checkpoint(model, best_error, 150, "final_optimized")
    
    # 保存实验结果
    results = {
        "experiment_timestamp": datetime.now().isoformat(),
        "experiment_type": "medical_grade_ensemble_optimization",
        "baseline_error": baseline_error,
        "simple_ensemble_error": simple_ensemble_error,
        "smart_ensemble_validation_error": float(val_error),
        "smart_ensemble_test_error": float(test_error),
        "test_time_augmentation_error": float(tta_error),
        "best_error": float(best_error),
        "medical_target": medical_target,
        "medical_grade_achieved": best_error <= medical_target,
        "status": status,
        "improvements": {
            "vs_baseline_percent": float(improvement_vs_baseline),
            "vs_simple_ensemble_percent": float(improvement_vs_simple),
            "tta_improvement_percent": float(tta_improvement)
        },
        "model_path": str(final_model_path)
    }
    
    results_dir = Path("results/medical_grade_experiments")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = results_dir / f"medical_grade_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 实验结果已保存: {results_file}")
    
    return trainer, results

if __name__ == "__main__":
    trainer, results = run_medical_grade_optimization()
