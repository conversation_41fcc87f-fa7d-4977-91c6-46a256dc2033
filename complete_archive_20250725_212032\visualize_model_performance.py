#!/usr/bin/env python3
"""
可视化模型性能 - 预测点vs真实点
Visualize Model Performance - Predicted vs Ground Truth
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import os

class HeatmapRegressionNet(nn.Module):
    """Heatmap回归网络 (与训练时相同)"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super(HeatmapRegressionNet, self).__init__()
        
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 点云特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        # 全局特征
        self.global_conv = nn.Conv1d(512, 1024, 1)
        
        # 特征融合
        self.fusion_conv1 = nn.Conv1d(1024 + 256, 512, 1)
        self.fusion_conv2 = nn.Conv1d(512, 256, 1)
        
        # Heatmap生成
        self.heatmap_conv1 = nn.Conv1d(256, 128, 1)
        self.heatmap_conv2 = nn.Conv1d(128, 64, 1)
        self.heatmap_conv3 = nn.Conv1d(64, num_keypoints, 1)
        
        # 激活函数
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.3)
        
        # Batch Normalization
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        # 点云特征提取
        x1 = self.relu(self.bn1(self.conv1(x)))
        x2 = self.relu(self.bn2(self.conv2(x1)))
        x3 = self.relu(self.bn3(self.conv3(x2)))
        x4 = self.relu(self.bn4(self.conv4(x3)))
        
        # 全局特征
        global_feat = self.relu(self.global_conv(x4))
        global_feat = torch.max(global_feat, 2, keepdim=True)[0]
        
        # 扩展全局特征
        global_feat_expanded = global_feat.repeat(1, 1, self.num_points)
        
        # 融合局部和全局特征
        combined_feat = torch.cat([x3, global_feat_expanded], 1)
        
        # 特征融合
        fused = self.relu(self.fusion_conv1(combined_feat))
        fused = self.dropout(fused)
        fused = self.relu(self.fusion_conv2(fused))
        
        # 生成热图
        heatmap = self.relu(self.heatmap_conv1(fused))
        heatmap = self.relu(self.heatmap_conv2(heatmap))
        heatmap = self.heatmap_conv3(heatmap)
        
        # 转置并应用softmax
        heatmap = heatmap.transpose(2, 1)
        
        # 对每个关键点的热图进行softmax
        heatmap_list = []
        for i in range(self.num_keypoints):
            hm_i = torch.softmax(heatmap[:, :, i], dim=1)
            heatmap_list.append(hm_i.unsqueeze(2))
        
        heatmap = torch.cat(heatmap_list, dim=2)
        
        return heatmap

def extract_keypoints_from_heatmap(heatmap, point_cloud):
    """从热图中提取关键点坐标"""
    
    batch_size, num_points, num_keypoints = heatmap.shape
    keypoints = torch.zeros(batch_size, num_keypoints, 3)
    
    for b in range(batch_size):
        for k in range(num_keypoints):
            # 加权平均方法
            weights = heatmap[b, :, k]
            weighted_coords = point_cloud[b] * weights.unsqueeze(1)
            keypoint = torch.sum(weighted_coords, dim=0) / torch.sum(weights)
            keypoints[b, k] = keypoint
    
    return keypoints

def load_model_and_data():
    """加载模型和测试数据"""
    
    print("🔥 加载最佳模型和测试数据...")
    
    # 加载模型
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    model = HeatmapRegressionNet(num_points=50000, num_keypoints=12)
    
    model_path = "best_heatmap_augmented_model.pth"
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return None, None, None, None
    
    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    print(f"✅ 模型加载成功 (验证误差: {checkpoint['val_error']:.2f}mm)")
    
    # 加载测试数据
    data_path = "f3_reduced_12kp_female_augmented.npz"
    if not os.path.exists(data_path):
        print(f"❌ 数据文件不存在: {data_path}")
        return None, None, None, None
    
    data = np.load(data_path, allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    
    # 使用测试集 (最后15%的数据)
    n_samples = len(point_clouds)
    n_train = int(n_samples * 0.7)
    n_val = int(n_samples * 0.15)
    test_start = n_train + n_val
    
    test_point_clouds = point_clouds[test_start:]
    test_keypoints = keypoints[test_start:]
    
    print(f"✅ 测试数据加载成功: {len(test_point_clouds)}个样本")
    
    return model, device, test_point_clouds, test_keypoints

def predict_keypoints(model, device, point_clouds, keypoints, num_samples=5):
    """预测关键点"""
    
    print(f"🎯 预测前{num_samples}个测试样本的关键点...")
    
    predictions = []
    ground_truths = []
    errors = []
    
    with torch.no_grad():
        for i in range(min(num_samples, len(point_clouds))):
            # 准备输入
            pc = torch.FloatTensor(point_clouds[i]).unsqueeze(0).to(device)
            gt_kp = keypoints[i]
            
            # 预测
            pred_heatmap = model(pc)
            pred_kp = extract_keypoints_from_heatmap(pred_heatmap.cpu(), pc.cpu())
            pred_kp = pred_kp.squeeze(0).numpy()
            
            # 计算误差
            error = np.mean(np.linalg.norm(pred_kp - gt_kp, axis=1))
            
            predictions.append(pred_kp)
            ground_truths.append(gt_kp)
            errors.append(error)
            
            print(f"  样本{i+1}: 误差 {error:.2f}mm")
    
    avg_error = np.mean(errors)
    print(f"✅ 平均预测误差: {avg_error:.2f}mm")
    
    return predictions, ground_truths, errors

def create_3d_visualization(predictions, ground_truths, errors, point_clouds):
    """创建3D可视化"""
    
    print("🎨 创建3D可视化图...")
    
    num_samples = len(predictions)
    
    # 创建大图
    fig = plt.figure(figsize=(20, 16))
    
    # 关键点名称 (F1/F2/F3各4个关键点)
    keypoint_names = [
        'F1-1', 'F1-2', 'F1-3', 'F1-4',
        'F2-1', 'F2-2', 'F2-3', 'F2-4', 
        'F3-1', 'F3-2', 'F3-3', 'F3-4'
    ]
    
    for i in range(num_samples):
        ax = fig.add_subplot(2, 3, i+1, projection='3d')
        
        # 获取数据
        pred_kp = predictions[i]
        gt_kp = ground_truths[i]
        pc = point_clouds[i]
        
        # 绘制点云 (采样显示)
        pc_sample = pc[::100]  # 每100个点取1个
        ax.scatter(pc_sample[:, 0], pc_sample[:, 1], pc_sample[:, 2], 
                  c='lightgray', s=0.5, alpha=0.3, label='Point Cloud')
        
        # 绘制真实关键点 (绿色)
        ax.scatter(gt_kp[:, 0], gt_kp[:, 1], gt_kp[:, 2], 
                  c='green', s=100, alpha=0.8, label='Ground Truth', marker='o')
        
        # 绘制预测关键点 (红色)
        ax.scatter(pred_kp[:, 0], pred_kp[:, 1], pred_kp[:, 2], 
                  c='red', s=100, alpha=0.8, label='Prediction', marker='^')
        
        # 绘制连接线显示误差
        for j in range(len(pred_kp)):
            ax.plot([gt_kp[j, 0], pred_kp[j, 0]], 
                   [gt_kp[j, 1], pred_kp[j, 1]], 
                   [gt_kp[j, 2], pred_kp[j, 2]], 
                   'b-', alpha=0.6, linewidth=1)
        
        # 设置标题和标签
        ax.set_title(f'Sample {i+1}\nError: {errors[i]:.2f}mm', fontsize=12, fontweight='bold')
        ax.set_xlabel('X (mm)')
        ax.set_ylabel('Y (mm)')
        ax.set_zlabel('Z (mm)')
        
        # 设置相同的坐标范围
        all_points = np.vstack([pred_kp, gt_kp])
        margin = 20
        ax.set_xlim(np.min(all_points[:, 0]) - margin, np.max(all_points[:, 0]) + margin)
        ax.set_ylim(np.min(all_points[:, 1]) - margin, np.max(all_points[:, 1]) + margin)
        ax.set_zlim(np.min(all_points[:, 2]) - margin, np.max(all_points[:, 2]) + margin)
        
        # 添加图例 (只在第一个子图)
        if i == 0:
            ax.legend(loc='upper right')
    
    # 添加总标题
    avg_error = np.mean(errors)
    fig.suptitle(f'Heatmap Model Performance Visualization\n'
                f'Average Error: {avg_error:.2f}mm | Model: 3.16mm Test Accuracy', 
                fontsize=16, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('model_performance_3d_visualization.png', dpi=300, bbox_inches='tight')
    print("✅ 3D可视化已保存: model_performance_3d_visualization.png")
    plt.close()

def create_error_analysis(predictions, ground_truths, errors):
    """创建误差分析图"""
    
    print("📊 创建误差分析图...")
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 总体误差分布
    ax1.bar(range(1, len(errors)+1), errors, color='skyblue', alpha=0.7)
    ax1.axhline(y=np.mean(errors), color='red', linestyle='--', linewidth=2, 
                label=f'Average: {np.mean(errors):.2f}mm')
    ax1.axhline(y=5.0, color='orange', linestyle='--', linewidth=2, 
                label='Medical Target: 5.0mm')
    ax1.set_xlabel('Sample Index')
    ax1.set_ylabel('Error (mm)')
    ax1.set_title('Per-Sample Error Distribution')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 每个关键点的平均误差
    keypoint_errors = []
    for kp_idx in range(12):
        kp_errs = []
        for i in range(len(predictions)):
            err = np.linalg.norm(predictions[i][kp_idx] - ground_truths[i][kp_idx])
            kp_errs.append(err)
        keypoint_errors.append(np.mean(kp_errs))
    
    keypoint_names = [f'F1-{i+1}' for i in range(4)] + \
                    [f'F2-{i+1}' for i in range(4)] + \
                    [f'F3-{i+1}' for i in range(4)]
    
    colors = ['red']*4 + ['green']*4 + ['blue']*4
    bars = ax2.bar(keypoint_names, keypoint_errors, color=colors, alpha=0.7)
    ax2.set_xlabel('Keypoint')
    ax2.set_ylabel('Average Error (mm)')
    ax2.set_title('Per-Keypoint Error Analysis')
    ax2.tick_params(axis='x', rotation=45)
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, err in zip(bars, keypoint_errors):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                f'{err:.1f}', ha='center', va='bottom', fontsize=9)
    
    # 3. F1/F2/F3区域误差对比
    f1_errors = keypoint_errors[:4]
    f2_errors = keypoint_errors[4:8]
    f3_errors = keypoint_errors[8:12]
    
    region_errors = [np.mean(f1_errors), np.mean(f2_errors), np.mean(f3_errors)]
    region_names = ['F1 Region', 'F2 Region', 'F3 Region']
    region_colors = ['red', 'green', 'blue']
    
    bars = ax3.bar(region_names, region_errors, color=region_colors, alpha=0.7)
    ax3.set_ylabel('Average Error (mm)')
    ax3.set_title('Anatomical Region Error Comparison')
    ax3.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, err in zip(bars, region_errors):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                f'{err:.2f}mm', ha='center', va='bottom', fontweight='bold')
    
    # 4. 性能对比
    methods = ['Original\nHeatmap\n(25 samples)', 'Augmented\nPointNet\n(250 samples)', 
               'Augmented\nHeatmap\n(250 samples)']
    method_errors = [4.88, 7.02, 3.16]
    method_colors = ['orange', 'red', 'green']
    
    bars = ax4.bar(methods, method_errors, color=method_colors, alpha=0.7)
    ax4.axhline(y=5.0, color='gray', linestyle='--', linewidth=2, 
                label='Medical Target: 5.0mm')
    ax4.set_ylabel('Test Error (mm)')
    ax4.set_title('Method Comparison')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, err in zip(bars, method_errors):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{err:.2f}mm', ha='center', va='bottom', fontweight='bold')
    
    # 标记最佳结果
    bars[2].set_edgecolor('black')
    bars[2].set_linewidth(3)
    
    plt.tight_layout()
    plt.savefig('model_error_analysis.png', dpi=300, bbox_inches='tight')
    print("✅ 误差分析图已保存: model_error_analysis.png")
    plt.close()

def main():
    """主函数"""
    
    print("🎨 可视化最佳Heatmap模型性能")
    print("🏆 模型精度: 3.16mm (35.3%提升)")
    print("=" * 80)
    
    # 加载模型和数据
    model, device, test_point_clouds, test_keypoints = load_model_and_data()
    if model is None:
        return
    
    # 预测关键点
    predictions, ground_truths, errors = predict_keypoints(
        model, device, test_point_clouds, test_keypoints, num_samples=5)
    
    # 创建3D可视化
    create_3d_visualization(predictions, ground_truths, errors, test_point_clouds[:5])
    
    # 创建误差分析
    create_error_analysis(predictions, ground_truths, errors)
    
    print(f"\n🎉 可视化完成!")
    print(f"📊 生成文件:")
    print(f"  • model_performance_3d_visualization.png - 3D预测vs真实对比")
    print(f"  • model_error_analysis.png - 详细误差分析")
    
    print(f"\n📈 关键结果:")
    print(f"  • 平均测试误差: {np.mean(errors):.2f}mm")
    print(f"  • 最佳样本误差: {np.min(errors):.2f}mm")
    print(f"  • 最差样本误差: {np.max(errors):.2f}mm")
    print(f"  • 医疗级状态: {'✅ 达标' if np.mean(errors) < 5.0 else '❌ 未达标'}")

if __name__ == "__main__":
    main()
