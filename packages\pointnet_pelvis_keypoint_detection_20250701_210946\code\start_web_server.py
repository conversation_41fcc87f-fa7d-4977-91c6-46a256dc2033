"""
启动本地Web服务器来查看3D可视化
解决VSCode Remote的文件访问问题
"""

import http.server
import socketserver
import webbrowser
import os
import threading
import time
from pathlib import Path

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器"""
    
    def end_headers(self):
        # 添加CORS头部，允许跨域访问
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def log_message(self, format, *args):
        # 简化日志输出
        return

def start_server(directory, port=8000):
    """启动Web服务器"""
    
    # 切换到指定目录
    os.chdir(directory)
    
    # 创建服务器
    handler = CustomHTTPRequestHandler
    
    try:
        with socketserver.TCPServer(("", port), handler) as httpd:
            print(f"🌐 Web服务器已启动!")
            print(f"📁 服务目录: {directory}")
            print(f"🔗 访问地址: http://localhost:{port}")
            print(f"📊 主页面: http://localhost:{port}/index.html")
            print(f"\n🎮 可用的3D可视化页面:")
            
            # 列出所有HTML文件
            html_files = list(Path('.').glob('*.html'))
            for html_file in sorted(html_files):
                if html_file.name == 'index.html':
                    print(f"  🏠 主页面: http://localhost:{port}/{html_file.name}")
                elif 'sample_' in html_file.name:
                    sample_num = html_file.name.split('_')[1]
                    print(f"  🎯 样本{sample_num}: http://localhost:{port}/{html_file.name}")
                elif 'comparison' in html_file.name:
                    print(f"  📊 对比仪表板: http://localhost:{port}/{html_file.name}")
                elif 'error' in html_file.name:
                    print(f"  📈 误差分析: http://localhost:{port}/{html_file.name}")
            
            print(f"\n💡 使用说明:")
            print(f"  • 在浏览器中打开上述任意链接")
            print(f"  • 推荐从主页面开始: http://localhost:{port}/index.html")
            print(f"  • 按 Ctrl+C 停止服务器")
            
            # 自动打开浏览器
            def open_browser():
                time.sleep(1)  # 等待服务器启动
                try:
                    webbrowser.open(f'http://localhost:{port}/index.html')
                    print(f"🚀 已自动打开浏览器")
                except:
                    print(f"⚠️ 无法自动打开浏览器，请手动访问: http://localhost:{port}/index.html")
            
            # 在后台线程中打开浏览器
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            print(f"\n🔄 服务器运行中...")
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 98:  # Address already in use
            print(f"❌ 端口 {port} 已被占用，尝试端口 {port + 1}")
            start_server(directory, port + 1)
        else:
            print(f"❌ 启动服务器失败: {e}")
    except KeyboardInterrupt:
        print(f"\n🛑 服务器已停止")

def find_web_directory():
    """查找Web可视化目录"""
    possible_dirs = [
        "output/web_3d_viewer",
        "output/simple_prediction_viz",
        "output/prediction_test"
    ]
    
    for dir_path in possible_dirs:
        if Path(dir_path).exists():
            html_files = list(Path(dir_path).glob('*.html'))
            if html_files:
                return Path(dir_path).absolute()
    
    return None

def main():
    """主函数"""
    print("🌐 启动3D可视化Web服务器")
    print("=" * 40)
    
    # 查找Web目录
    web_dir = find_web_directory()
    
    if web_dir is None:
        print("❌ 未找到Web可视化文件")
        print("请先运行以下命令生成可视化文件:")
        print("  python web_3d_viewer.py")
        print("  或")
        print("  python direct_3d_viewer.py")
        return
    
    print(f"✅ 找到Web目录: {web_dir}")
    
    # 列出可用文件
    html_files = list(web_dir.glob('*.html'))
    print(f"📁 发现 {len(html_files)} 个HTML文件:")
    for html_file in sorted(html_files):
        print(f"  • {html_file.name}")
    
    print(f"\n🚀 启动Web服务器...")
    
    try:
        start_server(str(web_dir))
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
