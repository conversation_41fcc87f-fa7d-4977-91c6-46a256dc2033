#!/usr/bin/env python3
"""
Improve F3 Training

Based on the results, complex models performed worse. Let's try:
1. Improved simple models with better training strategies
2. Better data preprocessing and augmentation
3. Advanced loss functions and training techniques
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import torch.nn.functional as F
import numpy as np
from pathlib import Path
import time
import json

class F3DatasetImproved(torch.utils.data.Dataset):
    """Improved F3 dataset with better preprocessing"""
    
    def __init__(self, data_dir, split, augment=False):
        self.split_dir = Path(data_dir) / split
        self.files = list(self.split_dir.glob('*_keypoints.npy'))
        self.augment = augment and split == 'train'
        print(f"{split}: {len(self.files)} samples")
        
        # Precompute normalization statistics
        self._compute_normalization_stats()
        
    def _compute_normalization_stats(self):
        """Compute dataset-wide normalization statistics"""
        all_points = []
        all_keypoints = []
        
        for kp_file in self.files:
            sample_id = kp_file.stem.replace('_keypoints', '')
            keypoints = np.load(kp_file)
            pc_file = kp_file.parent / f"{sample_id}_pointcloud.npy"
            pointcloud = np.load(pc_file)
            
            all_points.append(pointcloud)
            all_keypoints.append(keypoints)
        
        all_points = np.vstack(all_points)
        all_keypoints = np.vstack(all_keypoints)
        
        self.point_mean = np.mean(all_points, axis=0)
        self.point_std = np.std(all_points, axis=0)
        self.kp_mean = np.mean(all_keypoints, axis=0)
        self.kp_std = np.std(all_keypoints, axis=0)
        
        print(f"   数据归一化统计计算完成")
        
    def __len__(self):
        return len(self.files)
    
    def __getitem__(self, idx):
        kp_file = self.files[idx]
        sample_id = kp_file.stem.replace('_keypoints', '')
        
        keypoints = np.load(kp_file).astype(np.float32)
        pc_file = kp_file.parent / f"{sample_id}_pointcloud.npy"
        pointcloud = np.load(pc_file).astype(np.float32)
        
        # Normalize data
        pointcloud = (pointcloud - self.point_mean) / (self.point_std + 1e-8)
        keypoints = (keypoints - self.kp_mean) / (self.kp_std + 1e-8)
        
        # Data augmentation
        if self.augment:
            # More conservative augmentation for medical data
            # Random rotation (very small for medical precision)
            angle = np.random.uniform(-np.pi/24, np.pi/24)  # ±7.5 degrees
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ], dtype=np.float32)
            
            pointcloud = pointcloud @ rotation_matrix.T
            keypoints = keypoints @ rotation_matrix.T
            
            # Small translation in normalized space
            translation = np.random.uniform(-0.1, 0.1, 3).astype(np.float32)
            pointcloud += translation
            keypoints += translation
            
            # Small scaling
            scale = np.random.uniform(0.98, 1.02)
            pointcloud *= scale
            keypoints *= scale
            
            # Add small noise to point cloud only
            noise = np.random.normal(0, 0.01, pointcloud.shape).astype(np.float32)
            pointcloud += noise
        
        return torch.FloatTensor(pointcloud), torch.FloatTensor(keypoints)

class ImprovedSimpleNet(nn.Module):
    """Improved simple network with better architecture"""
    
    def __init__(self, num_keypoints=19):
        super().__init__()
        
        # Feature extraction with residual-like connections
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
        # Improved regression head
        self.fc1 = nn.Linear(512, 256)
        self.fc2 = nn.Linear(256, 128)
        self.fc3 = nn.Linear(128, 64)
        self.fc4 = nn.Linear(64, num_keypoints * 3)
        
        self.dropout1 = nn.Dropout(0.2)
        self.dropout2 = nn.Dropout(0.3)
        self.dropout3 = nn.Dropout(0.2)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # (B, 3, N)
        
        # Feature extraction with skip connections
        x1 = F.relu(self.bn1(self.conv1(x)))      # (B, 64, N)
        x2 = F.relu(self.bn2(self.conv2(x1)))     # (B, 128, N)
        x3 = F.relu(self.bn3(self.conv3(x2)))     # (B, 256, N)
        x4 = F.relu(self.bn4(self.conv4(x3)))     # (B, 512, N)
        
        # Global max pooling
        global_feat = torch.max(x4, 2)[0]  # (B, 512)
        
        # Improved regression with residual connections
        x = F.relu(self.fc1(global_feat))
        x = self.dropout1(x)
        
        x = F.relu(self.fc2(x))
        x = self.dropout2(x)
        
        x = F.relu(self.fc3(x))
        x = self.dropout3(x)
        
        x = self.fc4(x)
        
        return x.view(batch_size, 19, 3)

class AdaptiveLoss(nn.Module):
    """Adaptive loss that focuses on harder keypoints"""
    
    def __init__(self, alpha=2.0, beta=4.0):
        super().__init__()
        self.alpha = alpha
        self.beta = beta
        
    def forward(self, pred, target):
        # L2 distance for each keypoint
        distances = torch.norm(pred - target, dim=2)  # (B, 19)
        
        # Adaptive weighting based on distance
        weights = torch.pow(distances / distances.mean(), self.alpha)
        weights = torch.clamp(weights, 0.1, self.beta)
        
        # Weighted MSE loss
        mse_loss = torch.mean(weights * distances.pow(2))
        
        return mse_loss

def calculate_metrics(pred, target, denormalize_fn=None):
    """Calculate evaluation metrics with denormalization"""
    
    if denormalize_fn:
        pred = denormalize_fn(pred)
        target = denormalize_fn(target)
    
    pred_np = pred.cpu().numpy()
    target_np = target.cpu().numpy()
    
    distances = np.linalg.norm(pred_np - target_np, axis=-1)
    mean_error = np.mean(distances)
    
    acc_1mm = np.mean(distances <= 1.0) * 100
    acc_2mm = np.mean(distances <= 2.0) * 100
    acc_5mm = np.mean(distances <= 5.0) * 100
    acc_10mm = np.mean(distances <= 10.0) * 100
    
    return {
        'mean_error': mean_error,
        'acc_1mm': acc_1mm,
        'acc_2mm': acc_2mm,
        'acc_5mm': acc_5mm,
        'acc_10mm': acc_10mm
    }

def train_improved_model():
    """Train improved F3 model with better strategies"""
    
    print("🚀 **F3改进训练策略**")
    print("🎯 **策略: 数据归一化 + 改进架构 + 自适应损失 + 更好训练**")
    print("📊 **目标: 突破18.40mm基线，达到<10mm医疗级精度**")
    print("=" * 80)
    
    # Setup
    device = torch.device('cuda:2' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # Improved data loading
    train_dataset = F3DatasetImproved("F3SimpleDataset", "train", augment=True)
    val_dataset = F3DatasetImproved("F3SimpleDataset", "val", augment=False)
    
    train_loader = DataLoader(train_dataset, batch_size=6, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=6, shuffle=False, num_workers=2)
    
    # Denormalization function for metrics
    def denormalize_keypoints(kp_normalized):
        kp_np = kp_normalized.cpu().numpy()
        kp_denorm = kp_np * train_dataset.kp_std + train_dataset.kp_mean
        return torch.FloatTensor(kp_denorm)
    
    # Model
    model = ImprovedSimpleNet().to(device)
    
    # Advanced training setup
    criterion = AdaptiveLoss(alpha=1.5, beta=3.0)
    optimizer = optim.AdamW(model.parameters(), lr=0.002, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=2, eta_min=1e-6
    )
    
    print(f"🧠 改进模型参数: {sum(p.numel() for p in model.parameters()):,}")
    
    best_error = float('inf')
    history = []
    patience_counter = 0
    patience = 15
    
    print(f"\n🎯 开始改进训练")
    
    for epoch in range(50):
        # Training
        model.train()
        train_loss = 0
        
        for pc, kp in train_loader:
            pc, kp = pc.to(device), kp.to(device)
            
            optimizer.zero_grad()
            pred = model(pc)
            loss = criterion(pred, kp)
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # Validation
        model.eval()
        val_loss = 0
        all_pred = []
        all_target = []
        
        with torch.no_grad():
            for pc, kp in val_loader:
                pc, kp = pc.to(device), kp.to(device)
                pred = model(pc)
                loss = criterion(pred, kp)
                val_loss += loss.item()
                
                all_pred.append(pred.cpu())
                all_target.append(kp.cpu())
        
        val_loss /= len(val_loader)
        
        # Metrics with denormalization
        all_pred = torch.cat(all_pred, dim=0)
        all_target = torch.cat(all_target, dim=0)
        metrics = calculate_metrics(all_pred, all_target, denormalize_keypoints)
        
        # Scheduler step
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # Record
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'val_error': metrics['mean_error'],
            'acc_5mm': metrics['acc_5mm'],
            'acc_10mm': metrics['acc_10mm'],
            'learning_rate': current_lr
        })
        
        # Print progress
        if epoch % 5 == 0 or epoch < 10:
            print(f"Epoch {epoch+1:2d}: "
                  f"Train={train_loss:.3f}, Val={val_loss:.3f}, "
                  f"Error={metrics['mean_error']:.2f}mm, "
                  f"5mm={metrics['acc_5mm']:.1f}%, "
                  f"10mm={metrics['acc_10mm']:.1f}%, "
                  f"LR={current_lr:.2e}")
        
        # Save best model
        if metrics['mean_error'] < best_error:
            best_error = metrics['mean_error']
            patience_counter = 0
            
            torch.save({
                'model_state_dict': model.state_dict(),
                'epoch': epoch + 1,
                'error': best_error,
                'metrics': metrics,
                'normalization_stats': {
                    'point_mean': train_dataset.point_mean,
                    'point_std': train_dataset.point_std,
                    'kp_mean': train_dataset.kp_mean,
                    'kp_std': train_dataset.kp_std
                }
            }, 'best_f3_improved.pth')
            
            print(f"   ✅ 保存最佳模型: {best_error:.2f}mm")
        else:
            patience_counter += 1
        
        # Early stopping
        if patience_counter >= patience:
            print(f"   ⏹️ 早停: {patience}轮无改进")
            break
        
        # Success check
        if metrics['mean_error'] <= 10.0:
            print(f"   🎯 达到医疗级精度目标!")
            if metrics['mean_error'] <= 5.0:
                print(f"   🎉 达到优秀医疗级精度!")
                break
    
    print(f"\n🎯 **改进训练完成**")
    print(f"   最佳误差: {best_error:.2f}mm")
    print(f"   vs 原始基线: {(18.40 - best_error) / 18.40 * 100:+.1f}% 改进")
    
    if best_error <= 10.0:
        print(f"   🎉 成功达到医疗级精度!")
    elif best_error <= 15.0:
        print(f"   ✅ 显著改进，接近医疗级精度")
    else:
        print(f"   ⚠️ 有改进但仍需进一步优化")
    
    # Save results
    results = {
        'model_type': 'ImprovedSimpleNet',
        'best_error': best_error,
        'improvement_vs_baseline': (18.40 - best_error) / 18.40 * 100,
        'total_epochs': len(history),
        'history': history,
        'training_strategies': [
            'Data normalization',
            'Improved architecture',
            'Adaptive loss function',
            'AdamW optimizer',
            'Cosine annealing scheduler',
            'Gradient clipping',
            'Conservative augmentation'
        ]
    }
    
    with open('f3_improved_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📁 改进结果已保存: f3_improved_results.json")
    print(f"📁 最佳模型已保存: best_f3_improved.pth")
    
    return best_error

if __name__ == "__main__":
    try:
        error = train_improved_model()
        print(f"\n🎉 F3改进训练完成! 最终误差: {error:.2f}mm")
    except Exception as e:
        print(f"❌ 改进训练失败: {e}")
        import traceback
        traceback.print_exc()
