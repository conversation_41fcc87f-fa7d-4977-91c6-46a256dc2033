#!/bin/bash
# PointNet项目启动脚本

echo "🚀 PointNet骨盆关键点检测项目"
echo "================================"

# 检查环境
if ! command -v python &> /dev/null; then
    echo "❌ Python未安装"
    exit 1
fi

# 检查依赖
python -c "import torch, matplotlib, plotly" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️ 缺少依赖包，正在安装..."
    pip install torch torchvision matplotlib plotly tqdm
fi

echo "✅ 环境检查完成"

# 选择运行模式
echo ""
echo "请选择运行模式:"
echo "1. 测试预测"
echo "2. 3D可视化"
echo "3. Web服务器"
echo "4. 重新训练"

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo "🧪 运行预测测试..."
        python ../code/test_predictions.py
        ;;
    2)
        echo "🎮 启动3D可视化..."
        python ../code/direct_3d_viewer.py
        ;;
    3)
        echo "🌐 启动Web服务器..."
        python ../code/start_web_server.py
        ;;
    4)
        echo "🏋️ 开始重新训练..."
        python ../code/improved_training.py
        ;;
    *)
        echo "❌ 无效选择"
        ;;
esac
