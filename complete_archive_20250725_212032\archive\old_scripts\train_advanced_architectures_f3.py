#!/usr/bin/env python3
"""
Advanced Model Architectures for F3 Keypoint Detection

尝试多种先进架构和优化策略，目标突破5mm医疗精度
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import time
import json
import gc
import random
import math

# 设置随机种子
def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class StrictF3Dataset(Dataset):
    """严格测试集隔离的数据集 - 增强版"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points
        self.augment = augment
        
        np.random.seed(seed)
        
        # 加载数据
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        # 使用相同的测试集分割确保一致性
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        # 分离测试集
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            # 训练+验证集
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
        
        print(f"   {split}: {len(self.sample_ids)} 样本")
    
    def __len__(self):
        return len(self.sample_ids)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        # 智能下采样
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        # 增强的医疗级数据增强
        if self.augment:
            # 1. 多尺度旋转
            angle = np.random.uniform(-0.1, 0.1)  # ±5.7度
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
            point_cloud = point_cloud @ rotation.T
            keypoints = keypoints @ rotation.T
            
            # 2. 自适应平移
            translation = np.random.uniform(-0.5, 0.5, 3)  # ±0.5mm
            point_cloud += translation
            keypoints += translation
            
            # 3. 弹性变形 (模拟个体差异)
            if np.random.random() < 0.3:
                deform_strength = 0.02
                deform = np.random.normal(0, deform_strength, point_cloud.shape)
                point_cloud += deform
            
            # 4. 分层噪声
            noise_levels = [0.02, 0.05, 0.1]  # 不同强度噪声
            noise_level = np.random.choice(noise_levels)
            noise = np.random.normal(0, noise_level, point_cloud.shape)
            point_cloud += noise
            
            # 5. 点云密度变化
            if np.random.random() < 0.2:
                keep_ratio = np.random.uniform(0.9, 1.0)
                keep_num = int(len(point_cloud) * keep_ratio)
                keep_indices = np.random.choice(len(point_cloud), keep_num, replace=False)
                point_cloud = point_cloud[keep_indices]
                
                # 补充到目标点数
                if len(point_cloud) < self.num_points:
                    repeat_indices = np.random.choice(len(point_cloud), 
                                                    self.num_points - len(point_cloud), 
                                                    replace=True)
                    point_cloud = np.vstack([point_cloud, point_cloud[repeat_indices]])
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

class PointNetPlusPlus(nn.Module):
    """改进的PointNet++架构"""
    
    def __init__(self, num_keypoints: int = 19):
        super(PointNetPlusPlus, self).__init__()
        
        # Set Abstraction layers with different scales
        self.sa1 = SetAbstractionLayer(1024, 0.1, 32, 3, [32, 32, 64])
        self.sa2 = SetAbstractionLayer(256, 0.2, 32, 64, [64, 64, 128])
        self.sa3 = SetAbstractionLayer(64, 0.4, 32, 128, [128, 128, 256])
        self.sa4 = SetAbstractionLayer(16, 0.8, 32, 256, [256, 256, 512])
        
        # Feature Propagation layers
        self.fp4 = FeaturePropagationLayer(768, [256, 256])  # 512 + 256
        self.fp3 = FeaturePropagationLayer(384, [256, 256])  # 256 + 128
        self.fp2 = FeaturePropagationLayer(320, [256, 128])  # 256 + 64
        self.fp1 = FeaturePropagationLayer(128, [128, 128, 128])
        
        # Final layers
        self.conv1 = nn.Conv1d(128, 128, 1)
        self.bn1 = nn.BatchNorm1d(128)
        self.drop1 = nn.Dropout(0.5)
        self.conv2 = nn.Conv1d(128, num_keypoints * 3, 1)
        
    def forward(self, xyz):
        batch_size = xyz.size(0)
        
        # Set Abstraction
        l1_xyz, l1_points = self.sa1(xyz, None)
        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)
        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)
        l4_xyz, l4_points = self.sa4(l3_xyz, l3_points)
        
        # Feature Propagation
        l3_points = self.fp4(l3_xyz, l4_xyz, l3_points, l4_points)
        l2_points = self.fp3(l2_xyz, l3_xyz, l2_points, l3_points)
        l1_points = self.fp2(l1_xyz, l2_xyz, l1_points, l2_points)
        l0_points = self.fp1(xyz, l1_xyz, None, l1_points)
        
        # Final prediction
        feat = torch.relu(self.bn1(self.conv1(l0_points)))
        feat = self.drop1(feat)
        keypoints = self.conv2(feat)
        
        # Global average pooling
        keypoints = torch.mean(keypoints, dim=2)
        keypoints = keypoints.view(batch_size, 19, 3)
        
        return keypoints

class SetAbstractionLayer(nn.Module):
    """Set Abstraction Layer for PointNet++"""
    
    def __init__(self, npoint, radius, nsample, in_channel, mlp):
        super(SetAbstractionLayer, self).__init__()
        self.npoint = npoint
        self.radius = radius
        self.nsample = nsample
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        
        last_channel = in_channel
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm2d(out_channel))
            last_channel = out_channel
    
    def forward(self, xyz, points):
        batch_size, num_points, _ = xyz.size()
        
        # Simplified FPS
        if self.npoint < num_points:
            indices = torch.randperm(num_points)[:self.npoint]
            new_xyz = xyz[:, indices, :]
        else:
            new_xyz = xyz
        
        if points is None:
            points = xyz.transpose(1, 2).contiguous()
        
        # Apply MLPs
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            points = torch.relu(bn(conv(points.unsqueeze(-1)))).squeeze(-1)
        
        # Max pooling
        new_points = torch.max(points, dim=2)[0]
        new_points = new_points.unsqueeze(2).repeat(1, 1, new_xyz.size(1))
        
        return new_xyz, new_points

class FeaturePropagationLayer(nn.Module):
    """Feature Propagation Layer for PointNet++"""
    
    def __init__(self, in_channel, mlp):
        super(FeaturePropagationLayer, self).__init__()
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        
        last_channel = in_channel
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv1d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm1d(out_channel))
            last_channel = out_channel
    
    def forward(self, xyz1, xyz2, points1, points2):
        if points1 is not None:
            points = torch.cat([points1, points2], dim=1)
        else:
            points = points2
        
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            points = torch.relu(bn(conv(points)))
        
        return points

class DGCNN(nn.Module):
    """Dynamic Graph CNN for point cloud processing"""
    
    def __init__(self, num_keypoints: int = 19, k: int = 20):
        super(DGCNN, self).__init__()
        self.k = k
        
        self.bn1 = nn.BatchNorm2d(64)
        self.bn2 = nn.BatchNorm2d(64)
        self.bn3 = nn.BatchNorm2d(128)
        self.bn4 = nn.BatchNorm2d(256)
        self.bn5 = nn.BatchNorm1d(1024)
        
        self.conv1 = nn.Sequential(nn.Conv2d(6, 64, kernel_size=1, bias=False),
                                   self.bn1,
                                   nn.LeakyReLU(negative_slope=0.2))
        
        self.conv2 = nn.Sequential(nn.Conv2d(64*2, 64, kernel_size=1, bias=False),
                                   self.bn2,
                                   nn.LeakyReLU(negative_slope=0.2))
        
        self.conv3 = nn.Sequential(nn.Conv2d(64*2, 128, kernel_size=1, bias=False),
                                   self.bn3,
                                   nn.LeakyReLU(negative_slope=0.2))
        
        self.conv4 = nn.Sequential(nn.Conv2d(128*2, 256, kernel_size=1, bias=False),
                                   self.bn4,
                                   nn.LeakyReLU(negative_slope=0.2))
        
        self.conv5 = nn.Sequential(nn.Conv1d(512, 1024, kernel_size=1, bias=False),
                                   self.bn5,
                                   nn.LeakyReLU(negative_slope=0.2))
        
        self.linear1 = nn.Linear(1024, 512, bias=False)
        self.bn6 = nn.BatchNorm1d(512)
        self.dp1 = nn.Dropout(p=0.5)
        self.linear2 = nn.Linear(512, 256)
        self.bn7 = nn.BatchNorm1d(256)
        self.dp2 = nn.Dropout(p=0.5)
        self.linear3 = nn.Linear(256, num_keypoints * 3)
        
    def knn(self, x, k):
        inner = -2*torch.matmul(x.transpose(2, 1), x)
        xx = torch.sum(x**2, dim=1, keepdim=True)
        pairwise_distance = -xx - inner - xx.transpose(2, 1)
        
        idx = pairwise_distance.topk(k=k, dim=-1)[1]
        return idx
    
    def get_graph_feature(self, x, k, idx=None):
        batch_size = x.size(0)
        num_points = x.size(2)
        x = x.view(batch_size, -1, num_points)
        
        if idx is None:
            idx = self.knn(x, k=k)
        
        device = torch.device('cuda')
        
        idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1)*num_points
        
        idx = idx + idx_base
        
        idx = idx.view(-1)
        
        _, num_dims, _ = x.size()
        
        x = x.transpose(2, 1).contiguous()
        feature = x.view(batch_size*num_points, -1)[idx, :]
        feature = feature.view(batch_size, num_points, k, num_dims) 
        x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)
        
        feature = torch.cat((feature-x, x), dim=3).permute(0, 3, 1, 2).contiguous()
        
        return feature
    
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        x1 = self.get_graph_feature(x, k=self.k)
        x1 = self.conv1(x1)
        x1 = x1.max(dim=-1, keepdim=False)[0]
        
        x2 = self.get_graph_feature(x1, k=self.k)
        x2 = self.conv2(x2)
        x2 = x2.max(dim=-1, keepdim=False)[0]
        
        x3 = self.get_graph_feature(x2, k=self.k)
        x3 = self.conv3(x3)
        x3 = x3.max(dim=-1, keepdim=False)[0]
        
        x4 = self.get_graph_feature(x3, k=self.k)
        x4 = self.conv4(x4)
        x4 = x4.max(dim=-1, keepdim=False)[0]
        
        x = torch.cat((x1, x2, x3, x4), dim=1)
        
        x = self.conv5(x)
        x1 = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)
        x2 = F.adaptive_avg_pool1d(x, 1).view(batch_size, -1)
        x = torch.cat((x1, x2), 1)
        
        x = F.leaky_relu(self.bn6(self.linear1(x)), negative_slope=0.2)
        x = self.dp1(x)
        x = F.leaky_relu(self.bn7(self.linear2(x)), negative_slope=0.2)
        x = self.dp2(x)
        x = self.linear3(x)
        
        return x.view(batch_size, 19, 3)

class AttentionPointNet(nn.Module):
    """PointNet with attention mechanism"""
    
    def __init__(self, num_keypoints: int = 19):
        super(AttentionPointNet, self).__init__()
        
        # Point-wise feature extraction
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # Multi-head attention
        self.attention = nn.MultiheadAttention(1024, 8, dropout=0.1)
        
        # Global feature processing
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # (batch, 3, points)
        
        # Point-wise feature extraction
        x = torch.relu(self.bn1(self.conv1(x)))
        x = torch.relu(self.bn2(self.conv2(x)))
        x = torch.relu(self.bn3(self.conv3(x)))
        x = torch.relu(self.bn4(self.conv4(x)))
        x = torch.relu(self.bn5(self.conv5(x)))  # (batch, 1024, points)
        
        # Attention mechanism
        x = x.transpose(2, 1).transpose(0, 1)  # (points, batch, 1024)
        x_att, _ = self.attention(x, x, x)
        x = x + x_att  # Residual connection
        x = x.transpose(0, 1).transpose(1, 2)  # (batch, 1024, points)
        
        # Global max pooling
        x = torch.max(x, 2)[0]  # (batch, 1024)
        
        # Global feature processing
        x = torch.relu(self.bn_fc1(self.fc1(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc2(self.fc2(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc3(self.fc3(x)))
        x = self.dropout(x)
        x = self.fc4(x)
        
        return x.view(batch_size, 19, 3)

import torch.nn.functional as F

def main():
    """主函数 - 测试多种架构"""

    print("🚀 **先进架构对比实验**")
    print("🎯 **目标: 突破5mm医疗精度**")
    print("=" * 80)

    # 测试配置
    configs = [
        ('attention', 'mse'),      # 注意力机制 + MSE
        ('pointnet++', 'mse'),     # PointNet++ + MSE
        ('attention', 'adaptive'), # 注意力机制 + 自适应损失
    ]

    results = []

    for model_name, loss_name in configs:
        print(f"\n{'='*80}")
        print(f"🧪 **测试配置: {model_name.upper()} + {loss_name.upper()}**")
        print(f"{'='*80}")

        try:
            # 由于代码长度限制，这里只实现简化版本
            # 实际训练会在下一个文件中实现
            print(f"✅ 配置 {model_name} + {loss_name} 准备就绪")

        except Exception as e:
            print(f"❌ 配置失败: {e}")
            continue

    print(f"\n🎉 **架构对比实验准备完成!**")
    print(f"💡 **下一步**: 运行具体的训练脚本")

if __name__ == "__main__":
    main()
