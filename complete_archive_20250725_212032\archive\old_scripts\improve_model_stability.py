#!/usr/bin/env python3
"""
改进模型稳定性
实施多种策略提高预测一致性
"""

import numpy as np
import torch
import torch.nn.functional as F
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps

class StabilityEnhancer:
    """模型稳定性增强器"""
    
    def __init__(self, model, device):
        self.model = model
        self.device = device
        self.model.eval()
    
    def test_time_augmentation(self, point_cloud, num_augmentations=5):
        """测试时数据增强 - 多次预测取平均"""
        
        all_predictions = []
        all_confidences = []
        
        for i in range(num_augmentations):
            # 随机采样
            if len(point_cloud) > 8192:
                indices = np.random.choice(len(point_cloud), 8192, replace=False)
                pc_sampled = point_cloud[indices]
            else:
                pc_sampled = point_cloud.copy()
            
            # 轻微随机旋转 (±5度)
            if i > 0:  # 第一次不旋转
                angle = np.random.uniform(-5, 5) * np.pi / 180
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation_matrix = np.array([
                    [cos_a, -sin_a, 0],
                    [sin_a, cos_a, 0],
                    [0, 0, 1]
                ])
                pc_sampled = pc_sampled @ rotation_matrix.T
            
            # 预测
            pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                pred_heatmaps = self.model(pc_tensor)
            
            pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T
            pred_keypoints, confidences = extract_keypoints_from_heatmaps(
                pred_heatmaps_np.T, pc_sampled
            )
            
            # 如果进行了旋转，需要反向旋转预测结果
            if i > 0:
                pred_keypoints = pred_keypoints @ rotation_matrix
            
            all_predictions.append(pred_keypoints)
            all_confidences.append(confidences)
        
        # 计算平均预测
        avg_predictions = np.mean(all_predictions, axis=0)
        avg_confidences = np.mean(all_confidences, axis=0)
        
        # 计算预测一致性
        prediction_std = np.std(all_predictions, axis=0)
        consistency_score = 1.0 / (1.0 + np.mean(prediction_std))
        
        return avg_predictions, avg_confidences, consistency_score, all_predictions
    
    def confidence_weighted_prediction(self, point_cloud, confidence_threshold=0.5):
        """基于置信度的加权预测"""
        
        # 多次采样预测
        predictions = []
        confidences = []
        
        for _ in range(3):
            if len(point_cloud) > 8192:
                indices = np.random.choice(len(point_cloud), 8192, replace=False)
                pc_sampled = point_cloud[indices]
            else:
                pc_sampled = point_cloud
            
            pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                pred_heatmaps = self.model(pc_tensor)
            
            pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T
            pred_keypoints, conf = extract_keypoints_from_heatmaps(
                pred_heatmaps_np.T, pc_sampled
            )
            
            predictions.append(pred_keypoints)
            confidences.append(conf)
        
        # 基于置信度加权平均
        predictions = np.array(predictions)  # [3, 12, 3]
        confidences = np.array(confidences)  # [3, 12]
        
        # 归一化置信度作为权重
        weights = confidences / np.sum(confidences, axis=0, keepdims=True)
        
        # 加权平均
        weighted_predictions = np.sum(predictions * weights[:, :, np.newaxis], axis=0)
        avg_confidences = np.mean(confidences, axis=0)
        
        return weighted_predictions, avg_confidences
    
    def adaptive_sampling_prediction(self, point_cloud):
        """自适应采样预测"""
        
        # 根据点云大小调整采样策略
        pc_size = len(point_cloud)
        
        if pc_size > 20000:
            # 大点云：多次采样，每次采样更多点
            sample_sizes = [10240, 8192, 6144]
        elif pc_size > 10000:
            # 中等点云：标准采样
            sample_sizes = [8192, 6144, 4096]
        else:
            # 小点云：使用全部点
            sample_sizes = [pc_size]
        
        predictions = []
        confidences = []
        
        for sample_size in sample_sizes:
            if sample_size >= pc_size:
                pc_sampled = point_cloud
            else:
                indices = np.random.choice(pc_size, sample_size, replace=False)
                pc_sampled = point_cloud[indices]
            
            pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                pred_heatmaps = self.model(pc_tensor)
            
            pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T
            pred_keypoints, conf = extract_keypoints_from_heatmaps(
                pred_heatmaps_np.T, pc_sampled
            )
            
            predictions.append(pred_keypoints)
            confidences.append(conf)
        
        # 简单平均
        avg_predictions = np.mean(predictions, axis=0)
        avg_confidences = np.mean(confidences, axis=0)
        
        return avg_predictions, avg_confidences

def evaluate_stability_methods(enhancer, data, num_samples=5):
    """评估不同稳定性方法"""
    
    sample_ids = data['sample_ids']
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    
    methods = {
        'Original': 'original',
        'Test-Time Aug': 'tta',
        'Confidence Weighted': 'confidence',
        'Adaptive Sampling': 'adaptive'
    }
    
    results = {method: [] for method in methods.keys()}
    
    for i in range(min(num_samples, len(sample_ids))):
        sample_id = sample_ids[i]
        point_cloud = point_clouds[i]
        true_keypoints = keypoints[i]
        
        print(f"📊 Testing sample {i+1}/{num_samples}: {sample_id}")
        
        for method_name, method_type in methods.items():
            
            if method_type == 'original':
                # 原始方法
                if len(point_cloud) > 8192:
                    indices = np.random.choice(len(point_cloud), 8192, replace=False)
                    pc_sampled = point_cloud[indices]
                else:
                    pc_sampled = point_cloud
                
                pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(enhancer.device)
                
                with torch.no_grad():
                    pred_heatmaps = enhancer.model(pc_tensor)
                
                pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T
                pred_keypoints, confidences = extract_keypoints_from_heatmaps(
                    pred_heatmaps_np.T, pc_sampled
                )
                
            elif method_type == 'tta':
                # 测试时增强
                pred_keypoints, confidences, consistency, _ = enhancer.test_time_augmentation(point_cloud)
                
            elif method_type == 'confidence':
                # 置信度加权
                pred_keypoints, confidences = enhancer.confidence_weighted_prediction(point_cloud)
                
            elif method_type == 'adaptive':
                # 自适应采样
                pred_keypoints, confidences = enhancer.adaptive_sampling_prediction(point_cloud)
            
            # 计算误差
            errors = [np.linalg.norm(pred_keypoints[j] - true_keypoints[j]) 
                     for j in range(len(true_keypoints))]
            
            avg_error = np.mean(errors)
            accuracy_5mm = np.sum(np.array(errors) <= 5) / len(errors) * 100
            
            results[method_name].append({
                'sample_id': sample_id,
                'avg_error': avg_error,
                'accuracy_5mm': accuracy_5mm,
                'errors': errors
            })
            
            print(f"   {method_name:15}: {avg_error:.2f}mm ({accuracy_5mm:.1f}% ≤5mm)")
    
    return results

def compare_methods(results):
    """比较不同方法的效果"""
    
    print(f"\n📊 Method Comparison Summary")
    print("=" * 60)
    
    for method_name, method_results in results.items():
        avg_errors = [r['avg_error'] for r in method_results]
        accuracies = [r['accuracy_5mm'] for r in method_results]
        
        mean_error = np.mean(avg_errors)
        std_error = np.std(avg_errors)
        mean_accuracy = np.mean(accuracies)
        
        print(f"{method_name:15}: {mean_error:.2f}±{std_error:.2f}mm, {mean_accuracy:.1f}% ≤5mm")
    
    # 找出最佳方法
    best_method = min(results.keys(), 
                     key=lambda m: np.mean([r['avg_error'] for r in results[m]]))
    
    print(f"\n🏆 Best Method: {best_method}")
    
    return best_method

def main():
    """主函数"""
    print("🔧 Model Stability Improvement")
    print("Testing multiple strategies to improve prediction consistency")
    print("=" * 60)
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # 创建稳定性增强器
    enhancer = StabilityEnhancer(model, device)
    
    # 评估不同方法
    results = evaluate_stability_methods(enhancer, male_data, num_samples=5)
    
    # 比较方法效果
    best_method = compare_methods(results)
    
    print(f"\n💡 Recommendations:")
    print(f"1. Use {best_method} for improved stability")
    print("2. Implement ensemble of multiple sampling strategies")
    print("3. Add confidence-based filtering")
    print("4. Consider model retraining with stability-focused loss")

if __name__ == "__main__":
    main()
