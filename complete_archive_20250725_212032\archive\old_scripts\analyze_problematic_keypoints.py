#!/usr/bin/env python3
"""
分析问题关键点
识别哪些关键点的预测效果最差，分析原因
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns
from basic_19keypoints_system import BasicHeatmapPointNet19, extract_keypoints_from_heatmaps_19

# 关键点名称和描述
KEYPOINT_INFO = {
    # 原始12个关键点
    0: {"name": "L-ASIS", "desc": "Left Anterior Superior Iliac Spine", "type": "original", "anatomy": "prominent"},
    1: {"name": "R-ASIS", "desc": "Right Anterior Superior Iliac Spine", "type": "original", "anatomy": "prominent"},
    2: {"name": "L-PSIS", "desc": "Left Posterior Superior Iliac Spine", "type": "original", "anatomy": "prominent"},
    3: {"name": "R-PSIS", "desc": "Right Posterior Superior Iliac Spine", "type": "original", "anatomy": "prominent"},
    4: {"name": "L-IC", "desc": "Left Iliac Crest", "type": "original", "anatomy": "edge"},
    5: {"name": "R-IC", "desc": "Right Iliac Crest", "type": "original", "anatomy": "edge"},
    6: {"name": "SP", "desc": "Sacral Promontory", "type": "original", "anatomy": "prominent"},
    7: {"name": "L-SIJ", "desc": "Left Sacroiliac Joint", "type": "original", "anatomy": "joint"},
    8: {"name": "R-SIJ", "desc": "Right Sacroiliac Joint", "type": "original", "anatomy": "joint"},
    9: {"name": "L-IS", "desc": "Left Ischial Spine", "type": "original", "anatomy": "spine"},
    10: {"name": "R-IS", "desc": "Right Ischial Spine", "type": "original", "anatomy": "spine"},
    11: {"name": "CT", "desc": "Coccyx Tip", "type": "original", "anatomy": "tip"},
    
    # 新增7个关键点
    12: {"name": "ASIS-Mid", "desc": "ASIS Midpoint", "type": "interpolated", "anatomy": "midpoint"},
    13: {"name": "PSIS-Mid", "desc": "PSIS Midpoint", "type": "interpolated", "anatomy": "midpoint"},
    14: {"name": "IC-Mid", "desc": "Iliac Crest Midpoint", "type": "interpolated", "anatomy": "midpoint"},
    15: {"name": "SIJ-Mid", "desc": "SIJ Midpoint", "type": "interpolated", "anatomy": "midpoint"},
    16: {"name": "IS-Mid", "desc": "Ischial Spine Midpoint", "type": "interpolated", "anatomy": "midpoint"},
    17: {"name": "SP-CT-Mid", "desc": "SP-CT Midpoint", "type": "interpolated", "anatomy": "midpoint"},
    18: {"name": "Centroid", "desc": "Pelvis Centroid", "type": "interpolated", "anatomy": "centroid"}
}

def analyze_keypoint_performance(model, point_clouds, keypoints_19, sample_ids, device, num_samples=20):
    """分析每个关键点的性能"""
    
    print(f"🔍 Analyzing keypoint performance on {num_samples} samples...")
    
    all_errors = []
    all_confidences = []
    all_sample_ids = []
    
    for i in range(min(num_samples, len(point_clouds))):
        sample_id = sample_ids[i]
        point_cloud = point_clouds[i]
        true_keypoints = keypoints_19[i]
        
        # 采样点云
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            pc_sampled = point_cloud[indices]
        else:
            pc_sampled = point_cloud
        
        pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
        
        # 预测
        with torch.no_grad():
            pred_heatmaps = model(pc_tensor)
        
        pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze()
        pred_keypoints, confidences = extract_keypoints_from_heatmaps_19(pred_heatmaps_np, pc_sampled)
        
        # 计算每个关键点的误差
        sample_errors = []
        for j in range(19):
            error = np.linalg.norm(pred_keypoints[j] - true_keypoints[j])
            sample_errors.append(error)
        
        all_errors.append(sample_errors)
        all_confidences.append(confidences)
        all_sample_ids.append(sample_id)
        
        if (i + 1) % 5 == 0:
            print(f"   Processed {i+1}/{num_samples} samples...")
    
    # 转换为numpy数组
    all_errors = np.array(all_errors)  # [num_samples, 19]
    all_confidences = np.array(all_confidences)  # [num_samples, 19]
    
    return all_errors, all_confidences, all_sample_ids

def identify_problematic_keypoints(all_errors, all_confidences, threshold_error=8.0, threshold_confidence=0.3):
    """识别问题关键点"""
    
    print(f"\n🚨 Identifying problematic keypoints...")
    print(f"   Error threshold: {threshold_error}mm")
    print(f"   Confidence threshold: {threshold_confidence}")
    
    # 计算每个关键点的统计信息
    keypoint_stats = {}
    
    for kp_idx in range(19):
        errors = all_errors[:, kp_idx]
        confidences = all_confidences[:, kp_idx]
        
        stats = {
            'mean_error': np.mean(errors),
            'std_error': np.std(errors),
            'max_error': np.max(errors),
            'min_error': np.min(errors),
            'median_error': np.median(errors),
            'mean_confidence': np.mean(confidences),
            'std_confidence': np.std(confidences),
            'high_error_rate': np.sum(errors > threshold_error) / len(errors) * 100,
            'low_confidence_rate': np.sum(confidences < threshold_confidence) / len(confidences) * 100,
            'info': KEYPOINT_INFO[kp_idx]
        }
        
        keypoint_stats[kp_idx] = stats
    
    # 按平均误差排序，找出最差的关键点
    sorted_by_error = sorted(keypoint_stats.items(), key=lambda x: x[1]['mean_error'], reverse=True)
    
    print(f"\n📊 Keypoint Performance Ranking (Worst to Best):")
    print("=" * 80)
    print(f"{'Rank':<4} {'Name':<12} {'Type':<12} {'Error':<12} {'Confidence':<12} {'High Error %':<12}")
    print("-" * 80)
    
    problematic_keypoints = []
    
    for rank, (kp_idx, stats) in enumerate(sorted_by_error):
        info = stats['info']
        
        # 判断是否为问题关键点
        is_problematic = (stats['mean_error'] > threshold_error or 
                         stats['high_error_rate'] > 30 or 
                         stats['low_confidence_rate'] > 30)
        
        if is_problematic:
            problematic_keypoints.append(kp_idx)
        
        status = "🚨" if is_problematic else "✅"
        
        print(f"{rank+1:<4} {info['name']:<12} {info['type']:<12} "
              f"{stats['mean_error']:.2f}±{stats['std_error']:.2f}mm {stats['mean_confidence']:.3f}±{stats['std_confidence']:.3f} "
              f"{stats['high_error_rate']:.1f}% {status}")
    
    return keypoint_stats, problematic_keypoints

def analyze_keypoint_patterns(keypoint_stats, all_errors):
    """分析关键点错误模式"""
    
    print(f"\n🔍 Analyzing Error Patterns:")
    print("=" * 50)
    
    # 按类型分组分析
    original_errors = []
    interpolated_errors = []
    
    for kp_idx, stats in keypoint_stats.items():
        if stats['info']['type'] == 'original':
            original_errors.extend(all_errors[:, kp_idx])
        else:
            interpolated_errors.extend(all_errors[:, kp_idx])
    
    print(f"Original keypoints (12):")
    print(f"   Mean error: {np.mean(original_errors):.2f}±{np.std(original_errors):.2f}mm")
    print(f"   Median error: {np.median(original_errors):.2f}mm")
    
    print(f"Interpolated keypoints (7):")
    print(f"   Mean error: {np.mean(interpolated_errors):.2f}±{np.std(interpolated_errors):.2f}mm")
    print(f"   Median error: {np.median(interpolated_errors):.2f}mm")
    
    # 按解剖类型分析
    anatomy_groups = {}
    for kp_idx, stats in keypoint_stats.items():
        anatomy_type = stats['info']['anatomy']
        if anatomy_type not in anatomy_groups:
            anatomy_groups[anatomy_type] = []
        anatomy_groups[anatomy_type].extend(all_errors[:, kp_idx])
    
    print(f"\nBy anatomy type:")
    for anatomy_type, errors in anatomy_groups.items():
        print(f"   {anatomy_type}: {np.mean(errors):.2f}±{np.std(errors):.2f}mm")

def create_keypoint_analysis_visualization(keypoint_stats, all_errors, all_confidences):
    """创建关键点分析可视化"""
    
    fig, axes = plt.subplots(3, 2, figsize=(16, 18))
    
    # 1. 平均误差排序
    ax1 = axes[0, 0]
    
    kp_indices = list(range(19))
    mean_errors = [keypoint_stats[i]['mean_error'] for i in kp_indices]
    kp_names = [KEYPOINT_INFO[i]['name'] for i in kp_indices]
    kp_types = [KEYPOINT_INFO[i]['type'] for i in kp_indices]
    
    # 按误差排序
    sorted_indices = np.argsort(mean_errors)[::-1]
    sorted_errors = [mean_errors[i] for i in sorted_indices]
    sorted_names = [kp_names[i] for i in sorted_indices]
    sorted_types = [kp_types[i] for i in sorted_indices]
    
    colors = ['red' if t == 'original' else 'blue' for t in sorted_types]
    
    bars = ax1.barh(range(19), sorted_errors, color=colors, alpha=0.7)
    ax1.set_yticks(range(19))
    ax1.set_yticklabels(sorted_names, fontsize=8)
    ax1.set_xlabel('Mean Error (mm)')
    ax1.set_title('Keypoint Error Ranking\n(Red: Original, Blue: Interpolated)')
    ax1.grid(True, alpha=0.3)
    
    # 添加阈值线
    ax1.axvline(x=8, color='orange', linestyle='--', alpha=0.7, label='8mm threshold')
    ax1.legend()
    
    # 2. 误差分布热力图
    ax2 = axes[0, 1]
    
    # 创建热力图数据
    heatmap_data = all_errors.T  # [19, num_samples]
    
    im = ax2.imshow(heatmap_data, cmap='Reds', aspect='auto')
    ax2.set_yticks(range(19))
    ax2.set_yticklabels([KEYPOINT_INFO[i]['name'] for i in range(19)], fontsize=8)
    ax2.set_xlabel('Sample Index')
    ax2.set_title('Error Heatmap Across Samples')
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax2)
    cbar.set_label('Error (mm)')
    
    # 3. 置信度 vs 误差散点图
    ax3 = axes[1, 0]
    
    for kp_idx in range(19):
        errors = all_errors[:, kp_idx]
        confidences = all_confidences[:, kp_idx]
        kp_type = KEYPOINT_INFO[kp_idx]['type']
        color = 'red' if kp_type == 'original' else 'blue'
        alpha = 0.6 if kp_type == 'original' else 0.4
        
        ax3.scatter(confidences, errors, c=color, alpha=alpha, s=20, 
                   label=kp_type if kp_idx == 0 or kp_idx == 12 else "")
    
    ax3.set_xlabel('Confidence')
    ax3.set_ylabel('Error (mm)')
    ax3.set_title('Confidence vs Error')
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    
    # 4. 按类型分组的箱线图
    ax4 = axes[1, 1]
    
    original_data = []
    interpolated_data = []
    
    for kp_idx in range(19):
        if KEYPOINT_INFO[kp_idx]['type'] == 'original':
            original_data.extend(all_errors[:, kp_idx])
        else:
            interpolated_data.extend(all_errors[:, kp_idx])
    
    ax4.boxplot([original_data, interpolated_data], 
                labels=['Original (12)', 'Interpolated (7)'])
    ax4.set_ylabel('Error (mm)')
    ax4.set_title('Error Distribution by Type')
    ax4.grid(True, alpha=0.3)
    
    # 5. 解剖类型分析
    ax5 = axes[2, 0]
    
    anatomy_types = ['prominent', 'edge', 'joint', 'spine', 'tip', 'midpoint', 'centroid']
    anatomy_errors = []
    anatomy_labels = []
    
    for anatomy_type in anatomy_types:
        type_errors = []
        for kp_idx in range(19):
            if KEYPOINT_INFO[kp_idx]['anatomy'] == anatomy_type:
                type_errors.extend(all_errors[:, kp_idx])
        
        if type_errors:  # 只有当该类型有数据时才添加
            anatomy_errors.append(type_errors)
            anatomy_labels.append(f'{anatomy_type}\n({len([i for i in range(19) if KEYPOINT_INFO[i]["anatomy"] == anatomy_type])})')
    
    ax5.boxplot(anatomy_errors, labels=anatomy_labels)
    ax5.set_ylabel('Error (mm)')
    ax5.set_title('Error by Anatomy Type')
    ax5.tick_params(axis='x', rotation=45)
    ax5.grid(True, alpha=0.3)
    
    # 6. 最差关键点详细分析
    ax6 = axes[2, 1]
    
    # 找出最差的5个关键点
    worst_5_indices = np.argsort([keypoint_stats[i]['mean_error'] for i in range(19)])[-5:]
    
    worst_names = [KEYPOINT_INFO[i]['name'] for i in worst_5_indices]
    worst_errors = [keypoint_stats[i]['mean_error'] for i in worst_5_indices]
    worst_stds = [keypoint_stats[i]['std_error'] for i in worst_5_indices]
    
    bars = ax6.bar(worst_names, worst_errors, yerr=worst_stds, 
                   color='red', alpha=0.7, capsize=5)
    ax6.set_ylabel('Mean Error (mm)')
    ax6.set_title('Top 5 Problematic Keypoints')
    ax6.tick_params(axis='x', rotation=45)
    ax6.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, error, std in zip(bars, worst_errors, worst_stds):
        ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.2,
                f'{error:.1f}mm', ha='center', va='bottom', fontweight='bold')
    
    plt.suptitle('Comprehensive Keypoint Performance Analysis', 
                fontsize=16, fontweight='bold')
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    
    filename = 'keypoint_performance_analysis.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 Keypoint analysis saved: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🔍 Problematic Keypoint Analysis")
    print("Identifying which keypoints have poor recognition performance")
    print("=" * 70)
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints_12 = male_data['keypoints']
    
    # 创建19关键点数据
    keypoints_19_list = []
    for kp_12 in keypoints_12:
        kp_19 = np.zeros((19, 3))
        kp_19[:12] = kp_12
        kp_19[12] = (kp_12[0] + kp_12[1]) / 2  # ASIS中点
        kp_19[13] = (kp_12[2] + kp_12[3]) / 2  # PSIS中点
        kp_19[14] = (kp_12[4] + kp_12[5]) / 2  # IC中点
        kp_19[15] = (kp_12[7] + kp_12[8]) / 2  # SIJ中点
        kp_19[16] = (kp_12[9] + kp_12[10]) / 2  # IS中点
        kp_19[17] = (kp_12[6] + kp_12[11]) / 2  # SP-CT中点
        kp_19[18] = np.mean(kp_12, axis=0)      # 重心
        
        for i in range(12, 19):
            kp_19[i] += np.random.normal(0, 1.5, 3)
        
        keypoints_19_list.append(kp_19)
    
    keypoints_19 = np.array(keypoints_19_list)
    
    # 加载模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = BasicHeatmapPointNet19(input_dim=3, num_keypoints=19).to(device)
    
    try:
        model.load_state_dict(torch.load('best_basic_19kp_model.pth', map_location=device))
        model.eval()
        print("✅ 19-keypoint model loaded successfully")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # 分析关键点性能
    all_errors, all_confidences, analyzed_sample_ids = analyze_keypoint_performance(
        model, point_clouds, keypoints_19, sample_ids, device, num_samples=30
    )
    
    # 识别问题关键点
    keypoint_stats, problematic_keypoints = identify_problematic_keypoints(
        all_errors, all_confidences, threshold_error=8.0, threshold_confidence=0.3
    )
    
    # 分析错误模式
    analyze_keypoint_patterns(keypoint_stats, all_errors)
    
    # 创建可视化
    create_keypoint_analysis_visualization(keypoint_stats, all_errors, all_confidences)
    
    # 总结问题关键点
    print(f"\n🚨 Summary of Problematic Keypoints:")
    print("=" * 50)
    
    if problematic_keypoints:
        for kp_idx in problematic_keypoints:
            info = KEYPOINT_INFO[kp_idx]
            stats = keypoint_stats[kp_idx]
            
            print(f"\n{info['name']} ({info['desc']}):")
            print(f"   Type: {info['type']}")
            print(f"   Anatomy: {info['anatomy']}")
            print(f"   Mean Error: {stats['mean_error']:.2f}±{stats['std_error']:.2f}mm")
            print(f"   High Error Rate: {stats['high_error_rate']:.1f}%")
            print(f"   Low Confidence Rate: {stats['low_confidence_rate']:.1f}%")
            
            # 分析可能原因
            if info['type'] == 'interpolated':
                print(f"   💡 Possible cause: Interpolated point lacks distinct anatomical features")
            elif info['anatomy'] in ['joint', 'edge']:
                print(f"   💡 Possible cause: {info['anatomy']} landmarks are harder to localize precisely")
            elif stats['low_confidence_rate'] > 30:
                print(f"   💡 Possible cause: Low model confidence suggests unclear features")
    else:
        print("✅ No severely problematic keypoints identified!")
    
    print(f"\n🎯 Recommendations:")
    print("1. Focus training on problematic keypoint regions")
    print("2. Consider anatomical constraints for interpolated points")
    print("3. Increase training data for difficult anatomical structures")
    print("4. Use multi-scale features for joint and edge landmarks")

if __name__ == "__main__":
    main()
