#!/usr/bin/env python3
"""
正确的数据集扩展实验
Proper Dataset Expansion Experiment
解决数据泄露问题，使用真实的高性能模型架构
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import json
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from scipy.spatial.transform import Rotation as R
import warnings
warnings.filterwarnings('ignore')

# 导入真实的高性能模型架构
from keypoint_mutual_assistance import MutualAssistanceNet, AnatomicalConstraintLoss
from female_specific_optimization import FemaleOptimizedNet, FemaleSpecificConstraintLoss

class ProperDatasetExpansion:
    """正确的数据集扩展实验"""
    
    def __init__(self, device='cuda:1'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        self.experiment_results = []
        
    def load_baseline_data_with_proper_split(self):
        """加载基线数据并进行正确的数据分割"""
        print("📥 加载基线数据并进行正确分割")
        print("=" * 50)
        
        try:
            # 加载女性数据集
            female_data = np.load('archive/old_experiments/f3_reduced_12kp_female.npz')
            female_pc = female_data['point_clouds']
            female_kp = female_data['keypoints']
            
            # 加载男性数据集
            male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz')
            male_pc = male_data['point_clouds']
            male_kp = male_data['keypoints']
            
            print(f"✅ 原始数据:")
            print(f"   女性: {len(female_pc)}样本")
            print(f"   男性: {len(male_pc)}样本")
            print(f"   总计: {len(female_pc) + len(male_pc)}样本")
            
            # 关键：先分割，再增强
            # 女性数据分割 (80% train, 20% test)
            female_train_pc, female_test_pc, female_train_kp, female_test_kp = train_test_split(
                female_pc, female_kp, test_size=0.2, random_state=42)
            
            # 男性数据分割 (80% train, 20% test)
            male_train_pc, male_test_pc, male_train_kp, male_test_kp = train_test_split(
                male_pc, male_kp, test_size=0.2, random_state=42)
            
            print(f"\n📊 数据分割结果:")
            print(f"   女性训练: {len(female_train_pc)}样本")
            print(f"   女性测试: {len(female_test_pc)}样本")
            print(f"   男性训练: {len(male_train_pc)}样本")
            print(f"   男性测试: {len(male_test_pc)}样本")
            
            return (female_train_pc, female_train_kp, female_test_pc, female_test_kp,
                    male_train_pc, male_train_kp, male_test_pc, male_test_kp)
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return None
    
    def anatomically_aware_augmentation(self, pc, kp, num_augmentations=3):
        """解剖学感知的数据增强（仅用于训练数据）"""
        augmented_pc = []
        augmented_kp = []
        
        for i in range(num_augmentations):
            # 1. 小幅旋转 (±3度，更保守)
            rotation_angle = np.random.uniform(-3, 3) * np.pi / 180
            rotation_axis = np.random.choice([0, 1, 2])
            
            rotation_matrix = np.eye(3)
            if rotation_axis == 0:  # 绕x轴旋转
                rotation_matrix = np.array([
                    [1, 0, 0],
                    [0, np.cos(rotation_angle), -np.sin(rotation_angle)],
                    [0, np.sin(rotation_angle), np.cos(rotation_angle)]
                ])
            elif rotation_axis == 1:  # 绕y轴旋转
                rotation_matrix = np.array([
                    [np.cos(rotation_angle), 0, np.sin(rotation_angle)],
                    [0, 1, 0],
                    [-np.sin(rotation_angle), 0, np.cos(rotation_angle)]
                ])
            else:  # 绕z轴旋转
                rotation_matrix = np.array([
                    [np.cos(rotation_angle), -np.sin(rotation_angle), 0],
                    [np.sin(rotation_angle), np.cos(rotation_angle), 0],
                    [0, 0, 1]
                ])
            
            # 2. 小幅缩放 (0.98-1.02，更保守)
            scale_factor = np.random.uniform(0.98, 1.02)
            
            # 3. 小幅平移 (±1mm，更保守)
            translation = np.random.uniform(-1, 1, 3)
            
            # 4. 轻微噪声 (σ=0.05mm，更保守)
            noise_pc = np.random.normal(0, 0.05, pc.shape)
            noise_kp = np.random.normal(0, 0.02, kp.shape)
            
            # 应用变换
            center = np.mean(kp, axis=0)
            
            # 变换点云
            pc_centered = pc - center
            pc_transformed = (pc_centered @ rotation_matrix.T) * scale_factor + center + translation + noise_pc
            
            # 变换关键点
            kp_centered = kp - center
            kp_transformed = (kp_centered @ rotation_matrix.T) * scale_factor + center + translation + noise_kp
            
            augmented_pc.append(pc_transformed)
            augmented_kp.append(kp_transformed)
        
        return np.array(augmented_pc), np.array(augmented_kp)
    
    def create_augmented_training_data(self, train_pc, train_kp, target_samples):
        """创建增强的训练数据（不影响测试集）"""
        print(f"🔄 创建增强训练数据: {len(train_pc)} → {target_samples}")
        
        if target_samples <= len(train_pc):
            return train_pc, train_kp
        
        augmentations_needed = target_samples - len(train_pc)
        augmentations_per_sample = augmentations_needed // len(train_pc) + 1
        
        augmented_pc_list = [train_pc]
        augmented_kp_list = [train_kp]
        
        for i in range(len(train_pc)):
            aug_pc, aug_kp = self.anatomically_aware_augmentation(
                train_pc[i], train_kp[i], augmentations_per_sample)
            augmented_pc_list.append(aug_pc)
            augmented_kp_list.append(aug_kp)
        
        # 合并并截取到目标数量
        final_pc = np.vstack(augmented_pc_list)[:target_samples]
        final_kp = np.vstack(augmented_kp_list)[:target_samples]
        
        print(f"✅ 增强完成: {len(final_pc)}样本")
        return final_pc, final_kp
    
    def train_with_real_architecture(self, train_pc, train_kp, test_pc, test_kp, 
                                   model_type, stage_name):
        """使用真实的高性能模型架构训练"""
        print(f"\n🎯 训练{model_type}模型 - {stage_name}")
        print("=" * 50)
        
        # 选择模型架构和损失函数
        if model_type == "mutual_assistance":
            model = MutualAssistanceNet(num_points=50000, num_keypoints=12)
            criterion = AnatomicalConstraintLoss(alpha=1.0, beta=0.3, gamma=0.2, delta=0.2)
            lr = 0.0001
        elif model_type == "female_optimized":
            model = FemaleOptimizedNet(num_points=50000, num_keypoints=12)
            criterion = FemaleSpecificConstraintLoss(alpha=1.0, beta=0.4, gamma=0.2, delta=0.3)
            lr = 0.00005
        else:
            raise ValueError(f"未知模型类型: {model_type}")
        
        model = model.to(self.device)
        
        print(f"🏗️ {model_type}模型:")
        print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
        print(f"   训练样本: {len(train_pc)}")
        print(f"   测试样本: {len(test_pc)}")
        
        # 转换为张量
        train_pc_tensor = torch.FloatTensor(train_pc).to(self.device)
        train_kp_tensor = torch.FloatTensor(train_kp).to(self.device)
        test_pc_tensor = torch.FloatTensor(test_pc).to(self.device)
        test_kp_tensor = torch.FloatTensor(test_kp).to(self.device)
        
        # 创建数据加载器
        train_dataset = TensorDataset(train_pc_tensor, train_kp_tensor)
        train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True)
        
        # 优化器
        optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-4)
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=100, eta_min=1e-6)
        
        # 训练循环
        model.train()
        best_loss = float('inf')
        patience = 0
        
        for epoch in range(100):
            epoch_loss = 0.0
            
            for batch_pc, batch_kp in train_loader:
                optimizer.zero_grad()
                
                if model_type == "mutual_assistance":
                    predicted, _, _ = model(batch_pc)  # 相互辅助模型返回多个输出
                else:
                    predicted = model(batch_pc)

                # 处理损失函数返回值
                loss_output = criterion(predicted, batch_kp)
                if isinstance(loss_output, tuple):
                    loss = loss_output[0]  # 取第一个元素作为总损失
                else:
                    loss = loss_output
                loss.backward()
                optimizer.step()
                epoch_loss += loss.item()
            
            scheduler.step()
            avg_loss = epoch_loss / len(train_loader)
            
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience = 0
                torch.save(model.state_dict(), f'best_{stage_name}_{model_type}_model.pth')
            else:
                patience += 1
                if patience >= 15:
                    print(f"早停于epoch {epoch+1}")
                    break
            
            if epoch % 20 == 0:
                print(f"Epoch {epoch+1}: Loss = {avg_loss:.6f}")
        
        # 加载最佳模型并测试
        model.load_state_dict(torch.load(f'best_{stage_name}_{model_type}_model.pth'))
        model.eval()
        
        with torch.no_grad():
            if model_type == "mutual_assistance":
                predicted, _, _ = model(test_pc_tensor)
            else:
                predicted = model(test_pc_tensor)
            
            test_errors = torch.norm(predicted - test_kp_tensor, dim=2)
            avg_error = torch.mean(test_errors).item()
            
            # 计算准确率
            sample_errors = torch.mean(test_errors, dim=1)
            errors_5mm = torch.sum(sample_errors <= 5.0).item()
            errors_10mm = torch.sum(sample_errors <= 10.0).item()
            
            acc_5mm = (errors_5mm / len(test_pc)) * 100
            acc_10mm = (errors_10mm / len(test_pc)) * 100
        
        result = {
            'stage': stage_name,
            'model_type': model_type,
            'train_samples': len(train_pc),
            'test_samples': len(test_pc),
            'avg_error': avg_error,
            'accuracy_5mm': acc_5mm,
            'accuracy_10mm': acc_10mm,
            'medical_grade': avg_error <= 10.0,
            'excellent_grade': avg_error <= 5.0
        }
        
        print(f"\n📊 {stage_name} {model_type} 结果:")
        print(f"   训练样本: {result['train_samples']}")
        print(f"   测试样本: {result['test_samples']}")
        print(f"   平均误差: {result['avg_error']:.2f}mm")
        print(f"   5mm准确率: {result['accuracy_5mm']:.1f}%")
        print(f"   10mm准确率: {result['accuracy_10mm']:.1f}%")
        print(f"   医疗级达标: {'✅' if result['medical_grade'] else '❌'}")
        print(f"   优秀级达标: {'✅' if result['excellent_grade'] else '❌'}")
        
        self.experiment_results.append(result)
        return result
    
    def run_proper_expansion_experiment(self):
        """运行正确的扩展实验"""
        print("🚀 开始正确的数据集扩展实验")
        print("解决数据泄露问题，使用真实高性能架构")
        print("=" * 70)
        
        # 1. 加载数据并正确分割
        data_splits = self.load_baseline_data_with_proper_split()
        if data_splits is None:
            print("❌ 数据加载失败，退出")
            return
        
        (female_train_pc, female_train_kp, female_test_pc, female_test_kp,
         male_train_pc, male_train_kp, male_test_pc, male_test_kp) = data_splits
        
        # 2. 基线实验（原始训练数据）
        print(f"\n🎯 基线实验: 原始训练数据")
        
        # 女性基线
        self.train_with_real_architecture(
            female_train_pc, female_train_kp, female_test_pc, female_test_kp,
            "female_optimized", "baseline_female")
        
        # 男性基线
        self.train_with_real_architecture(
            male_train_pc, male_train_kp, male_test_pc, male_test_kp,
            "mutual_assistance", "baseline_male")
        
        # 3. 渐进式增强实验
        augmentation_stages = [
            {"name": "stage1", "female_target": 30, "male_target": 80, "description": "轻度增强"},
            {"name": "stage2", "female_target": 40, "male_target": 100, "description": "中度增强"},
            {"name": "stage3", "female_target": 50, "male_target": 120, "description": "重度增强"}
        ]
        
        for stage in augmentation_stages:
            print(f"\n🎯 {stage['name']}: {stage['description']}")
            
            # 女性增强实验
            aug_female_train_pc, aug_female_train_kp = self.create_augmented_training_data(
                female_train_pc, female_train_kp, stage['female_target'])
            
            self.train_with_real_architecture(
                aug_female_train_pc, aug_female_train_kp, female_test_pc, female_test_kp,
                "female_optimized", f"{stage['name']}_female")
            
            # 男性增强实验
            aug_male_train_pc, aug_male_train_kp = self.create_augmented_training_data(
                male_train_pc, male_train_kp, stage['male_target'])
            
            self.train_with_real_architecture(
                aug_male_train_pc, aug_male_train_kp, male_test_pc, male_test_kp,
                "mutual_assistance", f"{stage['name']}_male")
        
        # 4. 保存结果
        self.save_proper_experiment_results()
        
        return self.experiment_results
    
    def save_proper_experiment_results(self):
        """保存正确的实验结果"""
        results = {
            'experiment_type': 'proper_dataset_expansion',
            'key_improvements': [
                '解决了数据泄露问题',
                '使用真实的高性能模型架构',
                '正确的训练/测试分割',
                '保守的数据增强策略'
            ],
            'results': self.experiment_results,
            'summary': {
                'total_experiments': len(self.experiment_results),
                'architectures_used': ['MutualAssistanceNet', 'FemaleOptimizedNet'],
                'data_leakage_prevented': True
            },
            'timestamp': '2025-07-25'
        }
        
        with open('proper_expansion_experiment_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 正确的实验结果已保存到 proper_expansion_experiment_results.json")

def main():
    """主函数"""
    print("🎯 正确的数据集扩展实验")
    print("Proper Dataset Expansion Experiment")
    print("=" * 60)
    
    # 创建实验器
    experimenter = ProperDatasetExpansion()
    
    # 运行正确的扩展实验
    results = experimenter.run_proper_expansion_experiment()
    
    if results:
        print(f"\n🎉 正确实验完成总结:")
        print(f"✅ 完成{len(results)}个实验")
        print(f"🔧 使用真实高性能架构")
        print(f"🛡️  解决了数据泄露问题")
        print(f"📊 提供可信的性能评估")
    else:
        print("❌ 实验过程中出现问题")

if __name__ == "__main__":
    main()
