#!/usr/bin/env python3
"""
验证6.60mm基线性能
Verify 6.60mm Baseline Performance
重现原始的6.60mm结果并分析差异
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

class SimplifiedUniversalModel(nn.Module):
    """简化通用模型 - 与6.60mm基线完全一致"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super().__init__()
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 简化的特征提取器 (3层)
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.<PERSON>L<PERSON>(),
            nn.Conv1d(128, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
        )
        
        # 全局特征
        self.global_conv = nn.Conv1d(256, 512, 1)
        
        # 初始预测
        self.initial_predictor = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, num_keypoints * 3)
        )
        
        # 相互辅助机制
        self.mutual_assistance = nn.Sequential(
            nn.Linear(num_keypoints * 3, 128),
            nn.ReLU(),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, num_keypoints * 3)
        )
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取
        features = self.feature_extractor(x)  # [B, 256, N]
        
        # 全局特征
        global_features = self.global_conv(features)  # [B, 512, N]
        global_feat = torch.max(global_features, 2)[0]  # [B, 512]
        
        # 初始预测
        initial_kp = self.initial_predictor(global_feat)  # [B, num_keypoints*3]
        
        # 相互辅助
        assistance = self.mutual_assistance(initial_kp)  # [B, num_keypoints*3]
        
        # 最终预测 (残差连接)
        final_kp = initial_kp + 0.3 * assistance
        final_kp = final_kp.view(batch_size, self.num_keypoints, 3)
        
        return final_kp

def verify_baseline_performance():
    """验证基线性能"""
    print("🔍 验证6.60mm基线性能")
    print("=" * 50)
    
    # 设备设置
    device = 'cuda:1' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 加载数据
    try:
        female_data = np.load('archive/old_experiments/f3_reduced_12kp_female.npz')
        female_pc = female_data['point_clouds']
        female_kp = female_data['keypoints']
        
        male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz')
        male_pc = male_data['point_clouds']
        male_kp = male_data['keypoints']
        
        # 合并数据
        all_pc = np.vstack([female_pc, male_pc])
        all_kp = np.vstack([female_kp, male_kp])
        
        print(f"✅ 数据加载成功:")
        print(f"   总样本: {len(all_pc)}")
        print(f"   关键点: {all_kp.shape[1]}个")
        print(f"   点云形状: {all_pc.shape}")
        print(f"   关键点形状: {all_kp.shape}")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None
    
    # 数据分割 (使用与原始实验完全相同的参数)
    train_pc, test_pc, train_kp, test_kp = train_test_split(
        all_pc, all_kp, test_size=0.2, random_state=42)
    
    print(f"\n📊 数据分割:")
    print(f"   训练: {len(train_pc)}样本")
    print(f"   测试: {len(test_pc)}样本")
    
    # 创建模型
    model = SimplifiedUniversalModel(num_points=50000, num_keypoints=12).to(device)
    print(f"\n🏗️ 模型信息:")
    print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练设置
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    
    # 转换为张量
    train_pc_tensor = torch.FloatTensor(train_pc).to(device)
    train_kp_tensor = torch.FloatTensor(train_kp).to(device)
    test_pc_tensor = torch.FloatTensor(test_pc).to(device)
    test_kp_tensor = torch.FloatTensor(test_kp).to(device)
    
    # 创建数据加载器
    batch_size = min(8, len(train_pc) // 4) if len(train_pc) >= 16 else 4
    train_dataset = TensorDataset(train_pc_tensor, train_kp_tensor)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
    
    print(f"\n🎯 开始训练:")
    print(f"   批次大小: {batch_size}")
    print(f"   学习率: 0.001")
    
    # 训练循环
    model.train()
    best_loss = float('inf')
    patience = 0
    
    for epoch in range(100):
        epoch_loss = 0.0
        
        for batch_pc, batch_kp in train_loader:
            optimizer.zero_grad()
            
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            
            loss.backward()
            optimizer.step()
            epoch_loss += loss.item()
        
        avg_loss = epoch_loss / len(train_loader)
        scheduler.step(avg_loss)
        
        if avg_loss < best_loss:
            best_loss = avg_loss
            patience = 0
            torch.save(model.state_dict(), 'verify_baseline_model.pth')
        else:
            patience += 1
            if patience >= 15:
                print(f"早停于epoch {epoch+1}")
                break
        
        if epoch % 20 == 0:
            print(f"Epoch {epoch+1}: Loss = {avg_loss:.6f}")
    
    # 加载最佳模型并测试
    model.load_state_dict(torch.load('verify_baseline_model.pth'))
    model.eval()
    
    with torch.no_grad():
        predicted = model(test_pc_tensor)
        test_errors = torch.norm(predicted - test_kp_tensor, dim=2)
        avg_error = torch.mean(test_errors).item()
        
        # 计算准确率
        sample_errors = torch.mean(test_errors, dim=1)
        errors_5mm = torch.sum(sample_errors <= 5.0).item()
        errors_10mm = torch.sum(sample_errors <= 10.0).item()
        
        acc_5mm = (errors_5mm / len(test_pc)) * 100
        acc_10mm = (errors_10mm / len(test_pc)) * 100
    
    result = {
        'experiment': 'baseline_verification',
        'model_type': 'simplified_universal_12kp',
        'train_samples': len(train_pc),
        'test_samples': len(test_pc),
        'avg_error': avg_error,
        'accuracy_5mm': acc_5mm,
        'accuracy_10mm': acc_10mm,
        'medical_grade': avg_error <= 10.0,
        'excellent_grade': avg_error <= 5.0,
        'parameters': sum(p.numel() for p in model.parameters()),
        'epochs_trained': epoch + 1
    }
    
    print(f"\n📊 基线验证结果:")
    print(f"   平均误差: {result['avg_error']:.2f}mm")
    print(f"   5mm准确率: {result['accuracy_5mm']:.1f}%")
    print(f"   10mm准确率: {result['accuracy_10mm']:.1f}%")
    print(f"   医疗级达标: {'✅' if result['medical_grade'] else '❌'}")
    print(f"   优秀级达标: {'✅' if result['excellent_grade'] else '❌'}")
    print(f"   参数数量: {result['parameters']:,}")
    
    # 与6.60mm对比
    baseline_6_60 = 6.60
    difference = result['avg_error'] - baseline_6_60
    
    print(f"\n🔍 与6.60mm基线对比:")
    print(f"   当前结果: {result['avg_error']:.2f}mm")
    print(f"   基线结果: {baseline_6_60:.2f}mm")
    print(f"   差异: {difference:+.2f}mm")
    
    if abs(difference) < 0.5:
        print(f"   ✅ 结果一致 (差异<0.5mm)")
    elif difference > 0:
        print(f"   ⚠️  性能下降 {difference:.2f}mm")
    else:
        print(f"   🎉 性能提升 {-difference:.2f}mm")
    
    return result

def test_subset_performance():
    """测试子集性能"""
    print(f"\n🔢 测试关键点子集性能")
    print("=" * 50)
    
    device = 'cuda:1' if torch.cuda.is_available() else 'cpu'
    
    # 加载数据
    female_data = np.load('archive/old_experiments/f3_reduced_12kp_female.npz')
    female_pc = female_data['point_clouds']
    female_kp = female_data['keypoints']
    
    male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz')
    male_pc = male_data['point_clouds']
    male_kp = male_data['keypoints']
    
    all_pc = np.vstack([female_pc, male_pc])
    all_kp = np.vstack([female_kp, male_kp])
    
    # 测试不同的关键点子集
    subset_configs = {
        12: {'indices': list(range(12)), 'description': '完整12关键点'},
        6: {'indices': [0, 2, 4, 7, 9, 11], 'description': '6关键点子集'},
        3: {'indices': [0, 5, 11], 'description': '3关键点子集'}
    }
    
    results = []
    
    for num_kp, config in subset_configs.items():
        print(f"\n🎯 测试{num_kp}关键点配置")
        
        # 创建子集
        subset_kp = all_kp[:, config['indices'], :]
        
        # 数据分割
        train_pc, test_pc, train_kp, test_kp = train_test_split(
            all_pc, subset_kp, test_size=0.2, random_state=42)
        
        # 创建模型
        model = SimplifiedUniversalModel(num_points=50000, num_keypoints=num_kp).to(device)
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
        
        # 快速训练
        train_pc_tensor = torch.FloatTensor(train_pc).to(device)
        train_kp_tensor = torch.FloatTensor(train_kp).to(device)
        test_pc_tensor = torch.FloatTensor(test_pc).to(device)
        test_kp_tensor = torch.FloatTensor(test_kp).to(device)
        
        batch_size = min(8, len(train_pc) // 4) if len(train_pc) >= 16 else 4
        train_dataset = TensorDataset(train_pc_tensor, train_kp_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        
        model.train()
        for epoch in range(50):  # 快速训练验证
            epoch_loss = 0.0
            for batch_pc, batch_kp in train_loader:
                optimizer.zero_grad()
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                loss.backward()
                optimizer.step()
                epoch_loss += loss.item()
        
        # 测试
        model.eval()
        with torch.no_grad():
            predicted = model(test_pc_tensor)
            test_errors = torch.norm(predicted - test_kp_tensor, dim=2)
            avg_error = torch.mean(test_errors).item()
        
        result = {
            'num_keypoints': num_kp,
            'description': config['description'],
            'avg_error': avg_error,
            'parameters': sum(p.numel() for p in model.parameters())
        }
        
        results.append(result)
        print(f"   结果: {avg_error:.2f}mm ({result['parameters']:,}参数)")
    
    print(f"\n📊 子集性能对比:")
    for result in results:
        print(f"   {result['num_keypoints']}关键点: {result['avg_error']:.2f}mm")
    
    return results

def main():
    """主函数"""
    print("🔍 验证6.60mm基线性能实验")
    print("Verify 6.60mm Baseline Performance")
    print("=" * 60)
    
    # 验证基线性能
    baseline_result = verify_baseline_performance()
    
    if baseline_result:
        # 测试子集性能
        subset_results = test_subset_performance()
        
        print(f"\n🎉 验证实验完成:")
        print(f"✅ 基线12关键点: {baseline_result['avg_error']:.2f}mm")
        print(f"✅ 参数数量: {baseline_result['parameters']:,}")
        
        if baseline_result['avg_error'] <= 7.0:
            print(f"🎯 结果接近6.60mm基线，验证成功")
        else:
            print(f"⚠️  结果与6.60mm基线有差异，需要进一步分析")
    else:
        print("❌ 验证实验失败")

if __name__ == "__main__":
    main()
