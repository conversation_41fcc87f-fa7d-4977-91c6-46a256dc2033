<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="1280" height="720" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="url(#headerGrad)"/>
  <text x="640" y="50" text-anchor="middle" fill="white"
        font-family="SimHei, Arial, sans-serif" font-size="36" font-weight="bold">
    双SoftMax作用示例
  </text>

  <!-- Main content -->
  <rect x="50" y="100" width="1180" height="580" rx="15" fill="white" stroke="#374151" stroke-width="2"/>

  <!-- Table format -->
  <rect x="80" y="130" width="1120" height="520" rx="10" fill="#f8fafc" stroke="#374151" stroke-width="1"/>

  <!-- Table header -->
  <rect x="100" y="150" width="1080" height="40" rx="5" fill="#7c3aed" stroke="#7c3aed" stroke-width="1"/>
  <text x="640" y="175" text-anchor="middle" fill="white"
        font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    双SoftMax处理过程对比表
  </text>

  <!-- Column headers -->
  <rect x="100" y="200" width="120" height="35" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="160" y="222" text-anchor="middle" fill="#7c3aed"
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    处理步骤
  </text>

  <rect x="220" y="200" width="100" height="35" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="270" y="222" text-anchor="middle" fill="#7c3aed"
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    点1
  </text>

  <rect x="320" y="200" width="100" height="35" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="370" y="222" text-anchor="middle" fill="#7c3aed"
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    点2
  </text>

  <rect x="420" y="200" width="100" height="35" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="470" y="222" text-anchor="middle" fill="#7c3aed"
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    点3
  </text>

  <rect x="520" y="200" width="100" height="35" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="570" y="222" text-anchor="middle" fill="#7c3aed"
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    点4
  </text>

  <rect x="620" y="200" width="100" height="35" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="670" y="222" text-anchor="middle" fill="#7c3aed"
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    点5
  </text>

  <rect x="720" y="200" width="460" height="35" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="950" y="222" text-anchor="middle" fill="#7c3aed"
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    说明
  </text>

  <!-- Row 1: Original features -->
  <rect x="100" y="235" width="120" height="40" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="160" y="260" text-anchor="middle" fill="#374151"
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    原始特征值
  </text>

  <rect x="220" y="235" width="100" height="40" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="270" y="260" text-anchor="middle" fill="#374151"
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    2.1
  </text>

  <rect x="320" y="235" width="100" height="40" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="370" y="260" text-anchor="middle" fill="#374151"
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    2.3
  </text>

  <rect x="420" y="235" width="100" height="40" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="470" y="260" text-anchor="middle" fill="#374151"
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    1.9
  </text>

  <rect x="520" y="235" width="100" height="40" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
  <text x="570" y="260" text-anchor="middle" fill="#374151"
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    2.4
  </text>

  <rect x="620" y="235" width="100" height="40" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="670" y="260" text-anchor="middle" fill="#374151"
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    2.0
  </text>

  <rect x="720" y="235" width="460" height="40" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="950" y="260" text-anchor="middle" fill="#374151"
        font-family="SimHei, Arial, sans-serif" font-size="14">
    特征差异小，最大值仅比最小值大0.5
  </text>

  <!-- Row 2: First SoftMax -->
  <rect x="100" y="275" width="120" height="40" fill="#fef2f2" stroke="#f87171" stroke-width="1"/>
  <text x="160" y="300" text-anchor="middle" fill="#dc2626"
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    第一次SoftMax
  </text>

  <rect x="220" y="275" width="100" height="40" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="270" y="300" text-anchor="middle" fill="#374151"
        font-family="Arial, sans-serif" font-size="16">
    0.186
  </text>

  <rect x="320" y="275" width="100" height="40" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="370" y="300" text-anchor="middle" fill="#374151"
        font-family="Arial, sans-serif" font-size="16">
    0.228
  </text>

  <rect x="420" y="275" width="100" height="40" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="470" y="300" text-anchor="middle" fill="#374151"
        font-family="Arial, sans-serif" font-size="16">
    0.153
  </text>

  <rect x="520" y="275" width="100" height="40" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
  <text x="570" y="300" text-anchor="middle" fill="#374151"
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    0.251
  </text>

  <rect x="620" y="275" width="100" height="40" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="670" y="300" text-anchor="middle" fill="#374151"
        font-family="Arial, sans-serif" font-size="16">
    0.169
  </text>

  <rect x="720" y="275" width="460" height="40" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="950" y="300" text-anchor="middle" fill="#dc2626"
        font-family="SimHei, Arial, sans-serif" font-size="14">
    权重差异小，最优点4只占25.1%
  </text>

  <!-- Row 3: Threshold filtering -->
  <rect x="100" y="315" width="120" height="40" fill="#fff7ed" stroke="#f59e0b" stroke-width="1"/>
  <text x="160" y="340" text-anchor="middle" fill="#d97706"
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    阈值过滤
  </text>

  <rect x="220" y="315" width="100" height="40" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="270" y="340" text-anchor="middle" fill="#15803d"
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    保留
  </text>

  <rect x="320" y="315" width="100" height="40" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="370" y="340" text-anchor="middle" fill="#15803d"
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    保留
  </text>

  <rect x="420" y="315" width="100" height="40" fill="#fef2f2" stroke="#f87171" stroke-width="1"/>
  <text x="470" y="340" text-anchor="middle" fill="#dc2626"
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    过滤
  </text>

  <rect x="520" y="315" width="100" height="40" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="570" y="340" text-anchor="middle" fill="#15803d"
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    保留
  </text>

  <rect x="620" y="315" width="100" height="40" fill="#fef2f2" stroke="#f87171" stroke-width="1"/>
  <text x="670" y="340" text-anchor="middle" fill="#dc2626"
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    过滤
  </text>

  <rect x="720" y="315" width="460" height="40" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="950" y="340" text-anchor="middle" fill="#d97706"
        font-family="SimHei, Arial, sans-serif" font-size="14">
    阈值=0.18，过滤低权重噪声点
  </text>

  <!-- Row 4: Second SoftMax -->
  <rect x="100" y="355" width="120" height="40" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="160" y="380" text-anchor="middle" fill="#15803d"
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    第二次SoftMax
  </text>

  <rect x="220" y="355" width="100" height="40" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="270" y="380" text-anchor="middle" fill="#374151"
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    0.31
  </text>

  <rect x="320" y="355" width="100" height="40" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="370" y="380" text-anchor="middle" fill="#374151"
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    0.33
  </text>

  <rect x="420" y="355" width="100" height="40" fill="#f3f4f6" stroke="#9ca3af" stroke-width="1"/>
  <text x="470" y="380" text-anchor="middle" fill="#6b7280"
        font-family="Arial, sans-serif" font-size="16">
    0.00
  </text>

  <rect x="520" y="355" width="100" height="40" fill="#dcfce7" stroke="#22c55e" stroke-width="2"/>
  <text x="570" y="380" text-anchor="middle" fill="#15803d"
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    0.36
  </text>

  <rect x="620" y="355" width="100" height="40" fill="#f3f4f6" stroke="#9ca3af" stroke-width="1"/>
  <text x="670" y="380" text-anchor="middle" fill="#6b7280"
        font-family="Arial, sans-serif" font-size="16">
    0.00
  </text>

  <rect x="720" y="355" width="460" height="40" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="950" y="380" text-anchor="middle" fill="#15803d"
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    点4权重从25.1%提升到36%，成为主导点！
  </text>

  <!-- Summary -->
  <rect x="100" y="420" width="1080" height="50" rx="10" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="2"/>
  <text x="640" y="450" text-anchor="middle" fill="#7c3aed"
        font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    核心作用：从均匀分布 → 尖锐选择，实现精确定位
  </text>

  <!-- Key insight -->
  <rect x="100" y="490" width="1080" height="140" rx="10" fill="#fef7ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="640" y="520" text-anchor="middle" fill="#7c3aed"
        font-family="SimHei, Arial, sans-serif" font-size="18" font-weight="bold">
    关键洞察
  </text>
  <text x="120" y="550" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="16">
    • 权重差异放大：将微小的特征差异(0.5)放大为显著的权重差异
  </text>
  <text x="120" y="575" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="16">
    • 噪声点过滤：自动排除低权重的干扰点(点3、点5)
  </text>
  <text x="120" y="600" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="16">
    • 主导点突出：让最接近真实关键点的点4占据主导地位(36%)
  </text>
</svg>
