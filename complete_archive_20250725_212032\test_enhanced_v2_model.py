#!/usr/bin/env python3
"""
测试增强版v2模型的真实性能
Test enhanced v2 model real performance
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import json
from tqdm import tqdm

class EnhancedPointNet57(nn.Module):
    """增强版PointNet57 - 与训练时相同的架构"""
    
    def __init__(self, num_keypoints=57, dropout_rate=0.2):
        super(EnhancedPointNet57, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 残差连接
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        # 回归头
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, num_keypoints * 3)
        
        # 批归一化
        self.fc_bn1 = nn.BatchNorm1d(512)
        self.fc_bn2 = nn.BatchNorm1d(256)
        self.fc_bn3 = nn.BatchNorm1d(128)
        
        # Dropout
        self.dropout = nn.Dropout(dropout_rate)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取 + 残差连接
        x1 = F.relu(self.bn1(self.conv1(x)))
        x2 = F.relu(self.bn2(self.conv2(x1)))
        x3 = F.relu(self.bn3(self.conv3(x2)))
        x3_res = x3 + self.residual1(x1)
        
        x4 = F.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = F.relu(self.bn5(self.conv5(x4_res)))
        
        # 全局最大池化
        global_feat = torch.max(x5, 2)[0]
        
        # 回归
        x = F.relu(self.fc_bn1(self.fc1(global_feat)))
        x = self.dropout(x)
        x = F.relu(self.fc_bn2(self.fc2(x)))
        x = self.dropout(x)
        x = F.relu(self.fc_bn3(self.fc3(x)))
        x = self.dropout(x)
        
        keypoints = self.fc4(x)
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

class TestDataset57(Dataset):
    def __init__(self, point_clouds, keypoints):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def test_enhanced_v2_model():
    """测试增强版v2模型"""
    
    print("🔍 测试增强版v2模型的真实性能")
    print("=" * 80)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载数据和归一化
    print("📊 加载和归一化数据...")
    data = np.load('unified_pelvis_57_dataset.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints_57 = data['keypoints_57']
    sample_ids = data['sample_ids']
    
    # 数据归一化
    normalized_pc = []
    normalized_kp = []
    scalers = []
    
    for i in range(len(point_clouds)):
        pc = point_clouds[i].copy()
        kp = keypoints_57[i].copy()
        
        combined_data = np.vstack([pc, kp])
        scaler = StandardScaler()
        combined_normalized = scaler.fit_transform(combined_data)
        
        pc_normalized = combined_normalized[:len(pc)]
        kp_normalized = combined_normalized[len(pc):]
        
        normalized_pc.append(pc_normalized)
        normalized_kp.append(kp_normalized)
        scalers.append(scaler)
    
    normalized_pc = np.array(normalized_pc)
    normalized_kp = np.array(normalized_kp)
    
    print(f"✅ 数据归一化完成: {len(sample_ids)} 个样本")
    
    # 数据划分 - 与训练时相同
    indices = np.arange(len(normalized_pc))
    train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
    
    # 创建测试数据集
    test_dataset = TestDataset57(normalized_pc[test_indices], normalized_kp[test_indices])
    test_loader = DataLoader(test_dataset, batch_size=4, shuffle=False, drop_last=False)
    
    print(f"📋 测试集: {len(test_dataset)} 个样本")
    
    # 加载训练好的模型
    print("🤖 加载训练好的模型...")
    model = EnhancedPointNet57(num_keypoints=57, dropout_rate=0.2)
    model.load_state_dict(torch.load('best_enhanced_57_model_v2.pth'))
    model = model.to(device)
    model.eval()
    
    print(f"✅ 模型加载成功: {sum(p.numel() for p in model.parameters()):,} 参数")
    
    # 测试模型
    print("🔍 执行模型测试...")
    test_predictions = []
    test_targets = []
    
    with torch.no_grad():
        for batch_pc, batch_kp in tqdm(test_loader, desc="测试"):
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            predicted = model(batch_pc)
            
            test_predictions.append(predicted.cpu().numpy())
            test_targets.append(batch_kp.cpu().numpy())
    
    # 合并预测结果
    test_predictions = np.vstack(test_predictions)
    test_targets = np.vstack(test_targets)
    
    print(f"✅ 测试完成: {len(test_predictions)} 个预测")
    
    # 反归一化到真实尺度
    print("🔄 反归一化预测结果...")
    
    real_predictions = []
    real_targets = []
    
    for i, orig_idx in enumerate(test_indices):
        if i < len(test_predictions):
            pred_norm = test_predictions[i]
            target_norm = test_targets[i]
            
            scaler = scalers[orig_idx]
            
            # 反归一化预测
            dummy_pc = np.zeros((50000, 3))
            combined_pred = np.vstack([dummy_pc, pred_norm])
            combined_pred_denorm = scaler.inverse_transform(combined_pred)
            pred_real = combined_pred_denorm[50000:]
            
            # 反归一化目标
            combined_target = np.vstack([dummy_pc, target_norm])
            combined_target_denorm = scaler.inverse_transform(combined_target)
            target_real = combined_target_denorm[50000:]
            
            real_predictions.append(pred_real)
            real_targets.append(target_real)
    
    real_predictions = np.array(real_predictions)
    real_targets = np.array(real_targets)
    
    print(f"✅ 反归一化完成: {len(real_predictions)} 个样本")
    
    # 计算真实误差
    print("📊 计算真实误差...")
    
    total_error = 0.0
    region_errors = {'F1': [], 'F2': [], 'F3': []}
    all_errors = []
    
    for i in range(len(real_predictions)):
        pred = real_predictions[i]
        target = real_targets[i]
        
        # 计算每个关键点的误差
        distances = np.linalg.norm(pred - target, axis=1)
        total_error += np.mean(distances)
        all_errors.extend(distances)
        
        # 分区域计算
        f1_distances = distances[0:19]
        f2_distances = distances[19:38]
        f3_distances = distances[38:57]
        
        region_errors['F1'].extend(f1_distances)
        region_errors['F2'].extend(f2_distances)
        region_errors['F3'].extend(f3_distances)
    
    avg_error = total_error / len(real_predictions)
    
    # 计算医疗级准确率
    accuracy_5mm = np.mean(np.array(all_errors) < 5.0) * 100
    accuracy_10mm = np.mean(np.array(all_errors) < 10.0) * 100
    accuracy_15mm = np.mean(np.array(all_errors) < 15.0) * 100
    
    print(f"\n🎯 增强版v2模型最终结果:")
    print(f"   整体平均误差: {avg_error:.2f}mm")
    
    for region, errors in region_errors.items():
        if errors:
            mean_error = np.mean(errors)
            std_error = np.std(errors)
            max_error = np.max(errors)
            min_error = np.min(errors)
            print(f"   {region}区域: {mean_error:.2f}±{std_error:.2f}mm (范围: {min_error:.2f}-{max_error:.2f}mm)")
    
    print(f"\n   医疗级准确率:")
    print(f"     <5mm: {accuracy_5mm:.1f}%")
    print(f"     <10mm: {accuracy_10mm:.1f}%")
    print(f"     <15mm: {accuracy_15mm:.1f}%")
    
    print(f"\n📊 性能对比总结:")
    print(f"   12点模型: 6.21mm")
    print(f"   简单57点模型: 15.50mm")
    print(f"   归一化57点模型: 11.81mm")
    print(f"   增强版v2模型: {avg_error:.2f}mm")
    
    # 计算改进幅度
    improvement_vs_simple = (15.50 - avg_error) / 15.50 * 100
    improvement_vs_normalized = (11.81 - avg_error) / 11.81 * 100
    
    print(f"\n💡 改进幅度:")
    print(f"   相比简单57点: {improvement_vs_simple:+.1f}%")
    print(f"   相比归一化57点: {improvement_vs_normalized:+.1f}%")
    
    if avg_error < 8.0:
        print(f"\n🎉 增强版v2模型性能优秀！接近12点模型水平")
        print(f"💡 关键成功因素:")
        print(f"   ✅ 数据归一化解决训练稳定性")
        print(f"   ✅ 数据增强提高泛化能力")
        print(f"   ✅ 区域加权损失优化F1、F2区域")
        print(f"   ✅ 残差连接和深度网络")
    elif avg_error < 10.0:
        print(f"\n✅ 增强版v2模型性能良好！显著提升")
        print(f"💡 已经实现了重要突破")
    else:
        print(f"\n⚠️ 仍有改进空间，但已有显著提升")
    
    # 保存详细结果
    results = {
        'avg_error': float(avg_error),
        'region_errors': {k: float(np.mean(v)) for k, v in region_errors.items()},
        'accuracy_5mm': float(accuracy_5mm),
        'accuracy_10mm': float(accuracy_10mm),
        'accuracy_15mm': float(accuracy_15mm),
        'improvement_vs_simple': float(improvement_vs_simple),
        'improvement_vs_normalized': float(improvement_vs_normalized),
        'model_info': {
            'architecture': 'EnhancedPointNet57_v2',
            'parameters': sum(p.numel() for p in model.parameters()),
            'key_features': [
                'data_normalization',
                'data_augmentation',
                'region_weighted_loss',
                'residual_connections',
                'cosine_annealing_scheduler'
            ]
        }
    }
    
    with open('enhanced_v2_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 详细结果已保存: enhanced_v2_test_results.json")

if __name__ == "__main__":
    test_enhanced_v2_model()
