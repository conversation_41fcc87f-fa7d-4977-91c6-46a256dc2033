<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="1280" height="720" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="url(#headerGrad)"/>
  <text x="640" y="50" text-anchor="middle" fill="white" 
        font-family="SimHei, Arial, sans-serif" font-size="36" font-weight="bold">
    SoftMax原理：将数值转换为概率分布
  </text>
  
  <!-- Main content -->
  <rect x="50" y="100" width="1180" height="580" rx="15" fill="white" stroke="#374151" stroke-width="2"/>
  
  <!-- Core formula -->
  <rect x="80" y="130" width="1120" height="80" rx="10" fill="#f0f9ff" stroke="#3b82f6" stroke-width="2"/>
  <text x="640" y="160" text-anchor="middle" fill="#1e40af" 
        font-family="SimHei, Arial, sans-serif" font-size="24" font-weight="bold">
    SoftMax公式
  </text>
  <rect x="400" y="175" width="480" height="30" rx="5" fill="#dbeafe" stroke="#3b82f6" stroke-width="2"/>
  <text x="640" y="195" text-anchor="middle" fill="#1e40af" 
        font-family="Arial, sans-serif" font-size="18" font-weight="bold">
    P(i) = exp(x_i) / Σ exp(x_j)
  </text>
  
  <!-- Step by step example -->
  <rect x="80" y="230" width="1120" height="420" rx="10" fill="#f8fafc" stroke="#374151" stroke-width="1"/>
  
  <!-- Step 1: Input -->
  <text x="100" y="270" fill="#1e40af" font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    步骤1：输入原始数值
  </text>
  <rect x="100" y="280" width="1080" height="50" rx="5" fill="#fef7ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="140" y="310" fill="#374151" font-family="Arial, sans-serif" font-size="18">
    x = [1.0, 2.0, 3.0, 4.0]
  </text>
  <text x="400" y="310" fill="#7c3aed" font-family="SimHei, Arial, sans-serif" font-size="16">
    (可以是任意实数，正数、负数、零都可以)
  </text>
  
  <!-- Step 2: Exponential -->
  <text x="100" y="360" fill="#dc2626" font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    步骤2：计算指数值
  </text>
  <rect x="100" y="370" width="1080" height="80" rx="5" fill="#fef2f2" stroke="#f87171" stroke-width="1"/>
  <text x="140" y="400" fill="#374151" font-family="Arial, sans-serif" font-size="18">
    exp(1.0) = 2.72    exp(2.0) = 7.39    exp(3.0) = 20.09    exp(4.0) = 54.60
  </text>
  <text x="140" y="430" fill="#dc2626" font-family="SimHei, Arial, sans-serif" font-size="16">
    作用：将所有数值变为正数，并放大差异
  </text>
  
  <!-- Step 3: Sum -->
  <text x="100" y="480" fill="#d97706" font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    步骤3：计算总和
  </text>
  <rect x="100" y="490" width="1080" height="50" rx="5" fill="#fff7ed" stroke="#f59e0b" stroke-width="1"/>
  <text x="140" y="520" fill="#374151" font-family="Arial, sans-serif" font-size="18">
    总和 = 2.72 + 7.39 + 20.09 + 54.60 = 84.80
  </text>
  
  <!-- Step 4: Normalize -->
  <text x="100" y="570" fill="#15803d" font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    步骤4：归一化得到概率
  </text>
  <rect x="100" y="580" width="1080" height="60" rx="5" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="140" y="610" fill="#374151" font-family="Arial, sans-serif" font-size="18">
    P1 = 2.72/84.80 = 0.032    P2 = 7.39/84.80 = 0.087    P3 = 20.09/84.80 = 0.237    P4 = 54.60/84.80 = 0.644
  </text>
  <text x="140" y="630" fill="#15803d" font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    结果：所有概率和为1，最大值对应最高概率
  </text>
  
  <!-- Visual comparison -->
  <rect x="80" y="670" width="560" height="30" rx="5" fill="#f3f4f6" stroke="#9ca3af" stroke-width="1"/>
  <text x="100" y="690" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    输入对比：[1.0, 2.0, 3.0, 4.0] → 差异不明显
  </text>
  
  <rect x="660" y="670" width="540" height="30" rx="5" fill="#dcfce7" stroke="#22c55e" stroke-width="1"/>
  <text x="680" y="690" fill="#15803d" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    输出对比：[0.032, 0.087, 0.237, 0.644] → 差异明显，突出最大值
  </text>
</svg>
