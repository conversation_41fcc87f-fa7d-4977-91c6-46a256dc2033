<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="1280" height="720" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="url(#headerGrad)"/>
  <text x="640" y="50" text-anchor="middle" fill="white" 
        font-family="Arial, sans-serif" font-size="36" font-weight="bold">
    Double SoftMax Weighting Mechanism
  </text>
  
  <!-- Problem motivation -->
  <rect x="50" y="100" width="1180" height="100" rx="15" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <text x="640" y="130" text-anchor="middle" fill="#d97706" 
        font-family="Arial, sans-serif" font-size="22" font-weight="bold">
    Motivation: Addressing Weight Distribution Challenges
  </text>
  <text x="70" y="160" fill="#374151" font-family="Arial, sans-serif" font-size="16">
    <tspan font-weight="bold">Problem:</tspan> In dense point clouds, when region center sp_i is close to ground truth tp_i, 
    single SoftMax produces nearly uniform weights due to point similarity, leading to averaging effects that reduce precision.
  </text>
  <text x="70" y="185" fill="#374151" font-family="Arial, sans-serif" font-size="16">
    <tspan font-weight="bold">Solution:</tspan> Two-stage refinement to emphasize high-weight points and suppress low-weight points.
  </text>
  
  <!-- Main workflow -->
  <rect x="50" y="220" width="1180" height="460" rx="15" fill="white" stroke="#7c3aed" stroke-width="3"/>
  <text x="640" y="250" text-anchor="middle" fill="#7c3aed" 
        font-family="Arial, sans-serif" font-size="24" font-weight="bold">
    Double SoftMax Workflow
  </text>
  
  <!-- Step 1: Input -->
  <rect x="80" y="280" width="200" height="120" rx="10" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="2"/>
  <text x="180" y="305" text-anchor="middle" fill="#0c4a6e" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    Input: Potential Region
  </text>
  <text x="90" y="330" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • k points in region R_i
  </text>
  <text x="90" y="350" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • Feature vectors from PointNet
  </text>
  <text x="90" y="370" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • High point density/similarity
  </text>
  <text x="90" y="390" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • Need precise localization
  </text>
  
  <!-- Arrow 1 -->
  <path d="M 290 340 L 320 340" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
    </marker>
  </defs>
  
  <!-- Step 2: First SoftMax -->
  <rect x="330" y="280" width="200" height="120" rx="10" fill="#fef2f2" stroke="#ef4444" stroke-width="2"/>
  <text x="430" y="305" text-anchor="middle" fill="#dc2626" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    1st SoftMax
  </text>
  <text x="340" y="330" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    ω_j = exp(f_j) / Σexp(f_k)
  </text>
  <text x="340" y="350" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • Initial weight calculation
  </text>
  <text x="340" y="370" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • Based on feature similarity
  </text>
  <text x="340" y="390" fill="#dc2626" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Issue: Nearly uniform weights
  </text>
  
  <!-- Weight visualization 1 -->
  <rect x="340" y="410" width="180" height="30" rx="5" fill="#fee2e2" stroke="#fca5a5" stroke-width="1"/>
  <rect x="345" y="415" width="25" height="20" fill="#ef4444" opacity="0.6"/>
  <rect x="375" y="415" width="23" height="20" fill="#ef4444" opacity="0.5"/>
  <rect x="403" y="415" width="24" height="20" fill="#ef4444" opacity="0.55"/>
  <rect x="432" y="415" width="22" height="20" fill="#ef4444" opacity="0.5"/>
  <rect x="459" y="415" width="26" height="20" fill="#ef4444" opacity="0.6"/>
  <rect x="490" y="415" width="25" height="20" fill="#ef4444" opacity="0.55"/>
  <text x="430" y="455" text-anchor="middle" fill="#7f1d1d" 
        font-family="Arial, sans-serif" font-size="10">
    Uniform-like distribution
  </text>
  
  <!-- Arrow 2 -->
  <path d="M 540 340 L 570 340" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Step 3: Weight SoftMax -->
  <rect x="580" y="280" width="200" height="120" rx="10" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="2"/>
  <text x="680" y="305" text-anchor="middle" fill="#7c3aed" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    2nd SoftMax (Weight)
  </text>
  <text x="590" y="330" fill="#374151" font-family="Arial, sans-serif" font-size="11">
    WS(ω_j) = 1/(1 + exp(-M·(ω_j - (1/m - ε))))
  </text>
  <text x="590" y="350" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • Threshold-based filtering
  </text>
  <text x="590" y="370" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • Amplifies weight differences
  </text>
  <text x="590" y="390" fill="#7c3aed" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Result: Sharp weight distribution
  </text>
  
  <!-- Weight visualization 2 -->
  <rect x="590" y="410" width="180" height="30" rx="5" fill="#f3e8ff" stroke="#c084fc" stroke-width="1"/>
  <rect x="595" y="415" width="35" height="20" fill="#8b5cf6" opacity="0.9"/>
  <rect x="635" y="415" width="8" height="20" fill="#8b5cf6" opacity="0.2"/>
  <rect x="648" y="415" width="40" height="20" fill="#8b5cf6" opacity="0.8"/>
  <rect x="693" y="415" width="5" height="20" fill="#8b5cf6" opacity="0.1"/>
  <rect x="703" y="415" width="30" height="20" fill="#8b5cf6" opacity="0.7"/>
  <rect x="738" y="415" width="7" height="20" fill="#8b5cf6" opacity="0.15"/>
  <text x="680" y="455" text-anchor="middle" fill="#6b21a8" 
        font-family="Arial, sans-serif" font-size="10">
    Sharp, selective distribution
  </text>
  
  <!-- Arrow 3 -->
  <path d="M 790 340 L 820 340" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Step 4: Weighted Average -->
  <rect x="830" y="280" width="200" height="120" rx="10" fill="#f0fdf4" stroke="#22c55e" stroke-width="2"/>
  <text x="930" y="305" text-anchor="middle" fill="#15803d" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    Weighted Average
  </text>
  <text x="840" y="330" fill="#374151" font-family="Arial, sans-serif" font-size="11">
    kp_i = Σ(ω_j · WS(ω_j) · coord_j) / Σ(ω_j · WS(ω_j))
  </text>
  <text x="840" y="350" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • Final keypoint coordinates
  </text>
  <text x="840" y="370" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • High-weight points dominate
  </text>
  <text x="840" y="390" fill="#15803d" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Precise localization achieved
  </text>
  
  <!-- Arrow 4 -->
  <path d="M 1040 340 L 1070 340" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Step 5: Output -->
  <rect x="1080" y="280" width="150" height="120" rx="10" fill="#fef7ff" stroke="#a855f7" stroke-width="2"/>
  <text x="1155" y="305" text-anchor="middle" fill="#7c3aed" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    Final Keypoint
  </text>
  <text x="1090" y="330" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • Precise location
  </text>
  <text x="1090" y="350" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • Semantic label
  </text>
  <text x="1090" y="370" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    • High accuracy
  </text>
  <text x="1090" y="390" fill="#7c3aed" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Clinical ready
  </text>
  
  <!-- Mathematical details -->
  <rect x="80" y="480" width="550" height="180" rx="10" fill="#f8fafc" stroke="#64748b" stroke-width="2"/>
  <text x="355" y="505" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="18" font-weight="bold">
    Mathematical Formulation Details
  </text>
  
  <text x="90" y="535" fill="#374151" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Parameters:
  </text>
  <text x="90" y="555" fill="#6b7280" font-family="Arial, sans-serif" font-size="12">
    • M = 1 × 10^(2×(γ+3)), where γ = log₁₀⌈1/m⌉
  </text>
  <text x="90" y="575" fill="#6b7280" font-family="Arial, sans-serif" font-size="12">
    • ε = 5 × 10^(-1×(γ+2))
  </text>
  <text x="90" y="595" fill="#6b7280" font-family="Arial, sans-serif" font-size="12">
    • m = number of points in region
  </text>
  
  <text x="90" y="625" fill="#374151" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Threshold Function:
  </text>
  <text x="90" y="645" fill="#6b7280" font-family="Arial, sans-serif" font-size="12">
    WS(ω_j) = 1 / (1 + exp(-M × (ω_j - (1/m - ε))))
  </text>
  
  <!-- Performance comparison -->
  <rect x="650" y="480" width="580" height="180" rx="10" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="2"/>
  <text x="940" y="505" text-anchor="middle" fill="#0c4a6e" 
        font-family="Arial, sans-serif" font-size="18" font-weight="bold">
    Performance Comparison
  </text>
  
  <!-- Comparison table -->
  <rect x="670" y="520" width="540" height="130" rx="5" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  
  <!-- Table headers -->
  <rect x="670" y="520" width="180" height="30" fill="#f1f5f9" stroke="#cbd5e1" stroke-width="1"/>
  <text x="760" y="540" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Method
  </text>
  
  <rect x="850" y="520" width="180" height="30" fill="#f1f5f9" stroke="#cbd5e1" stroke-width="1"/>
  <text x="940" y="540" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    MRE (mm)
  </text>
  
  <rect x="1030" y="520" width="180" height="30" fill="#f1f5f9" stroke="#cbd5e1" stroke-width="1"/>
  <text x="1120" y="540" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Improvement
  </text>
  
  <!-- Table rows -->
  <rect x="670" y="550" width="180" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="760" y="567" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="11">
    Center Points Only
  </text>
  
  <rect x="850" y="550" width="180" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="940" y="567" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="11">
    1.81 ± 1.08
  </text>
  
  <rect x="1030" y="550" width="180" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="1120" y="567" text-anchor="middle" fill="#dc2626" 
        font-family="Arial, sans-serif" font-size="11">
    Baseline
  </text>
  
  <rect x="670" y="575" width="180" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="760" y="592" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="11">
    PointNet + Residual
  </text>
  
  <rect x="850" y="575" width="180" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="940" y="592" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="11">
    1.62 ± 1.04
  </text>
  
  <rect x="1030" y="575" width="180" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="1120" y="592" text-anchor="middle" fill="#f59e0b" 
        font-family="Arial, sans-serif" font-size="11">
    +0.19mm
  </text>
  
  <rect x="670" y="600" width="180" height="25" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="760" y="617" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    + Double SoftMax
  </text>
  
  <rect x="850" y="600" width="180" height="25" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="940" y="617" text-anchor="middle" fill="#15803d" 
        font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    1.43 ± 0.97
  </text>
  
  <rect x="1030" y="600" width="180" height="25" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="1120" y="617" text-anchor="middle" fill="#15803d" 
        font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    +0.38mm
  </text>
  
  <rect x="670" y="625" width="540" height="25" fill="#fef7ff" stroke="#a855f7" stroke-width="1"/>
  <text x="940" y="642" text-anchor="middle" fill="#7c3aed" 
        font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Key Innovation: 21% improvement in precision through selective weighting
  </text>
</svg>
