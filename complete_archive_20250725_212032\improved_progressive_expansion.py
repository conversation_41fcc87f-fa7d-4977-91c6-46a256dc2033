#!/usr/bin/env python3
"""
改进的渐进式数据集扩展策略
Improved Progressive Dataset Expansion Strategy
基于实际实验结果的优化方案
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import json
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

class ImprovedProgressiveExpansion:
    """改进的渐进式扩展器"""
    
    def __init__(self, device='cuda:1'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        self.expansion_history = []
        
    def analyze_expansion_failure(self):
        """分析扩展失败的原因"""
        print("🔍 分析渐进式扩展失败的原因")
        print("=" * 50)
        
        # 加载之前的实验结果
        try:
            with open('progressive_expansion_results.json', 'r') as f:
                results = json.load(f)
            
            analysis = {
                "关键发现": [
                    "基线97样本: 7.10mm (医疗级达标)",
                    "扩展到105样本: 13.73mm (性能下降)",
                    "扩展到124样本: 12.18mm (轻微改善)",
                    "扩展到150样本: 20.35mm (严重恶化)"
                ],
                
                "失败原因分析": {
                    "数据质量问题": [
                        "新增数据可能质量不如原始高质量数据",
                        "从57关键点提取12关键点可能引入误差",
                        "简单的性别分类方法不准确",
                        "点云重采样可能损失重要信息"
                    ],
                    
                    "模型问题": [
                        "简单模型架构无法处理更复杂的数据分布",
                        "没有使用预训练的高性能模型",
                        "训练策略过于简单",
                        "缺乏数据增强和正则化"
                    ],
                    
                    "扩展策略问题": [
                        "一次性添加太多低质量数据",
                        "没有质量控制机制",
                        "缺乏渐进式训练策略",
                        "没有保持原始高性能模型的知识"
                    ]
                },
                
                "关键洞察": [
                    "不是所有数据都有助于提升性能",
                    "数据质量比数据数量更重要",
                    "需要更智能的数据选择策略",
                    "应该基于高性能模型进行迁移学习"
                ]
            }
            
            print("📊 实验结果分析:")
            for finding in analysis["关键发现"]:
                print(f"  • {finding}")
            
            print(f"\n🚨 失败原因:")
            for category, reasons in analysis["失败原因分析"].items():
                print(f"  {category}:")
                for reason in reasons:
                    print(f"    - {reason}")
            
            print(f"\n💡 关键洞察:")
            for insight in analysis["关键洞察"]:
                print(f"  • {insight}")
            
            return analysis
            
        except Exception as e:
            print(f"❌ 无法加载之前的结果: {e}")
            return None
    
    def create_smart_expansion_strategy(self):
        """创建智能扩展策略"""
        print("\n🧠 创建智能扩展策略")
        print("=" * 50)
        
        smart_strategy = {
            "核心原则": [
                "质量优于数量",
                "渐进式知识保持",
                "智能数据选择",
                "持续质量监控"
            ],
            
            "改进方案": {
                "1. 基于高性能模型的迁移学习": {
                    "策略": "加载已训练的高性能模型作为起点",
                    "实现": [
                        "加载female_optimized.pth和mutual_assistance_男性.pth",
                        "冻结特征提取层",
                        "只微调分类层",
                        "使用知识蒸馏保持性能"
                    ]
                },
                
                "2. 智能数据质量评估": {
                    "策略": "在添加新数据前评估其质量",
                    "实现": [
                        "计算新样本与高质量样本的相似度",
                        "检查关键点的解剖学合理性",
                        "验证表面投影精度",
                        "只添加高质量样本"
                    ]
                },
                
                "3. 渐进式训练策略": {
                    "策略": "小批量添加数据，持续监控性能",
                    "实现": [
                        "每次只添加2-3个高质量样本",
                        "使用混合训练数据(新70% + 旧30%)",
                        "实时监控性能变化",
                        "性能下降时立即停止"
                    ]
                },
                
                "4. 数据增强优化": {
                    "策略": "通过数据增强而非新数据来扩展",
                    "实现": [
                        "几何变换(旋转、缩放、平移)",
                        "噪声注入",
                        "解剖学约束的变形",
                        "保持原始数据的高质量特性"
                    ]
                }
            },
            
            "实施步骤": {
                "步骤1": "加载并验证高性能基线模型",
                "步骤2": "评估可用额外数据的质量",
                "步骤3": "选择最高质量的2-3个样本",
                "步骤4": "使用迁移学习进行微调",
                "步骤5": "验证性能是否保持或提升",
                "步骤6": "如果成功，重复步骤3-5"
            }
        }
        
        print("核心原则:")
        for principle in smart_strategy["核心原则"]:
            print(f"  • {principle}")
        
        print(f"\n改进方案:")
        for method, details in smart_strategy["改进方案"].items():
            print(f"  {method}:")
            print(f"    策略: {details['策略']}")
        
        return smart_strategy
    
    def load_high_performance_models(self):
        """加载高性能模型"""
        print("\n📥 尝试加载高性能模型")
        print("=" * 40)
        
        models = {}
        
        # 尝试加载女性优化模型
        try:
            female_path = "female_optimized.pth"
            if Path(female_path).exists():
                female_checkpoint = torch.load(female_path, map_location=self.device)
                models['female'] = female_checkpoint
                print(f"✅ 女性优化模型加载成功")
            else:
                print(f"⚠️  女性模型文件不存在: {female_path}")
        except Exception as e:
            print(f"❌ 女性模型加载失败: {e}")
        
        # 尝试加载男性模型
        try:
            male_path = "mutual_assistance_男性.pth"
            if Path(male_path).exists():
                male_checkpoint = torch.load(male_path, map_location=self.device)
                models['male'] = male_checkpoint
                print(f"✅ 男性模型加载成功")
            else:
                print(f"⚠️  男性模型文件不存在: {male_path}")
        except Exception as e:
            print(f"❌ 男性模型加载失败: {e}")
        
        return models
    
    def evaluate_data_quality(self, pc, kp, reference_pc, reference_kp):
        """评估数据质量"""
        try:
            # 1. 检查数据完整性
            if len(pc) < 1000 or len(kp) != 12:
                return 0.0, "数据不完整"
            
            # 2. 检查关键点合理性
            kp_distances = np.linalg.norm(kp[1:] - kp[:-1], axis=1)
            if np.any(kp_distances < 1.0) or np.any(kp_distances > 200.0):
                return 0.2, "关键点距离异常"
            
            # 3. 计算与参考数据的相似度
            ref_center = np.mean(reference_kp, axis=0)
            new_center = np.mean(kp, axis=0)
            center_distance = np.linalg.norm(new_center - ref_center)
            
            # 4. 计算形状相似度
            ref_shape = reference_kp - ref_center
            new_shape = kp - new_center
            shape_similarity = np.mean([
                1.0 / (1.0 + np.linalg.norm(ref_shape[i] - new_shape[i]))
                for i in range(min(len(ref_shape), len(new_shape)))
            ])
            
            # 5. 综合质量评分
            quality_score = shape_similarity * 0.7 + (1.0 / (1.0 + center_distance/100)) * 0.3
            
            if quality_score > 0.7:
                return quality_score, "高质量"
            elif quality_score > 0.5:
                return quality_score, "中等质量"
            else:
                return quality_score, "低质量"
                
        except Exception as e:
            return 0.0, f"评估失败: {e}"
    
    def smart_data_selection(self, female_pc, female_kp, male_pc, male_kp, max_new_samples=5):
        """智能数据选择"""
        print(f"\n🎯 智能数据选择 (最多{max_new_samples}个高质量样本)")
        print("=" * 50)
        
        # 计算参考数据的中心
        ref_female_center = np.mean(female_kp, axis=(0,1))
        ref_male_center = np.mean(male_kp, axis=(0,1))
        
        # 扫描可用数据
        data_dir = Path('archive/old_experiments')
        sample_ids = []
        for kp_file in data_dir.glob('*_keypoints.npy'):
            sample_id = kp_file.stem.replace('_keypoints', '')
            pc_file = data_dir / f"{sample_id}_pointcloud.npy"
            if pc_file.exists():
                sample_ids.append(sample_id)
        
        # 评估每个样本的质量
        quality_scores = []
        for sample_id in sample_ids[:20]:  # 限制评估数量
            try:
                pc = np.load(f'archive/old_experiments/{sample_id}_pointcloud.npy')
                kp = np.load(f'archive/old_experiments/{sample_id}_keypoints.npy')
                
                # 提取12关键点
                if len(kp) >= 12:
                    kp_12 = kp[:12]  # 简化提取
                else:
                    continue
                
                # 评估质量
                female_score, female_reason = self.evaluate_data_quality(
                    pc, kp_12, female_pc[0], female_kp[0])
                male_score, male_reason = self.evaluate_data_quality(
                    pc, kp_12, male_pc[0], male_kp[0])
                
                # 选择更高的分数
                if female_score > male_score:
                    quality_scores.append((sample_id, female_score, 'female', female_reason))
                else:
                    quality_scores.append((sample_id, male_score, 'male', male_reason))
                    
            except Exception as e:
                continue
        
        # 按质量排序
        quality_scores.sort(key=lambda x: x[1], reverse=True)
        
        print(f"📊 数据质量评估结果 (前10个):")
        for i, (sample_id, score, gender, reason) in enumerate(quality_scores[:10]):
            print(f"  {i+1}. {sample_id}: {score:.3f} ({gender}, {reason})")
        
        # 选择最高质量的样本
        selected_samples = quality_scores[:max_new_samples]
        high_quality_samples = [s for s in selected_samples if s[1] > 0.5]
        
        print(f"\n✅ 选择了{len(high_quality_samples)}个高质量样本:")
        for sample_id, score, gender, reason in high_quality_samples:
            print(f"  {sample_id}: {score:.3f} ({gender})")
        
        return high_quality_samples
    
    def create_conservative_expansion_plan(self):
        """创建保守的扩展计划"""
        print("\n📋 创建保守的扩展计划")
        print("=" * 50)
        
        plan = {
            "策略名称": "保守质量优先扩展",
            "核心理念": "宁缺毋滥，质量第一",
            
            "具体计划": {
                "阶段1": {
                    "目标": "验证智能选择策略",
                    "行动": "添加2-3个最高质量样本",
                    "成功标准": "性能保持在8mm以内",
                    "失败处理": "回退到基线模型"
                },
                
                "阶段2": {
                    "目标": "小幅稳定扩展",
                    "行动": "再添加3-5个高质量样本",
                    "成功标准": "性能保持在9mm以内",
                    "失败处理": "停止扩展，分析原因"
                },
                
                "阶段3": {
                    "目标": "数据增强优化",
                    "行动": "通过数据增强而非新数据扩展",
                    "成功标准": "性能提升到6-7mm",
                    "失败处理": "保持当前最佳状态"
                }
            },
            
            "质量控制": [
                "每次只添加少量样本",
                "实时监控性能变化",
                "设置性能下降阈值",
                "保留回退机制"
            ]
        }
        
        print(f"策略: {plan['策略名称']}")
        print(f"理念: {plan['核心理念']}")
        
        print(f"\n具体计划:")
        for stage, details in plan["具体计划"].items():
            print(f"  {stage}: {details['目标']}")
            print(f"    行动: {details['行动']}")
            print(f"    成功标准: {details['成功标准']}")
        
        return plan
    
    def save_analysis_and_recommendations(self):
        """保存分析和建议"""
        analysis = {
            "expansion_failure_analysis": self.analyze_expansion_failure(),
            "smart_strategy": self.create_smart_expansion_strategy(),
            "conservative_plan": self.create_conservative_expansion_plan(),
            "key_recommendations": [
                "停止盲目添加数据，专注质量控制",
                "基于高性能模型进行迁移学习",
                "实施智能数据选择策略",
                "采用保守的渐进式扩展方法",
                "考虑数据增强而非新数据收集"
            ],
            "timestamp": "2025-07-25"
        }
        
        with open('improved_expansion_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 分析和建议已保存到 improved_expansion_analysis.json")
        return analysis

def main():
    """主函数"""
    print("🔍 改进的渐进式数据集扩展策略")
    print("Improved Progressive Dataset Expansion Strategy")
    print("=" * 60)
    
    # 创建改进的扩展器
    expander = ImprovedProgressiveExpansion()
    
    # 分析失败原因
    expander.analyze_expansion_failure()
    
    # 创建智能策略
    expander.create_smart_expansion_strategy()
    
    # 尝试加载高性能模型
    models = expander.load_high_performance_models()
    
    # 创建保守计划
    expander.create_conservative_expansion_plan()
    
    # 保存分析结果
    expander.save_analysis_and_recommendations()
    
    print(f"\n🎉 关键建议总结:")
    print(f"❌ 避免: 盲目添加大量低质量数据")
    print(f"✅ 推荐: 基于高性能模型的智能扩展")
    print(f"🎯 策略: 质量优于数量，保守渐进")
    print(f"🔧 方法: 迁移学习 + 智能选择 + 数据增强")

if __name__ == "__main__":
    main()
