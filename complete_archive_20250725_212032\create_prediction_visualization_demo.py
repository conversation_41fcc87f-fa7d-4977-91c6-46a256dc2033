#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测结果可视化演示
Prediction Results Visualization Demo
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def generate_demo_data():
    """生成演示数据"""
    print("🔧 生成演示数据...")
    
    # 生成一个骨盆形状的点云
    np.random.seed(42)
    
    # 骨盆的基本形状参数
    center = np.array([0, 0, 0])
    width = 200  # mm
    height = 150  # mm
    depth = 100   # mm
    
    # 生成椭球形点云
    n_points = 5000
    u = np.random.uniform(0, 2*np.pi, n_points)
    v = np.random.uniform(0, np.pi, n_points)
    
    x = width/2 * np.sin(v) * np.cos(u) + center[0]
    y = height/2 * np.sin(v) * np.sin(u) + center[1]
    z = depth/2 * np.cos(v) + center[2]
    
    # 添加噪声
    noise = np.random.normal(0, 2, (n_points, 3))
    point_cloud = np.column_stack([x, y, z]) + noise
    
    # 生成关键点（模拟骨盆的解剖标志点）
    keypoints_57 = []
    
    # F1区域关键点 (左侧髂骨)
    for i in range(19):
        angle = i * 2 * np.pi / 19
        kp = np.array([
            -width/3 + 20*np.cos(angle),
            height/3 + 15*np.sin(angle),
            -depth/4 + 10*np.random.randn()
        ])
        keypoints_57.append(kp)
    
    # F2区域关键点 (右侧髂骨)
    for i in range(19):
        angle = i * 2 * np.pi / 19
        kp = np.array([
            width/3 + 20*np.cos(angle),
            height/3 + 15*np.sin(angle),
            -depth/4 + 10*np.random.randn()
        ])
        keypoints_57.append(kp)
    
    # F3区域关键点 (骶骨)
    for i in range(19):
        angle = i * np.pi / 18  # 半圆
        kp = np.array([
            30*np.cos(angle),
            -height/3 + 25*np.sin(angle),
            depth/4 + 15*np.random.randn()
        ])
        keypoints_57.append(kp)
    
    keypoints_57 = np.array(keypoints_57)
    
    return point_cloud, keypoints_57

def generate_predictions_with_errors(true_keypoints, error_levels):
    """生成带有不同误差水平的预测结果"""
    predictions = {}
    
    for kp_count, error_mm in error_levels.items():
        # 选择对应数量的关键点
        if kp_count <= len(true_keypoints):
            indices = np.linspace(0, len(true_keypoints)-1, kp_count, dtype=int)
            true_kp = true_keypoints[indices]
        else:
            true_kp = true_keypoints
        
        # 添加误差生成预测
        error_std = error_mm / 3  # 3-sigma规则
        noise = np.random.normal(0, error_std, true_kp.shape)
        pred_kp = true_kp + noise
        
        predictions[kp_count] = {
            'true': true_kp,
            'pred': pred_kp,
            'error': error_mm
        }
    
    return predictions

def visualize_prediction_comparison(point_cloud, predictions, sample_title="Demo Sample"):
    """可视化预测对比"""
    print(f"🎨 创建预测结果可视化: {sample_title}")
    
    # 选择要展示的配置
    selected_configs = [3, 15, 47, 57]
    available_configs = [k for k in selected_configs if k in predictions]
    
    if len(available_configs) < 4:
        available_configs = list(predictions.keys())[:4]
    
    fig = plt.figure(figsize=(20, 15))
    
    for i, kp_count in enumerate(available_configs[:4]):
        ax = fig.add_subplot(2, 2, i+1, projection='3d')
        
        data = predictions[kp_count]
        true_kp = data['true']
        pred_kp = data['pred']
        error_mm = data['error']
        
        # 绘制点云（浅灰色，小点）
        ax.scatter(point_cloud[:, 0], point_cloud[:, 1], point_cloud[:, 2], 
                  c='lightgray', s=0.5, alpha=0.2, label='Point Cloud')
        
        # 绘制真实关键点（绿色，大点）
        ax.scatter(true_kp[:, 0], true_kp[:, 1], true_kp[:, 2], 
                  c='green', s=120, alpha=0.9, label='Ground Truth', 
                  marker='o', edgecolors='darkgreen', linewidth=1)
        
        # 绘制预测关键点（红色，大点）
        ax.scatter(pred_kp[:, 0], pred_kp[:, 1], pred_kp[:, 2], 
                  c='red', s=120, alpha=0.9, label='Prediction', 
                  marker='^', edgecolors='darkred', linewidth=1)
        
        # 绘制误差连接线
        for j in range(len(true_kp)):
            ax.plot([true_kp[j, 0], pred_kp[j, 0]], 
                   [true_kp[j, 1], pred_kp[j, 1]], 
                   [true_kp[j, 2], pred_kp[j, 2]], 
                   'b--', alpha=0.6, linewidth=1.5)
        
        # 计算实际误差
        actual_errors = np.linalg.norm(true_kp - pred_kp, axis=1)
        avg_actual_error = np.mean(actual_errors)
        
        # 设置标题和标签
        ax.set_title(f'{kp_count} Keypoints\nTarget: {error_mm:.2f}mm, Actual: {avg_actual_error:.2f}mm', 
                    fontsize=14, fontweight='bold')
        ax.set_xlabel('X (mm)', fontsize=12)
        ax.set_ylabel('Y (mm)', fontsize=12)
        ax.set_zlabel('Z (mm)', fontsize=12)
        
        # 设置图例
        ax.legend(loc='upper right', fontsize=10)
        
        # 设置坐标轴范围
        margin = 20
        ax.set_xlim([point_cloud[:, 0].min()-margin, point_cloud[:, 0].max()+margin])
        ax.set_ylim([point_cloud[:, 1].min()-margin, point_cloud[:, 1].max()+margin])
        ax.set_zlim([point_cloud[:, 2].min()-margin, point_cloud[:, 2].max()+margin])
        
        # 设置视角
        ax.view_init(elev=20, azim=45)
    
    plt.suptitle(f'Medical Pelvis Keypoint Detection - {sample_title}', 
                fontsize=16, fontweight='bold', y=0.95)
    plt.tight_layout()
    
    # 保存图片
    filename = f'prediction_visualization_{sample_title.lower().replace(" ", "_")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 可视化已保存: {filename}")
    return filename

def create_error_analysis_chart(predictions):
    """创建误差分析图表"""
    print("📊 创建误差分析图表")
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 提取数据
    kp_counts = sorted(predictions.keys())
    target_errors = [predictions[k]['error'] for k in kp_counts]
    actual_errors = []
    
    for k in kp_counts:
        true_kp = predictions[k]['true']
        pred_kp = predictions[k]['pred']
        errors = np.linalg.norm(true_kp - pred_kp, axis=1)
        actual_errors.append(np.mean(errors))
    
    # 1. 目标误差 vs 实际误差
    ax1.plot(kp_counts, target_errors, 'o-', label='Target Error', linewidth=2, markersize=8)
    ax1.plot(kp_counts, actual_errors, 's-', label='Actual Error', linewidth=2, markersize=8)
    ax1.axhline(y=10, color='orange', linestyle='--', alpha=0.7, label='Medical Grade (10mm)')
    ax1.axhline(y=5, color='red', linestyle='--', alpha=0.7, label='Excellent Grade (5mm)')
    ax1.set_xlabel('Number of Keypoints')
    ax1.set_ylabel('Average Error (mm)')
    ax1.set_title('Performance vs Keypoint Count')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 误差分布箱线图
    error_distributions = []
    labels = []
    for k in kp_counts[:6]:  # 只显示前6个配置
        true_kp = predictions[k]['true']
        pred_kp = predictions[k]['pred']
        errors = np.linalg.norm(true_kp - pred_kp, axis=1)
        error_distributions.append(errors)
        labels.append(f'{k}kp')
    
    ax2.boxplot(error_distributions, labels=labels)
    ax2.set_ylabel('Error (mm)')
    ax2.set_title('Error Distribution by Keypoint Count')
    ax2.grid(True, alpha=0.3)
    
    # 3. 医疗级达标率
    medical_rates = []
    excellent_rates = []
    
    for k in kp_counts:
        true_kp = predictions[k]['true']
        pred_kp = predictions[k]['pred']
        errors = np.linalg.norm(true_kp - pred_kp, axis=1)
        
        medical_rate = np.sum(errors <= 10) / len(errors) * 100
        excellent_rate = np.sum(errors <= 5) / len(errors) * 100
        
        medical_rates.append(medical_rate)
        excellent_rates.append(excellent_rate)
    
    width = 0.35
    x = np.arange(len(kp_counts))
    ax3.bar(x - width/2, medical_rates, width, label='Medical Grade (≤10mm)', alpha=0.8)
    ax3.bar(x + width/2, excellent_rates, width, label='Excellent Grade (≤5mm)', alpha=0.8)
    ax3.set_xlabel('Number of Keypoints')
    ax3.set_ylabel('Success Rate (%)')
    ax3.set_title('Medical Grade Achievement Rate')
    ax3.set_xticks(x)
    ax3.set_xticklabels([str(k) for k in kp_counts])
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 架构性能对比（基于实验结果）
    arch_data = {
        'balanced': [5.86, 6.42, 6.69, 6.37, 6.38, 6.07, 6.27],
        'enhanced': [7.14, 6.42, 6.13, 6.27, 6.00],
        'lightweight': [6.51],
        'auto': [6.64]
    }
    
    arch_names = list(arch_data.keys())
    arch_means = [np.mean(arch_data[arch]) for arch in arch_names]
    arch_stds = [np.std(arch_data[arch]) if len(arch_data[arch]) > 1 else 0 for arch in arch_names]
    
    colors = ['#F18F01', '#C73E1D', '#A23B72', '#2E86AB']
    bars = ax4.bar(arch_names, arch_means, yerr=arch_stds, capsize=5, 
                   color=colors, alpha=0.7)
    ax4.set_ylabel('Average Error (mm)')
    ax4.set_title('Architecture Performance Comparison')
    ax4.grid(True, alpha=0.3, axis='y')
    
    # 添加数值标签
    for bar, mean in zip(bars, arch_means):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                f'{mean:.2f}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('error_analysis_comprehensive.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 误差分析图表已保存: error_analysis_comprehensive.png")

def main():
    """主函数"""
    print("🎯 预测结果可视化演示")
    print("=" * 60)
    
    # 生成演示数据
    point_cloud, keypoints_57 = generate_demo_data()
    
    # 基于实验结果的误差水平
    error_levels = {
        3: 7.14,   # enhanced
        6: 6.42,   # enhanced  
        9: 6.13,   # enhanced
        12: 6.27,  # enhanced
        15: 5.86,  # balanced (最佳)
        19: 6.42,  # balanced
        24: 6.69,  # balanced
        28: 6.64,  # auto
        33: 6.51,  # lightweight
        38: 6.37,  # balanced
        43: 6.38,  # balanced
        47: 6.00,  # enhanced
        52: 6.07,  # balanced
        57: 6.27   # balanced
    }
    
    # 生成预测结果
    predictions = generate_predictions_with_errors(keypoints_57, error_levels)
    
    # 创建可视化
    visualize_prediction_comparison(point_cloud, predictions, "Optimal Models")
    
    # 创建误差分析
    create_error_analysis_chart(predictions)
    
    # 生成总结报告
    print("\n📋 可视化总结")
    print("=" * 50)
    print("✅ 生成的文件:")
    print("  - prediction_visualization_optimal_models.png")
    print("  - error_analysis_comprehensive.png")
    print("\n🎯 关键发现:")
    print(f"  - 最佳性能: {min(error_levels.values()):.2f}mm (15关键点)")
    print(f"  - 性能范围: {min(error_levels.values()):.2f}-{max(error_levels.values()):.2f}mm")
    print("  - 所有配置均达到医疗级标准(≤10mm)")
    print("  - balanced架构在多数情况下表现最佳")

if __name__ == "__main__":
    main()
