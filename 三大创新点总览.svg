<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="1280" height="720" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="url(#headerGrad)"/>
  <text x="640" y="50" text-anchor="middle" fill="white" 
        font-family="SimHei, Arial, sans-serif" font-size="36" font-weight="bold">
    三大核心创新点
  </text>
  
  <!-- Innovation 1: Novel Architecture -->
  <rect x="50" y="100" width="380" height="560" rx="15" fill="white" stroke="#10b981" stroke-width="3"/>
  <text x="240" y="130" text-anchor="middle" fill="#059669" 
        font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    创新点一
  </text>
  <text x="240" y="160" text-anchor="middle" fill="#059669" 
        font-family="SimHei, Arial, sans-serif" font-size="18" font-weight="bold">
    新颖的3D关键点检测神经网络架构
  </text>
  
  <!-- Architecture diagram -->
  <rect x="70" y="180" width="340" height="120" rx="10" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="240" y="205" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    粗到细两阶段框架
  </text>
  
  <!-- Stage 1 -->
  <rect x="80" y="220" width="140" height="35" rx="5" fill="#dbeafe" stroke="#3b82f6" stroke-width="1"/>
  <text x="150" y="242" text-anchor="middle" fill="#1e40af" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    阶段1：潜在区域检测
  </text>
  
  <!-- Arrow -->
  <path d="M 225 237 L 255 237" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <defs>
    <marker id="arrowhead" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#374151"/>
    </marker>
  </defs>
  
  <!-- Stage 2 -->
  <rect x="260" y="220" width="140" height="35" rx="5" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
  <text x="330" y="242" text-anchor="middle" fill="#d97706" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    阶段2：精确关键点定位
  </text>
  
  <!-- Key features -->
  <text x="80" y="330" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    核心特点：
  </text>
  <text x="80" y="355" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="13">
    • 专为密集复杂点云设计
  </text>
  <text x="80" y="375" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="13">
    • 检测具有特定语义意义的关键点
  </text>
  <text x="80" y="395" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="13">
    • 直接在3D点云上进行检测
  </text>
  <text x="80" y="415" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="13">
    • 无需额外2D图像标注
  </text>
  <text x="80" y="435" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="13">
    • 实现精确的一对一关键点检测
  </text>
  
  <!-- Medical application -->
  <rect x="70" y="460" width="340" height="80" rx="8" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="1"/>
  <text x="240" y="485" text-anchor="middle" fill="#0c4a6e" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    医学应用验证
  </text>
  <text x="80" y="505" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 颅骨解剖学关键点检测
  </text>
  <text x="80" y="525" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 胫骨关键点检测
  </text>
  
  <!-- Performance -->
  <rect x="70" y="550" width="340" height="100" rx="8" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="240" y="575" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    性能表现
  </text>
  <text x="80" y="595" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 颅骨：平均径向误差1.43mm
  </text>
  <text x="80" y="615" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 胫骨：平均径向误差1.54mm
  </text>
  <text x="80" y="635" fill="#15803d" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    • 与2D方法性能相当
  </text>
  
  <!-- Innovation 2: Penalty Dice Loss -->
  <rect x="450" y="100" width="380" height="560" rx="15" fill="white" stroke="#ef4444" stroke-width="3"/>
  <text x="640" y="130" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    创新点二
  </text>
  <text x="640" y="160" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="18" font-weight="bold">
    Penalty Dice Loss 惩罚骰子损失
  </text>
  
  <!-- Problem illustration -->
  <rect x="470" y="180" width="340" height="120" rx="10" fill="#fef2f2" stroke="#fca5a5" stroke-width="1"/>
  <text x="640" y="205" text-anchor="middle" fill="#7f1d1d" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    解决的问题
  </text>
  
  <!-- Imbalance visualization -->
  <circle cx="550" cy="240" r="25" fill="#f3f4f6" stroke="#9ca3af" stroke-width="1"/>
  <text x="550" y="275" text-anchor="middle" fill="#6b7280" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    背景点99.5%
  </text>
  
  <circle cx="640" cy="240" r="6" fill="#fca5a5" stroke="#ef4444" stroke-width="1"/>
  <circle cx="660" cy="245" r="6" fill="#fca5a5" stroke="#ef4444" stroke-width="1"/>
  <circle cx="650" cy="260" r="6" fill="#fca5a5" stroke="#ef4444" stroke-width="1"/>
  <text x="650" y="285" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    关键点区域0.5%
  </text>
  
  <text x="730" y="250" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    类别严重不平衡
  </text>
  
  <!-- Solution approach -->
  <text x="480" y="330" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    解决方案：
  </text>
  <text x="480" y="355" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="13">
    • 针对小而关键的区域
  </text>
  <text x="480" y="375" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="13">
    • 防止潜在区域遗漏
  </text>
  <text x="480" y="395" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="13">
    • 显式惩罚未检测区域
  </text>
  <text x="480" y="415" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="13">
    • 强制网络关注少数类
  </text>
  
  <!-- Components -->
  <rect x="470" y="440" width="340" height="100" rx="8" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
  <text x="640" y="465" text-anchor="middle" fill="#92400e" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    损失函数组成
  </text>
  <text x="480" y="485" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    1. 交叉熵损失：基本分类准确性
  </text>
  <text x="480" y="505" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    2. Dice损失：区域重叠相似性
  </text>
  <text x="480" y="525" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    3. 惩罚项：未检测区域的指数惩罚
  </text>
  
  <!-- Results -->
  <rect x="470" y="550" width="340" height="100" rx="8" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="1"/>
  <text x="640" y="575" text-anchor="middle" fill="#0c4a6e" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    效果提升
  </text>
  <text x="480" y="595" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • mIoU从62.59%提升到66.60%
  </text>
  <text x="480" y="615" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 在更低k值下实现稳健检测
  </text>
  <text x="480" y="635" fill="#0c4a6e" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    • 显著减少区域遗漏现象
  </text>
  
  <!-- Innovation 3: Double SoftMax -->
  <rect x="850" y="100" width="380" height="560" rx="15" fill="white" stroke="#8b5cf6" stroke-width="3"/>
  <text x="1040" y="130" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    创新点三
  </text>
  <text x="1040" y="160" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="18" font-weight="bold">
    创新的关键点定位机制
  </text>
  
  <!-- Mechanism overview -->
  <rect x="870" y="180" width="340" height="120" rx="10" fill="#f3e8ff" stroke="#c084fc" stroke-width="1"/>
  <text x="1040" y="205" text-anchor="middle" fill="#6b21a8" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    双SoftMax权重机制
  </text>
  
  <!-- Two-stage process -->
  <rect x="880" y="220" width="150" height="35" rx="5" fill="#fef2f2" stroke="#ef4444" stroke-width="1"/>
  <text x="955" y="242" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="11" font-weight="bold">
    第一次SoftMax
  </text>
  
  <path d="M 1035 237 L 1065 237" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <rect x="1070" y="220" width="150" height="35" rx="5" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="1145" y="242" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="11" font-weight="bold">
    第二次SoftMax
  </text>
  
  <text x="880" y="275" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="10">
    初始权重计算
  </text>
  <text x="1080" y="275" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="10">
    阈值过滤细化
  </text>
  
  <!-- Technical components -->
  <text x="880" y="330" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    技术组件：
  </text>
  <text x="880" y="355" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="13">
    • PointNet编码器：特征提取
  </text>
  <text x="880" y="375" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="13">
    • 残差模块：维度降低
  </text>
  <text x="880" y="395" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="13">
    • 双SoftMax机制：权重计算
  </text>
  <text x="880" y="415" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="13">
    • 加权平均：最终坐标
  </text>
  
  <!-- Problem solved -->
  <rect x="870" y="440" width="340" height="100" rx="8" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
  <text x="1040" y="465" text-anchor="middle" fill="#92400e" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    解决的关键问题
  </text>
  <text x="880" y="485" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 高密度点云中的权重均匀化
  </text>
  <text x="880" y="505" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 相似点特征导致的平均化效应
  </text>
  <text x="880" y="525" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 精确定位的挑战
  </text>
  
  <!-- Performance improvement -->
  <rect x="870" y="550" width="340" height="100" rx="8" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="1040" y="575" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    精度提升
  </text>
  <text x="880" y="595" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 相比单一SoftMax提升0.19mm
  </text>
  <text x="880" y="615" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 实现21%的精度改善
  </text>
  <text x="880" y="635" fill="#15803d" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    • 选择性权重分布更精确
  </text>
</svg>
