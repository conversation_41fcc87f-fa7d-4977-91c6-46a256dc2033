#!/usr/bin/env python3
"""
Optimize F3 Models - Advanced Architectures

Try different advanced architectures to improve F3 keypoint detection performance.
Current baseline: 18.40mm, Target: <10mm medical-grade precision.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import torch.nn.functional as F
import numpy as np
from pathlib import Path
import time
import json

class F3Dataset(torch.utils.data.Dataset):
    """F3 dataset with augmentation"""
    
    def __init__(self, data_dir, split, augment=False):
        self.split_dir = Path(data_dir) / split
        self.files = list(self.split_dir.glob('*_keypoints.npy'))
        self.augment = augment and split == 'train'
        print(f"{split}: {len(self.files)} samples")
        
    def __len__(self):
        return len(self.files)
    
    def __getitem__(self, idx):
        kp_file = self.files[idx]
        sample_id = kp_file.stem.replace('_keypoints', '')
        
        keypoints = np.load(kp_file).astype(np.float32)
        pc_file = kp_file.parent / f"{sample_id}_pointcloud.npy"
        pointcloud = np.load(pc_file).astype(np.float32)
        
        # Data augmentation
        if self.augment:
            # Random rotation (small angle for medical data)
            angle = np.random.uniform(-np.pi/12, np.pi/12)  # ±15 degrees
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ], dtype=np.float32)
            
            pointcloud = pointcloud @ rotation_matrix.T
            keypoints = keypoints @ rotation_matrix.T
            
            # Random translation
            translation = np.random.uniform(-2.0, 2.0, 3).astype(np.float32)
            pointcloud += translation
            keypoints += translation
            
            # Random scaling
            scale = np.random.uniform(0.95, 1.05)
            pointcloud *= scale
            keypoints *= scale
            
            # Add noise to point cloud
            noise = np.random.normal(0, 0.1, pointcloud.shape).astype(np.float32)
            pointcloud += noise
        
        return torch.FloatTensor(pointcloud), torch.FloatTensor(keypoints)

# Model 1: Enhanced PointNet with Attention
class PointNetWithAttention(nn.Module):
    """PointNet with self-attention mechanism"""
    
    def __init__(self, num_keypoints=19):
        super().__init__()
        
        # Feature extraction
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
        # Self-attention
        self.attention = nn.MultiheadAttention(512, 8, batch_first=True)
        
        # Regression head
        self.fc1 = nn.Linear(512, 256)
        self.fc2 = nn.Linear(256, 128)
        self.fc3 = nn.Linear(128, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # (B, 3, N)
        
        # Feature extraction
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        x = F.relu(self.bn4(self.conv4(x)))  # (B, 512, N)
        
        # Self-attention
        x = x.transpose(2, 1)  # (B, N, 512)
        x_att, _ = self.attention(x, x, x)
        x = x + x_att  # Residual connection
        
        # Global max pooling
        x = torch.max(x, 1)[0]  # (B, 512)
        
        # Regression
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x.view(batch_size, 19, 3)

# Model 2: PointNet++ Style with Hierarchical Features
class PointNetPlusPlus(nn.Module):
    """Simplified PointNet++ for keypoint detection"""
    
    def __init__(self, num_keypoints=19):
        super().__init__()
        
        # Set abstraction layers
        self.sa1_conv = nn.Conv1d(3, 64, 1)
        self.sa1_bn = nn.BatchNorm1d(64)
        
        self.sa2_conv = nn.Conv1d(64, 128, 1)
        self.sa2_bn = nn.BatchNorm1d(128)
        
        self.sa3_conv = nn.Conv1d(128, 256, 1)
        self.sa3_bn = nn.BatchNorm1d(256)
        
        self.sa4_conv = nn.Conv1d(256, 512, 1)
        self.sa4_bn = nn.BatchNorm1d(512)
        
        # Global feature
        self.global_conv = nn.Conv1d(512, 1024, 1)
        self.global_bn = nn.BatchNorm1d(1024)
        
        # Feature propagation (simplified)
        self.fp_conv1 = nn.Conv1d(1024 + 512, 512, 1)
        self.fp_conv2 = nn.Conv1d(512 + 256, 256, 1)
        
        # Final regression
        self.final_conv = nn.Conv1d(256, 128, 1)
        self.fc = nn.Linear(128, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # (B, 3, N)
        
        # Set abstraction
        x1 = F.relu(self.sa1_bn(self.sa1_conv(x)))      # (B, 64, N)
        x2 = F.relu(self.sa2_bn(self.sa2_conv(x1)))     # (B, 128, N)
        x3 = F.relu(self.sa3_bn(self.sa3_conv(x2)))     # (B, 256, N)
        x4 = F.relu(self.sa4_bn(self.sa4_conv(x3)))     # (B, 512, N)
        
        # Global feature
        global_feat = F.relu(self.global_bn(self.global_conv(x4)))  # (B, 1024, N)
        global_feat_max = torch.max(global_feat, 2)[0]  # (B, 1024)
        
        # Feature propagation (simplified)
        global_feat_expanded = global_feat_max.unsqueeze(2).expand(-1, -1, x4.size(2))
        fp1 = torch.cat([global_feat_expanded, x4], 1)  # (B, 1024+512, N)
        fp1 = F.relu(self.fp_conv1(fp1))  # (B, 512, N)
        
        fp2 = torch.cat([fp1, x3], 1)  # (B, 512+256, N)
        fp2 = F.relu(self.fp_conv2(fp2))  # (B, 256, N)
        
        # Final features
        final_feat = F.relu(self.final_conv(fp2))  # (B, 128, N)
        final_feat = torch.max(final_feat, 2)[0]  # (B, 128)
        
        # Regression
        final_feat = self.dropout(final_feat)
        output = self.fc(final_feat)
        
        return output.view(batch_size, 19, 3)

# Model 3: ResNet-style PointNet
class ResPointNet(nn.Module):
    """PointNet with residual connections"""
    
    def __init__(self, num_keypoints=19):
        super().__init__()
        
        # Initial conv
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.bn1 = nn.BatchNorm1d(64)
        
        # Residual blocks
        self.res_block1 = self._make_res_block(64, 128)
        self.res_block2 = self._make_res_block(128, 256)
        self.res_block3 = self._make_res_block(256, 512)
        
        # Global feature
        self.global_conv = nn.Conv1d(512, 1024, 1)
        self.global_bn = nn.BatchNorm1d(1024)
        
        # Regression head
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.3)
        
    def _make_res_block(self, in_channels, out_channels):
        return nn.Sequential(
            nn.Conv1d(in_channels, out_channels, 1),
            nn.BatchNorm1d(out_channels),
            nn.ReLU(),
            nn.Conv1d(out_channels, out_channels, 1),
            nn.BatchNorm1d(out_channels)
        )
    
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # (B, 3, N)
        
        # Initial features
        x = F.relu(self.bn1(self.conv1(x)))  # (B, 64, N)
        
        # Residual blocks with skip connections
        identity = x
        x = self.res_block1(x)  # (B, 128, N)
        if identity.size(1) != x.size(1):
            identity = F.adaptive_avg_pool1d(identity.unsqueeze(0), x.size(1)).squeeze(0)
        
        identity = x
        x = self.res_block2(x)  # (B, 256, N)
        x = F.relu(x + F.adaptive_avg_pool1d(identity.unsqueeze(0), x.size(1)).squeeze(0))
        
        identity = x
        x = self.res_block3(x)  # (B, 512, N)
        x = F.relu(x + F.adaptive_avg_pool1d(identity.unsqueeze(0), x.size(1)).squeeze(0))
        
        # Global feature
        x = F.relu(self.global_bn(self.global_conv(x)))  # (B, 1024, N)
        x = torch.max(x, 2)[0]  # (B, 1024)
        
        # Regression
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x.view(batch_size, 19, 3)

def calculate_metrics(pred, target):
    """Calculate evaluation metrics"""
    pred_np = pred.cpu().numpy()
    target_np = target.cpu().numpy()
    
    distances = np.linalg.norm(pred_np - target_np, axis=-1)
    mean_error = np.mean(distances)
    
    acc_1mm = np.mean(distances <= 1.0) * 100
    acc_2mm = np.mean(distances <= 2.0) * 100
    acc_5mm = np.mean(distances <= 5.0) * 100
    acc_10mm = np.mean(distances <= 10.0) * 100
    
    return {
        'mean_error': mean_error,
        'acc_1mm': acc_1mm,
        'acc_2mm': acc_2mm,
        'acc_5mm': acc_5mm,
        'acc_10mm': acc_10mm
    }

def train_model(model, model_name, device, train_loader, val_loader, epochs=30):
    """Train a single model"""
    
    print(f"\n🚀 训练 {model_name}")
    print("=" * 60)
    
    model = model.to(device)
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=5, min_lr=1e-6
    )
    
    best_error = float('inf')
    history = []
    patience_counter = 0
    patience = 10
    
    for epoch in range(epochs):
        # Training
        model.train()
        train_loss = 0
        
        for pc, kp in train_loader:
            pc, kp = pc.to(device), kp.to(device)
            
            optimizer.zero_grad()
            pred = model(pc)
            loss = criterion(pred, kp)
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # Validation
        model.eval()
        val_loss = 0
        all_pred = []
        all_target = []
        
        with torch.no_grad():
            for pc, kp in val_loader:
                pc, kp = pc.to(device), kp.to(device)
                pred = model(pc)
                loss = criterion(pred, kp)
                val_loss += loss.item()
                
                all_pred.append(pred.cpu())
                all_target.append(kp.cpu())
        
        val_loss /= len(val_loader)
        
        # Metrics
        all_pred = torch.cat(all_pred, dim=0)
        all_target = torch.cat(all_target, dim=0)
        metrics = calculate_metrics(all_pred, all_target)
        
        # Scheduler
        scheduler.step(val_loss)
        
        # Record
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'val_error': metrics['mean_error'],
            'acc_5mm': metrics['acc_5mm'],
            'acc_10mm': metrics['acc_10mm']
        })
        
        # Print progress
        if epoch % 5 == 0 or epoch == epochs - 1:
            print(f"Epoch {epoch+1:2d}: "
                  f"Train={train_loss:.3f}, Val={val_loss:.3f}, "
                  f"Error={metrics['mean_error']:.2f}mm, "
                  f"5mm={metrics['acc_5mm']:.1f}%, "
                  f"10mm={metrics['acc_10mm']:.1f}%")
        
        # Save best model
        if metrics['mean_error'] < best_error:
            best_error = metrics['mean_error']
            patience_counter = 0
            torch.save(model.state_dict(), f'best_{model_name.lower().replace(" ", "_")}.pth')
        else:
            patience_counter += 1
        
        # Early stopping
        if patience_counter >= patience:
            print(f"   早停: {patience}轮无改进")
            break
        
        # Success check
        if metrics['mean_error'] <= 5.0:
            print(f"   🎉 达到医疗级精度!")
            break
    
    print(f"   最佳误差: {best_error:.2f}mm")
    
    return best_error, history

def main():
    """Main optimization function"""
    
    print("🎯 **F3模型架构优化**")
    print("📊 **当前基线: 18.40mm → 目标: <10mm医疗级精度**")
    print("🧠 **测试架构: PointNet+Attention, PointNet++, ResPointNet**")
    print("=" * 80)
    
    # Setup
    device = torch.device('cuda:2' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # Data
    train_dataset = F3Dataset("F3SimpleDataset", "train", augment=True)
    val_dataset = F3Dataset("F3SimpleDataset", "val", augment=False)
    
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False, num_workers=2)
    
    # Models to test
    models = {
        "PointNet+Attention": PointNetWithAttention(),
        "PointNet++": PointNetPlusPlus(),
        "ResPointNet": ResPointNet()
    }
    
    results = {}
    
    # Train each model
    for model_name, model in models.items():
        try:
            best_error, history = train_model(
                model, model_name, device, train_loader, val_loader, epochs=25
            )
            results[model_name] = {
                'best_error': best_error,
                'history': history,
                'parameters': sum(p.numel() for p in model.parameters())
            }
        except Exception as e:
            print(f"❌ {model_name} 训练失败: {e}")
            results[model_name] = {'error': str(e)}
    
    # Results summary
    print(f"\n🏆 **F3模型优化结果总结**")
    print("=" * 80)
    
    successful_models = {k: v for k, v in results.items() if 'best_error' in v}
    
    if successful_models:
        # Sort by performance
        sorted_models = sorted(successful_models.items(), key=lambda x: x[1]['best_error'])
        
        print(f"📊 **性能排名**:")
        for i, (name, result) in enumerate(sorted_models):
            error = result['best_error']
            params = result['parameters']
            improvement = (18.40 - error) / 18.40 * 100
            
            print(f"   {i+1}. {name}:")
            print(f"      误差: {error:.2f}mm")
            print(f"      改进: {improvement:+.1f}% vs 基线")
            print(f"      参数: {params:,}")
            print(f"      评估: {'🎉 医疗级' if error <= 10 else '✅ 良好' if error <= 15 else '⚠️ 需改进'}")
        
        best_model, best_result = sorted_models[0]
        print(f"\n🥇 **最佳模型: {best_model}**")
        print(f"   最终误差: {best_result['best_error']:.2f}mm")
        print(f"   vs 基线改进: {(18.40 - best_result['best_error']) / 18.40 * 100:.1f}%")
        
        if best_result['best_error'] <= 10.0:
            print(f"   🎉 达到医疗级精度目标!")
        elif best_result['best_error'] <= 15.0:
            print(f"   ✅ 显著改进，接近医疗级精度")
        else:
            print(f"   ⚠️ 有改进但未达到目标")
    
    # Save results
    with open('f3_optimization_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📁 优化结果已保存: f3_optimization_results.json")
    
    return results

if __name__ == "__main__":
    try:
        results = main()
        print(f"\n🎉 F3模型优化完成!")
    except Exception as e:
        print(f"❌ 优化过程出错: {e}")
        import traceback
        traceback.print_exc()
