#!/usr/bin/env python3
"""
CPU Test Training

Test training on CPU to isolate CUDA issues.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import time
import json

class TinyF3Dataset(Dataset):
    """Tiny dataset for testing"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 1024):
        
        self.num_points = num_points
        
        # Load only first few samples
        data = np.load(data_path, allow_pickle=True)
        
        point_clouds = data['point_clouds'][:10]  # Only 10 samples
        keypoints = data['keypoints'][:10]
        
        if split == 'train':
            self.point_clouds = point_clouds[:6]
            self.keypoints = keypoints[:6]
        elif split == 'val':
            self.point_clouds = point_clouds[6:8]
            self.keypoints = keypoints[6:8]
        else:  # test
            self.point_clouds = point_clouds[8:10]
            self.keypoints = keypoints[8:10]
        
        print(f"   {split}: {len(self.point_clouds)} 样本")
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        # Downsample to very small size
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints)
        }

class TinyPointNet(nn.Module):
    """Tiny PointNet for testing"""
    
    def __init__(self):
        super(TinyPointNet, self).__init__()
        
        self.conv1 = nn.Conv1d(3, 32, 1)
        self.conv2 = nn.Conv1d(32, 64, 1)
        self.fc = nn.Linear(64, 19 * 3)
        
    def forward(self, x):
        # x: (batch, points, 3)
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # (batch, 3, points)
        
        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))
        x = torch.max(x, 2)[0]  # Global max pooling
        x = self.fc(x)
        
        return x.view(batch_size, 19, 3)

def test_cpu_training():
    """Test training on CPU"""
    
    print("🧪 **CPU训练测试**")
    print("=" * 40)
    
    # Force CPU
    device = torch.device('cpu')
    print(f"🖥️  设备: {device}")
    
    # Tiny dataset
    dataset_path = "high_quality_f3_dataset.npz"
    num_points = 1024
    
    print(f"📦 创建微型数据集...")
    train_dataset = TinyF3Dataset(dataset_path, 'train', num_points)
    val_dataset = TinyF3Dataset(dataset_path, 'val', num_points)
    
    # Tiny batch size
    batch_size = 2
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    print(f"📊 数据: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 批大小{batch_size}")
    
    # Tiny model
    model = TinyPointNet().to(device)
    print(f"🧠 模型参数: {sum(p.numel() for p in model.parameters()):,}")
    
    # Simple training
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.01)
    
    print(f"\n🎯 开始微型训练...")
    
    for epoch in range(5):  # Only 5 epochs
        print(f"\nEpoch {epoch+1}/5")
        
        # Training
        model.train()
        train_loss = 0.0
        
        for batch in train_loader:
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            pred_keypoints = model(point_cloud)
            loss = criterion(pred_keypoints, keypoints)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # Validation
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for batch in val_loader:
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                val_loss += loss.item()
        
        val_loss /= len(val_loader)
        
        print(f"训练损失: {train_loss:.4f}, 验证损失: {val_loss:.4f}")
    
    print("✅ CPU训练测试成功!")
    return True

def test_gpu_simple():
    """Test simple GPU operations"""
    
    print("\n🧪 **GPU简单测试**")
    print("=" * 40)
    
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return False
    
    try:
        device = torch.device('cuda:1')
        print(f"🖥️  测试设备: {device}")
        
        # Simple tensor operations
        x = torch.randn(10, 3).to(device)
        y = torch.randn(10, 3).to(device)
        z = x + y
        
        print(f"✅ 基本张量运算成功")
        
        # Simple model
        model = nn.Linear(3, 3).to(device)
        output = model(x)
        
        print(f"✅ 简单模型前向传播成功")
        
        # Simple training step
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters())
        
        loss = criterion(output, y)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        print(f"✅ 简单训练步骤成功")
        
        # Clean up
        del x, y, z, model, output
        torch.cuda.empty_cache()
        
        return True
        
    except Exception as e:
        print(f"❌ GPU测试失败: {e}")
        return False

def test_data_loading_only():
    """Test just data loading without training"""
    
    print("\n🧪 **纯数据加载测试**")
    print("=" * 40)
    
    try:
        dataset_path = "high_quality_f3_dataset.npz"
        
        # Create dataset
        train_dataset = TinyF3Dataset(dataset_path, 'train', 1024)
        train_loader = DataLoader(train_dataset, batch_size=2, shuffle=True)
        
        print("📦 数据集创建成功")
        
        # Test iteration
        for i, batch in enumerate(train_loader):
            point_cloud = batch['point_cloud']
            keypoints = batch['keypoints']
            
            print(f"批次 {i+1}: 点云{point_cloud.shape}, 关键点{keypoints.shape}")
            
            if i >= 2:  # Only test first few batches
                break
        
        print("✅ 数据加载测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run diagnostic tests"""
    
    print("🔧 **训练问题诊断测试**")
    print("=" * 50)
    
    tests = [
        ("数据加载", test_data_loading_only),
        ("GPU简单操作", test_gpu_simple),
        ("CPU训练", test_cpu_training)
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print(f"{'='*50}")
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} 成功")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 崩溃: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📋 **诊断完成**")
    print("💡 如果所有测试都通过，问题可能在于:")
    print("   1. 模型太复杂")
    print("   2. 批处理太大")
    print("   3. 内存不足")
    print("   4. CUDA版本兼容性")

if __name__ == "__main__":
    main()
