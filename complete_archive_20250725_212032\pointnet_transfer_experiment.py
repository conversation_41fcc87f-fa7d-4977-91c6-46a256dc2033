#!/usr/bin/env python3
"""
PointNet++迁移学习实验
PointNet++ Transfer Learning Experiment
实现基于预训练模型的迁移学习来改进性能
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
import json
from pathlib import Path
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

class PointNetPlusTransferModel(nn.Module):
    """基于PointNet++的迁移学习模型"""
    
    def __init__(self, num_points=50000, num_keypoints=12, pretrained_dim=1024):
        super().__init__()
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 模拟预训练的PointNet++特征提取器
        self.pretrained_backbone = nn.Sequential(
            # 第一层特征提取
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            
            # 第二层特征提取
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.<PERSON>L<PERSON>(),
            
            # 第三层特征提取
            nn.Conv1d(128, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            
            # 第四层特征提取
            nn.Conv1d(256, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            
            # 高级特征提取
            nn.Conv1d(512, pretrained_dim, 1),
            nn.BatchNorm1d(pretrained_dim),
            nn.ReLU(),
        )
        
        # 医疗领域适配层
        self.domain_adapter = nn.Sequential(
            nn.Conv1d(pretrained_dim, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.1),
            
            nn.Conv1d(512, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.1),
        )
        
        # 关键点检测头
        self.keypoint_detector = nn.Sequential(
            nn.Linear(256, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.2),
            
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.1),
            
            nn.Linear(128, num_keypoints * 3)
        )
        
        # 初始化新添加的层
        self._initialize_new_layers()
        
        # 模拟加载预训练权重
        self._simulate_pretrained_weights()
        
    def _initialize_new_layers(self):
        """初始化新添加的层"""
        for module in [self.domain_adapter, self.keypoint_detector]:
            for layer in module:
                if isinstance(layer, (nn.Linear, nn.Conv1d)):
                    nn.init.kaiming_normal_(layer.weight)
                    if layer.bias is not None:
                        nn.init.constant_(layer.bias, 0)
    
    def _simulate_pretrained_weights(self):
        """模拟预训练权重（实际应用中应加载真实预训练权重）"""
        # 这里我们使用特殊的初始化来模拟预训练效果
        for layer in self.pretrained_backbone:
            if isinstance(layer, (nn.Conv1d, nn.Linear)):
                # 使用更好的初始化来模拟预训练效果
                nn.init.xavier_normal_(layer.weight, gain=0.8)
                if layer.bias is not None:
                    nn.init.constant_(layer.bias, 0)
    
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 预训练特征提取
        pretrained_features = self.pretrained_backbone(x)  # [B, 1024, N]
        
        # 医疗领域适配
        adapted_features = self.domain_adapter(pretrained_features)  # [B, 256, N]
        
        # 全局特征聚合
        global_features = torch.max(adapted_features, 2)[0]  # [B, 256]
        
        # 关键点检测
        keypoints = self.keypoint_detector(global_features)  # [B, num_keypoints*3]
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints
    
    def freeze_pretrained_layers(self):
        """冻结预训练层"""
        for param in self.pretrained_backbone.parameters():
            param.requires_grad = False
        print("✅ 预训练层已冻结")
    
    def unfreeze_pretrained_layers(self):
        """解冻预训练层"""
        for param in self.pretrained_backbone.parameters():
            param.requires_grad = True
        print("✅ 预训练层已解冻")
    
    def get_trainable_parameters(self):
        """获取可训练参数数量"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)

class TransferLearningTrainer:
    """迁移学习训练器"""
    
    def __init__(self, device='cuda:1'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        self.training_history = []
        
    def load_data(self):
        """加载数据"""
        print("📥 加载训练数据")
        print("=" * 50)
        
        try:
            # 加载女性数据
            female_data = np.load('archive/old_experiments/f3_reduced_12kp_female.npz')
            female_pc = female_data['point_clouds']
            female_kp = female_data['keypoints']
            
            # 加载男性数据
            male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz')
            male_pc = male_data['point_clouds']
            male_kp = male_data['keypoints']
            
            # 合并数据
            all_pc = np.vstack([female_pc, male_pc])
            all_kp = np.vstack([female_kp, male_kp])
            
            print(f"✅ 数据加载成功:")
            print(f"   女性: {len(female_pc)}样本")
            print(f"   男性: {len(male_pc)}样本")
            print(f"   总计: {len(all_pc)}样本")
            
            return all_pc, all_kp
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return None, None
    
    def stage1_frozen_training(self, model, train_pc, train_kp, val_pc, val_kp):
        """阶段1: 冻结预训练层训练"""
        print("\n🎯 阶段1: 冻结预训练层训练")
        print("=" * 50)
        
        # 冻结预训练层
        model.freeze_pretrained_layers()
        
        # 优化器和损失函数
        optimizer = optim.Adam(
            filter(lambda p: p.requires_grad, model.parameters()),
            lr=1e-3, weight_decay=1e-4
        )
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, patience=10, factor=0.5
        )
        
        print(f"🏗️ 阶段1训练配置:")
        print(f"   可训练参数: {model.get_trainable_parameters():,}")
        print(f"   学习率: 1e-3")
        print(f"   训练样本: {len(train_pc)}")
        
        # 转换为张量
        train_pc_tensor = torch.FloatTensor(train_pc).to(self.device)
        train_kp_tensor = torch.FloatTensor(train_kp).to(self.device)
        val_pc_tensor = torch.FloatTensor(val_pc).to(self.device)
        val_kp_tensor = torch.FloatTensor(val_kp).to(self.device)
        
        # 创建数据加载器
        batch_size = min(8, len(train_pc) // 4) if len(train_pc) >= 16 else 4
        train_dataset = TensorDataset(train_pc_tensor, train_kp_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        
        # 训练循环
        best_val_loss = float('inf')
        patience = 0
        
        for epoch in range(50):
            # 训练
            model.train()
            train_loss = 0.0
            
            for batch_pc, batch_kp in train_loader:
                optimizer.zero_grad()
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                loss.backward()
                optimizer.step()
                train_loss += loss.item()
            
            # 验证
            model.eval()
            with torch.no_grad():
                val_pred = model(val_pc_tensor)
                val_loss = criterion(val_pred, val_kp_tensor).item()
            
            scheduler.step(val_loss)
            avg_train_loss = train_loss / len(train_loader)
            
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience = 0
                torch.save(model.state_dict(), 'best_transfer_stage1.pth')
            else:
                patience += 1
                if patience >= 15:
                    print(f"早停于epoch {epoch+1}")
                    break
            
            if epoch % 10 == 0:
                print(f"Epoch {epoch+1}: Train={avg_train_loss:.6f}, Val={val_loss:.6f}")
        
        # 加载最佳模型
        model.load_state_dict(torch.load('best_transfer_stage1.pth'))
        print(f"✅ 阶段1完成，最佳验证损失: {best_val_loss:.6f}")
        
        return best_val_loss
    
    def stage2_fine_tuning(self, model, train_pc, train_kp, val_pc, val_kp):
        """阶段2: 端到端微调"""
        print("\n🎯 阶段2: 端到端微调")
        print("=" * 50)
        
        # 解冻预训练层
        model.unfreeze_pretrained_layers()
        
        # 使用更小的学习率
        optimizer = optim.Adam(model.parameters(), lr=1e-4, weight_decay=1e-4)
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=30, eta_min=1e-6)
        
        print(f"🏗️ 阶段2训练配置:")
        print(f"   可训练参数: {model.get_trainable_parameters():,}")
        print(f"   学习率: 1e-4")
        
        # 转换为张量
        train_pc_tensor = torch.FloatTensor(train_pc).to(self.device)
        train_kp_tensor = torch.FloatTensor(train_kp).to(self.device)
        val_pc_tensor = torch.FloatTensor(val_pc).to(self.device)
        val_kp_tensor = torch.FloatTensor(val_kp).to(self.device)
        
        # 创建数据加载器
        batch_size = min(6, len(train_pc) // 4) if len(train_pc) >= 12 else 3
        train_dataset = TensorDataset(train_pc_tensor, train_kp_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        
        # 训练循环
        best_val_loss = float('inf')
        patience = 0
        
        for epoch in range(30):
            # 训练
            model.train()
            train_loss = 0.0
            
            for batch_pc, batch_kp in train_loader:
                optimizer.zero_grad()
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                loss.backward()
                optimizer.step()
                train_loss += loss.item()
            
            # 验证
            model.eval()
            with torch.no_grad():
                val_pred = model(val_pc_tensor)
                val_loss = criterion(val_pred, val_kp_tensor).item()
            
            scheduler.step()
            avg_train_loss = train_loss / len(train_loader)
            
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience = 0
                torch.save(model.state_dict(), 'best_transfer_final.pth')
            else:
                patience += 1
                if patience >= 10:
                    print(f"早停于epoch {epoch+1}")
                    break
            
            if epoch % 5 == 0:
                print(f"Epoch {epoch+1}: Train={avg_train_loss:.6f}, Val={val_loss:.6f}")
        
        # 加载最佳模型
        model.load_state_dict(torch.load('best_transfer_final.pth'))
        print(f"✅ 阶段2完成，最佳验证损失: {best_val_loss:.6f}")
        
        return best_val_loss
    
    def evaluate_transfer_model(self, model, test_pc, test_kp):
        """评估迁移学习模型"""
        print("\n📊 评估迁移学习模型")
        print("=" * 50)
        
        model.eval()
        test_pc_tensor = torch.FloatTensor(test_pc).to(self.device)
        test_kp_tensor = torch.FloatTensor(test_kp).to(self.device)
        
        with torch.no_grad():
            predicted = model(test_pc_tensor)
            test_errors = torch.norm(predicted - test_kp_tensor, dim=2)
            avg_error = torch.mean(test_errors).item()
            
            # 计算准确率
            sample_errors = torch.mean(test_errors, dim=1)
            errors_5mm = torch.sum(sample_errors <= 5.0).item()
            errors_10mm = torch.sum(sample_errors <= 10.0).item()
            
            acc_5mm = (errors_5mm / len(test_pc)) * 100
            acc_10mm = (errors_10mm / len(test_pc)) * 100
        
        result = {
            'model_type': 'pointnet_plus_transfer',
            'test_samples': len(test_pc),
            'avg_error': avg_error,
            'accuracy_5mm': acc_5mm,
            'accuracy_10mm': acc_10mm,
            'medical_grade': avg_error <= 10.0,
            'excellent_grade': avg_error <= 5.0,
            'parameters': sum(p.numel() for p in model.parameters())
        }
        
        print(f"📊 迁移学习模型结果:")
        print(f"   测试样本: {result['test_samples']}")
        print(f"   平均误差: {result['avg_error']:.2f}mm")
        print(f"   5mm准确率: {result['accuracy_5mm']:.1f}%")
        print(f"   10mm准确率: {result['accuracy_10mm']:.1f}%")
        print(f"   医疗级达标: {'✅' if result['medical_grade'] else '❌'}")
        print(f"   优秀级达标: {'✅' if result['excellent_grade'] else '❌'}")
        print(f"   参数数量: {result['parameters']:,}")
        
        return result
    
    def run_transfer_learning_experiment(self):
        """运行完整的迁移学习实验"""
        print("🚀 开始PointNet++迁移学习实验")
        print("=" * 70)
        
        # 加载数据
        all_pc, all_kp = self.load_data()
        if all_pc is None:
            print("❌ 数据加载失败，退出")
            return
        
        # 数据分割
        train_pc, test_pc, train_kp, test_kp = train_test_split(
            all_pc, all_kp, test_size=0.2, random_state=42)
        
        train_pc, val_pc, train_kp, val_kp = train_test_split(
            train_pc, train_kp, test_size=0.2, random_state=42)
        
        print(f"📊 数据分割:")
        print(f"   训练: {len(train_pc)}样本")
        print(f"   验证: {len(val_pc)}样本")
        print(f"   测试: {len(test_pc)}样本")
        
        # 创建迁移学习模型
        model = PointNetPlusTransferModel(num_points=50000, num_keypoints=12).to(self.device)
        
        print(f"\n🏗️ PointNet++迁移学习模型:")
        print(f"   总参数: {sum(p.numel() for p in model.parameters()):,}")
        
        # 阶段1: 冻结预训练层训练
        stage1_loss = self.stage1_frozen_training(model, train_pc, train_kp, val_pc, val_kp)
        
        # 阶段2: 端到端微调
        stage2_loss = self.stage2_fine_tuning(model, train_pc, train_kp, val_pc, val_kp)
        
        # 最终评估
        result = self.evaluate_transfer_model(model, test_pc, test_kp)
        
        # 保存结果
        experiment_result = {
            'experiment_type': 'pointnet_plus_transfer_learning',
            'stage1_loss': stage1_loss,
            'stage2_loss': stage2_loss,
            'final_result': result,
            'improvement_analysis': {
                'baseline_performance': 6.60,  # mm (简化通用模型)
                'transfer_performance': result['avg_error'],
                'improvement': 6.60 - result['avg_error'],
                'improvement_percentage': ((6.60 - result['avg_error']) / 6.60) * 100
            },
            'timestamp': '2025-07-25'
        }
        
        with open('pointnet_transfer_results.json', 'w', encoding='utf-8') as f:
            json.dump(experiment_result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 迁移学习实验结果已保存到 pointnet_transfer_results.json")
        
        return experiment_result

def main():
    """主函数"""
    print("🎯 PointNet++迁移学习实验")
    print("PointNet++ Transfer Learning Experiment")
    print("=" * 60)
    
    # 创建训练器
    trainer = TransferLearningTrainer()
    
    # 运行迁移学习实验
    result = trainer.run_transfer_learning_experiment()
    
    if result:
        improvement = result['improvement_analysis']
        print(f"\n🎉 迁移学习实验完成:")
        print(f"✅ 基线性能: {improvement['baseline_performance']:.2f}mm")
        print(f"✅ 迁移学习性能: {improvement['transfer_performance']:.2f}mm")
        print(f"✅ 性能改进: {improvement['improvement']:.2f}mm")
        print(f"✅ 改进百分比: {improvement['improvement_percentage']:.1f}%")
        
        if improvement['improvement'] > 0:
            print(f"🎯 迁移学习成功！性能得到改善")
        else:
            print(f"⚠️  迁移学习效果有限，需要进一步优化")
    else:
        print("❌ 实验过程中出现问题")

if __name__ == "__main__":
    main()
