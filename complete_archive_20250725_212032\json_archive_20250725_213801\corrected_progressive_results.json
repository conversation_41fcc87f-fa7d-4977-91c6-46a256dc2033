{"experiment_type": "corrected_progressive_keypoint_scaling", "description": "使用原始高性能架构的修正实验", "architecture": "original_high_performance_model", "baseline_reference": "6.60mm (SimplifiedUniversalModel)", "results": [{"num_keypoints": 3, "description": "极简配置 (首中尾)", "keypoint_indices": [0, 5, 11], "architecture": "original_high_performance", "train_samples": 77, "test_samples": 20, "avg_error": 8.972569465637207, "accuracy_5mm": 20.0, "accuracy_10mm": 80.0, "medical_grade": true, "excellent_grade": false, "parameters": 1625353, "epochs_trained": 100}, {"num_keypoints": 6, "description": "基础配置 (均匀分布)", "keypoint_indices": [0, 2, 4, 7, 9, 11], "architecture": "original_high_performance", "train_samples": 77, "test_samples": 20, "avg_error": 6.983215808868408, "accuracy_5mm": 30.0, "accuracy_10mm": 95.0, "medical_grade": true, "excellent_grade": false, "parameters": 1627666, "epochs_trained": 87}, {"num_keypoints": 9, "description": "中等配置 (密集采样)", "keypoint_indices": [0, 1, 3, 4, 6, 7, 8, 10, 11], "architecture": "original_high_performance", "train_samples": 77, "test_samples": 20, "avg_error": 11.048694610595703, "accuracy_5mm": 25.0, "accuracy_10mm": 95.0, "medical_grade": false, "excellent_grade": false, "parameters": 1629979, "epochs_trained": 88}, {"num_keypoints": 12, "description": "完整配置 (全部关键点)", "keypoint_indices": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "architecture": "original_high_performance", "train_samples": 77, "test_samples": 20, "avg_error": 12.10275936126709, "accuracy_5mm": 20.0, "accuracy_10mm": 80.0, "medical_grade": false, "excellent_grade": false, "parameters": 1632292, "epochs_trained": 100}], "architecture_details": {"feature_extractor": "5层卷积 (64→128→256→512→1024)", "global_features": "1024维", "prediction_head": "3层全连接 (256→512→256→输出)", "total_parameters": "约240万 (12关键点)"}, "comparison_note": "与之前简化架构的对比，验证架构一致性的重要性", "timestamp": "2025-07-25"}