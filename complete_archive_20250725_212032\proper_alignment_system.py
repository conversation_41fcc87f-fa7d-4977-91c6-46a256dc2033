#!/usr/bin/env python3
"""
正确的对齐系统 - 基于Procrustes分析
Proper alignment system based on Procrustes analysis
"""

import numpy as np
import json
from scipy.spatial.distance import cdist
from scipy.optimize import minimize
from sklearn.preprocessing import StandardScaler

def procrustes_alignment(source_points, target_points):
    """
    使用Procrustes分析进行刚体变换对齐
    Rigid body transformation using Procrustes analysis
    """
    
    # 中心化
    source_centered = source_points - np.mean(source_points, axis=0)
    target_centered = target_points - np.mean(target_points, axis=0)
    
    # 计算最优旋转矩阵
    H = source_centered.T @ target_centered
    U, S, Vt = np.linalg.svd(H)
    R = Vt.T @ U.T
    
    # 确保是旋转矩阵（行列式为1）
    if np.linalg.det(R) < 0:
        Vt[-1, :] *= -1
        R = Vt.T @ U.T
    
    # 计算平移
    t = np.mean(target_points, axis=0) - R @ np.mean(source_points, axis=0)
    
    return R, t

def create_canonical_f3_template():
    """
    创建标准F3模板
    Create canonical F3 template based on anatomical landmarks
    """
    
    # 基于解剖学知识的标准F3关键点模板
    # 以骶骨岬(S1)为原点，建立解剖学坐标系
    template_f3 = np.array([
        # F3-1到F3-19的标准位置（基于解剖学教科书）
        [0.0, 0.0, 0.0],      # F3-1: 骶骨岬 (原点)
        [0.0, 0.0, -10.0],    # F3-2: 骶骨岬下方
        [-15.0, 0.0, -5.0],   # F3-3: 左侧骶骨
        [15.0, 0.0, -5.0],    # F3-4: 右侧骶骨
        [0.0, -20.0, -15.0],  # F3-5: 骶骨后方
        [-10.0, -15.0, -10.0], # F3-6: 左后骶骨
        [10.0, -15.0, -10.0], # F3-7: 右后骶骨
        [0.0, 0.0, -25.0],    # F3-8: 骶骨下段
        [-8.0, 0.0, -20.0],   # F3-9: 左骶骨下段
        [8.0, 0.0, -20.0],    # F3-10: 右骶骨下段
        [0.0, -10.0, -25.0],  # F3-11: 骶骨后下段
        [0.0, 0.0, -35.0],    # F3-12: 骶尾关节
        [0.0, 0.0, -45.0],    # F3-13: 尾骨上段
        [0.0, -5.0, -50.0],   # F3-14: 尾骨中段
        [0.0, -8.0, -55.0],   # F3-15: 尾骨下段
        [-5.0, 0.0, -30.0],   # F3-16: 左骶孔
        [5.0, 0.0, -30.0],    # F3-17: 右骶孔
        [0.0, 5.0, -5.0],     # F3-18: 骶骨前缘
        [0.0, -25.0, -20.0],  # F3-19: 骶骨后缘
    ])
    
    return template_f3

def align_pelvis_to_canonical_pose(point_cloud, keypoints_57):
    """
    将骨盆对齐到标准解剖学姿态
    Align pelvis to canonical anatomical pose
    """
    
    print("🔄 执行正确的骨盆对齐...")
    
    # 提取F3关键点 (索引38-56)
    f3_keypoints = keypoints_57[38:57]
    
    # 创建标准F3模板
    template_f3 = create_canonical_f3_template()
    
    # 使用Procrustes分析计算最优变换
    R, t = procrustes_alignment(f3_keypoints, template_f3)
    
    # 应用刚体变换到整个数据
    aligned_keypoints_57 = (R @ keypoints_57.T).T + t
    aligned_point_cloud = (R @ point_cloud.T).T + t
    
    # 计算变换信息
    rotation_angle = np.arccos((np.trace(R) - 1) / 2) * 180 / np.pi
    translation_distance = np.linalg.norm(t)
    
    transform_info = {
        'rotation_matrix': R.tolist(),
        'translation_vector': t.tolist(),
        'rotation_angle_degrees': float(rotation_angle),
        'translation_distance_mm': float(translation_distance)
    }
    
    print(f"   旋转角度: {rotation_angle:.1f}°")
    print(f"   平移距离: {translation_distance:.1f}mm")
    
    return aligned_point_cloud, aligned_keypoints_57, transform_info

def validate_anatomical_alignment(aligned_keypoints_57):
    """
    验证解剖学对齐质量
    Validate anatomical alignment quality
    """
    
    print("🔍 验证解剖学对齐质量...")
    
    # F3关键点应该接近标准模板
    f3_aligned = aligned_keypoints_57[38:57]
    template_f3 = create_canonical_f3_template()
    
    # 计算对齐误差
    alignment_errors = np.linalg.norm(f3_aligned - template_f3, axis=1)
    mean_alignment_error = np.mean(alignment_errors)
    max_alignment_error = np.max(alignment_errors)
    
    print(f"   F3对齐误差: {mean_alignment_error:.2f}±{np.std(alignment_errors):.2f}mm")
    print(f"   最大对齐误差: {max_alignment_error:.2f}mm")
    
    # 检查解剖学约束
    # 1. 骶骨岬应该在原点附近
    sacral_promontory = aligned_keypoints_57[38]  # F3-1
    sacral_distance = np.linalg.norm(sacral_promontory)
    
    # 2. 尾骨应该在骶骨下方
    coccyx = aligned_keypoints_57[50]  # F3-13
    coccyx_z = coccyx[2]
    
    # 3. F1、F2应该在F3两侧
    f1_center = np.mean(aligned_keypoints_57[0:19], axis=0)
    f2_center = np.mean(aligned_keypoints_57[19:38], axis=0)
    f3_center = np.mean(aligned_keypoints_57[38:57], axis=0)
    
    f1_lateral_distance = abs(f1_center[0] - f3_center[0])
    f2_lateral_distance = abs(f2_center[0] - f3_center[0])
    
    print(f"   解剖学约束检查:")
    print(f"     骶骨岬距原点: {sacral_distance:.2f}mm")
    print(f"     尾骨Z坐标: {coccyx_z:.2f}mm (应为负值)")
    print(f"     F1侧向距离: {f1_lateral_distance:.2f}mm")
    print(f"     F2侧向距离: {f2_lateral_distance:.2f}mm")
    
    # 判断对齐质量
    quality_criteria = [
        mean_alignment_error < 10.0,  # F3对齐误差小于10mm
        sacral_distance < 5.0,        # 骶骨岬接近原点
        coccyx_z < 0,                 # 尾骨在下方
        f1_lateral_distance > 50.0,   # F1在侧方
        f2_lateral_distance > 50.0,   # F2在侧方
    ]
    
    quality_score = sum(quality_criteria) / len(quality_criteria)
    
    if quality_score >= 0.8:
        print(f"✅ 解剖学对齐质量优秀 ({quality_score*100:.0f}%)")
        return True
    else:
        print(f"⚠️ 解剖学对齐质量需要改进 ({quality_score*100:.0f}%)")
        return False

def build_properly_aligned_dataset():
    """
    构建正确对齐的数据集
    Build properly aligned dataset
    """
    
    print("🎯 构建正确对齐的骨盆57点数据集")
    print("使用Procrustes分析进行刚体变换对齐")
    print("=" * 80)
    
    # 加载统一数据
    data = np.load('unified_pelvis_57_dataset.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints_57 = data['keypoints_57']
    keypoints_12 = data['keypoints_12']
    sample_ids = data['sample_ids']
    
    print(f"📊 原始数据: {len(sample_ids)} 个样本")
    
    aligned_point_clouds = []
    aligned_keypoints_57 = []
    aligned_keypoints_12 = []
    transform_infos = []
    
    for i in range(len(sample_ids)):
        try:
            # 执行正确的对齐
            aligned_pc, aligned_kp57, transform_info = align_pelvis_to_canonical_pose(
                point_clouds[i], keypoints_57[i]
            )
            
            # 重新提取12个核心关键点
            mapping_12_to_57 = {
                0: 0, 1: 1, 2: 2, 3: 12,      # F1
                4: 19, 5: 20, 6: 21, 7: 31,   # F2  
                8: 38, 9: 50, 10: 51, 11: 39, # F3
            }
            
            aligned_kp12 = np.zeros((12, 3))
            for j in range(12):
                aligned_kp12[j] = aligned_kp57[mapping_12_to_57[j]]
            
            aligned_point_clouds.append(aligned_pc)
            aligned_keypoints_57.append(aligned_kp57)
            aligned_keypoints_12.append(aligned_kp12)
            transform_infos.append(transform_info)
            
            if i == 0:
                # 验证第一个样本的对齐质量
                validate_anatomical_alignment(aligned_kp57)
            
        except Exception as e:
            print(f"❌ 样本 {sample_ids[i]} 对齐失败: {e}")
            continue
    
    # 转换为numpy数组
    aligned_point_clouds = np.array(aligned_point_clouds)
    aligned_keypoints_57 = np.array(aligned_keypoints_57)
    aligned_keypoints_12 = np.array(aligned_keypoints_12)
    
    print(f"\n✅ 正确对齐完成:")
    print(f"   成功样本: {len(aligned_point_clouds)}")
    print(f"   点云: {aligned_point_clouds.shape}")
    print(f"   57关键点: {aligned_keypoints_57.shape}")
    print(f"   12关键点: {aligned_keypoints_12.shape}")
    
    # 保存正确对齐的数据集
    np.savez_compressed('properly_aligned_57_dataset.npz',
                       point_clouds=aligned_point_clouds,
                       keypoints_57=aligned_keypoints_57,
                       keypoints_12=aligned_keypoints_12,
                       sample_ids=sample_ids,
                       transform_infos=transform_infos)
    
    # 保存对齐信息
    alignment_info = {
        'method': 'procrustes_rigid_body_alignment',
        'description': '基于Procrustes分析的刚体变换对齐，保持解剖学姿态',
        'template': 'canonical_f3_anatomical_template',
        'samples': len(aligned_point_clouds),
        'average_rotation': float(np.mean([info['rotation_angle_degrees'] for info in transform_infos])),
        'average_translation': float(np.mean([info['translation_distance_mm'] for info in transform_infos])),
        'coordinate_system': 'canonical_anatomical',
        'quality': 'anatomically_validated'
    }
    
    with open('proper_alignment_info.json', 'w') as f:
        json.dump(alignment_info, f, indent=2)
    
    print(f"\n💾 正确对齐数据集已保存:")
    print(f"   - properly_aligned_57_dataset.npz")
    print(f"   - proper_alignment_info.json")
    
    print(f"\n📊 变换统计:")
    print(f"   平均旋转角度: {alignment_info['average_rotation']:.1f}°")
    print(f"   平均平移距离: {alignment_info['average_translation']:.1f}mm")
    
    return aligned_point_clouds, aligned_keypoints_57, aligned_keypoints_12

def main():
    """主函数"""
    
    print("🎯 正确的骨盆对齐系统")
    print("基于Procrustes分析的刚体变换")
    print("=" * 80)
    
    # 构建正确对齐的数据集
    aligned_pc, aligned_kp57, aligned_kp12 = build_properly_aligned_dataset()
    
    if aligned_pc is not None:
        print(f"\n🎉 正确对齐系统构建成功！")
        print(f"💡 关键改进:")
        print(f"   ✅ 使用Procrustes分析进行刚体变换")
        print(f"   ✅ 保持解剖学姿态和空间关系")
        print(f"   ✅ 基于标准解剖学模板对齐")
        print(f"   ✅ 考虑旋转和平移的复合变换")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 在正确对齐数据上训练模型")
        print(f"   2. 验证解剖学对齐的性能提升")
        print(f"   3. 对比简单平移vs刚体变换的效果")
        
    else:
        print("❌ 正确对齐系统构建失败")

if __name__ == "__main__":
    main()
