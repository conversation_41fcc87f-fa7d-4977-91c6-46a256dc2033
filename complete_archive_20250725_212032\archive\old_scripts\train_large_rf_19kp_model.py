#!/usr/bin/env python3
"""
训练大感受野的19关键点模型
专门解决F3-13等全局特征点的问题
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split

class LargeReceptiveFieldPointNet(nn.Module):
    """大感受野PointNet - 专门解决F3-13问题"""
    
    def __init__(self, input_dim=3, num_keypoints=19):
        super().__init__()
        
        # 第一阶段：局部特征提取 (小感受野)
        self.local_conv1 = nn.Conv1d(input_dim, 64, kernel_size=1)
        self.local_conv2 = nn.Conv1d(64, 128, kernel_size=1)
        
        # 第二阶段：中等感受野 (kernel=3)
        self.medium_conv1 = nn.Conv1d(128, 256, kernel_size=3, padding=1)
        self.medium_conv2 = nn.Conv1d(256, 512, kernel_size=3, padding=1)
        
        # 第三阶段：大感受野 (膨胀卷积)
        self.large_conv1 = nn.Conv1d(512, 512, kernel_size=3, padding=2, dilation=2)  # 感受野x2
        self.large_conv2 = nn.Conv1d(512, 512, kernel_size=3, padding=4, dilation=4)  # 感受野x4
        
        # 全局特征 (最大感受野)
        self.global_conv = nn.Conv1d(512, 1024, kernel_size=1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(512)
        self.bn6 = nn.BatchNorm1d(512)
        self.bn7 = nn.BatchNorm1d(1024)
        
        # 多尺度特征融合
        self.fusion_conv = nn.Conv1d(64 + 128 + 256 + 512 + 512 + 512 + 1024, 512, 1)
        self.fusion_bn = nn.BatchNorm1d(512)
        
        # 专门为极值点设计的分支
        self.extreme_point_branch = nn.Sequential(
            nn.Conv1d(512, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Conv1d(256, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 64, 1)
        )
        
        # 热力图回归头
        self.heatmap_head = nn.Sequential(
            nn.Conv1d(512 + 64, 256, 1),  # 融合主分支和极值分支
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Conv1d(256, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.2),
            
            nn.Conv1d(128, num_keypoints, 1)
        )
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        # x: [B, 3, N]
        
        # 多尺度特征提取
        # 局部特征 (感受野: 1点)
        x1 = torch.relu(self.bn1(self.local_conv1(x)))      # [B, 64, N]
        x2 = torch.relu(self.bn2(self.local_conv2(x1)))     # [B, 128, N]
        
        # 中等感受野 (感受野: 3点)
        x3 = torch.relu(self.bn3(self.medium_conv1(x2)))    # [B, 256, N]
        x4 = torch.relu(self.bn4(self.medium_conv2(x3)))    # [B, 512, N]
        
        # 大感受野 (膨胀卷积)
        x5 = torch.relu(self.bn5(self.large_conv1(x4)))     # [B, 512, N] - dilation=2
        x6 = torch.relu(self.bn6(self.large_conv2(x5)))     # [B, 512, N] - dilation=4
        
        # 全局特征
        x7 = self.bn7(self.global_conv(x6))                 # [B, 1024, N]
        
        # 多尺度特征融合
        multi_scale = torch.cat([x1, x2, x3, x4, x5, x6, x7], dim=1)  # [B, 3136, N]
        fused = torch.relu(self.fusion_bn(self.fusion_conv(multi_scale)))  # [B, 512, N]
        
        # 极值点专用分支
        extreme_features = self.extreme_point_branch(fused)  # [B, 64, N]
        
        # 融合主分支和极值分支
        final_features = torch.cat([fused, extreme_features], dim=1)  # [B, 576, N]
        
        # 应用dropout
        final_features = self.dropout(final_features)
        
        # 生成热力图
        heatmaps = self.heatmap_head(final_features)  # [B, 19, N]
        
        return heatmaps

class LargeRFDataset(Dataset):
    """大感受野训练数据集"""
    
    def __init__(self, point_clouds, keypoints, sample_ids, num_points=8192, 
                 sigma=6.0, augment=False):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.sample_ids = sample_ids
        self.num_points = num_points
        self.sigma = sigma
        self.augment = augment
        
        print(f"📊 大感受野数据集:")
        print(f"   样本数: {len(self.point_clouds)}")
        print(f"   关键点数: {len(self.keypoints[0]) if len(self.keypoints) > 0 else 0}")
        print(f"   数据增强: {self.augment}")
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        pc = self.point_clouds[idx].copy()
        kp = self.keypoints[idx].copy()
        sample_id = self.sample_ids[idx]
        
        # 数据增强
        if self.augment:
            pc, kp = self.apply_augmentation(pc, kp)
        
        # 采样点云
        if len(pc) > self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=False)
            pc = pc[indices]
        elif len(pc) < self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=True)
            pc = pc[indices]
        
        # 生成热力图目标 - 为极值点使用更尖锐的高斯
        heatmaps = self.create_adaptive_heatmap_targets(kp, pc)
        
        return {
            'point_cloud': torch.FloatTensor(pc).transpose(0, 1),  # [3, N]
            'heatmaps': torch.FloatTensor(heatmaps),  # [19, N]
            'keypoints': torch.FloatTensor(kp),  # [19, 3]
            'sample_id': sample_id
        }
    
    def create_adaptive_heatmap_targets(self, keypoints, point_cloud):
        """创建自适应热力图目标 - 为不同类型的关键点使用不同的sigma"""
        num_points = len(point_cloud)
        num_keypoints = len(keypoints)
        heatmaps = np.zeros((num_keypoints, num_points))
        
        # 为不同关键点定义不同的sigma
        adaptive_sigmas = {
            12: self.sigma * 0.7,  # F3-13 (Z最高点) - 更尖锐
            17: self.sigma * 0.7,  # F3-18 (Z最低点) - 更尖锐
            3: self.sigma * 1.2,   # F3-4 (问题点) - 更宽松
            13: self.sigma * 0.9,  # F3-14 (边界点) - 稍微尖锐
            18: self.sigma * 0.9,  # F3-19 (边界点) - 稍微尖锐
        }
        
        for kp_idx, keypoint in enumerate(keypoints):
            # 选择合适的sigma
            sigma = adaptive_sigmas.get(kp_idx, self.sigma)
            
            # 计算每个点到关键点的距离
            distances = np.linalg.norm(point_cloud - keypoint, axis=1)
            # 生成高斯分布
            heatmaps[kp_idx] = np.exp(-distances**2 / (2 * sigma**2))
        
        return heatmaps
    
    def apply_augmentation(self, pc, kp):
        """保守的数据增强"""
        # 小幅旋转
        if np.random.random() < 0.3:
            angle = np.random.uniform(-5, 5) * np.pi / 180
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            pc = pc @ rotation_matrix.T
            kp = kp @ rotation_matrix.T
        
        # 轻微缩放
        if np.random.random() < 0.2:
            scale = np.random.uniform(0.98, 1.02)
            pc = pc * scale
            kp = kp * scale
        
        # 小幅平移
        if np.random.random() < 0.2:
            translation = np.random.uniform(-1, 1, 3)
            pc = pc + translation
            kp = kp + translation
        
        return pc, kp

def train_large_rf_model(train_loader, val_loader, device, num_epochs=50):
    """训练大感受野模型"""
    
    # 创建大感受野模型
    model = LargeReceptiveFieldPointNet(input_dim=3, num_keypoints=19).to(device)
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=0.0005, weight_decay=0.01)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=8, factor=0.5)
    
    # 加权损失 - 给问题关键点更高权重
    criterion = nn.MSELoss(reduction='none')
    
    # 关键点权重 (重点关注F3-13和F3-4)
    keypoint_weights = torch.ones(19).to(device)
    keypoint_weights[12] = 3.0  # F3-13 (Z最高点) - 3倍权重
    keypoint_weights[3] = 2.0   # F3-4 (问题点) - 2倍权重
    keypoint_weights[13] = 1.5  # F3-14 (边界点) - 1.5倍权重
    keypoint_weights[18] = 1.5  # F3-19 (边界点) - 1.5倍权重
    
    # 训练历史
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    
    print(f"🚀 开始训练大感受野19关键点模型 ({num_epochs} epochs)")
    print(f"📊 模型参数: {sum(p.numel() for p in model.parameters())}")
    print(f"🎯 重点优化: F3-13 (3x权重), F3-4 (2x权重)")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0
        
        for batch_idx, batch in enumerate(train_loader):
            point_clouds = batch['point_cloud'].to(device)  # [B, 3, N]
            heatmap_targets = batch['heatmaps'].to(device)  # [B, 19, N]
            
            optimizer.zero_grad()
            
            # 前向传播
            pred_heatmaps = model(point_clouds)
            
            # 计算加权损失
            loss_per_kp = criterion(pred_heatmaps, heatmap_targets)  # [B, 19, N]
            loss_per_kp = torch.mean(loss_per_kp, dim=(0, 2))  # [19] - 每个关键点的平均损失
            
            # 应用权重
            weighted_loss = torch.sum(loss_per_kp * keypoint_weights) / torch.sum(keypoint_weights)
            
            # 反向传播
            weighted_loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += weighted_loss.item()
        
        # 验证阶段
        model.eval()
        val_loss = 0
        
        with torch.no_grad():
            for batch in val_loader:
                point_clouds = batch['point_cloud'].to(device)
                heatmap_targets = batch['heatmaps'].to(device)
                
                pred_heatmaps = model(point_clouds)
                
                # 计算加权验证损失
                loss_per_kp = criterion(pred_heatmaps, heatmap_targets)
                loss_per_kp = torch.mean(loss_per_kp, dim=(0, 2))
                weighted_loss = torch.sum(loss_per_kp * keypoint_weights) / torch.sum(keypoint_weights)
                
                val_loss += weighted_loss.item()
        
        # 计算平均损失
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            torch.save(model.state_dict(), 'best_large_rf_19kp_model.pth')
            print(f"✅ 保存最佳模型 (epoch {epoch+1})")
        
        # 打印进度
        if (epoch + 1) % 5 == 0:
            print(f"Epoch {epoch+1}/{num_epochs}:")
            print(f"  Train Loss: {avg_train_loss:.4f}")
            print(f"  Val Loss: {avg_val_loss:.4f}")
            print(f"  LR: {optimizer.param_groups[0]['lr']:.6f}")
    
    return model, train_losses, val_losses

def main():
    """主函数"""
    print("🔧 训练大感受野19关键点模型")
    print("专门解决F3-13 (Z最高点) 等全局特征问题")
    print("=" * 60)
    
    # 加载预处理后的数据
    data = np.load('f3_19kp_preprocessed.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    sample_ids = data['sample_ids']
    
    print(f"✅ 数据加载完成:")
    print(f"   样本数: {len(point_clouds)}")
    print(f"   关键点形状: {keypoints.shape}")
    print(f"   平均点云大小: {np.mean([len(pc) for pc in point_clouds]):.0f}")
    
    # 数据分割
    train_indices, val_indices = train_test_split(
        range(len(point_clouds)), test_size=0.2, random_state=42
    )
    
    # 创建数据集
    train_dataset = LargeRFDataset(
        point_clouds[train_indices], keypoints[train_indices], 
        sample_ids[train_indices], augment=True
    )
    
    val_dataset = LargeRFDataset(
        point_clouds[val_indices], keypoints[val_indices], 
        sample_ids[val_indices], augment=False
    )
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False, num_workers=2)
    
    print(f"\n📊 数据分割:")
    print(f"   训练集: {len(train_dataset)} 样本")
    print(f"   验证集: {len(val_dataset)} 样本")
    
    # 训练模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    model, train_losses, val_losses = train_large_rf_model(
        train_loader, val_loader, device, num_epochs=50
    )
    
    # 绘制训练曲线
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='Train Loss', alpha=0.8)
    plt.plot(val_losses, label='Validation Loss', alpha=0.8)
    plt.xlabel('Epoch')
    plt.ylabel('Weighted Loss')
    plt.title('Large Receptive Field 19-Point Training Progress')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('large_rf_19kp_training_progress.png', dpi=300, bbox_inches='tight')
    print("📊 训练进度保存: large_rf_19kp_training_progress.png")
    plt.close()
    
    print(f"\n🎉 大感受野19关键点模型训练完成!")
    print("✅ 最佳模型保存为: best_large_rf_19kp_model.pth")
    print("💡 关键改进:")
    print("   1. 膨胀卷积增加感受野 (1→3→6→12点)")
    print("   2. 多尺度特征融合")
    print("   3. 极值点专用分支")
    print("   4. 加权损失 (F3-13: 3x, F3-4: 2x)")
    print("   5. 自适应热力图目标")
    
    print(f"\n🎯 预期改进:")
    print("   • F3-13 (Z最高点): 26.38mm → <10mm")
    print("   • F3-4 (问题点): 15.30mm → <8mm")
    print("   • 整体性能: 7.51mm → <6mm")

if __name__ == "__main__":
    main()
