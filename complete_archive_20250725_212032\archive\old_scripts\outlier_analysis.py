#!/usr/bin/env python3
"""
异常样本详细分析
深入分析13个异常样本的具体问题和原因
"""

import numpy as np
import json
import matplotlib.pyplot as plt
from sklearn.ensemble import IsolationForest
from sklearn.neighbors import LocalOutlierFactor
from sklearn.decomposition import PCA
import seaborn as sns

def load_and_analyze_outliers():
    """加载数据并分析异常样本"""
    
    print("🔍 **异常样本详细分析**")
    print("深入分析13个异常样本的具体问题")
    print("=" * 80)
    
    # 加载数据集
    data = np.load('f3_reduced_12kp_stable.npz', allow_pickle=True)
    sample_ids = data['sample_ids']
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    
    # 加载质量评估报告
    with open('quality_assessment_20250718_143403/quality_assessment_report.json', 'r') as f:
        report = json.load(f)
    
    outlier_info = report['detailed_results']['outlier_detection']['outlier_samples']
    
    print(f"📊 **异常样本概览**:")
    print(f"   总样本数: {len(sample_ids)}")
    print(f"   异常样本数: {len(outlier_info)}")
    print(f"   异常比例: {len(outlier_info)/len(sample_ids)*100:.1f}%")
    
    # 异常样本ID列表
    outlier_ids = [info['sample_id'] for info in outlier_info]
    print(f"   异常样本ID: {outlier_ids}")
    
    return sample_ids, point_clouds, keypoints, outlier_info

def analyze_outlier_characteristics():
    """分析异常样本的特征"""
    
    sample_ids, point_clouds, keypoints, outlier_info = load_and_analyze_outliers()
    
    print(f"\n🔍 **异常样本特征分析**:")
    
    # 创建样本ID到索引的映射
    id_to_idx = {sid: i for i, sid in enumerate(sample_ids)}
    
    # 分析每个异常样本
    outlier_details = []
    
    for outlier in outlier_info:
        sample_id = outlier['sample_id']
        idx = id_to_idx[sample_id]
        
        pc = point_clouds[idx]
        kp = keypoints[idx]
        
        # 计算该样本的特征
        features = {
            'sample_id': sample_id,
            'index': idx,
            'isolation_forest': outlier['isolation_forest'],
            'local_outlier_factor': outlier['local_outlier_factor'],
            
            # 点云特征
            'pc_num_points': len(pc),
            'pc_mean': np.mean(pc, axis=0),
            'pc_std': np.std(pc, axis=0),
            'pc_range': np.max(pc, axis=0) - np.min(pc, axis=0),
            'pc_center': np.mean(pc, axis=0),
            
            # 关键点特征
            'kp_mean': np.mean(kp, axis=0),
            'kp_std': np.std(kp, axis=0),
            'kp_range': np.max(kp, axis=0) - np.min(kp, axis=0),
            'kp_center': np.mean(kp, axis=0),
            
            # 关键点间距离
            'kp_distances': [],
            'kp_distance_stats': {}
        }
        
        # 计算关键点间距离
        distances = []
        for i in range(12):
            for j in range(i+1, 12):
                dist = np.linalg.norm(kp[i] - kp[j])
                distances.append(dist)
        
        features['kp_distances'] = distances
        features['kp_distance_stats'] = {
            'mean': np.mean(distances),
            'std': np.std(distances),
            'min': np.min(distances),
            'max': np.max(distances)
        }
        
        # 点云-关键点关系
        from sklearn.neighbors import NearestNeighbors
        nbrs = NearestNeighbors(n_neighbors=1).fit(pc)
        distances_to_surface, _ = nbrs.kneighbors(kp)
        
        features['surface_distances'] = {
            'mean': np.mean(distances_to_surface),
            'std': np.std(distances_to_surface),
            'max': np.max(distances_to_surface),
            'distances': distances_to_surface.flatten()
        }
        
        outlier_details.append(features)
    
    return outlier_details

def compare_with_normal_samples():
    """与正常样本对比分析"""
    
    print(f"\n📊 **异常样本 vs 正常样本对比**:")
    
    # 加载数据
    data = np.load('f3_reduced_12kp_stable.npz', allow_pickle=True)
    sample_ids = data['sample_ids']
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    
    # 获取异常样本详情
    outlier_details = analyze_outlier_characteristics()
    outlier_ids = [detail['sample_id'] for detail in outlier_details]
    
    # 分析所有样本的特征
    all_features = []
    
    for i, (sid, pc, kp) in enumerate(zip(sample_ids, point_clouds, keypoints)):
        # 计算关键点间距离
        distances = []
        for j in range(12):
            for k in range(j+1, 12):
                dist = np.linalg.norm(kp[j] - kp[k])
                distances.append(dist)
        
        # 表面距离
        from sklearn.neighbors import NearestNeighbors
        nbrs = NearestNeighbors(n_neighbors=1).fit(pc)
        surface_dists, _ = nbrs.kneighbors(kp)
        
        features = {
            'sample_id': sid,
            'is_outlier': sid in outlier_ids,
            'pc_num_points': len(pc),
            'kp_distance_mean': np.mean(distances),
            'kp_distance_std': np.std(distances),
            'surface_distance_mean': np.mean(surface_dists),
            'surface_distance_max': np.max(surface_dists),
            'kp_range_x': np.max(kp[:, 0]) - np.min(kp[:, 0]),
            'kp_range_y': np.max(kp[:, 1]) - np.min(kp[:, 1]),
            'kp_range_z': np.max(kp[:, 2]) - np.min(kp[:, 2]),
            'kp_center': np.mean(kp, axis=0)
        }
        
        all_features.append(features)
    
    # 分离异常和正常样本
    outlier_features = [f for f in all_features if f['is_outlier']]
    normal_features = [f for f in all_features if not f['is_outlier']]
    
    print(f"   正常样本: {len(normal_features)}个")
    print(f"   异常样本: {len(outlier_features)}个")
    
    # 对比关键指标
    metrics = [
        'pc_num_points', 'kp_distance_mean', 'kp_distance_std',
        'surface_distance_mean', 'surface_distance_max',
        'kp_range_x', 'kp_range_y', 'kp_range_z'
    ]
    
    print(f"\n📈 **关键指标对比**:")
    print(f"{'指标':<20} {'正常样本均值':<15} {'异常样本均值':<15} {'差异':<10}")
    print("-" * 70)
    
    for metric in metrics:
        normal_values = [f[metric] for f in normal_features]
        outlier_values = [f[metric] for f in outlier_features]
        
        normal_mean = np.mean(normal_values)
        outlier_mean = np.mean(outlier_values)
        difference = (outlier_mean - normal_mean) / normal_mean * 100
        
        print(f"{metric:<20} {normal_mean:<15.3f} {outlier_mean:<15.3f} {difference:+.1f}%")
    
    return all_features, outlier_features, normal_features

def identify_outlier_patterns():
    """识别异常模式"""
    
    print(f"\n🔍 **异常模式识别**:")
    
    all_features, outlier_features, normal_features = compare_with_normal_samples()
    
    # 分析异常原因
    outlier_reasons = []
    
    for outlier in outlier_features:
        reasons = []
        
        # 检查点云大小异常
        normal_pc_sizes = [f['pc_num_points'] for f in normal_features]
        pc_mean = np.mean(normal_pc_sizes)
        pc_std = np.std(normal_pc_sizes)
        
        if abs(outlier['pc_num_points'] - pc_mean) > 2 * pc_std:
            reasons.append(f"点云大小异常: {outlier['pc_num_points']} (正常: {pc_mean:.0f}±{pc_std:.0f})")
        
        # 检查关键点距离异常
        normal_kp_dists = [f['kp_distance_mean'] for f in normal_features]
        kp_mean = np.mean(normal_kp_dists)
        kp_std = np.std(normal_kp_dists)
        
        if abs(outlier['kp_distance_mean'] - kp_mean) > 2 * kp_std:
            reasons.append(f"关键点距离异常: {outlier['kp_distance_mean']:.1f} (正常: {kp_mean:.1f}±{kp_std:.1f})")
        
        # 检查表面距离异常
        normal_surf_dists = [f['surface_distance_mean'] for f in normal_features]
        surf_mean = np.mean(normal_surf_dists)
        surf_std = np.std(normal_surf_dists)
        
        if abs(outlier['surface_distance_mean'] - surf_mean) > 2 * surf_std:
            reasons.append(f"表面距离异常: {outlier['surface_distance_mean']:.3f} (正常: {surf_mean:.3f}±{surf_std:.3f})")
        
        # 检查空间范围异常
        for axis, axis_name in enumerate(['x', 'y', 'z']):
            range_key = f'kp_range_{axis_name}'
            normal_ranges = [f[range_key] for f in normal_features]
            range_mean = np.mean(normal_ranges)
            range_std = np.std(normal_ranges)
            
            if abs(outlier[range_key] - range_mean) > 2 * range_std:
                reasons.append(f"{axis_name}轴范围异常: {outlier[range_key]:.1f} (正常: {range_mean:.1f}±{range_std:.1f})")
        
        if not reasons:
            reasons.append("轻微异常，可能是正常变异")
        
        outlier_reasons.append({
            'sample_id': outlier['sample_id'],
            'reasons': reasons,
            'severity': len([r for r in reasons if '异常' in r])
        })
    
    # 按严重程度分类
    severe_outliers = [o for o in outlier_reasons if o['severity'] >= 3]
    moderate_outliers = [o for o in outlier_reasons if o['severity'] == 2]
    mild_outliers = [o for o in outlier_reasons if o['severity'] <= 1]
    
    print(f"🚨 **严重异常 ({len(severe_outliers)}个)**: 建议移除")
    for outlier in severe_outliers:
        print(f"   {outlier['sample_id']}: {'; '.join(outlier['reasons'])}")
    
    print(f"\n⚠️ **中度异常 ({len(moderate_outliers)}个)**: 需要人工审核")
    for outlier in moderate_outliers:
        print(f"   {outlier['sample_id']}: {'; '.join(outlier['reasons'])}")
    
    print(f"\n💡 **轻微异常 ({len(mild_outliers)}个)**: 可能保留")
    for outlier in mild_outliers:
        print(f"   {outlier['sample_id']}: {'; '.join(outlier['reasons'])}")
    
    return outlier_reasons, severe_outliers, moderate_outliers, mild_outliers

def create_outlier_visualization():
    """创建异常样本可视化"""
    
    print(f"\n📊 **创建异常样本可视化**:")
    
    all_features, outlier_features, normal_features = compare_with_normal_samples()
    
    # 准备数据
    normal_data = {
        'kp_distance_mean': [f['kp_distance_mean'] for f in normal_features],
        'surface_distance_mean': [f['surface_distance_mean'] for f in normal_features],
        'kp_range_x': [f['kp_range_x'] for f in normal_features],
        'kp_range_y': [f['kp_range_y'] for f in normal_features]
    }
    
    outlier_data = {
        'kp_distance_mean': [f['kp_distance_mean'] for f in outlier_features],
        'surface_distance_mean': [f['surface_distance_mean'] for f in outlier_features],
        'kp_range_x': [f['kp_range_x'] for f in outlier_features],
        'kp_range_y': [f['kp_range_y'] for f in outlier_features]
    }
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 关键点距离分布
    axes[0, 0].hist(normal_data['kp_distance_mean'], bins=15, alpha=0.7, 
                   label='Normal', color='skyblue', density=True)
    axes[0, 0].hist(outlier_data['kp_distance_mean'], bins=5, alpha=0.7, 
                   label='Outlier', color='red', density=True)
    axes[0, 0].set_xlabel('Mean Keypoint Distance')
    axes[0, 0].set_ylabel('Density')
    axes[0, 0].set_title('Keypoint Distance Distribution')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 表面距离分布
    axes[0, 1].hist(normal_data['surface_distance_mean'], bins=15, alpha=0.7, 
                   label='Normal', color='lightgreen', density=True)
    axes[0, 1].hist(outlier_data['surface_distance_mean'], bins=5, alpha=0.7, 
                   label='Outlier', color='red', density=True)
    axes[0, 1].set_xlabel('Mean Surface Distance')
    axes[0, 1].set_ylabel('Density')
    axes[0, 1].set_title('Surface Distance Distribution')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # X轴范围分布
    axes[1, 0].hist(normal_data['kp_range_x'], bins=15, alpha=0.7, 
                   label='Normal', color='gold', density=True)
    axes[1, 0].hist(outlier_data['kp_range_x'], bins=5, alpha=0.7, 
                   label='Outlier', color='red', density=True)
    axes[1, 0].set_xlabel('X-axis Range')
    axes[1, 0].set_ylabel('Density')
    axes[1, 0].set_title('X-axis Range Distribution')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 散点图: 关键点距离 vs 表面距离
    axes[1, 1].scatter(normal_data['kp_distance_mean'], normal_data['surface_distance_mean'], 
                      alpha=0.6, label='Normal', color='blue', s=30)
    axes[1, 1].scatter(outlier_data['kp_distance_mean'], outlier_data['surface_distance_mean'], 
                      alpha=0.8, label='Outlier', color='red', s=50, marker='x')
    axes[1, 1].set_xlabel('Mean Keypoint Distance')
    axes[1, 1].set_ylabel('Mean Surface Distance')
    axes[1, 1].set_title('Keypoint vs Surface Distance')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('outlier_analysis.png', dpi=300, bbox_inches='tight')
    print(f"   📊 可视化图表已保存: outlier_analysis.png")
    plt.close()

def generate_recommendations():
    """生成处理建议"""
    
    print(f"\n💡 **异常样本处理建议**:")
    
    outlier_reasons, severe_outliers, moderate_outliers, mild_outliers = identify_outlier_patterns()
    
    recommendations = {
        "立即移除 (严重异常)": {
            "样本": [o['sample_id'] for o in severe_outliers],
            "原因": "多项指标严重偏离正常范围",
            "影响": "可能严重误导模型训练",
            "行动": "直接从训练集中移除"
        },
        
        "人工审核 (中度异常)": {
            "样本": [o['sample_id'] for o in moderate_outliers],
            "原因": "部分指标异常，需要专家判断",
            "影响": "可能影响模型性能",
            "行动": "医学专家审核，决定保留或修复"
        },
        
        "标记保留 (轻微异常)": {
            "样本": [o['sample_id'] for o in mild_outliers],
            "原因": "轻微偏离，可能是正常生物变异",
            "影响": "影响较小",
            "行动": "保留但标记，监控训练效果"
        }
    }
    
    for category, details in recommendations.items():
        print(f"\n🎯 **{category}**:")
        print(f"   样本数量: {len(details['样本'])}")
        print(f"   样本ID: {details['样本']}")
        print(f"   原因: {details['原因']}")
        print(f"   影响: {details['影响']}")
        print(f"   建议行动: {details['行动']}")
    
    # 预期效果
    total_outliers = len(severe_outliers) + len(moderate_outliers) + len(mild_outliers)
    remove_count = len(severe_outliers)
    review_count = len(moderate_outliers)
    keep_count = len(mild_outliers)
    
    print(f"\n📊 **预期处理效果**:")
    print(f"   原始样本: 97个")
    print(f"   移除样本: {remove_count}个")
    print(f"   审核样本: {review_count}个 (假设50%保留)")
    print(f"   保留样本: {keep_count}个")
    print(f"   预期剩余: {97 - remove_count - review_count//2}个高质量样本")
    print(f"   质量提升: 异常率从13.4%降到约5%以下")
    
    return recommendations

def main():
    """主函数"""
    
    print("🔍 **异常样本深度分析**")
    print("🎯 **目标: 理解异常样本的具体问题并制定处理策略**")
    print("=" * 80)
    
    # 分析异常样本特征
    outlier_details = analyze_outlier_characteristics()
    
    # 与正常样本对比
    all_features, outlier_features, normal_features = compare_with_normal_samples()
    
    # 识别异常模式
    outlier_reasons, severe_outliers, moderate_outliers, mild_outliers = identify_outlier_patterns()
    
    # 创建可视化
    create_outlier_visualization()
    
    # 生成处理建议
    recommendations = generate_recommendations()
    
    print(f"\n🎉 **异常样本分析完成!**")
    print(f"📊 发现的主要问题:")
    print(f"   • 严重异常: {len(severe_outliers)}个 - 建议移除")
    print(f"   • 中度异常: {len(moderate_outliers)}个 - 需要审核") 
    print(f"   • 轻微异常: {len(mild_outliers)}个 - 可以保留")
    print(f"📈 预期效果: 数据质量从88.8%提升到95%+")
    print(f"🎯 下一步: 开始逐一审核这些异常样本")

if __name__ == "__main__":
    main()
