"""
改进的数据加载器
支持数据增强和更好的点云生成
"""

import os
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset
from pathlib import Path
from improved_model import PointCloudAugmentation

class ImprovedDataset(Dataset):
    """改进的数据集类"""
    
    def __init__(self, data_root, num_points=2048, split='train', use_augmentation=True):
        self.data_root = Path(data_root)
        self.annotations_dir = self.data_root / "temp_annotations"
        self.num_points = num_points
        self.split = split
        self.use_augmentation = use_augmentation and (split == 'train')
        
        # 数据增强
        if self.use_augmentation:
            self.augmentation = PointCloudAugmentation(
                rotation_range=20,      # 增加旋转范围
                scale_range=0.15,       # 增加缩放范围
                noise_std=0.02,         # 增加噪声
                translation_range=8.0   # 增加平移范围
            )
        
        # 加载所有标注文件
        self.annotation_files = list(self.annotations_dir.glob("*.CSV"))
        print(f"Found {len(self.annotation_files)} annotation files for {split}")
        
        if len(self.annotation_files) == 0:
            raise ValueError(f"No annotation files found in {self.annotations_dir}")
    
    def __len__(self):
        return len(self.annotation_files)
    
    def __getitem__(self, idx):
        csv_file = self.annotation_files[idx]
        
        try:
            # 加载CSV文件
            for encoding in ['utf-8', 'gbk', 'gb2312', 'latin-1']:
                try:
                    df = pd.read_csv(csv_file, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue
            else:
                print(f"Could not decode {csv_file}, using dummy data")
                return self._create_dummy_data()
            
            if df.empty:
                return self._create_dummy_data()
            
            # 提取关键点坐标
            keypoints = df[['X', 'Y', 'Z']].to_numpy().astype(np.float32)
            
            # 生成更真实的点云
            point_cloud = self._generate_realistic_point_cloud(keypoints)
            
            # 应用数据增强
            if self.use_augmentation:
                point_cloud, keypoints = self.augmentation(point_cloud, keypoints)
            
            # 标准化坐标
            point_cloud, keypoints = self._normalize_coordinates(point_cloud, keypoints)
            
            return (
                torch.from_numpy(point_cloud).float(),
                torch.from_numpy(keypoints).float()
            )
            
        except Exception as e:
            print(f"Error processing {csv_file}: {e}")
            return self._create_dummy_data()
    
    def _generate_realistic_point_cloud(self, keypoints):
        """生成更真实的点云"""
        # 计算关键点的边界框
        min_coords = np.min(keypoints, axis=0)
        max_coords = np.max(keypoints, axis=0)
        center = (min_coords + max_coords) / 2
        
        # 扩大边界框以模拟完整的骨盆结构
        expansion = (max_coords - min_coords) * 0.3
        min_coords -= expansion
        max_coords += expansion
        
        # 生成基础点云
        base_points = []
        
        # 1. 在关键点附近生成密集点（模拟骨骼表面）
        for kp in keypoints:
            # 在每个关键点周围生成点
            num_local_points = max(5, self.num_points // (len(keypoints) * 2))
            local_points = self._generate_points_around_keypoint(kp, num_local_points)
            base_points.append(local_points)
        
        # 2. 在边界框内生成背景点（模拟周围组织）
        num_background = self.num_points - sum(len(pts) for pts in base_points)
        if num_background > 0:
            background_points = np.random.uniform(
                min_coords, max_coords, size=(num_background, 3)
            ).astype(np.float32)
            base_points.append(background_points)
        
        # 合并所有点
        all_points = np.vstack(base_points)
        
        # 如果点数超过需要的数量，随机采样
        if len(all_points) > self.num_points:
            indices = np.random.choice(len(all_points), self.num_points, replace=False)
            all_points = all_points[indices]
        elif len(all_points) < self.num_points:
            # 如果点数不够，重复采样
            indices = np.random.choice(len(all_points), self.num_points, replace=True)
            all_points = all_points[indices]
        
        return all_points
    
    def _generate_points_around_keypoint(self, keypoint, num_points, radius_range=(1.0, 5.0)):
        """在关键点周围生成点"""
        points = []
        
        for _ in range(num_points):
            # 随机半径和方向
            radius = np.random.uniform(radius_range[0], radius_range[1])
            direction = np.random.normal(0, 1, 3)
            direction = direction / np.linalg.norm(direction)
            
            # 生成点
            point = keypoint + direction * radius
            points.append(point)
        
        return np.array(points, dtype=np.float32)
    
    def _normalize_coordinates(self, point_cloud, keypoints):
        """标准化坐标到合理范围"""
        # 计算整体的中心和尺度
        all_points = np.vstack([point_cloud, keypoints])
        center = np.mean(all_points, axis=0)
        
        # 中心化
        point_cloud = point_cloud - center
        keypoints = keypoints - center
        
        # 缩放到合理范围 [-50, 50]
        max_range = np.max(np.abs(all_points - center))
        if max_range > 0:
            scale = 50.0 / max_range
            point_cloud = point_cloud * scale
            keypoints = keypoints * scale
        
        return point_cloud, keypoints
    
    def _create_dummy_data(self):
        """创建虚拟数据"""
        # 生成随机点云
        point_cloud = np.random.uniform(-50, 50, (self.num_points, 3)).astype(np.float32)
        
        # 生成随机关键点
        keypoints = np.random.uniform(-30, 30, (57, 3)).astype(np.float32)
        
        return (
            torch.from_numpy(point_cloud).float(),
            torch.from_numpy(keypoints).float()
        )

class ImprovedDataLoader:
    """改进的数据加载器管理器"""
    
    def __init__(self, data_root, batch_size=4, num_workers=2, num_points=2048):
        self.data_root = data_root
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.num_points = num_points
    
    def create_dataloaders(self, train_ratio=0.8):
        """创建训练和验证数据加载器"""
        from torch.utils.data import DataLoader, random_split
        
        # 创建完整数据集
        full_dataset = ImprovedDataset(
            self.data_root, 
            num_points=self.num_points, 
            split='full',
            use_augmentation=False  # 先不增强，等分割后再设置
        )
        
        # 分割数据集
        total_size = len(full_dataset)
        train_size = int(train_ratio * total_size)
        val_size = total_size - train_size
        
        train_dataset, val_dataset = random_split(
            full_dataset, [train_size, val_size],
            generator=torch.Generator().manual_seed(42)
        )
        
        # 为训练集启用数据增强
        train_dataset.dataset.split = 'train'
        train_dataset.dataset.use_augmentation = True
        train_dataset.dataset.augmentation = PointCloudAugmentation(
            rotation_range=20,
            scale_range=0.15,
            noise_std=0.02,
            translation_range=8.0
        )
        
        # 验证集不使用数据增强
        val_dataset.dataset.split = 'val'
        val_dataset.dataset.use_augmentation = False
        
        # 创建数据加载器
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            drop_last=True,
            pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            drop_last=False,
            pin_memory=True
        )
        
        print(f"训练样本: {len(train_dataset)}")
        print(f"验证样本: {len(val_dataset)}")
        print(f"训练批次: {len(train_loader)}")
        print(f"验证批次: {len(val_loader)}")
        
        return train_loader, val_loader

def test_improved_dataloader():
    """测试改进的数据加载器"""
    print("测试改进的数据加载器...")
    
    # 创建数据加载器
    data_loader_manager = ImprovedDataLoader(
        data_root="output/training_fixed",
        batch_size=2,
        num_workers=0,  # 测试时使用0避免多进程问题
        num_points=1024
    )
    
    try:
        train_loader, val_loader = data_loader_manager.create_dataloaders()
        
        # 测试训练数据
        print("\n测试训练数据:")
        for i, (point_cloud, keypoints) in enumerate(train_loader):
            print(f"批次 {i+1}:")
            print(f"  点云形状: {point_cloud.shape}")
            print(f"  关键点形状: {keypoints.shape}")
            print(f"  点云范围: [{point_cloud.min():.2f}, {point_cloud.max():.2f}]")
            print(f"  关键点范围: [{keypoints.min():.2f}, {keypoints.max():.2f}]")
            
            if i >= 1:  # 只测试前2个批次
                break
        
        # 测试验证数据
        print("\n测试验证数据:")
        for i, (point_cloud, keypoints) in enumerate(val_loader):
            print(f"批次 {i+1}:")
            print(f"  点云形状: {point_cloud.shape}")
            print(f"  关键点形状: {keypoints.shape}")
            
            if i >= 0:  # 只测试1个批次
                break
        
        print("\n数据加载器测试成功！")
        return True
        
    except Exception as e:
        print(f"数据加载器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_improved_dataloader()
