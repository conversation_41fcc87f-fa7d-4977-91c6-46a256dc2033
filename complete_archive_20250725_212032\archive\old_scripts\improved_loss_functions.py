#!/usr/bin/env python3
"""
改进的损失函数 - Phase 1架构改进
基于12关键点成功配置，实现更适合医疗关键点检测的损失函数
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class WingLoss(nn.Module):
    """
    Wing Loss - 专门为小误差优化的损失函数
    在小误差区域表现更好，适合医疗精度要求
    """
    
    def __init__(self, omega=10.0, epsilon=2.0):
        super(WingLoss, self).__init__()
        self.omega = omega
        self.epsilon = epsilon
        self.C = self.omega - self.omega * np.log(1 + self.omega / self.epsilon)
    
    def forward(self, pred, target):
        """
        Wing Loss计算
        在|x| < omega时使用log函数，在|x| >= omega时使用线性函数
        """
        diff = torch.abs(pred - target)
        
        # 小误差区域使用log函数 (更敏感)
        small_error_mask = diff < self.omega
        small_error_loss = self.omega * torch.log(1 + diff[small_error_mask] / self.epsilon)
        
        # 大误差区域使用线性函数 (避免梯度爆炸)
        large_error_loss = diff[~small_error_mask] - self.C
        
        # 合并损失
        loss = torch.zeros_like(diff)
        loss[small_error_mask] = small_error_loss
        loss[~small_error_mask] = large_error_loss
        
        return loss.mean()

class FocalLoss(nn.Module):
    """
    Focal Loss - 专注于难样本的损失函数
    自动关注预测困难的关键点
    """
    
    def __init__(self, alpha=1.0, gamma=2.0):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
    
    def forward(self, pred, target):
        """
        Focal Loss计算
        通过调制因子(1-pt)^gamma来降低易样本的权重
        """
        # 计算基础损失 (L1 loss)
        base_loss = F.l1_loss(pred, target, reduction='none')
        
        # 计算预测置信度 (距离越小置信度越高)
        distances = torch.norm(pred - target, dim=-1)
        max_distance = distances.max().detach()
        confidence = 1.0 - (distances / (max_distance + 1e-8))
        
        # 计算调制因子
        modulating_factor = torch.pow(1.0 - confidence, self.gamma)
        
        # 应用focal loss
        focal_loss = self.alpha * modulating_factor.unsqueeze(-1) * base_loss
        
        return focal_loss.mean()

class GeometricConsistencyLoss(nn.Module):
    """
    几何一致性损失 - 保持关键点间的相对位置关系
    基于医疗解剖学约束
    """

    def __init__(self, keypoint_pairs=None, weight=1.0):
        super(GeometricConsistencyLoss, self).__init__()
        self.weight = weight
        
        # 定义重要的关键点对 (基于12关键点的解剖关系)
        if keypoint_pairs is None:
            self.keypoint_pairs = [
                (0, 1),   # 关键点2-3: 左右对称点
                (2, 6),   # 关键点4-8: 左右对称点  
                (3, 4),   # 关键点5-6: 相邻上部点
                (7, 8),   # 关键点9-10: 相邻上部点
                (5, 9),   # 关键点7-11: 顶部对称点
                (10, 11), # 关键点16-17: 底部对称点
            ]
        else:
            self.keypoint_pairs = keypoint_pairs
    
    def forward(self, pred, target):
        """
        计算关键点间距离的一致性
        """
        total_loss = 0.0
        
        for i, j in self.keypoint_pairs:
            # 预测的关键点间距离
            pred_dist = torch.norm(pred[:, i, :] - pred[:, j, :], dim=1)
            
            # 真实的关键点间距离
            target_dist = torch.norm(target[:, i, :] - target[:, j, :], dim=1)
            
            # 距离一致性损失
            dist_loss = F.mse_loss(pred_dist, target_dist)
            total_loss += dist_loss
        
        return self.weight * total_loss / len(self.keypoint_pairs)

class AdaptiveWeightedLoss(nn.Module):
    """
    自适应加权损失 - 根据关键点重要性动态调整权重
    """
    
    def __init__(self, keypoint_weights=None):
        super(AdaptiveWeightedLoss, self).__init__()
        
        # 基于稳定性分数的关键点权重 (12个关键点)
        if keypoint_weights is None:
            # 权重基于稳定性分数，稳定性越高权重越大
            self.keypoint_weights = torch.tensor([
                0.847,  # 关键点2
                0.835,  # 关键点3
                0.592,  # 关键点4
                0.782,  # 关键点5
                0.855,  # 关键点6
                0.867,  # 关键点7
                0.597,  # 关键点8
                0.780,  # 关键点9
                0.863,  # 关键点10
                0.869,  # 关键点11 (最稳定)
                0.805,  # 关键点16
                0.809   # 关键点17
            ])
        else:
            self.keypoint_weights = torch.tensor(keypoint_weights)
    
    def forward(self, pred, target):
        """
        计算加权损失
        """
        # 计算每个关键点的损失
        point_losses = torch.norm(pred - target, dim=2)  # [batch, 12]
        
        # 应用权重
        device = pred.device
        weights = self.keypoint_weights.to(device)
        weighted_losses = point_losses * weights.unsqueeze(0)
        
        return weighted_losses.mean()

class MedicalKeypointLoss(nn.Module):
    """
    医疗关键点检测专用损失函数
    结合多种损失函数的优势
    """
    
    def __init__(self, 
                 wing_weight=0.4,
                 focal_weight=0.3, 
                 geometric_weight=0.2,
                 adaptive_weight=0.1,
                 wing_omega=8.0,
                 wing_epsilon=1.5,
                 focal_alpha=1.0,
                 focal_gamma=2.0):
        super(MedicalKeypointLoss, self).__init__()
        
        self.wing_weight = wing_weight
        self.focal_weight = focal_weight
        self.geometric_weight = geometric_weight
        self.adaptive_weight = adaptive_weight
        
        # 初始化各个损失函数
        self.wing_loss = WingLoss(omega=wing_omega, epsilon=wing_epsilon)
        self.focal_loss = FocalLoss(alpha=focal_alpha, gamma=focal_gamma)
        self.geometric_loss = GeometricConsistencyLoss(weight=1.0)
        self.adaptive_loss = AdaptiveWeightedLoss()
        
        print(f"🎯 医疗关键点损失函数初始化:")
        print(f"   Wing Loss权重: {wing_weight} (omega={wing_omega}, epsilon={wing_epsilon})")
        print(f"   Focal Loss权重: {focal_weight} (alpha={focal_alpha}, gamma={focal_gamma})")
        print(f"   几何一致性权重: {geometric_weight}")
        print(f"   自适应权重: {adaptive_weight}")
    
    def forward(self, pred, target):
        """
        计算组合损失
        """
        # 各个损失分量
        wing_loss_val = self.wing_loss(pred, target)
        focal_loss_val = self.focal_loss(pred, target)
        geometric_loss_val = self.geometric_loss(pred, target)
        adaptive_loss_val = self.adaptive_loss(pred, target)
        
        # 组合损失
        total_loss = (self.wing_weight * wing_loss_val + 
                     self.focal_weight * focal_loss_val +
                     self.geometric_weight * geometric_loss_val +
                     self.adaptive_weight * adaptive_loss_val)
        
        # 返回总损失和各分量 (用于监控)
        loss_components = {
            'total_loss': total_loss,
            'wing_loss': wing_loss_val,
            'focal_loss': focal_loss_val,
            'geometric_loss': geometric_loss_val,
            'adaptive_loss': adaptive_loss_val
        }
        
        return total_loss, loss_components

class ProgressiveLoss(nn.Module):
    """
    渐进式损失函数 - 训练过程中动态调整损失权重
    早期关注基础定位，后期关注精度优化
    """
    
    def __init__(self):
        super(ProgressiveLoss, self).__init__()
        self.medical_loss = MedicalKeypointLoss()
        self.epoch = 0
    
    def update_epoch(self, epoch):
        """更新当前训练轮数"""
        self.epoch = epoch
    
    def forward(self, pred, target):
        """
        根据训练进度调整损失权重
        """
        # 计算基础损失
        total_loss, components = self.medical_loss(pred, target)
        
        # 根据训练进度调整
        if self.epoch < 20:
            # 早期: 关注基础定位 (更多Wing Loss)
            wing_boost = 1.5
            geometric_boost = 0.5
        elif self.epoch < 50:
            # 中期: 平衡各种损失
            wing_boost = 1.0
            geometric_boost = 1.0
        else:
            # 后期: 关注精度和一致性
            wing_boost = 1.2
            geometric_boost = 1.5
        
        # 重新计算加权损失
        adjusted_loss = (wing_boost * self.medical_loss.wing_weight * components['wing_loss'] +
                        self.medical_loss.focal_weight * components['focal_loss'] +
                        geometric_boost * self.medical_loss.geometric_weight * components['geometric_loss'] +
                        self.medical_loss.adaptive_weight * components['adaptive_loss'])
        
        components['total_loss'] = adjusted_loss
        components['wing_boost'] = wing_boost
        components['geometric_boost'] = geometric_boost
        
        return adjusted_loss, components

def test_loss_functions():
    """测试损失函数"""
    
    print("🧪 **测试改进的损失函数**")
    print("=" * 50)
    
    # 创建测试数据
    batch_size = 4
    num_keypoints = 12
    
    # 模拟预测和真实值
    pred = torch.randn(batch_size, num_keypoints, 3) * 10
    target = torch.randn(batch_size, num_keypoints, 3) * 10
    
    # 测试各个损失函数
    print("📊 **损失函数测试结果**:")
    
    # Wing Loss
    wing_loss = WingLoss()
    wing_val = wing_loss(pred, target)
    print(f"   Wing Loss: {wing_val:.4f}")
    
    # Focal Loss
    focal_loss = FocalLoss()
    focal_val = focal_loss(pred, target)
    print(f"   Focal Loss: {focal_val:.4f}")
    
    # Geometric Consistency Loss
    geo_loss = GeometricConsistencyLoss()
    geo_val = geo_loss(pred, target)
    print(f"   Geometric Loss: {geo_val:.4f}")
    
    # Adaptive Weighted Loss
    adaptive_loss = AdaptiveWeightedLoss()
    adaptive_val = adaptive_loss(pred, target)
    print(f"   Adaptive Loss: {adaptive_val:.4f}")
    
    # Medical Keypoint Loss
    medical_loss = MedicalKeypointLoss()
    medical_val, components = medical_loss(pred, target)
    print(f"   Medical Loss (总计): {medical_val:.4f}")
    print(f"     - Wing分量: {components['wing_loss']:.4f}")
    print(f"     - Focal分量: {components['focal_loss']:.4f}")
    print(f"     - Geometric分量: {components['geometric_loss']:.4f}")
    print(f"     - Adaptive分量: {components['adaptive_loss']:.4f}")
    
    # Progressive Loss
    prog_loss = ProgressiveLoss()
    prog_loss.update_epoch(30)
    prog_val, prog_components = prog_loss(pred, target)
    print(f"   Progressive Loss: {prog_val:.4f}")
    
    print("\n✅ 所有损失函数测试通过!")

if __name__ == "__main__":
    test_loss_functions()
