#!/usr/bin/env python3
"""
训练增强数据的Heatmap模型
Train Heatmap Model with Augmented Data
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import os
from tqdm import tqdm
import time

class AugmentedHeatmapDataset(Dataset):
    """增强数据的Heatmap数据集"""
    
    def __init__(self, data_path, split='train', train_ratio=0.7, val_ratio=0.15):
        print(f"📊 加载增强数据集: {data_path}")
        
        if not os.path.exists(data_path):
            raise FileNotFoundError(f"数据文件不存在: {data_path}")
        
        # 加载数据
        data = np.load(data_path, allow_pickle=True)
        self.sample_ids = data['sample_ids']
        self.point_clouds = data['point_clouds']
        self.keypoints = data['keypoints']
        self.heatmaps = data['heatmaps']
        
        print(f"✅ 数据加载成功:")
        print(f"   总样本数: {len(self.sample_ids)}")
        print(f"   点云形状: {self.point_clouds.shape}")
        print(f"   关键点形状: {self.keypoints.shape}")
        print(f"   热图形状: {self.heatmaps.shape}")
        
        # 数据分割
        n_samples = len(self.sample_ids)
        n_train = int(n_samples * train_ratio)
        n_val = int(n_samples * val_ratio)
        
        if split == 'train':
            self.indices = list(range(n_train))
        elif split == 'val':
            self.indices = list(range(n_train, n_train + n_val))
        else:  # test
            self.indices = list(range(n_train + n_val, n_samples))
        
        print(f"📋 {split}集样本数: {len(self.indices)}")
    
    def __len__(self):
        return len(self.indices)
    
    def __getitem__(self, idx):
        real_idx = self.indices[idx]
        
        point_cloud = torch.FloatTensor(self.point_clouds[real_idx])
        keypoints = torch.FloatTensor(self.keypoints[real_idx])
        heatmap = torch.FloatTensor(self.heatmaps[real_idx])
        
        return point_cloud, keypoints, heatmap

class SimpleHeatmapNet(nn.Module):
    """简化的Heatmap网络"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super(SimpleHeatmapNet, self).__init__()
        
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 点云特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        
        # 全局特征
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # Heatmap生成
        self.heatmap_conv1 = nn.Conv1d(1024 + 256, 512, 1)
        self.heatmap_conv2 = nn.Conv1d(512, 256, 1)
        self.heatmap_conv3 = nn.Conv1d(256, num_keypoints, 1)
        
        # 激活函数
        self.relu = nn.ReLU()
        self.softmax = nn.Softmax(dim=2)
        
        # Dropout
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        # x: (batch_size, num_points, 3)
        x = x.transpose(2, 1)  # (batch_size, 3, num_points)
        
        # 点云特征提取
        x1 = self.relu(self.conv1(x))
        x2 = self.relu(self.conv2(x1))
        x3 = self.relu(self.conv3(x2))
        
        # 全局特征
        x4 = self.relu(self.conv4(x3))
        x5 = self.relu(self.conv5(x4))
        
        # 全局最大池化
        global_feat = torch.max(x5, 2, keepdim=True)[0]
        
        # 扩展全局特征
        global_feat_expanded = global_feat.repeat(1, 1, self.num_points)
        
        # 融合局部和全局特征
        combined_feat = torch.cat([x3, global_feat_expanded], 1)
        
        # 生成热图
        heatmap = self.relu(self.heatmap_conv1(combined_feat))
        heatmap = self.dropout(heatmap)
        heatmap = self.relu(self.heatmap_conv2(heatmap))
        heatmap = self.heatmap_conv3(heatmap)
        
        # 转置回来并应用softmax
        heatmap = heatmap.transpose(2, 1)  # (batch_size, num_points, num_keypoints)
        heatmap = self.softmax(heatmap.transpose(2, 1)).transpose(2, 1)
        
        return heatmap

def heatmap_loss(pred_heatmap, target_heatmap):
    """Heatmap损失函数"""
    # KL散度损失
    kl_loss = nn.KLDivLoss(reduction='batchmean')
    
    # 对预测热图取对数
    log_pred = torch.log(pred_heatmap + 1e-8)
    
    # 计算每个关键点的KL散度
    total_loss = 0
    for i in range(pred_heatmap.shape[2]):  # 遍历每个关键点
        loss_i = kl_loss(log_pred[:, :, i], target_heatmap[:, :, i])
        total_loss += loss_i
    
    return total_loss / pred_heatmap.shape[2]

def extract_keypoints_from_heatmap(heatmap, point_cloud):
    """从热图中提取关键点坐标"""
    batch_size, num_points, num_keypoints = heatmap.shape
    keypoints = torch.zeros(batch_size, num_keypoints, 3)
    
    for b in range(batch_size):
        for k in range(num_keypoints):
            # 找到最大概率的点
            max_idx = torch.argmax(heatmap[b, :, k])
            keypoints[b, k] = point_cloud[b, max_idx]
    
    return keypoints

def train_model():
    """训练模型"""
    
    print("🔥 开始训练增强数据的Heatmap模型")
    print("=" * 80)
    
    # 设备设置
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ 使用设备: {device}")
    
    # 数据加载
    data_path = "f3_reduced_12kp_female_augmented.npz"
    
    train_dataset = AugmentedHeatmapDataset(data_path, split='train')
    val_dataset = AugmentedHeatmapDataset(data_path, split='val')
    test_dataset = AugmentedHeatmapDataset(data_path, split='test')
    
    # 数据加载器
    batch_size = 4  # 减小批次大小避免内存问题
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=2)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=2)
    
    print(f"📊 数据加载器创建完成:")
    print(f"   训练批次数: {len(train_loader)}")
    print(f"   验证批次数: {len(val_loader)}")
    print(f"   测试批次数: {len(test_loader)}")
    
    # 模型初始化
    model = SimpleHeatmapNet(num_points=50000, num_keypoints=12)
    model = model.to(device)
    
    # 优化器和学习率调度
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.7)
    
    # 训练参数
    num_epochs = 50
    best_val_loss = float('inf')
    patience = 10
    patience_counter = 0
    
    print(f"🎯 训练参数:")
    print(f"   训练轮数: {num_epochs}")
    print(f"   批次大小: {batch_size}")
    print(f"   学习率: 0.001")
    print(f"   早停耐心: {patience}")
    
    # 训练循环
    train_losses = []
    val_losses = []
    
    print(f"\n🚀 开始训练...")
    start_time = time.time()
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for batch_idx, (point_cloud, target_keypoints, target_heatmap) in enumerate(train_pbar):
            point_cloud = point_cloud.to(device)
            target_heatmap = target_heatmap.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            pred_heatmap = model(point_cloud)
            
            # 计算损失
            loss = heatmap_loss(pred_heatmap, target_heatmap)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_pbar.set_postfix({'Loss': f'{loss.item():.4f}'})
        
        avg_train_loss = train_loss / len(train_loader)
        train_losses.append(avg_train_loss)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_errors = []
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for point_cloud, target_keypoints, target_heatmap in val_pbar:
                point_cloud = point_cloud.to(device)
                target_keypoints = target_keypoints.to(device)
                target_heatmap = target_heatmap.to(device)
                
                # 前向传播
                pred_heatmap = model(point_cloud)
                
                # 计算损失
                loss = heatmap_loss(pred_heatmap, target_heatmap)
                val_loss += loss.item()
                
                # 提取关键点并计算误差
                pred_keypoints = extract_keypoints_from_heatmap(pred_heatmap.cpu(), point_cloud.cpu())
                error = torch.mean(torch.norm(pred_keypoints - target_keypoints.cpu(), dim=2))
                val_errors.append(error.item())
                
                val_pbar.set_postfix({'Loss': f'{loss.item():.4f}', 'Error': f'{error.item():.2f}mm'})
        
        avg_val_loss = val_loss / len(val_loader)
        avg_val_error = np.mean(val_errors)
        val_losses.append(avg_val_loss)
        
        # 学习率调度
        scheduler.step()
        
        # 打印epoch结果
        print(f"\nEpoch {epoch+1}/{num_epochs}:")
        print(f"  训练损失: {avg_train_loss:.4f}")
        print(f"  验证损失: {avg_val_loss:.4f}")
        print(f"  验证误差: {avg_val_error:.2f}mm")
        print(f"  学习率: {scheduler.get_last_lr()[0]:.6f}")
        
        # 早停检查
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            patience_counter = 0
            
            # 保存最佳模型
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': avg_val_loss,
                'val_error': avg_val_error,
            }, 'best_augmented_heatmap_model.pth')
            print(f"  ✅ 保存最佳模型 (验证误差: {avg_val_error:.2f}mm)")
        else:
            patience_counter += 1
            print(f"  ⏳ 早停计数: {patience_counter}/{patience}")
        
        if patience_counter >= patience:
            print(f"🛑 早停触发，停止训练")
            break
    
    training_time = time.time() - start_time
    print(f"\n⏱️ 训练完成，总用时: {training_time/60:.1f}分钟")
    
    # 测试最佳模型
    print(f"\n🧪 测试最佳模型...")
    checkpoint = torch.load('best_augmented_heatmap_model.pth')
    model.load_state_dict(checkpoint['model_state_dict'])
    
    model.eval()
    test_errors = []
    
    with torch.no_grad():
        test_pbar = tqdm(test_loader, desc='Testing')
        for point_cloud, target_keypoints, target_heatmap in test_pbar:
            point_cloud = point_cloud.to(device)
            target_keypoints = target_keypoints.to(device)
            
            pred_heatmap = model(point_cloud)
            pred_keypoints = extract_keypoints_from_heatmap(pred_heatmap.cpu(), point_cloud.cpu())
            
            error = torch.mean(torch.norm(pred_keypoints - target_keypoints.cpu(), dim=2))
            test_errors.append(error.item())
            
            test_pbar.set_postfix({'Error': f'{error.item():.2f}mm'})
    
    final_test_error = np.mean(test_errors)
    
    print(f"\n🎉 训练完成!")
    print(f"=" * 60)
    print(f"📊 最终结果:")
    print(f"   最佳验证误差: {checkpoint['val_error']:.2f}mm")
    print(f"   最终测试误差: {final_test_error:.2f}mm")
    print(f"   训练样本数: {len(train_dataset)}")
    print(f"   模型保存: best_augmented_heatmap_model.pth")
    
    # 与之前结果对比
    print(f"\n📈 性能对比:")
    print(f"   原始Heatmap (25样本): 4.88mm")
    print(f"   增强Heatmap (250样本): {final_test_error:.2f}mm")
    
    if final_test_error < 4.88:
        improvement = ((4.88 - final_test_error) / 4.88) * 100
        print(f"   🎉 性能提升: {improvement:.1f}%")
        if final_test_error < 4.0:
            print(f"   🏆 突破4mm大关!")
    else:
        print(f"   ⚠️ 性能未提升，需要进一步调优")

if __name__ == "__main__":
    train_model()
