#!/usr/bin/env python3
"""
基线模型 + 双Softmax机制
在成功的6.208mm基线模型基础上，仅添加双Softmax精细化机制
保持原有架构不变，只在推理时应用双Softmax
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class SimplifiedDoubleSoftMax(nn.Module):
    """
    简化的双Softmax机制
    专门为基线模型设计，轻量级实现
    """
    
    def __init__(self, threshold_ratio=0.2, temperature=1.5):
        super(SimplifiedDoubleSoftMax, self).__init__()
        
        self.threshold_ratio = threshold_ratio
        self.temperature = temperature
        
        # 简单的权重计算网络
        self.weight_net = nn.Sequential(
            nn.Linear(3, 32),  # 输入点坐标
            nn.ReLU(),
            nn.Linear(32, 16),
            nn.ReLU(), 
            nn.Linear(16, 1)   # 输出权重
        )
        
        print(f"🎯 简化双Softmax机制:")
        print(f"   阈值比例: {threshold_ratio}")
        print(f"   温度参数: {temperature}")
    
    def forward(self, points, predicted_keypoint):
        """
        为单个关键点计算双Softmax权重
        
        Args:
            points: [num_points, 3] 候选点
            predicted_keypoint: [3] 预测的关键点
            
        Returns:
            refined_keypoint: [3] 精细化后的关键点
        """
        # 计算相对位置
        relative_pos = points - predicted_keypoint.unsqueeze(0)  # [num_points, 3]
        
        # 第一个Softmax - 基于距离的初始权重
        distances = torch.norm(relative_pos, dim=1)  # [num_points]
        distance_weights = F.softmax(-distances / self.temperature, dim=0)
        
        # 第二个Softmax - 基于神经网络的精细权重
        nn_weights = self.weight_net(relative_pos).squeeze(-1)  # [num_points]
        nn_weights = F.softmax(nn_weights / self.temperature, dim=0)
        
        # 组合权重
        combined_weights = 0.7 * distance_weights + 0.3 * nn_weights
        
        # 阈值过滤
        mean_weight = torch.mean(combined_weights)
        threshold = mean_weight * self.threshold_ratio
        filter_mask = combined_weights > threshold
        
        # 重新归一化
        filtered_weights = combined_weights * filter_mask.float()
        sum_weights = torch.sum(filtered_weights)
        
        if sum_weights > 1e-8:
            final_weights = filtered_weights / sum_weights
        else:
            final_weights = combined_weights  # 回退到原始权重
        
        # 加权平均得到精细化关键点
        refined_keypoint = torch.sum(final_weights.unsqueeze(-1) * points, dim=0)
        
        return refined_keypoint

class BaselineAdaptivePointNet(nn.Module):
    """
    基线AdaptivePointNet + 双Softmax精细化
    保持原有架构，只在推理时应用双Softmax
    """
    
    def __init__(self, num_keypoints: int, dropout_rate: float = 0.3):
        super(BaselineAdaptivePointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 完全复制基线模型架构
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(dropout_rate)
        
        # 双Softmax机制 (仅在推理时使用)
        self.double_softmax = SimplifiedDoubleSoftMax(
            threshold_ratio=0.2,
            temperature=1.5
        )
        
        print(f"🧠 基线模型 + 双Softmax: {num_keypoints}个关键点")
        print(f"   - 保持原有基线架构")
        print(f"   - 推理时应用双Softmax精细化")
        
    def forward(self, x):
        batch_size = x.size(0)
        x_input = x.transpose(2, 1)  # [batch, 3, num_points]
        
        # 基线模型的前向传播 (完全一致)
        x1 = torch.relu(self.bn1(self.conv1(x_input)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        
        x3_res = x3 + self.residual1(x1)
        
        x4 = torch.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = torch.relu(self.bn5(self.conv5(x4_res)))
        
        global_feat = torch.max(x5, 2)[0]
        
        feat = torch.relu(self.bn_fc1(self.fc1(global_feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc2(self.fc2(feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc3(self.fc3(feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc4(self.fc4(feat)))
        feat = self.dropout(feat)
        feat = self.fc5(feat)
        
        keypoints = feat.view(batch_size, self.num_keypoints, 3)
        
        # 推理时应用双Softmax精细化
        if not self.training:
            keypoints = self.apply_double_softmax_refinement(x, keypoints)
        
        return keypoints
    
    def apply_double_softmax_refinement(self, points, predicted_keypoints):
        """
        应用双Softmax精细化
        """
        batch_size = points.shape[0]
        refined_keypoints = []
        
        for b in range(batch_size):
            batch_points = points[b]  # [num_points, 3]
            batch_keypoints = predicted_keypoints[b]  # [num_keypoints, 3]
            
            batch_refined = []
            for k in range(self.num_keypoints):
                kp_pred = batch_keypoints[k]  # [3]
                
                # 找到最近的候选点
                distances = torch.norm(batch_points - kp_pred.unsqueeze(0), dim=1)
                K = min(256, batch_points.shape[0])  # 选择候选点数量
                _, nearest_indices = torch.topk(distances, K, largest=False)
                
                candidate_points = batch_points[nearest_indices]  # [K, 3]
                
                # 应用双Softmax
                refined_kp = self.double_softmax(candidate_points, kp_pred)
                batch_refined.append(refined_kp)
            
            refined_keypoints.append(torch.stack(batch_refined))
        
        return torch.stack(refined_keypoints)

class ImprovedLoss(nn.Module):
    """基线模型使用的损失函数"""
    
    def __init__(self, alpha=0.8, beta=0.2):
        super(ImprovedLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, pred, target):
        mse_loss = F.mse_loss(pred, target)
        smooth_l1_loss = F.smooth_l1_loss(pred, target)
        total_loss = self.alpha * mse_loss + self.beta * smooth_l1_loss
        return total_loss

class ReducedKeypointsF3Dataset(Dataset):
    """12关键点F3数据集 (复用基线配置)"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points
        self.augment = augment
        self.split = split
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
    
    def __len__(self):
        return len(self.sample_ids)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        # 基线模型的数据增强策略
        if self.augment and self.split == 'train':
            if np.random.random() < 0.7:
                angle = np.random.uniform(-0.08, 0.08)
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                point_cloud = point_cloud @ rotation.T
                keypoints = keypoints @ rotation.T
            
            if np.random.random() < 0.6:
                translation = np.random.uniform(-0.4, 0.4, 3)
                point_cloud += translation
                keypoints += translation
            
            if np.random.random() < 0.5:
                scale = np.random.uniform(0.99, 1.01, 3)
                point_cloud *= scale
                keypoints *= scale
            
            if np.random.random() < 0.6:
                noise_level = np.random.choice([0.02, 0.03, 0.04])
                noise = np.random.normal(0, noise_level, point_cloud.shape)
                point_cloud += noise
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def train_baseline_with_double_softmax():
    """训练基线模型 + 双Softmax"""
    
    print(f"🚀 **基线模型 + 双Softmax训练**")
    print(f"🎯 **基础**: 成功的6.208mm基线架构")
    print(f"📈 **目标**: 通过双Softmax精细化突破6mm")
    print(f"🔧 **策略**: 保持基线架构，仅在推理时应用双Softmax")
    print("=" * 80)
    
    set_seed(42)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 数据集 (完全复用基线配置)
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    train_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'train', 
                                            num_points=4096, test_samples=test_samples, 
                                            augment=True, seed=42)
    val_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'val', 
                                          num_points=4096, test_samples=test_samples, 
                                          augment=False, seed=42)
    
    batch_size = 4  # 基线配置
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
    
    # 模型
    model = BaselineAdaptivePointNet(num_keypoints=12, dropout_rate=0.3).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 模型参数: {total_params:,}")
    
    # 损失函数 (基线配置)
    criterion = ImprovedLoss(alpha=0.8, beta=0.2)
    
    # 优化器 (基线配置)
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
    
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=12, min_lr=1e-6
    )
    
    num_epochs = 150
    best_val_error = float('inf')
    patience = 20
    patience_counter = 0
    history = []
    min_delta = 0.005
    
    print(f"🎯 训练配置: 基线参数 + 双Softmax精细化")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 40)
        
        # 训练 (双Softmax不参与训练)
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        for batch in train_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)  # 训练时不使用双Softmax
                loss = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                        
            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue
        
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # 验证 (使用双Softmax精细化)
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)  # 推理时使用双Softmax
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # 学习率调度
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 打印结果
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"5mm={train_metrics['within_5mm_percent']:.1f}%, 7mm={train_metrics['within_7mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"5mm={val_metrics['within_5mm_percent']:.1f}%, 7mm={val_metrics['within_7mm_percent']:.1f}%")
        print(f"学习率: {current_lr:.2e}")
        
        # 保存历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'learning_rate': current_lr
        })
        
        # 检查改进
        current_error = val_metrics['mean_distance']
        improvement = best_val_error - current_error
        
        if improvement > min_delta:
            best_val_error = current_error
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics,
                'config': 'baseline_with_double_softmax'
            }, f'best_baseline_double_softmax_{best_val_error:.3f}mm.pth')
            
            print(f"🎉 新最佳! 验证误差: {best_val_error:.3f}mm (改进{improvement:.3f}mm)")
            
            if best_val_error <= 5.0:
                print(f"🏆 **突破5mm医疗级目标!**")
            elif best_val_error < 6.0:
                print(f"🎯 **突破6mm目标!** 双Softmax精细化有效")
            elif best_val_error < 6.208:
                print(f"✅ **超越基线!** 双Softmax改进有效")
        else:
            patience_counter += 1
            print(f"⏳ 无显著改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停触发")
            break
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    
    # 保存结果
    results = {
        'method': 'Baseline + Double SoftMax Refinement',
        'baseline_error': 6.208,
        'best_val_error': float(best_val_error),
        'improvement': float((6.208 - best_val_error) / 6.208 * 100),
        'training_time_minutes': float(total_time / 60),
        'epochs_trained': len(history),
        'history': history,
        'double_softmax_config': {
            'threshold_ratio': 0.2,
            'temperature': 1.5,
            'candidate_points': 256,
            'weight_combination': '0.7*distance + 0.3*neural'
        }
    }
    
    with open('baseline_double_softmax_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 **基线+双Softmax训练完成!**")
    print(f"📊 基线误差: 6.208mm")
    print(f"🎯 最佳误差: {best_val_error:.3f}mm")
    print(f"📈 改进幅度: {(6.208 - best_val_error) / 6.208 * 100:.1f}%")
    print(f"⏱️  训练时间: {total_time/60:.1f}分钟")
    
    if best_val_error < 6.0:
        print(f"🎉 **成功突破6mm目标!** 双Softmax精细化有效")
    elif best_val_error < 6.208:
        print(f"✅ **成功超越基线!** 双Softmax带来改进")
    else:
        print(f"💡 **接近基线性能** 双Softmax参数可能需要调优")
    
    return best_val_error, results

if __name__ == "__main__":
    set_seed(42)
    best_error, results = train_baseline_with_double_softmax()
