#!/usr/bin/env python3
"""
简化版Heatmap数据增强
Simple Heatmap Data Augmentation
"""

import numpy as np
import os
from tqdm import tqdm

class SimpleHeatmapAugmenter:
    """简化版Heatmap数据增强器"""
    
    def __init__(self, augment_factor=5):
        self.augment_factor = augment_factor
        print(f"🔥 简化版Heatmap增强器初始化")
        print(f"   增强倍数: {augment_factor}x")
    
    def rotate_3d(self, points, angles_deg):
        """3D旋转变换"""
        # 转换为弧度
        angles = np.radians(angles_deg)
        
        # 绕X轴旋转
        Rx = np.array([[1, 0, 0],
                       [0, np.cos(angles[0]), -np.sin(angles[0])],
                       [0, np.sin(angles[0]), np.cos(angles[0])]])
        
        # 绕Y轴旋转
        Ry = np.array([[np.cos(angles[1]), 0, np.sin(angles[1])],
                       [0, 1, 0],
                       [-np.sin(angles[1]), 0, np.cos(angles[1])]])
        
        # 绕Z轴旋转
        Rz = np.array([[np.cos(angles[2]), -np.sin(angles[2]), 0],
                       [np.sin(angles[2]), np.cos(angles[2]), 0],
                       [0, 0, 1]])
        
        # 组合旋转矩阵
        R = Rz @ Ry @ Rx
        
        # 应用旋转
        return points @ R.T
    
    def generate_heatmap(self, keypoints, point_cloud, sigma=5.0):
        """从关键点生成热图"""
        heatmaps = []
        
        for kp in keypoints:
            # 计算距离
            distances = np.linalg.norm(point_cloud - kp, axis=1)
            
            # 生成高斯分布
            heatmap = np.exp(-distances**2 / (2 * sigma**2))
            
            # 归一化
            if np.sum(heatmap) > 0:
                heatmap = heatmap / np.sum(heatmap)
            
            heatmaps.append(heatmap)
        
        return np.array(heatmaps)
    
    def spatial_augment(self, point_cloud, keypoints):
        """空间变换增强"""
        # 随机旋转角度 (±15度)
        angles = np.random.uniform(-15, 15, 3)
        
        # 旋转点云和关键点
        pc_rotated = self.rotate_3d(point_cloud, angles)
        kp_rotated = self.rotate_3d(keypoints, angles)
        
        return pc_rotated, kp_rotated
    
    def gaussian_augment(self, keypoints, point_cloud):
        """高斯核调整增强"""
        # 随机调整sigma
        sigma_factor = np.random.uniform(0.8, 1.2)
        new_sigma = 5.0 * sigma_factor
        
        # 生成新热图
        heatmap = self.generate_heatmap(keypoints, point_cloud, sigma=new_sigma)
        
        return heatmap
    
    def uncertainty_augment(self, keypoints, point_cloud):
        """不确定性增强"""
        # 添加随机噪声
        noise = np.random.normal(0, 2.0, keypoints.shape)
        kp_noisy = keypoints + noise
        
        # 生成新热图
        heatmap = self.generate_heatmap(kp_noisy, point_cloud)
        
        return kp_noisy, heatmap
    
    def augment_sample(self, point_cloud, keypoints):
        """增强单个样本"""
        augmented_samples = []
        
        # 原始样本
        original_heatmap = self.generate_heatmap(keypoints, point_cloud)
        augmented_samples.append({
            'point_cloud': point_cloud,
            'keypoints': keypoints,
            'heatmap': original_heatmap,
            'method': 'original'
        })
        
        # 生成增强样本
        for i in range(self.augment_factor):
            # 空间旋转增强
            pc_rot, kp_rot = self.spatial_augment(point_cloud, keypoints)
            hm_rot = self.generate_heatmap(kp_rot, pc_rot)
            
            augmented_samples.append({
                'point_cloud': pc_rot,
                'keypoints': kp_rot,
                'heatmap': hm_rot,
                'method': f'rotation_{i}'
            })
            
            # 高斯核调整
            hm_gauss = self.gaussian_augment(keypoints, point_cloud)
            
            augmented_samples.append({
                'point_cloud': point_cloud.copy(),
                'keypoints': keypoints.copy(),
                'heatmap': hm_gauss,
                'method': f'gaussian_{i}'
            })
            
            # 不确定性增强
            kp_uncertain, hm_uncertain = self.uncertainty_augment(keypoints, point_cloud)
            
            augmented_samples.append({
                'point_cloud': point_cloud.copy(),
                'keypoints': kp_uncertain,
                'heatmap': hm_uncertain,
                'method': f'uncertainty_{i}'
            })
        
        return augmented_samples

def load_female_dataset():
    """加载女性数据集"""
    print("📊 加载女性数据集...")
    
    female_path = "archive/old_experiments/f3_reduced_12kp_female.npz"
    
    if not os.path.exists(female_path):
        print(f"❌ 文件不存在: {female_path}")
        return None, None, None
    
    try:
        data = np.load(female_path, allow_pickle=True)
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        print(f"✅ 加载成功:")
        print(f"   样本数量: {len(sample_ids)}")
        print(f"   点云形状: {point_clouds.shape}")
        print(f"   关键点形状: {keypoints.shape}")
        
        return sample_ids, point_clouds, keypoints
        
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return None, None, None

def augment_female_dataset():
    """增强女性数据集"""
    print("🔥 开始增强女性数据集")
    print("=" * 60)
    
    # 加载数据
    sample_ids, point_clouds, keypoints = load_female_dataset()
    if sample_ids is None:
        return None
    
    # 初始化增强器
    augmenter = SimpleHeatmapAugmenter(augment_factor=3)
    
    # 存储结果
    all_point_clouds = []
    all_keypoints = []
    all_heatmaps = []
    all_sample_ids = []
    all_methods = []
    
    print(f"\n🚀 开始增强处理...")
    
    # 处理每个样本
    for i, (sample_id, pc, kp) in enumerate(zip(sample_ids, point_clouds, keypoints)):
        print(f"处理样本 {i+1}/{len(sample_ids)}: {sample_id}")
        
        # 增强样本
        augmented_samples = augmenter.augment_sample(pc, kp)
        
        # 收集结果
        for aug_sample in augmented_samples:
            all_point_clouds.append(aug_sample['point_cloud'])
            all_keypoints.append(aug_sample['keypoints'])
            all_heatmaps.append(aug_sample['heatmap'])
            all_sample_ids.append(f"{sample_id}_{aug_sample['method']}")
            all_methods.append(aug_sample['method'])
    
    # 转换为numpy数组
    all_point_clouds = np.array(all_point_clouds)
    all_keypoints = np.array(all_keypoints)
    all_heatmaps = np.array(all_heatmaps)
    
    print(f"\n📊 增强结果:")
    print(f"   原始样本: {len(sample_ids)}")
    print(f"   增强后总数: {len(all_sample_ids)}")
    print(f"   增强倍数: {len(all_sample_ids) / len(sample_ids):.1f}x")
    
    # 统计方法分布
    method_counts = {}
    for method in all_methods:
        method_type = method.split('_')[0]
        method_counts[method_type] = method_counts.get(method_type, 0) + 1
    
    print(f"\n📈 增强方法分布:")
    for method, count in method_counts.items():
        print(f"   {method}: {count}个样本")
    
    # 保存结果
    output_path = "f3_reduced_12kp_female_augmented.npz"
    
    try:
        np.savez_compressed(output_path,
                           sample_ids=all_sample_ids,
                           point_clouds=all_point_clouds,
                           keypoints=all_keypoints,
                           heatmaps=all_heatmaps,
                           methods=all_methods)
        
        print(f"\n💾 增强数据集已保存: {output_path}")
        
        # 验证保存的文件
        verify_data = np.load(output_path, allow_pickle=True)
        print(f"\n✅ 文件验证:")
        print(f"   样本ID数量: {len(verify_data['sample_ids'])}")
        print(f"   点云形状: {verify_data['point_clouds'].shape}")
        print(f"   关键点形状: {verify_data['keypoints'].shape}")
        print(f"   热图形状: {verify_data['heatmaps'].shape}")
        
        return {
            'sample_ids': all_sample_ids,
            'point_clouds': all_point_clouds,
            'keypoints': all_keypoints,
            'heatmaps': all_heatmaps,
            'methods': all_methods
        }
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return None

def analyze_augmentation_quality(augmented_data):
    """分析增强质量"""
    if augmented_data is None:
        return
    
    print(f"\n🔍 增强质量分析:")
    print("=" * 40)
    
    # 检查热图归一化
    heatmaps = augmented_data['heatmaps']
    print(f"📊 热图统计:")
    print(f"   热图形状: {heatmaps.shape}")
    
    # 检查前几个样本的热图和
    for i in range(min(3, len(heatmaps))):
        hm_sums = np.sum(heatmaps[i], axis=1)
        print(f"   样本{i}热图和: {hm_sums[:3]}...")  # 显示前3个关键点
    
    # 检查关键点范围
    keypoints = augmented_data['keypoints']
    kp_flat = keypoints.reshape(-1, 3)
    
    print(f"\n🎯 关键点范围:")
    print(f"   X: [{np.min(kp_flat[:, 0]):.2f}, {np.max(kp_flat[:, 0]):.2f}]")
    print(f"   Y: [{np.min(kp_flat[:, 1]):.2f}, {np.max(kp_flat[:, 1]):.2f}]")
    print(f"   Z: [{np.min(kp_flat[:, 2]):.2f}, {np.max(kp_flat[:, 2]):.2f}]")
    
    print(f"\n✅ 质量分析完成!")

def main():
    """主函数"""
    print("🔥 简化版Heatmap数据增强")
    print("🎯 目标: 将25个女性样本增强到250+个")
    print("=" * 80)
    
    # 执行增强
    augmented_data = augment_female_dataset()
    
    if augmented_data:
        # 分析质量
        analyze_augmentation_quality(augmented_data)
        
        print(f"\n🎉 增强完成!")
        print(f"✅ 女性样本从25个增加到{len(augmented_data['sample_ids'])}个")
        print(f"✅ 增强倍数: {len(augmented_data['sample_ids']) / 25:.1f}x")
        print(f"✅ 包含完整的热图数据")
        print(f"✅ 可直接用于Heatmap模型训练")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 使用增强数据集训练Heatmap模型")
        print(f"   2. 预期性能: 4.88mm → 3.5-4.0mm")
        print(f"   3. 对比增强前后效果")
    else:
        print("❌ 增强失败")

if __name__ == "__main__":
    main()
