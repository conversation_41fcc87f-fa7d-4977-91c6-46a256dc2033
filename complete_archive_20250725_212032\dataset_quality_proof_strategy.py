#!/usr/bin/env python3
"""
数据集质量证明策略：多模型优化 + 集成学习 + 后处理
Dataset Quality Proof Strategy: Multi-model Optimization + Ensemble + Post-processing
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import os
from tqdm import tqdm
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import Ridge
import open3d as o3d

class AdvancedPointTransformer(nn.Module):
    """高级Point Transformer模型"""
    
    def __init__(self, num_points=50000, num_keypoints=12, dim=256):
        super(AdvancedPointTransformer, self).__init__()
        
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        self.dim = dim
        
        # 输入嵌入
        self.input_embed = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, dim, 1)
        )
        
        # Point Transformer层
        self.transformer_layers = nn.ModuleList([
            PointTransformerLayer(dim) for _ in range(4)
        ])
        
        # 全局特征聚合
        self.global_pool = nn.AdaptiveMaxPool1d(1)
        
        # 关键点预测头
        self.keypoint_head = nn.Sequential(
            nn.Linear(dim, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, num_keypoints * 3)
        )
        
    def forward(self, x):
        # x: [B, N, 3]
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征嵌入
        x = self.input_embed(x)  # [B, dim, N]
        
        # Point Transformer层
        for layer in self.transformer_layers:
            x = layer(x)
        
        # 全局特征
        global_feat = self.global_pool(x).squeeze(-1)  # [B, dim]
        
        # 关键点预测
        keypoints = self.keypoint_head(global_feat)  # [B, num_keypoints*3]
        keypoints = keypoints.view(-1, self.num_keypoints, 3)
        
        return keypoints

class PointTransformerLayer(nn.Module):
    """Point Transformer层"""
    
    def __init__(self, dim):
        super(PointTransformerLayer, self).__init__()
        self.dim = dim
        
        self.q_conv = nn.Conv1d(dim, dim, 1)
        self.k_conv = nn.Conv1d(dim, dim, 1)
        self.v_conv = nn.Conv1d(dim, dim, 1)
        
        self.pos_mlp = nn.Sequential(
            nn.Conv1d(3, dim, 1),
            nn.ReLU(),
            nn.Conv1d(dim, dim, 1)
        )
        
        self.attn_mlp = nn.Sequential(
            nn.Conv1d(dim, dim, 1),
            nn.ReLU(),
            nn.Conv1d(dim, dim, 1)
        )
        
        self.norm = nn.LayerNorm(dim)
        
    def forward(self, x):
        # x: [B, dim, N]
        B, dim, N = x.shape
        
        # 生成位置编码 (简化版)
        pos = torch.randn(B, 3, N).to(x.device)
        pos_enc = self.pos_mlp(pos)
        
        # 注意力计算
        q = self.q_conv(x)
        k = self.k_conv(x)
        v = self.v_conv(x)
        
        # 简化的注意力机制
        attn = torch.softmax(torch.bmm(q.transpose(1, 2), k) / (dim ** 0.5), dim=-1)
        out = torch.bmm(v, attn.transpose(1, 2))
        
        # 残差连接和归一化
        out = out + x
        out = self.norm(out.transpose(1, 2)).transpose(1, 2)
        
        return out

class HybridRegressionModel(nn.Module):
    """混合回归模型：CNN + Transformer"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super(HybridRegressionModel, self).__init__()
        
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # CNN分支
        self.cnn_branch = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.AdaptiveMaxPool1d(1)
        )
        
        # Transformer分支
        self.transformer_branch = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(d_model=256, nhead=8, batch_first=True),
            num_layers=3
        )
        
        # 特征融合
        self.fusion = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 关键点预测
        self.keypoint_head = nn.Sequential(
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, num_keypoints * 3)
        )
        
    def forward(self, x):
        # x: [B, N, 3]
        B, N, _ = x.shape
        
        # CNN分支
        cnn_feat = self.cnn_branch(x.transpose(2, 1)).squeeze(-1)  # [B, 256]
        
        # Transformer分支 (采样部分点)
        sampled_x = x[:, ::10, :]  # 采样减少计算量
        trans_feat = self.transformer_branch(sampled_x)
        trans_feat = torch.mean(trans_feat, dim=1)  # [B, 256]
        
        # 特征融合
        fused_feat = torch.cat([cnn_feat, trans_feat], dim=1)
        fused_feat = self.fusion(fused_feat)
        
        # 关键点预测
        keypoints = self.keypoint_head(fused_feat)
        keypoints = keypoints.view(B, self.num_keypoints, 3)
        
        return keypoints

class GeometryConstrainedLoss(nn.Module):
    """几何约束损失函数"""
    
    def __init__(self, alpha=1.0, beta=0.1, gamma=0.05):
        super(GeometryConstrainedLoss, self).__init__()
        self.alpha = alpha  # 基础MSE权重
        self.beta = beta    # 距离约束权重
        self.gamma = gamma  # 角度约束权重
        
    def forward(self, pred_kp, target_kp):
        # 基础MSE损失
        mse_loss = F.mse_loss(pred_kp, target_kp)
        
        # 距离约束损失
        dist_loss = self.distance_constraint_loss(pred_kp, target_kp)
        
        # 角度约束损失
        angle_loss = self.angle_constraint_loss(pred_kp, target_kp)
        
        total_loss = self.alpha * mse_loss + self.beta * dist_loss + self.gamma * angle_loss
        
        return total_loss, mse_loss, dist_loss, angle_loss
    
    def distance_constraint_loss(self, pred_kp, target_kp):
        """关键点间距离约束"""
        # 计算预测和真实关键点间的距离矩阵
        pred_dist = torch.cdist(pred_kp, pred_kp)
        target_dist = torch.cdist(target_kp, target_kp)
        
        # 距离一致性损失
        dist_loss = F.mse_loss(pred_dist, target_dist)
        
        return dist_loss
    
    def angle_constraint_loss(self, pred_kp, target_kp):
        """关键点角度约束"""
        # 简化的角度约束：使用前3个点形成的角度
        if pred_kp.shape[1] < 3:
            return torch.tensor(0.0).to(pred_kp.device)
        
        def compute_angle(kp):
            v1 = kp[:, 1] - kp[:, 0]  # 向量1
            v2 = kp[:, 2] - kp[:, 0]  # 向量2
            cos_angle = F.cosine_similarity(v1, v2, dim=1)
            return cos_angle
        
        pred_angle = compute_angle(pred_kp)
        target_angle = compute_angle(target_kp)
        
        angle_loss = F.mse_loss(pred_angle, target_angle)
        
        return angle_loss

def load_clean_data():
    """加载清洁数据"""
    
    print("📊 加载清洁数据...")
    
    # 加载女性原始数据
    female_original_path = "archive/old_experiments/f3_reduced_12kp_female.npz"
    if os.path.exists(female_original_path):
        female_data = np.load(female_original_path, allow_pickle=True)
        female_pc = female_data['point_clouds']
        female_kp = female_data['keypoints']
        print(f"✅ 女性原始数据: {len(female_pc)}个样本")
    else:
        print(f"❌ 女性原始数据不存在")
        return None
    
    # 加载男性原始数据
    male_original_path = "archive/old_experiments/f3_reduced_12kp_male.npz"
    if os.path.exists(male_original_path):
        male_data = np.load(male_original_path, allow_pickle=True)
        male_pc = male_data['point_clouds']
        male_kp = male_data['keypoints']
        print(f"✅ 男性原始数据: {len(male_pc)}个样本")
    else:
        print(f"❌ 男性原始数据不存在")
        return None
    
    return female_pc, female_kp, male_pc, male_kp

def advanced_data_augmentation(pc, kp, target_size=200):
    """高级数据增强"""
    
    augmented_pc = []
    augmented_kp = []
    
    # 保留原始数据
    for i in range(len(pc)):
        augmented_pc.append(pc[i])
        augmented_kp.append(kp[i])
    
    # 生成增强数据
    n_augment_needed = target_size - len(pc)
    
    for i in tqdm(range(n_augment_needed), desc="高级数据增强"):
        idx = np.random.randint(0, len(pc))
        original_pc = pc[idx].copy()
        original_kp = kp[idx].copy()
        
        # 随机选择增强方法
        aug_type = np.random.choice(['rotation', 'noise', 'scale', 'elastic', 'dropout'])
        
        if aug_type == 'rotation':
            # 3D旋转
            angles = np.random.uniform(-5, 5, 3)
            aug_pc, aug_kp = apply_3d_rotation(original_pc, original_kp, angles)
        elif aug_type == 'noise':
            # 高斯噪声
            noise_pc = np.random.normal(0, 0.3, original_pc.shape)
            noise_kp = np.random.normal(0, 0.5, original_kp.shape)
            aug_pc = original_pc + noise_pc
            aug_kp = original_kp + noise_kp
        elif aug_type == 'scale':
            # 尺度变换
            scale_factor = np.random.uniform(0.98, 1.02)
            aug_pc = original_pc * scale_factor
            aug_kp = original_kp * scale_factor
        elif aug_type == 'elastic':
            # 弹性变形
            aug_pc, aug_kp = apply_elastic_deformation(original_pc, original_kp)
        else:  # dropout
            # 点云dropout
            aug_pc, aug_kp = apply_point_dropout(original_pc, original_kp)
        
        augmented_pc.append(aug_pc)
        augmented_kp.append(aug_kp)
    
    return np.array(augmented_pc), np.array(augmented_kp)

def apply_3d_rotation(point_cloud, keypoints, angles_deg):
    """应用3D旋转"""
    angles = np.radians(angles_deg)
    
    # 旋转矩阵
    cos_x, sin_x = np.cos(angles[0]), np.sin(angles[0])
    cos_y, sin_y = np.cos(angles[1]), np.sin(angles[1])
    cos_z, sin_z = np.cos(angles[2]), np.sin(angles[2])
    
    Rx = np.array([[1, 0, 0], [0, cos_x, -sin_x], [0, sin_x, cos_x]])
    Ry = np.array([[cos_y, 0, sin_y], [0, 1, 0], [-sin_y, 0, cos_y]])
    Rz = np.array([[cos_z, -sin_z, 0], [sin_z, cos_z, 0], [0, 0, 1]])
    
    R = Rz @ Ry @ Rx
    
    rotated_pc = point_cloud @ R.T
    rotated_kp = keypoints @ R.T
    
    return rotated_pc, rotated_kp

def apply_elastic_deformation(point_cloud, keypoints, sigma=2.0, alpha=10.0):
    """应用弹性变形"""
    # 简化的弹性变形
    displacement = np.random.normal(0, sigma, point_cloud.shape) * alpha / 1000
    
    deformed_pc = point_cloud + displacement
    
    # 对关键点应用相似的变形
    kp_displacement = np.random.normal(0, sigma/2, keypoints.shape) * alpha / 1000
    deformed_kp = keypoints + kp_displacement
    
    return deformed_pc, deformed_kp

def apply_point_dropout(point_cloud, keypoints, dropout_ratio=0.1):
    """应用点云dropout"""
    n_points = len(point_cloud)
    n_keep = int(n_points * (1 - dropout_ratio))
    
    # 随机选择保留的点
    keep_indices = np.random.choice(n_points, n_keep, replace=False)
    keep_indices = np.sort(keep_indices)
    
    dropped_pc = point_cloud[keep_indices]
    
    # 关键点保持不变
    return dropped_pc, keypoints

def train_advanced_model(train_pc, train_kp, val_pc, val_kp, gender_name, model_type="point_transformer"):
    """训练高级模型"""
    
    print(f"\n🚀 训练{gender_name}高级模型 ({model_type})")
    print("=" * 60)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    
    # 模型初始化
    if model_type == "point_transformer":
        model = AdvancedPointTransformer(num_points=50000, num_keypoints=12)
    else:
        model = HybridRegressionModel(num_points=50000, num_keypoints=12)
    
    model = model.to(device)
    
    print(f"🏗️ {gender_name}高级模型:")
    print(f"   模型类型: {model_type}")
    print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"   训练样本: {len(train_pc)}")
    print(f"   验证样本: {len(val_pc)}")
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=0.0001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=20, T_mult=2)
    criterion = GeometryConstrainedLoss(alpha=1.0, beta=0.1, gamma=0.05)
    
    # 训练参数
    num_epochs = 100
    batch_size = 2  # 减小批次大小适应复杂模型
    best_val_error = float('inf')
    patience = 30
    patience_counter = 0
    
    print(f"🎯 训练参数:")
    print(f"   训练轮数: {num_epochs}")
    print(f"   批次大小: {batch_size}")
    print(f"   优化器: AdamW + CosineAnnealingWarmRestarts")
    print(f"   损失函数: 几何约束损失")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_losses = []
        
        n_train_batches = len(train_pc) // batch_size
        
        for i in range(n_train_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(train_pc))
            
            batch_pc = torch.FloatTensor(train_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(train_kp[start_idx:end_idx]).to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            pred_kp = model(batch_pc)
            total_loss, mse_loss, dist_loss, angle_loss = criterion(pred_kp, batch_kp)
            
            # 反向传播
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_losses.append(total_loss.item())
        
        avg_train_loss = np.mean(train_losses)
        
        # 验证阶段
        model.eval()
        val_errors = []
        
        with torch.no_grad():
            n_val_batches = len(val_pc) // batch_size + (1 if len(val_pc) % batch_size > 0 else 0)
            
            for i in range(n_val_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(val_pc))
                
                batch_pc = torch.FloatTensor(val_pc[start_idx:end_idx]).to(device)
                batch_kp = torch.FloatTensor(val_kp[start_idx:end_idx]).to(device)
                
                pred_kp = model(batch_pc)
                
                for j in range(len(batch_kp)):
                    error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j], dim=1))
                    val_errors.append(error.item())
        
        avg_val_error = np.mean(val_errors)
        
        # 学习率调度
        scheduler.step()
        
        if epoch % 10 == 0:
            print(f"Epoch {epoch+1}/{num_epochs}: Loss={avg_train_loss:.4f}, Val={avg_val_error:.2f}mm")
        
        # 早停和模型保存
        if avg_val_error < best_val_error:
            best_val_error = avg_val_error
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'val_error': avg_val_error,
                'model_type': model_type
            }, f'advanced_{gender_name}_{model_type}.pth')
        else:
            patience_counter += 1
            if patience_counter >= patience:
                break
    
    print(f"✅ {gender_name}高级模型训练完成，最佳验证误差: {best_val_error:.2f}mm")
    
    return model, best_val_error

def geometric_post_processing(pred_keypoints, point_cloud):
    """几何后处理优化"""
    
    # 1. 表面投影约束
    projected_kp = project_to_surface(pred_keypoints, point_cloud)
    
    # 2. 解剖学约束
    constrained_kp = apply_anatomical_constraints(projected_kp)
    
    # 3. 平滑化处理
    smoothed_kp = smooth_keypoints(constrained_kp)
    
    return smoothed_kp

def project_to_surface(keypoints, point_cloud, threshold=2.0):
    """将关键点投影到最近的表面点"""
    
    projected_kp = []
    
    for kp in keypoints:
        # 计算到所有点的距离
        distances = np.linalg.norm(point_cloud - kp, axis=1)
        
        # 找到最近的点
        nearest_idx = np.argmin(distances)
        nearest_point = point_cloud[nearest_idx]
        
        # 如果距离太远，保持原位置；否则投影到表面
        if distances[nearest_idx] < threshold:
            projected_kp.append(nearest_point)
        else:
            projected_kp.append(kp)
    
    return np.array(projected_kp)

def apply_anatomical_constraints(keypoints):
    """应用解剖学约束"""
    
    # 简化的解剖学约束：确保关键点间的相对位置合理
    constrained_kp = keypoints.copy()
    
    # 例如：确保某些关键点的Z坐标顺序
    # 这里可以根据具体的解剖学知识添加约束
    
    return constrained_kp

def smooth_keypoints(keypoints, alpha=0.1):
    """关键点平滑化"""
    
    # 简单的移动平均平滑
    smoothed_kp = keypoints.copy()
    
    for i in range(1, len(keypoints) - 1):
        smoothed_kp[i] = (1 - 2*alpha) * keypoints[i] + alpha * (keypoints[i-1] + keypoints[i+1])
    
    return smoothed_kp

def test_advanced_ensemble(models, test_pc, test_kp, gender_name):
    """测试高级集成模型"""
    
    print(f"\n🧪 测试{gender_name}高级集成模型")
    print("=" * 60)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    
    # 将所有模型设置为评估模式
    for model in models:
        model.to(device)
        model.eval()
    
    test_errors = []
    individual_errors = [[] for _ in range(len(models))]
    
    batch_size = 2
    with torch.no_grad():
        n_test_batches = len(test_pc) // batch_size + (1 if len(test_pc) % batch_size > 0 else 0)
        
        for i in range(n_test_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(test_pc))
            
            batch_pc = torch.FloatTensor(test_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(test_kp[start_idx:end_idx]).to(device)
            
            # 获取所有模型的预测
            ensemble_predictions = []
            for model_idx, model in enumerate(models):
                pred_kp = model(batch_pc)
                ensemble_predictions.append(pred_kp.cpu().numpy())
                
                # 计算单个模型误差
                for j in range(len(batch_kp)):
                    error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j], dim=1))
                    individual_errors[model_idx].append(error.item())
            
            # 集成预测 (加权平均)
            weights = [0.4, 0.3, 0.3]  # 可以根据验证性能调整权重
            ensemble_pred = np.average(ensemble_predictions, axis=0, weights=weights)
            
            # 几何后处理
            for j in range(len(batch_kp)):
                processed_kp = geometric_post_processing(
                    ensemble_pred[j], batch_pc[j].cpu().numpy())
                
                error = np.mean(np.linalg.norm(
                    processed_kp - batch_kp[j].cpu().numpy(), axis=1))
                test_errors.append(error)
    
    ensemble_error = np.mean(test_errors)
    individual_avg_errors = [np.mean(errors) for errors in individual_errors]
    
    print(f"📊 {gender_name}高级集成模型测试结果:")
    print(f"   测试样本数: {len(test_pc)}")
    print(f"   集成模型误差: {ensemble_error:.2f}mm")
    print(f"   单个模型误差:")
    for i, error in enumerate(individual_avg_errors):
        print(f"     高级模型 #{i+1}: {error:.2f}mm")
    print(f"   集成改进: {np.mean(individual_avg_errors) - ensemble_error:.2f}mm")
    print(f"   医疗级状态: {'✅ 达标' if ensemble_error < 5.0 else '❌ 未达标'}")
    
    return ensemble_error, individual_avg_errors

def main():
    """主函数：数据集质量证明"""
    
    print("🎯 数据集质量证明：高级模型优化策略")
    print("🚀 目标: 证明数据集质量，实现<5mm医疗级精度")
    print("🔧 策略: 高级模型 + 集成学习 + 几何后处理")
    print("=" * 80)
    
    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 加载数据
    data_result = load_clean_data()
    if data_result is None:
        return
    
    female_pc, female_kp, male_pc, male_kp = data_result
    
    results = {}
    
    # 处理女性数据
    print(f"\n" + "="*80)
    print("👩 女性数据：高级模型优化")
    print("="*80)
    
    # 数据预处理和分割
    from dataset_paper_optimization import analyze_data_quality, remove_outliers, split_clean_data
    
    female_quality = analyze_data_quality(female_pc, female_kp, "女性")
    female_clean_pc, female_clean_kp, _ = remove_outliers(
        female_pc, female_kp, female_quality, removal_ratio=0.03)
    
    (female_train_pc, female_train_kp), (female_val_pc, female_val_kp), (female_test_pc, female_test_kp) = \
        split_clean_data(female_clean_pc, female_clean_kp, "女性")
    
    # 高级数据增强
    female_aug_train_pc, female_aug_train_kp = advanced_data_augmentation(
        female_train_pc, female_train_kp, target_size=200)
    
    # 训练多个高级模型
    female_models = []
    female_val_errors = []
    
    model_types = ["point_transformer", "hybrid", "point_transformer"]
    for i, model_type in enumerate(model_types):
        model, val_error = train_advanced_model(
            female_aug_train_pc, female_aug_train_kp,
            female_val_pc, female_val_kp, "女性", model_type)
        female_models.append(model)
        female_val_errors.append(val_error)
    
    # 测试女性高级集成模型
    female_ensemble_error, female_individual_errors = test_advanced_ensemble(
        female_models, female_test_pc, female_test_kp, "女性")
    
    results['female'] = {
        'ensemble_error': female_ensemble_error,
        'individual_errors': female_individual_errors,
        'val_errors': female_val_errors,
        'test_samples': len(female_test_pc)
    }
    
    # 处理男性数据
    print(f"\n" + "="*80)
    print("👨 男性数据：高级模型优化")
    print("="*80)
    
    male_quality = analyze_data_quality(male_pc, male_kp, "男性")
    male_clean_pc, male_clean_kp, _ = remove_outliers(
        male_pc, male_kp, male_quality, removal_ratio=0.03)
    
    (male_train_pc, male_train_kp), (male_val_pc, male_val_kp), (male_test_pc, male_test_kp) = \
        split_clean_data(male_clean_pc, male_clean_kp, "男性")
    
    # 高级数据增强
    male_aug_train_pc, male_aug_train_kp = advanced_data_augmentation(
        male_train_pc, male_train_kp, target_size=600)
    
    # 训练多个高级模型
    male_models = []
    male_val_errors = []
    
    for i, model_type in enumerate(model_types):
        model, val_error = train_advanced_model(
            male_aug_train_pc, male_aug_train_kp,
            male_val_pc, male_val_kp, "男性", model_type)
        male_models.append(model)
        male_val_errors.append(val_error)
    
    # 测试男性高级集成模型
    male_ensemble_error, male_individual_errors = test_advanced_ensemble(
        male_models, male_test_pc, male_test_kp, "男性")
    
    results['male'] = {
        'ensemble_error': male_ensemble_error,
        'individual_errors': male_individual_errors,
        'val_errors': male_val_errors,
        'test_samples': len(male_test_pc)
    }
    
    # 总结结果
    print(f"\n" + "="*80)
    print("🎉 数据集质量证明结果总结")
    print("="*80)
    
    print(f"📊 女性高级集成模型:")
    print(f"   集成模型误差: {results['female']['ensemble_error']:.2f}mm")
    print(f"   单个模型误差: {', '.join([f'{e:.2f}mm' for e in results['female']['individual_errors']])}")
    print(f"   测试样本: {results['female']['test_samples']}个")
    
    print(f"\n📊 男性高级集成模型:")
    print(f"   集成模型误差: {results['male']['ensemble_error']:.2f}mm")
    print(f"   单个模型误差: {', '.join([f'{e:.2f}mm' for e in results['male']['individual_errors']])}")
    print(f"   测试样本: {results['male']['test_samples']}个")
    
    # 数据集质量评估
    avg_error = (results['female']['ensemble_error'] + results['male']['ensemble_error']) / 2
    both_medical_grade = results['female']['ensemble_error'] < 5.0 and results['male']['ensemble_error'] < 5.0
    
    print(f"\n🎯 数据集质量证明:")
    print(f"   平均测试误差: {avg_error:.2f}mm")
    print(f"   医疗级精度: {'✅ 整体达标' if both_medical_grade else '❌ 部分达标'}")
    print(f"   数据集质量: {'✅ 高质量，适合发表' if both_medical_grade else '⚠️ 需要进一步优化'}")
    
    if both_medical_grade:
        print(f"\n🎉 数据集质量证明成功!")
        print(f"   ✅ 女性模型: {results['female']['ensemble_error']:.2f}mm < 5mm")
        print(f"   ✅ 男性模型: {results['male']['ensemble_error']:.2f}mm < 5mm")
        print(f"   ✅ 数据集具有良好的训练能力，支撑高质量论文发表")
    else:
        print(f"\n📈 数据集质量需要进一步优化")
        print(f"   建议: 增加数据样本、改进标注质量、优化数据处理流程")
    
    return results

if __name__ == "__main__":
    main()
