from ultralytics import YOLO
import os

# 确保使用绝对路径
current_dir = os.path.dirname(os.path.abspath(__file__))
data_yaml = os.path.join(current_dir, 'dataset', 'data.yaml')

# 加载预训练模型
model = YOLO('yolov8n.pt')  # 使用yolov8-nano模型

# 开始训练
results = model.train(
    data=data_yaml,        # 使用绝对路径
    epochs=100,            # 训练轮数
    imgsz=640,            # 图像大小
    batch=16,             # batch大小
    name='crossing_points' # 实验名称
)

# 加载训练好的模型
model = YOLO('runs/detect/crossing_points/weights/best.pt')

# 预测单张图像
results = model('fingerprint.png')

# 可视化结果
results[0].plot()
