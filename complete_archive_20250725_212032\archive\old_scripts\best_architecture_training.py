#!/usr/bin/env python3
"""
最佳架构训练方案
在现有数据上测试多种先进的点云架构，找出最适合医学关键点检测的模型
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import time
import os
from datetime import datetime

class MedicalKeypointDataset(Dataset):
    """医学关键点数据集"""
    
    def __init__(self, point_clouds, keypoints, sample_ids, augment=False):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.sample_ids = sample_ids
        self.augment = augment
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        pc = self.point_clouds[idx].copy()
        kp = self.keypoints[idx].copy()
        
        if self.augment:
            # 数据增强
            pc, kp = self.apply_augmentation(pc, kp)
        
        # 转换为tensor
        pc = torch.FloatTensor(pc).transpose(0, 1)  # [3, N]
        kp = torch.FloatTensor(kp.reshape(-1))  # [36] (12*3)
        
        return pc, kp, self.sample_ids[idx]
    
    def apply_augmentation(self, pc, kp):
        """应用数据增强"""
        # 随机旋转 (小角度)
        if np.random.random() > 0.5:
            angle = np.random.uniform(-5, 5) * np.pi / 180  # ±5度
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            pc = pc @ rotation_matrix.T
            kp = kp @ rotation_matrix.T
        
        # 随机缩放 (小幅度)
        if np.random.random() > 0.5:
            scale = np.random.uniform(0.95, 1.05)
            pc *= scale
            kp *= scale
        
        # 随机噪声 (很小)
        if np.random.random() > 0.5:
            noise_std = np.std(pc) * 0.01  # 1%的噪声
            pc += np.random.normal(0, noise_std, pc.shape)
        
        return pc, kp

class PointNet(nn.Module):
    """PointNet架构"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super(PointNet, self).__init__()
        
        # 特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(1024)
        
        # 回归头
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
    
    def forward(self, x):
        # x: [B, 3, N]
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.relu(self.bn2(self.conv2(x)))
        x = self.bn3(self.conv3(x))
        
        # 全局最大池化
        x = torch.max(x, 2)[0]  # [B, 1024]
        
        # 回归
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x

class PointNetPlusPlus(nn.Module):
    """简化的PointNet++架构"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super(PointNetPlusPlus, self).__init__()
        
        # 多尺度特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(512, 8, batch_first=True)
        
        # 回归头
        self.fc1 = nn.Linear(512, 256)
        self.fc2 = nn.Linear(256, 128)
        self.fc3 = nn.Linear(128, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.4)
        self.relu = nn.ReLU()
    
    def forward(self, x):
        # x: [B, 3, N]
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.relu(self.bn2(self.conv2(x)))
        x = self.relu(self.bn3(self.conv3(x)))
        x = self.relu(self.bn4(self.conv4(x)))
        
        # 注意力机制
        x = x.transpose(1, 2)  # [B, N, 512]
        x, _ = self.attention(x, x, x)
        
        # 全局平均池化 + 最大池化
        x_avg = torch.mean(x, dim=1)  # [B, 512]
        x_max = torch.max(x, dim=1)[0]  # [B, 512]
        x = x_avg + x_max
        
        # 回归
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x

class DGCNN(nn.Module):
    """动态图卷积网络"""
    
    def __init__(self, num_points=50000, num_keypoints=12, k=20):
        super(DGCNN, self).__init__()
        self.k = k
        
        # EdgeConv层
        self.conv1 = nn.Conv2d(6, 64, kernel_size=1, bias=False)
        self.conv2 = nn.Conv2d(128, 128, kernel_size=1, bias=False)
        self.conv3 = nn.Conv2d(256, 256, kernel_size=1, bias=False)
        
        self.bn1 = nn.BatchNorm2d(64)
        self.bn2 = nn.BatchNorm2d(128)
        self.bn3 = nn.BatchNorm2d(256)
        
        # 全局特征
        self.conv4 = nn.Conv1d(448, 1024, kernel_size=1, bias=False)
        self.bn4 = nn.BatchNorm1d(1024)
        
        # 回归头
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.5)
        self.relu = nn.ReLU()
    
    def knn(self, x, k):
        """K近邻搜索"""
        inner = -2 * torch.matmul(x.transpose(2, 1), x)
        xx = torch.sum(x**2, dim=1, keepdim=True)
        pairwise_distance = -xx - inner - xx.transpose(2, 1)
        idx = pairwise_distance.topk(k=k, dim=-1)[1]
        return idx
    
    def get_graph_feature(self, x, k, idx=None):
        """构建图特征"""
        batch_size = x.size(0)
        num_points = x.size(2)
        x = x.view(batch_size, -1, num_points)
        
        if idx is None:
            idx = self.knn(x, k=k)
        
        device = torch.device('cuda' if x.is_cuda else 'cpu')
        idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1) * num_points
        idx = idx + idx_base
        idx = idx.view(-1)
        
        _, num_dims, _ = x.size()
        x = x.transpose(2, 1).contiguous()
        feature = x.view(batch_size * num_points, -1)[idx, :]
        feature = feature.view(batch_size, num_points, k, num_dims)
        x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)
        
        feature = torch.cat((feature - x, x), dim=3).permute(0, 3, 1, 2).contiguous()
        return feature
    
    def forward(self, x):
        batch_size = x.size(0)
        
        # EdgeConv 1
        x1 = self.get_graph_feature(x, k=self.k)
        x1 = self.relu(self.bn1(self.conv1(x1)))
        x1 = x1.max(dim=-1, keepdim=False)[0]
        
        # EdgeConv 2
        x2 = self.get_graph_feature(x1, k=self.k)
        x2 = self.relu(self.bn2(self.conv2(x2)))
        x2 = x2.max(dim=-1, keepdim=False)[0]
        
        # EdgeConv 3
        x3 = self.get_graph_feature(x2, k=self.k)
        x3 = self.relu(self.bn3(self.conv3(x3)))
        x3 = x3.max(dim=-1, keepdim=False)[0]
        
        # 特征融合
        x = torch.cat((x1, x2, x3), dim=1)
        x = self.relu(self.bn4(self.conv4(x)))
        
        # 全局特征
        x = torch.max(x, 2)[0]
        
        # 回归
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x

def load_datasets():
    """加载数据集"""
    print("📊 **加载数据集**")
    
    # 加载完整数据集
    full_data = np.load('f3_reduced_12kp_stable.npz', allow_pickle=True)
    
    # 加载性别分离数据集
    female_data = np.load('f3_reduced_12kp_female.npz', allow_pickle=True)
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    
    datasets = {
        'full': {
            'sample_ids': full_data['sample_ids'],
            'point_clouds': full_data['point_clouds'],
            'keypoints': full_data['keypoints']
        },
        'female': {
            'sample_ids': female_data['sample_ids'],
            'point_clouds': female_data['point_clouds'],
            'keypoints': female_data['keypoints']
        },
        'male': {
            'sample_ids': male_data['sample_ids'],
            'point_clouds': male_data['point_clouds'],
            'keypoints': male_data['keypoints']
        }
    }
    
    print(f"   完整数据集: {len(datasets['full']['sample_ids'])}个样本")
    print(f"   女性数据集: {len(datasets['female']['sample_ids'])}个样本")
    print(f"   男性数据集: {len(datasets['male']['sample_ids'])}个样本")
    
    return datasets

def create_data_splits(datasets):
    """创建训练/验证/测试分割"""
    print(f"\n📋 **创建数据分割**")
    
    splits = {}
    
    for dataset_name, data in datasets.items():
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        n_samples = len(sample_ids)
        
        if n_samples < 10:
            print(f"   {dataset_name}: 样本太少({n_samples})，跳过")
            continue
        
        # 分割比例调整 (小数据集用更多训练数据)
        if n_samples < 30:
            train_ratio, val_ratio = 0.8, 0.2
            test_ratio = 0.0
        else:
            train_ratio, val_ratio, test_ratio = 0.7, 0.15, 0.15
        
        # 第一次分割: 训练 vs (验证+测试)
        train_idx, temp_idx = train_test_split(
            range(n_samples), 
            test_size=(val_ratio + test_ratio),
            random_state=42,
            shuffle=True
        )
        
        if test_ratio > 0:
            # 第二次分割: 验证 vs 测试
            val_idx, test_idx = train_test_split(
                temp_idx,
                test_size=test_ratio/(val_ratio + test_ratio),
                random_state=42,
                shuffle=True
            )
        else:
            val_idx = temp_idx
            test_idx = []
        
        splits[dataset_name] = {
            'train': {
                'sample_ids': sample_ids[train_idx],
                'point_clouds': point_clouds[train_idx],
                'keypoints': keypoints[train_idx]
            },
            'val': {
                'sample_ids': sample_ids[val_idx],
                'point_clouds': point_clouds[val_idx],
                'keypoints': keypoints[val_idx]
            }
        }
        
        if test_idx:
            splits[dataset_name]['test'] = {
                'sample_ids': sample_ids[test_idx],
                'point_clouds': point_clouds[test_idx],
                'keypoints': keypoints[test_idx]
            }
        
        print(f"   {dataset_name}: 训练{len(train_idx)}, 验证{len(val_idx)}, 测试{len(test_idx)}")
    
    return splits

def train_model(model, train_loader, val_loader, model_name, dataset_name, epochs=100):
    """训练模型"""
    print(f"\n🚀 **训练{model_name} - {dataset_name}数据集**")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    criterion = nn.MSELoss()
    
    # 训练历史
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    patience_counter = 0
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        
        for batch_idx, (pc, kp, _) in enumerate(train_loader):
            pc, kp = pc.to(device), kp.to(device)
            
            optimizer.zero_grad()
            pred = model(pc)
            loss = criterion(pred, kp)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for pc, kp, _ in val_loader:
                pc, kp = pc.to(device), kp.to(device)
                pred = model(pc)
                loss = criterion(pred, kp)
                val_loss += loss.item()
        
        val_loss /= len(val_loader)
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            # 保存最佳模型
            torch.save(model.state_dict(), f'best_{model_name}_{dataset_name}.pth')
        else:
            patience_counter += 1
        
        if epoch % 10 == 0:
            print(f"   Epoch {epoch}: Train Loss={train_loss:.6f}, Val Loss={val_loss:.6f}")
        
        # 早停
        if patience_counter >= 20:
            print(f"   早停于epoch {epoch}")
            break
    
    return train_losses, val_losses, best_val_loss

def evaluate_model(model, test_loader, model_name, dataset_name):
    """评估模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.eval()
    
    total_error = 0.0
    total_samples = 0
    errors = []
    
    with torch.no_grad():
        for pc, kp_true, sample_ids in test_loader:
            pc, kp_true = pc.to(device), kp_true.to(device)
            kp_pred = model(pc)
            
            # 计算每个样本的平均误差
            batch_size = pc.size(0)
            kp_true = kp_true.view(batch_size, -1, 3)
            kp_pred = kp_pred.view(batch_size, -1, 3)
            
            for i in range(batch_size):
                sample_error = torch.mean(torch.norm(kp_pred[i] - kp_true[i], dim=1)).item()
                errors.append(sample_error)
                total_error += sample_error
                total_samples += 1
    
    avg_error = total_error / total_samples if total_samples > 0 else 0
    
    print(f"   {model_name} - {dataset_name}: 平均误差 {avg_error:.2f}mm")
    
    return avg_error, errors

def main():
    """主函数"""
    print("🎯 **最佳架构训练实验**")
    print("在现有数据上测试多种先进架构")
    print("=" * 80)
    
    # 加载数据集
    datasets = load_datasets()
    
    # 创建数据分割
    splits = create_data_splits(datasets)
    
    # 定义模型架构
    models = {
        'PointNet': PointNet,
        'PointNet++': PointNetPlusPlus,
        'DGCNN': DGCNN
    }
    
    # 训练配置
    batch_size = 4  # 小批次适应小数据集
    epochs = 150
    
    results = {}
    
    # 对每个数据集和每个模型进行训练
    for dataset_name, split_data in splits.items():
        print(f"\n{'='*20} {dataset_name.upper()} 数据集 {'='*20}")
        
        # 创建数据加载器
        train_dataset = MedicalKeypointDataset(
            split_data['train']['point_clouds'],
            split_data['train']['keypoints'],
            split_data['train']['sample_ids'],
            augment=True
        )
        
        val_dataset = MedicalKeypointDataset(
            split_data['val']['point_clouds'],
            split_data['val']['keypoints'],
            split_data['val']['sample_ids'],
            augment=False
        )
        
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        
        results[dataset_name] = {}
        
        # 训练每个模型
        for model_name, model_class in models.items():
            print(f"\n--- 训练 {model_name} ---")
            
            try:
                model = model_class()
                train_losses, val_losses, best_val_loss = train_model(
                    model, train_loader, val_loader, model_name, dataset_name, epochs
                )
                
                # 在验证集上评估
                model.load_state_dict(torch.load(f'best_{model_name}_{dataset_name}.pth'))
                avg_error, errors = evaluate_model(model, val_loader, model_name, dataset_name)
                
                results[dataset_name][model_name] = {
                    'val_loss': best_val_loss,
                    'avg_error': avg_error,
                    'train_losses': train_losses,
                    'val_losses': val_losses
                }
                
            except Exception as e:
                print(f"   训练{model_name}失败: {e}")
                continue
    
    # 总结结果
    print(f"\n🏆 **训练结果总结**")
    print("=" * 80)
    
    for dataset_name, dataset_results in results.items():
        print(f"\n📊 **{dataset_name.upper()}数据集结果**:")
        print(f"{'模型':<15} {'验证损失':<12} {'平均误差(mm)':<15}")
        print("-" * 45)
        
        for model_name, metrics in dataset_results.items():
            print(f"{model_name:<15} {metrics['val_loss']:<12.6f} {metrics['avg_error']:<15.2f}")
    
    # 找出最佳模型
    print(f"\n🎯 **最佳模型推荐**:")
    
    for dataset_name, dataset_results in results.items():
        if dataset_results:
            best_model = min(dataset_results.items(), key=lambda x: x[1]['avg_error'])
            print(f"   {dataset_name}数据集: {best_model[0]} (误差: {best_model[1]['avg_error']:.2f}mm)")
    
    print(f"\n💡 **训练建议**:")
    print(f"   • 当前数据量较小，模型可能过拟合")
    print(f"   • 女性数据集样本太少，结果可能不可靠")
    print(f"   • 建议收集更多数据后重新训练")
    print(f"   • 可以使用当前最佳架构作为基准")

if __name__ == "__main__":
    main()
