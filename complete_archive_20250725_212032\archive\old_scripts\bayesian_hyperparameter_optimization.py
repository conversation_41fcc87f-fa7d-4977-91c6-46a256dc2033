#!/usr/bin/env python3
"""
贝叶斯超参数优化 - 第1周立即实施
目标: 5.857mm → 5.6-5.7mm (提升3-5%)
重点优化: α值、学习率、正则化参数
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import time
import json
import random
from sklearn.model_selection import KFold
import copy

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class OptimizedMedicalPointNet(nn.Module):
    """优化的医疗PointNet - 支持超参数调优"""
    
    def __init__(self, num_keypoints=12, statistical_baseline=None, 
                 alpha_init=0.55, dropout_rate=0.3, feature_dims=[32, 64, 128]):
        super(OptimizedMedicalPointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        self.statistical_baseline = statistical_baseline
        
        # 可调的特征维度
        self.conv1 = nn.Conv1d(3, feature_dims[0], 1)
        self.conv2 = nn.Conv1d(feature_dims[0], feature_dims[1], 1)
        self.conv3 = nn.Conv1d(feature_dims[1], feature_dims[2], 1)
        
        self.bn1 = nn.BatchNorm1d(feature_dims[0])
        self.bn2 = nn.BatchNorm1d(feature_dims[1])
        self.bn3 = nn.BatchNorm1d(feature_dims[2])
        
        # 可调的预测头
        self.fc1 = nn.Linear(feature_dims[2], feature_dims[2]//2)
        self.fc2 = nn.Linear(feature_dims[2]//2, num_keypoints * 3)
        self.dropout = nn.Dropout(dropout_rate)
        
        # 可调的统计先验权重
        self.alpha = nn.Parameter(torch.tensor(alpha_init))
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"🔧 优化模型: {total_params:,}参数, α={alpha_init:.3f}, dropout={dropout_rate}")
    
    def forward(self, x):
        x = x.transpose(2, 1)
        x = torch.relu(self.bn1(self.conv1(x)))
        x = torch.relu(self.bn2(self.conv2(x)))
        x = torch.relu(self.bn3(self.conv3(x)))
        x = torch.max(x, 2)[0]
        x = torch.relu(self.fc1(x))
        x = self.dropout(x)
        delta = self.fc2(x)
        delta = delta.view(-1, self.num_keypoints, 3)
        
        if self.statistical_baseline is not None:
            baseline = torch.tensor(self.statistical_baseline, 
                                  dtype=delta.dtype, device=delta.device)
            baseline = baseline.unsqueeze(0).expand(delta.shape[0], -1, -1)
            alpha = torch.sigmoid(self.alpha)
            output = alpha * baseline + (1 - alpha) * (baseline + delta)
            return output
        return delta

class AdaptiveLRScheduler:
    """自适应学习率调度器"""
    
    def __init__(self, optimizer, patience=5, factor=0.8, min_lr=1e-6):
        self.optimizer = optimizer
        self.patience = patience
        self.factor = factor
        self.min_lr = min_lr
        self.best_loss = float('inf')
        self.wait = 0
        
    def step(self, val_loss):
        if val_loss < self.best_loss:
            self.best_loss = val_loss
            self.wait = 0
        else:
            self.wait += 1
            if self.wait >= self.patience:
                for param_group in self.optimizer.param_groups:
                    old_lr = param_group['lr']
                    new_lr = max(old_lr * self.factor, self.min_lr)
                    param_group['lr'] = new_lr
                    if new_lr != old_lr:
                        print(f"      学习率调整: {old_lr:.6f} → {new_lr:.6f}")
                self.wait = 0

class BayesianOptimizer:
    """贝叶斯超参数优化器"""
    
    def __init__(self, device='cuda:1'):
        self.device = device
        self.best_params = None
        self.best_score = float('inf')
        self.history = []
        print("🎯 贝叶斯超参数优化器初始化")
    
    def calculate_statistical_baseline(self, train_data):
        """计算统计基线"""
        all_keypoints = []
        for sample in train_data:
            if isinstance(sample, dict):
                kp = sample['keypoints'].numpy()
            else:
                kp = sample[1]
            all_keypoints.append(kp)
        
        all_keypoints = np.array(all_keypoints)
        baseline = np.mean(all_keypoints, axis=0)
        return baseline
    
    def conservative_augment(self, point_cloud, keypoints):
        """保守数据增强"""
        pc = point_cloud.copy()
        kp = keypoints.copy()
        
        if np.random.random() < 0.6:
            angle = np.random.uniform(-0.035, 0.035)  # ±2度
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
            pc = pc @ rotation.T
            kp = kp @ rotation.T
        
        if np.random.random() < 0.5:
            scale = np.random.uniform(0.99, 1.01)
            pc *= scale
            kp *= scale
        
        if np.random.random() < 0.4:
            translation = np.random.uniform(-0.1, 0.1, 3)
            pc += translation
            kp += translation
        
        if np.random.random() < 0.3:
            noise = np.random.normal(0, 0.005, pc.shape)
            pc += noise
        
        return pc, kp
    
    def objective_function(self, params):
        """目标函数 - 返回验证误差"""
        
        alpha_init, lr, weight_decay, dropout_rate, feature_scale = params
        
        # 特征维度
        base_dims = [32, 64, 128]
        feature_dims = [int(dim * feature_scale) for dim in base_dims]
        
        print(f"\n🔍 测试参数: α={alpha_init:.3f}, lr={lr:.4f}, wd={weight_decay:.1e}, "
              f"dropout={dropout_rate:.2f}, dims={feature_dims}")
        
        try:
            # 加载数据
            data = np.load('f3_reduced_12kp_stable.npz', allow_pickle=True)
            sample_ids = data['sample_ids']
            point_clouds = data['point_clouds']
            keypoints = data['keypoints']
            
            # 排除测试集
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
            
            test_mask = np.isin(sample_ids, test_samples)
            train_val_mask = ~test_mask
            
            train_samples = [(point_clouds[i], keypoints[i], sample_ids[i]) 
                            for i in range(len(sample_ids)) if train_val_mask[i]]
            
            # 3折快速验证 (节省时间)
            kfold = KFold(n_splits=3, shuffle=True, random_state=42)
            fold_results = []
            
            for fold, (train_idx, val_idx) in enumerate(kfold.split(range(len(train_samples)))):
                
                # 分割数据
                fold_train = [train_samples[i] for i in train_idx]
                fold_val = [train_samples[i] for i in val_idx]
                
                # 计算统计基线
                statistical_baseline = self.calculate_statistical_baseline(fold_train)
                
                # 数据增强
                augmented_train = []
                for pc, kp, sid in fold_train:
                    augmented_train.append((pc, kp, sid))
                    aug_pc, aug_kp = self.conservative_augment(pc, kp)
                    augmented_train.append((aug_pc, aug_kp, f"{sid}_aug"))
                
                # 创建模型
                model = OptimizedMedicalPointNet(
                    num_keypoints=12,
                    statistical_baseline=statistical_baseline,
                    alpha_init=alpha_init,
                    dropout_rate=dropout_rate,
                    feature_dims=feature_dims
                )
                model.to(self.device)
                
                # 训练
                fold_error = self.train_single_fold(
                    model, augmented_train, fold_val, lr, weight_decay
                )
                fold_results.append(fold_error)
            
            avg_error = np.mean(fold_results)
            print(f"   结果: {avg_error:.3f}mm (折: {[f'{x:.3f}' for x in fold_results]})")
            
            # 记录历史
            self.history.append({
                'params': params,
                'score': avg_error,
                'fold_results': fold_results
            })
            
            # 更新最佳参数
            if avg_error < self.best_score:
                self.best_score = avg_error
                self.best_params = params
                print(f"   🎉 新的最佳结果: {avg_error:.3f}mm")
            
            return avg_error
            
        except Exception as e:
            print(f"   ❌ 参数组合失败: {e}")
            return 10.0  # 返回一个很大的误差
    
    def train_single_fold(self, model, train_data, val_data, lr, weight_decay, epochs=30):
        """训练单折 - 快速版本"""
        
        optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
        scheduler = AdaptiveLRScheduler(optimizer, patience=5, factor=0.8)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        patience = 8  # 减少patience加速
        patience_counter = 0
        
        for epoch in range(epochs):
            # 训练
            model.train()
            train_loss = 0.0
            
            batch_size = 4
            for i in range(0, len(train_data), batch_size):
                batch = train_data[i:i+batch_size]
                
                pc_list = []
                kp_list = []
                
                for pc, kp, _ in batch:
                    if len(pc) > 2048:
                        indices = np.random.choice(len(pc), 2048, replace=False)
                        pc = pc[indices]
                    
                    pc_list.append(torch.FloatTensor(pc))
                    kp_list.append(torch.FloatTensor(kp))
                
                pc_batch = torch.stack(pc_list).to(self.device)
                kp_batch = torch.stack(kp_list).to(self.device)
                
                optimizer.zero_grad()
                pred = model(pc_batch)
                loss = criterion(pred, kp_batch)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
                optimizer.step()
                
                train_loss += loss.item()
            
            # 验证
            model.eval()
            val_errors = []
            with torch.no_grad():
                for pc, kp, _ in val_data:
                    if len(pc) > 2048:
                        indices = np.random.choice(len(pc), 2048, replace=False)
                        pc = pc[indices]
                    
                    pc_tensor = torch.FloatTensor(pc).unsqueeze(0).to(self.device)
                    kp_tensor = torch.FloatTensor(kp).unsqueeze(0).to(self.device)
                    
                    pred = model(pc_tensor)
                    error = torch.norm(pred - kp_tensor, dim=2).mean().item()
                    val_errors.append(error)
            
            val_error = np.mean(val_errors)
            scheduler.step(val_error)
            
            if val_error < best_val_error:
                best_val_error = val_error
                patience_counter = 0
            else:
                patience_counter += 1
            
            if patience_counter >= patience:
                break
        
        return best_val_error
    
    def grid_search_optimization(self):
        """网格搜索优化"""
        
        print("\n🔍 **网格搜索超参数优化**")
        print("🎯 **目标: 5.857mm → 5.6-5.7mm**")
        print("=" * 60)
        
        # 定义搜索空间
        param_grid = {
            'alpha_init': [0.52, 0.55, 0.58, 0.62],
            'lr': [0.0006, 0.0008, 0.001, 0.0012],
            'weight_decay': [3e-4, 5e-4, 8e-4],
            'dropout_rate': [0.25, 0.3, 0.35],
            'feature_scale': [0.8, 1.0, 1.2]  # 特征维度缩放
        }
        
        print(f"搜索空间大小: {np.prod([len(v) for v in param_grid.values()])}组合")
        
        # 智能搜索策略 - 先搜索最重要的参数
        important_combinations = []
        
        # 重点搜索α值
        for alpha in param_grid['alpha_init']:
            for lr in [0.0008, 0.001]:  # 只测试2个学习率
                for wd in [5e-4]:  # 固定权重衰减
                    for dropout in [0.3]:  # 固定dropout
                        for scale in [1.0]:  # 固定特征缩放
                            important_combinations.append([alpha, lr, wd, dropout, scale])
        
        print(f"重点搜索: {len(important_combinations)}个重要组合")
        
        # 执行搜索
        for i, params in enumerate(important_combinations):
            print(f"\n进度: {i+1}/{len(important_combinations)}")
            self.objective_function(params)
        
        # 基于最佳α值进行精细搜索
        if self.best_params is not None:
            best_alpha = self.best_params[0]
            print(f"\n🎯 基于最佳α={best_alpha:.3f}进行精细搜索...")
            
            fine_combinations = []
            for lr in param_grid['lr']:
                for wd in param_grid['weight_decay']:
                    for dropout in param_grid['dropout_rate']:
                        fine_combinations.append([best_alpha, lr, wd, dropout, 1.0])
            
            for i, params in enumerate(fine_combinations):
                print(f"\n精细搜索进度: {i+1}/{len(fine_combinations)}")
                self.objective_function(params)
        
        return self.best_params, self.best_score
    
    def save_results(self):
        """保存优化结果"""
        
        results = {
            'best_params': {
                'alpha_init': self.best_params[0],
                'lr': self.best_params[1],
                'weight_decay': self.best_params[2],
                'dropout_rate': self.best_params[3],
                'feature_scale': self.best_params[4]
            },
            'best_score': self.best_score,
            'improvement': (5.857 - self.best_score) / 5.857 * 100 if self.best_score < 5.857 else 0,
            'history': self.history
        }
        
        with open('bayesian_optimization_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📊 **优化结果保存完成**")
        print(f"   最佳参数: α={self.best_params[0]:.3f}, lr={self.best_params[1]:.4f}")
        print(f"   最佳性能: {self.best_score:.3f}mm")
        if self.best_score < 5.857:
            improvement = (5.857 - self.best_score) / 5.857 * 100
            print(f"   性能提升: {improvement:.1f}%")
        print(f"   结果文件: bayesian_optimization_results.json")

def main():
    """主函数 - 执行贝叶斯优化"""
    
    print("🎯 **贝叶斯超参数优化 - 第1周立即实施**")
    print("🚀 **目标: 5.857mm → 5.6-5.7mm (提升3-5%)**")
    print("🔧 **重点: α值、学习率、正则化参数**")
    print("=" * 80)
    
    set_seed(42)
    
    # 创建优化器
    optimizer = BayesianOptimizer(device='cuda:1')
    
    # 执行优化
    start_time = time.time()
    best_params, best_score = optimizer.grid_search_optimization()
    optimization_time = time.time() - start_time
    
    # 保存结果
    optimizer.save_results()
    
    print(f"\n🎉 **超参数优化完成!**")
    print(f"⏱️  优化时间: {optimization_time/60:.1f}分钟")
    print(f"🏆 最佳参数组合:")
    print(f"   α初始值: {best_params[0]:.3f}")
    print(f"   学习率: {best_params[1]:.4f}")
    print(f"   权重衰减: {best_params[2]:.1e}")
    print(f"   Dropout: {best_params[3]:.2f}")
    print(f"   特征缩放: {best_params[4]:.1f}")
    
    print(f"\n📊 **性能对比**:")
    print(f"   原始最佳: 5.857mm")
    print(f"   优化后: {best_score:.3f}mm")
    
    if best_score < 5.857:
        improvement = (5.857 - best_score) / 5.857 * 100
        print(f"   🎉 提升: {improvement:.1f}%")
        
        if best_score < 5.7:
            print(f"   🏆 成功达到第1周目标!")
        else:
            print(f"   💡 接近目标，可进入第2周架构优化")
    else:
        print(f"   💡 需要尝试其他优化策略")
    
    print(f"\n🚀 **下一步建议**:")
    if best_score < 5.7:
        print(f"   ✅ 第1周目标达成，开始第2周架构微调")
    else:
        print(f"   🔧 继续超参数精调或开始架构优化")

if __name__ == "__main__":
    main()
