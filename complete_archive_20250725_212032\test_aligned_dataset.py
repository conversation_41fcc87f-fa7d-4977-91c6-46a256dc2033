#!/usr/bin/env python3
"""
测试对齐后的数据集
Test Aligned Dataset
验证F3坐标系对齐的效果
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
from sklearn.model_selection import train_test_split

class SimpleKeypointNet(nn.Module):
    """简单的关键点检测网络"""
    
    def __init__(self):
        super().__init__()
        self.feature_extractor = nn.Sequential(
            nn.Linear(3, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 128),
            nn.ReLU()
        )
        
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(128, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.<PERSON><PERSON><PERSON>(),
            nn.Linear(128, 19*3)
        )
        
    def forward(self, point_cloud):
        batch_size = point_cloud.size(0)
        pc_flat = point_cloud.view(-1, 3)
        features = self.feature_extractor(pc_flat)
        features = features.view(batch_size, -1, features.size(-1))
        global_feature, _ = torch.max(features, dim=1)
        keypoints = self.keypoint_regressor(global_feature)
        return keypoints.view(batch_size, 19, 3)

def test_alignment_effect():
    """测试对齐效果"""
    print("🧪 测试F3坐标系对齐效果")
    print("=" * 60)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🖥️  设备: {device}")
    
    # 加载原始数据
    print("\n📦 加载原始数据...")
    original_data = np.load('data/raw/high_quality_f3_dataset.npz', allow_pickle=True)
    orig_pcs = original_data['point_clouds']
    orig_kps = original_data['keypoints']
    
    # 准备原始数据 (简单下采样)
    orig_pcs_uniform = []
    for pc in orig_pcs:
        pc_array = np.array(pc, dtype=np.float32)
        if len(pc_array) > 4096:
            indices = np.random.choice(len(pc_array), 4096, replace=False)
            pc_uniform = pc_array[indices]
        else:
            indices = np.random.choice(len(pc_array), 4096, replace=True)
            pc_uniform = pc_array[indices]
        orig_pcs_uniform.append(pc_uniform)
    
    orig_pcs_uniform = np.array(orig_pcs_uniform)
    orig_kps_uniform = np.array([np.array(kp, dtype=np.float32) for kp in orig_kps])
    
    print(f"✅ 原始数据: {orig_pcs_uniform.shape}, {orig_kps_uniform.shape}")
    
    # 加载对齐数据
    print("\n📦 加载对齐数据...")
    aligned_files = list(Path("data/processed").glob("f3_aligned_dataset_*.npz"))
    latest_aligned = max(aligned_files, key=lambda x: x.stat().st_mtime)
    
    aligned_data = np.load(str(latest_aligned), allow_pickle=True)
    aligned_pcs = aligned_data['point_clouds']
    aligned_kps = aligned_data['keypoints']
    
    print(f"✅ 对齐数据: {aligned_pcs.shape}, {aligned_kps.shape}")
    
    # 数据划分
    indices = np.arange(len(orig_pcs_uniform))
    train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
    train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
    
    # 测试原始数据
    print(f"\n🎯 测试原始数据...")
    orig_error = train_and_test_model(
        orig_pcs_uniform[train_indices], orig_kps_uniform[train_indices],
        orig_pcs_uniform[test_indices], orig_kps_uniform[test_indices],
        device, "原始数据"
    )
    
    # 测试对齐数据
    print(f"\n🎯 测试对齐数据...")
    aligned_error = train_and_test_model(
        aligned_pcs[train_indices], aligned_kps[train_indices],
        aligned_pcs[test_indices], aligned_kps[test_indices],
        device, "对齐数据"
    )
    
    # 计算改进
    improvement = (orig_error - aligned_error) / orig_error * 100
    
    print(f"\n📊 对齐效果验证:")
    print("=" * 50)
    print(f"原始数据误差:   {orig_error:.2f}mm")
    print(f"对齐数据误差:   {aligned_error:.2f}mm")
    print(f"性能改进:       {improvement:+.1f}%")
    
    # 分析结果
    if improvement > 30:
        print("🎉 坐标系对齐效果显著！")
        status = "显著改进"
    elif improvement > 10:
        print("👍 坐标系对齐有明显效果")
        status = "明显改进"
    elif improvement > 0:
        print("📈 坐标系对齐有一定效果")
        status = "轻微改进"
    else:
        print("⚠️ 坐标系对齐效果不明显")
        status = "效果有限"
    
    # 保存测试结果
    results = {
        "test_timestamp": datetime.now().isoformat(),
        "test_type": "coordinate_alignment_verification",
        "original_error": orig_error,
        "aligned_error": aligned_error,
        "improvement_percent": improvement,
        "status": status,
        "data_info": {
            "original_data_shape": orig_pcs_uniform.shape,
            "aligned_data_shape": aligned_pcs.shape,
            "coordinate_system": "f3_centered",
            "crop_radius": "80mm"
        },
        "conclusion": f"坐标系对齐带来{improvement:+.1f}%的性能改进"
    }
    
    # 保存结果
    results_dir = Path("results/alignment_tests")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = results_dir / f"alignment_test_results_{timestamp}.json"
    
    import json
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 测试结果已保存: {results_file}")
    
    return results

def train_and_test_model(train_pcs, train_kps, test_pcs, test_kps, device, data_type):
    """训练并测试模型"""
    print(f"   训练{data_type}模型...")
    
    # 创建模型
    model = SimpleKeypointNet().to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=0.002, weight_decay=1e-4)
    criterion = nn.MSELoss()
    
    # 训练参数
    k_shot = 15
    epochs = 30
    
    for epoch in range(epochs):
        model.train()
        
        # 采样训练数据
        if len(train_pcs) > k_shot:
            selected_indices = np.random.choice(len(train_pcs), k_shot, replace=False)
            batch_pcs = train_pcs[selected_indices]
            batch_kps = train_kps[selected_indices]
        else:
            batch_pcs = train_pcs
            batch_kps = train_kps
        
        # 简单数据增强
        aug_pcs = []
        aug_kps = []
        
        for pc, kp in zip(batch_pcs, batch_kps):
            # 原始数据
            aug_pcs.append(pc)
            aug_kps.append(kp)
            
            # 轻微旋转
            angle = np.random.uniform(-0.05, 0.05)
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], dtype=np.float32)
            
            aug_pc = pc @ rotation.T
            aug_kp = kp @ rotation.T
            aug_pcs.append(aug_pc)
            aug_kps.append(aug_kp)
            
            # 轻微噪声
            noise_pc = pc + np.random.normal(0, 0.5, pc.shape).astype(np.float32)
            aug_pcs.append(noise_pc)
            aug_kps.append(kp)
        
        # 转换为tensor
        train_pcs_tensor = torch.FloatTensor(aug_pcs).to(device)
        train_kps_tensor = torch.FloatTensor(aug_kps).to(device)
        
        # 训练步骤
        optimizer.zero_grad()
        pred_kps = model(train_pcs_tensor)
        loss = criterion(pred_kps, train_kps_tensor)
        loss.backward()
        
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        if epoch % 10 == 0:
            print(f"     Epoch {epoch}: Loss={loss:.4f}")
        
        # 清理内存
        del train_pcs_tensor, train_kps_tensor, pred_kps, loss
        torch.cuda.empty_cache()
    
    # 测试
    model.eval()
    test_errors = []
    
    with torch.no_grad():
        for pc, kp in zip(test_pcs, test_kps):
            pc_tensor = torch.FloatTensor(pc).unsqueeze(0).to(device)
            kp_tensor = torch.FloatTensor(kp).unsqueeze(0).to(device)
            
            pred_kp = model(pc_tensor).cpu().numpy()[0]
            true_kp = kp
            
            # 计算误差 (在真实物理空间)
            errors = np.linalg.norm(pred_kp - true_kp, axis=1)
            mean_error = np.mean(errors)
            test_errors.append(mean_error)
            
            # 清理内存
            del pc_tensor, kp_tensor
            torch.cuda.empty_cache()
    
    overall_error = np.mean(test_errors)
    print(f"   ✅ {data_type}测试误差: {overall_error:.2f}mm")
    
    return overall_error

if __name__ == "__main__":
    results = test_alignment_effect()
