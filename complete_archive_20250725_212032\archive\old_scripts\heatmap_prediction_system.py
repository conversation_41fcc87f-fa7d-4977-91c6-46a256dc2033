#!/usr/bin/env python3
"""
热力图预测系统
输出关键点的概率分布热力图，而不是单一点预测
专门针对男性骨盆数据进行训练和预测
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import open3d as o3d
from scipy.spatial.distance import cdist
from scipy.ndimage import gaussian_filter
import seaborn as sns

class HeatmapKeypointDataset(Dataset):
    """热力图关键点数据集"""
    
    def __init__(self, point_clouds, keypoints, sample_ids, num_points=8192, heatmap_sigma=3.0):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.sample_ids = sample_ids
        self.num_points = num_points
        self.heatmap_sigma = heatmap_sigma
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        pc = self.point_clouds[idx].copy()
        kp = self.keypoints[idx].copy()
        
        # 随机采样点云
        if len(pc) > self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=False)
            pc = pc[indices]
        elif len(pc) < self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=True)
            pc = pc[indices]
        
        # 生成热力图标签
        heatmaps = self.generate_heatmaps(pc, kp)
        
        # 转换为tensor
        pc = torch.FloatTensor(pc).transpose(0, 1)  # [3, N]
        heatmaps = torch.FloatTensor(heatmaps)  # [12, N]
        
        return pc, heatmaps, self.sample_ids[idx]
    
    def generate_heatmaps(self, pc, kp):
        """为每个关键点生成热力图"""
        heatmaps = []
        
        for i in range(12):
            # 计算每个点云点到关键点的距离
            distances = np.linalg.norm(pc - kp[i], axis=1)
            
            # 使用高斯函数生成热力图
            heatmap = np.exp(-(distances ** 2) / (2 * self.heatmap_sigma ** 2))
            
            # 归一化
            if np.max(heatmap) > 0:
                heatmap = heatmap / np.max(heatmap)
            
            heatmaps.append(heatmap)
        
        return np.array(heatmaps)  # [12, N]

class HeatmapPointNet(nn.Module):
    """热力图预测的PointNet"""
    
    def __init__(self, num_points=8192, num_keypoints=12):
        super(HeatmapPointNet, self).__init__()
        
        # 特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        
        # 全局特征
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.bn4 = nn.BatchNorm1d(512)
        
        # 热力图预测头
        self.conv5 = nn.Conv1d(512 + 256, 256, 1)  # 全局+局部特征
        self.conv6 = nn.Conv1d(256, 128, 1)
        self.conv7 = nn.Conv1d(128, num_keypoints, 1)  # 输出每个关键点的热力图
        
        self.bn5 = nn.BatchNorm1d(256)
        self.bn6 = nn.BatchNorm1d(128)
        
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        # x: [B, 3, N]
        batch_size, _, num_points = x.size()
        
        # 特征提取
        x1 = self.relu(self.bn1(self.conv1(x)))
        x2 = self.relu(self.bn2(self.conv2(x1)))
        x3 = self.relu(self.bn3(self.conv3(x2)))
        
        # 全局特征
        x4 = self.relu(self.bn4(self.conv4(x3)))
        global_feat = torch.max(x4, 2)[0]  # [B, 512]
        
        # 扩展全局特征到每个点
        global_feat_expanded = global_feat.unsqueeze(2).repeat(1, 1, num_points)  # [B, 512, N]
        
        # 结合全局和局部特征
        combined_feat = torch.cat([global_feat_expanded, x3], dim=1)  # [B, 512+256, N]
        
        # 热力图预测
        x5 = self.relu(self.bn5(self.conv5(combined_feat)))
        x5 = self.dropout(x5)
        x6 = self.relu(self.bn6(self.conv6(x5)))
        x6 = self.dropout(x6)
        heatmaps = self.sigmoid(self.conv7(x6))  # [B, 12, N]
        
        return heatmaps

def load_male_dataset():
    """加载男性数据集"""
    print("📊 **加载男性数据集**")
    
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    print(f"   男性样本数: {len(sample_ids)}")
    print(f"   点云形状: {point_clouds.shape}")
    print(f"   关键点形状: {keypoints.shape}")
    
    return sample_ids, point_clouds, keypoints

def create_train_val_split(sample_ids, point_clouds, keypoints):
    """创建训练验证分割"""
    print(f"\n📋 **创建数据分割**")
    
    n_samples = len(sample_ids)
    
    # 80/20分割
    train_idx, val_idx = train_test_split(
        range(n_samples), 
        test_size=0.2,
        random_state=42,
        shuffle=True
    )
    
    train_data = {
        'sample_ids': sample_ids[train_idx],
        'point_clouds': point_clouds[train_idx],
        'keypoints': keypoints[train_idx]
    }
    
    val_data = {
        'sample_ids': sample_ids[val_idx],
        'point_clouds': point_clouds[val_idx],
        'keypoints': keypoints[val_idx]
    }
    
    print(f"   训练集: {len(train_idx)}个样本")
    print(f"   验证集: {len(val_idx)}个样本")
    
    return train_data, val_data

def train_heatmap_model(train_data, val_data, epochs=150):
    """训练热力图模型"""
    print(f"\n🚀 **训练热力图预测模型**")
    
    # 创建数据集
    train_dataset = HeatmapKeypointDataset(
        train_data['point_clouds'],
        train_data['keypoints'],
        train_data['sample_ids'],
        heatmap_sigma=2.0  # 调整热力图的扩散范围
    )
    
    val_dataset = HeatmapKeypointDataset(
        val_data['point_clouds'],
        val_data['keypoints'],
        val_data['sample_ids'],
        heatmap_sigma=2.0
    )
    
    train_loader = DataLoader(train_dataset, batch_size=2, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=2, shuffle=False)
    
    # 模型和优化器
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=20, factor=0.5)
    
    # 损失函数 - 使用加权BCE损失
    def heatmap_loss(pred_heatmaps, true_heatmaps):
        # 对正样本给予更高权重
        pos_weight = torch.ones_like(true_heatmaps) * 2.0
        criterion = nn.BCEWithLogitsLoss(pos_weight=pos_weight, reduction='none')
        
        # 计算损失
        loss = criterion(pred_heatmaps, true_heatmaps)
        
        # 对每个关键点分别计算损失，然后平均
        loss = loss.mean(dim=2).mean(dim=0)  # [12]
        return loss.mean()
    
    # 训练循环
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    patience_counter = 0
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        
        for batch_idx, (pc, heatmaps, _) in enumerate(train_loader):
            pc, heatmaps = pc.to(device), heatmaps.to(device)
            
            optimizer.zero_grad()
            pred_heatmaps = model(pc)
            loss = heatmap_loss(pred_heatmaps, heatmaps)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for pc, heatmaps, _ in val_loader:
                pc, heatmaps = pc.to(device), heatmaps.to(device)
                pred_heatmaps = model(pc)
                loss = heatmap_loss(pred_heatmaps, heatmaps)
                val_loss += loss.item()
        
        val_loss /= len(val_loader)
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_heatmap_model_male.pth')
        else:
            patience_counter += 1
        
        if epoch % 20 == 0:
            print(f"   Epoch {epoch}: Train Loss={train_loss:.6f}, Val Loss={val_loss:.6f}")
        
        # 早停
        if patience_counter >= 30:
            print(f"   早停于epoch {epoch}")
            break
    
    return model, train_losses, val_losses

def predict_heatmaps(model, sample_pc, sample_kp, sample_id):
    """预测单个样本的热力图"""
    print(f"\n🎯 **预测样本 {sample_id} 的热力图**")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.eval()
    
    # 准备数据
    if len(sample_pc) > 8192:
        indices = np.random.choice(len(sample_pc), 8192, replace=False)
        pc_sampled = sample_pc[indices]
    else:
        pc_sampled = sample_pc
    
    pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)  # [1, 3, N]
    
    with torch.no_grad():
        pred_heatmaps = model(pc_tensor)  # [1, 12, N]
        pred_heatmaps = pred_heatmaps.squeeze(0).cpu().numpy()  # [12, N]
    
    return pc_sampled, pred_heatmaps

def visualize_heatmap_predictions(pc, pred_heatmaps, true_kp, sample_id, keypoint_idx=0):
    """可视化热力图预测结果"""
    print(f"   📊 可视化关键点 {keypoint_idx} 的热力图")
    
    fig = plt.figure(figsize=(15, 5))
    
    # 1. 3D点云 + 真实关键点
    ax1 = fig.add_subplot(131, projection='3d')
    ax1.scatter(pc[:, 0], pc[:, 1], pc[:, 2], c='lightgray', s=1, alpha=0.6)
    ax1.scatter(true_kp[keypoint_idx, 0], true_kp[keypoint_idx, 1], true_kp[keypoint_idx, 2], 
               c='red', s=100, marker='*', label='True Keypoint')
    ax1.set_title(f'Original Point Cloud\nSample: {sample_id}')
    ax1.legend()
    
    # 2. 热力图预测
    ax2 = fig.add_subplot(132, projection='3d')
    heatmap = pred_heatmaps[keypoint_idx]
    
    # 根据热力图值着色
    colors = plt.cm.hot(heatmap)
    scatter = ax2.scatter(pc[:, 0], pc[:, 1], pc[:, 2], c=heatmap, cmap='hot', s=2, alpha=0.8)
    ax2.scatter(true_kp[keypoint_idx, 0], true_kp[keypoint_idx, 1], true_kp[keypoint_idx, 2], 
               c='blue', s=100, marker='*', label='True Keypoint')
    ax2.set_title(f'Heatmap Prediction\nKeypoint {keypoint_idx}')
    ax2.legend()
    
    # 3. 热力图分布直方图
    ax3 = fig.add_subplot(133)
    ax3.hist(heatmap, bins=50, alpha=0.7, color='orange')
    ax3.axvline(np.max(heatmap), color='red', linestyle='--', label=f'Max: {np.max(heatmap):.3f}')
    ax3.axvline(np.mean(heatmap), color='blue', linestyle='--', label=f'Mean: {np.mean(heatmap):.3f}')
    ax3.set_xlabel('Heatmap Value')
    ax3.set_ylabel('Frequency')
    ax3.set_title('Heatmap Distribution')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'heatmap_prediction_{sample_id}_kp{keypoint_idx}.png', dpi=300, bbox_inches='tight')
    print(f"     💾 保存: heatmap_prediction_{sample_id}_kp{keypoint_idx}.png")
    plt.close()

def analyze_heatmap_quality(pc, pred_heatmaps, true_kp):
    """分析热力图质量"""
    print(f"   📊 分析热力图质量")
    
    results = {}
    
    for i in range(12):
        heatmap = pred_heatmaps[i]
        true_point = true_kp[i]
        
        # 找到热力图最大值点
        max_idx = np.argmax(heatmap)
        pred_point = pc[max_idx]
        
        # 计算预测误差
        error = np.linalg.norm(pred_point - true_point)
        
        # 计算热力图的集中度
        concentration = np.sum(heatmap > 0.5) / len(heatmap)  # 高置信度点的比例
        
        # 计算热力图的最大值
        max_confidence = np.max(heatmap)
        
        results[i] = {
            'error': error,
            'concentration': concentration,
            'max_confidence': max_confidence,
            'pred_point': pred_point,
            'true_point': true_point
        }
        
        print(f"     关键点{i}: 误差={error:.2f}mm, 集中度={concentration:.3f}, 最大置信度={max_confidence:.3f}")
    
    # 总体统计
    avg_error = np.mean([r['error'] for r in results.values()])
    avg_concentration = np.mean([r['concentration'] for r in results.values()])
    avg_confidence = np.mean([r['max_confidence'] for r in results.values()])
    
    print(f"   📈 总体统计:")
    print(f"     平均误差: {avg_error:.2f}mm")
    print(f"     平均集中度: {avg_concentration:.3f}")
    print(f"     平均置信度: {avg_confidence:.3f}")
    
    return results

def create_uncertainty_visualization(pc, pred_heatmaps, true_kp, sample_id):
    """创建不确定性可视化"""
    print(f"   🎨 创建不确定性可视化")
    
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    axes = axes.flatten()
    
    for i in range(12):
        ax = axes[i]
        heatmap = pred_heatmaps[i]
        
        # 创建2D投影 (使用前两个维度)
        scatter = ax.scatter(pc[:, 0], pc[:, 1], c=heatmap, cmap='hot', s=1, alpha=0.8)
        ax.scatter(true_kp[i, 0], true_kp[i, 1], c='blue', s=50, marker='*', 
                  edgecolor='white', linewidth=1, label='True')
        
        # 找到最高置信度点
        max_idx = np.argmax(heatmap)
        ax.scatter(pc[max_idx, 0], pc[max_idx, 1], c='red', s=50, marker='x', 
                  linewidth=2, label='Predicted')
        
        ax.set_title(f'Keypoint {i}\nConf: {np.max(heatmap):.3f}')
        ax.legend()
        
        # 添加颜色条
        plt.colorbar(scatter, ax=ax, shrink=0.8)
    
    plt.suptitle(f'Heatmap Uncertainty Visualization - Sample {sample_id}', fontsize=16)
    plt.tight_layout()
    plt.savefig(f'uncertainty_visualization_{sample_id}.png', dpi=300, bbox_inches='tight')
    print(f"     💾 保存: uncertainty_visualization_{sample_id}.png")
    plt.close()

def main():
    """主函数"""
    print("🎯 **热力图关键点预测系统**")
    print("输出概率分布而不是单点预测，专注男性骨盆数据")
    print("=" * 80)
    
    # 加载男性数据集
    sample_ids, point_clouds, keypoints = load_male_dataset()
    
    # 创建训练验证分割
    train_data, val_data = create_train_val_split(sample_ids, point_clouds, keypoints)
    
    # 训练模型
    model, train_losses, val_losses = train_heatmap_model(train_data, val_data)
    
    # 加载最佳模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.load_state_dict(torch.load('best_heatmap_model_male.pth', map_location=device))
    
    # 选择几个验证样本进行预测
    test_indices = [0, 1, 2]  # 选择前3个验证样本
    
    for idx in test_indices:
        sample_id = val_data['sample_ids'][idx]
        sample_pc = val_data['point_clouds'][idx]
        sample_kp = val_data['keypoints'][idx]
        
        # 预测热力图
        pc_sampled, pred_heatmaps = predict_heatmaps(model, sample_pc, sample_kp, sample_id)
        
        # 分析质量
        results = analyze_heatmap_quality(pc_sampled, pred_heatmaps, sample_kp)
        
        # 可视化几个关键点
        for kp_idx in [0, 5, 11]:  # 选择几个代表性关键点
            visualize_heatmap_predictions(pc_sampled, pred_heatmaps, sample_kp, sample_id, kp_idx)
        
        # 创建不确定性可视化
        create_uncertainty_visualization(pc_sampled, pred_heatmaps, sample_kp, sample_id)
    
    print(f"\n🎉 **热力图预测系统完成!**")
    print(f"✅ 训练了专门的热力图预测模型")
    print(f"✅ 输出概率分布而不是单点预测")
    print(f"✅ 提供了不确定性量化")
    print(f"✅ 更适合医学应用的需求")
    
    print(f"\n💡 **优势**:")
    print(f"   • 提供置信度信息")
    print(f"   • 显示可能的关键点区域")
    print(f"   • 帮助医生理解预测的不确定性")
    print(f"   • 更符合医学诊断的思维模式")

if __name__ == "__main__":
    main()
