#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于真实训练模型的基准测试 - 使用已训练的52个模型
Real Trained Models Benchmark - Using 52 Pre-trained Models
"""

import numpy as np
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import pandas as pd
import json
import os
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

# 设置样式
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

class AdaptiveKeypointModel(nn.Module):
    """自适应关键点模型 - 与训练时相同的架构"""
    
    def __init__(self, num_points=50000, num_keypoints=12, architecture_type='auto'):
        super().__init__()
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        self.architecture_type = architecture_type
        
        if architecture_type == 'auto':
            if num_keypoints <= 6:
                self.arch_type = 'lightweight'
            elif num_keypoints <= 12:
                self.arch_type = 'balanced'
            elif num_keypoints <= 28:
                self.arch_type = 'enhanced'
            else:
                self.arch_type = 'deep'
        else:
            self.arch_type = architecture_type
        
        self._build_architecture()
    
    def _build_architecture(self):
        if self.arch_type == 'lightweight':
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(256, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, 128), nn.ReLU(), nn.Dropout(0.1),
                nn.Linear(128, self.num_keypoints * 3)
            )
            
        elif self.arch_type == 'balanced':
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(512, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, self.num_keypoints * 3)
            )
            
        elif self.arch_type == 'enhanced':
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
                nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(1024, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 1024), nn.ReLU(), nn.Dropout(0.4),
                nn.Linear(1024, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, self.num_keypoints * 3)
            )
        
        # 相互辅助机制
        mutual_dim = min(256, max(64, self.num_keypoints * 8))
        self.mutual_assistance = nn.Sequential(
            nn.Linear(self.num_keypoints * 3, mutual_dim),
            nn.ReLU(), nn.Dropout(0.2),
            nn.Linear(mutual_dim, mutual_dim // 2),
            nn.ReLU(),
            nn.Linear(mutual_dim // 2, self.num_keypoints * 3)
        )
    
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        features = self.feature_extractor(x)
        global_features = self.global_conv(features)
        global_feat = torch.max(global_features, 2)[0]
        
        initial_kp = self.predictor(global_feat)
        assistance = self.mutual_assistance(initial_kp)
        final_kp = initial_kp + 0.3 * assistance
        final_kp = final_kp.view(batch_size, self.num_keypoints, 3)
        
        return final_kp

class RealTrainedModelsBenchmark:
    """基于真实训练模型的基准测试"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️ 使用设备: {self.device}")
        
        # 加载真实训练结果
        self.load_real_results()
        
        # 加载数据集
        self.load_dataset()
        
        # 扫描可用的模型文件
        self.scan_available_models()
    
    def load_real_results(self):
        """加载真实的训练结果"""
        print("📊 加载真实训练结果...")
        
        with open('comprehensive_optimal_models_results.json', 'r') as f:
            self.real_results = json.load(f)
        
        print(f"✅ 加载了 {len(self.real_results['results'])} 个真实训练结果")
    
    def load_dataset(self):
        """加载数据集"""
        print("📥 加载数据集...")
        
        data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
        self.point_clouds = data['point_clouds']
        self.keypoints_57 = data['keypoints_57']
        
        # 创建测试集划分（与训练时一致）
        indices = np.arange(len(self.point_clouds))
        _, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
        
        self.test_pc = self.point_clouds[test_indices]
        self.test_kp_57 = self.keypoints_57[test_indices]
        
        print(f"✅ 测试集: {len(self.test_pc)} 样本")
    
    def scan_available_models(self):
        """扫描可用的模型文件"""
        print("🔍 扫描可用的模型文件...")
        
        self.available_models = []
        
        for filename in os.listdir('.'):
            if filename.startswith('best_') and filename.endswith('.pth'):
                # 解析文件名: best_15kp_balanced.pth
                parts = filename.replace('best_', '').replace('.pth', '').split('_')
                if len(parts) == 2:
                    kp_str, arch = parts
                    if kp_str.endswith('kp'):
                        kp_count = int(kp_str[:-2])
                        self.available_models.append({
                            'filename': filename,
                            'keypoints': kp_count,
                            'architecture': arch,
                            'path': filename
                        })
        
        print(f"✅ 找到 {len(self.available_models)} 个训练好的模型")
        
        # 按关键点数排序
        self.available_models.sort(key=lambda x: x['keypoints'])
    
    def normalize_data_for_inference(self, point_cloud, keypoints_subset):
        """应用训练时的归一化方法"""
        # 合并点云和关键点
        combined_data = np.vstack([point_cloud, keypoints_subset])

        # 使用StandardScaler归一化
        scaler = StandardScaler()
        combined_normalized = scaler.fit_transform(combined_data)

        # 分离归一化后的数据
        pc_normalized = combined_normalized[:len(point_cloud)]
        kp_normalized = combined_normalized[len(point_cloud):]

        return pc_normalized, kp_normalized, scaler

    def denormalize_prediction(self, prediction_normalized, scaler, num_points):
        """将归一化的预测结果反归一化到原始尺度"""
        # 创建虚拟的点云数据用于反归一化
        dummy_pc = np.zeros((num_points, 3))

        # 合并虚拟点云和预测关键点
        combined_data = np.vstack([dummy_pc, prediction_normalized])

        # 反归一化
        combined_denormalized = scaler.inverse_transform(combined_data)

        # 提取关键点部分
        prediction_denormalized = combined_denormalized[num_points:]

        return prediction_denormalized

    def evaluate_single_model(self, model_info):
        """评估单个模型"""

        kp_count = model_info['keypoints']
        architecture = model_info['architecture']
        model_path = model_info['path']

        print(f"🔄 评估 {kp_count}点 {architecture}架构...")

        try:
            # 创建模型
            model = AdaptiveKeypointModel(
                num_points=50000,
                num_keypoints=kp_count,
                architecture_type=architecture
            )

            # 加载权重
            checkpoint = torch.load(model_path, map_location=self.device)
            model.load_state_dict(checkpoint)
            model.to(self.device)
            model.eval()

            # 准备测试数据
            if kp_count == 57:
                test_kp = self.test_kp_57
            else:
                # 均匀采样关键点
                indices = np.linspace(0, 56, kp_count, dtype=int)
                test_kp = self.test_kp_57[:, indices, :]

            # 评估
            all_errors = []

            with torch.no_grad():
                for i in range(len(self.test_pc)):
                    # 应用训练时的归一化
                    pc_normalized, kp_normalized, scaler = self.normalize_data_for_inference(
                        self.test_pc[i], test_kp[i]
                    )

                    pc_tensor = torch.FloatTensor(pc_normalized).unsqueeze(0).to(self.device)
                    pred_kp_normalized = model(pc_tensor).cpu().numpy()[0]

                    # 反归一化预测结果
                    pred_kp = self.denormalize_prediction(pred_kp_normalized, scaler, len(self.test_pc[i]))
                    true_kp = test_kp[i]

                    # 计算误差
                    errors = np.linalg.norm(true_kp - pred_kp, axis=1)
                    all_errors.extend(errors)
            
            all_errors = np.array(all_errors)
            
            result = {
                'keypoints': kp_count,
                'architecture': architecture,
                'avg_error': np.mean(all_errors),
                'std_error': np.std(all_errors),
                'median_error': np.median(all_errors),
                'max_error': np.max(all_errors),
                'min_error': np.min(all_errors),
                'medical_rate': np.sum(all_errors <= 10) / len(all_errors) * 100,
                'excellent_rate': np.sum(all_errors <= 5) / len(all_errors) * 100,
                'precision_1mm': np.sum(all_errors <= 1) / len(all_errors) * 100,
                'num_params': sum(p.numel() for p in model.parameters()),
                'model_file': model_path
            }
            
            print(f"  ✅ 平均误差: {result['avg_error']:.2f}mm")
            
            return result
            
        except Exception as e:
            print(f"  ❌ 评估失败: {e}")
            return None
    
    def run_comprehensive_evaluation(self):
        """运行全面评估"""
        print("\n🚀 开始全面评估真实训练的模型...")
        print("=" * 80)
        
        self.evaluation_results = []
        
        for i, model_info in enumerate(self.available_models):
            print(f"\n📊 进度: {i+1}/{len(self.available_models)}")
            
            result = self.evaluate_single_model(model_info)
            if result:
                self.evaluation_results.append(result)
        
        # 保存结果
        self.save_evaluation_results()
        
        print(f"\n✅ 全面评估完成！成功评估了 {len(self.evaluation_results)} 个模型")
        
        return self.evaluation_results
    
    def save_evaluation_results(self):
        """保存评估结果"""
        
        # 保存为JSON
        results_dict = {
            'evaluation_type': 'real_trained_models_benchmark',
            'total_models': len(self.evaluation_results),
            'test_samples': len(self.test_pc),
            'results': self.evaluation_results,
            'timestamp': '2025-07-25'
        }
        
        with open('real_trained_models_evaluation.json', 'w') as f:
            json.dump(results_dict, f, indent=2, default=str)
        
        # 保存为CSV
        df = pd.DataFrame(self.evaluation_results)
        df.to_csv('real_trained_models_evaluation.csv', index=False)
        
        print("💾 评估结果已保存:")
        print("   📄 real_trained_models_evaluation.json")
        print("   📊 real_trained_models_evaluation.csv")
    
    def create_real_performance_comparison(self):
        """创建真实性能对比图表"""
        print("\n🎨 创建真实性能对比图表...")
        
        if not hasattr(self, 'evaluation_results'):
            print("❌ 请先运行评估")
            return
        
        df = pd.DataFrame(self.evaluation_results)
        
        # 创建对比图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 性能vs关键点数量
        colors = {'lightweight': '#E74C3C', 'balanced': '#3498DB', 'enhanced': '#2ECC71', 'auto': '#F39C12'}
        
        for arch in df['architecture'].unique():
            arch_data = df[df['architecture'] == arch].sort_values('keypoints')
            ax1.plot(arch_data['keypoints'], arch_data['avg_error'], 'o-', 
                    color=colors.get(arch, '#95A5A6'), linewidth=2, markersize=6, 
                    label=arch.capitalize(), alpha=0.8)
        
        ax1.axhline(y=10, color='orange', linestyle='--', alpha=0.7, label='Medical Grade (10mm)')
        ax1.axhline(y=5, color='green', linestyle='--', alpha=0.7, label='Excellent Grade (5mm)')
        ax1.set_xlabel('Number of Keypoints')
        ax1.set_ylabel('Average Error (mm)')
        ax1.set_title('Real Model Performance vs Keypoint Count')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 参数效率分析
        scatter = ax2.scatter(df['num_params']/1e6, df['avg_error'], 
                            c=[colors.get(arch, '#95A5A6') for arch in df['architecture']], 
                            s=100, alpha=0.7, edgecolors='black')
        
        for _, row in df.iterrows():
            ax2.annotate(f"{row['keypoints']}kp", 
                        (row['num_params']/1e6, row['avg_error']),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        ax2.set_xlabel('Parameters (M)')
        ax2.set_ylabel('Average Error (mm)')
        ax2.set_title('Parameter Efficiency Analysis')
        ax2.grid(True, alpha=0.3)
        
        # 3. 医疗级达标率
        medical_rates = df.groupby('architecture')['medical_rate'].mean()
        bars = ax3.bar(medical_rates.index, medical_rates.values, 
                      color=[colors.get(arch, '#95A5A6') for arch in medical_rates.index],
                      alpha=0.8, edgecolor='black')
        
        ax3.axhline(y=90, color='red', linestyle='--', alpha=0.7, label='90% Threshold')
        ax3.set_ylabel('Medical Grade Success Rate (%)')
        ax3.set_title('Medical Grade Achievement by Architecture')
        ax3.legend()
        ax3.grid(True, alpha=0.3, axis='y')
        
        # 4. 最佳模型展示
        best_models = df.nsmallest(10, 'avg_error')
        
        bars = ax4.barh(range(len(best_models)), best_models['avg_error'],
                       color=[colors.get(arch, '#95A5A6') for arch in best_models['architecture']],
                       alpha=0.8, edgecolor='black')
        
        ax4.set_yticks(range(len(best_models)))
        ax4.set_yticklabels([f"{row['keypoints']}kp {row['architecture']}" 
                            for _, row in best_models.iterrows()])
        ax4.set_xlabel('Average Error (mm)')
        ax4.set_title('Top 10 Best Performing Models')
        ax4.grid(True, alpha=0.3, axis='x')
        
        plt.suptitle('Real Trained Models: Comprehensive Performance Analysis', 
                     fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        filename = 'real_trained_models_performance_analysis.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()
        
        print(f"✅ 真实性能对比图表已保存: {filename}")
        
        return filename

if __name__ == "__main__":
    print("🧪 基于真实训练模型的基准测试")
    print("使用52个已训练的模型进行评估")
    print("=" * 80)
    
    # 创建基准测试框架
    benchmark = RealTrainedModelsBenchmark()
    
    # 运行全面评估
    results = benchmark.run_comprehensive_evaluation()
    
    # 创建性能对比图表
    chart_file = benchmark.create_real_performance_comparison()
    
    print(f"\n📋 真实基准测试总结:")
    print(f"   🔬 评估模型数: {len(results)}")
    print(f"   📊 测试样本数: {len(benchmark.test_pc)}")
    print(f"   🎯 关键点范围: 3-57个")
    print(f"   🏗️ 架构类型: 4种 (lightweight, balanced, enhanced, auto)")
    
    if results:
        best_model = min(results, key=lambda x: x['avg_error'])
        print(f"\n🏆 最佳模型:")
        print(f"   📊 {best_model['keypoints']}点 {best_model['architecture']}架构")
        print(f"   📊 平均误差: {best_model['avg_error']:.2f}mm")
        print(f"   📊 医疗级达标率: {best_model['medical_rate']:.1f}%")
    
    print(f"\n💡 这是基于您真实训练的52个模型的评估结果！")
