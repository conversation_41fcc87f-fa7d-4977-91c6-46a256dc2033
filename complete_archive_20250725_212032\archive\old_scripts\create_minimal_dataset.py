#!/usr/bin/env python3
"""
Create Minimal Dataset for Testing

Create a small dataset with just a few samples to verify the data loading works correctly.
"""

import numpy as np
import pandas as pd
from pathlib import Path
import json
import struct

def load_annotation_file(csv_path: str):
    """Load annotation CSV file with proper encoding"""
    try:
        df = pd.read_csv(csv_path, encoding='gbk')
    except:
        try:
            df = pd.read_csv(csv_path, encoding='utf-8')
        except:
            df = pd.read_csv(csv_path, encoding='latin-1')
    
    keypoints = df[['X', 'Y', 'Z']].values
    labels = df['label'].values.tolist()
    
    return keypoints, labels

def read_stl_binary_limited(stl_path: str, max_triangles=1000):
    """Read STL file with triangle limit to avoid memory issues"""
    try:
        with open(stl_path, 'rb') as f:
            f.read(80)  # Skip header
            num_triangles = struct.unpack('<I', f.read(4))[0]
            
            # Limit triangles to avoid memory issues
            triangles_to_read = min(num_triangles, max_triangles)
            
            vertices = []
            for i in range(triangles_to_read):
                f.read(12)  # Skip normal
                for j in range(3):
                    x, y, z = struct.unpack('<fff', f.read(12))
                    vertices.append([x, y, z])
                f.read(2)  # Skip attribute
            
            return np.array(vertices)
    except Exception as e:
        print(f"STL读取失败: {e}")
        return None

def separate_keypoints_by_region(keypoints, labels):
    """Separate keypoints by F1/F2/F3 regions based on labels"""
    
    f1_keypoints = []
    f2_keypoints = []
    f3_keypoints = []
    
    for i, label in enumerate(labels):
        if isinstance(label, str):
            if label.startswith('F_1') or label.startswith('F1'):
                f1_keypoints.append(keypoints[i])
            elif label.startswith('F_2') or label.startswith('F2'):
                f2_keypoints.append(keypoints[i])
            elif label.startswith('F_3') or label.startswith('F3'):
                f3_keypoints.append(keypoints[i])
    
    return {
        'F1': np.array(f1_keypoints) if f1_keypoints else np.array([]),
        'F2': np.array(f2_keypoints) if f2_keypoints else np.array([]),
        'F3': np.array(f3_keypoints) if f3_keypoints else np.array([])
    }

def process_single_sample_minimal(sample_id):
    """Process single sample with minimal operations"""
    
    print(f"🔧 处理样本 {sample_id}")
    
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    annotations_dir = data_dir / "annotations"
    stl_dir = data_dir / "stl_models"
    
    # Load annotation
    csv_file = annotations_dir / f"{sample_id}-Table-XYZ.CSV"
    
    if not csv_file.exists():
        print(f"   ❌ 标注文件不存在")
        return None
    
    try:
        keypoints, labels = load_annotation_file(str(csv_file))
        print(f"   ✅ 加载了 {len(keypoints)} 个关键点")
    except Exception as e:
        print(f"   ❌ 标注加载失败: {e}")
        return None
    
    # Separate keypoints
    regions = separate_keypoints_by_region(keypoints, labels)
    
    # Load F3 STL only (most reliable region)
    f3_stl_file = stl_dir / f"{sample_id}-F_3.stl"
    
    if not f3_stl_file.exists():
        print(f"   ❌ F3 STL文件不存在")
        return None
    
    f3_vertices = read_stl_binary_limited(str(f3_stl_file), max_triangles=500)
    
    if f3_vertices is None or len(regions['F3']) == 0:
        print(f"   ❌ F3数据不完整")
        return None
    
    # Simple distance validation
    f3_keypoints = regions['F3']
    distances = []
    
    for kp in f3_keypoints:
        dists = np.linalg.norm(f3_vertices - kp, axis=1)
        min_dist = np.min(dists)
        distances.append(min_dist)
    
    mean_dist = np.mean(distances)
    within_5mm = np.sum(np.array(distances) <= 5.0) / len(distances) * 100
    
    print(f"   📊 F3对齐: 平均{mean_dist:.2f}mm, {within_5mm:.1f}% ≤5mm")
    
    # Sample point cloud (very small)
    if len(f3_vertices) > 1000:
        indices = np.random.choice(len(f3_vertices), 1000, replace=False)
        sampled_vertices = f3_vertices[indices]
    else:
        sampled_vertices = f3_vertices
    
    result = {
        'sample_id': sample_id,
        'f3_point_cloud': sampled_vertices,
        'f3_keypoints': f3_keypoints,
        'alignment_quality': {
            'mean_distance': mean_dist,
            'within_5mm_percent': within_5mm
        }
    }
    
    print(f"   ✅ 处理成功")
    return result

def create_minimal_dataset():
    """Create minimal dataset with just a few samples"""
    
    print("🏗️ **创建最小验证数据集**")
    print("=" * 50)
    
    # Get first few available samples
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    annotations_dir = data_dir / "annotations"
    
    xyz_files = list(annotations_dir.glob("*-Table-XYZ.CSV"))
    
    if not xyz_files:
        print("❌ 没有找到标注文件")
        return None
    
    # Test with first 3 samples only
    test_samples = []
    for csv_file in xyz_files[:3]:
        sample_id = csv_file.stem.split('-')[0]
        test_samples.append(sample_id)
    
    print(f"🎯 测试样本: {test_samples}")
    
    # Process samples
    processed_samples = []
    
    for sample_id in test_samples:
        result = process_single_sample_minimal(sample_id)
        if result is not None:
            processed_samples.append(result)
    
    print(f"\n📊 **处理结果**")
    print(f"   成功样本: {len(processed_samples)}")
    
    if processed_samples:
        # Save as simple JSON
        output_data = {
            'dataset_name': 'Minimal_F3_Validation',
            'creation_date': str(pd.Timestamp.now()),
            'total_samples': len(processed_samples),
            'samples': []
        }
        
        for sample in processed_samples:
            sample_data = {
                'sample_id': sample['sample_id'],
                'point_cloud_shape': sample['f3_point_cloud'].shape,
                'keypoints_shape': sample['f3_keypoints'].shape,
                'alignment_quality': sample['alignment_quality']
            }
            output_data['samples'].append(sample_data)
        
        # Save metadata
        with open('minimal_dataset_info.json', 'w') as f:
            json.dump(output_data, f, indent=2, default=str)
        
        # Save actual data as numpy
        np.savez_compressed('minimal_f3_dataset.npz',
                           sample_ids=[s['sample_id'] for s in processed_samples],
                           point_clouds=[s['f3_point_cloud'] for s in processed_samples],
                           keypoints=[s['f3_keypoints'] for s in processed_samples])
        
        print(f"   ✅ 数据已保存:")
        print(f"      📄 minimal_dataset_info.json")
        print(f"      📦 minimal_f3_dataset.npz")
        
        # Summary
        avg_quality = np.mean([s['alignment_quality']['mean_distance'] for s in processed_samples])
        avg_5mm = np.mean([s['alignment_quality']['within_5mm_percent'] for s in processed_samples])
        
        print(f"\n📈 **质量总结**")
        print(f"   平均对齐距离: {avg_quality:.2f}mm")
        print(f"   平均5mm精度: {avg_5mm:.1f}%")
        
        return processed_samples
    
    return None

def test_minimal_dataset():
    """Test loading the minimal dataset"""
    
    print(f"\n🧪 **测试最小数据集加载**")
    
    try:
        # Load data
        data = np.load('minimal_f3_dataset.npz', allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        print(f"   ✅ 成功加载数据:")
        print(f"      样本数: {len(sample_ids)}")
        print(f"      样本ID: {list(sample_ids)}")
        
        for i, sample_id in enumerate(sample_ids):
            pc_shape = point_clouds[i].shape
            kp_shape = keypoints[i].shape
            print(f"      {sample_id}: 点云{pc_shape}, 关键点{kp_shape}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 加载失败: {e}")
        return False

if __name__ == "__main__":
    # Create minimal dataset
    result = create_minimal_dataset()
    
    if result:
        # Test loading
        test_minimal_dataset()
        
        print(f"\n🎉 **最小数据集创建成功!**")
        print(f"💡 这证明了数据加载流程是正确的")
        print(f"🎯 下一步: 基于此创建完整的训练数据集")
    else:
        print(f"\n❌ **最小数据集创建失败**")
