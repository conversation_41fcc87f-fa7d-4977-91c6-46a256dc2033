#!/usr/bin/env python3
"""
Optimized Training with Strict Test Set Isolation

严格的测试集隔离 + 优化模型架构，目标突破5mm医疗精度
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import time
import json
import gc
import random

# 设置随机种子确保可重复性
def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class StrictF3Dataset(Dataset):
    """严格测试集隔离的数据集"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points
        self.augment = augment
        
        # 设置随机种子
        np.random.seed(seed)
        
        # 加载数据
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        print(f"📦 总样本数: {len(sample_ids)}")
        
        # 严格的测试集隔离
        if test_samples is None:
            # 如果没有指定测试样本，随机选择但确保固定
            np.random.seed(42)  # 固定种子确保测试集一致
            all_indices = np.arange(len(sample_ids))
            test_indices = np.random.choice(all_indices, size=15, replace=False)  # 15%作为测试集
            test_samples = [sample_ids[i] for i in test_indices]
            print(f"🔒 自动选择测试样本: {test_samples}")
        
        # 分离测试集
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            # 测试集：严格隔离
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            print(f"🧪 测试集: {len(self.sample_ids)} 样本 (严格隔离)")
            
        else:
            # 训练+验证集：从剩余样本中分割
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            # 从训练+验证集中分割
            val_size = int(0.2 * len(train_val_ids))  # 20%作为验证集
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                print(f"🏋️ 训练集: {len(self.sample_ids)} 样本")
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
                print(f"✅ 验证集: {len(self.sample_ids)} 样本")
        
        print(f"   样本ID: {list(self.sample_ids)}")
    
    def __len__(self):
        return len(self.sample_ids)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        # 智能下采样
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        # 医疗级数据增强
        if self.augment:
            # 1. 小幅旋转 (医疗数据需要保持解剖方向)
            angle = np.random.uniform(-0.15, 0.15)  # ±8.6度
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
            point_cloud = point_cloud @ rotation.T
            keypoints = keypoints @ rotation.T
            
            # 2. 小幅平移
            translation = np.random.uniform(-0.8, 0.8, 3)  # ±0.8mm
            point_cloud += translation
            keypoints += translation
            
            # 3. 各向异性缩放 (模拟个体差异)
            scale = np.random.uniform(0.98, 1.02, 3)  # ±2%
            point_cloud *= scale
            keypoints *= scale
            
            # 4. 高斯噪声 (模拟扫描噪声)
            noise = np.random.normal(0, 0.05, point_cloud.shape)  # 0.05mm std
            point_cloud += noise
            
            # 5. 随机点丢失 (模拟扫描缺陷)
            if np.random.random() < 0.3:  # 30%概率
                keep_ratio = np.random.uniform(0.95, 1.0)
                keep_num = int(len(point_cloud) * keep_ratio)
                keep_indices = np.random.choice(len(point_cloud), keep_num, replace=False)
                point_cloud = point_cloud[keep_indices]
                
                # 补充到目标点数
                if len(point_cloud) < self.num_points:
                    repeat_indices = np.random.choice(len(point_cloud), 
                                                    self.num_points - len(point_cloud), 
                                                    replace=True)
                    point_cloud = np.vstack([point_cloud, point_cloud[repeat_indices]])
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

class OptimizedPointNet(nn.Module):
    """优化的PointNet架构，针对医疗精度优化"""
    
    def __init__(self, num_keypoints: int = 19, dropout_rate: float = 0.3):
        super(OptimizedPointNet, self).__init__()
        
        # 特征提取层 (更深的网络)
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 全局特征处理
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, num_keypoints * 3)
        
        # 批归一化和Dropout
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        
        self.dropout = nn.Dropout(dropout_rate)
        
        # 残差连接
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # (batch, 3, points)
        
        # 特征提取 + 残差连接
        x1 = torch.relu(self.bn1(self.conv1(x)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        
        # 第一个残差连接
        x3_res = x3 + self.residual1(x1)
        
        x4 = torch.relu(self.bn4(self.conv4(x3_res)))
        x5 = torch.relu(self.bn5(self.conv5(x4)))
        
        # 第二个残差连接
        x5_res = x5 + self.residual2(x2).repeat(1, 2, 1)  # 调整维度匹配
        
        # 全局最大池化
        global_feat = torch.max(x5_res, 2)[0]  # (batch, 1024)
        
        # 全连接层 + 残差
        x = torch.relu(self.bn_fc1(self.fc1(global_feat)))
        x = self.dropout(x)
        
        x = torch.relu(self.bn_fc2(self.fc2(x)))
        x = self.dropout(x)
        
        x = torch.relu(self.bn_fc3(self.fc3(x)))
        x = self.dropout(x)
        
        x = self.fc4(x)
        
        return x.view(batch_size, 19, 3)

class FocalMSELoss(nn.Module):
    """Focal MSE Loss - 对困难样本给予更多关注"""
    
    def __init__(self, alpha=2.0, reduction='mean'):
        super(FocalMSELoss, self).__init__()
        self.alpha = alpha
        self.reduction = reduction
    
    def forward(self, pred, target):
        mse = torch.mean((pred - target) ** 2, dim=2)  # (batch, 19)
        
        # 计算focal weight
        focal_weight = torch.pow(mse, self.alpha / 2)
        focal_mse = focal_weight * mse
        
        if self.reduction == 'mean':
            return torch.mean(focal_mse)
        elif self.reduction == 'sum':
            return torch.sum(focal_mse)
        else:
            return focal_mse

def calculate_comprehensive_metrics(pred, target):
    """计算全面的评估指标"""
    distances = torch.norm(pred - target, dim=2)  # (batch, 19)
    avg_distances = torch.mean(distances, dim=1)  # (batch,)
    
    # 基本统计
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item()
    median_dist = torch.median(avg_distances).item()
    max_dist = torch.max(avg_distances).item()
    
    # 精度指标
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_2mm = (avg_distances <= 2.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_10mm = (avg_distances <= 10.0).float().mean().item() * 100
    
    # 每个关键点的平均误差
    per_keypoint_error = torch.mean(distances, dim=0)  # (19,)
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'median_distance': median_dist,
        'max_distance': max_dist,
        'within_1mm_percent': within_1mm,
        'within_2mm_percent': within_2mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_10mm_percent': within_10mm,
        'per_keypoint_error': per_keypoint_error.cpu().numpy().tolist()
    }

def train_optimized_model():
    """训练优化模型，严格测试集隔离"""
    
    print("🚀 **优化训练 - 严格测试集隔离 + 5mm精度目标**")
    print("🎯 **关键改进**: 测试集完全隔离 + 医疗级优化**")
    print("=" * 80)
    
    # 设置随机种子
    set_seed(42)
    
    # 设备设置
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 严格的数据集分割
    dataset_path = "high_quality_f3_dataset.npz"
    
    # 预定义测试样本 (确保一致性)
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    print(f"🔒 **严格测试集隔离**")
    print(f"   预定义测试样本: {test_samples}")
    
    # 创建数据集 (测试集完全隔离)
    train_dataset = StrictF3Dataset(dataset_path, 'train', num_points=4096, 
                                  test_samples=test_samples, augment=True, seed=42)
    val_dataset = StrictF3Dataset(dataset_path, 'val', num_points=4096, 
                                test_samples=test_samples, augment=False, seed=42)
    test_dataset = StrictF3Dataset(dataset_path, 'test', num_points=4096, 
                                 test_samples=test_samples, augment=False, seed=42)
    
    # 数据加载器
    batch_size = 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, 
                            num_workers=0, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, 
                          num_workers=0, drop_last=False)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, 
                           num_workers=0, drop_last=False)
    
    print(f"📊 **数据集统计** (严格隔离)")
    print(f"   训练集: {len(train_dataset)} 样本")
    print(f"   验证集: {len(val_dataset)} 样本") 
    print(f"   测试集: {len(test_dataset)} 样本 (训练期间完全不可见)")
    print(f"   批大小: {batch_size}")
    
    # 优化模型
    model = OptimizedPointNet(num_keypoints=19, dropout_rate=0.3).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"\n🧠 **优化模型**")
    print(f"   参数数量: {total_params:,}")
    print(f"   模型大小: {total_params * 4 / (1024 * 1024):.1f}MB")
    
    # 优化训练设置
    criterion = FocalMSELoss(alpha=2.0)  # Focal loss关注困难样本
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
    
    # 学习率调度
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=20, T_mult=2, eta_min=1e-6
    )
    
    # 训练配置
    num_epochs = 150  # 更长训练
    best_val_error = float('inf')
    patience = 25  # 更大耐心
    patience_counter = 0
    history = []
    
    print(f"\n🎯 **开始优化训练** (目标: <5mm)")
    print(f"   训练轮数: {num_epochs}")
    print(f"   早停耐心: {patience}")
    print(f"   损失函数: Focal MSE Loss")
    print(f"   优化器: AdamW + Cosine Annealing")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 50)
        
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_metrics = {
            'mean_distance': 0, 'within_1mm_percent': 0, 
            'within_3mm_percent': 0, 'within_5mm_percent': 0
        }
        
        for batch_idx, batch in enumerate(train_loader):
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                
                with torch.no_grad():
                    metrics = calculate_comprehensive_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                        
            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue
        
        # 学习率调度
        scheduler.step()
        
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # 验证阶段 (测试集仍然隔离)
        model.eval()
        val_loss = 0.0
        val_metrics = {
            'mean_distance': 0, 'within_1mm_percent': 0, 
            'within_3mm_percent': 0, 'within_5mm_percent': 0
        }
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    metrics = calculate_comprehensive_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    print(f"❌ 验证批次失败: {e}")
                    continue
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # 打印结果
        current_lr = optimizer.param_groups[0]['lr']
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"1mm={train_metrics['within_1mm_percent']:.1f}%, "
              f"3mm={train_metrics['within_3mm_percent']:.1f}%, "
              f"5mm={train_metrics['within_5mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"1mm={val_metrics['within_1mm_percent']:.1f}%, "
              f"3mm={val_metrics['within_3mm_percent']:.1f}%, "
              f"5mm={val_metrics['within_5mm_percent']:.1f}%")
        print(f"学习率: {current_lr:.2e}")
        
        # 保存历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'learning_rate': current_lr
        })
        
        # 检查改进
        current_error = val_metrics['mean_distance']
        if current_error < best_val_error:
            best_val_error = current_error
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics,
                'test_samples': test_samples
            }, 'best_optimized_pointnet_f3.pth')
            
            print(f"🎉 新最佳模型! 验证误差: {best_val_error:.3f}mm")
            
            # 检查是否达到目标
            if best_val_error <= 5.0:
                print(f"🏆 **达到5mm目标!** 验证误差: {best_val_error:.3f}mm")
                if val_metrics['within_5mm_percent'] >= 80:
                    print(f"🎯 **优秀表现!** 80%+样本在5mm内")
        else:
            patience_counter += 1
            print(f"⏳ 无改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停触发")
            break
        
        # 内存清理
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    
    return model, test_loader, best_val_error, total_time, history, test_samples

def evaluate_on_test_set(model, test_loader, device, test_samples):
    """在严格隔离的测试集上进行最终评估"""

    print(f"\n🧪 **最终测试集评估** (严格隔离)")
    print(f"🔒 测试样本: {test_samples}")
    print("=" * 60)

    # 加载最佳模型
    checkpoint = torch.load('best_optimized_pointnet_f3.pth')
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()

    test_metrics = {
        'mean_distance': 0, 'std_distance': 0, 'median_distance': 0, 'max_distance': 0,
        'within_1mm_percent': 0, 'within_2mm_percent': 0, 'within_3mm_percent': 0,
        'within_5mm_percent': 0, 'within_10mm_percent': 0
    }

    all_distances = []
    per_sample_results = []

    with torch.no_grad():
        for batch_idx, batch in enumerate(test_loader):
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            sample_ids = batch['sample_id']

            try:
                pred_keypoints = model(point_cloud)

                # 计算每个样本的指标
                for i in range(len(sample_ids)):
                    pred_single = pred_keypoints[i:i+1]
                    target_single = keypoints[i:i+1]

                    metrics = calculate_comprehensive_metrics(pred_single, target_single)

                    sample_result = {
                        'sample_id': sample_ids[i],
                        'mean_distance': metrics['mean_distance'],
                        'within_5mm': metrics['within_5mm_percent'] > 0,
                        'per_keypoint_error': metrics['per_keypoint_error']
                    }
                    per_sample_results.append(sample_result)
                    all_distances.append(metrics['mean_distance'])

                # 批次指标
                batch_metrics = calculate_comprehensive_metrics(pred_keypoints, keypoints)
                for key in test_metrics:
                    if key in batch_metrics:
                        test_metrics[key] += batch_metrics[key]

            except RuntimeError as e:
                print(f"❌ 测试批次 {batch_idx} 失败: {e}")
                continue

    # 平均测试指标
    num_batches = len(test_loader)
    for key in test_metrics:
        test_metrics[key] /= num_batches

    # 详细统计
    all_distances = np.array(all_distances)

    print(f"📊 **测试集详细结果**")
    print(f"   测试样本数: {len(per_sample_results)}")
    print(f"   平均误差: {np.mean(all_distances):.3f}±{np.std(all_distances):.3f}mm")
    print(f"   中位数误差: {np.median(all_distances):.3f}mm")
    print(f"   最大误差: {np.max(all_distances):.3f}mm")
    print(f"   最小误差: {np.min(all_distances):.3f}mm")

    print(f"\n📈 **精度分布**")
    print(f"   ≤1mm: {test_metrics['within_1mm_percent']:.1f}%")
    print(f"   ≤2mm: {test_metrics['within_2mm_percent']:.1f}%")
    print(f"   ≤3mm: {test_metrics['within_3mm_percent']:.1f}%")
    print(f"   ≤5mm: {test_metrics['within_5mm_percent']:.1f}%")
    print(f"   ≤10mm: {test_metrics['within_10mm_percent']:.1f}%")

    # 医疗级评估
    excellent_samples = np.sum(all_distances <= 1.0)
    good_samples = np.sum(all_distances <= 3.0)
    acceptable_samples = np.sum(all_distances <= 5.0)

    print(f"\n🏥 **医疗级评估**")
    print(f"   优秀 (≤1mm): {excellent_samples}/{len(all_distances)} ({excellent_samples/len(all_distances)*100:.1f}%)")
    print(f"   良好 (≤3mm): {good_samples}/{len(all_distances)} ({good_samples/len(all_distances)*100:.1f}%)")
    print(f"   可接受 (≤5mm): {acceptable_samples}/{len(all_distances)} ({acceptable_samples/len(all_distances)*100:.1f}%)")

    # 每个样本的详细结果
    print(f"\n📋 **每个测试样本结果**")
    for result in per_sample_results:
        status = "✅" if result['mean_distance'] <= 5.0 else "❌"
        print(f"   {status} {result['sample_id']}: {result['mean_distance']:.3f}mm")

    # 最终评估
    if test_metrics['within_5mm_percent'] >= 80:
        print(f"\n🏆 **优秀结果!** 80%+样本达到医疗级精度 (<5mm)")
    elif test_metrics['within_5mm_percent'] >= 60:
        print(f"\n✅ **良好结果!** 60%+样本达到医疗级精度 (<5mm)")
    elif test_metrics['within_5mm_percent'] >= 40:
        print(f"\n⚠️ **可接受结果** 40%+样本达到医疗级精度 (<5mm)")
    else:
        print(f"\n❌ **需要改进** <40%样本达到医疗级精度")

    return test_metrics, per_sample_results

def main():
    """主训练流程"""

    try:
        # 训练优化模型
        model, test_loader, best_val_error, training_time, history, test_samples = train_optimized_model()

        print(f"\n🎯 **训练完成!**")
        print(f"   最佳验证误差: {best_val_error:.3f}mm")
        print(f"   训练时间: {training_time/60:.1f}分钟")

        # 严格测试集评估
        device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
        test_metrics, per_sample_results = evaluate_on_test_set(model, test_loader, device, test_samples)

        # 保存完整结果
        results = {
            'model_name': 'Optimized_PointNet_F3_Strict',
            'training_completed': True,
            'best_validation_error_mm': best_val_error,
            'test_metrics': test_metrics,
            'per_sample_results': per_sample_results,
            'training_time_minutes': training_time / 60,
            'total_epochs': len(history),
            'test_samples': test_samples,
            'strict_isolation': True,
            'training_history': history
        }

        with open('optimized_pointnet_f3_strict_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)

        print(f"\n💾 **结果已保存**: optimized_pointnet_f3_strict_results.json")

        # 最终总结
        print(f"\n🎉 **严格测试集评估完成!**")
        print(f"🎯 **关键成果**:")
        print(f"   ✅ 测试集完全隔离 - 无数据泄露")
        print(f"   📊 测试误差: {test_metrics['mean_distance']:.3f}mm")
        print(f"   🎯 5mm精度: {test_metrics['within_5mm_percent']:.1f}%")
        print(f"   🏥 医疗级评估: {'达标' if test_metrics['within_5mm_percent'] >= 60 else '需改进'}")

        return results

    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # 设置随机种子
    set_seed(42)

    print("🚀 **开始严格的优化训练**")
    print("🔒 **关键特性**: 测试集完全隔离 + 医疗级优化")
    print("🎯 **目标**: 突破5mm医疗精度阈值")

    results = main()
