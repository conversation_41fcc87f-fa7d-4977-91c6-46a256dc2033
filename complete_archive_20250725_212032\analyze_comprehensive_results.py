#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面最佳模型搜索结果分析
Comprehensive Optimal Model Search Results Analysis
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_results():
    """加载结果"""
    try:
        with open('comprehensive_optimal_models_results_cleaned.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        with open('comprehensive_optimal_models_results.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data

def create_comprehensive_analysis():
    """创建综合分析"""
    print("📈 创建全面最佳模型搜索分析")
    print("=" * 60)

    # 加载数据
    data = load_results()
    results = data['results']

    # 提取数据（只包含成功的结果）
    keypoint_counts = sorted([int(k) for k in results.keys()])
    errors = [results[str(kp)]['avg_error'] for kp in keypoint_counts]
    architectures = [results[str(kp)]['architecture'] for kp in keypoint_counts]
    parameters = [results[str(kp)]['parameters'] for kp in keypoint_counts]

    print(f"📊 成功配置数: {len(keypoint_counts)}")
    print(f"📊 失败架构: deep (GPU内存不足), auto (部分高关键点数量时内存不足)")
    
    # 创建可视化
    fig = plt.figure(figsize=(20, 16))
    
    # 1. 性能曲线
    ax1 = plt.subplot(2, 3, 1)
    plt.plot(keypoint_counts, errors, 'o-', linewidth=3, markersize=8, color='#2E86AB')
    plt.axhline(y=10, color='orange', linestyle='--', linewidth=2, label='Medical Grade (10mm)')
    plt.axhline(y=5, color='red', linestyle='--', linewidth=2, label='Excellent Grade (5mm)')
    plt.xlabel('Number of Keypoints', fontsize=12)
    plt.ylabel('Average Error (mm)', fontsize=12)
    plt.title('Performance vs Keypoint Count', fontsize=14, fontweight='bold')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. 架构分布
    ax2 = plt.subplot(2, 3, 2)
    arch_counts = Counter(architectures)
    colors = ['#A23B72', '#F18F01', '#C73E1D', '#2E86AB', '#F24236']
    wedges, texts, autotexts = plt.pie(arch_counts.values(), labels=arch_counts.keys(), 
                                      autopct='%1.1f%%', colors=colors[:len(arch_counts)])
    plt.title('Architecture Distribution', fontsize=14, fontweight='bold')
    
    # 3. 参数数量 vs 性能
    ax3 = plt.subplot(2, 3, 3)
    colors_arch = {'lightweight': '#A23B72', 'balanced': '#F18F01', 'enhanced': '#C73E1D', 
                   'deep': '#2E86AB', 'auto': '#F24236'}
    for i, arch in enumerate(architectures):
        plt.scatter(parameters[i]/1000000, errors[i], c=colors_arch[arch], 
                   s=100, alpha=0.7, label=arch if arch not in [architectures[j] for j in range(i)] else "")
    plt.xlabel('Parameters (Millions)', fontsize=12)
    plt.ylabel('Average Error (mm)', fontsize=12)
    plt.title('Parameters vs Performance', fontsize=14, fontweight='bold')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 4. 架构性能对比
    ax4 = plt.subplot(2, 3, 4)
    arch_performance = {}
    for i, arch in enumerate(architectures):
        if arch not in arch_performance:
            arch_performance[arch] = []
        arch_performance[arch].append(errors[i])
    
    arch_names = list(arch_performance.keys())
    arch_errors = [np.mean(arch_performance[arch]) for arch in arch_names]
    arch_stds = [np.std(arch_performance[arch]) if len(arch_performance[arch]) > 1 else 0 
                 for arch in arch_names]
    
    bars = plt.bar(arch_names, arch_errors, yerr=arch_stds, capsize=5, 
                   color=[colors_arch[arch] for arch in arch_names], alpha=0.7)
    plt.ylabel('Average Error (mm)', fontsize=12)
    plt.title('Architecture Performance Comparison', fontsize=14, fontweight='bold')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3, axis='y')
    
    # 5. 最佳性能区间
    ax5 = plt.subplot(2, 3, 5)
    excellent_count = sum(1 for e in errors if e <= 5.0)
    medical_count = sum(1 for e in errors if 5.0 < e <= 10.0)
    poor_count = sum(1 for e in errors if e > 10.0)
    
    categories = ['Excellent\n(≤5mm)', 'Medical\n(5-10mm)', 'Poor\n(>10mm)']
    counts = [excellent_count, medical_count, poor_count]
    colors_grade = ['#2E8B57', '#FF8C00', '#DC143C']
    
    bars = plt.bar(categories, counts, color=colors_grade, alpha=0.7)
    plt.ylabel('Number of Configurations', fontsize=12)
    plt.title('Performance Grade Distribution', fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3, axis='y')
    
    # 添加数值标签
    for bar, count in zip(bars, counts):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                str(count), ha='center', va='bottom', fontweight='bold')
    
    # 6. 关键点数量 vs 架构选择
    ax6 = plt.subplot(2, 3, 6)
    arch_kp_data = {}
    for i, arch in enumerate(architectures):
        if arch not in arch_kp_data:
            arch_kp_data[arch] = []
        arch_kp_data[arch].append(keypoint_counts[i])
    
    y_pos = 0
    for arch, kp_list in arch_kp_data.items():
        plt.scatter(kp_list, [y_pos] * len(kp_list), 
                   c=colors_arch[arch], s=100, alpha=0.7, label=arch)
        y_pos += 1
    
    plt.xlabel('Number of Keypoints', fontsize=12)
    plt.ylabel('Architecture', fontsize=12)
    plt.title('Keypoint Count vs Architecture Selection', fontsize=14, fontweight='bold')
    plt.yticks(range(len(arch_kp_data)), list(arch_kp_data.keys()))
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('comprehensive_optimal_models_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印统计信息
    print("\n📊 统计摘要")
    print("=" * 50)
    print(f"成功配置数: {len(results)}")
    print(f"关键点范围: {min(keypoint_counts)}-{max(keypoint_counts)}")
    print(f"平均误差范围: {min(errors):.2f}-{max(errors):.2f}mm")
    print(f"最佳性能: {min(errors):.2f}mm ({keypoint_counts[errors.index(min(errors))]}关键点)")

    print(f"\n🏆 成功架构统计:")
    for arch, count in arch_counts.items():
        avg_error = np.mean([errors[i] for i, a in enumerate(architectures) if a == arch])
        print(f"  {arch}: {count}次最佳 (平均误差: {avg_error:.2f}mm)")

    print(f"\n❌ 失败架构:")
    print(f"  deep: 所有配置均因GPU内存不足失败")
    print(f"  auto: 高关键点数量时因GPU内存不足失败")

    print(f"\n🎯 性能等级:")
    print(f"  优秀 (≤5mm): {excellent_count}/{len(errors)} ({excellent_count/len(errors)*100:.1f}%)")
    print(f"  医疗级 (5-10mm): {medical_count}/{len(errors)} ({medical_count/len(errors)*100:.1f}%)")
    print(f"  较差 (>10mm): {poor_count}/{len(errors)} ({poor_count/len(errors)*100:.1f}%)")

def create_detailed_table():
    """创建详细结果表格"""
    print("\n📋 详细结果表格")
    print("=" * 80)
    
    data = load_results()
    results = data['results']
    
    # 创建DataFrame
    df_data = []
    for kp_str, result in results.items():
        df_data.append({
            'Keypoints': int(kp_str),
            'Architecture': result['architecture'],
            'Avg_Error_mm': result['avg_error'],
            'Parameters_M': result['parameters'] / 1000000,
            'Medical_Grade': '✓' if result['medical_grade'] else '✗',
            'Excellent_Grade': '✓' if result['excellent_grade'] else '✗'
        })
    
    df = pd.DataFrame(df_data)
    df = df.sort_values('Keypoints')
    
    print(df.to_string(index=False, float_format='%.2f'))
    
    # 保存为CSV
    df.to_csv('comprehensive_optimal_models_table.csv', index=False)
    print(f"\n💾 详细表格已保存到 comprehensive_optimal_models_table.csv")

if __name__ == "__main__":
    create_comprehensive_analysis()
    create_detailed_table()
    print("\n✅ 全面分析完成！")
