#!/usr/bin/env python3
"""
高级训练策略优化
Advanced Training Strategies
使用更好的损失函数和训练策略优化Point Transformer
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
from sklearn.model_selection import train_test_split
import json
import math

# 导入之前的Point Transformer架构
from point_transformer_keypoint_detection import PointTransformerKeypointNet

class WingLoss(nn.Module):
    """Wing Loss - 对小误差更敏感的损失函数"""
    
    def __init__(self, omega=10.0, epsilon=2.0):
        super().__init__()
        self.omega = omega
        self.epsilon = epsilon
        self.C = self.omega - self.omega * math.log(1 + self.omega / self.epsilon)
        
    def forward(self, pred, target):
        diff = torch.abs(pred - target)
        
        # Wing loss条件
        condition = diff < self.omega
        
        # 小误差区域：更敏感的对数损失
        loss_small = self.omega * torch.log(1 + diff / self.epsilon)
        
        # 大误差区域：线性损失
        loss_large = diff - self.C
        
        loss = torch.where(condition, loss_small, loss_large)
        return torch.mean(loss)

class AdaptiveFocalLoss(nn.Module):
    """自适应Focal Loss - 关注困难样本"""
    
    def __init__(self, alpha=2.0, gamma=2.0):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        
    def forward(self, pred, target):
        # 计算L2距离
        distances = torch.norm(pred - target, dim=-1)  # (B, num_keypoints)
        
        # 计算权重 (距离越大，权重越高)
        weights = torch.pow(distances / (distances.mean() + 1e-8), self.gamma)
        
        # 加权MSE损失
        mse_loss = torch.pow(distances, 2)
        focal_loss = self.alpha * weights * mse_loss
        
        return torch.mean(focal_loss)

class CombinedLoss(nn.Module):
    """组合损失函数"""
    
    def __init__(self, wing_weight=0.7, focal_weight=0.2, mse_weight=0.1):
        super().__init__()
        self.wing_loss = WingLoss(omega=8.0, epsilon=1.5)
        self.focal_loss = AdaptiveFocalLoss(alpha=1.5, gamma=2.0)
        self.mse_loss = nn.MSELoss()
        
        self.wing_weight = wing_weight
        self.focal_weight = focal_weight
        self.mse_weight = mse_weight
        
    def forward(self, pred, target):
        wing = self.wing_loss(pred, target)
        focal = self.focal_loss(pred, target)
        mse = self.mse_loss(pred, target)
        
        total_loss = (self.wing_weight * wing + 
                     self.focal_weight * focal + 
                     self.mse_weight * mse)
        
        return total_loss, {
            'wing': wing.item(),
            'focal': focal.item(),
            'mse': mse.item(),
            'total': total_loss.item()
        }

class CurriculumLearning:
    """课程学习策略"""
    
    def __init__(self, total_epochs=100):
        self.total_epochs = total_epochs
        
    def get_difficulty_level(self, epoch):
        """根据epoch返回难度级别"""
        progress = epoch / self.total_epochs
        
        if progress < 0.3:
            return 'easy'
        elif progress < 0.7:
            return 'medium'
        else:
            return 'hard'
    
    def get_augmentation_strength(self, epoch):
        """根据epoch返回数据增强强度"""
        progress = epoch / self.total_epochs
        
        # 逐渐增加增强强度
        rotation_range = 0.01 + progress * 0.04  # 0.01 -> 0.05 radians
        noise_std = 0.1 + progress * 0.4  # 0.1 -> 0.5 mm
        scale_range = 0.01 + progress * 0.02  # ±1% -> ±3%
        
        return {
            'rotation_range': rotation_range,
            'noise_std': noise_std,
            'scale_range': scale_range
        }

class AdvancedTrainer:
    """高级训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        self.curriculum = CurriculumLearning(total_epochs=100)
        
    def load_aligned_data(self):
        """加载对齐后的数据"""
        print("📦 加载F3对齐数据...")
        
        aligned_files = list(Path("data/processed").glob("f3_aligned_dataset_*.npz"))
        if not aligned_files:
            raise FileNotFoundError("未找到F3对齐数据集")
        
        latest_file = max(aligned_files, key=lambda x: x.stat().st_mtime)
        print(f"   使用数据: {latest_file}")
        
        data = np.load(str(latest_file), allow_pickle=True)
        point_clouds = np.array(data['point_clouds'], dtype=np.float32)
        keypoints = np.array(data['keypoints'], dtype=np.float32)
        
        print(f"✅ 数据加载完成: {point_clouds.shape}, {keypoints.shape}")
        
        # 数据划分
        indices = np.arange(len(point_clouds))
        train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
        train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
        
        print(f"   训练: {len(train_indices)}, 验证: {len(val_indices)}, 测试: {len(test_indices)}")
        return self.data
    
    def advanced_augmentation(self, point_clouds, keypoints, aug_params):
        """高级数据增强"""
        aug_pcs = []
        aug_kps = []
        
        for pc, kp in zip(point_clouds, keypoints):
            # 原始数据
            aug_pcs.append(pc)
            aug_kps.append(kp)
            
            # 自适应旋转增强
            for _ in range(3):
                angle = np.random.uniform(-aug_params['rotation_range'], aug_params['rotation_range'])
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                
                # 随机选择旋转轴
                axis = np.random.choice(['x', 'y', 'z'])
                if axis == 'x':
                    rotation = np.array([[1, 0, 0], [0, cos_a, -sin_a], [0, sin_a, cos_a]], dtype=np.float32)
                elif axis == 'y':
                    rotation = np.array([[cos_a, 0, sin_a], [0, 1, 0], [-sin_a, 0, cos_a]], dtype=np.float32)
                else:  # z轴
                    rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], dtype=np.float32)
                
                aug_pc = pc @ rotation.T
                aug_kp = kp @ rotation.T
                aug_pcs.append(aug_pc)
                aug_kps.append(aug_kp)
            
            # 自适应噪声增强
            for _ in range(2):
                noise_pc = pc + np.random.normal(0, aug_params['noise_std'], pc.shape).astype(np.float32)
                aug_pcs.append(noise_pc)
                aug_kps.append(kp)
            
            # 自适应缩放增强
            scale = np.random.uniform(1 - aug_params['scale_range'], 1 + aug_params['scale_range'])
            scaled_pc = pc * scale
            scaled_kp = kp * scale
            aug_pcs.append(scaled_pc)
            aug_kps.append(scaled_kp)
            
            # 弹性变形 (轻微)
            if np.random.random() < 0.3:  # 30%概率
                elastic_pc = self.elastic_deformation(pc, sigma=0.5, alpha=1.0)
                aug_pcs.append(elastic_pc)
                aug_kps.append(kp)  # 关键点不变形
        
        return aug_pcs, aug_kps
    
    def elastic_deformation(self, points, sigma=0.5, alpha=1.0):
        """弹性变形"""
        # 简化的弹性变形
        displacement = np.random.normal(0, sigma, points.shape).astype(np.float32)
        displacement = displacement * alpha
        
        # 应用高斯平滑
        from scipy.ndimage import gaussian_filter1d
        for i in range(3):
            displacement[:, i] = gaussian_filter1d(displacement[:, i], sigma=1.0)
        
        return points + displacement
    
    def train_with_advanced_strategies(self, epochs=100, lr=0.0008, k_shot=25):
        """使用高级策略训练"""
        print(f"\n🚀 高级训练策略实验")
        print(f"   参数: epochs={epochs}, lr={lr}, k_shot={k_shot}")
        print(f"   策略: Wing Loss + Focal Loss + 课程学习 + 高级增强")
        
        # 创建模型
        model = PointTransformerKeypointNet(num_keypoints=19, num_neighbors=16).to(self.device)
        
        # 高级优化器配置
        optimizer = torch.optim.AdamW(
            model.parameters(), 
            lr=lr, 
            weight_decay=1e-4,
            betas=(0.9, 0.999),
            eps=1e-8
        )
        
        # 学习率调度器 - 余弦退火 + 重启
        scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer, T_0=20, T_mult=2, eta_min=1e-6
        )
        
        # 组合损失函数
        criterion = CombinedLoss()
        
        # 训练状态
        best_val_error = float('inf')
        best_model_state = None
        patience = 0
        max_patience = 35
        
        train_losses = []
        val_errors = []
        loss_components = []
        
        print(f"   模型参数: {sum(p.numel() for p in model.parameters()):,}")
        
        for epoch in range(epochs):
            # 课程学习参数
            difficulty = self.curriculum.get_difficulty_level(epoch)
            aug_params = self.curriculum.get_augmentation_strength(epoch)
            
            # 训练阶段
            model.train()
            epoch_losses = []
            epoch_loss_components = []
            
            # 根据课程学习调整k_shot
            if difficulty == 'easy':
                current_k_shot = max(15, k_shot // 2)
            elif difficulty == 'medium':
                current_k_shot = k_shot
            else:  # hard
                current_k_shot = min(len(self.data['train']['point_clouds']), k_shot + 10)
            
            # 采样训练数据
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                min(current_k_shot, len(self.data['train']['point_clouds'])), 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 高级数据增强
            aug_pcs, aug_kps = self.advanced_augmentation(train_pcs, train_kps, aug_params)
            
            # 分批训练
            batch_size = 3  # 稍微减小以适应更复杂的增强
            for i in range(0, len(aug_pcs), batch_size):
                try:
                    batch_pcs = torch.FloatTensor(aug_pcs[i:i+batch_size]).to(self.device)
                    batch_kps = torch.FloatTensor(aug_kps[i:i+batch_size]).to(self.device)
                    
                    optimizer.zero_grad()
                    pred_kps = model(batch_pcs)
                    
                    # 组合损失
                    loss, loss_dict = criterion(pred_kps, batch_kps)
                    loss.backward()
                    
                    # 梯度裁剪
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    
                    optimizer.step()
                    
                    epoch_losses.append(loss.item())
                    epoch_loss_components.append(loss_dict)
                    
                    # 清理内存
                    del batch_pcs, batch_kps, pred_kps, loss
                    torch.cuda.empty_cache()
                    
                except Exception as e:
                    print(f"   批次训练错误: {e}")
                    torch.cuda.empty_cache()
                    continue
            
            scheduler.step()
            
            if epoch_losses:
                avg_loss = np.mean(epoch_losses)
                train_losses.append(avg_loss)
                
                # 平均损失组件
                avg_components = {}
                for key in epoch_loss_components[0].keys():
                    avg_components[key] = np.mean([comp[key] for comp in epoch_loss_components])
                loss_components.append(avg_components)
            else:
                avg_loss = 0
                train_losses.append(0)
                loss_components.append({'wing': 0, 'focal': 0, 'mse': 0, 'total': 0})
            
            # 验证阶段
            if epoch % 5 == 0:
                val_error = self.evaluate_model(model, 'val')
                val_errors.append(val_error)
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                    patience = 0
                else:
                    patience += 1
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f} (W:{avg_components.get('wing', 0):.2f}, "
                      f"F:{avg_components.get('focal', 0):.2f}, M:{avg_components.get('mse', 0):.2f}), "
                      f"Val={val_error:.3f}mm, LR={optimizer.param_groups[0]['lr']:.6f}, "
                      f"K={current_k_shot}, Diff={difficulty}, P={patience}")
                
                if patience >= max_patience:
                    print(f"早停在epoch {epoch}")
                    break
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, K={current_k_shot}, Diff={difficulty}")
        
        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)
            print(f"✅ 加载最佳模型 (验证误差: {best_val_error:.3f}mm)")
        
        self.model = model
        self.training_history = {
            'train_losses': train_losses,
            'val_errors': val_errors,
            'loss_components': loss_components,
            'best_val_error': best_val_error
        }
        
        return model, best_val_error
    
    def evaluate_model(self, model, split='test'):
        """评估模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            batch_size = 2
            for i in range(0, len(pcs), batch_size):
                try:
                    batch_pcs = torch.FloatTensor(pcs[i:i+batch_size]).to(self.device)
                    batch_kps = torch.FloatTensor(kps[i:i+batch_size]).to(self.device)
                    
                    pred_kps = model(batch_pcs)
                    
                    for j in range(len(batch_pcs)):
                        error = torch.mean(torch.norm(pred_kps[j] - batch_kps[j], dim=1))
                        total_error += error.item()
                        num_samples += 1
                    
                    del batch_pcs, batch_kps, pred_kps
                    torch.cuda.empty_cache()
                    
                except Exception as e:
                    print(f"   评估错误: {e}")
                    torch.cuda.empty_cache()
                    continue
        
        return total_error / num_samples if num_samples > 0 else float('inf')
    
    def save_advanced_model(self, model, val_error):
        """保存高级训练的模型"""
        output_dir = Path("trained_models/advanced_strategies")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_path = output_dir / f"advanced_pt_f3_{val_error:.3f}mm_{timestamp}.pth"
        
        torch.save({
            'model_state_dict': model.state_dict(),
            'model_config': {
                'num_keypoints': 19,
                'num_neighbors': 16,
                'architecture': 'PointTransformer_Advanced'
            },
            'training_history': self.training_history,
            'validation_error': val_error,
            'training_strategies': [
                'WingLoss + FocalLoss + MSE',
                'CurriculumLearning',
                'AdvancedAugmentation',
                'CosineAnnealingWarmRestarts'
            ],
            'timestamp': timestamp
        }, model_path)
        
        print(f"💾 高级模型已保存: {model_path}")
        return model_path

def run_advanced_training_experiment():
    """运行高级训练实验"""
    print("🚀 高级训练策略实验")
    print("=" * 60)
    print("策略: Wing Loss + Focal Loss + 课程学习 + 高级数据增强")
    
    # 创建高级训练器
    trainer = AdvancedTrainer()
    
    # 加载数据
    data = trainer.load_aligned_data()
    
    # 高级训练
    model, val_error = trainer.train_with_advanced_strategies(
        epochs=100, lr=0.0008, k_shot=25
    )
    
    # 测试模型
    test_error = trainer.evaluate_model(model, 'test')
    
    print(f"\n📊 高级训练结果:")
    print("=" * 40)
    print(f"验证误差: {val_error:.3f}mm")
    print(f"测试误差: {test_error:.3f}mm")
    
    # 与之前的Point Transformer对比
    baseline_pt_error = 8.13  # 之前的Point Transformer结果
    improvement = (baseline_pt_error - test_error) / baseline_pt_error * 100
    
    print(f"\n📈 与基础Point Transformer对比:")
    print(f"基础Point Transformer: {baseline_pt_error:.2f}mm")
    print(f"高级训练策略: {test_error:.2f}mm")
    print(f"策略改进: {improvement:+.1f}%")
    
    # 与医疗级目标对比
    medical_target = 5.0
    progress_to_medical = (medical_target - test_error) / medical_target * 100
    
    print(f"\n🎯 医疗级精度进展:")
    print(f"医疗级目标: {medical_target:.1f}mm")
    print(f"当前误差: {test_error:.2f}mm")
    if test_error <= medical_target:
        print("🎉 已达到医疗级精度！")
        status = "医疗级精度达成"
    else:
        remaining = test_error - medical_target
        print(f"距离目标: {remaining:.2f}mm")
        status = f"距离医疗级还需{remaining:.2f}mm"
    
    # 保存模型
    model_path = trainer.save_advanced_model(model, val_error)
    
    # 保存实验结果
    results = {
        "experiment_timestamp": datetime.now().isoformat(),
        "architecture": "PointTransformer_Advanced",
        "training_strategies": [
            "WingLoss + FocalLoss + MSE",
            "CurriculumLearning", 
            "AdvancedAugmentation",
            "CosineAnnealingWarmRestarts"
        ],
        "validation_error": float(val_error),
        "test_error": float(test_error),
        "baseline_pt_error": baseline_pt_error,
        "strategy_improvement_percent": float(improvement),
        "medical_target": medical_target,
        "medical_grade_achieved": test_error <= medical_target,
        "status": status,
        "model_path": str(model_path),
        "training_config": {
            "epochs": 100,
            "learning_rate": 0.0008,
            "k_shot": 25,
            "batch_size": 3,
            "loss_function": "Combined(Wing+Focal+MSE)",
            "scheduler": "CosineAnnealingWarmRestarts"
        }
    }
    
    # 保存结果
    results_dir = Path("results/advanced_training_experiments")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = results_dir / f"advanced_training_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 实验结果已保存: {results_file}")
    
    return trainer, results

if __name__ == "__main__":
    trainer, results = run_advanced_training_experiment()
