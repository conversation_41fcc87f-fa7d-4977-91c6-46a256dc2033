<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="1280" height="720" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="url(#headerGrad)"/>
  <text x="640" y="50" text-anchor="middle" fill="white" 
        font-family="SimHei, Arial, sans-serif" font-size="36" font-weight="bold">
    创新点二详解：Penalty Dice Loss 惩罚骰子损失
  </text>
  
  <!-- Problem Section -->
  <rect x="50" y="100" width="580" height="280" rx="15" fill="white" stroke="#ef4444" stroke-width="3"/>
  <text x="340" y="130" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="22" font-weight="bold">
    核心问题：类别不平衡挑战
  </text>
  
  <!-- Visual representation of imbalance -->
  <rect x="80" y="160" width="520" height="120" rx="10" fill="#fef2f2" stroke="#fca5a5" stroke-width="1"/>
  
  <!-- Large circle for background -->
  <circle cx="200" cy="220" r="50" fill="#f3f4f6" stroke="#9ca3af" stroke-width="2"/>
  <text x="200" y="280" text-anchor="middle" fill="#6b7280" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    背景点（多数类）
  </text>
  <text x="200" y="300" text-anchor="middle" fill="#6b7280" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    约99.5%的点
  </text>
  
  <!-- Small circles for keypoints -->
  <circle cx="400" cy="200" r="8" fill="#fca5a5" stroke="#ef4444" stroke-width="2"/>
  <circle cx="420" cy="210" r="8" fill="#fca5a5" stroke="#ef4444" stroke-width="2"/>
  <circle cx="430" cy="230" r="8" fill="#fca5a5" stroke="#ef4444" stroke-width="2"/>
  <circle cx="410" cy="240" r="8" fill="#fca5a5" stroke="#ef4444" stroke-width="2"/>
  
  <text x="425" y="260" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    关键点区域（少数类）
  </text>
  <text x="425" y="280" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="12">
    约0.5%的点
  </text>
  
  <!-- Problem description -->
  <rect x="70" y="300" width="540" height="70" rx="8" fill="#fee2e2" stroke="#fca5a5" stroke-width="1"/>
  <text x="340" y="325" text-anchor="middle" fill="#7f1d1d" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    传统损失函数的问题
  </text>
  <text x="80" y="345" fill="#7f1d1d" font-family="SimHei, Arial, sans-serif" font-size="13">
    • 交叉熵损失专注于多数类的高准确率，忽略小的关键区域
  </text>
  <text x="80" y="365" fill="#7f1d1d" font-family="SimHei, Arial, sans-serif" font-size="13">
    • 即使遗漏关键点区域，整体损失值仍然很低，网络无法有效学习
  </text>
  
  <!-- Solution Section -->
  <rect x="650" y="100" width="580" height="280" rx="15" fill="white" stroke="#10b981" stroke-width="3"/>
  <text x="940" y="130" text-anchor="middle" fill="#059669" 
        font-family="SimHei, Arial, sans-serif" font-size="22" font-weight="bold">
    解决方案：Penalty Dice Loss
  </text>
  
  <!-- Three components visualization -->
  <rect x="670" y="160" width="540" height="120" rx="10" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="940" y="185" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    三重损失函数设计
  </text>
  
  <!-- Component 1 -->
  <rect x="680" y="200" width="160" height="60" rx="5" fill="#fef2f2" stroke="#ef4444" stroke-width="1"/>
  <text x="760" y="220" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    交叉熵损失
  </text>
  <text x="760" y="240" text-anchor="middle" fill="#7f1d1d" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    基本分类准确性
  </text>
  <text x="760" y="255" text-anchor="middle" fill="#7f1d1d" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    提供优化梯度
  </text>
  
  <!-- Plus sign -->
  <text x="860" y="235" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="20" font-weight="bold">
    +
  </text>
  
  <!-- Component 2 -->
  <rect x="880" y="200" width="160" height="60" rx="5" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="960" y="220" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    Dice损失
  </text>
  <text x="960" y="240" text-anchor="middle" fill="#047857" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    区域重叠相似性
  </text>
  <text x="960" y="255" text-anchor="middle" fill="#047857" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    处理类别不平衡
  </text>
  
  <!-- Plus sign -->
  <text x="1060" y="235" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="20" font-weight="bold">
    +
  </text>
  
  <!-- Component 3 -->
  <rect x="1080" y="200" width="120" height="60" rx="5" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="1140" y="220" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    惩罚项
  </text>
  <text x="1140" y="240" text-anchor="middle" fill="#6b21a8" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    未检测区域
  </text>
  <text x="1140" y="255" text-anchor="middle" fill="#6b21a8" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    指数惩罚
  </text>
  
  <!-- Key innovation -->
  <rect x="670" y="300" width="540" height="70" rx="8" fill="#fef7ff" stroke="#a855f7" stroke-width="1"/>
  <text x="940" y="325" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    核心创新：显式未检测惩罚机制
  </text>
  <text x="680" y="345" fill="#6b21a8" font-family="SimHei, Arial, sans-serif" font-size="13">
    • 对完全遗漏的关键点区域施加指数级惩罚
  </text>
  <text x="680" y="365" fill="#6b21a8" font-family="SimHei, Arial, sans-serif" font-size="13">
    • 强制网络关注小而重要的区域，防止区域遗漏现象
  </text>
  
  <!-- Detailed Mechanism -->
  <rect x="50" y="400" width="1180" height="280" rx="15" fill="white" stroke="#7c3aed" stroke-width="3"/>
  <text x="640" y="430" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="24" font-weight="bold">
    工作机制详解
  </text>
  
  <!-- Step by step process -->
  <rect x="80" y="460" width="350" height="200" rx="10" fill="#fef2f2" stroke="#ef4444" stroke-width="2"/>
  <text x="255" y="485" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    步骤1：交叉熵损失计算
  </text>
  <text x="90" y="510" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="13">
    作用：确保基本的分类准确性
  </text>
  <text x="90" y="530" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 计算每个点的分类概率
  </text>
  <text x="90" y="550" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 提供标准的监督学习信号
  </text>
  <text x="90" y="570" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 处理表示良好的类别
  </text>
  <text x="90" y="600" fill="#dc2626" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    局限性：
  </text>
  <text x="90" y="620" fill="#dc2626" font-family="SimHei, Arial, sans-serif" font-size="11">
    • 偏向多数类（背景点）
  </text>
  <text x="90" y="640" fill="#dc2626" font-family="SimHei, Arial, sans-serif" font-size="11">
    • 对小区域关注不足
  </text>
  
  <rect x="465" y="460" width="350" height="200" rx="10" fill="#f0fdf4" stroke="#22c55e" stroke-width="2"/>
  <text x="640" y="485" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    步骤2：Dice损失增强
  </text>
  <text x="475" y="510" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="13">
    作用：测量区域重叠相似性
  </text>
  <text x="475" y="530" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 关注预测区域与真实区域的重叠
  </text>
  <text x="475" y="550" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 更好地处理类别不平衡
  </text>
  <text x="475" y="570" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 强调少数类的重要性
  </text>
  <text x="475" y="600" fill="#15803d" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    优势：
  </text>
  <text x="475" y="620" fill="#15803d" font-family="SimHei, Arial, sans-serif" font-size="11">
    • 区域感知的优化方式
  </text>
  <text x="475" y="640" fill="#15803d" font-family="SimHei, Arial, sans-serif" font-size="11">
    • 平衡不同类别的贡献
  </text>
  
  <rect x="850" y="460" width="350" height="200" rx="10" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="2"/>
  <text x="1025" y="485" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    步骤3：惩罚项强化
  </text>
  <text x="860" y="510" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="13">
    作用：显式惩罚未检测区域
  </text>
  <text x="860" y="530" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 计算预测与真实区域的并集与交集比
  </text>
  <text x="860" y="550" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 对遗漏区域施加指数级惩罚
  </text>
  <text x="860" y="570" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 强制网络关注所有关键区域
  </text>
  <text x="860" y="600" fill="#7c3aed" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    创新性：
  </text>
  <text x="860" y="620" fill="#7c3aed" font-family="SimHei, Arial, sans-serif" font-size="11">
    • 首次提出显式未检测惩罚
  </text>
  <text x="860" y="640" fill="#7c3aed" font-family="SimHei, Arial, sans-serif" font-size="11">
    • 防止完全区域遗漏
  </text>
  
  <!-- Results -->
  <rect x="80" y="680" width="1120" height="30" rx="8" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="1"/>
  <text x="640" y="700" text-anchor="middle" fill="#0c4a6e" 
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    实验效果：区域检测mIoU从62.59%显著提升至66.60%，在更低k值下实现稳健的关键点区域检测
  </text>
</svg>
