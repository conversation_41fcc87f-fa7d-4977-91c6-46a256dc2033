#!/usr/bin/env python3
"""
19关键点热力图可视化
展示基础19关键点模型的热力图效果
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from matplotlib.colors import LinearSegmentedColormap
from basic_19keypoints_system import BasicHeatmapPointNet19, extract_keypoints_from_heatmaps_19

def create_19kp_colormap():
    """创建19关键点专用的彩虹色配色方案"""
    colors = [
        '#F0F0F0',  # 浅灰 - 背景
        '#E6E6FA',  # 薰衣草色
        '#9370DB',  # 中紫色
        '#4169E1',  # 皇家蓝
        '#00BFFF',  # 深天蓝
        '#00FFFF',  # 青色
        '#00FF7F',  # 春绿色
        '#ADFF2F',  # 绿黄色
        '#FFFF00',  # 黄色
        '#FFD700',  # 金色
        '#FFA500',  # 橙色
        '#FF4500',  # 橙红色
        '#FF0000',  # 红色
        '#DC143C',  # 深红色
        '#8B0000'   # 暗红色
    ]
    return LinearSegmentedColormap.from_list('kp19_rainbow', colors, N=256)

def combine_19kp_heatmaps(pred_heatmaps, method='weighted'):
    """组合19个关键点的热力图"""
    
    if method == 'max':
        # 取每个点在所有关键点中的最大置信度
        combined = np.max(pred_heatmaps, axis=0)
    elif method == 'weighted':
        # 加权组合 - 给原始12个关键点更高权重
        weights = np.ones(19)
        weights[:12] = 1.2  # 原始12个关键点权重更高
        weights[12:] = 0.8  # 插值关键点权重稍低
        
        weighted_heatmaps = pred_heatmaps * weights[:, np.newaxis]
        combined = np.max(weighted_heatmaps, axis=0)
    elif method == 'sum':
        # 累加所有关键点的置信度
        combined = np.sum(pred_heatmaps, axis=0)
    
    # 归一化到[0,1]
    if np.max(combined) > 0:
        combined = combined / np.max(combined)
    
    return combined

def create_19kp_heatmap_visualization(point_cloud, pred_heatmaps, true_keypoints, 
                                    pred_keypoints, sampled_pc, sample_id):
    """创建19关键点热力图可视化"""
    
    print(f"🌈 Creating 19-keypoint heatmap visualization for sample {sample_id}")
    
    kp19_cmap = create_19kp_colormap()
    
    # 创建2x2子图
    fig = plt.figure(figsize=(20, 16))
    
    # 1. 组合热力图 - 加权方法
    ax1 = fig.add_subplot(2, 2, 1, projection='3d')
    
    combined_heatmap = combine_19kp_heatmaps(pred_heatmaps, 'weighted')
    
    # 采样用于显示
    if len(sampled_pc) > 6000:
        sample_indices = np.random.choice(len(sampled_pc), 6000, replace=False)
        display_pc = sampled_pc[sample_indices]
        display_heatmap = combined_heatmap[sample_indices]
    else:
        display_pc = sampled_pc
        display_heatmap = combined_heatmap
        sample_indices = None
    
    # 分层显示热力图
    bg_mask = display_heatmap < 0.1
    low_mask = (display_heatmap >= 0.1) & (display_heatmap < 0.3)
    mid_mask = (display_heatmap >= 0.3) & (display_heatmap < 0.6)
    high_mask = (display_heatmap >= 0.6) & (display_heatmap < 0.85)
    peak_mask = display_heatmap >= 0.85
    
    if np.any(bg_mask):
        ax1.scatter(display_pc[bg_mask, 0], display_pc[bg_mask, 1], display_pc[bg_mask, 2],
                   c=display_heatmap[bg_mask], cmap=kp19_cmap, s=0.5, alpha=0.3, vmin=0, vmax=1)
    
    if np.any(low_mask):
        ax1.scatter(display_pc[low_mask, 0], display_pc[low_mask, 1], display_pc[low_mask, 2],
                   c=display_heatmap[low_mask], cmap=kp19_cmap, s=1.5, alpha=0.6, vmin=0, vmax=1)
    
    if np.any(mid_mask):
        ax1.scatter(display_pc[mid_mask, 0], display_pc[mid_mask, 1], display_pc[mid_mask, 2],
                   c=display_heatmap[mid_mask], cmap=kp19_cmap, s=4, alpha=0.8, vmin=0, vmax=1)
    
    if np.any(high_mask):
        scatter = ax1.scatter(display_pc[high_mask, 0], display_pc[high_mask, 1], display_pc[high_mask, 2],
                            c=display_heatmap[high_mask], cmap=kp19_cmap, s=8, alpha=0.9, vmin=0, vmax=1)
    
    if np.any(peak_mask):
        ax1.scatter(display_pc[peak_mask, 0], display_pc[peak_mask, 1], display_pc[peak_mask, 2],
                   c='white', s=20, marker='o', 
                   edgecolor='darkred', linewidth=1, alpha=1.0, zorder=10)
    
    # 显示关键点
    ax1.scatter(true_keypoints[:, 0], true_keypoints[:, 1], true_keypoints[:, 2],
               c='yellow', s=150, marker='*', 
               edgecolor='black', linewidth=2, alpha=1.0, zorder=20, label='True (19)')
    
    ax1.scatter(pred_keypoints[:, 0], pred_keypoints[:, 1], pred_keypoints[:, 2],
               c='red', s=120, marker='x', 
               linewidth=3, alpha=1.0, zorder=20, label='Predicted (19)')
    
    # 连接线
    for j in range(len(true_keypoints)):
        ax1.plot([true_keypoints[j, 0], pred_keypoints[j, 0]], 
                [true_keypoints[j, 1], pred_keypoints[j, 1]], 
                [true_keypoints[j, 2], pred_keypoints[j, 2]], 
                'w-', alpha=0.7, linewidth=1, zorder=15)
    
    errors = [np.linalg.norm(pred_keypoints[j] - true_keypoints[j]) for j in range(len(true_keypoints))]
    avg_error = np.mean(errors)
    
    ax1.set_title(f'Combined 19-Keypoint Heatmap\nSample {sample_id}\nAvg Error: {avg_error:.1f}mm')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.view_init(elev=20, azim=45)
    
    # 2. 原始12个关键点的热力图
    ax2 = fig.add_subplot(2, 2, 2, projection='3d')
    
    original_12_heatmaps = pred_heatmaps[:12]  # 前12个关键点
    combined_12 = np.max(original_12_heatmaps, axis=0)
    
    if np.max(combined_12) > 0:
        combined_12 = combined_12 / np.max(combined_12)
    
    if sample_indices is not None:
        display_heatmap_12 = combined_12[sample_indices]
    else:
        display_heatmap_12 = combined_12
    
    scatter2 = ax2.scatter(display_pc[:, 0], display_pc[:, 1], display_pc[:, 2],
                          c=display_heatmap_12, cmap=kp19_cmap, s=2, alpha=0.7, vmin=0, vmax=1)
    
    # 显示前12个关键点
    ax2.scatter(true_keypoints[:12, 0], true_keypoints[:12, 1], true_keypoints[:12, 2],
               c='yellow', s=150, marker='*', 
               edgecolor='black', linewidth=2, alpha=1.0, zorder=20)
    
    ax2.scatter(pred_keypoints[:12, 0], pred_keypoints[:12, 1], pred_keypoints[:12, 2],
               c='red', s=120, marker='x', 
               linewidth=3, alpha=1.0, zorder=20)
    
    errors_12 = [np.linalg.norm(pred_keypoints[j] - true_keypoints[j]) for j in range(12)]
    avg_error_12 = np.mean(errors_12)
    
    ax2.set_title(f'Original 12 Keypoints\nAvg Error: {avg_error_12:.1f}mm')
    ax2.grid(True, alpha=0.3)
    ax2.view_init(elev=20, azim=45)
    
    # 3. 新增7个关键点的热力图
    ax3 = fig.add_subplot(2, 2, 3, projection='3d')
    
    new_7_heatmaps = pred_heatmaps[12:]  # 后7个关键点
    combined_7 = np.max(new_7_heatmaps, axis=0)
    
    if np.max(combined_7) > 0:
        combined_7 = combined_7 / np.max(combined_7)
    
    if sample_indices is not None:
        display_heatmap_7 = combined_7[sample_indices]
    else:
        display_heatmap_7 = combined_7
    
    scatter3 = ax3.scatter(display_pc[:, 0], display_pc[:, 1], display_pc[:, 2],
                          c=display_heatmap_7, cmap=kp19_cmap, s=2, alpha=0.7, vmin=0, vmax=1)
    
    # 显示后7个关键点
    ax3.scatter(true_keypoints[12:, 0], true_keypoints[12:, 1], true_keypoints[12:, 2],
               c='yellow', s=150, marker='*', 
               edgecolor='black', linewidth=2, alpha=1.0, zorder=20)
    
    ax3.scatter(pred_keypoints[12:, 0], pred_keypoints[12:, 1], pred_keypoints[12:, 2],
               c='red', s=120, marker='x', 
               linewidth=3, alpha=1.0, zorder=20)
    
    errors_7 = [np.linalg.norm(pred_keypoints[j] - true_keypoints[j]) for j in range(12, 19)]
    avg_error_7 = np.mean(errors_7)
    
    ax3.set_title(f'Additional 7 Keypoints\nAvg Error: {avg_error_7:.1f}mm')
    ax3.grid(True, alpha=0.3)
    ax3.view_init(elev=20, azim=45)
    
    # 4. 统计信息和误差分析
    ax4 = fig.add_subplot(2, 2, 4)
    
    # 误差分布柱状图
    keypoint_indices = np.arange(19)
    colors = ['blue' if i < 12 else 'orange' for i in range(19)]
    
    bars = ax4.bar(keypoint_indices, errors, color=colors, alpha=0.7)
    ax4.set_xlabel('Keypoint Index')
    ax4.set_ylabel('Error (mm)')
    ax4.set_title('Individual Keypoint Errors')
    ax4.grid(True, alpha=0.3)
    
    # 添加分组标签
    ax4.axvline(x=11.5, color='red', linestyle='--', alpha=0.7)
    ax4.text(5.5, max(errors)*0.9, 'Original 12', ha='center', fontweight='bold', 
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    ax4.text(15.5, max(errors)*0.9, 'Additional 7', ha='center', fontweight='bold',
             bbox=dict(boxstyle='round', facecolor='orange', alpha=0.8))
    
    # 添加平均线
    ax4.axhline(y=avg_error_12, color='blue', linestyle='-', alpha=0.7, 
               label=f'Avg Original: {avg_error_12:.1f}mm')
    ax4.axhline(y=avg_error_7, color='orange', linestyle='-', alpha=0.7,
               label=f'Avg Additional: {avg_error_7:.1f}mm')
    ax4.axhline(y=avg_error, color='red', linestyle='-', alpha=0.7,
               label=f'Overall Avg: {avg_error:.1f}mm')
    ax4.legend()
    
    # 添加颜色条
    if 'scatter' in locals():
        cbar = plt.colorbar(scatter, ax=[ax1, ax2, ax3], shrink=0.6, aspect=30)
        cbar.set_label('Heatmap Confidence', fontsize=12, fontweight='bold')
    
    plt.suptitle(f'19-Keypoint Heatmap Analysis - Sample {sample_id}\n'
                f'Basic Model with Extended Anatomical Coverage', 
                fontsize=16, fontweight='bold')
    
    plt.tight_layout(rect=[0, 0, 0.95, 0.92])
    
    # 保存
    filename = f'19kp_heatmap_analysis_{sample_id}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"   🌈 19-keypoint heatmap analysis saved: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🌈 19-Keypoint Heatmap Visualization")
    print("Analyzing basic 19-keypoint model heatmap performance")
    print("=" * 60)
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints_12 = male_data['keypoints']
    
    # 创建19关键点数据
    keypoints_19_list = []
    for kp_12 in keypoints_12:
        kp_19 = np.zeros((19, 3))
        kp_19[:12] = kp_12
        kp_19[12] = (kp_12[0] + kp_12[1]) / 2  # L-ASIS和R-ASIS的中点
        kp_19[13] = (kp_12[2] + kp_12[3]) / 2  # L-PSIS和R-PSIS的中点
        kp_19[14] = (kp_12[4] + kp_12[5]) / 2  # L-IC和R-IC的中点
        kp_19[15] = (kp_12[7] + kp_12[8]) / 2  # L-SIJ和R-SIJ的中点
        kp_19[16] = (kp_12[9] + kp_12[10]) / 2  # L-IS和R-IS的中点
        kp_19[17] = (kp_12[6] + kp_12[11]) / 2  # SP和CT的中点
        kp_19[18] = np.mean(kp_12, axis=0)      # 所有关键点的重心
        
        for i in range(12, 19):
            kp_19[i] += np.random.normal(0, 1.5, 3)
        
        keypoints_19_list.append(kp_19)
    
    keypoints_19 = np.array(keypoints_19_list)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = BasicHeatmapPointNet19(input_dim=3, num_keypoints=19).to(device)
    
    try:
        model.load_state_dict(torch.load('best_basic_19kp_model.pth', map_location=device))
        model.eval()
        print("✅ 19-keypoint model loaded successfully")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # 选择样本进行展示
    demo_samples = [0, 1, 2]  # 展示前3个样本
    
    for sample_idx in demo_samples:
        sample_id = sample_ids[sample_idx]
        point_cloud = point_clouds[sample_idx]
        true_keypoints = keypoints_19[sample_idx]
        
        print(f"\n🌈 Processing sample: {sample_id}")
        
        # 采样点云用于预测
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            pc_sampled = point_cloud[indices]
        else:
            pc_sampled = point_cloud
        
        # 预测
        pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
        
        with torch.no_grad():
            pred_heatmaps = model(pc_tensor)
        
        # 获取热力图和关键点
        pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze()  # [19, N]
        pred_keypoints, confidences = extract_keypoints_from_heatmaps_19(
            pred_heatmaps_np, pc_sampled
        )
        
        # 计算误差
        errors = [np.linalg.norm(pred_keypoints[i] - true_keypoints[i]) 
                 for i in range(len(true_keypoints))]
        
        print(f"   📊 Point cloud size: {len(point_cloud)}")
        print(f"   🎯 Average error: {np.mean(errors):.2f}mm")
        print(f"   📈 Average confidence: {np.mean(confidences):.3f}")
        print(f"   🔍 Original 12 KP error: {np.mean(errors[:12]):.2f}mm")
        print(f"   🔍 Additional 7 KP error: {np.mean(errors[12:]):.2f}mm")
        
        # 创建热力图可视化
        create_19kp_heatmap_visualization(
            point_cloud, pred_heatmaps_np, true_keypoints, 
            pred_keypoints, pc_sampled, sample_id
        )
    
    print(f"\n🎉 19-Keypoint Heatmap Visualization Complete!")
    print("✅ Detailed heatmap analysis for each sample")
    print("✅ Comparison between original 12 and additional 7 keypoints")
    print("✅ Combined heatmap visualization")
    print("✅ Individual keypoint error analysis")

if __name__ == "__main__":
    main()
