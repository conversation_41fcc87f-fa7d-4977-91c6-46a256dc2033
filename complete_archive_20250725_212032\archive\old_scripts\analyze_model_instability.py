#!/usr/bin/env python3
"""
分析模型不稳定性问题
找出为什么某些样本表现好，某些样本表现差
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps

def analyze_sample_characteristics(point_cloud, keypoints, sample_id):
    """分析样本特征"""
    
    # 点云特征
    pc_center = np.mean(point_cloud, axis=0)
    pc_std = np.std(point_cloud, axis=0)
    pc_range = np.max(point_cloud, axis=0) - np.min(point_cloud, axis=0)
    pc_density = len(point_cloud) / (pc_range[0] * pc_range[1] * pc_range[2])
    
    # 关键点特征
    kp_center = np.mean(keypoints, axis=0)
    kp_std = np.std(keypoints, axis=0)
    kp_range = np.max(keypoints, axis=0) - np.min(keypoints, axis=0)
    
    # 关键点分布
    kp_spread = np.mean([np.linalg.norm(kp - kp_center) for kp in keypoints])
    
    # 点云-关键点对齐
    center_offset = np.linalg.norm(pc_center - kp_center)
    
    return {
        'sample_id': sample_id,
        'pc_size': len(point_cloud),
        'pc_center': pc_center,
        'pc_std': pc_std,
        'pc_range': pc_range,
        'pc_density': pc_density,
        'kp_center': kp_center,
        'kp_std': kp_std,
        'kp_range': kp_range,
        'kp_spread': kp_spread,
        'center_offset': center_offset
    }

def evaluate_model_on_samples(model, device, data, num_samples=10):
    """评估模型在多个样本上的表现"""
    
    sample_ids = data['sample_ids']
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    
    results = []
    
    for i in range(min(num_samples, len(sample_ids))):
        sample_id = sample_ids[i]
        point_cloud = point_clouds[i]
        true_keypoints = keypoints[i]
        
        print(f"📊 Evaluating sample {i+1}/{num_samples}: {sample_id}")
        
        # 分析样本特征
        characteristics = analyze_sample_characteristics(point_cloud, true_keypoints, sample_id)
        
        # 采样点云用于预测
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            pc_sampled = point_cloud[indices]
        else:
            pc_sampled = point_cloud
        
        # 预测关键点
        pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
        
        with torch.no_grad():
            pred_heatmaps = model(pc_tensor)
        
        pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T
        pred_keypoints, confidences = extract_keypoints_from_heatmaps(
            pred_heatmaps_np.T, pc_sampled
        )
        
        # 计算误差
        errors = [np.linalg.norm(pred_keypoints[j] - true_keypoints[j]) 
                 for j in range(len(true_keypoints))]
        
        avg_error = np.mean(errors)
        max_error = np.max(errors)
        accuracy_5mm = np.sum(np.array(errors) <= 5) / len(errors) * 100
        
        result = {
            **characteristics,
            'avg_error': avg_error,
            'max_error': max_error,
            'accuracy_5mm': accuracy_5mm,
            'errors': errors,
            'confidences': confidences,
            'pred_keypoints': pred_keypoints,
            'true_keypoints': true_keypoints
        }
        
        results.append(result)
        
        print(f"   Avg Error: {avg_error:.2f}mm, Accuracy ≤5mm: {accuracy_5mm:.1f}%")
    
    return results

def analyze_performance_patterns(results):
    """分析性能模式，找出影响因素"""
    
    print(f"\n🔍 Performance Pattern Analysis")
    print("=" * 60)
    
    # 按性能分组
    good_samples = [r for r in results if r['avg_error'] < 5.0]
    poor_samples = [r for r in results if r['avg_error'] > 8.0]
    medium_samples = [r for r in results if 5.0 <= r['avg_error'] <= 8.0]
    
    print(f"📊 Performance Distribution:")
    print(f"   Good (< 5mm): {len(good_samples)} samples")
    print(f"   Medium (5-8mm): {len(medium_samples)} samples")
    print(f"   Poor (> 8mm): {len(poor_samples)} samples")
    
    if len(good_samples) > 0 and len(poor_samples) > 0:
        print(f"\n🔍 Comparing Good vs Poor Samples:")
        
        # 比较特征
        features = ['pc_size', 'pc_density', 'kp_spread', 'center_offset']
        
        for feature in features:
            good_values = [s[feature] for s in good_samples]
            poor_values = [s[feature] for s in poor_samples]
            
            good_mean = np.mean(good_values)
            poor_mean = np.mean(poor_values)
            
            print(f"   {feature}:")
            print(f"     Good samples: {good_mean:.2f}")
            print(f"     Poor samples: {poor_mean:.2f}")
            print(f"     Difference: {abs(good_mean - poor_mean):.2f}")
    
    # 相关性分析
    print(f"\n📈 Correlation Analysis:")
    
    avg_errors = [r['avg_error'] for r in results]
    
    correlations = {}
    for feature in ['pc_size', 'pc_density', 'kp_spread', 'center_offset']:
        values = [r[feature] for r in results]
        correlation = np.corrcoef(avg_errors, values)[0, 1]
        correlations[feature] = correlation
        print(f"   {feature} vs avg_error: {correlation:.3f}")
    
    return correlations

def create_instability_visualization(results):
    """创建不稳定性可视化"""
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 误差分布
    ax1 = axes[0, 0]
    avg_errors = [r['avg_error'] for r in results]
    sample_ids = [r['sample_id'] for r in results]
    
    colors = ['green' if e < 5 else 'orange' if e < 8 else 'red' for e in avg_errors]
    bars = ax1.bar(range(len(avg_errors)), avg_errors, color=colors, alpha=0.7)
    ax1.set_xlabel('Sample Index')
    ax1.set_ylabel('Average Error (mm)')
    ax1.set_title('Error Distribution Across Samples')
    ax1.grid(True, alpha=0.3)
    
    # 添加样本ID标签
    for i, (bar, sample_id) in enumerate(zip(bars, sample_ids)):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                str(sample_id)[-3:], ha='center', va='bottom', fontsize=8, rotation=45)
    
    # 2. 点云大小 vs 误差
    ax2 = axes[0, 1]
    pc_sizes = [r['pc_size'] for r in results]
    ax2.scatter(pc_sizes, avg_errors, c=colors, alpha=0.7, s=100)
    ax2.set_xlabel('Point Cloud Size')
    ax2.set_ylabel('Average Error (mm)')
    ax2.set_title('Point Cloud Size vs Error')
    ax2.grid(True, alpha=0.3)
    
    # 3. 关键点分布 vs 误差
    ax3 = axes[0, 2]
    kp_spreads = [r['kp_spread'] for r in results]
    ax3.scatter(kp_spreads, avg_errors, c=colors, alpha=0.7, s=100)
    ax3.set_xlabel('Keypoint Spread')
    ax3.set_ylabel('Average Error (mm)')
    ax3.set_title('Keypoint Spread vs Error')
    ax3.grid(True, alpha=0.3)
    
    # 4. 中心偏移 vs 误差
    ax4 = axes[1, 0]
    center_offsets = [r['center_offset'] for r in results]
    ax4.scatter(center_offsets, avg_errors, c=colors, alpha=0.7, s=100)
    ax4.set_xlabel('Center Offset')
    ax4.set_ylabel('Average Error (mm)')
    ax4.set_title('Center Offset vs Error')
    ax4.grid(True, alpha=0.3)
    
    # 5. 准确率分布
    ax5 = axes[1, 1]
    accuracies = [r['accuracy_5mm'] for r in results]
    ax5.bar(range(len(accuracies)), accuracies, color=colors, alpha=0.7)
    ax5.set_xlabel('Sample Index')
    ax5.set_ylabel('Accuracy ≤5mm (%)')
    ax5.set_title('Accuracy Distribution')
    ax5.grid(True, alpha=0.3)
    
    # 6. 误差vs准确率
    ax6 = axes[1, 2]
    ax6.scatter(avg_errors, accuracies, c=colors, alpha=0.7, s=100)
    ax6.set_xlabel('Average Error (mm)')
    ax6.set_ylabel('Accuracy ≤5mm (%)')
    ax6.set_title('Error vs Accuracy')
    ax6.grid(True, alpha=0.3)
    
    plt.suptitle('Model Instability Analysis\nIdentifying Factors Affecting Performance', 
                fontsize=16, fontweight='bold')
    plt.tight_layout(rect=[0, 0, 1, 0.93])
    
    filename = 'model_instability_analysis.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 Instability analysis saved: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🔍 Model Instability Analysis")
    print("Identifying why performance varies across samples")
    print("=" * 60)
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model.eval()
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # 评估多个样本
    results = evaluate_model_on_samples(model, device, male_data, num_samples=10)
    
    # 分析性能模式
    correlations = analyze_performance_patterns(results)
    
    # 创建可视化
    create_instability_visualization(results)
    
    # 提出改进建议
    print(f"\n💡 Improvement Recommendations:")
    
    if abs(correlations.get('pc_size', 0)) > 0.3:
        print("1. 🔧 Point cloud size affects performance - consider adaptive sampling")
    
    if abs(correlations.get('center_offset', 0)) > 0.3:
        print("2. 🔧 Center alignment affects performance - improve preprocessing")
    
    if abs(correlations.get('kp_spread', 0)) > 0.3:
        print("3. 🔧 Keypoint distribution affects performance - add data augmentation")
    
    print("4. 🔧 Consider ensemble methods to improve stability")
    print("5. 🔧 Add more training data for edge cases")
    print("6. 🔧 Implement robust loss functions")
    print("7. 🔧 Use test-time augmentation")

if __name__ == "__main__":
    main()
