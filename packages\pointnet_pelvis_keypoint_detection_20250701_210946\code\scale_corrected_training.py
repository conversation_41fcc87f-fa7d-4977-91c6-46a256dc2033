"""
尺度校正训练脚本
基于验证结果的改进训练
"""

import torch
import torch.optim as optim
import numpy as np
from tqdm import tqdm
from pathlib import Path
import matplotlib.pyplot as plt
import json

from scale_corrected_model import ScaleCorrectedPointNet, ScaleCorrectedLoss, AdaptiveScaleCorrector, apply_post_scale_correction
from improved_data_loader import ImprovedDataLoader
from save_best_model import BestSimplePointNet

class ScaleCorrectedTrainer:
    """尺度校正训练器"""
    
    def __init__(self, data_root="output/training_fixed", batch_size=2):
        self.data_root = data_root
        self.batch_size = batch_size
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 创建输出目录
        self.output_dir = Path("output/scale_corrected_training")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🚀 尺度校正训练器初始化")
        print(f"Device: {self.device}, Batch size: {batch_size}")
        
    def load_data(self):
        """加载数据"""
        print("加载训练数据...")
        
        data_loader_manager = ImprovedDataLoader(
            data_root=self.data_root,
            batch_size=self.batch_size,
            num_workers=0,
            num_points=512
        )
        
        train_loader, val_loader = data_loader_manager.create_dataloaders(train_ratio=0.8)
        
        print(f"训练样本: {len(train_loader)} batches")
        print(f"验证样本: {len(val_loader)} batches")
        
        return train_loader, val_loader
    
    def train_baseline_with_post_correction(self, num_epochs=15):
        """训练基础模型 + 后处理尺度校正"""
        print("🔧 训练基础模型 + 后处理尺度校正...")
        
        # 创建基础模型
        model = BestSimplePointNet(num_keypoints=57).to(self.device)
        criterion = torch.nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=0.001)
        
        # 加载数据
        train_loader, val_loader = self.load_data()
        
        # 训练历史
        history = {
            'train_loss': [],
            'val_loss': [],
            'mean_error': [],
            'mean_error_corrected': [],
            'accuracy_5mm': [],
            'accuracy_5mm_corrected': []
        }
        
        best_accuracy = 0.0
        best_accuracy_corrected = 0.0
        
        for epoch in range(1, num_epochs + 1):
            print(f"\nEpoch {epoch}/{num_epochs}")
            
            # 训练
            model.train()
            train_loss = 0.0
            
            for point_cloud, keypoints in tqdm(train_loader, desc="Training"):
                point_cloud = point_cloud.to(self.device)
                keypoints = keypoints.to(self.device)
                
                optimizer.zero_grad()
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
            
            avg_train_loss = train_loss / len(train_loader)
            
            # 验证
            model.eval()
            val_loss = 0.0
            all_distances = []
            all_distances_corrected = []
            
            with torch.no_grad():
                for point_cloud, keypoints in val_loader:
                    point_cloud = point_cloud.to(self.device)
                    keypoints = keypoints.to(self.device)
                    
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    # 原始预测误差
                    distances = torch.norm(pred_keypoints - keypoints, dim=2)
                    all_distances.append(distances.cpu())
                    
                    # 尺度校正后的误差
                    corrected_pred = apply_post_scale_correction(pred_keypoints, scale_factor=1.048)
                    distances_corrected = torch.norm(corrected_pred - keypoints, dim=2)
                    all_distances_corrected.append(distances_corrected.cpu())
            
            # 计算性能指标
            avg_val_loss = val_loss / len(val_loader)
            
            all_distances = torch.cat(all_distances, dim=0).flatten()
            all_distances_corrected = torch.cat(all_distances_corrected, dim=0).flatten()
            
            mean_error = torch.mean(all_distances).item()
            mean_error_corrected = torch.mean(all_distances_corrected).item()
            
            accuracy_5mm = (all_distances <= 5.0).float().mean().item() * 100
            accuracy_5mm_corrected = (all_distances_corrected <= 5.0).float().mean().item() * 100
            
            # 记录历史
            history['train_loss'].append(avg_train_loss)
            history['val_loss'].append(avg_val_loss)
            history['mean_error'].append(mean_error)
            history['mean_error_corrected'].append(mean_error_corrected)
            history['accuracy_5mm'].append(accuracy_5mm)
            history['accuracy_5mm_corrected'].append(accuracy_5mm_corrected)
            
            # 打印结果
            print(f"Train Loss: {avg_train_loss:.3f}, Val Loss: {avg_val_loss:.3f}")
            print(f"原始 - 平均误差: {mean_error:.2f}mm, 5mm准确率: {accuracy_5mm:.1f}%")
            print(f"校正 - 平均误差: {mean_error_corrected:.2f}mm, 5mm准确率: {accuracy_5mm_corrected:.1f}%")
            print(f"改进: 误差减少 {mean_error - mean_error_corrected:.2f}mm, 准确率提升 {accuracy_5mm_corrected - accuracy_5mm:.1f}%")
            
            # 保存最佳模型
            if accuracy_5mm > best_accuracy:
                best_accuracy = accuracy_5mm
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'accuracy_5mm': accuracy_5mm,
                    'accuracy_5mm_corrected': accuracy_5mm_corrected,
                    'mean_error': mean_error,
                    'mean_error_corrected': mean_error_corrected
                }, self.output_dir / 'best_baseline_model.pth')
            
            if accuracy_5mm_corrected > best_accuracy_corrected:
                best_accuracy_corrected = accuracy_5mm_corrected
        
        print(f"\n训练完成!")
        print(f"最佳原始5mm准确率: {best_accuracy:.1f}%")
        print(f"最佳校正5mm准确率: {best_accuracy_corrected:.1f}%")
        print(f"尺度校正带来的改进: {best_accuracy_corrected - best_accuracy:.1f}%")
        
        return model, history
    
    def train_integrated_scale_model(self, num_epochs=15):
        """训练集成尺度校正的模型"""
        print("🔧 训练集成尺度校正模型...")
        
        # 创建尺度校正模型
        model = ScaleCorrectedPointNet(num_keypoints=57, scale_correction=True).to(self.device)
        criterion = ScaleCorrectedLoss(coord_weight=1.0, scale_weight=0.1, range_weight=0.05)
        optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
        
        # 加载数据
        train_loader, val_loader = self.load_data()
        
        # 自适应尺度校正器
        adaptive_corrector = AdaptiveScaleCorrector()
        
        # 训练历史
        history = {
            'train_loss': [],
            'val_loss': [],
            'mean_error': [],
            'accuracy_5mm': [],
            'scale_factors': [],
            'loss_components': []
        }
        
        best_accuracy = 0.0
        
        for epoch in range(1, num_epochs + 1):
            print(f"\nEpoch {epoch}/{num_epochs}")
            
            # 训练
            model.train()
            train_loss = 0.0
            loss_components = {'coord_loss': 0.0, 'scale_loss': 0.0, 'range_loss': 0.0}
            
            for point_cloud, keypoints in tqdm(train_loader, desc="Training"):
                point_cloud = point_cloud.to(self.device)
                keypoints = keypoints.to(self.device)
                
                optimizer.zero_grad()
                
                output = model(point_cloud)
                if isinstance(output, tuple):
                    pred_keypoints, pred_scale = output
                    loss, loss_dict = criterion(pred_keypoints, keypoints, pred_scale)
                else:
                    pred_keypoints = output
                    loss, loss_dict = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                # 更新自适应校正器
                adaptive_corrector.update_scale_factor(pred_keypoints.detach(), keypoints)
                
                train_loss += loss.item()
                for key, value in loss_dict.items():
                    if key in loss_components:
                        loss_components[key] += value
            
            avg_train_loss = train_loss / len(train_loader)
            for key in loss_components:
                loss_components[key] /= len(train_loader)
            
            # 验证
            model.eval()
            val_loss = 0.0
            all_distances = []
            
            with torch.no_grad():
                for point_cloud, keypoints in val_loader:
                    point_cloud = point_cloud.to(self.device)
                    keypoints = keypoints.to(self.device)
                    
                    output = model(point_cloud)
                    if isinstance(output, tuple):
                        pred_keypoints, pred_scale = output
                        loss, _ = criterion(pred_keypoints, keypoints, pred_scale)
                    else:
                        pred_keypoints = output
                        loss, _ = criterion(pred_keypoints, keypoints)
                    
                    val_loss += loss.item()
                    
                    distances = torch.norm(pred_keypoints - keypoints, dim=2)
                    all_distances.append(distances.cpu())
            
            # 计算性能指标
            avg_val_loss = val_loss / len(val_loader)
            all_distances = torch.cat(all_distances, dim=0).flatten()
            
            mean_error = torch.mean(all_distances).item()
            accuracy_5mm = (all_distances <= 5.0).float().mean().item() * 100
            current_scale = adaptive_corrector.get_current_scale_factor()
            
            # 记录历史
            history['train_loss'].append(avg_train_loss)
            history['val_loss'].append(avg_val_loss)
            history['mean_error'].append(mean_error)
            history['accuracy_5mm'].append(accuracy_5mm)
            history['scale_factors'].append(current_scale)
            history['loss_components'].append(loss_components.copy())
            
            # 打印结果
            print(f"Train Loss: {avg_train_loss:.3f}, Val Loss: {avg_val_loss:.3f}")
            print(f"平均误差: {mean_error:.2f}mm, 5mm准确率: {accuracy_5mm:.1f}%")
            print(f"当前自适应尺度因子: {current_scale:.3f}")
            print(f"损失组件 - 坐标: {loss_components['coord_loss']:.3f}, "
                  f"尺度: {loss_components['scale_loss']:.3f}, 范围: {loss_components['range_loss']:.3f}")
            
            # 保存最佳模型
            if accuracy_5mm > best_accuracy:
                best_accuracy = accuracy_5mm
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'accuracy_5mm': accuracy_5mm,
                    'mean_error': mean_error,
                    'scale_factor': current_scale
                }, self.output_dir / 'best_integrated_model.pth')
                
                print(f"🎉 新的最佳模型! 5mm准确率: {best_accuracy:.1f}%")
        
        print(f"\n训练完成! 最佳5mm准确率: {best_accuracy:.1f}%")
        
        return model, history
    
    def compare_approaches(self):
        """比较不同方法"""
        print("🔄 开始方法对比...")
        
        results = {}
        
        # 方法1: 基础模型 + 后处理校正
        print("\n" + "="*50)
        print("方法1: 基础模型 + 后处理尺度校正")
        print("="*50)
        
        try:
            model1, history1 = self.train_baseline_with_post_correction(num_epochs=12)
            results['baseline_post_correction'] = {
                'description': '基础模型 + 后处理校正',
                'best_accuracy_original': max(history1['accuracy_5mm']),
                'best_accuracy_corrected': max(history1['accuracy_5mm_corrected']),
                'improvement': max(history1['accuracy_5mm_corrected']) - max(history1['accuracy_5mm']),
                'history': history1
            }
        except Exception as e:
            print(f"❌ 方法1训练失败: {e}")
            results['baseline_post_correction'] = {'error': str(e)}
        
        # 方法2: 集成尺度校正模型
        print("\n" + "="*50)
        print("方法2: 集成尺度校正模型")
        print("="*50)
        
        try:
            model2, history2 = self.train_integrated_scale_model(num_epochs=12)
            results['integrated_scale_correction'] = {
                'description': '集成尺度校正模型',
                'best_accuracy': max(history2['accuracy_5mm']),
                'final_scale_factor': history2['scale_factors'][-1],
                'history': history2
            }
        except Exception as e:
            print(f"❌ 方法2训练失败: {e}")
            results['integrated_scale_correction'] = {'error': str(e)}
        
        # 生成对比报告
        self.generate_comparison_report(results)
        
        return results
    
    def generate_comparison_report(self, results):
        """生成对比报告"""
        print(f"\n📊 方法对比报告")
        print("=" * 60)
        
        for method, result in results.items():
            if 'error' in result:
                print(f"{result.get('description', method)}: 训练失败")
            else:
                print(f"{result['description']}:")
                if 'best_accuracy_corrected' in result:
                    print(f"  原始最佳5mm准确率: {result['best_accuracy_original']:.1f}%")
                    print(f"  校正最佳5mm准确率: {result['best_accuracy_corrected']:.1f}%")
                    print(f"  改进幅度: {result['improvement']:.1f}%")
                else:
                    print(f"  最佳5mm准确率: {result['best_accuracy']:.1f}%")
                    if 'final_scale_factor' in result:
                        print(f"  最终尺度因子: {result['final_scale_factor']:.3f}")
        
        # 保存详细报告
        with open(self.output_dir / 'comparison_report.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n详细报告保存在: {self.output_dir}/comparison_report.json")

def main():
    """主函数"""
    # 先测试尺度校正模型
    print("🧪 测试尺度校正模型...")
    from scale_corrected_model import test_scale_correction
    test_scale_correction()
    
    # 创建训练器并进行对比
    trainer = ScaleCorrectedTrainer(
        data_root="output/training_fixed",
        batch_size=2
    )
    
    results = trainer.compare_approaches()
    
    print("🎉 尺度校正训练完成!")

if __name__ == "__main__":
    main()
