#!/usr/bin/env python3
"""
Phase 2: 注意力机制架构训练
基于12关键点成功配置，测试注意力增强和特征金字塔架构
目标：从6.208mm突破到5.5mm以下
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random
from attention_pointnet import AttentionPointNet, FeaturePyramidPointNet
from double_softmax_pointnet import DoubleSoftMaxPointNet, AdaptiveDoubleSoftMaxPointNet

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class ImprovedLoss(nn.Module):
    """改进损失函数"""
    
    def __init__(self, alpha=0.8, beta=0.2):
        super(ImprovedLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, pred, target):
        mse_loss = F.mse_loss(pred, target)
        smooth_l1_loss = F.smooth_l1_loss(pred, target)
        total_loss = self.alpha * mse_loss + self.beta * smooth_l1_loss
        return total_loss

class ReducedKeypointsF3Dataset(Dataset):
    """12关键点F3数据集"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points
        self.augment = augment
        self.split = split
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
    
    def __len__(self):
        return len(self.sample_ids)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        # 数据增强
        if self.augment and self.split == 'train':
            if np.random.random() < 0.7:
                angle = np.random.uniform(-0.08, 0.08)
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                point_cloud = point_cloud @ rotation.T
                keypoints = keypoints @ rotation.T
            
            if np.random.random() < 0.6:
                translation = np.random.uniform(-0.4, 0.4, 3)
                point_cloud += translation
                keypoints += translation
            
            if np.random.random() < 0.5:
                scale = np.random.uniform(0.99, 1.01, 3)
                point_cloud *= scale
                keypoints *= scale
            
            if np.random.random() < 0.6:
                noise_level = np.random.choice([0.02, 0.03, 0.04])
                noise = np.random.normal(0, noise_level, point_cloud.shape)
                point_cloud += noise
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def train_attention_model(model_class, model_name, config):
    """训练注意力模型"""
    
    print(f"🚀 **Phase 2: {model_name}训练**")
    print(f"🎯 **基础**: 12关键点成功配置 (6.208mm)")
    print(f"📈 **目标**: 突破6mm，向5.5mm迈进")
    print(f"🏗️ **架构**: {model_name}")
    print("=" * 80)
    
    set_seed(42)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 数据集
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    train_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'train', 
                                            num_points=4096, test_samples=test_samples, 
                                            augment=True, seed=42)
    val_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'val', 
                                          num_points=4096, test_samples=test_samples, 
                                          augment=False, seed=42)
    
    batch_size = config['batch_size']
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
    print(f"📊 批次大小: {batch_size}")
    
    # 模型
    model = model_class(num_keypoints=12, dropout_rate=config['dropout']).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 模型参数: {total_params:,}")
    
    # 损失函数
    criterion = ImprovedLoss(alpha=0.8, beta=0.2)
    
    # 优化器
    optimizer = optim.AdamW(model.parameters(), lr=config['lr'], weight_decay=config['weight_decay'])
    
    # 学习率调度
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=config['lr_factor'], patience=config['lr_patience'], min_lr=1e-6
    )
    
    num_epochs = config['epochs']
    best_val_error = float('inf')
    patience = config['patience']
    patience_counter = 0
    history = []
    min_delta = 0.005
    
    print(f"🎯 训练配置: {num_epochs}轮, 耐心{patience}, 学习率{config['lr']}")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 40)
        
        # 训练
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        for batch in train_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                        
            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue
        
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # 学习率调度
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 打印结果
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"5mm={train_metrics['within_5mm_percent']:.1f}%, 7mm={train_metrics['within_7mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"5mm={val_metrics['within_5mm_percent']:.1f}%, 7mm={val_metrics['within_7mm_percent']:.1f}%")
        print(f"学习率: {current_lr:.2e}")
        
        # 保存历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'learning_rate': current_lr
        })
        
        # 检查改进
        current_error = val_metrics['mean_distance']
        improvement = best_val_error - current_error
        
        if improvement > min_delta:
            best_val_error = current_error
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics,
                'model_name': model_name,
                'config': config
            }, f'best_{model_name.lower().replace(" ", "_")}_12kp_{best_val_error:.3f}mm.pth')
            
            print(f"🎉 新最佳! 验证误差: {best_val_error:.3f}mm (改进{improvement:.3f}mm)")
            
            if best_val_error <= 5.0:
                print(f"🏆 **突破5mm医疗级目标!**")
            elif best_val_error < 5.5:
                print(f"🎯 **突破5.5mm目标!** 接近医疗级精度")
            elif best_val_error < 6.0:
                print(f"✅ **突破6mm目标!** 显著改进")
            elif best_val_error < 6.208:
                print(f"✅ **超越基线!** 架构改进有效")
        else:
            patience_counter += 1
            print(f"⏳ 无显著改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停触发")
            break
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    
    return best_val_error, total_time, history

def phase2_architecture_comparison():
    """Phase 2架构对比实验"""
    
    print(f"🚀 **Phase 2: 架构级改进对比实验**")
    print(f"🎯 **目标**: 从6.208mm基线突破到5.5mm以下")
    print(f"🏗️ **策略**: 注意力机制 + 多尺度特征提取")
    print("=" * 80)
    
    # 实验配置
    configs = [
        {
            'model_class': AttentionPointNet,
            'model_name': 'Attention PointNet',
            'config': {
                'batch_size': 4,
                'lr': 0.0008,
                'weight_decay': 1e-4,
                'dropout': 0.4,
                'lr_factor': 0.7,
                'lr_patience': 12,
                'epochs': 150,
                'patience': 20
            }
        },
        {
            'model_class': FeaturePyramidPointNet,
            'model_name': 'Feature Pyramid PointNet',
            'config': {
                'batch_size': 4,
                'lr': 0.0008,
                'weight_decay': 1e-4,
                'dropout': 0.4,
                'lr_factor': 0.7,
                'lr_patience': 12,
                'epochs': 150,
                'patience': 20
            }
        },
        {
            'model_class': DoubleSoftMaxPointNet,
            'model_name': 'Double SoftMax PointNet',
            'config': {
                'batch_size': 4,
                'lr': 0.0008,
                'weight_decay': 1e-4,
                'dropout': 0.4,
                'lr_factor': 0.7,
                'lr_patience': 12,
                'epochs': 150,
                'patience': 20
            }
        },
        {
            'model_class': AdaptiveDoubleSoftMaxPointNet,
            'model_name': 'Adaptive Double SoftMax PointNet',
            'config': {
                'batch_size': 4,
                'lr': 0.0006,  # 稍微降低学习率
                'weight_decay': 1e-4,
                'dropout': 0.4,
                'lr_factor': 0.7,
                'lr_patience': 12,
                'epochs': 150,
                'patience': 20
            }
        }
    ]
    
    results = []
    
    for i, exp_config in enumerate(configs, 1):
        print(f"\n{'='*80}")
        print(f"🧪 **实验 {i}/{len(configs)}**: {exp_config['model_name']}**")
        print(f"{'='*80}")
        
        try:
            best_error, training_time, history = train_attention_model(
                exp_config['model_class'],
                exp_config['model_name'],
                exp_config['config']
            )
            
            result = {
                'model_name': exp_config['model_name'],
                'best_val_error': float(best_error),
                'training_time_minutes': float(training_time / 60),
                'improvement_vs_baseline': float((6.208 - best_error) / 6.208 * 100),
                'epochs_trained': len(history),
                'history': history,
                'config': exp_config['config']
            }
            results.append(result)
            
            print(f"\n🎯 **{exp_config['model_name']}完成!**")
            print(f"   最佳验证误差: {best_error:.3f}mm")
            print(f"   训练时间: {training_time/60:.1f}分钟")
            print(f"   vs基线改进: {(6.208 - best_error) / 6.208 * 100:+.1f}%")
            
        except Exception as e:
            print(f"❌ {exp_config['model_name']}训练失败: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    # 保存结果
    final_results = {
        'phase': 'Phase 2 - Architecture Improvements',
        'baseline_error': 6.208,
        'target_error': 5.5,
        'results': results,
        'architectures_tested': [config['model_name'] for config in configs]
    }
    
    with open('phase2_architecture_results.json', 'w', encoding='utf-8') as f:
        json.dump(final_results, f, indent=2, ensure_ascii=False)
    
    # 分析结果
    print(f"\n🎉 **Phase 2架构改进完成!**")
    print("=" * 80)
    
    if results:
        # 按性能排序
        results.sort(key=lambda x: x['best_val_error'])
        
        print(f"📊 **架构性能排名**:")
        print(f"{'排名':<4} {'架构':<25} {'验证误差':<10} {'vs基线':<10} {'训练时间':<10}")
        print("-" * 75)
        
        for i, result in enumerate(results, 1):
            model_name = result['model_name']
            error = result['best_val_error']
            improvement = result['improvement_vs_baseline']
            time_min = result['training_time_minutes']
            
            print(f"{i:<4} {model_name:<25} {error:<10.3f} {improvement:+.1f}%{'':<4} {time_min:<10.1f}")
        
        best_result = results[0]
        print(f"\n🏆 **最佳架构**: {best_result['model_name']}")
        print(f"🎯 **最佳误差**: {best_result['best_val_error']:.3f}mm")
        print(f"📈 **改进幅度**: {best_result['improvement_vs_baseline']:+.1f}%")
        
        if best_result['best_val_error'] < 5.5:
            print(f"🎉 **成功突破5.5mm目标!** 接近医疗级精度")
        elif best_result['best_val_error'] < 6.0:
            print(f"🎯 **成功突破6mm目标!** 显著改进")
        elif best_result['best_val_error'] < 6.208:
            print(f"✅ **成功超越基线!** 架构改进有效")
        else:
            print(f"💡 **需要进一步优化** 考虑更高级的架构")
    
    return results

if __name__ == "__main__":
    set_seed(42)
    results = phase2_architecture_comparison()
