
# 训练脚本使用说明
## Training Script Usage Guide

### 🔥 主要训练脚本
1. **proper_heatmap_training.py** - 正确的Heatmap架构训练
   - 用途: 训练Heatmap回归网络
   - 输入: f3_reduced_12kp_female_augmented.npz
   - 输出: best_heatmap_augmented_model.pth
   - 性能: 3.16mm

2. **simple_heatmap_augmentation.py** - 数据增强脚本
   - 用途: 生成10倍增强数据
   - 输入: 原始25个女性样本
   - 输出: 250个增强样本
   - 方法: 旋转+高斯+不确定性

### 🚀 快速重现结果
```bash
# 1. 生成增强数据
python simple_heatmap_augmentation.py

# 2. 训练模型
python proper_heatmap_training.py

# 3. 结果: 3.16mm精度
```

### 📊 关键参数
- 学习率: 0.0005
- 批次大小: 4
- 训练轮数: 40
- 数据增强倍数: 10x
- 架构: HeatmapRegressionNet

### ⚠️ 重要提醒
- 必须使用Heatmap架构，不能用直接回归
- GPU内存需求: 建议8GB+
- 训练时间: 约20-30分钟
- 数据格式: 点云+关键点+热图
