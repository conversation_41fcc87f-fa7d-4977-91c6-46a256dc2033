#!/usr/bin/env python3
"""
基于97样本的小样本学习实验
Few-Shot Learning Experiment with 97 Samples
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import random
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
import time

class PrototypicalNetwork(nn.Module):
    """原型网络 - 适配97样本医疗关键点检测"""
    
    def __init__(self, input_dim=3, hidden_dim=256, output_dim=19*3):
        super().__init__()
        
        # 点云特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 128),
            nn.ReLU(),
            nn.Linear(128, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # 关键点回归器
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(hidden_dim, 512),
            nn.<PERSON>U(),
            nn.Dropout(0.3),
            nn.<PERSON>ar(512, 256),
            nn.<PERSON>L<PERSON>(),
            nn.Linear(256, output_dim)
        )
        
    def forward(self, point_cloud):
        """
        Args:
            point_cloud: (B, N, 3) 点云数据
        Returns:
            keypoints: (B, 19, 3) 预测的关键点
        """
        # 全局特征提取
        features = self.feature_extractor(point_cloud)  # (B, N, hidden_dim)
        global_feature = torch.max(features, dim=1)[0]  # (B, hidden_dim)
        
        # 关键点预测
        keypoints = self.keypoint_regressor(global_feature)  # (B, 19*3)
        keypoints = keypoints.view(-1, 19, 3)  # (B, 19, 3)
        
        return keypoints

class MedicalDataAugmentation:
    """医疗数据增强 - 97样本专用"""
    
    def __init__(self):
        self.augmentation_count = 0
        
    def augment_sample(self, point_cloud, keypoints, num_augmentations=3):
        """增强单个样本"""
        augmented_samples = []
        
        for i in range(num_augmentations):
            # 1. 小角度旋转 (医疗合理范围)
            angle = np.random.uniform(-0.1, 0.1)  # ±5.7度
            aug_pc, aug_kp = self.rotate_sample(point_cloud, keypoints, angle)
            
            # 2. 轻微缩放 (个体差异)
            scale = np.random.uniform(0.98, 1.02)
            aug_pc *= scale
            aug_kp *= scale
            
            # 3. 医疗噪声
            noise = np.random.normal(0, 0.01, aug_pc.shape)
            aug_pc += noise
            
            augmented_samples.append((aug_pc, aug_kp))
            
        return augmented_samples
    
    def rotate_sample(self, pc, kp, angle):
        """旋转样本"""
        cos_a, sin_a = np.cos(angle), np.sin(angle)
        rotation_matrix = np.array([
            [cos_a, -sin_a, 0],
            [sin_a, cos_a, 0],
            [0, 0, 1]
        ])
        
        rotated_pc = pc @ rotation_matrix.T
        rotated_kp = kp @ rotation_matrix.T
        
        return rotated_pc, rotated_kp

class FewShotDataset:
    """97样本的小样本学习数据集"""
    
    def __init__(self, data_path='data/raw/high_quality_f3_dataset.npz'):
        self.load_data(data_path)
        self.augmentation = MedicalDataAugmentation()
        
    def load_data(self, data_path):
        """加载97样本数据"""
        print(f"📦 加载数据: {data_path}")
        
        data = np.load(data_path, allow_pickle=True)
        self.sample_ids = data['sample_ids']
        self.point_clouds = data['point_clouds']
        self.keypoints = data['keypoints']
        
        print(f"✅ 数据加载完成:")
        print(f"   样本数: {len(self.sample_ids)}")
        print(f"   点云形状: {self.point_clouds.shape}")
        print(f"   关键点形状: {self.keypoints.shape}")
        
        # 数据预处理
        self.preprocess_data()
        
    def preprocess_data(self):
        """数据预处理"""
        print("🔧 数据预处理...")
        
        # 下采样到4096点
        processed_pcs = []
        for pc in self.point_clouds:
            if len(pc) > 4096:
                indices = np.random.choice(len(pc), 4096, replace=False)
                pc_sampled = pc[indices]
            else:
                pc_sampled = pc
            processed_pcs.append(pc_sampled)
        
        self.point_clouds = np.array(processed_pcs)
        print(f"✅ 预处理完成，点云形状: {self.point_clouds.shape}")
        
    def create_few_shot_splits(self, test_size=0.15, val_size=0.15, random_state=42):
        """创建小样本学习数据划分"""
        print("📊 创建小样本学习数据划分...")
        
        indices = np.arange(len(self.sample_ids))
        
        # 训练集 vs 测试集
        train_val_indices, test_indices = train_test_split(
            indices, test_size=test_size, random_state=random_state
        )
        
        # 训练集 vs 验证集
        train_indices, val_indices = train_test_split(
            train_val_indices, test_size=val_size/(1-test_size), random_state=random_state
        )
        
        self.splits = {
            'train': train_indices,
            'val': val_indices, 
            'test': test_indices
        }
        
        print(f"✅ 数据划分完成:")
        print(f"   训练集: {len(train_indices)} 样本")
        print(f"   验证集: {len(val_indices)} 样本") 
        print(f"   测试集: {len(test_indices)} 样本")
        
        return self.splits
    
    def sample_episode(self, split='train', k_shot=5, n_query=3):
        """采样一个训练episode"""
        split_indices = self.splits[split]
        
        # 随机选择 k_shot + n_query 个样本
        episode_indices = np.random.choice(
            split_indices, 
            size=min(k_shot + n_query, len(split_indices)), 
            replace=False
        )
        
        support_indices = episode_indices[:k_shot]
        query_indices = episode_indices[k_shot:k_shot+n_query]
        
        # 获取数据
        support_pcs = self.point_clouds[support_indices]
        support_kps = self.keypoints[support_indices]
        query_pcs = self.point_clouds[query_indices]
        query_kps = self.keypoints[query_indices]
        
        return (support_pcs, support_kps), (query_pcs, query_kps)

class FewShotTrainer:
    """小样本学习训练器"""
    
    def __init__(self, model, device='cuda'):
        self.model = model.to(device)
        self.device = device
        self.optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        self.criterion = nn.MSELoss()
        
    def train_episode(self, support_data, query_data):
        """训练一个episode"""
        support_pcs, support_kps = support_data
        query_pcs, query_kps = query_data
        
        # 转换为tensor
        support_pcs = torch.FloatTensor(support_pcs).to(self.device)
        support_kps = torch.FloatTensor(support_kps).to(self.device)
        query_pcs = torch.FloatTensor(query_pcs).to(self.device)
        query_kps = torch.FloatTensor(query_kps).to(self.device)
        
        self.optimizer.zero_grad()
        
        # 前向传播
        pred_support = self.model(support_pcs)
        pred_query = self.model(query_pcs)
        
        # 计算损失
        support_loss = self.criterion(pred_support, support_kps)
        query_loss = self.criterion(pred_query, query_kps)
        total_loss = support_loss + query_loss
        
        # 反向传播
        total_loss.backward()
        self.optimizer.step()
        
        return total_loss.item(), support_loss.item(), query_loss.item()
    
    def evaluate(self, dataset, split='val', k_shot=5, n_query=3, num_episodes=50):
        """评估模型性能"""
        self.model.eval()
        total_error = 0
        
        with torch.no_grad():
            for _ in range(num_episodes):
                support_data, query_data = dataset.sample_episode(split, k_shot, n_query)
                query_pcs, query_kps = query_data
                
                query_pcs = torch.FloatTensor(query_pcs).to(self.device)
                query_kps = torch.FloatTensor(query_kps).to(self.device)
                
                pred_kps = self.model(query_pcs)
                
                # 计算平均距离误差 (mm)
                error = torch.mean(torch.norm(pred_kps - query_kps, dim=2))
                total_error += error.item()
        
        avg_error = total_error / num_episodes
        return avg_error

def main():
    """主实验函数"""
    print("🚀 开始97样本小样本学习实验")
    print("=" * 60)
    
    # 1. 加载数据
    dataset = FewShotDataset()
    splits = dataset.create_few_shot_splits()
    
    # 2. 创建模型
    model = PrototypicalNetwork()
    trainer = FewShotTrainer(model)
    
    # 3. 不同shot数的实验
    shot_configs = [1, 3, 5, 10]
    results = {}
    
    for k_shot in shot_configs:
        print(f"\n🎯 开始 {k_shot}-shot 实验")
        print("-" * 40)
        
        # 重新初始化模型
        model = PrototypicalNetwork()
        trainer = FewShotTrainer(model)
        
        # 训练
        num_episodes = 500
        for episode in range(num_episodes):
            support_data, query_data = dataset.sample_episode('train', k_shot, 3)
            loss, s_loss, q_loss = trainer.train_episode(support_data, query_data)
            
            if episode % 100 == 0:
                val_error = trainer.evaluate(dataset, 'val', k_shot, 3, 20)
                print(f"Episode {episode}: Loss={loss:.4f}, Val_Error={val_error:.2f}mm")
        
        # 最终评估
        final_error = trainer.evaluate(dataset, 'test', k_shot, 3, 100)
        results[k_shot] = final_error
        
        print(f"✅ {k_shot}-shot 最终误差: {final_error:.2f}mm")
    
    # 4. 结果总结
    print(f"\n📊 实验结果总结")
    print("=" * 40)
    for k_shot, error in results.items():
        print(f"{k_shot}-shot: {error:.2f}mm")
    
    return results

if __name__ == "__main__":
    results = main()
