"""
医疗关键点检测的小样本学习实现方案
Medical Few-Shot Learning Implementation for Keypoint Detection
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import os
from torch.utils.data import DataLoader, Dataset
import json
from typing import Dict, List, Tuple
import random
from sklearn.model_selection import train_test_split

class MedicalKeypointDataset(Dataset):
    """医疗关键点数据集 - 支持小样本学习"""
    
    def __init__(self, data_dir, split='train', few_shot_config=None):
        self.data_dir = data_dir
        self.split = split
        self.few_shot_config = few_shot_config
        
        # 加载数据
        self.samples = self.load_samples()
        
        # 如果是小样本设置，限制样本数量
        if few_shot_config:
            self.samples = self.create_few_shot_split()
    
    def load_samples(self):
        """加载所有样本"""
        samples = []
        
        # 假设数据结构
        for region in ['F1', 'F2', 'F3']:
            region_dir = os.path.join(self.data_dir, region)
            if os.path.exists(region_dir):
                for file in os.listdir(region_dir):
                    if file.endswith('.npz'):
                        samples.append({
                            'path': os.path.join(region_dir, file),
                            'region': region,
                            'sample_id': file.replace('.npz', '')
                        })
        
        return samples
    
    def create_few_shot_split(self):
        """创建小样本数据分割"""
        k_shot = self.few_shot_config.get('k_shot', 1)
        
        # 按区域分组
        region_samples = {'F1': [], 'F2': [], 'F3': []}
        for sample in self.samples:
            region_samples[sample['region']].append(sample)
        
        # 每个区域选择k_shot个样本
        few_shot_samples = []
        for region, samples in region_samples.items():
            if len(samples) >= k_shot:
                selected = random.sample(samples, k_shot)
                few_shot_samples.extend(selected)
        
        return few_shot_samples
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        
        # 加载点云和关键点
        data = np.load(sample['path'])
        point_cloud = data['point_cloud']  # (N, 3)
        keypoints = data['keypoints']      # (19, 3) for each region
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'region': sample['region'],
            'sample_id': sample['sample_id']
        }

class PrototypicalKeypointNetwork(nn.Module):
    """原型网络 - 专门用于医疗关键点检测"""
    
    def __init__(self, input_dim=3, hidden_dim=256, num_keypoints=19):
        super().__init__()
        
        # 点云特征提取器
        self.point_encoder = nn.Sequential(
            nn.Linear(input_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 128),
            nn.ReLU(),
            nn.Linear(128, hidden_dim),
            nn.ReLU()
        )
        
        # 全局特征聚合
        self.global_pool = nn.AdaptiveMaxPool1d(1)
        
        # 关键点回归头
        self.keypoint_head = nn.Sequential(
            nn.Linear(hidden_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, num_keypoints * 3)
        )
        
    def extract_features(self, point_cloud):
        """提取点云特征"""
        # point_cloud: (B, N, 3)
        B, N, _ = point_cloud.shape
        
        # 逐点特征提取
        point_features = self.point_encoder(point_cloud)  # (B, N, hidden_dim)
        
        # 全局特征聚合
        global_features = point_features.transpose(1, 2)  # (B, hidden_dim, N)
        global_features = self.global_pool(global_features).squeeze(-1)  # (B, hidden_dim)
        
        return global_features
    
    def forward(self, point_cloud):
        """前向传播"""
        features = self.extract_features(point_cloud)
        keypoints = self.keypoint_head(features)
        return keypoints.view(-1, 19, 3)  # (B, 19, 3)

class MedicalFewShotTrainer:
    """医疗小样本学习训练器"""
    
    def __init__(self, model, device='cuda'):
        self.model = model.to(device)
        self.device = device
        self.optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        
    def prototypical_training(self, support_loader, query_loader, episodes=1000):
        """原型网络训练"""
        self.model.train()
        
        for episode in range(episodes):
            episode_loss = 0
            
            # 获取支持集和查询集
            support_data = next(iter(support_loader))
            query_data = next(iter(query_loader))
            
            # 移动到设备
            support_pc = support_data['point_cloud'].to(self.device)
            support_kp = support_data['keypoints'].to(self.device)
            query_pc = query_data['point_cloud'].to(self.device)
            query_kp = query_data['keypoints'].to(self.device)
            
            # 提取特征
            support_features = self.model.extract_features(support_pc)
            query_features = self.model.extract_features(query_pc)
            
            # 计算原型 (按区域分组)
            prototypes = self.compute_prototypes(support_features, support_data['region'])
            
            # 计算查询集预测
            query_pred = self.predict_with_prototypes(query_features, prototypes)
            
            # 计算损失
            loss = F.mse_loss(query_pred, query_kp)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()
            
            episode_loss += loss.item()
            
            if episode % 100 == 0:
                print(f"Episode {episode}, Loss: {episode_loss:.4f}")
    
    def compute_prototypes(self, features, regions):
        """计算每个区域的原型"""
        prototypes = {}
        
        for i, region in enumerate(regions):
            if region not in prototypes:
                prototypes[region] = []
            prototypes[region].append(features[i])
        
        # 计算每个区域的平均特征
        for region in prototypes:
            prototypes[region] = torch.stack(prototypes[region]).mean(dim=0)
        
        return prototypes
    
    def predict_with_prototypes(self, query_features, prototypes):
        """使用原型进行预测"""
        predictions = []
        
        for feature in query_features:
            # 找到最近的原型
            min_distance = float('inf')
            best_prototype = None
            
            for region, prototype in prototypes.items():
                distance = F.mse_loss(feature, prototype)
                if distance < min_distance:
                    min_distance = distance
                    best_prototype = prototype
            
            # 使用最佳原型生成关键点
            pred_keypoints = self.model.keypoint_head(best_prototype.unsqueeze(0))
            predictions.append(pred_keypoints.squeeze(0))
        
        return torch.stack(predictions).view(-1, 19, 3)

class MedicalDataAugmentation:
    """医疗数据增强 - 小样本学习专用"""
    
    def __init__(self):
        self.augmentation_strategies = [
            self.anatomical_rotation,
            self.medical_noise,
            self.scale_variation,
            self.local_deformation
        ]
    
    def augment_sample(self, point_cloud, keypoints, num_augmentations=5):
        """增强单个样本"""
        augmented_samples = [(point_cloud, keypoints)]  # 原始样本
        
        for _ in range(num_augmentations):
            # 随机选择增强策略
            strategy = random.choice(self.augmentation_strategies)
            aug_pc, aug_kp = strategy(point_cloud.clone(), keypoints.clone())
            augmented_samples.append((aug_pc, aug_kp))
        
        return augmented_samples
    
    def anatomical_rotation(self, pc, kp):
        """解剖学感知旋转"""
        # 小角度旋转 (±15度)
        angle = random.uniform(-15, 15)
        angle_rad = np.radians(angle)
        
        cos_a, sin_a = np.cos(angle_rad), np.sin(angle_rad)
        rotation_matrix = torch.tensor([
            [cos_a, -sin_a, 0],
            [sin_a, cos_a, 0],
            [0, 0, 1]
        ], dtype=pc.dtype, device=pc.device)
        
        rotated_pc = pc @ rotation_matrix.T
        rotated_kp = kp @ rotation_matrix.T
        
        return rotated_pc, rotated_kp
    
    def medical_noise(self, pc, kp):
        """医学噪声注入"""
        noise_level = random.uniform(0.001, 0.005)
        
        pc_noise = torch.normal(0, noise_level, pc.shape, device=pc.device)
        kp_noise = torch.normal(0, noise_level * 0.5, kp.shape, device=kp.device)
        
        return pc + pc_noise, kp + kp_noise
    
    def scale_variation(self, pc, kp):
        """尺度变化"""
        scale = random.uniform(0.95, 1.05)
        return pc * scale, kp * scale
    
    def local_deformation(self, pc, kp):
        """局部形变"""
        deform_strength = random.uniform(0.01, 0.03)
        
        pc_deform = torch.normal(0, deform_strength, pc.shape, device=pc.device)
        kp_deform = torch.normal(0, deform_strength * 0.3, kp.shape, device=kp.device)
        
        return pc + pc_deform, kp + kp_deform

def create_few_shot_experiment(data_dir, k_shot=1, n_way=3):
    """创建小样本学习实验"""
    
    # 配置
    few_shot_config = {
        'k_shot': k_shot,
        'n_way': n_way
    }
    
    # 创建数据集
    train_dataset = MedicalKeypointDataset(
        data_dir, 
        split='train', 
        few_shot_config=few_shot_config
    )
    
    val_dataset = MedicalKeypointDataset(
        data_dir, 
        split='val', 
        few_shot_config=few_shot_config
    )
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=k_shot, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=k_shot, shuffle=False)
    
    # 创建模型
    model = PrototypicalKeypointNetwork()
    
    # 创建训练器
    trainer = MedicalFewShotTrainer(model)
    
    # 数据增强
    augmentation = MedicalDataAugmentation()
    
    return {
        'model': model,
        'trainer': trainer,
        'train_loader': train_loader,
        'val_loader': val_loader,
        'augmentation': augmentation
    }

def main():
    """主函数 - 运行小样本学习实验"""
    
    # 实验配置
    data_dir = "/home/<USER>/pjc/GCN/Data"  # 你的数据目录
    k_shot = 1  # 每类1个样本
    n_way = 3   # 3个类别 (F1, F2, F3)
    
    print(f"🚀 开始 {k_shot}-shot {n_way}-way 医疗关键点检测实验")
    
    # 创建实验
    experiment = create_few_shot_experiment(data_dir, k_shot, n_way)
    
    # 训练
    print("📚 开始原型网络训练...")
    experiment['trainer'].prototypical_training(
        experiment['train_loader'],
        experiment['val_loader'],
        episodes=1000
    )
    
    print("✅ 小样本学习实验完成！")
    
    # 保存模型
    torch.save(experiment['model'].state_dict(), 'few_shot_keypoint_model.pth')
    print("💾 模型已保存")

if __name__ == "__main__":
    main()
