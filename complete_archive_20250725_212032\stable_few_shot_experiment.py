#!/usr/bin/env python3
"""
稳定的小样本学习实验 - 97样本医疗关键点检测
Stable Few-Shot Learning Experiment for 97 Medical Samples
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import random
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
import time
import json
from datetime import datetime

class ImprovedKeypointNet(nn.Module):
    """改进的关键点检测网络"""
    
    def __init__(self, input_dim=3, hidden_dim=512, output_dim=19*3, dropout=0.3):
        super().__init__()
        
        # 更深的特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(128, 256),
            nn.BatchNorm1d(256),
            nn.<PERSON><PERSON><PERSON>(),
            nn.Dropout(dropout),
            
            nn.Linear(256, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(hidden_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU()
        )
        
        # 关键点回归器
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(hidden_dim, 1024),
            nn.BatchNorm1d(1024),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            
            nn.Linear(256, output_dim)
        )
        
    def forward(self, point_cloud):
        """
        Args:
            point_cloud: (B, N, 3) 点云数据
        Returns:
            keypoints: (B, 19, 3) 预测的关键点
        """
        batch_size = point_cloud.size(0)
        
        # 重塑为 (B*N, 3)
        pc_flat = point_cloud.view(-1, 3)
        
        # 特征提取
        features = self.feature_extractor(pc_flat)  # (B*N, hidden_dim)
        
        # 重塑回 (B, N, hidden_dim)
        features = features.view(batch_size, -1, features.size(-1))
        
        # 全局最大池化
        global_feature, _ = torch.max(features, dim=1)  # (B, hidden_dim)
        
        # 关键点预测
        keypoints = self.keypoint_regressor(global_feature)  # (B, 19*3)
        keypoints = keypoints.view(-1, 19, 3)  # (B, 19, 3)
        
        return keypoints

class EnhancedDataAugmentation:
    """增强的医疗数据增强"""
    
    def __init__(self):
        self.augmentation_strategies = [
            self.rotate_augment,
            self.scale_augment,
            self.noise_augment,
            self.jitter_augment,
            self.dropout_augment
        ]
        
    def augment_batch(self, point_clouds, keypoints, num_augmentations=2):
        """批量数据增强"""
        augmented_pcs = []
        augmented_kps = []

        # 添加原始数据
        for pc, kp in zip(point_clouds, keypoints):
            augmented_pcs.append(pc)
            augmented_kps.append(kp)

        # 生成增强数据
        for pc, kp in zip(point_clouds, keypoints):
            for _ in range(num_augmentations):
                # 随机选择增强策略
                strategy = random.choice(self.augmentation_strategies)
                aug_pc, aug_kp = strategy(pc.copy(), kp.copy())
                augmented_pcs.append(aug_pc)
                augmented_kps.append(aug_kp)

        # 确保所有点云有相同的点数
        target_points = 4096
        processed_pcs = []
        for pc in augmented_pcs:
            if len(pc) > target_points:
                indices = np.random.choice(len(pc), target_points, replace=False)
                pc = pc[indices]
            elif len(pc) < target_points:
                # 重复采样到目标点数
                indices = np.random.choice(len(pc), target_points, replace=True)
                pc = pc[indices]
            processed_pcs.append(pc)

        return np.stack(processed_pcs), np.stack(augmented_kps)
    
    def rotate_augment(self, pc, kp):
        """旋转增强"""
        angle = np.random.uniform(-0.15, 0.15)  # ±8.6度
        cos_a, sin_a = np.cos(angle), np.sin(angle)
        rotation_matrix = np.array([
            [cos_a, -sin_a, 0],
            [sin_a, cos_a, 0],
            [0, 0, 1]
        ])
        return pc @ rotation_matrix.T, kp @ rotation_matrix.T
    
    def scale_augment(self, pc, kp):
        """缩放增强"""
        scale = np.random.uniform(0.95, 1.05)
        return pc * scale, kp * scale
    
    def noise_augment(self, pc, kp):
        """噪声增强"""
        noise = np.random.normal(0, 0.02, pc.shape)
        return pc + noise, kp
    
    def jitter_augment(self, pc, kp):
        """抖动增强"""
        jitter = np.random.normal(0, 0.01, pc.shape)
        return pc + jitter, kp
    
    def dropout_augment(self, pc, kp):
        """点云dropout"""
        keep_ratio = np.random.uniform(0.8, 0.95)
        num_keep = int(len(pc) * keep_ratio)
        indices = np.random.choice(len(pc), num_keep, replace=False)
        return pc[indices], kp

class StableFewShotTrainer:
    """稳定的小样本学习训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        self.augmentation = EnhancedDataAugmentation()
        
    def load_and_prepare_data(self, data_path='data/raw/high_quality_f3_dataset.npz'):
        """加载和准备数据"""
        print(f"📦 加载数据: {data_path}")
        
        data = np.load(data_path, allow_pickle=True)
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        print(f"✅ 原始数据: {len(sample_ids)} 样本")
        
        # 下采样点云
        processed_pcs = []
        for pc in point_clouds:
            if len(pc) > 4096:
                indices = np.random.choice(len(pc), 4096, replace=False)
                pc_sampled = pc[indices]
            else:
                pc_sampled = pc
            processed_pcs.append(pc_sampled)
        
        point_clouds = np.array(processed_pcs)
        
        # 数据划分
        indices = np.arange(len(sample_ids))
        train_val_indices, test_indices = train_test_split(
            indices, test_size=0.15, random_state=42
        )
        train_indices, val_indices = train_test_split(
            train_val_indices, test_size=0.18, random_state=42  # 约15%验证集
        )
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices],
                'sample_ids': sample_ids[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices],
                'sample_ids': sample_ids[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices],
                'sample_ids': sample_ids[test_indices]
            }
        }
        
        print(f"✅ 数据划分: 训练{len(train_indices)}, 验证{len(val_indices)}, 测试{len(test_indices)}")
        return self.data
    
    def train_few_shot_model(self, k_shot, epochs=100, lr=0.001):
        """训练小样本模型"""
        print(f"\n🎯 训练 {k_shot}-shot 模型")
        
        # 创建模型
        model = ImprovedKeypointNet().to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        patience_counter = 0
        
        for epoch in range(epochs):
            model.train()
            
            # 从训练集中采样k_shot个样本
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                min(k_shot, len(self.data['train']['point_clouds'])), 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 数据增强
            aug_pcs, aug_kps = self.augmentation.augment_batch(train_pcs, train_kps, num_augmentations=3)
            
            # 转换为tensor
            aug_pcs = torch.FloatTensor(aug_pcs).to(self.device)
            aug_kps = torch.FloatTensor(aug_kps).to(self.device)
            
            # 训练
            optimizer.zero_grad()
            pred_kps = model(aug_pcs)
            loss = criterion(pred_kps, aug_kps)
            loss.backward()
            optimizer.step()
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_model(model, 'val')
                scheduler.step(val_error)
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                    patience_counter = 0
                else:
                    patience_counter += 1
                
                print(f"Epoch {epoch}: Loss={loss:.4f}, Val_Error={val_error:.2f}mm")
                
                # 早停
                if patience_counter >= 20:
                    print(f"早停在epoch {epoch}")
                    break
        
        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def evaluate_model(self, model, split='test'):
        """评估模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for pc, kp in zip(pcs, kps):
                pc_tensor = torch.FloatTensor(pc).unsqueeze(0).to(self.device)
                kp_tensor = torch.FloatTensor(kp).unsqueeze(0).to(self.device)
                
                pred_kp = model(pc_tensor)
                error = torch.mean(torch.norm(pred_kp - kp_tensor, dim=2))
                total_error += error.item()
                num_samples += 1
        
        return total_error / num_samples if num_samples > 0 else float('inf')

def run_stable_experiment():
    """运行稳定的小样本学习实验"""
    print("🚀 稳定小样本学习实验")
    print("=" * 50)
    
    # 初始化训练器
    trainer = StableFewShotTrainer()
    
    # 加载数据
    data = trainer.load_and_prepare_data()
    
    # 不同shot配置的实验
    shot_configs = [1, 3, 5, 10, 20]
    results = {}
    
    for k_shot in shot_configs:
        print(f"\n{'='*60}")
        
        # 训练模型
        model, val_error = trainer.train_few_shot_model(k_shot, epochs=150, lr=0.001)
        
        # 测试评估
        test_error = trainer.evaluate_model(model, 'test')
        results[k_shot] = test_error
        
        print(f"✅ {k_shot}-shot 最终测试误差: {test_error:.2f}mm")
    
    # 结果分析
    print(f"\n📊 实验结果总结")
    print("=" * 40)
    for k_shot, error in results.items():
        improvement = (8.0 - error) / 8.0 * 100  # 假设基线8mm
        print(f"{k_shot:2d}-shot: {error:6.2f}mm (改进: {improvement:+5.1f}%)")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = Path("results/few_shot_experiments")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    result_data = {
        "experiment_type": "stable_few_shot",
        "timestamp": timestamp,
        "results": results,
        "dataset_info": {
            "total_samples": 97,
            "train_samples": len(data['train']['point_clouds']),
            "val_samples": len(data['val']['point_clouds']),
            "test_samples": len(data['test']['point_clouds'])
        }
    }
    
    with open(results_dir / f"stable_few_shot_results_{timestamp}.json", 'w') as f:
        json.dump(result_data, f, indent=2)
    
    return results

if __name__ == "__main__":
    results = run_stable_experiment()
