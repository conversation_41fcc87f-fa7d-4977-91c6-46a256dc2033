#!/usr/bin/env python3
"""
测试医学级数据集
Test Medical Grade Dataset
验证新创建的医学级数据集的模型性能
"""

import sys
import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
from sklearn.model_selection import train_test_split

# 添加原始代码路径
sys.path.insert(0, os.path.abspath("archive/old_scripts"))

def set_seed(seed=42):
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

# 导入原始模块
from ensemble_double_softmax_exact import ExactEnsembleDoubleSoftMaxPointNet

class MedicalDataset(Dataset):
    """医学数据集"""
    
    def __init__(self, point_clouds, keypoints):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def quick_train_test(model, train_loader, val_loader, epochs=50, device='cuda', target_error=None):
    """快速训练测试"""
    
    print(f"🚀 快速训练测试 ({epochs}轮)")
    if target_error:
        print(f"   目标误差: {target_error}mm")
    
    model = model.to(device)
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
    criterion = nn.MSELoss()
    
    best_val_error = float('inf')
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            loss.backward()
            
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        # 验证
        model.eval()
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        if val_error < best_val_error:
            best_val_error = val_error
        
        # 每10轮打印一次
        if epoch % 10 == 0 or epoch < 5:
            print(f"Epoch {epoch+1:2d}: Train: {train_error:.3f}, Val: {val_error:.3f}")
        
        # 目标达成检查
        if target_error and val_error <= target_error:
            print(f"🎉 达到目标误差 {target_error}mm！在第{epoch+1}轮")
            break
    
    print(f"✅ 最佳验证误差: {best_val_error:.3f}mm")
    return best_val_error

def test_medical_grade_datasets():
    """测试医学级数据集"""
    
    print("🏥 测试医学级数据集")
    print("验证新创建的医学级数据集性能")
    print("=" * 80)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 测试配置
    batch_size = 4
    epochs = 60
    
    results = {}
    
    # 测试不同规模的数据集
    test_configs = [
        {'points': 12, 'target': 6.0, 'file': 'medical_grade_progressive_12_dataset.npz'},
        {'points': 15, 'target': 7.0, 'file': 'medical_grade_progressive_15_dataset.npz'},
        {'points': 19, 'target': 8.0, 'file': 'medical_grade_progressive_19_dataset.npz'},
        {'points': 57, 'target': 12.0, 'file': 'medical_grade_full_57_dataset.npz'}
    ]
    
    for config in test_configs:
        num_points = config['points']
        target_error = config['target']
        filename = config['file']
        
        print(f"\n🎯 测试{num_points}点医学级数据集")
        print("=" * 50)
        
        try:
            # 加载数据集
            data = np.load(filename, allow_pickle=True)
            
            if num_points == 57:
                point_clouds = data['point_clouds']
                keypoints = data['keypoints_57']
            else:
                point_clouds = data['point_clouds']
                keypoints = data['keypoints']
            
            print(f"📊 数据集信息:")
            print(f"   样本数: {len(point_clouds)}")
            print(f"   关键点数: {num_points}")
            print(f"   点云形状: {point_clouds.shape}")
            print(f"   关键点形状: {keypoints.shape}")
            
            # 数据划分
            indices = np.arange(len(point_clouds))
            train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
            train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
            
            train_dataset = MedicalDataset(point_clouds[train_indices], keypoints[train_indices])
            val_dataset = MedicalDataset(point_clouds[val_indices], keypoints[val_indices])
            
            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
            val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
            
            print(f"📋 数据划分: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
            
            # 创建模型
            set_seed(123)
            model = ExactEnsembleDoubleSoftMaxPointNet(num_keypoints=num_points, dropout_rate=0.3, num_ensembles=3)
            
            # 训练模型
            best_error = quick_train_test(model, train_loader, val_loader, epochs, device, target_error)
            
            # 记录结果
            results[num_points] = {
                'best_error': best_error,
                'target_error': target_error,
                'success': best_error <= target_error,
                'samples': len(point_clouds),
                'dataset_type': 'medical_grade'
            }
            
            print(f"📊 {num_points}点结果:")
            print(f"   最佳误差: {best_error:.3f}mm")
            print(f"   目标误差: {target_error:.3f}mm")
            print(f"   {'✅ 成功' if best_error <= target_error else '❌ 未达标'}")
            
        except Exception as e:
            print(f"❌ {num_points}点测试失败: {e}")
            results[num_points] = {
                'best_error': float('inf'),
                'target_error': target_error,
                'success': False,
                'error': str(e)
            }
    
    # 对比分析
    print(f"\n📊 医学级数据集性能总结")
    print("=" * 80)
    
    print(f"{'关键点数':<8} {'最佳误差':<10} {'目标误差':<10} {'状态':<8} {'样本数':<8} {'改进评估'}")
    print("-" * 70)
    
    for num_points, result in results.items():
        if result['success']:
            status = "✅ 成功"
            improvement = "优秀"
        elif result['best_error'] < result['target_error'] * 1.2:
            status = "⚠️ 接近"
            improvement = "良好"
        else:
            status = "❌ 失败"
            improvement = "需改进"
        
        samples = result.get('samples', 0)
        
        print(f"{num_points:<8} {result['best_error']:<10.3f} {result['target_error']:<10.3f} {status:<8} {samples:<8} {improvement}")
    
    # 与历史数据对比
    print(f"\n📊 与历史基准对比")
    print("-" * 50)
    
    historical_benchmarks = {
        12: 6.067,  # 历史最佳
        57: 21.0    # 直接跳跃预期
    }
    
    for num_points in [12, 57]:
        if num_points in results and num_points in historical_benchmarks:
            current = results[num_points]['best_error']
            historical = historical_benchmarks[num_points]
            improvement = (historical - current) / historical * 100
            
            print(f"   {num_points}点: {current:.3f}mm vs 历史{historical:.3f}mm")
            print(f"        改进: {improvement:+.1f}%")
    
    # 渐进式扩展分析
    if all(k in results for k in [12, 15, 19]):
        print(f"\n📊 渐进式扩展分析")
        print("-" * 50)
        
        error_12 = results[12]['best_error']
        error_15 = results[15]['best_error']
        error_19 = results[19]['best_error']
        
        expansion_12_15 = (error_15 - error_12) / error_12 * 100
        expansion_15_19 = (error_19 - error_15) / error_15 * 100
        
        print(f"   12→15点扩展代价: {expansion_12_15:+.1f}%")
        print(f"   15→19点扩展代价: {expansion_15_19:+.1f}%")
        
        if expansion_12_15 < 20 and expansion_15_19 < 20:
            print(f"   ✅ 渐进式扩展成功！代价可控")
        else:
            print(f"   ⚠️ 扩展代价较高，需要优化")
    
    # 保存结果
    import json
    with open('medical_grade_dataset_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 测试结果已保存: medical_grade_dataset_test_results.json")
    
    return results

def main():
    """主函数"""
    
    print("🏥 医学级数据集性能测试")
    print("验证基于原始医学数据创建的高质量数据集")
    print("=" * 80)
    
    try:
        results = test_medical_grade_datasets()
        
        # 最终评估
        print(f"\n🎯 最终评估:")
        
        successful_tests = [k for k, v in results.items() if v['success']]
        total_tests = len(results)
        
        print(f"   成功测试: {len(successful_tests)}/{total_tests}")
        print(f"   成功率: {len(successful_tests)/total_tests*100:.1f}%")
        
        if len(successful_tests) >= 3:
            print(f"   🎉 医学级数据集验证成功！")
            print(f"   💡 原始医学数据处理策略有效")
            print(f"   🚀 可以进行大规模训练和应用")
        elif len(successful_tests) >= 2:
            print(f"   ✅ 医学级数据集基本成功")
            print(f"   🔧 部分配置需要进一步优化")
        else:
            print(f"   ⚠️ 需要进一步改进数据处理策略")
        
        print(f"\n💡 关键洞察:")
        print(f"   📊 原始医学数据是最宝贵的资源")
        print(f"   🏥 医学级质量控制显著提升性能")
        print(f"   🔧 坐标系标准化和表面投影优化是关键")
        print(f"   🚀 渐进式扩展策略在医学数据上有效")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
