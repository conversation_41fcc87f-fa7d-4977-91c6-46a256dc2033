#!/usr/bin/env python3
"""
可视化集成双Softmax模型的预测效果
展示预测点和真实点在点云上的对比
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json
import os

# 简化的基线模型定义
class SimpleBaselinePointNet(nn.Module):
    """简化的基线PointNet模型"""

    def __init__(self, num_keypoints: int, dropout_rate: float = 0.3):
        super(SimpleBaselinePointNet, self).__init__()

        self.num_keypoints = num_keypoints

        # 基线架构
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)

        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)

        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)

        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)

        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)

        self.dropout = nn.Dropout(dropout_rate)

    def forward(self, x):
        batch_size = x.size(0)
        x_input = x.transpose(2, 1)

        x1 = torch.relu(self.bn1(self.conv1(x_input)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))

        x3_res = x3 + self.residual1(x1)

        x4 = torch.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)

        x5 = torch.relu(self.bn5(self.conv5(x4_res)))

        global_feat = torch.max(x5, 2)[0]

        feat = torch.relu(self.bn_fc1(self.fc1(global_feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc2(self.fc2(feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc3(self.fc3(feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc4(self.fc4(feat)))
        feat = self.dropout(feat)
        feat = self.fc5(feat)

        keypoints = feat.view(batch_size, self.num_keypoints, 3)

        return keypoints

# 简化的数据集类
class ReducedKeypointsF3Dataset:
    """简化的12关键点F3数据集"""

    def __init__(self, data_path: str, split: str = 'val', num_points: int = 4096,
                 test_samples: list = None, seed: int = 42):

        self.num_points = num_points
        self.split = split

        np.random.seed(seed)

        data = np.load(data_path, allow_pickle=True)

        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']

        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118',
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']

        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask

        if split == 'test' or split == 'val':
            # 使用验证集作为展示
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]

            val_size = int(0.2 * len(train_val_ids))
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)

            self.sample_ids = train_val_ids[val_indices]
            self.point_clouds = train_val_pcs[val_indices]
            self.keypoints = train_val_kps[val_indices]

    def __len__(self):
        return len(self.sample_ids)

    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()

        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]

        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

def load_best_model():
    """加载最佳可用模型"""

    print("🔍 **寻找最佳模型文件**")

    # 查找所有模型文件
    model_files = []
    for file in os.listdir('.'):
        if file.startswith('best_') and file.endswith('.pth'):
            model_files.append(file)

    if not model_files:
        print("❌ 未找到模型文件")
        return None

    # 选择误差最小的模型
    best_file = None
    best_error = float('inf')

    for file in model_files:
        try:
            # 从文件名提取误差值
            parts = file.split('_')
            for part in parts:
                if 'mm.pth' in part:
                    error = float(part.replace('mm.pth', ''))
                    if error < best_error:
                        best_error = error
                        best_file = file
                    break
        except:
            continue

    if best_file is None:
        print("❌ 无法解析模型文件名")
        return None

    print(f"✅ 找到最佳模型: {best_file} (误差: {best_error:.3f}mm)")

    # 加载模型
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')

    # 创建简化模型
    model = SimpleBaselinePointNet(num_keypoints=12).to(device)

    # 尝试加载权重
    try:
        checkpoint = torch.load(best_file, map_location=device)

        # 尝试直接加载
        try:
            model.load_state_dict(checkpoint['model_state_dict'])
        except:
            # 如果失败，尝试加载兼容的权重
            model_dict = model.state_dict()
            checkpoint_dict = checkpoint['model_state_dict']

            # 过滤掉不匹配的键
            filtered_dict = {}
            for k, v in checkpoint_dict.items():
                if k in model_dict and model_dict[k].shape == v.shape:
                    filtered_dict[k] = v

            model_dict.update(filtered_dict)
            model.load_state_dict(model_dict)
            print(f"⚠️  部分权重加载成功 ({len(filtered_dict)}/{len(model_dict)})")

        model.eval()
        print(f"✅ 模型加载成功，设备: {device}")

        return model, device, best_error

    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None

def get_test_samples():
    """获取测试样本"""

    print("📊 **加载测试数据**")

    # 使用验证集作为测试
    test_samples = ['600114', '600115', '600116', '600117', '600118',
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']

    val_dataset = ReducedKeypointsF3Dataset(
        'f3_reduced_12kp_stable.npz', 'val',
        num_points=4096, test_samples=test_samples,
        seed=42
    )

    print(f"✅ 验证集样本数: {len(val_dataset)}")

    return val_dataset

def predict_keypoints(model, device, dataset, num_samples=5):
    """预测关键点"""
    
    print(f"🔮 **预测关键点** (样本数: {num_samples})")
    
    predictions = []
    
    with torch.no_grad():
        for i in range(min(num_samples, len(dataset))):
            sample = dataset[i]
            
            point_cloud = sample['point_cloud'].unsqueeze(0).to(device)  # [1, num_points, 3]
            true_keypoints = sample['keypoints'].numpy()  # [num_keypoints, 3]
            sample_id = sample['sample_id']
            
            # 模型预测
            pred_keypoints = model(point_cloud)  # [1, num_keypoints, 3]
            pred_keypoints = pred_keypoints.squeeze(0).cpu().numpy()  # [num_keypoints, 3]
            
            # 计算误差
            distances = np.linalg.norm(pred_keypoints - true_keypoints, axis=1)
            mean_error = np.mean(distances)
            
            predictions.append({
                'sample_id': sample_id,
                'point_cloud': point_cloud.squeeze(0).cpu().numpy(),  # [num_points, 3]
                'pred_keypoints': pred_keypoints,
                'true_keypoints': true_keypoints,
                'distances': distances,
                'mean_error': mean_error
            })
            
            print(f"   样本 {sample_id}: 平均误差 {mean_error:.3f}mm")
    
    return predictions

def create_3d_visualization(prediction, save_path=None):
    """创建3D可视化"""
    
    point_cloud = prediction['point_cloud']
    pred_keypoints = prediction['pred_keypoints']
    true_keypoints = prediction['true_keypoints']
    distances = prediction['distances']
    sample_id = prediction['sample_id']
    mean_error = prediction['mean_error']
    
    # 创建3D图
    fig = plt.figure(figsize=(15, 10))
    
    # 主视图
    ax1 = fig.add_subplot(221, projection='3d')
    
    # 绘制点云 (灰色，半透明)
    ax1.scatter(point_cloud[:, 0], point_cloud[:, 1], point_cloud[:, 2], 
               c='lightgray', alpha=0.3, s=1, label='Point Cloud')
    
    # 绘制真实关键点 (绿色)
    ax1.scatter(true_keypoints[:, 0], true_keypoints[:, 1], true_keypoints[:, 2],
               c='green', s=100, alpha=0.8, label='True Keypoints', marker='o')
    
    # 绘制预测关键点 (红色)
    ax1.scatter(pred_keypoints[:, 0], pred_keypoints[:, 1], pred_keypoints[:, 2],
               c='red', s=100, alpha=0.8, label='Predicted Keypoints', marker='^')
    
    # 绘制连接线
    for i in range(len(true_keypoints)):
        ax1.plot([true_keypoints[i, 0], pred_keypoints[i, 0]],
                [true_keypoints[i, 1], pred_keypoints[i, 1]],
                [true_keypoints[i, 2], pred_keypoints[i, 2]],
                'b--', alpha=0.6, linewidth=1)
    
    ax1.set_title(f'Sample {sample_id} - Mean Error: {mean_error:.3f}mm\n3D View')
    ax1.set_xlabel('X (mm)')
    ax1.set_ylabel('Y (mm)')
    ax1.set_zlabel('Z (mm)')
    ax1.legend()
    
    # XY平面视图
    ax2 = fig.add_subplot(222)
    ax2.scatter(point_cloud[:, 0], point_cloud[:, 1], c='lightgray', alpha=0.3, s=1)
    ax2.scatter(true_keypoints[:, 0], true_keypoints[:, 1], c='green', s=100, alpha=0.8, marker='o')
    ax2.scatter(pred_keypoints[:, 0], pred_keypoints[:, 1], c='red', s=100, alpha=0.8, marker='^')
    
    for i in range(len(true_keypoints)):
        ax2.plot([true_keypoints[i, 0], pred_keypoints[i, 0]],
                [true_keypoints[i, 1], pred_keypoints[i, 1]], 'b--', alpha=0.6)
    
    ax2.set_title('XY Plane View')
    ax2.set_xlabel('X (mm)')
    ax2.set_ylabel('Y (mm)')
    ax2.grid(True, alpha=0.3)
    ax2.axis('equal')
    
    # XZ平面视图
    ax3 = fig.add_subplot(223)
    ax3.scatter(point_cloud[:, 0], point_cloud[:, 2], c='lightgray', alpha=0.3, s=1)
    ax3.scatter(true_keypoints[:, 0], true_keypoints[:, 2], c='green', s=100, alpha=0.8, marker='o')
    ax3.scatter(pred_keypoints[:, 0], pred_keypoints[:, 2], c='red', s=100, alpha=0.8, marker='^')
    
    for i in range(len(true_keypoints)):
        ax3.plot([true_keypoints[i, 0], pred_keypoints[i, 0]],
                [true_keypoints[i, 2], pred_keypoints[i, 2]], 'b--', alpha=0.6)
    
    ax3.set_title('XZ Plane View')
    ax3.set_xlabel('X (mm)')
    ax3.set_ylabel('Z (mm)')
    ax3.grid(True, alpha=0.3)
    ax3.axis('equal')
    
    # 误差分析
    ax4 = fig.add_subplot(224)
    keypoint_indices = range(len(distances))
    bars = ax4.bar(keypoint_indices, distances, alpha=0.7)
    
    # 颜色编码：绿色(<5mm), 黄色(5-7mm), 红色(>7mm)
    for i, (bar, dist) in enumerate(zip(bars, distances)):
        if dist < 5.0:
            bar.set_color('green')
        elif dist < 7.0:
            bar.set_color('orange')
        else:
            bar.set_color('red')
    
    ax4.axhline(y=5.0, color='green', linestyle='--', alpha=0.7, label='5mm (Medical Grade)')
    ax4.axhline(y=7.0, color='orange', linestyle='--', alpha=0.7, label='7mm (Acceptable)')
    ax4.set_title(f'Per-Keypoint Error Analysis\nMean: {mean_error:.3f}mm')
    ax4.set_xlabel('Keypoint Index')
    ax4.set_ylabel('Error (mm)')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✅ 可视化已保存: {save_path}")
    
    return fig

def create_summary_visualization(predictions, save_path=None):
    """创建总结可视化"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 误差分布直方图
    all_errors = []
    for pred in predictions:
        all_errors.extend(pred['distances'])
    
    axes[0, 0].hist(all_errors, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].axvline(x=5.0, color='green', linestyle='--', label='5mm (Medical Grade)')
    axes[0, 0].axvline(x=7.0, color='orange', linestyle='--', label='7mm (Acceptable)')
    axes[0, 0].axvline(x=np.mean(all_errors), color='red', linestyle='-', label=f'Mean: {np.mean(all_errors):.3f}mm')
    axes[0, 0].set_title('Error Distribution (All Keypoints)')
    axes[0, 0].set_xlabel('Error (mm)')
    axes[0, 0].set_ylabel('Frequency')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 每个样本的平均误差
    sample_ids = [pred['sample_id'] for pred in predictions]
    mean_errors = [pred['mean_error'] for pred in predictions]
    
    bars = axes[0, 1].bar(range(len(sample_ids)), mean_errors, alpha=0.7)
    for i, (bar, error) in enumerate(zip(bars, mean_errors)):
        if error < 5.0:
            bar.set_color('green')
        elif error < 7.0:
            bar.set_color('orange')
        else:
            bar.set_color('red')
    
    axes[0, 1].axhline(y=5.0, color='green', linestyle='--', alpha=0.7)
    axes[0, 1].axhline(y=7.0, color='orange', linestyle='--', alpha=0.7)
    axes[0, 1].set_title('Mean Error per Sample')
    axes[0, 1].set_xlabel('Sample Index')
    axes[0, 1].set_ylabel('Mean Error (mm)')
    axes[0, 1].set_xticks(range(len(sample_ids)))
    axes[0, 1].set_xticklabels([f'{sid}' for sid in sample_ids], rotation=45)
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 关键点误差热图
    keypoint_errors = np.array([pred['distances'] for pred in predictions])
    im = axes[1, 0].imshow(keypoint_errors, cmap='RdYlGn_r', aspect='auto')
    axes[1, 0].set_title('Keypoint Error Heatmap')
    axes[1, 0].set_xlabel('Keypoint Index')
    axes[1, 0].set_ylabel('Sample Index')
    axes[1, 0].set_yticks(range(len(sample_ids)))
    axes[1, 0].set_yticklabels(sample_ids)
    plt.colorbar(im, ax=axes[1, 0], label='Error (mm)')
    
    # 4. 性能统计
    within_5mm = np.mean(np.array(all_errors) <= 5.0) * 100
    within_7mm = np.mean(np.array(all_errors) <= 7.0) * 100
    overall_mean = np.mean(all_errors)
    overall_std = np.std(all_errors)
    
    stats_text = f"""
    Performance Statistics:
    
    Overall Mean Error: {overall_mean:.3f} ± {overall_std:.3f} mm
    
    Success Rates:
    • Within 5mm (Medical Grade): {within_5mm:.1f}%
    • Within 7mm (Acceptable): {within_7mm:.1f}%
    
    Per-Sample Mean Errors:
    • Best: {min(mean_errors):.3f}mm
    • Worst: {max(mean_errors):.3f}mm
    • Average: {np.mean(mean_errors):.3f}mm
    
    Model: Ensemble Double SoftMax
    Keypoints: 12
    """
    
    axes[1, 1].text(0.05, 0.95, stats_text, transform=axes[1, 1].transAxes, 
                   fontsize=10, verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    axes[1, 1].set_xlim(0, 1)
    axes[1, 1].set_ylim(0, 1)
    axes[1, 1].axis('off')
    axes[1, 1].set_title('Performance Summary')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✅ 总结可视化已保存: {save_path}")
    
    return fig

def main():
    """主函数"""
    
    print("🎨 **集成双Softmax模型预测效果可视化**")
    print("🎯 **展示预测点和真实点在点云上的对比**")
    print("=" * 80)
    
    # 1. 加载最佳模型
    model_info = load_best_model()
    if model_info is None:
        print("❌ 无法加载模型，退出")
        return
    
    model, device, best_error = model_info
    
    # 2. 获取测试数据
    dataset = get_test_samples()
    
    # 3. 预测关键点
    predictions = predict_keypoints(model, device, dataset, num_samples=5)
    
    # 4. 创建可视化目录
    os.makedirs('visualizations', exist_ok=True)
    
    # 5. 为每个样本创建详细可视化
    print(f"\n🎨 **创建详细可视化**")
    for i, prediction in enumerate(predictions):
        sample_id = prediction['sample_id']
        save_path = f'visualizations/sample_{sample_id}_prediction.png'
        
        fig = create_3d_visualization(prediction, save_path)
        plt.close(fig)  # 关闭图形以节省内存
        
        print(f"   样本 {sample_id}: 误差 {prediction['mean_error']:.3f}mm")
    
    # 6. 创建总结可视化
    print(f"\n📊 **创建总结可视化**")
    summary_fig = create_summary_visualization(predictions, 'visualizations/prediction_summary.png')
    plt.close(summary_fig)
    
    # 7. 保存预测结果
    results = {
        'model_error': float(best_error),
        'num_samples': len(predictions),
        'predictions': []
    }
    
    for pred in predictions:
        results['predictions'].append({
            'sample_id': pred['sample_id'],
            'mean_error': float(pred['mean_error']),
            'keypoint_errors': pred['distances'].tolist(),
            'within_5mm_percent': float(np.mean(pred['distances'] <= 5.0) * 100),
            'within_7mm_percent': float(np.mean(pred['distances'] <= 7.0) * 100)
        })
    
    with open('visualizations/prediction_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # 8. 打印总结
    all_errors = []
    for pred in predictions:
        all_errors.extend(pred['distances'])
    
    print(f"\n🎉 **可视化完成!**")
    print("=" * 50)
    print(f"📁 可视化文件保存在: visualizations/")
    print(f"📊 样本数量: {len(predictions)}")
    print(f"🎯 整体平均误差: {np.mean(all_errors):.3f}mm")
    print(f"✅ 5mm内成功率: {np.mean(np.array(all_errors) <= 5.0) * 100:.1f}%")
    print(f"✅ 7mm内成功率: {np.mean(np.array(all_errors) <= 7.0) * 100:.1f}%")
    print(f"📈 模型基线误差: {best_error:.3f}mm")
    
    print(f"\n💡 **查看可视化**:")
    print(f"   - 详细预测: visualizations/sample_*_prediction.png")
    print(f"   - 总结分析: visualizations/prediction_summary.png")
    print(f"   - 数值结果: visualizations/prediction_results.json")

if __name__ == "__main__":
    main()
