#!/usr/bin/env python3
"""
基于数据增强的数据集扩展策略
Data Augmentation-Based Dataset Expansion Strategy
通过智能数据增强而非添加新数据来扩展数据集
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import json
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from scipy.spatial.transform import Rotation as R
import warnings
warnings.filterwarnings('ignore')

class DataAugmentationExpander:
    """基于数据增强的扩展器"""
    
    def __init__(self, device='cuda:1'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        self.augmentation_history = []
        
    def load_baseline_data(self):
        """加载基线数据"""
        print("📥 加载基线12关键点数据集")
        print("=" * 50)
        
        try:
            # 加载女性数据集
            female_data = np.load('archive/old_experiments/f3_reduced_12kp_female.npz')
            female_pc = female_data['point_clouds']
            female_kp = female_data['keypoints']
            
            # 加载男性数据集
            male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz')
            male_pc = male_data['point_clouds']
            male_kp = male_data['keypoints']
            
            print(f"✅ 基线数据集:")
            print(f"   女性: {len(female_pc)}样本")
            print(f"   男性: {len(male_pc)}样本")
            print(f"   总计: {len(female_pc) + len(male_pc)}样本")
            
            return female_pc, female_kp, male_pc, male_kp
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return None, None, None, None
    
    def anatomically_aware_augmentation(self, pc, kp, num_augmentations=3):
        """解剖学感知的数据增强"""
        augmented_pc = []
        augmented_kp = []
        
        for i in range(num_augmentations):
            # 1. 小幅旋转 (±5度)
            rotation_angle = np.random.uniform(-5, 5) * np.pi / 180
            rotation_axis = np.random.choice([0, 1, 2])  # x, y, z轴
            
            rotation_matrix = np.eye(3)
            if rotation_axis == 0:  # 绕x轴旋转
                rotation_matrix = np.array([
                    [1, 0, 0],
                    [0, np.cos(rotation_angle), -np.sin(rotation_angle)],
                    [0, np.sin(rotation_angle), np.cos(rotation_angle)]
                ])
            elif rotation_axis == 1:  # 绕y轴旋转
                rotation_matrix = np.array([
                    [np.cos(rotation_angle), 0, np.sin(rotation_angle)],
                    [0, 1, 0],
                    [-np.sin(rotation_angle), 0, np.cos(rotation_angle)]
                ])
            else:  # 绕z轴旋转
                rotation_matrix = np.array([
                    [np.cos(rotation_angle), -np.sin(rotation_angle), 0],
                    [np.sin(rotation_angle), np.cos(rotation_angle), 0],
                    [0, 0, 1]
                ])
            
            # 2. 小幅缩放 (0.95-1.05)
            scale_factor = np.random.uniform(0.95, 1.05)
            
            # 3. 小幅平移 (±2mm)
            translation = np.random.uniform(-2, 2, 3)
            
            # 4. 高斯噪声 (σ=0.1mm)
            noise_pc = np.random.normal(0, 0.1, pc.shape)
            noise_kp = np.random.normal(0, 0.05, kp.shape)  # 关键点噪声更小
            
            # 应用变换
            # 计算中心点
            center = np.mean(kp, axis=0)
            
            # 变换点云
            pc_centered = pc - center
            pc_transformed = (pc_centered @ rotation_matrix.T) * scale_factor + center + translation + noise_pc
            
            # 变换关键点
            kp_centered = kp - center
            kp_transformed = (kp_centered @ rotation_matrix.T) * scale_factor + center + translation + noise_kp
            
            augmented_pc.append(pc_transformed)
            augmented_kp.append(kp_transformed)
        
        return np.array(augmented_pc), np.array(augmented_kp)
    
    def smart_augmentation_strategy(self, female_pc, female_kp, male_pc, male_kp, 
                                   target_female_samples=50, target_male_samples=100):
        """智能数据增强策略"""
        print(f"\n🎯 智能数据增强策略")
        print("=" * 50)
        
        print(f"目标:")
        print(f"  女性样本: {len(female_pc)} → {target_female_samples}")
        print(f"  男性样本: {len(male_pc)} → {target_male_samples}")
        
        # 计算需要增强的数量
        female_augmentations_needed = max(0, target_female_samples - len(female_pc))
        male_augmentations_needed = max(0, target_male_samples - len(male_pc))
        
        print(f"\n需要增强:")
        print(f"  女性: {female_augmentations_needed}个")
        print(f"  男性: {male_augmentations_needed}个")
        
        # 女性数据增强
        augmented_female_pc = [female_pc]
        augmented_female_kp = [female_kp]
        
        if female_augmentations_needed > 0:
            print(f"\n🔄 女性数据增强中...")
            augmentations_per_sample = female_augmentations_needed // len(female_pc) + 1
            
            for i in range(len(female_pc)):
                aug_pc, aug_kp = self.anatomically_aware_augmentation(
                    female_pc[i], female_kp[i], augmentations_per_sample)
                augmented_female_pc.append(aug_pc)
                augmented_female_kp.append(aug_kp)
        
        # 男性数据增强
        augmented_male_pc = [male_pc]
        augmented_male_kp = [male_kp]
        
        if male_augmentations_needed > 0:
            print(f"🔄 男性数据增强中...")
            augmentations_per_sample = male_augmentations_needed // len(male_pc) + 1
            
            for i in range(len(male_pc)):
                aug_pc, aug_kp = self.anatomically_aware_augmentation(
                    male_pc[i], male_kp[i], augmentations_per_sample)
                augmented_male_pc.append(aug_pc)
                augmented_male_kp.append(aug_kp)
        
        # 合并数据
        final_female_pc = np.vstack(augmented_female_pc)[:target_female_samples]
        final_female_kp = np.vstack(augmented_female_kp)[:target_female_samples]
        final_male_pc = np.vstack(augmented_male_pc)[:target_male_samples]
        final_male_kp = np.vstack(augmented_male_kp)[:target_male_samples]
        
        print(f"\n✅ 增强完成:")
        print(f"  女性: {len(final_female_pc)}样本")
        print(f"  男性: {len(final_male_pc)}样本")
        print(f"  总计: {len(final_female_pc) + len(final_male_pc)}样本")
        
        return final_female_pc, final_female_kp, final_male_pc, final_male_kp
    
    def create_improved_model(self):
        """创建改进的模型"""
        class ImprovedKeypointModel(nn.Module):
            def __init__(self, num_points=50000, num_keypoints=12):
                super().__init__()
                self.num_keypoints = num_keypoints
                
                # 更深的特征提取网络
                self.feature_extractor = nn.Sequential(
                    nn.Conv1d(3, 64, 1),
                    nn.BatchNorm1d(64),
                    nn.ReLU(),
                    nn.Conv1d(64, 128, 1),
                    nn.BatchNorm1d(128),
                    nn.ReLU(),
                    nn.Conv1d(128, 256, 1),
                    nn.BatchNorm1d(256),
                    nn.ReLU(),
                    nn.Conv1d(256, 512, 1),
                    nn.BatchNorm1d(512),
                    nn.ReLU(),
                )
                
                # 注意力机制
                self.attention = nn.Sequential(
                    nn.Conv1d(512, 256, 1),
                    nn.BatchNorm1d(256),
                    nn.ReLU(),
                    nn.Conv1d(256, 1, 1),
                    nn.Sigmoid()
                )
                
                # 回归头
                self.regressor = nn.Sequential(
                    nn.Linear(512, 256),
                    nn.BatchNorm1d(256),
                    nn.ReLU(),
                    nn.Dropout(0.3),
                    nn.Linear(256, 128),
                    nn.BatchNorm1d(128),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(128, num_keypoints * 3)
                )
            
            def forward(self, x):
                batch_size = x.size(0)
                x = x.transpose(2, 1)  # [B, 3, N]
                
                # 特征提取
                features = self.feature_extractor(x)  # [B, 512, N]
                
                # 注意力权重
                attention_weights = self.attention(features)  # [B, 1, N]
                
                # 加权全局特征
                weighted_features = features * attention_weights  # [B, 512, N]
                global_feat = torch.sum(weighted_features, dim=2)  # [B, 512]
                
                # 回归
                output = self.regressor(global_feat)  # [B, num_keypoints*3]
                return output.view(batch_size, self.num_keypoints, 3)
        
        return ImprovedKeypointModel()
    
    def train_with_augmented_data(self, female_pc, female_kp, male_pc, male_kp, stage_name):
        """使用增强数据训练模型"""
        print(f"\n🎯 使用增强数据训练模型 - {stage_name}")
        print("=" * 50)
        
        # 合并数据
        all_pc = np.vstack([female_pc, male_pc])
        all_kp = np.vstack([female_kp, male_kp])
        
        print(f"训练数据: {len(all_pc)}样本")
        
        # 数据分割
        train_pc, test_pc, train_kp, test_kp = train_test_split(
            all_pc, all_kp, test_size=0.2, random_state=42)
        
        # 转换为张量
        train_pc_tensor = torch.FloatTensor(train_pc).to(self.device)
        train_kp_tensor = torch.FloatTensor(train_kp).to(self.device)
        test_pc_tensor = torch.FloatTensor(test_pc).to(self.device)
        test_kp_tensor = torch.FloatTensor(test_kp).to(self.device)
        
        # 创建数据加载器
        train_dataset = TensorDataset(train_pc_tensor, train_kp_tensor)
        train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True)
        
        # 创建改进的模型
        model = self.create_improved_model().to(self.device)
        optimizer = optim.AdamW(model.parameters(), lr=0.0005, weight_decay=1e-4)
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=100, eta_min=1e-6)
        criterion = nn.MSELoss()
        
        print(f"模型参数: {sum(p.numel() for p in model.parameters()):,}")
        
        # 训练循环
        model.train()
        best_loss = float('inf')
        patience = 0
        
        for epoch in range(100):
            epoch_loss = 0.0
            
            for batch_pc, batch_kp in train_loader:
                optimizer.zero_grad()
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                loss.backward()
                optimizer.step()
                epoch_loss += loss.item()
            
            scheduler.step()
            avg_loss = epoch_loss / len(train_loader)
            
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience = 0
                torch.save(model.state_dict(), f'best_augmented_{stage_name}_model.pth')
            else:
                patience += 1
                if patience >= 15:
                    print(f"早停于epoch {epoch+1}")
                    break
            
            if epoch % 20 == 0:
                print(f"Epoch {epoch+1}: Loss = {avg_loss:.6f}, LR = {scheduler.get_last_lr()[0]:.2e}")
        
        # 加载最佳模型并测试
        model.load_state_dict(torch.load(f'best_augmented_{stage_name}_model.pth'))
        model.eval()
        
        with torch.no_grad():
            predicted = model(test_pc_tensor)
            test_errors = torch.norm(predicted - test_kp_tensor, dim=2)
            avg_error = torch.mean(test_errors).item()
            
            # 计算准确率
            sample_errors = torch.mean(test_errors, dim=1)
            errors_5mm = torch.sum(sample_errors <= 5.0).item()
            errors_10mm = torch.sum(sample_errors <= 10.0).item()
            
            acc_5mm = (errors_5mm / len(test_pc)) * 100
            acc_10mm = (errors_10mm / len(test_pc)) * 100
        
        result = {
            'stage': stage_name,
            'samples': len(all_pc),
            'female_samples': len(female_pc),
            'male_samples': len(male_pc),
            'avg_error': avg_error,
            'accuracy_5mm': acc_5mm,
            'accuracy_10mm': acc_10mm,
            'medical_grade': avg_error <= 10.0,
            'augmentation_method': 'anatomically_aware'
        }
        
        print(f"\n📊 {stage_name} 训练结果:")
        print(f"   样本数: {result['samples']} (女{result['female_samples']} + 男{result['male_samples']})")
        print(f"   平均误差: {result['avg_error']:.2f}mm")
        print(f"   5mm准确率: {result['accuracy_5mm']:.1f}%")
        print(f"   10mm准确率: {result['accuracy_10mm']:.1f}%")
        print(f"   医疗级达标: {'✅' if result['medical_grade'] else '❌'}")
        
        self.augmentation_history.append(result)
        return result
    
    def run_augmentation_expansion(self):
        """运行基于数据增强的扩展"""
        print("🚀 开始基于数据增强的数据集扩展")
        print("=" * 60)
        
        # 1. 加载基线数据
        female_pc, female_kp, male_pc, male_kp = self.load_baseline_data()
        if female_pc is None:
            print("❌ 无法加载基线数据，退出")
            return
        
        # 2. 基线性能测试
        print(f"\n🎯 阶段0: 基线性能测试")
        baseline_result = self.train_with_augmented_data(
            female_pc, female_kp, male_pc, male_kp, "baseline")
        
        # 3. 渐进式数据增强
        augmentation_stages = [
            {"name": "stage1_light", "female_target": 40, "male_target": 90, "description": "轻度增强"},
            {"name": "stage2_moderate", "female_target": 60, "male_target": 120, "description": "中度增强"},
            {"name": "stage3_heavy", "female_target": 80, "male_target": 150, "description": "重度增强"}
        ]
        
        for stage in augmentation_stages:
            print(f"\n🎯 {stage['name']}: {stage['description']}")
            
            # 数据增强
            aug_female_pc, aug_female_kp, aug_male_pc, aug_male_kp = \
                self.smart_augmentation_strategy(
                    female_pc, female_kp, male_pc, male_kp,
                    target_female_samples=stage['female_target'],
                    target_male_samples=stage['male_target'])
            
            # 训练和测试
            stage_result = self.train_with_augmented_data(
                aug_female_pc, aug_female_kp, aug_male_pc, aug_male_kp,
                stage['name'])
        
        # 4. 保存结果
        self.save_augmentation_results()
        
        return self.augmentation_history
    
    def save_augmentation_results(self):
        """保存增强结果"""
        results = {
            'augmentation_history': self.augmentation_history,
            'summary': {
                'total_stages': len(self.augmentation_history),
                'final_samples': self.augmentation_history[-1]['samples'] if self.augmentation_history else 0,
                'final_performance': self.augmentation_history[-1]['avg_error'] if self.augmentation_history else 0,
                'improvement': (self.augmentation_history[0]['avg_error'] - self.augmentation_history[-1]['avg_error']) if len(self.augmentation_history) > 1 else 0,
                'method': 'anatomically_aware_augmentation'
            },
            'timestamp': '2025-07-25'
        }
        
        with open('augmentation_expansion_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 增强结果已保存到 augmentation_expansion_results.json")

def main():
    """主函数"""
    print("🎯 基于数据增强的数据集扩展")
    print("Data Augmentation-Based Dataset Expansion")
    print("=" * 60)
    
    # 创建增强扩展器
    expander = DataAugmentationExpander()
    
    # 运行数据增强扩展
    results = expander.run_augmentation_expansion()
    
    if results:
        print(f"\n🎉 数据增强扩展完成总结:")
        print(f"✅ 完成{len(results)}个增强阶段")
        print(f"📊 最终样本数: {results[-1]['samples']}")
        print(f"🎯 最终性能: {results[-1]['avg_error']:.2f}mm")
        print(f"📈 性能改进: {(results[0]['avg_error'] - results[-1]['avg_error']):.2f}mm")
        print(f"🔧 方法: 解剖学感知数据增强")
    else:
        print("❌ 增强过程中出现问题")

if __name__ == "__main__":
    main()
