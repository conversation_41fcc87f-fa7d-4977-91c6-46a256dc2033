#!/usr/bin/env python3
"""
关键点难度分析
Keypoint Difficulty Analysis
分析57个关键点的预测难度，筛选出最容易预测的点
"""

import torch
import torch.nn as nn
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import json
from tqdm import tqdm
import matplotlib.pyplot as plt

class KeypointDifficultyAnalyzer:
    """关键点难度分析器"""
    
    def __init__(self, dataset_path='high_quality_pelvis_57_dataset.npz'):
        self.dataset_path = dataset_path
        self.load_dataset()
        
    def load_dataset(self):
        """加载数据集"""
        print("📊 加载高质量57点数据集...")
        data = np.load(self.dataset_path, allow_pickle=True)
        
        self.point_clouds = data['point_clouds']
        self.keypoints_57 = data['keypoints_57']
        self.sample_ids = data['sample_ids']
        
        print(f"✅ 数据集加载完成: {len(self.sample_ids)} 个样本")
        
    def analyze_keypoint_variance(self):
        """分析关键点的方差 - 方差大的点通常更难预测"""
        print("🔍 分析关键点方差...")
        
        # 计算每个关键点在所有样本中的方差
        keypoint_variances = []
        keypoint_ranges = []
        
        for kp_idx in range(57):
            # 提取所有样本的第kp_idx个关键点
            all_coords = self.keypoints_57[:, kp_idx, :]  # [N, 3]
            
            # 计算方差和范围
            variance = np.var(all_coords, axis=0)  # [3] - X,Y,Z方差
            coord_range = np.ptp(all_coords, axis=0)  # [3] - X,Y,Z范围
            
            # 总体方差和范围
            total_variance = np.mean(variance)
            total_range = np.mean(coord_range)
            
            keypoint_variances.append(total_variance)
            keypoint_ranges.append(total_range)
        
        keypoint_variances = np.array(keypoint_variances)
        keypoint_ranges = np.array(keypoint_ranges)
        
        # 按区域分组
        f1_variances = keypoint_variances[0:19]
        f2_variances = keypoint_variances[19:38]
        f3_variances = keypoint_variances[38:57]
        
        print(f"📊 关键点方差分析:")
        print(f"   F1区域平均方差: {np.mean(f1_variances):.2f}")
        print(f"   F2区域平均方差: {np.mean(f2_variances):.2f}")
        print(f"   F3区域平均方差: {np.mean(f3_variances):.2f}")
        
        return keypoint_variances, keypoint_ranges
    
    def analyze_prediction_errors(self):
        """通过简单模型分析各点的预测误差"""
        print("🔍 分析各关键点的预测误差...")
        
        # 使用我们最佳的终极模型来分析各点误差
        from final_optimized_model import UltimatePointNet57, create_ultimate_normalization
        
        # 数据预处理
        normalized_pc, normalized_kp, scalers = create_ultimate_normalization(
            self.point_clouds, self.keypoints_57
        )
        
        # 数据划分
        indices = np.arange(len(normalized_pc))
        train_indices, test_indices = train_test_split(indices, test_size=0.3, random_state=42)
        
        # 加载最佳模型
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        model = UltimatePointNet57(dropout_rate=0.15).to(device)
        
        try:
            model.load_state_dict(torch.load('best_ultimate_model.pth', map_location=device))
            print("✅ 加载最佳终极模型")
        except:
            print("⚠️ 未找到最佳模型，使用随机初始化")
        
        model.eval()
        
        # 分析各点误差
        keypoint_errors = []
        
        with torch.no_grad():
            for i in test_indices:
                pc = torch.FloatTensor(normalized_pc[i]).transpose(0, 1).unsqueeze(0).to(device)
                kp_true = torch.FloatTensor(normalized_kp[i]).to(device)
                
                kp_pred = model(pc).squeeze(0)
                
                # 计算每个关键点的误差
                errors = torch.norm(kp_pred - kp_true, dim=1).cpu().numpy()
                keypoint_errors.append(errors)
        
        keypoint_errors = np.array(keypoint_errors)  # [N_test, 57]
        avg_keypoint_errors = np.mean(keypoint_errors, axis=0)  # [57]
        
        print(f"📊 各关键点平均预测误差:")
        print(f"   F1区域: {np.mean(avg_keypoint_errors[0:19]):.3f}")
        print(f"   F2区域: {np.mean(avg_keypoint_errors[19:38]):.3f}")
        print(f"   F3区域: {np.mean(avg_keypoint_errors[38:57]):.3f}")
        
        return avg_keypoint_errors
    
    def rank_keypoints_by_difficulty(self, keypoint_variances, keypoint_errors):
        """根据难度对关键点排序"""
        print("🎯 根据难度对关键点排序...")
        
        # 归一化方差和误差
        norm_variances = (keypoint_variances - np.min(keypoint_variances)) / (np.max(keypoint_variances) - np.min(keypoint_variances))
        norm_errors = (keypoint_errors - np.min(keypoint_errors)) / (np.max(keypoint_errors) - np.min(keypoint_errors))
        
        # 综合难度分数 (方差权重0.3，误差权重0.7)
        difficulty_scores = 0.3 * norm_variances + 0.7 * norm_errors
        
        # 创建关键点信息
        keypoint_info = []
        for i in range(57):
            region = 'F1' if i < 19 else ('F2' if i < 38 else 'F3')
            region_idx = i if i < 19 else (i - 19 if i < 38 else i - 38)
            
            keypoint_info.append({
                'global_idx': i,
                'region': region,
                'region_idx': region_idx,
                'variance': keypoint_variances[i],
                'error': keypoint_errors[i],
                'difficulty': difficulty_scores[i]
            })
        
        # 按难度排序
        keypoint_info.sort(key=lambda x: x['difficulty'])
        
        print(f"📋 关键点难度排序 (前10个最容易预测):")
        print(f"{'排名':<4} {'全局ID':<6} {'区域':<4} {'区域ID':<6} {'方差':<8} {'误差':<8} {'难度':<8}")
        print("-" * 60)
        
        for i, kp in enumerate(keypoint_info[:10]):
            print(f"{i+1:<4} {kp['global_idx']:<6} {kp['region']:<4} {kp['region_idx']:<6} "
                  f"{kp['variance']:<8.3f} {kp['error']:<8.3f} {kp['difficulty']:<8.3f}")
        
        print(f"\n📋 关键点难度排序 (后10个最难预测):")
        print(f"{'排名':<4} {'全局ID':<6} {'区域':<4} {'区域ID':<6} {'方差':<8} {'误差':<8} {'难度':<8}")
        print("-" * 60)
        
        for i, kp in enumerate(keypoint_info[-10:], len(keypoint_info)-9):
            print(f"{i:<4} {kp['global_idx']:<6} {kp['region']:<4} {kp['region_idx']:<6} "
                  f"{kp['variance']:<8.3f} {kp['error']:<8.3f} {kp['difficulty']:<8.3f}")
        
        return keypoint_info
    
    def suggest_keypoint_subsets(self, keypoint_info):
        """建议不同数量的关键点子集"""
        print("\n🎯 建议关键点子集...")
        
        # 确保每个区域都有代表
        def create_balanced_subset(n_points):
            # 按区域分组
            f1_points = [kp for kp in keypoint_info if kp['region'] == 'F1']
            f2_points = [kp for kp in keypoint_info if kp['region'] == 'F2']
            f3_points = [kp for kp in keypoint_info if kp['region'] == 'F3']
            
            # 每个区域选择最容易的点
            n_per_region = n_points // 3
            remainder = n_points % 3
            
            selected = []
            selected.extend(f1_points[:n_per_region + (1 if remainder > 0 else 0)])
            selected.extend(f2_points[:n_per_region + (1 if remainder > 1 else 0)])
            selected.extend(f3_points[:n_per_region])
            
            return selected[:n_points]
        
        # 建议不同的子集
        subsets = {
            12: create_balanced_subset(12),
            18: create_balanced_subset(18),
            24: create_balanced_subset(24),
            30: create_balanced_subset(30),
            36: create_balanced_subset(36),
            42: create_balanced_subset(42)
        }
        
        print(f"📊 建议的关键点子集:")
        for n_points, subset in subsets.items():
            f1_count = sum(1 for kp in subset if kp['region'] == 'F1')
            f2_count = sum(1 for kp in subset if kp['region'] == 'F2')
            f3_count = sum(1 for kp in subset if kp['region'] == 'F3')
            
            avg_difficulty = np.mean([kp['difficulty'] for kp in subset])
            
            print(f"   {n_points:2d}点: F1={f1_count}, F2={f2_count}, F3={f3_count}, "
                  f"平均难度={avg_difficulty:.3f}")
            
            # 显示选中的点
            global_indices = [kp['global_idx'] for kp in subset]
            print(f"       全局索引: {global_indices}")
        
        return subsets
    
    def save_analysis_results(self, keypoint_info, subsets):
        """保存分析结果"""
        
        results = {
            'analysis_method': 'variance_and_prediction_error',
            'total_keypoints': 57,
            'keypoint_ranking': keypoint_info,
            'suggested_subsets': {}
        }
        
        for n_points, subset in subsets.items():
            results['suggested_subsets'][n_points] = {
                'global_indices': [kp['global_idx'] for kp in subset],
                'region_distribution': {
                    'F1': sum(1 for kp in subset if kp['region'] == 'F1'),
                    'F2': sum(1 for kp in subset if kp['region'] == 'F2'),
                    'F3': sum(1 for kp in subset if kp['region'] == 'F3')
                },
                'avg_difficulty': float(np.mean([kp['difficulty'] for kp in subset])),
                'expected_performance_improvement': f"预期比57点提升{(57/n_points-1)*100:.1f}%"
            }
        
        with open('keypoint_difficulty_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 分析结果已保存: keypoint_difficulty_analysis.json")

def main():
    """主函数"""
    
    print("🎯 关键点难度分析")
    print("分析57个关键点的预测难度，筛选最容易预测的点")
    print("=" * 80)
    
    # 创建分析器
    analyzer = KeypointDifficultyAnalyzer()
    
    # 1. 分析关键点方差
    keypoint_variances, keypoint_ranges = analyzer.analyze_keypoint_variance()
    
    # 2. 分析预测误差
    try:
        keypoint_errors = analyzer.analyze_prediction_errors()
    except Exception as e:
        print(f"⚠️ 预测误差分析失败，使用方差作为替代: {e}")
        keypoint_errors = keypoint_variances
    
    # 3. 难度排序
    keypoint_info = analyzer.rank_keypoints_by_difficulty(keypoint_variances, keypoint_errors)
    
    # 4. 建议子集
    subsets = analyzer.suggest_keypoint_subsets(keypoint_info)
    
    # 5. 保存结果
    analyzer.save_analysis_results(keypoint_info, subsets)
    
    print(f"\n🎉 关键点难度分析完成！")
    print(f"💡 主要发现:")
    print(f"   ✅ 识别了最容易和最难预测的关键点")
    print(f"   ✅ 建议了6种不同数量的关键点子集")
    print(f"   ✅ 每个子集都保持区域平衡")
    
    print(f"\n🚀 下一步建议:")
    print(f"   1. 从12点子集开始验证")
    print(f"   2. 逐步增加到18点、24点等")
    print(f"   3. 找到性能和复杂度的最佳平衡点")
    print(f"   4. 目标：在保持高性能的同时简化任务")

if __name__ == "__main__":
    main()
