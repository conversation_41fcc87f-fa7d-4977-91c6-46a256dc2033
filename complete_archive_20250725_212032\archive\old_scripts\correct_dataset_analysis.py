#!/usr/bin/env python3
"""
正确的数据集分析
重新检查数据集，之前的分析可能有误
"""

import numpy as np
import matplotlib.pyplot as plt
import json

def correct_dataset_analysis():
    """正确分析数据集"""
    print("🔍 **重新分析数据集**")
    print("🎯 **检查之前分析的错误**")
    print("=" * 60)
    
    # 加载数据
    data = np.load('f3_reduced_12kp_stable.npz', allow_pickle=True)
    sample_ids = data['sample_ids']
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    
    print(f"📊 基本信息:")
    print(f"   样本数: {len(sample_ids)}")
    print(f"   点云数组形状: {point_clouds.shape}")
    print(f"   关键点数组形状: {keypoints.shape}")
    
    # 检查前几个样本的实际数据
    print(f"\n🔍 检查前3个样本的实际数据:")
    for i in range(min(3, len(sample_ids))):
        pc = point_clouds[i]
        kp = keypoints[i]
        
        print(f"\n样本 {i} (ID: {sample_ids[i]}):")
        print(f"   点云形状: {pc.shape}")
        print(f"   点云范围: X[{pc[:, 0].min():.3f}, {pc[:, 0].max():.3f}], "
              f"Y[{pc[:, 1].min():.3f}, {pc[:, 1].max():.3f}], "
              f"Z[{pc[:, 2].min():.3f}, {pc[:, 2].max():.3f}]")
        print(f"   关键点形状: {kp.shape}")
        print(f"   关键点范围: X[{kp[:, 0].min():.3f}, {kp[:, 0].max():.3f}], "
              f"Y[{kp[:, 1].min():.3f}, {kp[:, 1].max():.3f}], "
              f"Z[{kp[:, 2].min():.3f}, {kp[:, 2].max():.3f}]")
        
        # 检查点云是否真的有变化
        unique_points = len(np.unique(pc.reshape(-1, 3), axis=0))
        print(f"   唯一点数: {unique_points} / {len(pc)}")
        
        # 检查点云密度计算
        pc_range = np.ptp(pc, axis=0)  # 每个轴的范围
        volume = np.prod(pc_range)
        density = len(pc) / volume if volume > 0 else 0
        print(f"   点云范围: {pc_range}")
        print(f"   体积: {volume:.6f}")
        print(f"   密度: {density:.6f}")
    
    # 重新计算样本间的真实差异
    print(f"\n📏 重新计算样本间差异:")
    
    # 计算关键点的真实差异
    kp_differences = []
    for i in range(len(keypoints)):
        for j in range(i+1, min(i+6, len(keypoints))):  # 只比较前几个样本
            diff = np.linalg.norm(keypoints[i] - keypoints[j], axis=1)
            avg_diff = np.mean(diff)
            kp_differences.append(avg_diff)
            if len(kp_differences) <= 5:  # 只打印前5个
                print(f"   样本{i} vs 样本{j}: 平均关键点差异 = {avg_diff:.3f}mm")
    
    if kp_differences:
        print(f"   关键点差异统计: 平均={np.mean(kp_differences):.3f}mm, "
              f"标准差={np.std(kp_differences):.3f}mm")
    
    # 检查点云的真实差异
    print(f"\n☁️  检查点云差异:")
    pc_differences = []
    for i in range(min(3, len(point_clouds))):
        for j in range(i+1, min(i+3, len(point_clouds))):
            # 随机采样1000个点进行比较
            sample_size = min(1000, len(point_clouds[i]), len(point_clouds[j]))
            
            pc1_sample = point_clouds[i][np.random.choice(len(point_clouds[i]), sample_size, replace=False)]
            pc2_sample = point_clouds[j][np.random.choice(len(point_clouds[j]), sample_size, replace=False)]
            
            # 计算最近邻距离
            from scipy.spatial.distance import cdist
            distances = cdist(pc1_sample, pc2_sample)
            min_distances = np.min(distances, axis=1)
            avg_diff = np.mean(min_distances)
            pc_differences.append(avg_diff)
            
            print(f"   点云{i} vs 点云{j}: 平均最近邻距离 = {avg_diff:.3f}mm")
    
    # 检查数据的实际分布
    print(f"\n📊 数据分布检查:")
    
    # 关键点的实际分布
    all_kps = np.concatenate(keypoints, axis=0)
    print(f"   所有关键点统计:")
    print(f"     数量: {len(all_kps)}")
    print(f"     X轴: 均值={all_kps[:, 0].mean():.3f}, 标准差={all_kps[:, 0].std():.3f}")
    print(f"     Y轴: 均值={all_kps[:, 1].mean():.3f}, 标准差={all_kps[:, 1].std():.3f}")
    print(f"     Z轴: 均值={all_kps[:, 2].mean():.3f}, 标准差={all_kps[:, 2].std():.3f}")
    
    # 检查每个样本的关键点中心
    print(f"\n🎯 每个样本的关键点中心:")
    centers = []
    for i in range(min(10, len(keypoints))):
        center = np.mean(keypoints[i], axis=0)
        centers.append(center)
        print(f"   样本{i}: 中心 = [{center[0]:.3f}, {center[1]:.3f}, {center[2]:.3f}]")
    
    centers = np.array(centers)
    if len(centers) > 1:
        center_std = np.std(centers, axis=0)
        print(f"   中心点标准差: [{center_std[0]:.3f}, {center_std[1]:.3f}, {center_std[2]:.3f}]")
    
    # 重新评估极简基线
    print(f"\n🎯 重新评估极简基线:")
    
    # 计算真实的平均关键点
    mean_keypoints = np.mean(keypoints, axis=0)
    print(f"   平均关键点形状: {mean_keypoints.shape}")
    
    # 计算每个样本与平均值的差异
    errors = []
    for i in range(len(keypoints)):
        diff = np.linalg.norm(keypoints[i] - mean_keypoints, axis=1)
        avg_error = np.mean(diff)
        errors.append(avg_error)
    
    errors = np.array(errors)
    print(f"   极简基线误差统计:")
    print(f"     平均误差: {errors.mean():.3f}mm")
    print(f"     标准差: {errors.std():.3f}mm")
    print(f"     最小误差: {errors.min():.3f}mm")
    print(f"     最大误差: {errors.max():.3f}mm")
    
    # 检查测试集的极简基线性能
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    test_mask = np.isin(sample_ids, test_samples)
    train_mask = ~test_mask
    
    if np.any(train_mask) and np.any(test_mask):
        # 用训练集计算平均关键点
        train_keypoints = keypoints[train_mask]
        test_keypoints = keypoints[test_mask]
        
        train_mean = np.mean(train_keypoints, axis=0)
        
        # 在测试集上评估
        test_errors = []
        for test_kp in test_keypoints:
            diff = np.linalg.norm(test_kp - train_mean, axis=1)
            avg_error = np.mean(diff)
            test_errors.append(avg_error)
        
        test_errors = np.array(test_errors)
        print(f"\n📊 测试集极简基线性能:")
        print(f"   训练集样本数: {len(train_keypoints)}")
        print(f"   测试集样本数: {len(test_keypoints)}")
        print(f"   测试集平均误差: {test_errors.mean():.3f}mm")
        print(f"   测试集标准差: {test_errors.std():.3f}mm")
        
        # 这应该接近我们之前看到的6.041mm
        print(f"\n🔍 与之前分析对比:")
        print(f"   之前报告的极简基线: 6.041mm")
        print(f"   重新计算的结果: {test_errors.mean():.3f}mm")
        print(f"   差异: {abs(test_errors.mean() - 6.041):.3f}mm")
    
    print(f"\n💡 **分析结论**:")
    if errors.std() > 5.0:
        print(f"   ✅ 数据集有足够的变异性 (标准差 {errors.std():.3f}mm)")
        print(f"   ✅ 可以支持深度学习训练")
    else:
        print(f"   ⚠️  数据集变异性较小 (标准差 {errors.std():.3f}mm)")
        print(f"   💡 这解释了为什么极简基线表现很好")
    
    if errors.mean() < 10.0:
        print(f"   📊 极简基线性能合理 ({errors.mean():.3f}mm)")
    else:
        print(f"   ❓ 极简基线性能需要进一步分析")

if __name__ == "__main__":
    correct_dataset_analysis()
