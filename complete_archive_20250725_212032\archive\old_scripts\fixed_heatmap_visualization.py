#!/usr/bin/env python3
"""
修复的热图可视化
直接解决可视化问题，确保热图有明显的颜色差异
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps

def create_enhanced_colormap():
    """创建增强的颜色映射"""
    # 创建更鲜明的颜色映射
    colors = ['#000033', '#000080', '#0000FF', '#00FFFF', '#00FF00', '#FFFF00', '#FF8000', '#FF0000', '#FFFFFF']
    n_bins = 256
    cmap = LinearSegmentedColormap.from_list('enhanced_heatmap', colors, N=n_bins)
    return cmap

def enhance_heatmap_contrast(heatmap, method='power'):
    """增强热图对比度"""
    
    if method == 'power':
        # 幂函数增强
        enhanced = np.power(heatmap, 0.3)
    elif method == 'log':
        # 对数增强
        enhanced = np.log1p(heatmap * 100) / np.log1p(100)
    elif method == 'sigmoid':
        # Sigmoid增强
        enhanced = 1 / (1 + np.exp(-10 * (heatmap - 0.5)))
    elif method == 'percentile':
        # 百分位数拉伸
        p5, p95 = np.percentile(heatmap, [5, 95])
        enhanced = np.clip((heatmap - p5) / (p95 - p5), 0, 1)
    else:
        # 线性拉伸
        enhanced = (heatmap - np.min(heatmap)) / (np.max(heatmap) - np.min(heatmap))
    
    return enhanced

def create_synthetic_heatmap_demo():
    """创建合成热图演示"""
    print("🎨 **创建合成热图演示**")
    
    # 生成合成点云
    np.random.seed(42)
    n_points = 5000
    
    # 创建一个球形点云
    phi = np.random.uniform(0, 2*np.pi, n_points)
    costheta = np.random.uniform(-1, 1, n_points)
    u = np.random.uniform(0, 1, n_points)
    
    theta = np.arccos(costheta)
    r = 10 * np.cbrt(u)
    
    x = r * np.sin(theta) * np.cos(phi)
    y = r * np.sin(theta) * np.sin(phi)
    z = r * np.cos(theta)
    
    point_cloud = np.column_stack([x, y, z])
    
    # 创建几个合成关键点
    keypoints = np.array([
        [5, 0, 0],    # 右侧
        [-5, 0, 0],   # 左侧
        [0, 5, 0],    # 前方
        [0, -5, 0],   # 后方
        [0, 0, 5],    # 上方
        [0, 0, -5]    # 下方
    ])
    
    # 生成合成热图
    heatmaps = []
    for kp in keypoints:
        distances = np.linalg.norm(point_cloud - kp, axis=1)
        heatmap = np.exp(-distances**2 / (2 * 3.0**2))  # sigma=3.0
        heatmaps.append(heatmap)
    
    heatmaps = np.array(heatmaps).T  # [N, 6]
    
    # 创建可视化
    fig, axes = plt.subplots(2, 3, figsize=(18, 12), subplot_kw={'projection': '3d'})
    cmap = create_enhanced_colormap()
    
    for i in range(6):
        row = i // 3
        col = i % 3
        ax = axes[row, col]
        
        heatmap = heatmaps[:, i]
        
        # 增强对比度
        heatmap_enhanced = enhance_heatmap_contrast(heatmap, method='power')
        
        # 创建散点图
        scatter = ax.scatter(point_cloud[:, 0], point_cloud[:, 1], point_cloud[:, 2], 
                           c=heatmap_enhanced, cmap=cmap, s=2, alpha=0.8, vmin=0, vmax=1)
        
        # 添加关键点位置
        ax.scatter(keypoints[i, 0], keypoints[i, 1], keypoints[i, 2], 
                  c='white', s=100, marker='*', edgecolor='black', linewidth=2)
        
        ax.set_title(f'Synthetic Heatmap {i+1}')
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
        
        # 添加颜色条
        plt.colorbar(scatter, ax=ax, shrink=0.6)
    
    plt.tight_layout()
    plt.savefig('synthetic_heatmap_demo.png', dpi=300, bbox_inches='tight')
    print("   📊 合成热图演示已保存: synthetic_heatmap_demo.png")
    plt.close()

def visualize_real_heatmap_with_fixes():
    """使用修复方法可视化真实热图"""
    print("\n🔧 **使用修复方法可视化真实热图**")
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    # 选择样本
    sample_idx = 0
    sample_id = sample_ids[sample_idx]
    point_cloud = point_clouds[sample_idx]
    true_keypoints = keypoints[sample_idx]
    
    # 采样点云
    if len(point_cloud) > 8192:
        indices = np.random.choice(len(point_cloud), 8192, replace=False)
        pc_sampled = point_cloud[indices]
    else:
        pc_sampled = point_cloud
    
    # 加载模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model.eval()
        print(f"   ✅ 模型加载成功")
    except Exception as e:
        print(f"   ❌ 模型加载失败: {e}")
        return
    
    # 预测热图
    pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
    
    with torch.no_grad():
        pred_heatmaps = model(pc_tensor)
    
    pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T  # [N, 12]
    
    # 提取关键点
    pred_keypoints, confidences = extract_keypoints_from_heatmaps(
        pred_heatmaps_np.T, pc_sampled
    )
    
    # 创建修复的可视化
    fig = plt.figure(figsize=(20, 16))
    cmap = create_enhanced_colormap()
    
    # 显示前9个关键点
    for i in range(9):
        ax = fig.add_subplot(3, 3, i+1, projection='3d')
        
        heatmap = pred_heatmaps_np[:, i]
        
        # 尝试不同的增强方法
        if i < 3:
            heatmap_enhanced = enhance_heatmap_contrast(heatmap, method='power')
            method_name = 'Power Enhancement'
        elif i < 6:
            heatmap_enhanced = enhance_heatmap_contrast(heatmap, method='percentile')
            method_name = 'Percentile Stretch'
        else:
            heatmap_enhanced = enhance_heatmap_contrast(heatmap, method='sigmoid')
            method_name = 'Sigmoid Enhancement'
        
        # 创建散点图
        scatter = ax.scatter(pc_sampled[:, 0], pc_sampled[:, 1], pc_sampled[:, 2], 
                           c=heatmap_enhanced, cmap=cmap, s=0.5, alpha=0.8, vmin=0, vmax=1)
        
        # 添加真实关键点
        ax.scatter(true_keypoints[i, 0], true_keypoints[i, 1], true_keypoints[i, 2], 
                  c='white', s=80, marker='*', edgecolor='black', linewidth=2, label='True')
        
        # 添加预测关键点
        ax.scatter(pred_keypoints[i, 0], pred_keypoints[i, 1], pred_keypoints[i, 2], 
                  c='yellow', s=60, marker='o', edgecolor='black', linewidth=2, label='Predicted')
        
        # 计算误差
        error = np.linalg.norm(pred_keypoints[i] - true_keypoints[i])
        
        ax.set_title(f'KP{i} - {method_name}\nConf: {confidences[i]:.3f}, Error: {error:.1f}mm', 
                    fontsize=10)
        ax.set_xlabel('X', fontsize=8)
        ax.set_ylabel('Y', fontsize=8)
        ax.set_zlabel('Z', fontsize=8)
        ax.tick_params(labelsize=6)
        
        if i == 0:  # 只在第一个子图显示图例
            ax.legend(fontsize=8)
        
        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=ax, shrink=0.6, pad=0.1)
        cbar.ax.tick_params(labelsize=6)
    
    plt.suptitle(f'Fixed Heatmap Visualization - Sample {sample_id}', fontsize=16)
    plt.tight_layout()
    plt.savefig(f'fixed_heatmap_visualization_{sample_id}.png', dpi=300, bbox_inches='tight')
    print(f"   📊 修复的热图可视化已保存: fixed_heatmap_visualization_{sample_id}.png")
    plt.close()

def create_heatmap_analysis_report():
    """创建热图分析报告"""
    print("\n📊 **创建热图分析报告**")
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model.eval()
    except Exception as e:
        print(f"   ❌ 模型加载失败: {e}")
        return
    
    # 分析多个样本
    sample_indices = [0, 5, 10]
    all_errors = []
    all_confidences = []
    
    fig, axes = plt.subplots(len(sample_indices), 4, figsize=(20, 15))
    
    for idx, sample_idx in enumerate(sample_indices):
        sample_id = sample_ids[sample_idx]
        point_cloud = point_clouds[sample_idx]
        true_keypoints = keypoints[sample_idx]
        
        # 采样和预测
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            pc_sampled = point_cloud[indices]
        else:
            pc_sampled = point_cloud
        
        pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
        
        with torch.no_grad():
            pred_heatmaps = model(pc_tensor)
        
        pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T
        pred_keypoints, confidences = extract_keypoints_from_heatmaps(
            pred_heatmaps_np.T, pc_sampled
        )
        
        # 计算误差
        errors = []
        for i in range(12):
            error = np.linalg.norm(pred_keypoints[i] - true_keypoints[i])
            errors.append(error)
        
        all_errors.extend(errors)
        all_confidences.extend(confidences)
        
        # 可视化最好和最差的关键点
        best_kp_idx = np.argmin(errors)
        worst_kp_idx = np.argmax(errors)
        
        # 最好的关键点
        ax1 = axes[idx, 0] if len(sample_indices) > 1 else axes[0]
        ax1.remove()
        ax1 = fig.add_subplot(len(sample_indices), 4, idx*4+1, projection='3d')
        
        heatmap_best = enhance_heatmap_contrast(pred_heatmaps_np[:, best_kp_idx], method='power')
        scatter1 = ax1.scatter(pc_sampled[:, 0], pc_sampled[:, 1], pc_sampled[:, 2], 
                              c=heatmap_best, cmap='hot', s=1, alpha=0.8)
        ax1.scatter(true_keypoints[best_kp_idx, 0], true_keypoints[best_kp_idx, 1], 
                   true_keypoints[best_kp_idx, 2], c='white', s=50, marker='*', 
                   edgecolor='black', linewidth=1)
        ax1.set_title(f'{sample_id} - Best KP{best_kp_idx}\nError: {errors[best_kp_idx]:.1f}mm')
        
        # 最差的关键点
        ax2 = axes[idx, 1] if len(sample_indices) > 1 else axes[1]
        ax2.remove()
        ax2 = fig.add_subplot(len(sample_indices), 4, idx*4+2, projection='3d')
        
        heatmap_worst = enhance_heatmap_contrast(pred_heatmaps_np[:, worst_kp_idx], method='power')
        scatter2 = ax2.scatter(pc_sampled[:, 0], pc_sampled[:, 1], pc_sampled[:, 2], 
                              c=heatmap_worst, cmap='hot', s=1, alpha=0.8)
        ax2.scatter(true_keypoints[worst_kp_idx, 0], true_keypoints[worst_kp_idx, 1], 
                   true_keypoints[worst_kp_idx, 2], c='white', s=50, marker='*', 
                   edgecolor='black', linewidth=1)
        ax2.set_title(f'{sample_id} - Worst KP{worst_kp_idx}\nError: {errors[worst_kp_idx]:.1f}mm')
        
        # 误差分布
        ax3 = axes[idx, 2] if len(sample_indices) > 1 else axes[2]
        ax3.bar(range(12), errors, color='skyblue', alpha=0.7)
        ax3.set_xlabel('Keypoint Index')
        ax3.set_ylabel('Error (mm)')
        ax3.set_title(f'{sample_id} - Error Distribution')
        ax3.grid(True, alpha=0.3)
        
        # 置信度分布
        ax4 = axes[idx, 3] if len(sample_indices) > 1 else axes[3]
        ax4.bar(range(12), confidences, color='orange', alpha=0.7)
        ax4.set_xlabel('Keypoint Index')
        ax4.set_ylabel('Confidence')
        ax4.set_title(f'{sample_id} - Confidence Distribution')
        ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('heatmap_analysis_report.png', dpi=300, bbox_inches='tight')
    print(f"   📊 热图分析报告已保存: heatmap_analysis_report.png")
    plt.close()
    
    # 打印总体统计
    print(f"\n📈 **总体统计**:")
    print(f"   平均误差: {np.mean(all_errors):.2f}±{np.std(all_errors):.2f}mm")
    print(f"   平均置信度: {np.mean(all_confidences):.3f}±{np.std(all_confidences):.3f}")
    print(f"   最佳误差: {np.min(all_errors):.2f}mm")
    print(f"   最差误差: {np.max(all_errors):.2f}mm")

def main():
    """主函数"""
    print("🎨 **修复的热图可视化系统**")
    print("解决热图颜色单一问题，提供清晰的概率分布可视化")
    print("=" * 80)
    
    # 1. 创建合成热图演示
    create_synthetic_heatmap_demo()
    
    # 2. 使用修复方法可视化真实热图
    visualize_real_heatmap_with_fixes()
    
    # 3. 创建分析报告
    create_heatmap_analysis_report()
    
    print(f"\n🎉 **修复的热图可视化完成!**")
    print(f"✅ 合成热图演示 - 验证可视化方法")
    print(f"✅ 多种对比度增强方法")
    print(f"✅ 增强的颜色映射")
    print(f"✅ 详细的分析报告")
    print(f"✅ 清晰的概率分布可视化")

if __name__ == "__main__":
    main()
