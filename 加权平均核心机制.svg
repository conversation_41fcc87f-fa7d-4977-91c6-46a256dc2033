<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="1280" height="720" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="url(#headerGrad)"/>
  <text x="640" y="50" text-anchor="middle" fill="white"
        font-family="SimHei, Arial, sans-serif" font-size="36" font-weight="bold">
    加权平均的核心作用：从离散到连续的精确定位
  </text>

  <!-- Main content -->
  <rect x="50" y="100" width="1180" height="580" rx="15" fill="white" stroke="#374151" stroke-width="2"/>

  <!-- Core concept -->
  <rect x="80" y="130" width="1120" height="120" rx="10" fill="#f0fdf4" stroke="#22c55e" stroke-width="3"/>
  <text x="640" y="160" text-anchor="middle" fill="#15803d"
        font-family="SimHei, Arial, sans-serif" font-size="28" font-weight="bold">
    核心突破：亚像素级精确定位
  </text>
  <rect x="300" y="180" width="680" height="35" rx="5" fill="#dcfce7" stroke="#22c55e" stroke-width="2"/>
  <text x="640" y="202" text-anchor="middle" fill="#15803d"
        font-family="Arial, sans-serif" font-size="18" font-weight="bold">
    kp_i = Σ(weight_j × coord_j) / Σ(weight_j)
  </text>
  <text x="640" y="235" text-anchor="middle" fill="#374151"
        font-family="SimHei, Arial, sans-serif" font-size="16">
    结果可以是原始点之间的任意位置 → 突破离散点限制
  </text>

  <!-- Two main sections -->
  <!-- Left: Core problem and solution -->
  <rect x="80" y="270" width="560" height="380" rx="10" fill="#fef7ff" stroke="#8b5cf6" stroke-width="2"/>
  <text x="360" y="300" text-anchor="middle" fill="#7c3aed"
        font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    🎯 核心问题与解决方案
  </text>

  <!-- Problem -->
  <rect x="100" y="320" width="520" height="120" rx="8" fill="#fef2f2" stroke="#f87171" stroke-width="1"/>
  <text x="360" y="345" text-anchor="middle" fill="#dc2626"
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    问题：离散点云的精度限制
  </text>
  <text x="110" y="370" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14">
    • 真实关键点位置：连续空间中的任意坐标
  </text>
  <text x="110" y="395" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14">
    • 点云采样：离散的有限个点
  </text>
  <text x="110" y="420" fill="#dc2626" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    • 矛盾：如何从离散点精确定位连续位置？
  </text>

  <!-- Solution -->
  <rect x="100" y="450" width="520" height="120" rx="8" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="360" y="475" text-anchor="middle" fill="#15803d"
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    解决方案：加权平均实现智能插值
  </text>
  <text x="110" y="500" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14">
    • 高权重点：更接近真实关键点
  </text>
  <text x="110" y="525" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14">
    • 加权组合：在最优点之间精确插值
  </text>
  <text x="110" y="550" fill="#15803d" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    • 结果：亚像素级精确定位
  </text>

  <!-- Key insight -->
  <rect x="100" y="580" width="520" height="60" rx="5" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="360" y="605" text-anchor="middle" fill="#7c3aed"
        font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    核心洞察：加权平均 = 智能插值算法
  </text>
  <text x="110" y="625" fill="#7c3aed" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    在连续空间中找到最优解，突破离散采样限制
  </text>

  <!-- Right: Concrete example -->
  <rect x="660" y="270" width="540" height="380" rx="10" fill="#fff7ed" stroke="#f59e0b" stroke-width="2"/>
  <text x="930" y="300" text-anchor="middle" fill="#d97706"
        font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    📊 具体计算示例
  </text>

  <!-- Input data -->
  <rect x="680" y="320" width="500" height="100" rx="8" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
  <text x="930" y="345" text-anchor="middle" fill="#d97706"
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    输入：区域内3个点
  </text>
  <text x="690" y="370" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    点1: 坐标(1.0, 2.0, 3.0)  权重: 0.2
  </text>
  <text x="690" y="390" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    点2: 坐标(2.0, 3.0, 4.0)  权重: 0.3
  </text>
  <text x="690" y="410" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    点3: 坐标(3.0, 4.0, 5.0)  权重: 0.5
  </text>

  <!-- Calculation process -->
  <rect x="680" y="430" width="500" height="120" rx="8" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="1"/>
  <text x="930" y="455" text-anchor="middle" fill="#7c3aed"
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    计算过程
  </text>
  <text x="690" y="480" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    分子 = 0.2×(1,2,3) + 0.3×(2,3,4) + 0.5×(3,4,5)
  </text>
  <text x="690" y="500" fill="#374151" font-family="Arial, sans-serif" font-size="13">
         = (0.2,0.4,0.6) + (0.6,0.9,1.2) + (1.5,2.0,2.5)
  </text>
  <text x="690" y="520" fill="#374151" font-family="Arial, sans-serif" font-size="13">
         = (2.3, 3.3, 4.3)
  </text>
  <text x="690" y="540" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    分母 = 0.2 + 0.3 + 0.5 = 1.0
  </text>

  <!-- Result -->
  <rect x="680" y="560" width="500" height="80" rx="8" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="930" y="585" text-anchor="middle" fill="#15803d"
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    最终结果
  </text>
  <text x="690" y="610" fill="#15803d" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    关键点坐标 = (2.3, 3.3, 4.3)
  </text>
  <text x="690" y="630" fill="#15803d" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    ✨ 这个坐标不在任何原始点上！实现亚像素定位
  </text>
</svg>
