#!/usr/bin/env python3
"""
Create High Quality Dataset for Medical Keypoint Detection

Use complete STL reading for maximum alignment quality, even if slower.
Priority: Quality over Speed for medical applications.
"""

import numpy as np
import pandas as pd
from pathlib import Path
import json
import struct
import gc
import os

def load_annotation_file_robust(csv_path: str):
    """Robustly load annotation CSV file handling various format issues"""
    
    # Try different encodings
    encodings = ['gbk', 'utf-8', 'latin-1', 'cp1252']
    
    first_line = None
    working_encoding = None
    
    # Find working encoding
    for encoding in encodings:
        try:
            with open(csv_path, 'r', encoding=encoding) as f:
                first_line = f.readline().strip()
                working_encoding = encoding
                break
        except UnicodeDecodeError:
            continue
    
    if first_line is None:
        print(f"   ❌ 无法读取文件，所有编码都失败")
        return None, None
    
    has_header = 'label' in first_line.lower() and ('x' in first_line.lower() or 'X' in first_line)
    
    try:
        if has_header:
            # Standard format with header
            df = pd.read_csv(csv_path, encoding=working_encoding)
        else:
            # No header format - need to add column names
            df = pd.read_csv(csv_path, encoding=working_encoding, header=None)
            
            # Determine column structure based on number of columns
            if df.shape[1] >= 9:
                # Full format: label,X,Y,Z,defined,selected,visible,locked,description
                df.columns = ['label', 'X', 'Y', 'Z', 'defined', 'selected', 'visible', 'locked', 'description']
            elif df.shape[1] >= 4:
                # Minimal format: label,X,Y,Z
                df.columns = ['label', 'X', 'Y', 'Z'] + [f'col_{i}' for i in range(4, df.shape[1])]
            else:
                print(f"   ❌ 列数不足: {df.shape[1]}")
                return None, None
    
    except Exception as e:
        print(f"   ❌ 文件读取失败: {e}")
        return None, None
    
    # Validate required columns
    if 'X' not in df.columns or 'Y' not in df.columns or 'Z' not in df.columns:
        print(f"   ❌ 缺少坐标列")
        return None, None
    
    if 'label' not in df.columns:
        print(f"   ❌ 缺少标签列")
        return None, None
    
    # Extract data
    keypoints = df[['X', 'Y', 'Z']].values
    labels = df['label'].values.tolist()
    
    # Validate data
    if len(keypoints) == 0:
        print(f"   ❌ 没有关键点数据")
        return None, None
    
    # Check for valid coordinates
    if np.any(np.isnan(keypoints)) or np.any(np.isinf(keypoints)):
        print(f"   ❌ 坐标数据包含无效值")
        return None, None
    
    return keypoints, labels

def read_stl_binary_complete_high_quality(stl_path: str):
    """Read complete STL file with highest quality - same as the good version"""
    try:
        with open(stl_path, 'rb') as f:
            f.read(80)  # Skip header
            num_triangles = struct.unpack('<I', f.read(4))[0]
            
            vertices = []
            for i in range(num_triangles):
                f.read(12)  # Skip normal
                for j in range(3):
                    x, y, z = struct.unpack('<fff', f.read(12))
                    vertices.append([x, y, z])
                f.read(2)  # Skip attribute
            
            vertices = np.array(vertices)
            
            # Remove duplicates to reduce memory but keep all unique vertices
            unique_vertices = np.unique(vertices, axis=0)
            del vertices
            gc.collect()
            
            return unique_vertices
            
    except Exception as e:
        print(f"   ❌ STL读取失败: {e}")
        return None

def separate_keypoints_by_region(keypoints, labels):
    """Separate keypoints by F1/F2/F3 regions based on labels"""
    
    f1_keypoints = []
    f2_keypoints = []
    f3_keypoints = []
    
    for i, label in enumerate(labels):
        if isinstance(label, str):
            label_upper = label.upper()
            if 'F_1' in label_upper or 'F1' in label_upper:
                f1_keypoints.append(keypoints[i])
            elif 'F_2' in label_upper or 'F2' in label_upper:
                f2_keypoints.append(keypoints[i])
            elif 'F_3' in label_upper or 'F3' in label_upper:
                f3_keypoints.append(keypoints[i])
    
    return {
        'F1': np.array(f1_keypoints) if f1_keypoints else np.array([]).reshape(0, 3),
        'F2': np.array(f2_keypoints) if f2_keypoints else np.array([]).reshape(0, 3),
        'F3': np.array(f3_keypoints) if f3_keypoints else np.array([]).reshape(0, 3)
    }

def process_single_sample_high_quality(sample_id, target_region='F3'):
    """Process single sample with highest quality - complete STL reading"""
    
    print(f"🔧 处理样本 {sample_id} ({target_region}) - 高质量模式")
    
    data_dir = "/home/<USER>/pjc/GCN/Data"
    data_path = Path(data_dir)
    annotations_dir = data_path / "annotations"
    stl_dir = data_path / "stl_models"
    
    # Load annotation with robust handling
    csv_file = annotations_dir / f"{sample_id}-Table-XYZ.CSV"
    
    if not csv_file.exists():
        print(f"   ❌ 标注文件不存在")
        return None
    
    keypoints, labels = load_annotation_file_robust(str(csv_file))
    
    if keypoints is None:
        return None
    
    print(f"   ✅ 加载了 {len(keypoints)} 个关键点")
    
    # Separate keypoints
    regions = separate_keypoints_by_region(keypoints, labels)
    
    # Check target region
    if len(regions[target_region]) == 0:
        print(f"   ❌ {target_region}关键点为空")
        return None
    
    # Load target region STL with complete reading
    stl_file = stl_dir / f"{sample_id}-F_{target_region[-1]}.stl"
    
    if not stl_file.exists():
        print(f"   ❌ {target_region} STL文件不存在")
        return None
    
    print(f"   📖 读取完整{target_region} STL文件...")
    vertices = read_stl_binary_complete_high_quality(str(stl_file))
    
    if vertices is None:
        return None
    
    print(f"   ✅ {target_region} STL: {len(vertices)} 个唯一顶点")
    
    # Calculate alignment quality with complete STL
    region_keypoints = regions[target_region]
    distances = []
    
    print(f"   🎯 计算高精度对齐质量...")
    for i, kp in enumerate(region_keypoints):
        dists = np.linalg.norm(vertices - kp, axis=1)
        min_dist = np.min(dists)
        distances.append(min_dist)
        
        if (i + 1) % 5 == 0:  # Progress indicator
            print(f"      进度: {i+1}/{len(region_keypoints)}")
    
    distances = np.array(distances)
    mean_dist = np.mean(distances)
    within_1mm = np.sum(distances <= 1.0) / len(distances) * 100
    within_5mm = np.sum(distances <= 5.0) / len(distances) * 100
    
    print(f"   📊 {target_region}高质量对齐:")
    print(f"      平均距离: {mean_dist:.3f}mm")
    print(f"      ≤1mm: {within_1mm:.1f}%")
    print(f"      ≤5mm: {within_5mm:.1f}%")
    
    # Sample point cloud for training (but from complete STL) - Use 50K points for medical grade
    target_points = 50000
    if len(vertices) > target_points:
        indices = np.random.choice(len(vertices), target_points, replace=False)
        sampled_vertices = vertices[indices].copy()
    else:
        # Duplicate if not enough points
        indices = np.random.choice(len(vertices), target_points, replace=True)
        sampled_vertices = vertices[indices].copy()
    
    # Simple centering (preserve scale for medical accuracy)
    kp_center = np.mean(region_keypoints, axis=0)
    centered_pc = sampled_vertices - kp_center
    centered_kps = region_keypoints - kp_center
    
    result = {
        'sample_id': sample_id,
        'point_cloud': centered_pc.astype(np.float32),
        'keypoints': centered_kps.astype(np.float32),
        'center': kp_center.astype(np.float32),
        'alignment_quality': {
            'mean_distance': float(mean_dist),
            'within_1mm_percent': float(within_1mm),
            'within_5mm_percent': float(within_5mm)
        },
        'stl_vertices_count': len(vertices)
    }
    
    # Cleanup
    del vertices, sampled_vertices, distances
    del centered_pc, centered_kps
    gc.collect()
    
    print(f"   ✅ 高质量处理成功")
    return result

def create_high_quality_f3_dataset():
    """Create high quality F3 dataset with complete STL reading"""

    print("🏗️ **创建高质量F3数据集 (完整STL模式)**")
    print("🎯 **优先级: 质量 > 速度，适用于医疗应用**")
    print("=" * 80)

    # Get available samples
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    annotations_dir = data_dir / "annotations"

    xyz_files = list(annotations_dir.glob("*-Table-XYZ.CSV"))
    excluded_samples = {'600025', '600026', '600027'}  # Known LPS format

    valid_sample_ids = []
    for csv_file in xyz_files:
        sample_id = csv_file.stem.split('-')[0]
        if sample_id not in excluded_samples:
            valid_sample_ids.append(sample_id)

    print(f"📂 找到 {len(valid_sample_ids)} 个候选样本")
    print(f"⏱️  预计处理时间: ~{len(valid_sample_ids) * 2} 分钟 (高质量模式)")

    # Process samples one by one (slower but higher quality)
    processed_samples = []
    failed_samples = []

    for i, sample_id in enumerate(valid_sample_ids):
        print(f"\n📦 处理样本 {i+1}/{len(valid_sample_ids)}: {sample_id}")
        print(f"⏳ 预计剩余时间: ~{(len(valid_sample_ids) - i - 1) * 2} 分钟")

        result = process_single_sample_high_quality(sample_id, 'F3')

        if result is not None:
            processed_samples.append(result)
            print(f"   🎉 成功! 累计: {len(processed_samples)} 个样本")
        else:
            failed_samples.append(sample_id)
            print(f"   💔 失败! 累计失败: {len(failed_samples)} 个样本")

        # Force cleanup after each sample
        gc.collect()

        # Continue processing all available samples for maximum dataset size
        # No early stopping - process all 97 samples for complete dataset

    print(f"\n📊 **高质量处理结果**")
    print(f"   成功样本: {len(processed_samples)}")
    print(f"   失败样本: {len(failed_samples)}")

    if failed_samples:
        print(f"   失败列表: {failed_samples}")

    return processed_samples, failed_samples

def save_high_quality_dataset(samples, failed_samples):
    """Save high quality dataset with detailed quality statistics"""

    if len(samples) < 5:
        print(f"❌ 高质量样本数量不足 ({len(samples)})，无法创建数据集")
        return None

    print(f"\n💎 **保存高质量F3数据集**")

    # Calculate detailed quality statistics
    qualities = [s['alignment_quality'] for s in samples]
    distances = [q['mean_distance'] for q in qualities]
    within_1mm = [q['within_1mm_percent'] for q in qualities]
    within_5mm = [q['within_5mm_percent'] for q in qualities]
    stl_counts = [s['stl_vertices_count'] for s in samples]

    avg_dist = np.mean(distances)
    std_dist = np.std(distances)
    min_dist = np.min(distances)
    max_dist = np.max(distances)

    avg_1mm = np.mean(within_1mm)
    avg_5mm = np.mean(within_5mm)

    avg_stl_vertices = np.mean(stl_counts)

    print(f"   📈 高质量统计:")
    print(f"      平均对齐距离: {avg_dist:.3f}±{std_dist:.3f}mm")
    print(f"      距离范围: {min_dist:.3f}-{max_dist:.3f}mm")
    print(f"      平均1mm精度: {avg_1mm:.1f}%")
    print(f"      平均5mm精度: {avg_5mm:.1f}%")
    print(f"      平均STL顶点数: {avg_stl_vertices:.0f}")

    # Quality assessment
    excellent_samples = sum(1 for d in distances if d <= 1.0)
    good_samples = sum(1 for d in distances if d <= 5.0)

    print(f"   🏆 质量评估:")
    print(f"      优秀样本 (≤1mm): {excellent_samples}/{len(samples)} ({excellent_samples/len(samples)*100:.1f}%)")
    print(f"      良好样本 (≤5mm): {good_samples}/{len(samples)} ({good_samples/len(samples)*100:.1f}%)")

    # Create comprehensive metadata
    dataset_info = {
        'dataset_name': 'High_Quality_F3_Medical_Dataset',
        'creation_date': str(pd.Timestamp.now()),
        'processing_mode': 'Complete_STL_High_Quality',
        'total_samples': len(samples),
        'failed_samples': len(failed_samples),
        'quality_stats': {
            'avg_distance_mm': float(avg_dist),
            'std_distance_mm': float(std_dist),
            'min_distance_mm': float(min_dist),
            'max_distance_mm': float(max_dist),
            'avg_1mm_percent': float(avg_1mm),
            'avg_5mm_percent': float(avg_5mm),
            'avg_stl_vertices': float(avg_stl_vertices)
        },
        'quality_assessment': {
            'excellent_samples_1mm': excellent_samples,
            'good_samples_5mm': good_samples,
            'excellent_percentage': float(excellent_samples/len(samples)*100),
            'good_percentage': float(good_samples/len(samples)*100)
        },
        'sample_details': [
            {
                'sample_id': s['sample_id'],
                'alignment_quality': s['alignment_quality'],
                'stl_vertices_count': s['stl_vertices_count']
            }
            for s in samples
        ],
        'failed_sample_ids': failed_samples
    }

    # Save metadata
    with open('high_quality_f3_dataset_info.json', 'w') as f:
        json.dump(dataset_info, f, indent=2)

    # Save training data
    np.savez_compressed('high_quality_f3_dataset.npz',
                       sample_ids=[s['sample_id'] for s in samples],
                       point_clouds=np.stack([s['point_cloud'] for s in samples]),
                       keypoints=np.stack([s['keypoints'] for s in samples]),
                       centers=np.stack([s['center'] for s in samples]))

    print(f"   ✅ 高质量数据已保存:")
    print(f"      📄 high_quality_f3_dataset_info.json")
    print(f"      📦 high_quality_f3_dataset.npz")
    print(f"      📊 {len(samples)} 个高质量样本，每个4096点云+19关键点")

    return 'high_quality_f3_dataset.npz'

if __name__ == "__main__":
    # Create high quality F3 dataset
    print("🚀 开始创建高质量医疗关键点数据集...")

    samples, failed = create_high_quality_f3_dataset()

    if samples:
        # Save dataset
        dataset_path = save_high_quality_dataset(samples, failed)

        if dataset_path:
            print(f"\n🎉 **高质量F3数据集创建成功!**")
            print(f"📁 文件: {dataset_path}")
            print(f"📊 样本数: {len(samples)}")
            print(f"💎 质量: 医疗级高精度对齐")
            print(f"🎯 用途: 高质量F3关键点检测模型训练")
            print(f"💡 下一步: 使用此高质量数据集训练模型")
        else:
            print(f"\n❌ **数据集保存失败**")
    else:
        print(f"\n❌ **没有成功处理的高质量样本**")
