#!/usr/bin/env python3
"""
根因分析
Root Cause Analysis
系统性分析无法复现5mm性能的根本原因
"""

import torch
import numpy as np
import os
import json
from pathlib import Path

def analyze_historical_model_details():
    """详细分析历史模型"""
    
    print("🔍 历史模型详细分析")
    print("=" * 60)
    
    historical_model_path = "archive/old_models/best_exact_ensemble_seed123_5.371mm.pth"
    
    if not os.path.exists(historical_model_path):
        print(f"❌ 历史模型文件不存在")
        return None
    
    try:
        checkpoint = torch.load(historical_model_path, map_location='cpu')
        
        print(f"📋 历史模型详细信息:")
        
        if isinstance(checkpoint, dict):
            for key, value in checkpoint.items():
                if key == 'model_state_dict':
                    state_dict = value
                    print(f"\n🏗️ 模型架构分析:")
                    
                    # 分析每一层的详细信息
                    conv_layers = []
                    fc_layers = []
                    bn_layers = []
                    other_layers = []
                    
                    for param_name, param_tensor in state_dict.items():
                        if 'conv' in param_name:
                            conv_layers.append((param_name, param_tensor.shape))
                        elif 'fc' in param_name:
                            fc_layers.append((param_name, param_tensor.shape))
                        elif 'bn' in param_name:
                            bn_layers.append((param_name, param_tensor.shape))
                        else:
                            other_layers.append((param_name, param_tensor.shape))
                    
                    print(f"   卷积层 ({len(conv_layers)}):")
                    for name, shape in conv_layers:
                        print(f"     {name}: {shape}")
                    
                    print(f"   全连接层 ({len(fc_layers)}):")
                    for name, shape in fc_layers:
                        print(f"     {name}: {shape}")
                    
                    print(f"   批归一化层 ({len(bn_layers)}):")
                    for name, shape in bn_layers[:5]:  # 只显示前5个
                        print(f"     {name}: {shape}")
                    if len(bn_layers) > 5:
                        print(f"     ... 还有{len(bn_layers)-5}个")
                    
                    print(f"   其他层 ({len(other_layers)}):")
                    for name, shape in other_layers:
                        print(f"     {name}: {shape}")
                    
                    # 分析输出维度
                    last_fc = None
                    for name, shape in fc_layers:
                        if 'weight' in name:
                            last_fc = (name, shape)
                    
                    if last_fc:
                        print(f"\n🎯 输出分析:")
                        print(f"   最后FC层: {last_fc[0]} -> {last_fc[1]}")
                        output_dim = last_fc[1][0]
                        print(f"   输出维度: {output_dim}")
                        
                        if output_dim == 36:  # 12 * 3
                            print(f"   ✅ 确认12点模型 (12 * 3 = 36)")
                        elif output_dim == 171:  # 57 * 3
                            print(f"   ⚠️ 这是57点模型？")
                        else:
                            print(f"   ❓ 未知输出格式")
                
                elif key == 'val_metrics':
                    print(f"\n📊 历史验证指标:")
                    for metric_name, metric_value in value.items():
                        print(f"   {metric_name}: {metric_value}")
                
                elif key in ['epoch', 'best_val_error']:
                    print(f"   {key}: {value}")
        
        return checkpoint
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def analyze_historical_dataset():
    """分析历史数据集"""
    
    print(f"\n🔍 历史数据集分析")
    print("=" * 60)
    
    # 寻找可能的历史数据集
    possible_datasets = [
        "f3_reduced_12kp_stable.npz",
        "archive/old_experiments/f3_reduced_12kp_stable.npz",
        "Archive_F3_Experiments/01_Dataset_Creation/f3_reduced_12kp_stable.npz"
    ]
    
    historical_dataset = None
    
    for dataset_path in possible_datasets:
        if os.path.exists(dataset_path):
            print(f"✅ 找到历史数据集: {dataset_path}")
            
            try:
                data = np.load(dataset_path, allow_pickle=True)
                historical_dataset = data
                
                print(f"📊 历史数据集内容:")
                for key in data.keys():
                    array = data[key]
                    if hasattr(array, 'shape'):
                        print(f"   {key}: {array.shape} {array.dtype}")
                    else:
                        print(f"   {key}: {type(array)}")
                
                # 分析关键点数据
                if 'keypoints' in data:
                    keypoints = data['keypoints']
                    print(f"\n🎯 关键点分析:")
                    print(f"   形状: {keypoints.shape}")
                    print(f"   数据范围: [{np.min(keypoints):.2f}, {np.max(keypoints):.2f}]")
                    print(f"   平均值: {np.mean(keypoints, axis=(0,1))}")
                    print(f"   标准差: {np.std(keypoints, axis=(0,1))}")
                
                # 分析点云数据
                if 'point_clouds' in data:
                    point_clouds = data['point_clouds']
                    print(f"\n☁️ 点云分析:")
                    print(f"   形状: {point_clouds.shape}")
                    print(f"   数据范围: [{np.min(point_clouds):.2f}, {np.max(point_clouds):.2f}]")
                    print(f"   平均值: {np.mean(point_clouds, axis=(0,1))}")
                    print(f"   标准差: {np.std(point_clouds, axis=(0,1))}")
                
                break
                
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
        else:
            print(f"❌ 未找到: {dataset_path}")
    
    return historical_dataset

def analyze_current_dataset():
    """分析当前数据集"""
    
    print(f"\n🔍 当前数据集分析")
    print("=" * 60)
    
    current_dataset_path = "high_quality_pelvis_57_dataset.npz"
    
    if not os.path.exists(current_dataset_path):
        print(f"❌ 当前数据集不存在: {current_dataset_path}")
        return None
    
    try:
        data = np.load(current_dataset_path, allow_pickle=True)
        
        print(f"📊 当前数据集内容:")
        for key in data.keys():
            array = data[key]
            if hasattr(array, 'shape'):
                print(f"   {key}: {array.shape} {array.dtype}")
            else:
                print(f"   {key}: {type(array)}")
        
        # 分析57点关键点
        if 'keypoints_57' in data:
            keypoints_57 = data['keypoints_57']
            print(f"\n🎯 57点关键点分析:")
            print(f"   形状: {keypoints_57.shape}")
            print(f"   数据范围: [{np.min(keypoints_57):.2f}, {np.max(keypoints_57):.2f}]")
            print(f"   平均值: {np.mean(keypoints_57, axis=(0,1))}")
            print(f"   标准差: {np.std(keypoints_57, axis=(0,1))}")
            
            # 提取12点子集进行对比
            historical_12_indices = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17]
            keypoints_12_subset = keypoints_57[:, historical_12_indices, :]
            
            print(f"\n🎯 12点子集分析:")
            print(f"   形状: {keypoints_12_subset.shape}")
            print(f"   数据范围: [{np.min(keypoints_12_subset):.2f}, {np.max(keypoints_12_subset):.2f}]")
            print(f"   平均值: {np.mean(keypoints_12_subset, axis=(0,1))}")
            print(f"   标准差: {np.std(keypoints_12_subset, axis=(0,1))}")
        
        # 分析点云数据
        if 'point_clouds' in data:
            point_clouds = data['point_clouds']
            print(f"\n☁️ 点云分析:")
            print(f"   形状: {point_clouds.shape}")
            print(f"   数据范围: [{np.min(point_clouds):.2f}, {np.max(point_clouds):.2f}]")
            print(f"   平均值: {np.mean(point_clouds, axis=(0,1))}")
            print(f"   标准差: {np.std(point_clouds, axis=(0,1))}")
        
        return data
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def compare_datasets(historical_data, current_data):
    """对比数据集差异"""
    
    print(f"\n📊 数据集对比分析")
    print("=" * 60)
    
    if historical_data is None or current_data is None:
        print(f"❌ 无法对比，缺少数据集")
        return
    
    print(f"🔍 关键差异分析:")
    
    # 样本数量对比
    if 'keypoints' in historical_data and 'keypoints_57' in current_data:
        hist_samples = historical_data['keypoints'].shape[0]
        curr_samples = current_data['keypoints_57'].shape[0]
        
        print(f"   样本数量:")
        print(f"     历史数据集: {hist_samples}")
        print(f"     当前数据集: {curr_samples}")
        print(f"     差异: {curr_samples - hist_samples} ({(curr_samples/hist_samples-1)*100:+.1f}%)")
    
    # 数据范围对比
    if 'keypoints' in historical_data and 'keypoints_57' in current_data:
        hist_kp = historical_data['keypoints']
        curr_kp_12 = current_data['keypoints_57'][:, [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17], :]
        
        print(f"\n   关键点数据范围:")
        print(f"     历史数据集: [{np.min(hist_kp):.2f}, {np.max(hist_kp):.2f}]")
        print(f"     当前12点子集: [{np.min(curr_kp_12):.2f}, {np.max(curr_kp_12):.2f}]")
        
        hist_range = np.max(hist_kp) - np.min(hist_kp)
        curr_range = np.max(curr_kp_12) - np.min(curr_kp_12)
        print(f"     范围差异: {curr_range/hist_range:.2f}x")
    
    # 数据分布对比
    if 'keypoints' in historical_data and 'keypoints_57' in current_data:
        hist_mean = np.mean(historical_data['keypoints'])
        hist_std = np.std(historical_data['keypoints'])
        
        curr_mean = np.mean(curr_kp_12)
        curr_std = np.std(curr_kp_12)
        
        print(f"\n   数据分布:")
        print(f"     历史数据集: 均值={hist_mean:.2f}, 标准差={hist_std:.2f}")
        print(f"     当前12点子集: 均值={curr_mean:.2f}, 标准差={curr_std:.2f}")
        print(f"     均值差异: {abs(curr_mean - hist_mean):.2f}")
        print(f"     标准差比例: {curr_std/hist_std:.2f}x")

def analyze_architecture_differences():
    """分析架构差异"""
    
    print(f"\n🏗️ 架构差异分析")
    print("=" * 60)
    
    # 对比我们复现的架构和历史模型
    print(f"📋 架构对比:")
    print(f"   历史模型参数: 1,502,390")
    print(f"   我们复现参数: 1,495,911")
    print(f"   差异: {1502390 - 1495911} ({(1495911/1502390-1)*100:+.2f}%)")
    
    print(f"\n🔍 可能的架构差异:")
    differences = [
        "双Softmax机制的具体实现细节",
        "集成模块的权重融合方法",
        "残差连接的具体位置",
        "批归一化的应用方式",
        "Dropout的具体配置",
        "权重初始化方法",
        "激活函数的选择"
    ]
    
    for i, diff in enumerate(differences, 1):
        print(f"   {i}. {diff}")

def determine_root_cause():
    """确定根本原因"""
    
    print(f"\n🎯 根本原因判断")
    print("=" * 60)
    
    causes = [
        {
            "原因": "数据集完全不同",
            "可能性": "极高",
            "证据": [
                "历史使用f3_reduced_12kp_stable.npz",
                "我们使用high_quality_pelvis_57_dataset.npz的子集",
                "数据范围、分布可能完全不同",
                "样本数量可能不同"
            ],
            "验证方法": "直接加载历史数据集进行训练"
        },
        {
            "原因": "数据预处理方法不同",
            "可能性": "高",
            "证据": [
                "不同的归一化方法",
                "不同的坐标系处理",
                "不同的数据增强策略"
            ],
            "验证方法": "分析历史数据的预处理流程"
        },
        {
            "原因": "模型架构细节差异",
            "可能性": "中",
            "证据": [
                "参数数量略有差异",
                "双Softmax实现可能不同",
                "集成方法可能不同"
            ],
            "验证方法": "逐层对比模型结构"
        },
        {
            "原因": "训练策略不同",
            "可能性": "中",
            "证据": [
                "不同的超参数",
                "不同的学习率调度",
                "不同的损失函数"
            ],
            "验证方法": "复现历史训练配置"
        },
        {
            "原因": "评估方法不同",
            "可能性": "低",
            "证据": [
                "不同的误差计算方法",
                "不同的坐标系"
            ],
            "验证方法": "分析历史评估代码"
        }
    ]
    
    print(f"{'原因':<20} {'可能性':<8} {'主要证据'}")
    print("-" * 70)
    
    for cause in causes:
        main_evidence = cause['证据'][0] if cause['证据'] else "无"
        print(f"{cause['原因']:<20} {cause['可能性']:<8} {main_evidence}")
    
    print(f"\n💡 最可能的根本原因:")
    print(f"   🥇 数据集完全不同 (极高可能性)")
    print(f"   🥈 数据预处理方法不同 (高可能性)")
    print(f"   🥉 模型架构细节差异 (中等可能性)")

def main():
    """主函数"""
    
    print("🎯 根因分析")
    print("系统性分析无法复现5mm性能的根本原因")
    print("=" * 80)
    
    # 1. 分析历史模型
    historical_model = analyze_historical_model_details()
    
    # 2. 分析历史数据集
    historical_dataset = analyze_historical_dataset()
    
    # 3. 分析当前数据集
    current_dataset = analyze_current_dataset()
    
    # 4. 对比数据集
    compare_datasets(historical_dataset, current_dataset)
    
    # 5. 分析架构差异
    analyze_architecture_differences()
    
    # 6. 确定根本原因
    determine_root_cause()
    
    print(f"\n🎯 结论:")
    print(f"   根本原因很可能是数据集差异，而不是模型架构")
    print(f"   需要找到并使用历史的f3_reduced_12kp_stable.npz数据集")
    print(f"   才能真正验证模型架构的有效性")
    
    print(f"\n🚀 下一步建议:")
    print(f"   1. 寻找历史数据集f3_reduced_12kp_stable.npz")
    print(f"   2. 如果找到，直接用历史数据训练我们的模型")
    print(f"   3. 对比使用相同数据集的性能差异")
    print(f"   4. 这样可以分离数据集和架构的影响")

if __name__ == "__main__":
    main()
