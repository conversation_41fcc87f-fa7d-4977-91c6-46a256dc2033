#!/usr/bin/env python3
"""
多模态特征融合方案
结合几何特征、密度特征、曲率特征等
专门针对骶骨等复杂解剖结构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class GeometricFeatureExtractor(nn.<PERSON>dule):
    """几何特征提取器"""
    
    def __init__(self, k=16):
        super(GeometricFeatureExtractor, self).__init__()
        
        self.k = k
        
        # 距离特征
        self.distance_mlp = nn.Sequential(
            nn.Conv2d(1, 32, 1),
            nn.BatchNorm2d(32),
            nn.ReLU(),
            nn.Conv2d(32, 64, 1),
            nn.Batch<PERSON>orm2d(64),
            nn.ReLU()
        )
        
        # 角度特征
        self.angle_mlp = nn.Sequential(
            nn.Conv2d(1, 32, 1),
            nn.BatchNorm2d(32),
            nn.<PERSON><PERSON><PERSON>(),
            nn.Conv2d(32, 64, 1),
            nn.<PERSON><PERSON><PERSON>orm2d(64),
            nn.ReLU()
        )
        
    def compute_geometric_features(self, xyz):
        """计算几何特征"""
        B, N, _ = xyz.shape
        device = xyz.device

        # K近邻
        inner = -2 * torch.matmul(xyz, xyz.transpose(2, 1))
        xx = torch.sum(xyz**2, dim=2, keepdim=True)
        pairwise_distance = -xx - inner - xx.transpose(2, 1)
        idx = pairwise_distance.topk(k=self.k, dim=-1)[1]

        # 获取邻居点
        idx_base = torch.arange(0, B, device=device).view(-1, 1, 1) * N
        idx = idx + idx_base
        idx = idx.view(-1)

        neighbors = xyz.view(B*N, -1)[idx, :].view(B, N, self.k, 3)
        center = xyz.view(B, N, 1, 3).repeat(1, 1, self.k, 1)

        # 相对向量
        relative_pos = neighbors - center  # [B, N, k, 3]

        # 距离特征
        distances = torch.norm(relative_pos, dim=3, keepdim=True)  # [B, N, k, 1]

        # 角度特征 (与第一个邻居的夹角)
        if self.k > 1:
            ref_vec = relative_pos[:, :, 0:1, :]  # [B, N, 1, 3]
            ref_vec = ref_vec.repeat(1, 1, self.k, 1)

            # 计算夹角
            dot_product = torch.sum(relative_pos * ref_vec, dim=3, keepdim=True)
            norm_product = torch.norm(relative_pos, dim=3, keepdim=True) * torch.norm(ref_vec, dim=3, keepdim=True)
            angles = torch.acos(torch.clamp(dot_product / (norm_product + 1e-8), -1, 1))
        else:
            angles = torch.zeros_like(distances, device=device)

        return distances.to(device), angles.to(device), relative_pos.to(device)
    
    def forward(self, xyz):
        """
        Args:
            xyz: [B, N, 3] 点云坐标
        """
        distances, angles, relative_pos = self.compute_geometric_features(xyz)
        
        # 处理距离特征
        dist_features = self.distance_mlp(distances.permute(0, 3, 1, 2))  # [B, 64, N, k]
        
        # 处理角度特征
        angle_features = self.angle_mlp(angles.permute(0, 3, 1, 2))  # [B, 64, N, k]
        
        # 聚合
        dist_features = dist_features.max(dim=-1)[0]  # [B, 64, N]
        angle_features = angle_features.max(dim=-1)[0]  # [B, 64, N]
        
        return dist_features, angle_features, relative_pos

class DensityFeatureExtractor(nn.Module):
    """密度特征提取器 - 检测孔洞等稀疏区域"""
    
    def __init__(self, radius_list=[1.0, 2.0, 4.0]):
        super(DensityFeatureExtractor, self).__init__()
        
        self.radius_list = radius_list
        
        # 多尺度密度特征
        self.density_mlps = nn.ModuleList([
            nn.Sequential(
                nn.Conv1d(1, 32, 1),
                nn.BatchNorm1d(32),
                nn.ReLU(),
                nn.Conv1d(32, 64, 1),
                nn.BatchNorm1d(64),
                nn.ReLU()
            ) for _ in radius_list
        ])
        
        # 密度变化特征
        self.density_change_mlp = nn.Sequential(
            nn.Conv1d(len(radius_list), 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU()
        )
        
    def compute_density(self, xyz, radius):
        """计算指定半径内的点密度"""
        B, N, _ = xyz.shape
        device = xyz.device

        # 计算距离矩阵
        inner = -2 * torch.matmul(xyz, xyz.transpose(2, 1))
        xx = torch.sum(xyz**2, dim=2, keepdim=True)
        pairwise_distance = torch.sqrt(-xx - inner - xx.transpose(2, 1) + 1e-8)

        # 统计半径内的点数
        within_radius = (pairwise_distance < radius).float()
        density = torch.sum(within_radius, dim=2, keepdim=True)  # [B, N, 1]

        return density.to(device)
    
    def forward(self, xyz):
        """
        Args:
            xyz: [B, N, 3] 点云坐标
        """
        density_features = []
        raw_densities = []
        
        # 多尺度密度计算
        for i, radius in enumerate(self.radius_list):
            density = self.compute_density(xyz, radius)  # [B, N, 1]
            raw_densities.append(density)
            
            # 特征提取
            density_feat = self.density_mlps[i](density.transpose(2, 1))  # [B, 64, N]
            density_features.append(density_feat)
        
        # 密度变化特征
        density_stack = torch.cat(raw_densities, dim=2).transpose(2, 1)  # [B, len(radius_list), N]
        density_change = self.density_change_mlp(density_stack)  # [B, 128, N]
        
        # 组合所有密度特征
        all_density_features = torch.cat(density_features + [density_change], dim=1)
        
        return all_density_features

class CurvatureFeatureExtractor(nn.Module):
    """曲率特征提取器"""
    
    def __init__(self, k=16):
        super(CurvatureFeatureExtractor, self).__init__()
        
        self.k = k
        
        # 主曲率特征
        self.principal_curvature_mlp = nn.Sequential(
            nn.Conv1d(2, 64, 1),  # 主曲率1 + 主曲率2
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU()
        )
        
        # 高斯曲率特征
        self.gaussian_curvature_mlp = nn.Sequential(
            nn.Conv1d(1, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU()
        )
        
    def estimate_curvature(self, xyz):
        """估计曲率"""
        B, N, _ = xyz.shape
        device = xyz.device

        # K近邻
        inner = -2 * torch.matmul(xyz, xyz.transpose(2, 1))
        xx = torch.sum(xyz**2, dim=2, keepdim=True)
        pairwise_distance = -xx - inner - xx.transpose(2, 1)
        idx = pairwise_distance.topk(k=self.k, dim=-1)[1]

        # 获取邻居点
        idx_base = torch.arange(0, B, device=device).view(-1, 1, 1) * N
        idx = idx + idx_base
        idx = idx.view(-1)

        neighbors = xyz.view(B*N, -1)[idx, :].view(B, N, self.k, 3)
        center = xyz.view(B, N, 1, 3)

        # 简化的曲率估计 (基于邻居点的分布)
        relative_pos = neighbors - center  # [B, N, k, 3]

        # 计算协方差矩阵的特征值作为曲率的近似
        cov_matrix = torch.matmul(relative_pos.transpose(3, 2), relative_pos)  # [B, N, 3, 3]

        # 特征值分解 (简化版)
        eigenvalues = torch.linalg.eigvals(cov_matrix).real  # [B, N, 3]
        eigenvalues = torch.sort(eigenvalues, dim=2, descending=True)[0]

        # 主曲率
        k1 = eigenvalues[:, :, 0:1]  # 最大特征值
        k2 = eigenvalues[:, :, 1:2]  # 第二大特征值

        # 高斯曲率 (k1 * k2)
        gaussian_curvature = k1 * k2

        return k1.to(device), k2.to(device), gaussian_curvature.to(device)
    
    def forward(self, xyz):
        """
        Args:
            xyz: [B, N, 3] 点云坐标
        """
        k1, k2, gaussian_curvature = self.estimate_curvature(xyz)
        
        # 主曲率特征
        principal_curvatures = torch.cat([k1, k2], dim=2).transpose(2, 1)  # [B, 2, N]
        principal_features = self.principal_curvature_mlp(principal_curvatures)  # [B, 128, N]
        
        # 高斯曲率特征
        gaussian_features = self.gaussian_curvature_mlp(gaussian_curvature.transpose(2, 1))  # [B, 128, N]
        
        return principal_features, gaussian_features

class MultiModalFeatureFusion(nn.Module):
    """多模态特征融合网络"""
    
    def __init__(self, num_keypoints=12):
        super(MultiModalFeatureFusion, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 各种特征提取器
        self.geometric_extractor = GeometricFeatureExtractor(k=16)
        self.density_extractor = DensityFeatureExtractor(radius_list=[1.0, 2.0, 4.0])
        self.curvature_extractor = CurvatureFeatureExtractor(k=16)
        
        # 基础PointNet特征
        self.pointnet_conv1 = nn.Conv1d(3, 64, 1)
        self.pointnet_conv2 = nn.Conv1d(64, 128, 1)
        self.pointnet_conv3 = nn.Conv1d(128, 256, 1)
        
        self.pointnet_bn1 = nn.BatchNorm1d(64)
        self.pointnet_bn2 = nn.BatchNorm1d(128)
        self.pointnet_bn3 = nn.BatchNorm1d(256)
        
        # 特征融合
        # 几何(64+64) + 密度(64*3+128) + 曲率(128+128) + PointNet(256) = 128+320+256+256 = 960
        self.feature_fusion = nn.Sequential(
            nn.Conv1d(960, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Conv1d(512, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 关键点预测
        self.keypoint_head = nn.Sequential(
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(64, num_keypoints * 3)
        )
        
        print(f"🧠 多模态特征融合: {num_keypoints}个关键点")
        print(f"   - 几何特征: 距离 + 角度")
        print(f"   - 密度特征: 多尺度密度 + 变化")
        print(f"   - 曲率特征: 主曲率 + 高斯曲率")
        print(f"   - PointNet特征: 基础特征")
        
    def forward(self, x):
        """
        Args:
            x: [B, N, 3] 输入点云
        """
        batch_size = x.size(0)
        xyz = x  # [B, N, 3]
        
        # 1. 几何特征
        dist_features, angle_features, _ = self.geometric_extractor(xyz)  # [B, 64, N], [B, 64, N]
        
        # 2. 密度特征
        density_features = self.density_extractor(xyz)  # [B, 320, N]
        
        # 3. 曲率特征
        principal_features, gaussian_features = self.curvature_extractor(xyz)  # [B, 128, N], [B, 128, N]
        
        # 4. PointNet特征
        pointnet_input = xyz.transpose(2, 1)  # [B, 3, N]
        pn_feat1 = F.relu(self.pointnet_bn1(self.pointnet_conv1(pointnet_input)))
        pn_feat2 = F.relu(self.pointnet_bn2(self.pointnet_conv2(pn_feat1)))
        pn_feat3 = F.relu(self.pointnet_bn3(self.pointnet_conv3(pn_feat2)))
        
        # 5. 特征融合
        all_features = torch.cat([
            dist_features, angle_features,  # 几何特征
            density_features,               # 密度特征
            principal_features, gaussian_features,  # 曲率特征
            pn_feat3                       # PointNet特征
        ], dim=1)  # [B, 960, N]
        
        fused_features = self.feature_fusion(all_features)  # [B, 256, N]
        
        # 6. 全局特征
        global_features = fused_features.max(dim=-1)[0]  # [B, 256]
        
        # 7. 关键点预测
        keypoints = self.keypoint_head(global_features)  # [B, num_keypoints * 3]
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

def test_multimodal_fusion():
    """测试多模态特征融合"""
    
    print("🧪 **测试多模态特征融合**")
    print("🎯 **几何+密度+曲率+PointNet特征**")
    print("=" * 80)
    
    batch_size = 2
    num_points = 4096
    num_keypoints = 12
    
    # 创建测试数据
    test_input = torch.randn(batch_size, num_points, 3)
    
    print(f"📊 测试输入: {test_input.shape}")
    
    # 测试模型
    model = MultiModalFeatureFusion(num_keypoints=num_keypoints)
    
    with torch.no_grad():
        output = model(test_input)
        print(f"📊 输出形状: {output.shape}")
    
    # 参数统计
    total_params = sum(p.numel() for p in model.parameters())
    print(f"\n📊 模型参数: {total_params:,}")
    
    print(f"\n✅ 多模态特征融合测试通过!")
    
    return model

if __name__ == "__main__":
    model = test_multimodal_fusion()
    
    print(f"\n🎉 **多模态特征融合准备完成!**")
    print("=" * 50)
    print(f"🔬 核心创新:")
    print(f"   1. 几何特征: 距离和角度关系")
    print(f"   2. 密度特征: 多尺度点密度 (检测孔洞)")
    print(f"   3. 曲率特征: 主曲率和高斯曲率")
    print(f"   4. 特征融合: 960维 -> 256维")
    print(f"🎯 预期改进:")
    print(f"   - 骶骨孔洞的密度特征检测")
    print(f"   - 曲率变化的精确捕获")
    print(f"   - 多模态信息互补")
