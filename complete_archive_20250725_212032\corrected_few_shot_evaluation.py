#!/usr/bin/env python3
"""
修正的小样本学习评估 - 真实物理空间误差
Corrected Few-Shot Learning Evaluation - Real Physical Space Errors
"""

import torch
import torch.nn as nn
import numpy as np
import json
from datetime import datetime
from pathlib import Path
from sklearn.model_selection import train_test_split

class CorrectedKeypointNet(nn.Module):
    """修正的关键点检测网络 - 直接在物理空间训练"""
    
    def __init__(self, input_dim=3, hidden_dim=256, output_dim=19*3, dropout=0.2):
        super().__init__()
        
        # 简化的网络架构
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(128, 256),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(256, hidden_dim),
            nn.ReLU()
        )
        
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(hidden_dim, 512),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, output_dim)
        )
        
    def forward(self, point_cloud):
        batch_size = point_cloud.size(0)
        
        # 点级特征提取
        pc_flat = point_cloud.view(-1, 3)  # (B*N, 3)
        features = self.feature_extractor(pc_flat)  # (B*N, hidden_dim)
        features = features.view(batch_size, -1, features.size(-1))  # (B, N, hidden_dim)
        
        # 全局最大池化
        global_feature, _ = torch.max(features, dim=1)  # (B, hidden_dim)
        
        # 关键点预测
        keypoints = self.keypoint_regressor(global_feature)  # (B, 19*3)
        keypoints = keypoints.view(batch_size, 19, 3)  # (B, 19, 3)
        
        return keypoints

class CorrectedFewShotTrainer:
    """修正的小样本学习训练器 - 在真实物理空间训练"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def load_data_without_normalization(self, data_path='data/raw/high_quality_f3_dataset.npz'):
        """加载数据但不进行标准化"""
        print(f"📦 加载数据: {data_path}")
        
        data = np.load(data_path, allow_pickle=True)
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        print(f"原始数据范围:")
        print(f"  点云: [{np.min(point_clouds):.1f}, {np.max(point_clouds):.1f}]mm")
        print(f"  关键点: [{np.min(keypoints):.1f}, {np.max(keypoints):.1f}]mm")
        
        # 只进行下采样，不标准化
        processed_pcs = []
        for pc in point_clouds:
            if len(pc) > 4096:
                indices = np.random.choice(len(pc), 4096, replace=False)
                pc_sampled = pc[indices]
            else:
                indices = np.random.choice(len(pc), 4096, replace=True)
                pc_sampled = pc[indices]
            processed_pcs.append(pc_sampled)
        
        point_clouds = np.array(processed_pcs)
        
        # 数据划分
        indices = np.arange(len(sample_ids))
        train_val_indices, test_indices = train_test_split(
            indices, test_size=0.15, random_state=42
        )
        train_indices, val_indices = train_test_split(
            train_val_indices, test_size=0.18, random_state=42
        )
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices],
                'sample_ids': sample_ids[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices],
                'sample_ids': sample_ids[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices],
                'sample_ids': sample_ids[test_indices]
            }
        }
        
        print(f"✅ 数据准备完成: 训练{len(train_indices)}, 验证{len(val_indices)}, 测试{len(test_indices)}")
        return self.data
    
    def train_model_physical_space(self, k_shot, epochs=100, lr=0.0001):
        """在物理空间直接训练模型"""
        print(f"\n🎯 在物理空间训练 {k_shot}-shot 模型")
        
        # 创建模型
        model = CorrectedKeypointNet().to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=15, factor=0.5)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        patience = 0
        max_patience = 30
        
        for epoch in range(epochs):
            model.train()
            
            # 采样训练数据
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                min(k_shot, len(self.data['train']['point_clouds'])), 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 简单的数据增强
            aug_pcs = []
            aug_kps = []
            
            for pc, kp in zip(train_pcs, train_kps):
                # 原始数据
                aug_pcs.append(pc)
                aug_kps.append(kp)
                
                # 轻微旋转
                angle = np.random.uniform(-0.05, 0.05)  # ±3度
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                
                aug_pc = pc @ rotation.T
                aug_kp = kp @ rotation.T
                aug_pcs.append(aug_pc)
                aug_kps.append(aug_kp)
                
                # 轻微噪声
                noise_pc = pc + np.random.normal(0, 0.5, pc.shape)  # 0.5mm噪声
                aug_pcs.append(noise_pc)
                aug_kps.append(kp)
            
            # 转换为tensor
            aug_pcs = torch.FloatTensor(np.stack(aug_pcs)).to(self.device)
            aug_kps = torch.FloatTensor(np.stack(aug_kps)).to(self.device)
            
            # 训练步骤
            optimizer.zero_grad()
            pred_kps = model(aug_pcs)
            loss = criterion(pred_kps, aug_kps)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_model_physical(model, 'val')
                scheduler.step(val_error)
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                    patience = 0
                else:
                    patience += 1
                
                print(f"Epoch {epoch:3d}: Loss={loss:.2f}, Val_Error={val_error:.2f}mm, LR={optimizer.param_groups[0]['lr']:.6f}")
                
                if patience >= max_patience:
                    print(f"早停在epoch {epoch}")
                    break
        
        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def evaluate_model_physical(self, model, split='test'):
        """在物理空间评估模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for pc, kp in zip(pcs, kps):
                pc_tensor = torch.FloatTensor(pc).unsqueeze(0).to(self.device)
                kp_tensor = torch.FloatTensor(kp).unsqueeze(0).to(self.device)
                
                pred_kp = model(pc_tensor)
                
                # 直接在物理空间计算误差 (mm)
                error = torch.mean(torch.norm(pred_kp - kp_tensor, dim=2))
                total_error += error.item()
                num_samples += 1
        
        return total_error / num_samples if num_samples > 0 else float('inf')

def run_corrected_experiment():
    """运行修正的小样本学习实验"""
    print("🔧 修正的小样本学习实验 - 物理空间训练")
    print("=" * 60)
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 初始化训练器
    trainer = CorrectedFewShotTrainer()
    
    # 加载数据 (不标准化)
    data = trainer.load_data_without_normalization()
    
    # 实验配置
    shot_configs = [3, 5, 10, 15, 20]
    results = {}
    
    for k_shot in shot_configs:
        print(f"\n{'='*60}")
        
        # 训练模型
        model, val_error = trainer.train_model_physical_space(k_shot, epochs=100, lr=0.0001)
        
        # 测试评估
        test_error = trainer.evaluate_model_physical(model, 'test')
        results[k_shot] = test_error
        
        print(f"✅ {k_shot}-shot 最终测试误差: {test_error:.2f}mm")
    
    # 结果分析
    print(f"\n📊 修正后的实验结果")
    print("=" * 50)
    
    for k_shot, error in results.items():
        medical_grade = "✅" if error < 5.0 else "❌"
        precision_grade = "🎯" if error < 2.0 else "📈" if error < 10.0 else "❌"
        print(f"{k_shot:2d}-shot: {error:6.2f}mm {medical_grade} {precision_grade}")
    
    # 找出最佳配置
    best_shot = min(results.keys(), key=lambda k: results[k])
    best_error = results[best_shot]
    
    print(f"\n🏆 最佳配置: {best_shot}-shot")
    print(f"🎯 最佳误差: {best_error:.2f}mm")
    
    if best_error < 2.0:
        print("🎉 达到医疗级精度 (<2mm)!")
    elif best_error < 5.0:
        print("👍 接近医疗应用水平 (<5mm)")
    elif best_error < 10.0:
        print("📈 有改进空间，但仍需优化")
    else:
        print("❌ 需要重新设计方法")
    
    # 与基线对比
    baseline_error = 7.5  # 假设的基线误差
    print(f"\n📈 与基线对比:")
    for k_shot, error in results.items():
        improvement = (baseline_error - error) / baseline_error * 100
        print(f"{k_shot:2d}-shot: {improvement:+5.1f}% 改进")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = Path("results/few_shot_experiments")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    result_data = {
        "experiment_type": "corrected_physical_space_few_shot",
        "timestamp": timestamp,
        "results": results,
        "best_configuration": {
            "k_shot": best_shot,
            "error_mm": best_error
        },
        "notes": "训练和评估都在真实物理空间进行，无标准化误导",
        "dataset_info": {
            "total_samples": 97,
            "train_samples": len(data['train']['point_clouds']),
            "val_samples": len(data['val']['point_clouds']),
            "test_samples": len(data['test']['point_clouds'])
        }
    }
    
    with open(results_dir / f"corrected_few_shot_results_{timestamp}.json", 'w') as f:
        json.dump(result_data, f, indent=2)
    
    print(f"\n💾 修正结果已保存")
    
    return results

if __name__ == "__main__":
    results = run_corrected_experiment()
