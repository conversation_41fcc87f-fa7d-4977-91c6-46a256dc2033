<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="1280" height="720" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="url(#headerGrad)"/>
  <text x="640" y="50" text-anchor="middle" fill="white" 
        font-family="Arial, sans-serif" font-size="36" font-weight="bold">
    Conclusion & Future Work
  </text>
  
  <!-- Main Contributions -->
  <rect x="50" y="100" width="580" height="580" rx="15" fill="white" stroke="#10b981" stroke-width="3"/>
  <text x="340" y="130" text-anchor="middle" fill="#059669" 
        font-family="Arial, sans-serif" font-size="24" font-weight="bold">
    Main Contributions
  </text>
  
  <!-- Contribution 1 -->
  <rect x="70" y="150" width="540" height="120" rx="10" fill="#f0fdf4" stroke="#22c55e" stroke-width="2"/>
  <text x="90" y="175" fill="#15803d" font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    1️⃣ Novel Neural Network Architecture for 3D Keypoint Detection
  </text>
  <text x="90" y="200" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • First supervised framework for detecting keypoints with specific semantic meaning
  </text>
  <text x="90" y="220" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Direct detection on dense, complex 3D point clouds (medical applications)
  </text>
  <text x="90" y="240" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Eliminates need for additional 2D image annotations
  </text>
  <text x="90" y="260" fill="#15803d" font-family="Arial, sans-serif" font-size="13" font-weight="bold">
    ✓ Enables precise one-to-one keypoint detection with anatomical significance
  </text>
  
  <!-- Contribution 2 -->
  <rect x="70" y="280" width="540" height="120" rx="10" fill="#fef2f2" stroke="#ef4444" stroke-width="2"/>
  <text x="90" y="305" fill="#dc2626" font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    2️⃣ Introduction of "Penalty Dice Loss"
  </text>
  <text x="90" y="330" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Addresses class imbalance challenge in dense point clouds
  </text>
  <text x="90" y="350" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Specifically targets small yet critical areas (keypoint regions)
  </text>
  <text x="90" y="370" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Prevents undetected regions through exponential penalty mechanism
  </text>
  <text x="90" y="390" fill="#dc2626" font-family="Arial, sans-serif" font-size="13" font-weight="bold">
    ✓ Improves mIoU from 62.59% to 66.60%, enables robust detection at lower k values
  </text>
  
  <!-- Contribution 3 -->
  <rect x="70" y="410" width="540" height="120" rx="10" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="2"/>
  <text x="90" y="435" fill="#7c3aed" font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    3️⃣ Innovative Keypoint Localization Mechanism
  </text>
  <text x="90" y="460" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • PointNet encoder with residual modules for feature extraction
  </text>
  <text x="90" y="480" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Novel "Double SoftMax" weighting mechanism for precise localization
  </text>
  <text x="90" y="500" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Weighted average approach for final keypoint coordinates
  </text>
  <text x="90" y="520" fill="#7c3aed" font-family="Arial, sans-serif" font-size="13" font-weight="bold">
    ✓ Achieves 1.43mm MRE (skull), 1.54mm MRE (tibia), comparable to 2D methods
  </text>
  
  <!-- Performance Summary -->
  <rect x="70" y="540" width="540" height="130" rx="10" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="2"/>
  <text x="340" y="565" text-anchor="middle" fill="#0c4a6e" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    🏆 Performance Summary
  </text>
  <text x="90" y="590" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Skull Dataset: 1.43mm MRE, 76.00% SDR (2mm threshold)
  </text>
  <text x="90" y="610" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Tibia Dataset: 1.54mm MRE, 76.40% SDR (2mm threshold)
  </text>
  <text x="90" y="630" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Meets clinical accuracy requirements (&lt;2mm error)
  </text>
  <text x="90" y="650" fill="#0c4a6e" font-family="Arial, sans-serif" font-size="13" font-weight="bold">
    • Demonstrates broad applicability to dense, complex point clouds
  </text>
  
  <!-- Future Work & Limitations -->
  <rect x="650" y="100" width="580" height="580" rx="15" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <text x="940" y="130" text-anchor="middle" fill="#d97706" 
        font-family="Arial, sans-serif" font-size="24" font-weight="bold">
    Future Work & Limitations
  </text>
  
  <!-- Current Limitations -->
  <rect x="670" y="150" width="540" height="140" rx="10" fill="#fef3c7" stroke="#fcd34d" stroke-width="2"/>
  <text x="940" y="175" text-anchor="middle" fill="#92400e" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    ⚠️ Current Limitations
  </text>
  <text x="680" y="200" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Manual 3D annotation has larger absolute errors compared to 2D images
  </text>
  <text x="680" y="220" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Limited training dataset size affects model stability
  </text>
  <text x="680" y="240" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Inference time (~2s) may limit real-time intraoperative applications
  </text>
  <text x="680" y="260" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Performance varies with anatomical variations and atypical samples
  </text>
  <text x="680" y="280" fill="#92400e" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Need for larger datasets and optimization for real-time processing
  </text>
  
  <!-- Short-term Future Work -->
  <rect x="670" y="300" width="540" height="160" rx="10" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="2"/>
  <text x="940" y="325" text-anchor="middle" fill="#0c4a6e" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    🎯 Short-term Future Work (1-2 years)
  </text>
  <text x="680" y="350" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Optimize network architecture for real-time processing
  </text>
  <text x="680" y="370" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Expand datasets with more diverse anatomical variations
  </text>
  <text x="680" y="390" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Develop automated quality assessment metrics
  </text>
  <text x="680" y="410" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Integrate with existing clinical workflow systems
  </text>
  <text x="680" y="430" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Conduct larger-scale clinical validation studies
  </text>
  <text x="680" y="450" fill="#0c4a6e" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Focus: Clinical deployment and validation
  </text>
  
  <!-- Long-term Future Work -->
  <rect x="670" y="470" width="540" height="200" rx="10" fill="#f3e8ff" stroke="#c084fc" stroke-width="2"/>
  <text x="940" y="495" text-anchor="middle" fill="#7c3aed" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    🚀 Long-term Vision (3-5 years)
  </text>
  <text x="680" y="520" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Multi-modal integration (CT, MRI, ultrasound, optical imaging)
  </text>
  <text x="680" y="540" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Extension to other anatomical structures (vertebrae, pelvis, etc.)
  </text>
  <text x="680" y="560" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Dynamic keypoint tracking for surgical navigation
  </text>
  <text x="680" y="580" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • AI-assisted surgical planning with predictive modeling
  </text>
  <text x="680" y="600" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Personalized treatment recommendations based on keypoint analysis
  </text>
  <text x="680" y="620" fill="#374151" font-family="Arial, sans-serif" font-size="13">
    • Cross-domain applications (industrial, robotics, autonomous systems)
  </text>
  <text x="680" y="640" fill="#7c3aed" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Vision: Comprehensive 3D understanding for precision medicine
  </text>
  <text x="680" y="660" fill="#6b21a8" font-family="Arial, sans-serif" font-size="11" font-style="italic">
    "Transforming medical imaging from visualization to intelligent analysis"
  </text>
</svg>
