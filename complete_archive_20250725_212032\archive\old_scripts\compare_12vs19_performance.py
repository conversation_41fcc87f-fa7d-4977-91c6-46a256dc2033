#!/usr/bin/env python3
"""
对比12点vs19点性能差异
分析为什么19点性能突然变差
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps
from train_real_f3_19keypoints import RealF3HeatmapPointNet

def extract_keypoints_from_heatmaps_f3(heatmaps, point_cloud):
    """从F3热力图中提取关键点位置"""
    num_keypoints = heatmaps.shape[0]
    keypoints = []
    confidences = []

    for kp_idx in range(num_keypoints):
        heatmap = heatmaps[kp_idx]

        # 找到最大值位置
        max_idx = np.argmax(heatmap)
        max_confidence = heatmap[max_idx]

        # 提取对应的3D坐标
        keypoint_3d = point_cloud[max_idx]

        keypoints.append(keypoint_3d)
        confidences.append(max_confidence)

    return np.array(keypoints), np.array(confidences)

def load_original_12kp_data():
    """加载原始12关键点数据"""
    try:
        data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
        return data['point_clouds'], data['keypoints'], data['sample_ids']
    except:
        return None, None, None

def load_real_19kp_data():
    """加载真实19关键点数据"""
    try:
        data = np.load('f3_19kp_real.npz', allow_pickle=True)
        return data['point_clouds'], data['keypoints'], data['sample_ids']
    except:
        return None, None, None

def test_12kp_model_on_samples(model_12kp, point_clouds, keypoints_12, sample_ids, device):
    """测试12关键点模型"""
    
    print("🔍 测试12关键点模型...")
    
    results_12kp = []
    
    for i in range(len(point_clouds)):
        sample_id = sample_ids[i]
        point_cloud = point_clouds[i]
        true_keypoints = keypoints_12[i]
        
        # 采样点云
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            pc_sampled = point_cloud[indices]
        else:
            pc_sampled = point_cloud
        
        pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
        
        # 预测
        with torch.no_grad():
            pred_heatmaps = model_12kp(pc_tensor)
        
        pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze()
        pred_keypoints, confidences = extract_keypoints_from_heatmaps(pred_heatmaps_np, pc_sampled)
        
        # 计算误差
        errors = [np.linalg.norm(pred_keypoints[j] - true_keypoints[j]) for j in range(len(true_keypoints))]
        avg_error = np.mean(errors)
        
        results_12kp.append({
            'sample_id': sample_id,
            'avg_error': avg_error,
            'errors': errors,
            'confidences': confidences
        })
        
        print(f"   样本 {sample_id}: {avg_error:.2f}mm")
    
    return results_12kp

def test_19kp_model_on_samples(model_19kp, point_clouds, keypoints_19, sample_ids, device):
    """测试19关键点模型"""
    
    print("🔍 测试19关键点模型...")
    
    results_19kp = []
    
    for i in range(len(point_clouds)):
        sample_id = sample_ids[i]
        point_cloud = point_clouds[i]
        true_keypoints = keypoints_19[i]
        
        # 采样点云
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            pc_sampled = point_cloud[indices]
        else:
            pc_sampled = point_cloud
        
        pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
        
        # 预测
        with torch.no_grad():
            pred_heatmaps = model_19kp(pc_tensor)
        
        pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze()
        pred_keypoints, confidences = extract_keypoints_from_heatmaps_f3(pred_heatmaps_np, pc_sampled)
        
        # 计算误差
        errors = [np.linalg.norm(pred_keypoints[j] - true_keypoints[j]) for j in range(len(true_keypoints))]
        avg_error = np.mean(errors)
        
        results_19kp.append({
            'sample_id': sample_id,
            'avg_error': avg_error,
            'errors': errors,
            'confidences': confidences
        })
        
        print(f"   样本 {sample_id}: {avg_error:.2f}mm")
    
    return results_19kp

def analyze_data_differences(pc_12kp, kp_12kp, ids_12kp, pc_19kp, kp_19kp, ids_19kp):
    """分析数据差异"""
    
    print("\n🔍 数据差异分析:")
    print("=" * 50)
    
    # 找到共同样本
    common_ids = set(ids_12kp) & set(ids_19kp)
    print(f"共同样本数: {len(common_ids)}")
    print(f"12点数据样本数: {len(ids_12kp)}")
    print(f"19点数据样本数: {len(ids_19kp)}")
    
    if len(common_ids) > 0:
        # 分析共同样本的点云差异
        sample_id = list(common_ids)[0]
        
        idx_12 = list(ids_12kp).index(sample_id)
        idx_19 = list(ids_19kp).index(sample_id)
        
        pc_12 = pc_12kp[idx_12]
        pc_19 = pc_19kp[idx_19]
        kp_12 = kp_12kp[idx_12]
        kp_19 = kp_19kp[idx_19]
        
        print(f"\n样本 {sample_id} 对比:")
        print(f"   12点数据点云大小: {len(pc_12)}")
        print(f"   19点数据点云大小: {len(pc_19)}")
        print(f"   12点关键点数: {len(kp_12)}")
        print(f"   19点关键点数: {len(kp_19)}")
        
        # 检查点云范围
        print(f"\n点云范围对比:")
        print(f"   12点数据 X: {np.min(pc_12[:, 0]):.1f} - {np.max(pc_12[:, 0]):.1f}")
        print(f"   19点数据 X: {np.min(pc_19[:, 0]):.1f} - {np.max(pc_19[:, 0]):.1f}")
        print(f"   12点数据 Y: {np.min(pc_12[:, 1]):.1f} - {np.max(pc_12[:, 1]):.1f}")
        print(f"   19点数据 Y: {np.min(pc_19[:, 1]):.1f} - {np.max(pc_19[:, 1]):.1f}")
        print(f"   12点数据 Z: {np.min(pc_12[:, 2]):.1f} - {np.max(pc_12[:, 2]):.1f}")
        print(f"   19点数据 Z: {np.min(pc_19[:, 2]):.1f} - {np.max(pc_19[:, 2]):.1f}")
        
        # 检查关键点范围
        print(f"\n关键点范围对比:")
        print(f"   12点关键点 X: {np.min(kp_12[:, 0]):.1f} - {np.max(kp_12[:, 0]):.1f}")
        print(f"   19点关键点 X: {np.min(kp_19[:, 0]):.1f} - {np.max(kp_19[:, 0]):.1f}")
        print(f"   12点关键点 Z: {np.min(kp_12[:, 2]):.1f} - {np.max(kp_12[:, 2]):.1f}")
        print(f"   19点关键点 Z: {np.min(kp_19[:, 2]):.1f} - {np.max(kp_19[:, 2]):.1f}")
        
        return sample_id, pc_12, pc_19, kp_12, kp_19
    
    return None, None, None, None, None

def create_comparison_visualization(results_12kp, results_19kp, sample_id, pc_12, pc_19, kp_12, kp_19):
    """创建对比可视化"""
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 性能对比
    ax1 = axes[0, 0]
    
    if results_12kp and results_19kp:
        errors_12 = [r['avg_error'] for r in results_12kp]
        errors_19 = [r['avg_error'] for r in results_19kp]
        
        ax1.boxplot([errors_12, errors_19], labels=['12-Point', '19-Point'])
        ax1.set_ylabel('Average Error (mm)')
        ax1.set_title('Performance Comparison')
        ax1.grid(True, alpha=0.3)
        
        # 添加统计信息
        ax1.text(0.5, 0.95, f'12-Point: {np.mean(errors_12):.1f}±{np.std(errors_12):.1f}mm\n'
                           f'19-Point: {np.mean(errors_19):.1f}±{np.std(errors_19):.1f}mm',
                transform=ax1.transAxes, ha='center', va='top',
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 2. 样本误差对比
    ax2 = axes[0, 1]
    
    if results_12kp and results_19kp:
        sample_ids = [r['sample_id'] for r in results_12kp]
        errors_12 = [r['avg_error'] for r in results_12kp]
        errors_19 = [r['avg_error'] for r in results_19kp]
        
        x = np.arange(len(sample_ids))
        width = 0.35
        
        ax2.bar(x - width/2, errors_12, width, label='12-Point', alpha=0.7)
        ax2.bar(x + width/2, errors_19, width, label='19-Point', alpha=0.7)
        
        ax2.set_xlabel('Sample Index')
        ax2.set_ylabel('Average Error (mm)')
        ax2.set_title('Error by Sample')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
    
    # 3. 点云对比 (如果有共同样本)
    if pc_12 is not None and pc_19 is not None:
        ax3 = axes[0, 2]
        
        # 采样显示
        if len(pc_12) > 3000:
            idx_12 = np.random.choice(len(pc_12), 3000, replace=False)
            display_pc_12 = pc_12[idx_12]
        else:
            display_pc_12 = pc_12
            
        if len(pc_19) > 3000:
            idx_19 = np.random.choice(len(pc_19), 3000, replace=False)
            display_pc_19 = pc_19[idx_19]
        else:
            display_pc_19 = pc_19
        
        ax3.scatter(display_pc_12[:, 0], display_pc_12[:, 2], 
                   c='blue', s=1, alpha=0.5, label='12-Point Data')
        ax3.scatter(display_pc_19[:, 0], display_pc_19[:, 2], 
                   c='red', s=1, alpha=0.5, label='19-Point Data')
        
        ax3.set_xlabel('X (mm)')
        ax3.set_ylabel('Z (mm)')
        ax3.set_title(f'Point Cloud Comparison\nSample {sample_id}')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.axis('equal')
    
    # 4. 关键点分布对比
    if kp_12 is not None and kp_19 is not None:
        ax4 = axes[1, 0]
        
        ax4.scatter(kp_12[:, 0], kp_12[:, 2], c='blue', s=100, marker='o', 
                   alpha=0.8, label='12 Keypoints')
        ax4.scatter(kp_19[:, 0], kp_19[:, 2], c='red', s=50, marker='^', 
                   alpha=0.8, label='19 Keypoints')
        
        ax4.set_xlabel('X (mm)')
        ax4.set_ylabel('Z (mm)')
        ax4.set_title(f'Keypoint Distribution\nSample {sample_id}')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        ax4.axis('equal')
    
    # 5. 数据统计对比
    ax5 = axes[1, 1]
    ax5.axis('off')
    
    stats_text = "Data Statistics Comparison:\n\n"
    
    if pc_12 is not None and pc_19 is not None:
        stats_text += f"Point Cloud Size:\n"
        stats_text += f"  12-Point: {len(pc_12):,} points\n"
        stats_text += f"  19-Point: {len(pc_19):,} points\n\n"
        
        stats_text += f"Point Cloud Range:\n"
        stats_text += f"  12-Point X: {np.max(pc_12[:, 0]) - np.min(pc_12[:, 0]):.1f}mm\n"
        stats_text += f"  19-Point X: {np.max(pc_19[:, 0]) - np.min(pc_19[:, 0]):.1f}mm\n"
        stats_text += f"  12-Point Z: {np.max(pc_12[:, 2]) - np.min(pc_12[:, 2]):.1f}mm\n"
        stats_text += f"  19-Point Z: {np.max(pc_19[:, 2]) - np.min(pc_19[:, 2]):.1f}mm\n\n"
    
    if results_12kp and results_19kp:
        avg_12 = np.mean([r['avg_error'] for r in results_12kp])
        avg_19 = np.mean([r['avg_error'] for r in results_19kp])
        
        stats_text += f"Performance:\n"
        stats_text += f"  12-Point Avg: {avg_12:.2f}mm\n"
        stats_text += f"  19-Point Avg: {avg_19:.2f}mm\n"
        stats_text += f"  Degradation: {avg_19 - avg_12:.2f}mm\n"
        stats_text += f"  Relative: {(avg_19 - avg_12) / avg_12 * 100:.1f}%\n"
    
    ax5.text(0.05, 0.95, stats_text, transform=ax5.transAxes, fontsize=10,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    
    # 6. 可能原因分析
    ax6 = axes[1, 2]
    ax6.axis('off')
    
    analysis_text = "Possible Reasons for Performance Drop:\n\n"
    analysis_text += "1. Data Source Differences:\n"
    analysis_text += "   • Different STL files?\n"
    analysis_text += "   • Different preprocessing?\n"
    analysis_text += "   • Coordinate system mismatch?\n\n"
    
    analysis_text += "2. Model Architecture:\n"
    analysis_text += "   • Same architecture for different tasks?\n"
    analysis_text += "   • Different output dimensions (12 vs 19)?\n"
    analysis_text += "   • Training data size difference?\n\n"
    
    analysis_text += "3. Training Issues:\n"
    analysis_text += "   • Insufficient training data (20 samples)?\n"
    analysis_text += "   • Different loss functions?\n"
    analysis_text += "   • Overfitting to small dataset?\n\n"
    
    analysis_text += "4. Evaluation Differences:\n"
    analysis_text += "   • Different test samples?\n"
    analysis_text += "   • Different evaluation metrics?\n"
    analysis_text += "   • Random sampling effects?\n"
    
    ax6.text(0.05, 0.95, analysis_text, transform=ax6.transAxes, fontsize=9,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
    
    plt.suptitle('12-Point vs 19-Point Performance Analysis\nWhy Did Performance Drop?', 
                fontsize=16, fontweight='bold')
    plt.tight_layout(rect=[0, 0, 1, 0.93])
    
    filename = '12vs19_performance_comparison.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 对比分析保存: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🔍 12点 vs 19点性能对比分析")
    print("分析为什么19点性能突然变差")
    print("=" * 60)
    
    # 加载数据
    pc_12kp, kp_12kp, ids_12kp = load_original_12kp_data()
    pc_19kp, kp_19kp, ids_19kp = load_real_19kp_data()
    
    if pc_12kp is None:
        print("❌ 无法加载12点数据")
        return
    
    if pc_19kp is None:
        print("❌ 无法加载19点数据")
        return
    
    print(f"✅ 数据加载成功")
    print(f"   12点数据: {len(pc_12kp)} 样本")
    print(f"   19点数据: {len(pc_19kp)} 样本")
    
    # 分析数据差异
    sample_id, pc_12, pc_19, kp_12, kp_19 = analyze_data_differences(
        pc_12kp, kp_12kp, ids_12kp, pc_19kp, kp_19kp, ids_19kp
    )
    
    # 加载模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 12点模型
    model_12kp = None
    try:
        model_12kp = HeatmapPointNet().to(device)
        model_12kp.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model_12kp.eval()
        print("✅ 12点模型加载成功")
    except Exception as e:
        print(f"❌ 12点模型加载失败: {e}")
    
    # 19点模型
    model_19kp = None
    try:
        model_19kp = RealF3HeatmapPointNet(input_dim=3, num_keypoints=19).to(device)
        model_19kp.load_state_dict(torch.load('best_real_f3_19kp_model.pth', map_location=device))
        model_19kp.eval()
        print("✅ 19点模型加载成功")
    except Exception as e:
        print(f"❌ 19点模型加载失败: {e}")
    
    # 测试模型性能
    results_12kp = None
    results_19kp = None
    
    if model_12kp is not None:
        results_12kp = test_12kp_model_on_samples(model_12kp, pc_12kp, kp_12kp, ids_12kp, device)
    
    if model_19kp is not None:
        results_19kp = test_19kp_model_on_samples(model_19kp, pc_19kp, kp_19kp, ids_19kp, device)
    
    # 创建对比可视化
    create_comparison_visualization(results_12kp, results_19kp, sample_id, pc_12, pc_19, kp_12, kp_19)
    
    # 总结分析
    print(f"\n🎯 性能对比总结:")
    print("=" * 50)
    
    if results_12kp:
        avg_12 = np.mean([r['avg_error'] for r in results_12kp])
        print(f"12点模型平均误差: {avg_12:.2f}mm")
    
    if results_19kp:
        avg_19 = np.mean([r['avg_error'] for r in results_19kp])
        print(f"19点模型平均误差: {avg_19:.2f}mm")
    
    if results_12kp and results_19kp:
        degradation = avg_19 - avg_12
        print(f"性能下降: {degradation:.2f}mm ({degradation/avg_12*100:.1f}%)")
    
    print(f"\n💡 可能的原因:")
    print("1. 数据来源不同 - 19点数据直接从STL加载，12点数据可能经过预处理")
    print("2. 训练数据量 - 19点只有20个样本，可能不足")
    print("3. 任务复杂度 - 19个关键点比12个更难学习")
    print("4. 坐标系不一致 - STL文件和标注文件可能有偏移")
    print("5. 模型架构 - 可能需要针对19点任务优化")

if __name__ == "__main__":
    main()
