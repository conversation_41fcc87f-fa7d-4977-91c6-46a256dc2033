#!/usr/bin/env python3
"""
监控过夜优化进度
实时显示优化状态和结果
"""

import os
import json
import time
import glob
from datetime import datetime

def monitor_optimization():
    """监控优化进程"""
    
    print("🔍 **过夜优化监控器**")
    print("📊 **实时显示优化进度和结果**")
    print("=" * 60)
    
    # 查找结果目录
    result_dirs = glob.glob("overnight_optimization_*")
    
    if not result_dirs:
        print("❌ 未找到优化结果目录")
        print("💡 程序可能还在启动中...")
        return
    
    # 使用最新的结果目录
    latest_dir = max(result_dirs, key=os.path.getctime)
    print(f"📁 监控目录: {latest_dir}")
    
    # 监控循环
    last_update = 0
    
    while True:
        try:
            # 检查进度报告
            progress_file = os.path.join(latest_dir, "progress_report.json")
            
            if os.path.exists(progress_file):
                # 检查文件是否更新
                current_mtime = os.path.getmtime(progress_file)
                
                if current_mtime > last_update:
                    last_update = current_mtime
                    
                    with open(progress_file, 'r') as f:
                        progress = json.load(f)
                    
                    print(f"\n⏰ 更新时间: {progress['timestamp']}")
                    print(f"📊 已测试配置: {progress['total_configs_tested']}")
                    print(f"✅ 有效结果: {progress['valid_results']}")
                    print(f"🏆 当前最佳: {progress['current_best']['config_name']}")
                    print(f"📈 最佳性能: {progress['current_best']['best_performance']:.3f}mm")
                    print(f"📊 平均性能: {progress['current_best']['avg_performance']:.3f}mm")
                    print(f"🎯 基线对比: {progress['baseline']:.3f}mm")
                    
                    if progress['current_best']['best_performance'] < progress['baseline']:
                        improvement = (progress['baseline'] - progress['current_best']['best_performance']) / progress['baseline'] * 100
                        print(f"🎉 突破基线! 提升{improvement:.1f}%")
                    else:
                        gap = (progress['current_best']['best_performance'] - progress['baseline']) / progress['baseline'] * 100
                        print(f"💡 距离基线: +{gap:.1f}%")
            
            # 检查最终报告
            final_file = os.path.join(latest_dir, "final_report.json")
            if os.path.exists(final_file):
                print(f"\n🎉 **优化完成!**")
                
                with open(final_file, 'r') as f:
                    final_report = json.load(f)
                
                summary = final_report['summary']
                print(f"⏱️ 总运行时间: {summary['total_runtime_hours']:.1f}小时")
                print(f"🔧 总配置数: {summary['total_configs']}")
                print(f"✅ 有效配置: {summary['valid_configs']}")
                print(f"🏆 最佳性能: {summary['best_performance']:.3f}mm")
                print(f"🎯 基线: {summary['baseline']:.3f}mm")
                
                if summary['breakthrough']:
                    improvement = (summary['baseline'] - summary['best_performance']) / summary['baseline'] * 100
                    print(f"🎉 成功突破! 提升{improvement:.1f}%")
                else:
                    print(f"💡 未突破基线，但获得了宝贵经验")
                
                print(f"\n🏆 **前5名配置**:")
                for i, result in enumerate(final_report['top_10_results'][:5]):
                    config_name = result['config']['name']
                    best_perf = result['best_fold']
                    print(f"   {i+1}. {config_name}: {best_perf:.3f}mm")
                
                break
            
            # 等待30秒再检查
            time.sleep(30)
            
        except KeyboardInterrupt:
            print(f"\n⏹️ 监控停止")
            break
        except Exception as e:
            print(f"\n❌ 监控错误: {e}")
            time.sleep(30)

def check_process_status():
    """检查进程状态"""
    
    import subprocess
    
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        if 'comprehensive_overnight_optimization' in result.stdout:
            print("✅ 优化进程正在运行")
            return True
        else:
            print("❌ 优化进程未运行")
            return False
    except:
        print("❓ 无法检查进程状态")
        return False

def show_log_tail():
    """显示日志尾部"""
    
    log_file = "overnight_optimization.log"
    if os.path.exists(log_file):
        print(f"\n📋 **最新日志 (最后20行)**:")
        print("-" * 60)
        
        try:
            with open(log_file, 'r') as f:
                lines = f.readlines()
                for line in lines[-20:]:
                    print(line.rstrip())
        except:
            print("❌ 无法读取日志文件")
    else:
        print("❌ 日志文件不存在")

if __name__ == "__main__":
    print("🌙 **过夜优化状态检查**")
    print("=" * 60)
    
    # 检查进程状态
    is_running = check_process_status()
    
    # 显示日志
    show_log_tail()
    
    if is_running:
        print(f"\n🔍 开始监控优化进度...")
        monitor_optimization()
    else:
        print(f"\n💡 优化进程未运行，可能已完成或出错")
        
        # 检查是否有结果
        result_dirs = glob.glob("overnight_optimization_*")
        if result_dirs:
            latest_dir = max(result_dirs, key=os.path.getctime)
            print(f"📁 发现结果目录: {latest_dir}")
            
            final_file = os.path.join(latest_dir, "final_report.json")
            if os.path.exists(final_file):
                print(f"🎉 发现最终报告，优化已完成!")
            else:
                print(f"💡 优化可能被中断，检查中间结果...")
