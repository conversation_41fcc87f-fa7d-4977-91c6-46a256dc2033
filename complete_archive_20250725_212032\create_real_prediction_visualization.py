#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实模型预测结果可视化
Real Model Prediction Results Visualization
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import torch
import torch.nn as nn
import torch.nn.functional as F
import pandas as pd
import os
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class AdaptiveKeypointModel(nn.Module):
    """自适应关键点模型 - 根据关键点数量自动调整架构"""
    
    def __init__(self, num_points=50000, num_keypoints=12, architecture_type='auto'):
        super().__init__()
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        self.architecture_type = architecture_type
        
        # 根据关键点数量选择最佳架构
        if architecture_type == 'auto':
            if num_keypoints <= 6:
                self.arch_type = 'lightweight'
            elif num_keypoints <= 12:
                self.arch_type = 'balanced'
            elif num_keypoints <= 28:
                self.arch_type = 'enhanced'
            else:
                self.arch_type = 'deep'
        else:
            self.arch_type = architecture_type
        
        # 构建对应架构
        self._build_architecture()
    
    def _build_architecture(self):
        """根据类型构建架构"""
        
        if self.arch_type == 'lightweight':
            # 轻量级架构 (3-6关键点)
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(256, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, 128), nn.ReLU(), nn.Dropout(0.1),
                nn.Linear(128, self.num_keypoints * 3)
            )
            
        elif self.arch_type == 'balanced':
            # 平衡架构 (7-12关键点)
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(512, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, self.num_keypoints * 3)
            )
            
        elif self.arch_type == 'enhanced':
            # 增强架构 (13-28关键点)
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
                nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(1024, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 1024), nn.ReLU(), nn.Dropout(0.4),
                nn.Linear(1024, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, self.num_keypoints * 3)
            )
            
        else:  # deep
            # 深度架构 (29-57关键点)
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
                nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU(),
                nn.Conv1d(1024, 2048, 1), nn.BatchNorm1d(2048), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(2048, 1024, 1)
            self.predictor = nn.Sequential(
                nn.Linear(1024, 2048), nn.ReLU(), nn.Dropout(0.5),
                nn.Linear(2048, 1024), nn.ReLU(), nn.Dropout(0.4),
                nn.Linear(1024, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, self.num_keypoints * 3)
            )
        
        # 相互辅助机制 (所有架构都有)
        mutual_dim = min(256, max(64, self.num_keypoints * 8))
        self.mutual_assistance = nn.Sequential(
            nn.Linear(self.num_keypoints * 3, mutual_dim),
            nn.ReLU(), nn.Dropout(0.2),
            nn.Linear(mutual_dim, mutual_dim // 2),
            nn.ReLU(),
            nn.Linear(mutual_dim // 2, self.num_keypoints * 3)
        )
    
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取
        features = self.feature_extractor(x)
        
        # 全局特征
        global_features = self.global_conv(features)
        global_feat = torch.max(global_features, 2)[0]
        
        # 初始预测
        initial_kp = self.predictor(global_feat)
        
        # 相互辅助
        assistance = self.mutual_assistance(initial_kp)
        
        # 最终预测
        final_kp = initial_kp + 0.3 * assistance
        final_kp = final_kp.view(batch_size, self.num_keypoints, 3)
        
        return final_kp

def load_optimal_models():
    """加载最佳模型配置"""
    print("📋 加载最佳模型配置...")
    
    # 读取最佳模型表格
    df = pd.read_csv('comprehensive_optimal_models_table.csv')
    
    optimal_models = {}
    for _, row in df.iterrows():
        keypoints = int(row['Keypoints'])
        architecture = row['Architecture']
        avg_error = float(row['Avg_Error_mm'])
        
        model_path = f'best_{keypoints}kp_{architecture}.pth'
        if os.path.exists(model_path):
            optimal_models[keypoints] = {
                'path': model_path,
                'architecture': architecture,
                'expected_error': avg_error
            }
            print(f"  ✅ {keypoints}点 {architecture}架构: {avg_error:.2f}mm")
        else:
            print(f"  ❌ 模型文件不存在: {model_path}")
    
    return optimal_models

def load_real_data():
    """加载真实医疗数据"""
    print("📥 加载真实医疗数据...")
    
    data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints_57 = data['keypoints_57']
    
    print(f"✅ 数据加载完成: {len(point_clouds)}个样本")
    print(f"   点云形状: {point_clouds.shape}")
    print(f"   关键点形状: {keypoints_57.shape}")
    
    return point_clouds, keypoints_57

def predict_with_model(model, point_cloud, device='cuda'):
    """使用模型进行预测"""
    model.eval()
    with torch.no_grad():
        pc_tensor = torch.FloatTensor(point_cloud).unsqueeze(0).to(device)
        prediction = model(pc_tensor)
        return prediction.cpu().numpy()[0]

def create_real_prediction_visualization():
    """创建真实预测结果可视化"""
    print("🎯 创建真实模型预测结果可视化")
    print("=" * 60)
    
    # 加载数据和模型
    point_clouds, keypoints_57 = load_real_data()
    optimal_models = load_optimal_models()
    
    # 选择要测试的模型配置
    selected_configs = [3, 15, 47, 57]  # 代表性配置
    available_configs = [k for k in selected_configs if k in optimal_models]
    
    if len(available_configs) < 4:
        available_configs = sorted(list(optimal_models.keys()))[:4]
    
    print(f"🔧 测试配置: {available_configs}")
    
    # 选择测试样本
    sample_idx = 0
    test_pc = point_clouds[sample_idx]
    true_kp_57 = keypoints_57[sample_idx]
    
    print(f"📊 测试样本 {sample_idx}")
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ 使用设备: {device}")
    
    # 进行预测
    predictions = {}
    
    for kp_count in available_configs:
        model_info = optimal_models[kp_count]
        
        print(f"\n🔄 测试 {kp_count}点 {model_info['architecture']}架构...")
        
        # 创建模型
        model = AdaptiveKeypointModel(
            num_points=50000, 
            num_keypoints=kp_count, 
            architecture_type=model_info['architecture']
        )
        
        # 加载权重
        try:
            checkpoint = torch.load(model_info['path'], map_location=device)
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
            else:
                model.load_state_dict(checkpoint)
            model.to(device)
            
            # 进行预测
            pred_kp = predict_with_model(model, test_pc, device)
            
            # 选择对应的真实关键点
            if kp_count == 57:
                true_kp = true_kp_57
            else:
                # 均匀采样选择关键点
                indices = np.linspace(0, 56, kp_count, dtype=int)
                true_kp = true_kp_57[indices]
            
            # 计算误差
            errors = np.linalg.norm(true_kp - pred_kp, axis=1)
            avg_error = np.mean(errors)
            
            predictions[kp_count] = {
                'true': true_kp,
                'pred': pred_kp,
                'errors': errors,
                'avg_error': avg_error,
                'expected_error': model_info['expected_error'],
                'architecture': model_info['architecture']
            }
            
            print(f"  ✅ 预测完成: 实际误差 {avg_error:.2f}mm (期望 {model_info['expected_error']:.2f}mm)")
            
        except Exception as e:
            print(f"  ❌ 模型加载失败: {e}")
            continue
    
    return test_pc, predictions, sample_idx

def visualize_real_predictions(test_pc, predictions, sample_idx):
    """可视化真实预测结果"""
    print(f"\n🎨 创建真实预测结果可视化...")

    # 创建2x2子图
    fig = plt.figure(figsize=(20, 16))

    configs = sorted(list(predictions.keys()))[:4]

    for i, kp_count in enumerate(configs):
        ax = fig.add_subplot(2, 2, i+1, projection='3d')

        data = predictions[kp_count]
        true_kp = data['true']
        pred_kp = data['pred']
        avg_error = data['avg_error']
        expected_error = data['expected_error']
        architecture = data['architecture']

        # 采样点云以提高可视化性能
        sample_indices = np.random.choice(len(test_pc), min(3000, len(test_pc)), replace=False)
        pc_sample = test_pc[sample_indices]

        # 绘制点云（浅灰色背景）
        ax.scatter(pc_sample[:, 0], pc_sample[:, 1], pc_sample[:, 2],
                  c='lightgray', s=0.3, alpha=0.15, label='Point Cloud')

        # 绘制真实关键点（绿色圆点）
        ax.scatter(true_kp[:, 0], true_kp[:, 1], true_kp[:, 2],
                  c='green', s=80, alpha=0.9, label='Ground Truth',
                  marker='o', edgecolors='darkgreen', linewidth=1.5)

        # 绘制预测关键点（红色三角）
        ax.scatter(pred_kp[:, 0], pred_kp[:, 1], pred_kp[:, 2],
                  c='red', s=80, alpha=0.9, label='Prediction',
                  marker='^', edgecolors='darkred', linewidth=1.5)

        # 绘制误差连接线
        for j in range(len(true_kp)):
            ax.plot([true_kp[j, 0], pred_kp[j, 0]],
                   [true_kp[j, 1], pred_kp[j, 1]],
                   [true_kp[j, 2], pred_kp[j, 2]],
                   'b--', alpha=0.6, linewidth=1.2)

        # 设置标题
        title = f'{kp_count} Keypoints ({architecture})\n'
        title += f'Actual: {avg_error:.2f}mm, Expected: {expected_error:.2f}mm'
        ax.set_title(title, fontsize=12, fontweight='bold')

        # 设置标签
        ax.set_xlabel('X (mm)', fontsize=10)
        ax.set_ylabel('Y (mm)', fontsize=10)
        ax.set_zlabel('Z (mm)', fontsize=10)

        # 设置图例
        if i == 0:
            ax.legend(loc='upper right', fontsize=9)

        # 设置视角
        ax.view_init(elev=20, azim=45)

        # 设置坐标轴范围
        margin = 20
        ax.set_xlim([test_pc[:, 0].min()-margin, test_pc[:, 0].max()+margin])
        ax.set_ylim([test_pc[:, 1].min()-margin, test_pc[:, 1].max()+margin])
        ax.set_zlim([test_pc[:, 2].min()-margin, test_pc[:, 2].max()+margin])

    plt.suptitle(f'Real Medical Pelvis Keypoint Detection - Sample {sample_idx}',
                fontsize=16, fontweight='bold', y=0.95)
    plt.tight_layout()

    # 保存图片
    filename = f'real_prediction_results_sample_{sample_idx}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()

    print(f"✅ 真实预测可视化已保存: {filename}")
    return filename

def create_error_analysis(predictions):
    """创建误差分析图表"""
    print("📊 创建真实模型误差分析...")

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 提取数据
    configs = sorted(predictions.keys())
    actual_errors = [predictions[k]['avg_error'] for k in configs]
    expected_errors = [predictions[k]['expected_error'] for k in configs]
    architectures = [predictions[k]['architecture'] for k in configs]

    # 1. 实际误差 vs 期望误差
    ax1.plot(configs, expected_errors, 'o-', label='Expected Error',
             linewidth=2, markersize=8, color='blue')
    ax1.plot(configs, actual_errors, 's-', label='Actual Error',
             linewidth=2, markersize=8, color='red')
    ax1.axhline(y=10, color='orange', linestyle='--', alpha=0.7, label='Medical Grade (10mm)')
    ax1.axhline(y=5, color='green', linestyle='--', alpha=0.7, label='Excellent Grade (5mm)')
    ax1.set_xlabel('Number of Keypoints')
    ax1.set_ylabel('Average Error (mm)')
    ax1.set_title('Real Model Performance vs Expected')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. 误差分布箱线图
    error_distributions = []
    labels = []
    for k in configs:
        error_distributions.append(predictions[k]['errors'])
        labels.append(f'{k}kp\n{predictions[k]["architecture"]}')

    ax2.boxplot(error_distributions, labels=labels)
    ax2.set_ylabel('Error (mm)')
    ax2.set_title('Error Distribution by Configuration')
    ax2.grid(True, alpha=0.3)

    # 3. 医疗级达标率
    medical_rates = []
    excellent_rates = []

    for k in configs:
        errors = predictions[k]['errors']
        medical_rate = np.sum(errors <= 10) / len(errors) * 100
        excellent_rate = np.sum(errors <= 5) / len(errors) * 100
        medical_rates.append(medical_rate)
        excellent_rates.append(excellent_rate)

    width = 0.35
    x = np.arange(len(configs))
    ax3.bar(x - width/2, medical_rates, width, label='Medical Grade (≤10mm)', alpha=0.8)
    ax3.bar(x + width/2, excellent_rates, width, label='Excellent Grade (≤5mm)', alpha=0.8)
    ax3.set_xlabel('Number of Keypoints')
    ax3.set_ylabel('Success Rate (%)')
    ax3.set_title('Medical Grade Achievement Rate')
    ax3.set_xticks(x)
    ax3.set_xticklabels([str(k) for k in configs])
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 4. 架构性能对比
    arch_performance = {}
    for k in configs:
        arch = predictions[k]['architecture']
        error = predictions[k]['avg_error']
        if arch not in arch_performance:
            arch_performance[arch] = []
        arch_performance[arch].append(error)

    arch_names = list(arch_performance.keys())
    arch_means = [np.mean(arch_performance[arch]) for arch in arch_names]
    arch_stds = [np.std(arch_performance[arch]) if len(arch_performance[arch]) > 1 else 0
                 for arch in arch_names]

    colors = ['#F18F01', '#C73E1D', '#A23B72', '#2E86AB'][:len(arch_names)]
    bars = ax4.bar(arch_names, arch_means, yerr=arch_stds, capsize=5,
                   color=colors, alpha=0.7)
    ax4.set_ylabel('Average Error (mm)')
    ax4.set_title('Architecture Performance Comparison')
    ax4.grid(True, alpha=0.3, axis='y')

    # 添加数值标签
    for bar, mean in zip(bars, arch_means):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                f'{mean:.2f}', ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()
    plt.savefig('real_model_error_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("✅ 真实模型误差分析已保存: real_model_error_analysis.png")

def print_summary(predictions, sample_idx):
    """打印总结报告"""
    print(f"\n📋 真实模型测试总结 (样本 {sample_idx})")
    print("=" * 60)

    configs = sorted(predictions.keys())

    print("🎯 模型性能:")
    for k in configs:
        data = predictions[k]
        print(f"  {k:2d}点 {data['architecture']:10s}: "
              f"实际 {data['avg_error']:5.2f}mm (期望 {data['expected_error']:5.2f}mm)")

    # 找到最佳性能
    best_config = min(configs, key=lambda k: predictions[k]['avg_error'])
    best_data = predictions[best_config]

    print(f"\n🏆 最佳性能: {best_config}点 {best_data['architecture']}架构")
    print(f"   误差: {best_data['avg_error']:.2f}mm")
    print(f"   医疗级达标率: {np.sum(best_data['errors'] <= 10) / len(best_data['errors']) * 100:.1f}%")
    print(f"   优秀级达标率: {np.sum(best_data['errors'] <= 5) / len(best_data['errors']) * 100:.1f}%")

    print(f"\n📊 整体统计:")
    all_errors = [predictions[k]['avg_error'] for k in configs]
    print(f"   平均误差范围: {min(all_errors):.2f} - {max(all_errors):.2f}mm")
    print(f"   所有模型均达到医疗级标准 (≤10mm): {'✅' if max(all_errors) <= 10 else '❌'}")

if __name__ == "__main__":
    # 创建真实预测可视化
    test_pc, predictions, sample_idx = create_real_prediction_visualization()

    if predictions:
        # 创建可视化
        visualize_real_predictions(test_pc, predictions, sample_idx)

        # 创建误差分析
        create_error_analysis(predictions)

        # 打印总结
        print_summary(predictions, sample_idx)
    else:
        print("❌ 没有成功加载任何模型进行测试")
