#!/usr/bin/env python3
"""
测试修正版预处理效果 - 真实物理空间评估
Test Corrected Preprocessing Effects - Real Physical Space Evaluation
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json
from pathlib import Path
from datetime import datetime
from sklearn.model_selection import train_test_split

class SimpleKeypointNet(nn.Module):
    """简单的关键点检测网络"""
    
    def __init__(self):
        super().__init__()
        self.feature_extractor = nn.Sequential(
            nn.Linear(3, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 128),
            nn.ReLU()
        )
        
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(128, 256),
            nn.<PERSON><PERSON><PERSON>(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 19*3)
        )
        
    def forward(self, point_cloud):
        batch_size = point_cloud.size(0)
        pc_flat = point_cloud.view(-1, 3)
        features = self.feature_extractor(pc_flat)
        features = features.view(batch_size, -1, features.size(-1))
        global_feature, _ = torch.max(features, dim=1)
        keypoints = self.keypoint_regressor(global_feature)
        return keypoints.view(batch_size, 19, 3)

class CorrectedPreprocessingTester:
    """修正版预处理测试器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def load_data(self, data_path, data_type="unknown"):
        """加载数据"""
        print(f"📦 加载{data_type}数据: {data_path}")
        
        data = np.load(data_path, allow_pickle=True)
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        # 确保数据类型正确
        point_clouds = np.array([np.array(pc, dtype=np.float32) for pc in point_clouds])
        keypoints = np.array([np.array(kp, dtype=np.float32) for kp in keypoints])
        
        print(f"✅ {data_type}数据加载完成: {len(sample_ids)} 样本")
        print(f"   点云形状: {point_clouds.shape}")
        print(f"   关键点形状: {keypoints.shape}")
        print(f"   点云范围: [{np.min(point_clouds):.1f}, {np.max(point_clouds):.1f}]mm")
        print(f"   关键点范围: [{np.min(keypoints):.1f}, {np.max(keypoints):.1f}]mm")
        
        # 数据划分
        indices = np.arange(len(sample_ids))
        train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
        train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
        
        return {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
    
    def train_model(self, data, data_type="unknown", k_shot=15, epochs=40):
        """训练模型"""
        print(f"\n🎯 训练模型 ({data_type}, {k_shot}-shot, {epochs} epochs)")
        
        # 创建模型
        model = SimpleKeypointNet().to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=0.002, weight_decay=1e-4)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        patience = 0
        max_patience = 20
        
        for epoch in range(epochs):
            model.train()
            
            # 采样训练数据
            train_indices = np.random.choice(
                len(data['train']['point_clouds']), 
                min(k_shot, len(data['train']['point_clouds'])), 
                replace=False
            )
            
            train_pcs = data['train']['point_clouds'][train_indices]
            train_kps = data['train']['keypoints'][train_indices]
            
            # 简单数据增强
            aug_pcs = []
            aug_kps = []
            
            for pc, kp in zip(train_pcs, train_kps):
                # 原始数据
                aug_pcs.append(pc)
                aug_kps.append(kp)
                
                # 轻微旋转 (保持在真实物理空间)
                angle = np.random.uniform(-0.05, 0.05)  # ±3度
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], dtype=np.float32)
                
                aug_pc = pc @ rotation.T
                aug_kp = kp @ rotation.T
                aug_pcs.append(aug_pc)
                aug_kps.append(aug_kp)
                
                # 轻微噪声 (物理空间的合理噪声)
                noise_pc = pc + np.random.normal(0, 0.5, pc.shape).astype(np.float32)  # 0.5mm噪声
                aug_pcs.append(noise_pc)
                aug_kps.append(kp)
            
            # 转换为tensor
            train_pcs_tensor = torch.FloatTensor(aug_pcs).to(self.device)
            train_kps_tensor = torch.FloatTensor(aug_kps).to(self.device)
            
            # 训练步骤
            optimizer.zero_grad()
            pred_kps = model(train_pcs_tensor)
            loss = criterion(pred_kps, train_kps_tensor)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            scheduler.step()
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_model(model, data, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                    patience = 0
                else:
                    patience += 1
                
                print(f"Epoch {epoch:2d}: Loss={loss:.3f}, Val_Error={val_error:.2f}mm, LR={optimizer.param_groups[0]['lr']:.6f}")
                
                if patience >= max_patience:
                    print(f"早停在epoch {epoch}")
                    break
            
            # 清理内存
            del train_pcs_tensor, train_kps_tensor, pred_kps, loss
            torch.cuda.empty_cache()
        
        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def evaluate_model(self, model, data, split='test'):
        """评估模型 - 在真实物理空间"""
        model.eval()
        total_error = 0
        num_samples = 0
        detailed_errors = []
        
        with torch.no_grad():
            pcs = data[split]['point_clouds']
            kps = data[split]['keypoints']
            
            for pc, kp in zip(pcs, kps):
                pc_tensor = torch.FloatTensor(pc).unsqueeze(0).to(self.device)
                kp_tensor = torch.FloatTensor(kp).unsqueeze(0).to(self.device)
                
                pred_kp = model(pc_tensor).cpu().numpy()[0]
                true_kp = kp
                
                # 在真实物理空间计算误差 (mm)
                errors = np.linalg.norm(pred_kp - true_kp, axis=1)
                mean_error = np.mean(errors)
                
                total_error += mean_error
                num_samples += 1
                detailed_errors.append(mean_error)
                
                # 清理内存
                del pc_tensor, kp_tensor
                torch.cuda.empty_cache()
        
        overall_error = total_error / num_samples if num_samples > 0 else float('inf')
        return overall_error
    
    def detailed_evaluation(self, model, data, split='test'):
        """详细评估 - 返回每个样本的结果"""
        model.eval()
        detailed_results = []
        
        with torch.no_grad():
            pcs = data[split]['point_clouds']
            kps = data[split]['keypoints']
            
            for i, (pc, kp) in enumerate(zip(pcs, kps)):
                pc_tensor = torch.FloatTensor(pc).unsqueeze(0).to(self.device)
                pred_kp = model(pc_tensor).cpu().numpy()[0]
                true_kp = kp
                
                # 计算误差
                errors = np.linalg.norm(pred_kp - true_kp, axis=1)
                mean_error = np.mean(errors)
                
                detailed_results.append({
                    'sample_index': i,
                    'predicted': pred_kp,
                    'true': true_kp,
                    'errors': errors,
                    'mean_error': mean_error
                })
                
                # 清理内存
                del pc_tensor
                torch.cuda.empty_cache()
        
        return detailed_results

def run_corrected_comparison():
    """运行修正版对比测试"""
    print("🔍 修正版预处理效果对比测试")
    print("=" * 60)
    print("目标: 在真实物理空间验证预处理效果")
    
    tester = CorrectedPreprocessingTester()
    
    # 测试原始数据
    print("\n" + "="*60)
    print("📊 测试原始数据 (真实物理空间)")
    original_data = tester.load_data('data/raw/high_quality_f3_dataset.npz', "原始")
    original_model, original_val_error = tester.train_model(original_data, "原始数据", k_shot=15, epochs=40)
    original_test_error = tester.evaluate_model(original_model, original_data, 'test')
    
    print(f"✅ 原始数据结果: {original_test_error:.2f}mm")
    
    # 清理GPU内存
    del original_model, original_data
    torch.cuda.empty_cache()
    
    # 测试修正版预处理数据
    print("\n" + "="*60)
    print("📊 测试修正版预处理数据 (真实物理空间)")
    
    # 查找最新的修正版预处理文件
    processed_files = list(Path("data/processed").glob("corrected_lightweight_*.npz"))
    if not processed_files:
        print("❌ 未找到修正版预处理数据")
        return
    
    latest_file = max(processed_files, key=lambda x: x.stat().st_mtime)
    corrected_data = tester.load_data(str(latest_file), "修正版预处理")
    corrected_model, corrected_val_error = tester.train_model(corrected_data, "修正版预处理", k_shot=15, epochs=40)
    corrected_test_error = tester.evaluate_model(corrected_model, corrected_data, 'test')
    
    print(f"✅ 修正版预处理结果: {corrected_test_error:.2f}mm")
    
    # 计算真实改进
    real_improvement = (original_test_error - corrected_test_error) / original_test_error * 100
    
    print(f"\n📊 真实物理空间对比结果:")
    print("=" * 50)
    print(f"原始数据误差:     {original_test_error:.2f}mm")
    print(f"修正预处理误差:   {corrected_test_error:.2f}mm")
    print(f"真实改进幅度:     {real_improvement:+.1f}%")
    
    # 分析结果
    print(f"\n🤔 结果分析:")
    if real_improvement > 10:
        print("🎉 修正版预处理效果显著！")
        status = "显著改进"
    elif real_improvement > 0:
        print("👍 修正版预处理有一定效果")
        status = "有效改进"
    elif real_improvement > -5:
        print("📊 修正版预处理效果中性")
        status = "效果中性"
    else:
        print("⚠️ 修正版预处理可能有负面影响")
        status = "需要调整"
    
    # 详细评估前几个样本
    print(f"\n📊 详细样本分析:")
    corrected_detailed = tester.detailed_evaluation(corrected_model, corrected_data, 'test')
    
    for i, result in enumerate(corrected_detailed[:5]):
        print(f"样本 {i+1}: 平均误差 = {result['mean_error']:.2f}mm")
        print(f"  误差范围: {np.min(result['errors']):.2f} - {np.max(result['errors']):.2f}mm")
        print(f"  <5mm的点: {np.sum(result['errors'] < 5.0)}/19")
        print(f"  <10mm的点: {np.sum(result['errors'] < 10.0)}/19")
    
    # 可视化对比
    visualize_corrected_results(corrected_detailed[:3])
    
    # 保存结果
    results = {
        "test_timestamp": datetime.now().isoformat(),
        "test_type": "corrected_preprocessing_comparison",
        "evaluation_space": "real_physical_space_mm",
        "original_error": original_test_error,
        "corrected_error": corrected_test_error,
        "real_improvement_percent": real_improvement,
        "status": status,
        "test_config": {
            "k_shot": 15,
            "epochs": 40,
            "point_cloud_size": 4096,
            "model": "SimpleKeypointNet",
            "evaluation_space": "真实物理空间 (mm)"
        },
        "detailed_sample_errors": [r['mean_error'] for r in corrected_detailed],
        "conclusion": f"修正版预处理在真实物理空间的改进为{real_improvement:+.1f}%"
    }
    
    # 保存结果
    results_dir = Path("results/corrected_tests")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = results_dir / f"corrected_preprocessing_test_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 测试结果已保存: {results_file}")
    
    return results

def visualize_corrected_results(detailed_results):
    """可视化修正版结果"""
    print(f"\n📊 可视化修正版结果...")
    
    viz_dir = Path("results/corrected_visualization")
    viz_dir.mkdir(parents=True, exist_ok=True)
    
    for i, result in enumerate(detailed_results):
        fig = plt.figure(figsize=(15, 10))
        
        # 3D可视化
        ax1 = fig.add_subplot(221, projection='3d')
        
        true_kp = result['true']
        pred_kp = result['predicted']
        
        ax1.scatter(true_kp[:, 0], true_kp[:, 1], true_kp[:, 2], 
                   c='red', s=100, label='True Keypoints', marker='o', alpha=0.8)
        ax1.scatter(pred_kp[:, 0], pred_kp[:, 1], pred_kp[:, 2], 
                   c='blue', s=100, label='Predicted Keypoints', marker='^', alpha=0.8)
        
        # 绘制误差线
        for j in range(len(true_kp)):
            ax1.plot([true_kp[j, 0], pred_kp[j, 0]], 
                    [true_kp[j, 1], pred_kp[j, 1]], 
                    [true_kp[j, 2], pred_kp[j, 2]], 'k--', alpha=0.5)
        
        ax1.set_title(f'Sample {i+1} - Mean Error: {result["mean_error"]:.2f}mm')
        ax1.legend()
        ax1.set_xlabel('X (mm)')
        ax1.set_ylabel('Y (mm)')
        ax1.set_zlabel('Z (mm)')
        
        # 误差分布
        ax2 = fig.add_subplot(222)
        errors = result['errors']
        ax2.bar(range(len(errors)), errors, alpha=0.7, color='skyblue')
        ax2.axhline(y=5.0, color='red', linestyle='--', label='5mm threshold')
        ax2.axhline(y=10.0, color='orange', linestyle='--', label='10mm threshold')
        ax2.set_title(f'Error Distribution - Sample {i+1}')
        ax2.set_xlabel('Keypoint Index')
        ax2.set_ylabel('Error (mm)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # X-Y投影
        ax3 = fig.add_subplot(223)
        ax3.scatter(true_kp[:, 0], true_kp[:, 1], c='red', s=50, label='True', alpha=0.7)
        ax3.scatter(pred_kp[:, 0], pred_kp[:, 1], c='blue', s=50, label='Predicted', alpha=0.7)
        for j in range(len(true_kp)):
            ax3.plot([true_kp[j, 0], pred_kp[j, 0]], 
                    [true_kp[j, 1], pred_kp[j, 1]], 'k--', alpha=0.3)
        ax3.set_title('X-Y Projection')
        ax3.set_xlabel('X (mm)')
        ax3.set_ylabel('Y (mm)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 统计信息
        ax4 = fig.add_subplot(224)
        stats_text = f'Sample {i+1} Statistics:\n\n'
        stats_text += f'Mean Error: {result["mean_error"]:.2f} mm\n'
        stats_text += f'Max Error: {np.max(errors):.2f} mm\n'
        stats_text += f'Min Error: {np.min(errors):.2f} mm\n'
        stats_text += f'Std Error: {np.std(errors):.2f} mm\n'
        stats_text += f'Median Error: {np.median(errors):.2f} mm\n\n'
        stats_text += f'Points < 5mm: {np.sum(errors < 5.0)}/19 ({np.sum(errors < 5.0)/19*100:.1f}%)\n'
        stats_text += f'Points < 10mm: {np.sum(errors < 10.0)}/19 ({np.sum(errors < 10.0)/19*100:.1f}%)\n'
        stats_text += f'Points < 15mm: {np.sum(errors < 15.0)}/19 ({np.sum(errors < 15.0)/19*100:.1f}%)\n\n'
        
        if result["mean_error"] < 5.0:
            stats_text += '🎉 Excellent Result!'
        elif result["mean_error"] < 10.0:
            stats_text += '👍 Good Result!'
        elif result["mean_error"] < 15.0:
            stats_text += '📈 Acceptable Result'
        else:
            stats_text += '⚠️ Needs Improvement'
        
        ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, 
                fontsize=10, verticalalignment='top', fontfamily='monospace')
        ax4.set_xlim(0, 1)
        ax4.set_ylim(0, 1)
        ax4.set_title('Statistics')
        ax4.axis('off')
        
        plt.tight_layout()
        plt.savefig(viz_dir / f"corrected_result_sample_{i+1}.png", 
                   dpi=150, bbox_inches='tight')
        print(f"💾 保存可视化: corrected_result_sample_{i+1}.png")
        plt.show()

if __name__ == "__main__":
    results = run_corrected_comparison()
