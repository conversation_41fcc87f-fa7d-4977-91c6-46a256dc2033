#!/usr/bin/env python3
"""
测试15点扩展
Test 15-Point Expansion
基于修复后的数据集，验证12点→15点的渐进式扩展
"""

import sys
import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
from sklearn.model_selection import train_test_split

# 添加原始代码路径
sys.path.insert(0, os.path.abspath("archive/old_scripts"))

def set_seed(seed=42):
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

# 导入原始模块
from ensemble_double_softmax_exact import ExactEnsembleDoubleSoftMaxPointNet

class ProgressiveDataset(Dataset):
    """渐进式数据集"""
    
    def __init__(self, point_clouds, keypoints):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def train_progressive_model(model, train_loader, val_loader, epochs=80, device='cuda', target_error=None):
    """训练渐进式模型"""
    
    print(f"🚀 训练{model.num_keypoints}点模型...")
    if target_error:
        print(f"   目标误差: {target_error}mm")
    
    model = model.to(device)
    
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=15, min_lr=1e-6
    )
    
    criterion = nn.MSELoss()
    
    best_val_error = float('inf')
    patience_counter = 0
    patience = 25
    
    start_time = time.time()
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            loss.backward()
            
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                val_loss += loss.item()
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 早停检查
        if val_error < best_val_error:
            best_val_error = val_error
            patience_counter = 0
            torch.save(model.state_dict(), f'best_15point_model.pth')
        else:
            patience_counter += 1
        
        # 打印进度
        if epoch % 10 == 0 or epoch < 5:
            print(f"Epoch {epoch+1:3d}: "
                  f"Train: {train_error:.3f}, Val: {val_error:.3f}, "
                  f"LR: {current_lr:.2e}")
        
        # 目标达成检查
        if target_error and val_error <= target_error:
            print(f"🎉 达到目标误差 {target_error}mm！在第{epoch+1}轮")
            break
        
        if patience_counter >= patience:
            print(f"⏹️ 早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    training_time = time.time() - start_time
    
    # 加载最佳模型
    model.load_state_dict(torch.load(f'best_15point_model.pth'))
    
    print(f"✅ {model.num_keypoints}点训练完成!")
    print(f"   最佳验证误差: {best_val_error:.3f}mm")
    print(f"   训练时间: {training_time/60:.1f}分钟")
    
    return model, best_val_error

def test_15point_expansion():
    """测试15点扩展"""
    
    print("🎯 测试15点扩展")
    print("验证12点→15点的渐进式扩展可行性")
    print("=" * 80)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载修复后的数据集
    print("📊 加载修复后数据集...")
    
    try:
        fixed_data = np.load('emergency_fixed_final_dataset.npz', allow_pickle=True)
        point_clouds = fixed_data['point_clouds']
        keypoints_57 = fixed_data['keypoints_57']
        
        print(f"✅ 数据加载成功: {len(point_clouds)}样本")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 定义关键点子集
    keypoint_subsets = {
        12: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17],  # 历史最佳12点
        15: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17, 0, 1, 12],  # 增加3个F1点
    }
    
    results = {}
    
    # 测试12点和15点
    for num_points in [12, 15]:
        print(f"\n🎯 测试{num_points}点模型")
        print("=" * 50)
        
        # 准备数据
        subset_indices = keypoint_subsets[num_points]
        subset_keypoints = keypoints_57[:, subset_indices, :]
        
        # 数据划分
        indices = np.arange(len(point_clouds))
        train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
        train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
        
        train_dataset = ProgressiveDataset(point_clouds[train_indices], subset_keypoints[train_indices])
        val_dataset = ProgressiveDataset(point_clouds[val_indices], subset_keypoints[val_indices])
        
        # 数据加载器
        batch_size = 4
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
        
        print(f"📋 数据: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
        
        # 创建模型
        set_seed(123)  # 确保可重复性
        model = ExactEnsembleDoubleSoftMaxPointNet(num_keypoints=num_points, dropout_rate=0.3, num_ensembles=3)
        
        # 设置目标误差
        target_errors = {12: 7.5, 15: 8.0}  # 基于之前的结果设置合理目标
        target_error = target_errors[num_points]
        
        # 训练模型
        model, best_error = train_progressive_model(
            model, train_loader, val_loader, epochs=80, device=device, target_error=target_error
        )
        
        # 记录结果
        results[num_points] = {
            'best_error': best_error,
            'target_error': target_error,
            'success': best_error <= target_error,
            'keypoint_indices': subset_indices
        }
        
        print(f"📊 {num_points}点结果:")
        print(f"   最佳误差: {best_error:.3f}mm")
        print(f"   目标误差: {target_error:.3f}mm")
        print(f"   {'✅ 成功' if best_error <= target_error else '❌ 未达标'}")
    
    # 分析扩展效果
    print(f"\n📊 渐进式扩展效果分析")
    print("=" * 80)
    
    if 12 in results and 15 in results:
        error_12 = results[12]['best_error']
        error_15 = results[15]['best_error']
        
        expansion_cost = (error_15 - error_12) / error_12 * 100
        
        print(f"📋 扩展效果:")
        print(f"   12点基线: {error_12:.3f}mm")
        print(f"   15点扩展: {error_15:.3f}mm")
        print(f"   扩展代价: {expansion_cost:+.1f}%")
        
        # 评估扩展成功性
        if error_15 <= 8.0:
            expansion_success = "✅ 成功"
            next_step = "可以尝试19点扩展"
        elif error_15 <= 10.0:
            expansion_success = "⚠️ 勉强可接受"
            next_step = "需要优化后再扩展"
        else:
            expansion_success = "❌ 失败"
            next_step = "需要重新设计策略"
        
        print(f"   扩展评估: {expansion_success}")
        print(f"   下一步建议: {next_step}")
        
        # 预测后续扩展
        if error_15 <= 8.0:
            print(f"\n🔮 后续扩展预测:")
            predicted_19 = error_15 * 1.15  # 预估19点会增加15%误差
            predicted_24 = error_15 * 1.35  # 预估24点会增加35%误差
            
            print(f"   预测19点: {predicted_19:.3f}mm")
            print(f"   预测24点: {predicted_24:.3f}mm")
            print(f"   19点可行性: {'✅ 高' if predicted_19 < 10 else '⚠️ 中' if predicted_19 < 12 else '❌ 低'}")
            print(f"   24点可行性: {'✅ 高' if predicted_24 < 12 else '⚠️ 中' if predicted_24 < 15 else '❌ 低'}")
    
    # 保存结果
    import json
    with open('15point_expansion_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 测试结果已保存: 15point_expansion_results.json")
    
    return results

def main():
    """主函数"""
    
    print("🚀 15点扩展测试")
    print("基于修复后数据集验证渐进式扩展策略")
    print("=" * 80)
    
    try:
        results = test_15point_expansion()
        
        # 总结
        print(f"\n🎯 总结:")
        if 12 in results and 15 in results:
            success_12 = results[12]['success']
            success_15 = results[15]['success']
            
            if success_12 and success_15:
                print(f"   🎉 渐进式扩展验证成功！")
                print(f"   ✅ 12点和15点都达到目标")
                print(f"   🚀 可以继续19点扩展")
            elif success_12:
                print(f"   ⚠️ 12点成功，15点需要优化")
                print(f"   🔧 建议调整15点策略")
            else:
                print(f"   ❌ 12点基线需要进一步改进")
                print(f"   🔄 建议重新优化数据集")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
