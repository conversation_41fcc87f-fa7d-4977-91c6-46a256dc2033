#!/usr/bin/env python3
"""
小数据集深度学习解决方案
基于最新研究的10大技术方法
针对我们的97样本医疗点云数据集
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

# ============================================================================
# 方法1: 迁移学习 (Transfer Learning)
# ============================================================================

class PretrainedPointNet(nn.Module):
    """基于预训练的PointNet"""
    
    def __init__(self, num_keypoints=12, pretrained_path=None):
        super(PretrainedPointNet, self).__init__()
        
        # 预训练的特征提取器 (冻结前几层)
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU()
        )
        
        # 可训练的任务特定层
        self.task_specific = nn.Sequential(
            nn.Conv1d(256, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.AdaptiveMaxPool1d(1),
            nn.Flatten(),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(256, num_keypoints * 3)
        )
        
        # 冻结预训练层
        for param in self.feature_extractor.parameters():
            param.requires_grad = False
            
        print("🔄 迁移学习: 冻结预训练特征提取器，只训练任务特定层")
    
    def forward(self, x):
        x = x.transpose(2, 1)  # [B, 3, N]
        features = self.feature_extractor(x)
        output = self.task_specific(features)
        return output.view(-1, 12, 3)

# ============================================================================
# 方法2: 自监督学习 (Self-Supervised Learning)
# ============================================================================

class MaskedAutoEncoder(nn.Module):
    """掩码自编码器 - 自监督预训练"""
    
    def __init__(self, mask_ratio=0.75):
        super(MaskedAutoEncoder, self).__init__()
        
        self.mask_ratio = mask_ratio
        
        # 编码器
        self.encoder = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU()
        )
        
        # 解码器
        self.decoder = nn.Sequential(
            nn.Conv1d(256, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 3, 1)
        )
        
        print(f"🎭 自监督学习: 掩码比例{mask_ratio}, 重建点云")
    
    def random_mask(self, x):
        """随机掩码点云"""
        B, N, C = x.shape
        num_mask = int(N * self.mask_ratio)
        
        masked_x = x.clone()
        for b in range(B):
            mask_indices = torch.randperm(N)[:num_mask]
            masked_x[b, mask_indices] = 0
        
        return masked_x
    
    def forward(self, x):
        # 掩码输入
        masked_x = self.random_mask(x)
        
        # 编码-解码
        x_input = masked_x.transpose(2, 1)
        encoded = self.encoder(x_input)
        decoded = self.decoder(encoded)
        
        return decoded.transpose(2, 1)

# ============================================================================
# 方法3: 少样本学习 (Few-Shot Learning)
# ============================================================================

class PrototypicalNetwork(nn.Module):
    """原型网络 - 少样本学习"""
    
    def __init__(self, feature_dim=256):
        super(PrototypicalNetwork, self).__init__()
        
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, feature_dim, 1),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.AdaptiveMaxPool1d(1),
            nn.Flatten()
        )
        
        print(f"🎯 少样本学习: 原型网络，特征维度{feature_dim}")
    
    def forward(self, support_set, query_set):
        """
        support_set: [N_way, K_shot, N_points, 3]
        query_set: [N_query, N_points, 3]
        """
        # 提取支持集特征
        support_features = []
        for way in support_set:
            way_features = []
            for shot in way:
                feat = self.feature_extractor(shot.transpose(1, 0).unsqueeze(0))
                way_features.append(feat)
            # 计算原型 (平均特征)
            prototype = torch.stack(way_features).mean(dim=0)
            support_features.append(prototype)
        
        prototypes = torch.stack(support_features)  # [N_way, feature_dim]
        
        # 提取查询集特征
        query_features = []
        for query in query_set:
            feat = self.feature_extractor(query.transpose(1, 0).unsqueeze(0))
            query_features.append(feat)
        
        query_features = torch.stack(query_features)  # [N_query, feature_dim]
        
        # 计算距离
        distances = torch.cdist(query_features, prototypes)
        
        return -distances  # 负距离作为logits

# ============================================================================
# 方法4: 数据增强 + 生成对抗网络
# ============================================================================

class PointCloudGAN(nn.Module):
    """点云生成对抗网络"""
    
    def __init__(self, latent_dim=128, num_points=1024):
        super(PointCloudGAN, self).__init__()
        
        self.latent_dim = latent_dim
        self.num_points = num_points
        
        # 生成器
        self.generator = nn.Sequential(
            nn.Linear(latent_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 512),
            nn.ReLU(),
            nn.Linear(512, 1024),
            nn.ReLU(),
            nn.Linear(1024, num_points * 3),
            nn.Tanh()
        )
        
        # 判别器
        self.discriminator = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.LeakyReLU(0.2),
            nn.Conv1d(64, 128, 1),
            nn.LeakyReLU(0.2),
            nn.Conv1d(128, 256, 1),
            nn.LeakyReLU(0.2),
            nn.AdaptiveMaxPool1d(1),
            nn.Flatten(),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )
        
        print(f"🎨 生成对抗网络: 生成{num_points}个点的点云")
    
    def generate(self, batch_size):
        """生成新的点云"""
        z = torch.randn(batch_size, self.latent_dim)
        generated = self.generator(z)
        return generated.view(batch_size, self.num_points, 3)

# ============================================================================
# 方法5: 对比学习 (Contrastive Learning)
# ============================================================================

class ContrastiveLearning(nn.Module):
    """对比学习"""
    
    def __init__(self, feature_dim=256, temperature=0.1):
        super(ContrastiveLearning, self).__init__()
        
        self.temperature = temperature
        
        self.encoder = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.AdaptiveMaxPool1d(1),
            nn.Flatten()
        )
        
        # 投影头
        self.projection = nn.Sequential(
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, feature_dim)
        )
        
        print(f"🔗 对比学习: 特征维度{feature_dim}, 温度{temperature}")
    
    def forward(self, x1, x2):
        """
        x1, x2: 同一点云的两个增强版本
        """
        # 编码
        h1 = self.encoder(x1.transpose(2, 1))
        h2 = self.encoder(x2.transpose(2, 1))
        
        # 投影
        z1 = F.normalize(self.projection(h1), dim=1)
        z2 = F.normalize(self.projection(h2), dim=1)
        
        return z1, z2
    
    def contrastive_loss(self, z1, z2):
        """对比损失"""
        batch_size = z1.shape[0]
        
        # 计算相似度矩阵
        sim_matrix = torch.mm(z1, z2.t()) / self.temperature
        
        # 正样本在对角线上
        labels = torch.arange(batch_size).to(z1.device)
        
        # 对比损失
        loss = F.cross_entropy(sim_matrix, labels)
        
        return loss

# ============================================================================
# 方法6: 元学习 (Meta-Learning)
# ============================================================================

class MAML(nn.Module):
    """模型无关元学习"""
    
    def __init__(self, num_keypoints=12, meta_lr=0.001, inner_lr=0.01):
        super(MAML, self).__init__()
        
        self.meta_lr = meta_lr
        self.inner_lr = inner_lr
        
        self.model = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.AdaptiveMaxPool1d(1),
            nn.Flatten(),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, num_keypoints * 3)
        )
        
        print(f"🧠 元学习: MAML, 元学习率{meta_lr}, 内部学习率{inner_lr}")
    
    def forward(self, x):
        x = x.transpose(2, 1)
        output = self.model(x)
        return output.view(-1, 12, 3)
    
    def inner_update(self, support_x, support_y, num_steps=5):
        """内部循环更新"""
        # 克隆参数
        fast_weights = {}
        for name, param in self.named_parameters():
            fast_weights[name] = param.clone()
        
        # 内部更新步骤
        for step in range(num_steps):
            pred = self.forward(support_x)
            loss = F.mse_loss(pred, support_y)
            
            # 计算梯度
            grads = torch.autograd.grad(loss, self.parameters(), create_graph=True)
            
            # 更新快速权重
            for (name, param), grad in zip(self.named_parameters(), grads):
                fast_weights[name] = param - self.inner_lr * grad
        
        return fast_weights

# ============================================================================
# 方法7: 知识蒸馏 (Knowledge Distillation)
# ============================================================================

class KnowledgeDistillation(nn.Module):
    """知识蒸馏"""
    
    def __init__(self, teacher_model, student_model, temperature=4.0, alpha=0.7):
        super(KnowledgeDistillation, self).__init__()
        
        self.teacher = teacher_model
        self.student = student_model
        self.temperature = temperature
        self.alpha = alpha
        
        # 冻结教师模型
        for param in self.teacher.parameters():
            param.requires_grad = False
        
        print(f"👨‍🏫 知识蒸馏: 温度{temperature}, 权重{alpha}")
    
    def forward(self, x, targets=None):
        student_output = self.student(x)
        
        if targets is not None:
            with torch.no_grad():
                teacher_output = self.teacher(x)
            
            # 蒸馏损失
            distill_loss = F.mse_loss(
                student_output / self.temperature,
                teacher_output / self.temperature
            )
            
            # 任务损失
            task_loss = F.mse_loss(student_output, targets)
            
            # 总损失
            total_loss = self.alpha * distill_loss + (1 - self.alpha) * task_loss
            
            return student_output, total_loss
        
        return student_output

# ============================================================================
# 方法8: 数据合成 (Data Synthesis)
# ============================================================================

class DataSynthesizer:
    """数据合成器"""
    
    def __init__(self, original_data):
        self.original_data = original_data
        print("🔬 数据合成: 基于原始数据生成新样本")
    
    def mixup(self, x1, x2, alpha=0.2):
        """Mixup数据增强"""
        lam = np.random.beta(alpha, alpha)
        mixed = lam * x1 + (1 - lam) * x2
        return mixed, lam
    
    def cutmix(self, x1, x2, alpha=1.0):
        """CutMix数据增强"""
        lam = np.random.beta(alpha, alpha)
        
        # 随机选择要替换的点
        num_points = x1.shape[1]
        num_cut = int(num_points * (1 - lam))
        
        cut_indices = np.random.choice(num_points, num_cut, replace=False)
        
        mixed = x1.clone()
        mixed[:, cut_indices] = x2[:, cut_indices]
        
        return mixed, lam
    
    def gaussian_noise_augmentation(self, x, noise_std=0.01):
        """高斯噪声增强"""
        noise = torch.randn_like(x) * noise_std
        return x + noise
    
    def rotation_augmentation(self, x, max_angle=0.1):
        """旋转增强"""
        angle = np.random.uniform(-max_angle, max_angle)
        cos_a, sin_a = np.cos(angle), np.sin(angle)
        rotation_matrix = torch.tensor([
            [cos_a, -sin_a, 0],
            [sin_a, cos_a, 0],
            [0, 0, 1]
        ], dtype=x.dtype, device=x.device)
        
        return torch.matmul(x, rotation_matrix.T)

# ============================================================================
# 方法9: 主动学习 (Active Learning)
# ============================================================================

class ActiveLearning:
    """主动学习"""
    
    def __init__(self, model, uncertainty_method='entropy'):
        self.model = model
        self.uncertainty_method = uncertainty_method
        print(f"🎯 主动学习: 不确定性方法 - {uncertainty_method}")
    
    def uncertainty_sampling(self, unlabeled_data, n_samples=10):
        """基于不确定性的采样"""
        self.model.eval()
        uncertainties = []
        
        with torch.no_grad():
            for data in unlabeled_data:
                # 多次前向传播 (Monte Carlo Dropout)
                predictions = []
                for _ in range(10):
                    pred = self.model(data.unsqueeze(0))
                    predictions.append(pred)
                
                predictions = torch.stack(predictions)
                
                if self.uncertainty_method == 'variance':
                    uncertainty = torch.var(predictions, dim=0).mean()
                elif self.uncertainty_method == 'entropy':
                    mean_pred = torch.mean(predictions, dim=0)
                    uncertainty = -torch.sum(mean_pred * torch.log(mean_pred + 1e-8))
                
                uncertainties.append(uncertainty.item())
        
        # 选择最不确定的样本
        uncertain_indices = np.argsort(uncertainties)[-n_samples:]
        
        return uncertain_indices

# ============================================================================
# 方法10: 集成学习 (Ensemble Learning)
# ============================================================================

class EnsembleLearning:
    """集成学习"""
    
    def __init__(self, models, weights=None):
        self.models = models
        self.weights = weights or [1.0 / len(models)] * len(models)
        print(f"🎭 集成学习: {len(models)}个模型")
    
    def predict(self, x):
        """集成预测"""
        predictions = []
        
        for model in self.models:
            model.eval()
            with torch.no_grad():
                pred = model(x)
                predictions.append(pred)
        
        # 加权平均
        ensemble_pred = torch.zeros_like(predictions[0])
        for pred, weight in zip(predictions, self.weights):
            ensemble_pred += weight * pred
        
        return ensemble_pred
    
    def uncertainty_estimation(self, x):
        """不确定性估计"""
        predictions = []
        
        for model in self.models:
            model.eval()
            with torch.no_grad():
                pred = model(x)
                predictions.append(pred)
        
        predictions = torch.stack(predictions)
        
        # 计算方差作为不确定性
        uncertainty = torch.var(predictions, dim=0)
        
        return torch.mean(predictions, dim=0), uncertainty

def create_comprehensive_solution():
    """创建综合解决方案"""
    
    print("🚀 **小数据集深度学习综合解决方案**")
    print("📚 **基于最新研究的10大技术**")
    print("🎯 **针对97样本医疗点云数据集**")
    print("=" * 80)
    
    solutions = {
        "1. 迁移学习": "使用预训练模型，冻结特征提取器",
        "2. 自监督学习": "掩码自编码器预训练",
        "3. 少样本学习": "原型网络，支持集-查询集",
        "4. 生成对抗网络": "生成新的训练样本",
        "5. 对比学习": "学习数据表示",
        "6. 元学习": "学会快速适应新任务",
        "7. 知识蒸馏": "从大模型学习知识",
        "8. 数据合成": "Mixup, CutMix等增强",
        "9. 主动学习": "智能选择标注样本",
        "10. 集成学习": "多模型组合预测"
    }
    
    print("\n📋 **可用解决方案:**")
    for key, value in solutions.items():
        print(f"   {key}: {value}")
    
    print(f"\n💡 **针对我们数据集的建议:**")
    print(f"   🥇 优先级1: 迁移学习 + 数据增强")
    print(f"   🥈 优先级2: 自监督预训练 + 少样本学习")
    print(f"   🥉 优先级3: 集成学习 + 主动学习")
    
    print(f"\n🎯 **实施策略:**")
    print(f"   1. 使用预训练PointNet作为特征提取器")
    print(f"   2. 应用强数据增强 (旋转、噪声、Mixup)")
    print(f"   3. 自监督预训练增加数据利用率")
    print(f"   4. 集成多个简单模型而非单个复杂模型")
    print(f"   5. 使用交叉验证确保结果可靠性")

if __name__ == "__main__":
    create_comprehensive_solution()
