[{"stage": 1, "config": {"num_points": 2048, "batch_size": 2, "complexity": "medium", "max_samples": 50, "epochs": 40}, "test_metrics": {"mean_distance": 7.825608491897583, "within_1mm_percent": 0.0, "within_5mm_percent": 12.5, "within_10mm_percent": 87.5}, "training_time_minutes": 0.23677462339401245}, {"stage": 2, "config": {"num_points": 4096, "batch_size": 2, "complexity": "medium", "max_samples": 50, "epochs": 40}, "test_metrics": {"mean_distance": 7.917630076408386, "within_1mm_percent": 0.0, "within_5mm_percent": 12.5, "within_10mm_percent": 87.5}, "training_time_minutes": 0.15179178714752198}, {"stage": 3, "config": {"num_points": 4096, "batch_size": 4, "complexity": "medium", "max_samples": 50, "epochs": 40}, "test_metrics": {"mean_distance": 6.364511728286743, "within_1mm_percent": 0.0, "within_5mm_percent": 12.5, "within_10mm_percent": 100.0}, "training_time_minutes": 0.09263366460800171}, {"stage": 4, "config": {"num_points": 4096, "batch_size": 4, "complexity": "large", "max_samples": 50, "epochs": 40}, "test_metrics": {"mean_distance": 6.818037033081055, "within_1mm_percent": 0.0, "within_5mm_percent": 12.5, "within_10mm_percent": 87.5}, "training_time_minutes": 0.1736173113187154}]