#!/usr/bin/env python3
"""
测试预处理后的数据效果
Test Preprocessed Data Performance
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import json
from pathlib import Path
from datetime import datetime
from sklearn.model_selection import train_test_split

# 导入之前的救援模型
class RescueKeypointNet(nn.Module):
    """救援版关键点检测网络"""
    
    def __init__(self, input_dim=3, hidden_dim=256, output_dim=19*3, dropout=0.3):
        super().__init__()
        
        # 多尺度特征提取
        self.conv1 = nn.Conv1d(input_dim, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, hidden_dim, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(hidden_dim)
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(hidden_dim, num_heads=8, batch_first=True)
        
        # 残差连接
        self.residual_conv = nn.Conv1d(input_dim, hidden_dim, 1)
        
        # 关键点回归器
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(hidden_dim, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            
            nn.Linear(128, output_dim)
        )
        
    def forward(self, point_cloud):
        batch_size = point_cloud.size(0)
        x = point_cloud.transpose(2, 1)  # (B, 3, N)
        
        # 多尺度特征提取
        x1 = F.relu(self.bn1(self.conv1(x)))
        x2 = F.relu(self.bn2(self.conv2(x1)))
        x3 = F.relu(self.bn3(self.conv3(x2)))
        x4 = F.relu(self.bn4(self.conv4(x3)))
        
        # 残差连接
        residual = self.residual_conv(point_cloud.transpose(2, 1))
        x4 = x4 + residual
        
        # 全局特征
        global_feature = torch.max(x4, 2)[0]  # (B, hidden_dim)
        
        # 自注意力
        global_feature_expanded = global_feature.unsqueeze(1)  # (B, 1, hidden_dim)
        attended_feature, _ = self.attention(global_feature_expanded, global_feature_expanded, global_feature_expanded)
        attended_feature = attended_feature.squeeze(1)  # (B, hidden_dim)
        
        # 预测关键点
        keypoints = self.keypoint_regressor(attended_feature)
        return keypoints.view(batch_size, 19, 3)

class PreprocessedDataTester:
    """预处理数据测试器"""
    
    def __init__(self, preprocessed_data_path, device='cuda'):
        self.preprocessed_data_path = preprocessed_data_path
        self.device = device
        self.load_preprocessed_data()
        
    def load_preprocessed_data(self):
        """加载预处理后的数据"""
        print(f"📦 加载预处理数据: {self.preprocessed_data_path}")
        
        data = np.load(self.preprocessed_data_path, allow_pickle=True)
        self.sample_ids = data['sample_ids']
        self.point_clouds = data['point_clouds']
        self.keypoints = data['keypoints']
        self.original_point_clouds = data['original_point_clouds']
        self.original_keypoints = data['original_keypoints']
        
        print(f"✅ 预处理数据加载完成: {len(self.sample_ids)} 样本")
        print(f"   预处理后点云形状: {self.point_clouds.shape}")
        print(f"   预处理后关键点形状: {self.keypoints.shape}")
        
        # 数据划分
        indices = np.arange(len(self.sample_ids))
        train_val_indices, test_indices = train_test_split(
            indices, test_size=0.15, random_state=42
        )
        train_indices, val_indices = train_test_split(
            train_val_indices, test_size=0.18, random_state=42
        )
        
        self.data_splits = {
            'train': {
                'point_clouds': self.point_clouds[train_indices],
                'keypoints': self.keypoints[train_indices]
            },
            'val': {
                'point_clouds': self.point_clouds[val_indices],
                'keypoints': self.keypoints[val_indices]
            },
            'test': {
                'point_clouds': self.point_clouds[test_indices],
                'keypoints': self.keypoints[test_indices]
            }
        }
        
        print(f"✅ 数据划分完成: 训练{len(train_indices)}, 验证{len(val_indices)}, 测试{len(test_indices)}")
        
    def train_model_on_preprocessed_data(self, k_shot=20, epochs=100, lr=0.001):
        """在预处理数据上训练模型"""
        print(f"\n🎯 在预处理数据上训练 {k_shot}-shot 模型")
        
        # 创建模型
        model = RescueKeypointNet().to(self.device)
        optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-4)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        patience = 0
        max_patience = 30
        
        for epoch in range(epochs):
            model.train()
            
            # 采样训练数据
            train_indices = np.random.choice(
                len(self.data_splits['train']['point_clouds']), 
                min(k_shot, len(self.data_splits['train']['point_clouds'])), 
                replace=False
            )
            
            train_pcs = self.data_splits['train']['point_clouds'][train_indices]
            train_kps = self.data_splits['train']['keypoints'][train_indices]
            
            # 简单数据增强
            aug_pcs = []
            aug_kps = []
            
            for pc, kp in zip(train_pcs, train_kps):
                # 原始数据
                aug_pcs.append(pc)
                aug_kps.append(kp)
                
                # 轻微旋转
                for _ in range(3):
                    angle = np.random.uniform(-0.05, 0.05)  # ±3度
                    cos_a, sin_a = np.cos(angle), np.sin(angle)
                    rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                    
                    aug_pc = pc @ rotation.T
                    aug_kp = kp @ rotation.T
                    aug_pcs.append(aug_pc)
                    aug_kps.append(aug_kp)
                
                # 轻微噪声
                noise_pc = pc + np.random.normal(0, 0.01, pc.shape)
                aug_pcs.append(noise_pc)
                aug_kps.append(kp)
            
            # 确保数据类型正确并转换为tensor
            aug_pcs_array = []
            aug_kps_array = []

            for pc, kp in zip(aug_pcs, aug_kps):
                aug_pcs_array.append(np.array(pc, dtype=np.float32))
                aug_kps_array.append(np.array(kp, dtype=np.float32))

            aug_pcs = torch.FloatTensor(np.stack(aug_pcs_array)).to(self.device)
            aug_kps = torch.FloatTensor(np.stack(aug_kps_array)).to(self.device)
            
            # 训练步骤
            optimizer.zero_grad()
            pred_kps = model(aug_pcs)
            loss = criterion(pred_kps, aug_kps)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            scheduler.step()
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_model(model, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                    patience = 0
                else:
                    patience += 1
                
                print(f"Epoch {epoch:3d}: Loss={loss:.4f}, Val_Error={val_error:.3f}, LR={optimizer.param_groups[0]['lr']:.6f}")
                
                if patience >= max_patience:
                    print(f"早停在epoch {epoch}")
                    break
        
        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def evaluate_model(self, model, split='test'):
        """评估模型 - 在标准化空间中"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data_splits[split]['point_clouds']
            kps = self.data_splits[split]['keypoints']
            
            for pc, kp in zip(pcs, kps):
                pc_array = np.array(pc, dtype=np.float32)
                kp_array = np.array(kp, dtype=np.float32)

                pc_tensor = torch.FloatTensor(pc_array).unsqueeze(0).to(self.device)
                kp_tensor = torch.FloatTensor(kp_array).unsqueeze(0).to(self.device)
                
                pred_kp = model(pc_tensor)
                error = torch.mean(torch.norm(pred_kp - kp_tensor, dim=2))
                total_error += error.item()
                num_samples += 1
        
        return total_error / num_samples if num_samples > 0 else float('inf')
    
    def compare_with_baseline(self, baseline_error=9.12):
        """与基线对比"""
        print(f"\n📊 与基线对比测试")
        print("=" * 50)
        
        # 测试不同shot配置
        shot_configs = [10, 15, 20, 25]
        results = {}
        
        for k_shot in shot_configs:
            print(f"\n🎯 测试 {k_shot}-shot...")
            
            # 训练模型
            model, val_error = self.train_model_on_preprocessed_data(k_shot, epochs=80, lr=0.001)
            
            # 测试评估
            test_error = self.evaluate_model(model, 'test')
            results[k_shot] = test_error
            
            # 计算改进
            improvement = (baseline_error - test_error) / baseline_error * 100
            
            print(f"✅ {k_shot}-shot 结果: {test_error:.3f} (改进: {improvement:+.1f}%)")
        
        # 找出最佳结果
        best_shot = min(results.keys(), key=lambda k: results[k])
        best_error = results[best_shot]
        total_improvement = (baseline_error - best_error) / baseline_error * 100
        
        print(f"\n📊 预处理效果总结:")
        print(f"   基线误差: {baseline_error:.2f}")
        print(f"   最佳结果: {best_error:.3f} ({best_shot}-shot)")
        print(f"   总体改进: {total_improvement:+.1f}%")
        
        if total_improvement > 5:
            print("🎉 预处理效果显著！")
        elif total_improvement > 0:
            print("👍 预处理有一定效果")
        else:
            print("⚠️ 预处理效果不明显，可能需要调整策略")
        
        return results, best_error, total_improvement

def main():
    """主函数"""
    print("🧪 测试预处理数据效果")
    print("=" * 60)
    
    # 查找最新的预处理数据
    processed_dir = Path("data/processed")
    if not processed_dir.exists():
        print("❌ 未找到预处理数据目录")
        return
    
    # 找到最新的预处理文件
    preprocessed_files = list(processed_dir.glob("lightweight_preprocessed_*.npz"))
    if not preprocessed_files:
        print("❌ 未找到预处理数据文件")
        return
    
    latest_file = max(preprocessed_files, key=lambda x: x.stat().st_mtime)
    print(f"📁 使用预处理数据: {latest_file}")
    
    # 创建测试器
    tester = PreprocessedDataTester(str(latest_file))
    
    # 运行对比测试
    results, best_error, improvement = tester.compare_with_baseline()
    
    # 保存测试结果
    test_results = {
        "test_timestamp": datetime.now().isoformat(),
        "preprocessed_data_file": str(latest_file),
        "baseline_error": 9.12,
        "best_error": best_error,
        "total_improvement_percent": improvement,
        "detailed_results": results,
        "conclusion": "显著改进" if improvement > 5 else "有效改进" if improvement > 0 else "效果不明显"
    }
    
    # 保存结果
    results_dir = Path("results/preprocessing_tests")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = results_dir / f"preprocessing_test_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(test_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 测试结果已保存: {results_file}")
    
    # 给出下一步建议
    print(f"\n🎯 下一步建议:")
    if improvement > 5:
        print("1. 预处理效果很好，可以继续进行模型架构优化")
        print("2. 尝试Point Transformer等更先进的架构")
        print("3. 探索图神经网络方法")
    elif improvement > 0:
        print("1. 预处理有一定效果，可以尝试其他预处理方法")
        print("2. 考虑更强的数据增强策略")
        print("3. 同时进行模型架构改进")
    else:
        print("1. 当前预处理策略效果有限")
        print("2. 建议尝试其他预处理方法或直接进行模型优化")
        print("3. 重点关注数据增强和架构创新")
    
    return test_results

if __name__ == "__main__":
    results = main()
