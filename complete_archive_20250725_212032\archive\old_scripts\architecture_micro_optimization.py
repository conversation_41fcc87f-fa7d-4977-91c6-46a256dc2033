#!/usr/bin/env python3
"""
架构微调优化 - 第2周实施
基于超参数优化的洞察，专注于架构层面的改进
目标: 突破5.857mm，达到5.5-5.7mm
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import time
import json
import random
from sklearn.model_selection import KFold
import copy

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class ResidualMedicalPointNet(nn.Module):
    """带残差连接的医疗PointNet"""
    
    def __init__(self, num_keypoints=12, statistical_baseline=None, 
                 feature_dims=[28, 56, 112], activation='swish'):
        super(ResidualMedicalPointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        self.statistical_baseline = statistical_baseline
        
        # 优化的特征维度 (黄金比例)
        self.conv1 = nn.Conv1d(3, feature_dims[0], 1)
        self.conv2 = nn.Conv1d(feature_dims[0], feature_dims[1], 1)
        self.conv3 = nn.Conv1d(feature_dims[1], feature_dims[2], 1)
        
        # LayerNorm替代BatchNorm (更适合小批次)
        self.ln1 = nn.LayerNorm(feature_dims[0])
        self.ln2 = nn.LayerNorm(feature_dims[1])
        self.ln3 = nn.LayerNorm(feature_dims[2])
        
        # 轻量级残差连接
        self.residual1 = nn.Conv1d(feature_dims[0], feature_dims[1], 1)  # skip connection
        self.residual2 = nn.Conv1d(feature_dims[0], feature_dims[2], 1)  # long skip
        
        # 可学习的残差权重
        self.res_weight1 = nn.Parameter(torch.tensor(0.1))
        self.res_weight2 = nn.Parameter(torch.tensor(0.05))
        
        # 激活函数选择
        if activation == 'swish':
            self.activation = nn.SiLU()  # Swish
        elif activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'mish':
            self.activation = nn.Mish()
        else:
            self.activation = nn.ReLU()
        
        # 自适应全局池化
        self.global_pool = nn.AdaptiveMaxPool1d(1)
        
        # 改进的预测头
        self.fc1 = nn.Linear(feature_dims[2], feature_dims[2]//2)
        self.fc2 = nn.Linear(feature_dims[2]//2, feature_dims[2]//4)
        self.fc3 = nn.Linear(feature_dims[2]//4, num_keypoints * 3)
        
        # 自适应dropout
        self.dropout1 = nn.Dropout(0.25)
        self.dropout2 = nn.Dropout(0.35)
        
        # 统计先验权重 (从优化结果初始化)
        self.alpha = nn.Parameter(torch.tensor(0.58))
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"🏗️ 残差医疗PointNet: {total_params:,}参数")
        print(f"   特征维度: {feature_dims}")
        print(f"   激活函数: {activation}")
        print(f"   归一化: LayerNorm")
    
    def forward(self, x):
        batch_size = x.shape[0]
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 第一层
        x1 = self.conv1(x)  # [B, 28, N]
        x1 = x1.transpose(2, 1)  # [B, N, 28] for LayerNorm
        x1 = self.ln1(x1)
        x1 = x1.transpose(2, 1)  # [B, 28, N]
        x1 = self.activation(x1)
        
        # 第二层 + 残差连接
        x2 = self.conv2(x1)  # [B, 56, N]
        x2 = x2.transpose(2, 1)
        x2 = self.ln2(x2)
        x2 = x2.transpose(2, 1)
        
        # 残差连接1
        residual1 = self.residual1(x1)  # [B, 56, N]
        x2 = x2 + self.res_weight1 * residual1
        x2 = self.activation(x2)
        
        # 第三层 + 长距离残差连接
        x3 = self.conv3(x2)  # [B, 112, N]
        x3 = x3.transpose(2, 1)
        x3 = self.ln3(x3)
        x3 = x3.transpose(2, 1)
        
        # 长距离残差连接
        residual2 = self.residual2(x1)  # [B, 112, N]
        x3 = x3 + self.res_weight2 * residual2
        x3 = self.activation(x3)
        
        # 全局特征
        global_feat = self.global_pool(x3).squeeze(-1)  # [B, 112]
        
        # 改进的预测头
        x = self.activation(self.fc1(global_feat))
        x = self.dropout1(x)
        x = self.activation(self.fc2(x))
        x = self.dropout2(x)
        delta = self.fc3(x)
        
        delta = delta.view(-1, self.num_keypoints, 3)
        
        # 统计先验集成
        if self.statistical_baseline is not None:
            baseline = torch.tensor(self.statistical_baseline, 
                                  dtype=delta.dtype, device=delta.device)
            baseline = baseline.unsqueeze(0).expand(delta.shape[0], -1, -1)
            alpha = torch.sigmoid(self.alpha)
            output = alpha * baseline + (1 - alpha) * (baseline + delta)
            return output
        
        return delta

class AttentionMedicalPointNet(nn.Module):
    """带注意力机制的医疗PointNet"""
    
    def __init__(self, num_keypoints=12, statistical_baseline=None):
        super(AttentionMedicalPointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        self.statistical_baseline = statistical_baseline
        
        # 特征提取
        self.conv1 = nn.Conv1d(3, 32, 1)
        self.conv2 = nn.Conv1d(32, 64, 1)
        self.conv3 = nn.Conv1d(64, 128, 1)
        
        self.bn1 = nn.BatchNorm1d(32)
        self.bn2 = nn.BatchNorm1d(64)
        self.bn3 = nn.BatchNorm1d(128)
        
        # 轻量级自注意力
        self.attention_conv = nn.Conv1d(128, 64, 1)
        self.attention_score = nn.Conv1d(64, 1, 1)
        
        # 预测头
        self.fc1 = nn.Linear(128, 64)
        self.fc2 = nn.Linear(64, num_keypoints * 3)
        self.dropout = nn.Dropout(0.3)
        
        self.alpha = nn.Parameter(torch.tensor(0.58))
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"🎯 注意力医疗PointNet: {total_params:,}参数")
    
    def forward(self, x):
        x = x.transpose(2, 1)
        
        # 特征提取
        x1 = torch.relu(self.bn1(self.conv1(x)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))  # [B, 128, N]
        
        # 自注意力机制
        att_feat = torch.relu(self.attention_conv(x3))  # [B, 64, N]
        att_scores = torch.softmax(self.attention_score(att_feat), dim=2)  # [B, 1, N]
        
        # 注意力加权的全局特征
        weighted_feat = x3 * att_scores  # [B, 128, N]
        global_feat = torch.sum(weighted_feat, dim=2)  # [B, 128]
        
        # 预测
        x = torch.relu(self.fc1(global_feat))
        x = self.dropout(x)
        delta = self.fc2(x)
        delta = delta.view(-1, self.num_keypoints, 3)
        
        # 统计先验集成
        if self.statistical_baseline is not None:
            baseline = torch.tensor(self.statistical_baseline, 
                                  dtype=delta.dtype, device=delta.device)
            baseline = baseline.unsqueeze(0).expand(delta.shape[0], -1, -1)
            alpha = torch.sigmoid(self.alpha)
            output = alpha * baseline + (1 - alpha) * (baseline + delta)
            return output
        
        return delta

class ArchitectureOptimizer:
    """架构优化器"""
    
    def __init__(self, device='cuda:1'):
        self.device = device
        self.results = []
        print("🏗️ 架构微调优化器初始化")
    
    def calculate_statistical_baseline(self, train_data):
        """计算统计基线"""
        all_keypoints = []
        for sample in train_data:
            if isinstance(sample, dict):
                kp = sample['keypoints'].numpy()
            else:
                kp = sample[1]
            all_keypoints.append(kp)
        
        all_keypoints = np.array(all_keypoints)
        baseline = np.mean(all_keypoints, axis=0)
        return baseline
    
    def conservative_augment(self, point_cloud, keypoints):
        """保守数据增强"""
        pc = point_cloud.copy()
        kp = keypoints.copy()
        
        if np.random.random() < 0.6:
            angle = np.random.uniform(-0.035, 0.035)
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
            pc = pc @ rotation.T
            kp = kp @ rotation.T
        
        if np.random.random() < 0.5:
            scale = np.random.uniform(0.99, 1.01)
            pc *= scale
            kp *= scale
        
        if np.random.random() < 0.4:
            translation = np.random.uniform(-0.1, 0.1, 3)
            pc += translation
            kp += translation
        
        if np.random.random() < 0.3:
            noise = np.random.normal(0, 0.005, pc.shape)
            pc += noise
        
        return pc, kp
    
    def test_architecture_variants(self):
        """测试不同架构变体"""
        
        print("\n🏗️ **架构微调优化测试**")
        print("🎯 **目标: 突破5.857mm基线**")
        print("=" * 60)
        
        # 定义测试的架构变体
        architectures = [
            {
                'name': '黄金比例维度 + Swish',
                'model_class': ResidualMedicalPointNet,
                'params': {'feature_dims': [28, 56, 112], 'activation': 'swish'}
            },
            {
                'name': '黄金比例维度 + GELU',
                'model_class': ResidualMedicalPointNet,
                'params': {'feature_dims': [28, 56, 112], 'activation': 'gelu'}
            },
            {
                'name': '紧凑维度 + Mish',
                'model_class': ResidualMedicalPointNet,
                'params': {'feature_dims': [24, 48, 96], 'activation': 'mish'}
            },
            {
                'name': '注意力机制',
                'model_class': AttentionMedicalPointNet,
                'params': {}
            },
            {
                'name': '原始最佳配置',
                'model_class': ResidualMedicalPointNet,
                'params': {'feature_dims': [32, 64, 128], 'activation': 'relu'}
            }
        ]
        
        # 加载数据
        data = np.load('f3_reduced_12kp_stable.npz', allow_pickle=True)
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        # 排除测试集
        test_samples = ['600114', '600115', '600116', '600117', '600118', 
                       '600119', '600120', '600121', '600122', '600123',
                       '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        train_samples = [(point_clouds[i], keypoints[i], sample_ids[i]) 
                        for i in range(len(sample_ids)) if train_val_mask[i]]
        
        # 测试每个架构
        for i, arch_config in enumerate(architectures):
            print(f"\n📈 测试架构 {i+1}/{len(architectures)}: {arch_config['name']}")
            
            result = self.test_single_architecture(
                train_samples, 
                arch_config['model_class'], 
                arch_config['params'],
                arch_config['name']
            )
            
            self.results.append({
                'name': arch_config['name'],
                'performance': result,
                'params': arch_config['params']
            })
            
            print(f"   结果: {result:.3f}mm")
        
        return self.results
    
    def test_single_architecture(self, train_samples, model_class, model_params, arch_name):
        """测试单个架构"""
        
        # 3折快速验证
        kfold = KFold(n_splits=3, shuffle=True, random_state=42)
        fold_results = []
        
        for fold, (train_idx, val_idx) in enumerate(kfold.split(range(len(train_samples)))):
            
            # 分割数据
            fold_train = [train_samples[i] for i in train_idx]
            fold_val = [train_samples[i] for i in val_idx]
            
            # 计算统计基线
            statistical_baseline = self.calculate_statistical_baseline(fold_train)
            
            # 数据增强
            augmented_train = []
            for pc, kp, sid in fold_train:
                augmented_train.append((pc, kp, sid))
                aug_pc, aug_kp = self.conservative_augment(pc, kp)
                augmented_train.append((aug_pc, aug_kp, f"{sid}_aug"))
            
            # 创建模型
            model = model_class(
                num_keypoints=12,
                statistical_baseline=statistical_baseline,
                **model_params
            )
            model.to(self.device)
            
            # 训练
            fold_error = self.train_single_fold(model, augmented_train, fold_val)
            fold_results.append(fold_error)
        
        return np.mean(fold_results)
    
    def train_single_fold(self, model, train_data, val_data, epochs=35):
        """训练单折"""
        
        # 使用最佳超参数
        optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=5e-4)
        scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=10)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        patience = 10
        patience_counter = 0
        
        for epoch in range(epochs):
            # 训练
            model.train()
            train_loss = 0.0
            
            batch_size = 4
            for i in range(0, len(train_data), batch_size):
                batch = train_data[i:i+batch_size]
                
                pc_list = []
                kp_list = []
                
                for pc, kp, _ in batch:
                    if len(pc) > 2048:
                        indices = np.random.choice(len(pc), 2048, replace=False)
                        pc = pc[indices]
                    
                    pc_list.append(torch.FloatTensor(pc))
                    kp_list.append(torch.FloatTensor(kp))
                
                pc_batch = torch.stack(pc_list).to(self.device)
                kp_batch = torch.stack(kp_list).to(self.device)
                
                optimizer.zero_grad()
                pred = model(pc_batch)
                loss = criterion(pred, kp_batch)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
                optimizer.step()
                
                train_loss += loss.item()
            
            # 验证
            model.eval()
            val_errors = []
            with torch.no_grad():
                for pc, kp, _ in val_data:
                    if len(pc) > 2048:
                        indices = np.random.choice(len(pc), 2048, replace=False)
                        pc = pc[indices]
                    
                    pc_tensor = torch.FloatTensor(pc).unsqueeze(0).to(self.device)
                    kp_tensor = torch.FloatTensor(kp).unsqueeze(0).to(self.device)
                    
                    pred = model(pc_tensor)
                    error = torch.norm(pred - kp_tensor, dim=2).mean().item()
                    val_errors.append(error)
            
            val_error = np.mean(val_errors)
            scheduler.step()
            
            if val_error < best_val_error:
                best_val_error = val_error
                patience_counter = 0
            else:
                patience_counter += 1
            
            if patience_counter >= patience:
                break
        
        return best_val_error
    
    def save_results(self):
        """保存结果"""
        
        # 排序结果
        sorted_results = sorted(self.results, key=lambda x: x['performance'])
        
        results_data = {
            'best_architecture': sorted_results[0],
            'all_results': sorted_results,
            'baseline_performance': 5.857,
            'improvement': (5.857 - sorted_results[0]['performance']) / 5.857 * 100 if sorted_results[0]['performance'] < 5.857 else 0
        }
        
        with open('architecture_optimization_results.json', 'w') as f:
            json.dump(results_data, f, indent=2)
        
        print(f"\n📊 **架构优化结果**:")
        print(f"   最佳架构: {sorted_results[0]['name']}")
        print(f"   最佳性能: {sorted_results[0]['performance']:.3f}mm")
        print(f"   vs基线: 5.857mm")
        
        if sorted_results[0]['performance'] < 5.857:
            improvement = (5.857 - sorted_results[0]['performance']) / 5.857 * 100
            print(f"   🎉 提升: {improvement:.1f}%")
        
        print(f"\n📋 **所有架构结果**:")
        for result in sorted_results:
            print(f"   {result['name']}: {result['performance']:.3f}mm")

def main():
    """主函数 - 架构微调优化"""
    
    print("🏗️ **架构微调优化 - 第2周实施**")
    print("🎯 **目标: 突破5.857mm，达到5.5-5.7mm**")
    print("🔧 **策略: 残差连接+新激活函数+注意力机制**")
    print("=" * 80)
    
    set_seed(42)
    
    # 创建优化器
    optimizer = ArchitectureOptimizer(device='cuda:1')
    
    # 测试架构变体
    start_time = time.time()
    results = optimizer.test_architecture_variants()
    optimization_time = time.time() - start_time
    
    # 保存结果
    optimizer.save_results()
    
    print(f"\n🎉 **架构优化完成!**")
    print(f"⏱️  优化时间: {optimization_time/60:.1f}分钟")
    
    # 找到最佳架构
    best_result = min(results, key=lambda x: x['performance'])
    
    print(f"\n🏆 **最佳架构**:")
    print(f"   名称: {best_result['name']}")
    print(f"   性能: {best_result['performance']:.3f}mm")
    print(f"   参数: {best_result['params']}")
    
    if best_result['performance'] < 5.857:
        improvement = (5.857 - best_result['performance']) / 5.857 * 100
        print(f"   🎉 成功提升: {improvement:.1f}%")
        
        if best_result['performance'] < 5.7:
            print(f"   🏆 达到第2周目标!")
        else:
            print(f"   💡 接近目标，可进入第3周高级技术")
    else:
        print(f"   💡 需要尝试第3周的高级优化技术")
    
    print(f"\n🚀 **下一步建议**:")
    if best_result['performance'] < 5.7:
        print(f"   ✅ 第2周目标达成，开始第3周高级技术")
    else:
        print(f"   🔧 继续架构优化或开始高级技术实验")

if __name__ == "__main__":
    main()
