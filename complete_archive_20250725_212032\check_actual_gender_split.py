#!/usr/bin/env python3
"""
检查实际的男女分离数据集数量
Check Actual Gender Split Dataset Numbers
"""

import numpy as np
import os

def check_gender_datasets():
    """检查男女数据集的实际数量"""
    
    print("🔍 检查实际的男女分离数据集数量")
    print("=" * 50)
    
    # 检查存档中的数据集
    female_path = "archive/old_experiments/f3_reduced_12kp_female.npz"
    male_path = "archive/old_experiments/f3_reduced_12kp_male.npz"
    
    if os.path.exists(female_path):
        female_data = np.load(female_path, allow_pickle=True)
        female_count = len(female_data['sample_ids'])
        print(f"✅ 女性数据集: {female_count}个样本")
        print(f"   文件: {female_path}")
        print(f"   样本ID示例: {female_data['sample_ids'][:5].tolist()}")
    else:
        print(f"❌ 女性数据集文件不存在: {female_path}")
        female_count = 0
    
    if os.path.exists(male_path):
        male_data = np.load(male_path, allow_pickle=True)
        male_count = len(male_data['sample_ids'])
        print(f"✅ 男性数据集: {male_count}个样本")
        print(f"   文件: {male_path}")
        print(f"   样本ID示例: {male_data['sample_ids'][:5].tolist()}")
    else:
        print(f"❌ 男性数据集文件不存在: {male_path}")
        male_count = 0
    
    # 检查原始数据集
    original_paths = [
        "f3_reduced_12kp_stable.npz",
        "archive/old_experiments/f3_reduced_12kp_stable.npz"
    ]
    
    original_count = 0
    for orig_path in original_paths:
        if os.path.exists(orig_path):
            orig_data = np.load(orig_path, allow_pickle=True)
            original_count = len(orig_data['sample_ids'])
            print(f"✅ 原始数据集: {original_count}个样本")
            print(f"   文件: {orig_path}")
            break
    
    if original_count == 0:
        print("❌ 原始数据集文件未找到")
    
    # 计算比例
    total_split = female_count + male_count
    if total_split > 0:
        female_ratio = female_count / total_split * 100
        male_ratio = male_count / total_split * 100
        
        print(f"\n📊 分离统计:")
        print(f"   原始数据集: {original_count}个样本")
        print(f"   分离后总计: {total_split}个样本")
        print(f"   女性比例: {female_count}个 ({female_ratio:.1f}%)")
        print(f"   男性比例: {male_count}个 ({male_ratio:.1f}%)")
        
        if original_count > 0 and total_split != original_count:
            print(f"   ⚠️  分离后样本数与原始不符: {total_split} vs {original_count}")
    
    return female_count, male_count, original_count

def check_other_datasets():
    """检查其他可能的数据集"""
    
    print(f"\n🔍 检查其他可能的数据集文件:")
    print("=" * 50)
    
    # 查找所有可能的数据集文件
    import glob
    
    npz_files = glob.glob("**/*.npz", recursive=True)
    
    for file_path in npz_files:
        if any(keyword in file_path.lower() for keyword in ['gender', 'male', 'female', 'split']):
            try:
                data = np.load(file_path, allow_pickle=True)
                if 'sample_ids' in data:
                    count = len(data['sample_ids'])
                    print(f"   {file_path}: {count}个样本")
            except:
                print(f"   {file_path}: 无法读取")

def main():
    """主函数"""
    
    female_count, male_count, original_count = check_gender_datasets()
    check_other_datasets()
    
    print(f"\n💡 结论:")
    if female_count > 0 and male_count > 0:
        print(f"   实际的男女分离比例是: {female_count}:{male_count}")
        if female_count < 20 and male_count > 70:
            print(f"   ✅ 符合您记忆的20多:70多的比例!")
        elif female_count == 12 and male_count == 8:
            print(f"   这是12:8的比例，可能不是您记忆的那个")
        else:
            print(f"   这个比例需要进一步确认")
    else:
        print(f"   未找到完整的男女分离数据集")

if __name__ == "__main__":
    main()
