#!/usr/bin/env python3
"""
Training Diagnostics Analyzer for F3 Keypoint Detection

深入分析训练过程的充分性、速度合理性和质量
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import torch
import time
from pathlib import Path

def analyze_training_convergence(results_file):
    """分析训练收敛性"""
    
    print("🔍 **训练收敛性分析**")
    print("=" * 60)
    
    with open(results_file, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    # 提取训练历史
    if 'history' in results:
        history = results['history']
    else:
        print("❌ 未找到训练历史数据")
        return
    
    epochs = [h['epoch'] for h in history]
    train_losses = [h['train_loss'] for h in history]
    val_losses = [h['val_loss'] for h in history]
    train_errors = [h['train_metrics']['mean_distance'] for h in history]
    val_errors = [h['val_metrics']['mean_distance'] for h in history]
    learning_rates = [h['learning_rate'] for h in history]
    
    print(f"📊 训练轮数: {len(epochs)} epochs")
    print(f"📊 最终训练损失: {train_losses[-1]:.4f}")
    print(f"📊 最终验证损失: {val_losses[-1]:.4f}")
    print(f"📊 最终训练误差: {train_errors[-1]:.3f}mm")
    print(f"📊 最终验证误差: {val_errors[-1]:.3f}mm")
    
    # 分析收敛性
    print(f"\n🔍 **收敛性诊断**")
    
    # 1. 损失函数收敛分析
    last_10_train_losses = train_losses[-10:] if len(train_losses) >= 10 else train_losses
    last_10_val_losses = val_losses[-10:] if len(val_losses) >= 10 else val_losses
    
    train_loss_std = np.std(last_10_train_losses)
    val_loss_std = np.std(last_10_val_losses)
    
    print(f"   最后10轮训练损失标准差: {train_loss_std:.4f}")
    print(f"   最后10轮验证损失标准差: {val_loss_std:.4f}")
    
    if train_loss_std < 0.1 and val_loss_std < 0.1:
        print("   ✅ 损失函数已收敛")
    elif train_loss_std < 1.0 and val_loss_std < 1.0:
        print("   ⚠️ 损失函数基本收敛，但可能还有下降空间")
    else:
        print("   ❌ 损失函数未充分收敛")
    
    # 2. 误差收敛分析
    last_10_train_errors = train_errors[-10:] if len(train_errors) >= 10 else train_errors
    last_10_val_errors = val_errors[-10:] if len(val_errors) >= 10 else val_errors
    
    train_error_std = np.std(last_10_train_errors)
    val_error_std = np.std(last_10_val_errors)
    
    print(f"   最后10轮训练误差标准差: {train_error_std:.3f}mm")
    print(f"   最后10轮验证误差标准差: {val_error_std:.3f}mm")
    
    # 3. 过拟合检测
    train_val_gap = train_errors[-1] - val_errors[-1]
    print(f"   训练-验证误差差距: {train_val_gap:.3f}mm")
    
    if abs(train_val_gap) < 0.5:
        print("   ✅ 无明显过拟合")
    elif abs(train_val_gap) < 1.0:
        print("   ⚠️ 轻微过拟合倾向")
    else:
        print("   ❌ 存在过拟合问题")
    
    # 4. 学习率分析
    final_lr = learning_rates[-1]
    initial_lr = learning_rates[0]
    print(f"   初始学习率: {initial_lr:.2e}")
    print(f"   最终学习率: {final_lr:.2e}")
    print(f"   学习率衰减比例: {final_lr/initial_lr:.2e}")
    
    # 5. 早停分析
    best_epoch = np.argmin(val_errors) + 1
    total_epochs = len(epochs)
    epochs_after_best = total_epochs - best_epoch
    
    print(f"   最佳验证误差出现在: 第{best_epoch}轮")
    print(f"   最佳轮次后继续训练: {epochs_after_best}轮")
    
    if epochs_after_best >= 15:
        print("   ✅ 早停机制正常工作")
    elif epochs_after_best >= 10:
        print("   ⚠️ 早停可能过于保守")
    else:
        print("   ❌ 可能过早停止训练")
    
    return {
        'converged': train_loss_std < 0.1 and val_loss_std < 0.1,
        'overfitting': abs(train_val_gap) > 1.0,
        'early_stop_appropriate': epochs_after_best >= 10,
        'best_epoch': best_epoch,
        'total_epochs': total_epochs
    }

def analyze_training_speed(results_file):
    """分析训练速度合理性"""

    print(f"\n🚀 **训练速度分析**")
    print("=" * 60)

    with open(results_file, 'r', encoding='utf-8') as f:
        results = json.load(f)

    training_time = results.get('training_time_minutes', 0)
    total_epochs = len(results.get('history', []))

    print(f"📊 总训练时间: {training_time:.1f}分钟")
    print(f"📊 总训练轮数: {total_epochs}")

    if total_epochs > 0:
        print(f"📊 平均每轮时间: {training_time*60/total_epochs:.1f}秒")

        # 估算合理的训练时间
        train_samples = 68  # 从之前的输出得知
        batch_size = 4
        batches_per_epoch = train_samples // batch_size + (1 if train_samples % batch_size else 0)

        print(f"📊 训练样本数: {train_samples}")
        print(f"📊 批次大小: {batch_size}")
        print(f"📊 每轮批次数: {batches_per_epoch}")
        print(f"📊 平均每批次时间: {training_time*60/(total_epochs*batches_per_epoch):.2f}秒")

        # 速度合理性评估
        avg_batch_time = training_time*60/(total_epochs*batches_per_epoch)

        if avg_batch_time < 0.5:
            print("   ⚠️ 训练速度异常快，可能存在问题")
            print("   建议检查: 数据加载、模型复杂度、GPU利用率")
        elif avg_batch_time < 2.0:
            print("   ✅ 训练速度正常")
        else:
            print("   ⚠️ 训练速度较慢，可能需要优化")

        speed_reasonable = 0.5 <= avg_batch_time <= 2.0
    else:
        print("   ❌ 无训练历史数据，无法分析每轮时间")
        avg_batch_time = 0
        speed_reasonable = False

    return {
        'training_time_minutes': training_time,
        'avg_batch_time_seconds': avg_batch_time,
        'speed_reasonable': speed_reasonable,
        'total_epochs': total_epochs
    }

def benchmark_data_loading_speed():
    """基准测试数据加载速度"""
    
    print(f"\n⏱️ **数据加载速度基准测试**")
    print("=" * 60)
    
    try:
        from train_precomputed_keypoint_sampling_f3 import PrecomputedKeypointF3Dataset
        from torch.utils.data import DataLoader
        
        # 创建数据集
        dataset_path = "high_quality_f3_dataset.npz"
        test_samples = ['600114', '600115', '600116', '600117', '600118']
        
        print("🔄 创建训练数据集...")
        start_time = time.time()
        train_dataset = PrecomputedKeypointF3Dataset(
            dataset_path, 'train', num_points=4096, 
            test_samples=test_samples, augment=True, 
            seed=42, focus_ratio=0.7
        )
        dataset_creation_time = time.time() - start_time
        print(f"   数据集创建时间: {dataset_creation_time:.2f}秒")
        
        # 测试数据加载速度
        batch_size = 4
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
        
        print("🔄 测试数据加载速度...")
        start_time = time.time()
        batch_times = []
        
        for i, batch in enumerate(train_loader):
            batch_start = time.time()
            # 模拟简单的数据处理
            point_cloud = batch['point_cloud']
            keypoints = batch['keypoints']
            batch_end = time.time()
            
            batch_times.append(batch_end - batch_start)
            
            if i >= 10:  # 只测试前10个批次
                break
        
        total_time = time.time() - start_time
        avg_batch_time = np.mean(batch_times)
        
        print(f"   测试批次数: {len(batch_times)}")
        print(f"   总加载时间: {total_time:.2f}秒")
        print(f"   平均每批次时间: {avg_batch_time:.3f}秒")
        print(f"   数据加载速度: {batch_size/avg_batch_time:.1f} 样本/秒")
        
        return {
            'dataset_creation_time': dataset_creation_time,
            'avg_batch_loading_time': avg_batch_time,
            'samples_per_second': batch_size/avg_batch_time
        }
        
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        return None

def benchmark_model_inference_speed():
    """基准测试模型推理速度"""
    
    print(f"\n🧠 **模型推理速度基准测试**")
    print("=" * 60)
    
    try:
        from train_precomputed_keypoint_sampling_f3 import ConservativePointNet
        import torch
        
        device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️ 测试设备: {device}")
        
        # 创建模型
        model = ConservativePointNet(num_keypoints=19).to(device)
        model.eval()
        
        # 创建测试数据
        batch_size = 4
        num_points = 4096
        test_input = torch.randn(batch_size, num_points, 3).to(device)
        
        # 预热
        with torch.no_grad():
            for _ in range(5):
                _ = model(test_input)
        
        # 测试推理速度
        torch.cuda.synchronize() if torch.cuda.is_available() else None
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(100):
                output = model(test_input)
        
        torch.cuda.synchronize() if torch.cuda.is_available() else None
        total_time = time.time() - start_time
        
        avg_inference_time = total_time / 100
        samples_per_second = batch_size / avg_inference_time
        
        print(f"   测试批次: 100")
        print(f"   批次大小: {batch_size}")
        print(f"   总推理时间: {total_time:.2f}秒")
        print(f"   平均每批次推理时间: {avg_inference_time:.3f}秒")
        print(f"   推理速度: {samples_per_second:.1f} 样本/秒")
        
        return {
            'avg_inference_time': avg_inference_time,
            'samples_per_second': samples_per_second
        }
        
    except Exception as e:
        print(f"❌ 模型推理测试失败: {e}")
        return None

def analyze_model_quality(results_file):
    """分析模型质量"""
    
    print(f"\n📊 **模型质量分析**")
    print("=" * 60)
    
    with open(results_file, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    test_error = results.get('test_error', 0)
    test_std = results.get('test_std', 0)
    all_distances = results.get('all_distances', [])
    
    print(f"📊 测试误差: {test_error:.3f}±{test_std:.3f}mm")
    print(f"📊 测试样本数: {len(all_distances)}")
    
    if all_distances:
        distances = np.array(all_distances)
        
        print(f"📊 误差分布:")
        print(f"   最小值: {np.min(distances):.3f}mm")
        print(f"   25%分位数: {np.percentile(distances, 25):.3f}mm")
        print(f"   中位数: {np.median(distances):.3f}mm")
        print(f"   75%分位数: {np.percentile(distances, 75):.3f}mm")
        print(f"   最大值: {np.max(distances):.3f}mm")
        
        # 质量评估
        excellent_samples = np.sum(distances <= 5.0)
        good_samples = np.sum(distances <= 7.0)
        acceptable_samples = np.sum(distances <= 10.0)
        
        print(f"📊 质量分布:")
        print(f"   优秀(≤5mm): {excellent_samples}/{len(distances)} ({excellent_samples/len(distances)*100:.1f}%)")
        print(f"   良好(≤7mm): {good_samples}/{len(distances)} ({good_samples/len(distances)*100:.1f}%)")
        print(f"   可接受(≤10mm): {acceptable_samples}/{len(distances)} ({acceptable_samples/len(distances)*100:.1f}%)")
        
        # 一致性分析
        cv = test_std / test_error  # 变异系数
        print(f"📊 预测一致性:")
        print(f"   变异系数: {cv:.3f}")
        
        if cv < 0.15:
            print("   ✅ 预测非常一致")
        elif cv < 0.25:
            print("   ✅ 预测较为一致")
        else:
            print("   ⚠️ 预测一致性有待提高")
    
    return {
        'test_error': test_error,
        'test_std': test_std,
        'coefficient_of_variation': test_std / test_error if test_error > 0 else 0,
        'excellent_rate': excellent_samples/len(distances) if all_distances else 0
    }

def comprehensive_training_diagnosis(results_file):
    """综合训练诊断"""
    
    print("🔍 **综合训练诊断报告**")
    print("=" * 80)
    
    # 1. 收敛性分析
    convergence_analysis = analyze_training_convergence(results_file)
    
    # 2. 速度分析
    speed_analysis = analyze_training_speed(results_file)
    
    # 3. 数据加载基准测试
    data_loading_benchmark = benchmark_data_loading_speed()
    
    # 4. 模型推理基准测试
    inference_benchmark = benchmark_model_inference_speed()
    
    # 5. 模型质量分析
    quality_analysis = analyze_model_quality(results_file)
    
    # 综合评估
    print(f"\n🎯 **综合评估与建议**")
    print("=" * 60)
    
    issues = []
    recommendations = []
    
    # 收敛性问题
    if not convergence_analysis['converged']:
        issues.append("训练未充分收敛")
        recommendations.append("增加训练轮数或调整学习率调度")
    
    if convergence_analysis['overfitting']:
        issues.append("存在过拟合")
        recommendations.append("增加正则化或减少模型复杂度")
    
    if not convergence_analysis['early_stop_appropriate']:
        issues.append("早停可能过早")
        recommendations.append("调整早停耐心参数")
    
    # 速度问题
    if not speed_analysis['speed_reasonable']:
        issues.append("训练速度异常")
        recommendations.append("检查数据加载和模型计算流程")
    
    # 质量问题
    if quality_analysis['excellent_rate'] < 0.1:
        issues.append("高精度样本比例过低")
        recommendations.append("优化采样策略或损失函数")
    
    if quality_analysis['coefficient_of_variation'] > 0.25:
        issues.append("预测一致性不足")
        recommendations.append("增加训练数据或改进数据增强")
    
    if issues:
        print("⚠️ **发现的问题:**")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
        
        print(f"\n💡 **改进建议:**")
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. {rec}")
    else:
        print("✅ **训练质量良好，未发现明显问题**")
    
    return {
        'convergence': convergence_analysis,
        'speed': speed_analysis,
        'data_loading': data_loading_benchmark,
        'inference': inference_benchmark,
        'quality': quality_analysis,
        'issues': issues,
        'recommendations': recommendations
    }

if __name__ == "__main__":
    results_file = "detailed_diagnostic_results.json"

    if Path(results_file).exists():
        diagnosis = comprehensive_training_diagnosis(results_file)

        # 保存诊断结果
        with open('training_diagnosis_report.json', 'w', encoding='utf-8') as f:
            json.dump(diagnosis, f, indent=2, ensure_ascii=False)

        print(f"\n💾 **诊断报告已保存**: training_diagnosis_report.json")
    else:
        print(f"❌ 未找到结果文件: {results_file}")
        print("请先运行 train_with_detailed_diagnostics.py")
