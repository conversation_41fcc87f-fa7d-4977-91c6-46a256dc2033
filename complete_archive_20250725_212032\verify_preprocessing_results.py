#!/usr/bin/env python3
"""
验证预处理结果 - 可视化1.28mm误差的真实性
Verify Preprocessing Results - Visualize the Reality of 1.28mm Error
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json
from pathlib import Path
from datetime import datetime
from sklearn.model_selection import train_test_split

class LightweightKeypointNet(nn.Module):
    """轻量级关键点检测网络"""
    
    def __init__(self, input_dim=3, hidden_dim=128, output_dim=19*3, dropout=0.2):
        super().__init__()
        
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU()
        )
        
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(hidden_dim, 256),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, output_dim)
        )
        
    def forward(self, point_cloud):
        batch_size = point_cloud.size(0)
        pc_flat = point_cloud.view(-1, 3)
        features = self.feature_extractor(pc_flat)
        features = features.view(batch_size, -1, features.size(-1))
        global_feature, _ = torch.max(features, dim=1)
        keypoints = self.keypoint_regressor(global_feature)
        return keypoints.view(batch_size, 19, 3)

class ResultVerifier:
    """结果验证器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def load_and_prepare_data(self):
        """加载原始和预处理数据"""
        print("📦 加载原始和预处理数据...")
        
        # 加载原始数据
        original_data = np.load('data/raw/high_quality_f3_dataset.npz', allow_pickle=True)
        self.original_sample_ids = original_data['sample_ids']
        self.original_point_clouds = original_data['point_clouds']
        self.original_keypoints = original_data['keypoints']
        
        # 加载预处理数据
        processed_files = list(Path("data/processed").glob("lightweight_preprocessed_*.npz"))
        latest_file = max(processed_files, key=lambda x: x.stat().st_mtime)
        
        processed_data = np.load(str(latest_file), allow_pickle=True)
        self.processed_point_clouds = processed_data['point_clouds']
        self.processed_keypoints = processed_data['keypoints']
        self.normalization_params = processed_data['normalization_params']
        
        print(f"✅ 数据加载完成")
        print(f"   原始数据范围: 点云[{np.min(self.original_point_clouds):.2f}, {np.max(self.original_point_clouds):.2f}]")
        print(f"   预处理数据范围: 点云[{np.min(self.processed_point_clouds):.2f}, {np.max(self.processed_point_clouds):.2f}]")
        print(f"   原始关键点范围: [{np.min(self.original_keypoints):.2f}, {np.max(self.original_keypoints):.2f}]")
        print(f"   预处理关键点范围: [{np.min(self.processed_keypoints):.2f}, {np.max(self.processed_keypoints):.2f}]")
        
    def train_quick_model(self, use_processed=True):
        """快速训练一个模型用于验证"""
        print(f"\n🎯 训练模型 ({'预处理' if use_processed else '原始'}数据)")
        
        # 选择数据
        if use_processed:
            point_clouds = self.processed_point_clouds
            keypoints = self.processed_keypoints
        else:
            # 对原始数据进行简单下采样
            downsampled_pcs = []
            for pc in self.original_point_clouds:
                pc_array = np.array(pc, dtype=np.float32)
                if len(pc_array) > 2048:
                    indices = np.random.choice(len(pc_array), 2048, replace=False)
                    pc_downsampled = pc_array[indices]
                else:
                    pc_downsampled = pc_array
                downsampled_pcs.append(pc_downsampled)
            
            # 统一点数
            uniform_pcs = []
            for pc in downsampled_pcs:
                if len(pc) < 2048:
                    indices = np.random.choice(len(pc), 2048, replace=True)
                    pc = pc[indices]
                uniform_pcs.append(pc)
            
            point_clouds = np.array(uniform_pcs, dtype=np.float32)
            keypoints = np.array([np.array(kp, dtype=np.float32) for kp in self.original_keypoints])
        
        # 数据划分
        indices = np.arange(len(point_clouds))
        train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
        train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
        
        # 创建模型
        model = LightweightKeypointNet().to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=0.002, weight_decay=1e-4)
        criterion = nn.MSELoss()
        
        # 训练
        k_shot = 15
        epochs = 30
        
        for epoch in range(epochs):
            model.train()
            
            # 采样训练数据
            selected_indices = np.random.choice(train_indices, min(k_shot, len(train_indices)), replace=False)
            train_pcs = point_clouds[selected_indices]
            train_kps = keypoints[selected_indices]
            
            # 简单增强
            aug_pcs = []
            aug_kps = []
            
            for pc, kp in zip(train_pcs, train_kps):
                aug_pcs.append(pc)
                aug_kps.append(kp)
                
                # 轻微旋转
                angle = np.random.uniform(-0.05, 0.05)
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], dtype=np.float32)
                
                aug_pc = pc @ rotation.T
                aug_kp = kp @ rotation.T
                aug_pcs.append(aug_pc)
                aug_kps.append(aug_kp)
            
            # 训练
            batch_pcs = torch.FloatTensor(aug_pcs).to(self.device)
            batch_kps = torch.FloatTensor(aug_kps).to(self.device)
            
            optimizer.zero_grad()
            pred_kps = model(batch_pcs)
            loss = criterion(pred_kps, batch_kps)
            loss.backward()
            optimizer.step()
            
            if epoch % 10 == 0:
                print(f"Epoch {epoch}: Loss={loss:.4f}")
            
            # 清理内存
            del batch_pcs, batch_kps, pred_kps, loss
            torch.cuda.empty_cache()
        
        # 返回模型和测试数据
        test_data = {
            'point_clouds': point_clouds[test_indices],
            'keypoints': keypoints[test_indices],
            'indices': test_indices
        }
        
        return model, test_data
    
    def calculate_real_world_errors(self, model, test_data, use_processed=True):
        """计算真实世界误差"""
        print(f"\n📏 计算真实世界误差 ({'预处理' if use_processed else '原始'}数据)")
        
        model.eval()
        detailed_results = []
        
        with torch.no_grad():
            for i, (pc, kp) in enumerate(zip(test_data['point_clouds'], test_data['keypoints'])):
                # 确保数据类型正确
                pc_array = np.array(pc, dtype=np.float32)
                kp_array = np.array(kp, dtype=np.float32)

                # 预测
                pc_tensor = torch.FloatTensor(pc_array).unsqueeze(0).to(self.device)
                pred_kp = model(pc_tensor).cpu().numpy()[0]
                
                if use_processed:
                    # 预处理数据需要反标准化到真实物理空间
                    test_idx = test_data['indices'][i]
                    original_idx = test_idx  # 假设索引对应
                    
                    if original_idx < len(self.normalization_params):
                        norm_params = self.normalization_params[original_idx]
                        
                        if isinstance(norm_params, dict) and 'method' in norm_params:
                            if norm_params['method'] == 'robust':
                                # 反鲁棒标准化
                                median = np.array(norm_params['median'], dtype=np.float32)
                                mad = np.array(norm_params['mad'], dtype=np.float32)

                                pred_kp_real = pred_kp * mad + median
                                kp_real = kp_array * mad + median
                            else:
                                # 如果没有标准化参数，使用原始数据作为参考
                                pred_kp_real = pred_kp
                                kp_real = kp_array
                        else:
                            pred_kp_real = pred_kp
                            kp_real = kp_array
                    else:
                        pred_kp_real = pred_kp
                        kp_real = kp_array
                else:
                    # 原始数据直接使用
                    pred_kp_real = pred_kp
                    kp_real = kp_array
                
                # 计算误差
                errors = np.linalg.norm(pred_kp_real - kp_real, axis=1)
                mean_error = np.mean(errors)
                
                detailed_results.append({
                    'sample_index': i,
                    'predicted_keypoints': pred_kp_real,
                    'true_keypoints': kp_real,
                    'errors': errors,
                    'mean_error': mean_error,
                    'normalized_predicted': pred_kp,
                    'normalized_true': kp_array
                })
                
                print(f"样本 {i}: 平均误差 = {mean_error:.2f}mm (范围: {np.min(errors):.2f}-{np.max(errors):.2f}mm)")
        
        return detailed_results
    
    def visualize_predictions(self, results, data_type="processed", max_samples=3):
        """可视化预测结果"""
        print(f"\n📊 可视化{data_type}数据的预测结果")
        
        viz_dir = Path("results/verification_visualization")
        viz_dir.mkdir(parents=True, exist_ok=True)
        
        for i, result in enumerate(results[:max_samples]):
            fig = plt.figure(figsize=(20, 12))
            
            # 真实世界空间的预测
            ax1 = fig.add_subplot(231, projection='3d')
            
            true_kp = result['true_keypoints']
            pred_kp = result['predicted_keypoints']
            
            ax1.scatter(true_kp[:, 0], true_kp[:, 1], true_kp[:, 2], 
                       c='red', s=100, label='真实关键点', marker='o', alpha=0.8)
            ax1.scatter(pred_kp[:, 0], pred_kp[:, 1], pred_kp[:, 2], 
                       c='blue', s=100, label='预测关键点', marker='^', alpha=0.8)
            
            # 绘制连接线显示误差
            for j in range(len(true_kp)):
                ax1.plot([true_kp[j, 0], pred_kp[j, 0]], 
                        [true_kp[j, 1], pred_kp[j, 1]], 
                        [true_kp[j, 2], pred_kp[j, 2]], 
                        'k--', alpha=0.5, linewidth=1)
            
            ax1.set_title(f'真实世界空间 - 样本{i+1}\n平均误差: {result["mean_error"]:.2f}mm')
            ax1.legend()
            ax1.set_xlabel('X (mm)')
            ax1.set_ylabel('Y (mm)')
            ax1.set_zlabel('Z (mm)')
            
            # 标准化空间的预测
            ax2 = fig.add_subplot(232, projection='3d')
            
            norm_true = result['normalized_true']
            norm_pred = result['normalized_predicted']
            
            ax2.scatter(norm_true[:, 0], norm_true[:, 1], norm_true[:, 2], 
                       c='red', s=100, label='标准化真实', marker='o', alpha=0.8)
            ax2.scatter(norm_pred[:, 0], norm_pred[:, 1], norm_pred[:, 2], 
                       c='blue', s=100, label='标准化预测', marker='^', alpha=0.8)
            
            ax2.set_title(f'标准化空间 - 样本{i+1}')
            ax2.legend()
            ax2.set_xlabel('X (标准化)')
            ax2.set_ylabel('Y (标准化)')
            ax2.set_zlabel('Z (标准化)')
            
            # 误差分布
            ax3 = fig.add_subplot(233)
            errors = result['errors']
            ax3.bar(range(len(errors)), errors, alpha=0.7)
            ax3.set_title(f'各关键点误差分布')
            ax3.set_xlabel('关键点索引')
            ax3.set_ylabel('误差 (mm)')
            ax3.grid(True, alpha=0.3)
            
            # X-Y平面投影
            ax4 = fig.add_subplot(234)
            ax4.scatter(true_kp[:, 0], true_kp[:, 1], c='red', s=50, label='真实', alpha=0.7)
            ax4.scatter(pred_kp[:, 0], pred_kp[:, 1], c='blue', s=50, label='预测', alpha=0.7)
            for j in range(len(true_kp)):
                ax4.plot([true_kp[j, 0], pred_kp[j, 0]], 
                        [true_kp[j, 1], pred_kp[j, 1]], 'k--', alpha=0.3)
            ax4.set_title('X-Y平面投影')
            ax4.set_xlabel('X (mm)')
            ax4.set_ylabel('Y (mm)')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
            
            # 误差统计
            ax5 = fig.add_subplot(235)
            stats_text = f'样本 {i+1} 误差统计:\n'
            stats_text += f'平均误差: {result["mean_error"]:.3f} mm\n'
            stats_text += f'最大误差: {np.max(errors):.3f} mm\n'
            stats_text += f'最小误差: {np.min(errors):.3f} mm\n'
            stats_text += f'标准差: {np.std(errors):.3f} mm\n'
            stats_text += f'中位数: {np.median(errors):.3f} mm\n'
            stats_text += f'<1mm的点: {np.sum(errors < 1.0)}/19\n'
            stats_text += f'<2mm的点: {np.sum(errors < 2.0)}/19\n'
            stats_text += f'<5mm的点: {np.sum(errors < 5.0)}/19'
            
            ax5.text(0.05, 0.95, stats_text, transform=ax5.transAxes, 
                    fontsize=12, verticalalignment='top', fontfamily='monospace')
            ax5.set_xlim(0, 1)
            ax5.set_ylim(0, 1)
            ax5.set_title('误差统计')
            ax5.axis('off')
            
            # 数据范围对比
            ax6 = fig.add_subplot(236)
            range_text = f'数据范围对比:\n\n'
            range_text += f'真实关键点范围:\n'
            range_text += f'X: [{np.min(true_kp[:, 0]):.1f}, {np.max(true_kp[:, 0]):.1f}] mm\n'
            range_text += f'Y: [{np.min(true_kp[:, 1]):.1f}, {np.max(true_kp[:, 1]):.1f}] mm\n'
            range_text += f'Z: [{np.min(true_kp[:, 2]):.1f}, {np.max(true_kp[:, 2]):.1f}] mm\n\n'
            
            range_text += f'标准化关键点范围:\n'
            range_text += f'X: [{np.min(norm_true[:, 0]):.3f}, {np.max(norm_true[:, 0]):.3f}]\n'
            range_text += f'Y: [{np.min(norm_true[:, 1]):.3f}, {np.max(norm_true[:, 1]):.3f}]\n'
            range_text += f'Z: [{np.min(norm_true[:, 2]):.3f}, {np.max(norm_true[:, 2]):.3f}]'
            
            ax6.text(0.05, 0.95, range_text, transform=ax6.transAxes, 
                    fontsize=10, verticalalignment='top', fontfamily='monospace')
            ax6.set_xlim(0, 1)
            ax6.set_ylim(0, 1)
            ax6.set_title('数据范围')
            ax6.axis('off')
            
            plt.tight_layout()
            
            # 保存图片
            plt.savefig(viz_dir / f"verification_{data_type}_sample_{i+1}.png", 
                       dpi=150, bbox_inches='tight')
            print(f"💾 保存可视化: verification_{data_type}_sample_{i+1}.png")
            
            plt.show()

def main():
    """主函数 - 验证预处理结果"""
    print("🔍 验证预处理结果的真实性")
    print("=" * 60)
    
    verifier = ResultVerifier()
    
    # 加载数据
    verifier.load_and_prepare_data()
    
    # 训练并测试预处理数据
    print("\n" + "="*60)
    processed_model, processed_test_data = verifier.train_quick_model(use_processed=True)
    processed_results = verifier.calculate_real_world_errors(processed_model, processed_test_data, use_processed=True)
    
    # 训练并测试原始数据
    print("\n" + "="*60)
    original_model, original_test_data = verifier.train_quick_model(use_processed=False)
    original_results = verifier.calculate_real_world_errors(original_model, original_test_data, use_processed=False)
    
    # 计算总体统计
    processed_errors = [r['mean_error'] for r in processed_results]
    original_errors = [r['mean_error'] for r in original_results]
    
    print(f"\n📊 验证结果总结:")
    print("=" * 40)
    print(f"预处理数据:")
    print(f"  平均误差: {np.mean(processed_errors):.2f} mm")
    print(f"  误差范围: {np.min(processed_errors):.2f} - {np.max(processed_errors):.2f} mm")
    print(f"  标准差: {np.std(processed_errors):.2f} mm")
    
    print(f"\n原始数据:")
    print(f"  平均误差: {np.mean(original_errors):.2f} mm")
    print(f"  误差范围: {np.min(original_errors):.2f} - {np.max(original_errors):.2f} mm")
    print(f"  标准差: {np.std(original_errors):.2f} mm")
    
    improvement = (np.mean(original_errors) - np.mean(processed_errors)) / np.mean(original_errors) * 100
    print(f"\n改进幅度: {improvement:+.1f}%")
    
    # 可视化结果
    verifier.visualize_predictions(processed_results, "processed", max_samples=3)
    verifier.visualize_predictions(original_results, "original", max_samples=3)
    
    # 保存验证报告
    verification_report = {
        "verification_timestamp": datetime.now().isoformat(),
        "processed_data_stats": {
            "mean_error": np.mean(processed_errors),
            "std_error": np.std(processed_errors),
            "min_error": np.min(processed_errors),
            "max_error": np.max(processed_errors),
            "errors": processed_errors
        },
        "original_data_stats": {
            "mean_error": np.mean(original_errors),
            "std_error": np.std(original_errors),
            "min_error": np.min(original_errors),
            "max_error": np.max(original_errors),
            "errors": original_errors
        },
        "improvement_percent": improvement,
        "conclusion": "验证完成，请查看可视化结果"
    }
    
    # 保存报告
    report_dir = Path("results/verification_reports")
    report_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = report_dir / f"verification_report_{timestamp}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(verification_report, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 验证报告已保存: {report_file}")
    
    return verification_report

if __name__ == "__main__":
    report = main()
