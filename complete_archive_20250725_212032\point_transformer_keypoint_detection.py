#!/usr/bin/env python3
"""
Point Transformer关键点检测
Point Transformer for Keypoint Detection
基于最新的Point Transformer架构进行F3关键点检测
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
from sklearn.model_selection import train_test_split
import json

class PointTransformerLayer(nn.Module):
    """Point Transformer层"""
    
    def __init__(self, in_channels, out_channels, num_neighbors=16):
        super().__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.num_neighbors = num_neighbors
        
        # 线性变换层
        self.linear_q = nn.Linear(in_channels, out_channels)
        self.linear_k = nn.Linear(in_channels, out_channels)
        self.linear_v = nn.Linear(in_channels, out_channels)
        
        # 位置编码
        self.pos_mlp = nn.Sequential(
            nn.Linear(3, out_channels),
            nn.ReLU(),
            nn.Linear(out_channels, out_channels)
        )
        
        # 注意力权重
        self.attn_mlp = nn.Sequential(
            nn.Linear(out_channels, out_channels),
            nn.ReLU(),
            nn.Linear(out_channels, out_channels)
        )
        
        # 输出变换
        self.linear_out = nn.Linear(out_channels, out_channels)
        
        # 归一化
        self.norm = nn.LayerNorm(out_channels)
        
    def forward(self, features, positions):
        """
        features: (B, N, C) 点特征
        positions: (B, N, 3) 点坐标
        """
        B, N, C = features.shape
        
        # 计算Q, K, V
        Q = self.linear_q(features)  # (B, N, C_out)
        K = self.linear_k(features)  # (B, N, C_out)
        V = self.linear_v(features)  # (B, N, C_out)
        
        # 计算距离和邻居
        dist = torch.cdist(positions, positions)  # (B, N, N)
        
        # 选择最近的k个邻居
        _, neighbor_idx = torch.topk(dist, self.num_neighbors, dim=-1, largest=False)  # (B, N, k)
        
        # 收集邻居特征
        batch_idx = torch.arange(B).view(B, 1, 1).expand(B, N, self.num_neighbors)
        point_idx = torch.arange(N).view(1, N, 1).expand(B, N, self.num_neighbors)
        
        K_neighbors = K[batch_idx, neighbor_idx]  # (B, N, k, C_out)
        V_neighbors = V[batch_idx, neighbor_idx]  # (B, N, k, C_out)
        pos_neighbors = positions[batch_idx, neighbor_idx]  # (B, N, k, 3)
        
        # 位置编码
        pos_diff = positions.unsqueeze(2) - pos_neighbors  # (B, N, k, 3)
        pos_encoding = self.pos_mlp(pos_diff)  # (B, N, k, C_out)
        
        # 计算注意力
        Q_expanded = Q.unsqueeze(2).expand(B, N, self.num_neighbors, -1)  # (B, N, k, C_out)
        attn_input = Q_expanded - K_neighbors + pos_encoding  # (B, N, k, C_out)
        attn_weights = self.attn_mlp(attn_input)  # (B, N, k, C_out)
        attn_weights = F.softmax(attn_weights, dim=2)  # (B, N, k, C_out)
        
        # 应用注意力
        attended_features = torch.sum(attn_weights * (V_neighbors + pos_encoding), dim=2)  # (B, N, C_out)
        
        # 输出变换和残差连接
        output = self.linear_out(attended_features)
        
        # 如果维度匹配，添加残差连接
        if self.in_channels == self.out_channels:
            output = output + features
        
        output = self.norm(output)
        
        return output

class PointTransformerKeypointNet(nn.Module):
    """Point Transformer关键点检测网络"""
    
    def __init__(self, num_keypoints=19, num_neighbors=16):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 输入特征提取
        self.input_mlp = nn.Sequential(
            nn.Linear(3, 64),
            nn.ReLU(),
            nn.Linear(64, 128)
        )
        
        # Point Transformer层
        self.transformer1 = PointTransformerLayer(128, 128, num_neighbors)
        self.transformer2 = PointTransformerLayer(128, 256, num_neighbors)
        self.transformer3 = PointTransformerLayer(256, 512, num_neighbors)
        
        # 全局特征聚合
        self.global_mlp = nn.Sequential(
            nn.Linear(512, 1024),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 关键点回归器
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, num_keypoints * 3)
        )
        
    def forward(self, point_cloud):
        """
        point_cloud: (B, N, 3)
        """
        B, N, _ = point_cloud.shape
        
        # 输入特征提取
        features = self.input_mlp(point_cloud)  # (B, N, 128)
        positions = point_cloud  # (B, N, 3)
        
        # Point Transformer层
        features = self.transformer1(features, positions)  # (B, N, 128)
        features = self.transformer2(features, positions)  # (B, N, 256)
        features = self.transformer3(features, positions)  # (B, N, 512)
        
        # 全局最大池化
        global_feature, _ = torch.max(features, dim=1)  # (B, 512)
        
        # 全局特征处理
        global_feature = self.global_mlp(global_feature)  # (B, 512)
        
        # 关键点预测
        keypoints = self.keypoint_regressor(global_feature)  # (B, num_keypoints * 3)
        keypoints = keypoints.view(B, self.num_keypoints, 3)
        
        return keypoints

class PointTransformerTrainer:
    """Point Transformer训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def load_aligned_data(self):
        """加载对齐后的数据"""
        print("📦 加载F3对齐数据...")
        
        # 找到最新的对齐数据
        aligned_files = list(Path("data/processed").glob("f3_aligned_dataset_*.npz"))
        if not aligned_files:
            raise FileNotFoundError("未找到F3对齐数据集")
        
        latest_file = max(aligned_files, key=lambda x: x.stat().st_mtime)
        print(f"   使用数据: {latest_file}")
        
        data = np.load(str(latest_file), allow_pickle=True)
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        sample_ids = data['sample_ids']
        
        # 确保数据类型正确
        point_clouds = np.array(point_clouds, dtype=np.float32)
        keypoints = np.array(keypoints, dtype=np.float32)
        
        print(f"✅ 数据加载完成: {point_clouds.shape}, {keypoints.shape}")
        
        # 数据划分
        indices = np.arange(len(point_clouds))
        train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
        train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
        
        print(f"   训练: {len(train_indices)}, 验证: {len(val_indices)}, 测试: {len(test_indices)}")
        
        return self.data
    
    def train_model(self, epochs=60, lr=0.001, k_shot=20):
        """训练Point Transformer模型"""
        print(f"\n🚀 训练Point Transformer模型")
        print(f"   参数: epochs={epochs}, lr={lr}, k_shot={k_shot}")
        
        # 创建模型
        model = PointTransformerKeypointNet(num_keypoints=19, num_neighbors=16).to(self.device)
        
        # 计算模型参数
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"   模型参数: {total_params:,} (可训练: {trainable_params:,})")
        
        # 优化器和调度器
        optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-4)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)
        criterion = nn.MSELoss()
        
        # 训练循环
        best_val_error = float('inf')
        best_model_state = None
        patience = 0
        max_patience = 25
        
        train_losses = []
        val_errors = []
        
        for epoch in range(epochs):
            # 训练阶段
            model.train()
            epoch_losses = []
            
            # 采样训练数据
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                min(k_shot, len(self.data['train']['point_clouds'])), 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 数据增强
            aug_pcs, aug_kps = self.augment_data(train_pcs, train_kps)
            
            # 分批训练
            batch_size = 4  # Point Transformer内存需求较大
            for i in range(0, len(aug_pcs), batch_size):
                batch_pcs = torch.FloatTensor(aug_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(aug_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                pred_kps = model(batch_pcs)
                loss = criterion(pred_kps, batch_kps)
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.step()
                epoch_losses.append(loss.item())
                
                # 清理内存
                del batch_pcs, batch_kps, pred_kps, loss
                torch.cuda.empty_cache()
            
            scheduler.step()
            avg_loss = np.mean(epoch_losses)
            train_losses.append(avg_loss)
            
            # 验证阶段
            if epoch % 5 == 0:
                val_error = self.evaluate_model(model, 'val')
                val_errors.append(val_error)
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                    patience = 0
                else:
                    patience += 1
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val_Error={val_error:.3f}mm, "
                      f"LR={optimizer.param_groups[0]['lr']:.6f}, Patience={patience}")
                
                if patience >= max_patience:
                    print(f"早停在epoch {epoch}")
                    break
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)
            print(f"✅ 加载最佳模型 (验证误差: {best_val_error:.3f}mm)")
        
        self.model = model
        self.training_history = {
            'train_losses': train_losses,
            'val_errors': val_errors,
            'best_val_error': best_val_error
        }
        
        return model, best_val_error
    
    def augment_data(self, point_clouds, keypoints):
        """数据增强"""
        aug_pcs = []
        aug_kps = []
        
        for pc, kp in zip(point_clouds, keypoints):
            # 原始数据
            aug_pcs.append(pc)
            aug_kps.append(kp)
            
            # 旋转增强 (更小的角度，因为是医学数据)
            for _ in range(2):
                angle = np.random.uniform(-0.03, 0.03)  # ±1.7度
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], dtype=np.float32)
                
                aug_pc = pc @ rotation.T
                aug_kp = kp @ rotation.T
                aug_pcs.append(aug_pc)
                aug_kps.append(aug_kp)
            
            # 噪声增强 (医学级精度)
            noise_pc = pc + np.random.normal(0, 0.3, pc.shape).astype(np.float32)  # 0.3mm噪声
            aug_pcs.append(noise_pc)
            aug_kps.append(kp)
            
            # 轻微缩放
            scale = np.random.uniform(0.98, 1.02)  # ±2%缩放
            scaled_pc = pc * scale
            scaled_kp = kp * scale
            aug_pcs.append(scaled_pc)
            aug_kps.append(scaled_kp)
        
        return aug_pcs, aug_kps
    
    def evaluate_model(self, model, split='test'):
        """评估模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            # 分批评估以减少内存使用
            batch_size = 2
            for i in range(0, len(pcs), batch_size):
                batch_pcs = torch.FloatTensor(pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(kps[i:i+batch_size]).to(self.device)
                
                pred_kps = model(batch_pcs)
                
                for j in range(len(batch_pcs)):
                    error = torch.mean(torch.norm(pred_kps[j] - batch_kps[j], dim=1))
                    total_error += error.item()
                    num_samples += 1
                
                # 清理内存
                del batch_pcs, batch_kps, pred_kps
                torch.cuda.empty_cache()
        
        return total_error / num_samples if num_samples > 0 else float('inf')
    
    def save_model(self, model, val_error, output_dir="trained_models/point_transformer"):
        """保存模型"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_path = output_dir / f"point_transformer_f3_{val_error:.3f}mm_{timestamp}.pth"
        
        torch.save({
            'model_state_dict': model.state_dict(),
            'model_config': {
                'num_keypoints': 19,
                'num_neighbors': 16,
                'architecture': 'PointTransformer'
            },
            'training_history': self.training_history,
            'validation_error': val_error,
            'timestamp': timestamp
        }, model_path)
        
        print(f"💾 模型已保存: {model_path}")
        return model_path

def run_point_transformer_experiment():
    """运行Point Transformer实验"""
    print("🚀 Point Transformer关键点检测实验")
    print("=" * 60)
    
    # 创建训练器
    trainer = PointTransformerTrainer()
    
    # 加载数据
    data = trainer.load_aligned_data()
    
    # 训练模型
    model, val_error = trainer.train_model(epochs=60, lr=0.001, k_shot=20)
    
    # 测试模型
    test_error = trainer.evaluate_model(model, 'test')
    
    print(f"\n📊 Point Transformer结果:")
    print("=" * 40)
    print(f"验证误差: {val_error:.3f}mm")
    print(f"测试误差: {test_error:.3f}mm")
    
    # 与基线对比
    baseline_error = 22.94  # 之前的对齐数据基线
    improvement = (baseline_error - test_error) / baseline_error * 100
    
    print(f"\n📈 与基线对比:")
    print(f"基线误差 (简单网络): {baseline_error:.2f}mm")
    print(f"Point Transformer: {test_error:.2f}mm")
    print(f"架构改进: {improvement:+.1f}%")
    
    # 评估结果
    if improvement > 20:
        print("🎉 Point Transformer效果显著！")
        status = "显著改进"
    elif improvement > 10:
        print("👍 Point Transformer有明显效果")
        status = "明显改进"
    elif improvement > 0:
        print("📈 Point Transformer有一定效果")
        status = "轻微改进"
    else:
        print("⚠️ Point Transformer效果不明显")
        status = "效果有限"
    
    # 保存模型
    model_path = trainer.save_model(model, val_error)
    
    # 保存实验结果
    results = {
        "experiment_timestamp": datetime.now().isoformat(),
        "architecture": "PointTransformer",
        "dataset": "f3_aligned_dataset",
        "validation_error": float(val_error),
        "test_error": float(test_error),
        "baseline_error": baseline_error,
        "improvement_percent": float(improvement),
        "status": status,
        "model_path": str(model_path),
        "training_config": {
            "epochs": 60,
            "learning_rate": 0.001,
            "k_shot": 20,
            "batch_size": 4,
            "num_neighbors": 16
        },
        "conclusion": f"Point Transformer在F3关键点检测上获得{improvement:+.1f}%的改进"
    }
    
    # 保存结果
    results_dir = Path("results/point_transformer_experiments")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = results_dir / f"point_transformer_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 实验结果已保存: {results_file}")
    
    return trainer, results

if __name__ == "__main__":
    trainer, results = run_point_transformer_experiment()
