#!/usr/bin/env python3
"""
紧急数据集修复
Emergency Dataset Fix
立即修复坐标系和投影质量问题，支持12点→15点扩展
"""

import numpy as np
import torch
from sklearn.preprocessing import StandardScaler
from scipy.spatial.distance import cdist
import json

def emergency_coordinate_fix():
    """紧急坐标系修复"""
    
    print("🔥 紧急坐标系修复")
    print("=" * 60)
    
    # 加载数据集
    print("📊 加载数据集...")
    
    # 历史数据集（参考标准）
    hist_data = np.load('archive/old_experiments/f3_reduced_12kp_stable.npz', allow_pickle=True)
    hist_pc = hist_data['point_clouds']
    hist_kp = hist_data['keypoints']
    
    # 当前数据集
    curr_data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
    curr_pc = curr_data['point_clouds']
    curr_kp_57 = curr_data['keypoints_57']
    sample_ids = curr_data['sample_ids']
    
    # 提取对应的12点
    hist_12_indices = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17]
    curr_kp_12 = curr_kp_57[:, hist_12_indices, :]
    
    print(f"✅ 数据加载完成")
    print(f"   历史数据: {len(hist_pc)}样本")
    print(f"   当前数据: {len(curr_pc)}样本")
    
    # 分析当前问题
    print(f"\n📋 当前问题分析:")
    hist_scale = np.std(hist_kp)
    curr_scale = np.std(curr_kp_12)
    scale_ratio = curr_scale / hist_scale
    
    hist_center = np.mean(hist_kp, axis=(0,1))
    curr_center = np.mean(curr_kp_12, axis=(0,1))
    
    print(f"   尺度比例: {scale_ratio:.2f}")
    print(f"   历史中心: {hist_center}")
    print(f"   当前中心: {curr_center}")
    
    # 修复策略1: 尺度归一化
    print(f"\n🔧 修复1: 尺度归一化")
    scale_factor = 1.0 / scale_ratio
    print(f"   缩放因子: {scale_factor:.3f}")
    
    # 应用尺度修复
    curr_pc_fixed = curr_pc * scale_factor
    curr_kp_12_fixed = curr_kp_12 * scale_factor
    curr_kp_57_fixed = curr_kp_57 * scale_factor
    
    # 修复策略2: 中心对齐
    print(f"\n🔧 修复2: 中心对齐")
    curr_center_fixed = np.mean(curr_kp_12_fixed, axis=(0,1))
    center_offset = hist_center - curr_center_fixed
    print(f"   中心偏移: {center_offset}")
    
    # 应用中心修复
    curr_pc_fixed = curr_pc_fixed + center_offset
    curr_kp_12_fixed = curr_kp_12_fixed + center_offset
    curr_kp_57_fixed = curr_kp_57_fixed + center_offset
    
    # 验证修复效果
    print(f"\n✅ 修复效果验证:")
    fixed_scale = np.std(curr_kp_12_fixed)
    fixed_center = np.mean(curr_kp_12_fixed, axis=(0,1))
    
    print(f"   修复后尺度: {fixed_scale:.2f} (目标: {hist_scale:.2f})")
    print(f"   修复后中心: {fixed_center}")
    print(f"   尺度误差: {abs(fixed_scale - hist_scale)/hist_scale*100:.1f}%")
    print(f"   中心误差: {np.linalg.norm(fixed_center - hist_center):.3f}")
    
    # 保存修复后的数据集
    print(f"\n💾 保存修复后数据集...")
    
    np.savez_compressed(
        'emergency_fixed_dataset.npz',
        point_clouds=curr_pc_fixed,
        keypoints_57=curr_kp_57_fixed,
        keypoints_12=curr_kp_12_fixed,
        sample_ids=sample_ids,
        scale_factor=scale_factor,
        center_offset=center_offset,
        quality_metrics={
            'scale_error_percent': abs(fixed_scale - hist_scale)/hist_scale*100,
            'center_error': np.linalg.norm(fixed_center - hist_center),
            'original_scale_ratio': scale_ratio
        }
    )
    
    print(f"✅ 紧急修复完成!")
    print(f"   保存文件: emergency_fixed_dataset.npz")
    
    return curr_pc_fixed, curr_kp_57_fixed, curr_kp_12_fixed

def emergency_projection_fix(point_clouds, keypoints_57):
    """紧急表面投影修复"""
    
    print(f"\n🔥 紧急表面投影修复")
    print("=" * 60)
    
    print(f"📊 分析当前投影质量...")
    
    # 分析前10个样本的投影质量
    projection_distances = []
    fixed_keypoints_57 = keypoints_57.copy()
    
    for i in range(min(10, len(point_clouds))):
        pc = point_clouds[i]
        kp = keypoints_57[i]
        
        # 计算每个关键点到最近点云点的距离
        distances = cdist(kp, pc)
        min_distances = np.min(distances, axis=1)
        nearest_indices = np.argmin(distances, axis=1)
        
        projection_distances.extend(min_distances)
        
        # 对距离>2mm的关键点进行表面投影
        bad_projection_mask = min_distances > 2.0
        if np.any(bad_projection_mask):
            print(f"   样本{i}: {np.sum(bad_projection_mask)}个关键点需要投影修复")
            
            # 将这些关键点投影到最近的表面点
            for j in np.where(bad_projection_mask)[0]:
                nearest_point_idx = nearest_indices[j]
                fixed_keypoints_57[i, j] = pc[nearest_point_idx]
    
    # 验证修复效果
    print(f"\n✅ 投影修复效果验证:")
    
    # 重新计算投影质量
    new_projection_distances = []
    for i in range(min(10, len(point_clouds))):
        pc = point_clouds[i]
        kp = fixed_keypoints_57[i]
        
        distances = cdist(kp, pc)
        min_distances = np.min(distances, axis=1)
        new_projection_distances.extend(min_distances)
    
    original_avg = np.mean(projection_distances)
    fixed_avg = np.mean(new_projection_distances)
    original_1mm = np.mean(np.array(projection_distances) < 1.0) * 100
    fixed_1mm = np.mean(np.array(new_projection_distances) < 1.0) * 100
    
    print(f"   原始平均距离: {original_avg:.3f}mm")
    print(f"   修复后平均距离: {fixed_avg:.3f}mm")
    print(f"   原始<1mm准确率: {original_1mm:.1f}%")
    print(f"   修复后<1mm准确率: {fixed_1mm:.1f}%")
    print(f"   改进幅度: {(original_avg - fixed_avg)/original_avg*100:.1f}%")
    
    return fixed_keypoints_57

def create_progressive_subsets(point_clouds, keypoints_57):
    """创建渐进式扩展子集"""
    
    print(f"\n🔧 创建渐进式扩展子集")
    print("=" * 60)
    
    # 定义渐进式关键点选择策略
    progressive_subsets = {
        12: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17],  # 历史最佳12点
        15: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17, 0, 1, 12],  # 增加3个稳定F1点
        19: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17, 0, 1, 12, 13, 14, 15, 18],  # 完整F3
        24: list(range(24)),  # 前24个最稳定的点
        30: list(range(30)),  # 前30个点
        38: list(range(38)),  # 前38个点
        45: list(range(45)),  # 前45个点
        57: list(range(57))   # 全部57点
    }
    
    # 保存各个子集
    for num_points, indices in progressive_subsets.items():
        subset_keypoints = keypoints_57[:, indices, :]
        
        # 计算子集质量指标
        avg_inter_distance = []
        for i in range(len(subset_keypoints)):
            kp = subset_keypoints[i]
            distances = cdist(kp, kp)
            upper_tri = np.triu(distances, k=1)
            non_zero_distances = upper_tri[upper_tri > 0]
            avg_inter_distance.extend(non_zero_distances)
        
        quality_score = np.std(avg_inter_distance) / np.mean(avg_inter_distance)
        
        print(f"   {num_points}点子集: 变异系数 {quality_score:.3f}")
        
        # 保存子集
        np.savez_compressed(
            f'progressive_subset_{num_points}points.npz',
            point_clouds=point_clouds,
            keypoints=subset_keypoints,
            keypoint_indices=indices,
            num_points=num_points,
            quality_score=quality_score
        )
    
    print(f"✅ 渐进式子集创建完成!")
    return progressive_subsets

def validate_emergency_fixes():
    """验证紧急修复效果"""
    
    print(f"\n🔍 验证紧急修复效果")
    print("=" * 60)
    
    # 加载修复后的数据集
    try:
        fixed_data = np.load('emergency_fixed_dataset.npz', allow_pickle=True)
        quality_metrics = fixed_data['quality_metrics'].item()
        
        print(f"📊 修复质量评估:")
        print(f"   尺度误差: {quality_metrics['scale_error_percent']:.1f}%")
        print(f"   中心误差: {quality_metrics['center_error']:.3f}")
        print(f"   原始尺度比例: {quality_metrics['original_scale_ratio']:.2f}")
        
        # 质量评级
        if quality_metrics['scale_error_percent'] < 5:
            scale_grade = "优秀"
        elif quality_metrics['scale_error_percent'] < 10:
            scale_grade = "良好"
        else:
            scale_grade = "需改进"
        
        if quality_metrics['center_error'] < 1.0:
            center_grade = "优秀"
        elif quality_metrics['center_error'] < 2.0:
            center_grade = "良好"
        else:
            center_grade = "需改进"
        
        print(f"\n📋 质量评级:")
        print(f"   尺度修复: {scale_grade}")
        print(f"   中心对齐: {center_grade}")
        
        # 预期性能改进
        expected_improvement = min(50, quality_metrics['original_scale_ratio'] * 15)
        print(f"\n🚀 预期性能改进:")
        print(f"   12点模型: {expected_improvement:.1f}%改进")
        print(f"   15点扩展: 可行性从低提升到中")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔥 紧急数据集修复")
    print("立即修复关键质量问题，支持渐进式扩展")
    print("=" * 80)
    
    try:
        # 1. 紧急坐标系修复
        point_clouds, keypoints_57, keypoints_12 = emergency_coordinate_fix()
        
        # 2. 紧急表面投影修复
        keypoints_57_fixed = emergency_projection_fix(point_clouds, keypoints_57)
        
        # 3. 创建渐进式子集
        progressive_subsets = create_progressive_subsets(point_clouds, keypoints_57_fixed)
        
        # 4. 验证修复效果
        validation_success = validate_emergency_fixes()
        
        # 5. 保存最终修复数据集
        print(f"\n💾 保存最终修复数据集...")
        
        np.savez_compressed(
            'emergency_fixed_final_dataset.npz',
            point_clouds=point_clouds,
            keypoints_57=keypoints_57_fixed,
            keypoints_12=keypoints_57_fixed[:, [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17], :],
            progressive_subsets=progressive_subsets,
            fix_summary={
                'coordinate_fix': True,
                'projection_fix': True,
                'progressive_subsets': True,
                'validation_success': validation_success
            }
        )
        
        print(f"\n🎉 紧急修复完成!")
        print(f"   ✅ 坐标系修复: 尺度和中心对齐")
        print(f"   ✅ 表面投影修复: 改进投影精度")
        print(f"   ✅ 渐进式子集: 12/15/19/24点数据集")
        print(f"   ✅ 质量验证: {'通过' if validation_success else '需要进一步调整'}")
        
        print(f"\n🚀 下一步建议:")
        print(f"   1. 使用emergency_fixed_final_dataset.npz重新训练12点模型")
        print(f"   2. 验证12点性能是否提升到6.0mm以下")
        print(f"   3. 尝试15点扩展，验证渐进式策略")
        print(f"   4. 如果成功，继续19点和24点扩展")
        
    except Exception as e:
        print(f"❌ 紧急修复失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
