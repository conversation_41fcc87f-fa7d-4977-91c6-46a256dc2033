#!/usr/bin/env python3
"""
简单优化平衡模型 - 基于成功的女性模型架构
Simple Optimization for Balanced Model - Based on Successful Female Model Architecture
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import os
from tqdm import tqdm

class OptimizedHeatmapNet(nn.Module):
    """优化的Heatmap回归网络 - 基于成功的女性模型"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super(OptimizedHeatmapNet, self).__init__()
        
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 点云特征提取 (与成功的女性模型相同)
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        # 全局特征
        self.global_conv = nn.Conv1d(512, 1024, 1)
        
        # 特征融合 (增强版)
        self.fusion_conv1 = nn.Conv1d(1024 + 256, 512, 1)
        self.fusion_conv2 = nn.Conv1d(512, 256, 1)
        
        # 性别自适应层 (轻量级)
        self.gender_adapt = nn.Sequential(
            nn.Conv1d(256, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # Heatmap生成
        self.heatmap_conv1 = nn.Conv1d(256, 128, 1)
        self.heatmap_conv2 = nn.Conv1d(128, 64, 1)
        self.heatmap_conv3 = nn.Conv1d(64, num_keypoints, 1)
        
        # 激活函数和正则化
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.3)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        # 点云特征提取
        x1 = self.relu(self.bn1(self.conv1(x)))
        x2 = self.relu(self.bn2(self.conv2(x1)))
        x3 = self.relu(self.bn3(self.conv3(x2)))
        x4 = self.relu(self.bn4(self.conv4(x3)))
        
        # 全局特征
        global_feat = self.relu(self.global_conv(x4))
        global_feat = torch.max(global_feat, 2, keepdim=True)[0]
        
        # 扩展全局特征
        global_feat_expanded = global_feat.repeat(1, 1, self.num_points)
        
        # 融合局部和全局特征
        combined_feat = torch.cat([x3, global_feat_expanded], 1)
        
        # 特征融合
        fused = self.relu(self.fusion_conv1(combined_feat))
        fused = self.dropout(fused)
        fused = self.relu(self.fusion_conv2(fused))
        
        # 性别自适应
        adapted = self.gender_adapt(fused)
        
        # 生成热图
        heatmap = self.relu(self.heatmap_conv1(adapted))
        heatmap = self.relu(self.heatmap_conv2(heatmap))
        heatmap = self.heatmap_conv3(heatmap)
        
        # 转置并应用softmax
        heatmap = heatmap.transpose(2, 1)
        
        # 对每个关键点的热图进行softmax
        heatmap_list = []
        for i in range(self.num_keypoints):
            hm_i = torch.softmax(heatmap[:, :, i], dim=1)
            heatmap_list.append(hm_i.unsqueeze(2))
        
        final_heatmap = torch.cat(heatmap_list, dim=2)
        
        return final_heatmap

def heatmap_loss_function(pred_heatmap, target_heatmap):
    """简化的热图损失函数"""
    
    # 检查并调整target_heatmap的维度
    if len(target_heatmap.shape) == 3 and target_heatmap.shape[1] == 12:
        target_heatmap = target_heatmap.transpose(1, 2)
    
    # KL散度损失
    kl_loss = nn.KLDivLoss(reduction='batchmean')
    
    total_loss = 0
    batch_size, num_points, num_keypoints = pred_heatmap.shape
    
    for i in range(num_keypoints):
        log_pred = torch.log(pred_heatmap[:, :, i] + 1e-8)
        loss_i = kl_loss(log_pred, target_heatmap[:, :, i])
        total_loss += loss_i
    
    return total_loss / num_keypoints

def extract_keypoints_from_heatmap(heatmap, point_cloud):
    """从热图中提取关键点坐标"""
    
    batch_size, num_points, num_keypoints = heatmap.shape
    keypoints = torch.zeros(batch_size, num_keypoints, 3)
    
    for b in range(batch_size):
        for k in range(num_keypoints):
            weights = heatmap[b, :, k]
            weighted_coords = point_cloud[b] * weights.unsqueeze(1)
            keypoint = torch.sum(weighted_coords, dim=0) / torch.sum(weights)
            keypoints[b, k] = keypoint
    
    return keypoints

def load_balanced_dataset():
    """加载平衡的男女数据集"""
    
    print("📊 加载平衡的男女数据集...")
    
    # 加载女性数据
    female_data = np.load("f3_reduced_12kp_female_augmented.npz", allow_pickle=True)
    female_pc = female_data['point_clouds']
    female_kp = female_data['keypoints']
    
    # 加载男性数据
    male_data = np.load("f3_reduced_12kp_male_augmented.npz", allow_pickle=True)
    male_pc = male_data['point_clouds']
    male_kp = male_data['keypoints']
    
    print(f"✅ 女性数据: {len(female_pc)}个样本")
    print(f"✅ 男性数据: {len(male_pc)}个样本")
    
    # 平衡数据量
    min_count = min(len(female_pc), len(male_pc))
    print(f"📊 平衡到: 各{min_count}个样本")
    
    # 随机采样
    female_indices = np.random.choice(len(female_pc), min_count, replace=False)
    male_indices = np.random.choice(len(male_pc), min_count, replace=False)
    
    # 合并数据
    all_pc = np.concatenate([female_pc[female_indices], male_pc[male_indices]])
    all_kp = np.concatenate([female_kp[female_indices], male_kp[male_indices]])
    
    # 性别标签
    gender_labels = np.concatenate([
        np.zeros(min_count),  # 0 = 女性
        np.ones(min_count)    # 1 = 男性
    ])
    
    # 生成热图
    print("🔥 生成热图...")
    all_hm = []
    for i in tqdm(range(len(all_pc)), desc="生成热图"):
        hm = generate_heatmap_from_keypoints(all_kp[i], all_pc[i])
        all_hm.append(hm)
    all_hm = np.array(all_hm)
    
    print(f"📊 最终数据集:")
    print(f"   总样本: {len(all_pc)}")
    print(f"   点云形状: {all_pc.shape}")
    print(f"   关键点形状: {all_kp.shape}")
    print(f"   热图形状: {all_hm.shape}")
    
    return all_pc, all_kp, all_hm, gender_labels

def generate_heatmap_from_keypoints(keypoints, point_cloud, sigma=5.0):
    """从关键点生成热图"""
    heatmaps = []
    
    for kp in keypoints:
        distances = np.linalg.norm(point_cloud - kp, axis=1)
        heatmap = np.exp(-distances**2 / (2 * sigma**2))
        
        if np.sum(heatmap) > 0:
            heatmap = heatmap / np.sum(heatmap)
        
        heatmaps.append(heatmap)
    
    return np.array(heatmaps)

def train_simple_optimized_model():
    """训练简单优化模型"""
    
    print("🚀 开始简单优化训练")
    print("🎯 目标: 基于成功的女性模型架构优化平衡模型")
    print("=" * 80)
    
    # 加载数据
    all_pc, all_kp, all_hm, gender_labels = load_balanced_dataset()
    
    # 数据分割
    n_samples = len(all_pc)
    n_train = int(n_samples * 0.7)
    n_val = int(n_samples * 0.15)
    
    indices = np.random.permutation(n_samples)
    train_indices = indices[:n_train]
    val_indices = indices[n_train:n_train + n_val]
    test_indices = indices[n_train + n_val:]
    
    train_pc, train_kp, train_hm = all_pc[train_indices], all_kp[train_indices], all_hm[train_indices]
    val_pc, val_kp, val_hm = all_pc[val_indices], all_kp[val_indices], all_hm[val_indices]
    test_pc, test_kp, test_hm = all_pc[test_indices], all_kp[test_indices], all_hm[test_indices]
    
    train_gender = gender_labels[train_indices]
    val_gender = gender_labels[val_indices]
    test_gender = gender_labels[test_indices]
    
    print(f"📋 数据分割:")
    print(f"   训练集: {len(train_indices)}个样本")
    print(f"   验证集: {len(val_indices)}个样本")
    print(f"   测试集: {len(test_indices)}个样本")
    
    # 设备设置
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ 使用设备: {device}")
    
    # 模型初始化
    model = OptimizedHeatmapNet(num_points=50000, num_keypoints=12)
    model = model.to(device)
    
    print(f"🏗️ 优化模型架构:")
    print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"   特色: 基于成功女性模型 + 性别自适应")
    
    # 优化器 (使用成功的配置)
    optimizer = optim.Adam(model.parameters(), lr=0.0005, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=15, gamma=0.7)
    
    # 训练参数
    num_epochs = 40
    batch_size = 4
    best_val_error = float('inf')
    patience = 10
    patience_counter = 0
    
    print(f"🎯 训练参数:")
    print(f"   训练轮数: {num_epochs}")
    print(f"   批次大小: {batch_size}")
    print(f"   学习率: 0.0005")
    print(f"   优化器: Adam")
    
    print(f"\n🚀 开始训练...")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        n_train_batches = len(train_pc) // batch_size
        
        train_pbar = tqdm(range(n_train_batches), desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for i in train_pbar:
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(train_pc))
            
            batch_pc = torch.FloatTensor(train_pc[start_idx:end_idx]).to(device)
            batch_hm = torch.FloatTensor(train_hm[start_idx:end_idx]).to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            pred_hm = model(batch_pc)
            loss = heatmap_loss_function(pred_hm, batch_hm)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_pbar.set_postfix({'Loss': f'{loss.item():.4f}'})
        
        avg_train_loss = train_loss / n_train_batches
        
        # 验证阶段
        model.eval()
        val_errors = []
        val_errors_female = []
        val_errors_male = []
        
        with torch.no_grad():
            n_val_batches = len(val_pc) // batch_size + (1 if len(val_pc) % batch_size > 0 else 0)
            
            for i in range(n_val_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(val_pc))
                
                batch_pc = torch.FloatTensor(val_pc[start_idx:end_idx]).to(device)
                batch_kp = torch.FloatTensor(val_kp[start_idx:end_idx]).to(device)
                batch_gender = val_gender[start_idx:end_idx]
                
                pred_hm = model(batch_pc)
                pred_kp = extract_keypoints_from_heatmap(pred_hm.cpu(), batch_pc.cpu())
                
                for j in range(len(batch_kp)):
                    error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j].cpu(), dim=1))
                    val_errors.append(error.item())
                    
                    if batch_gender[j] == 0:  # 女性
                        val_errors_female.append(error.item())
                    else:  # 男性
                        val_errors_male.append(error.item())
        
        avg_val_error = np.mean(val_errors)
        avg_val_error_female = np.mean(val_errors_female) if val_errors_female else 0
        avg_val_error_male = np.mean(val_errors_male) if val_errors_male else 0
        
        # 学习率调度
        scheduler.step()
        
        print(f"\nEpoch {epoch+1}/{num_epochs}:")
        print(f"  训练损失: {avg_train_loss:.4f}")
        print(f"  验证误差 (总体): {avg_val_error:.2f}mm")
        print(f"  验证误差 (女性): {avg_val_error_female:.2f}mm")
        print(f"  验证误差 (男性): {avg_val_error_male:.2f}mm")
        print(f"  学习率: {scheduler.get_last_lr()[0]:.6f}")
        
        # 早停和模型保存
        if avg_val_error < best_val_error:
            best_val_error = avg_val_error
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'val_error': avg_val_error,
                'val_error_female': avg_val_error_female,
                'val_error_male': avg_val_error_male,
            }, 'best_simple_optimized_model.pth')
            print(f"  ✅ 保存最佳模型 (验证误差: {avg_val_error:.2f}mm)")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f"  ⏹️ 早停触发 (耐心: {patience})")
                break
    
    return test_pc, test_kp, test_gender

def main():
    """主函数"""
    
    print("🚀 简单优化平衡模型")
    print("🎯 目标: 基于成功的女性模型架构")
    print("💡 策略: 简单有效的优化")
    print("=" * 80)
    
    # 训练简单优化模型
    test_pc, test_kp, test_gender = train_simple_optimized_model()
    
    print(f"\n🧪 测试简单优化模型...")
    
    # 加载最佳模型进行测试
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    model = OptimizedHeatmapNet(num_points=50000, num_keypoints=12)
    
    checkpoint = torch.load('best_simple_optimized_model.pth')
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    # 测试
    test_errors = []
    test_errors_female = []
    test_errors_male = []
    
    batch_size = 4
    with torch.no_grad():
        n_test_batches = len(test_pc) // batch_size + (1 if len(test_pc) % batch_size > 0 else 0)
        
        for i in range(n_test_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(test_pc))
            
            batch_pc = torch.FloatTensor(test_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(test_kp[start_idx:end_idx]).to(device)
            batch_gender = test_gender[start_idx:end_idx]
            
            pred_hm = model(batch_pc)
            pred_kp = extract_keypoints_from_heatmap(pred_hm.cpu(), batch_pc.cpu())
            
            for j in range(len(batch_kp)):
                error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j].cpu(), dim=1))
                test_errors.append(error.item())
                
                if batch_gender[j] == 0:  # 女性
                    test_errors_female.append(error.item())
                else:  # 男性
                    test_errors_male.append(error.item())
    
    final_test_error = np.mean(test_errors)
    final_test_error_female = np.mean(test_errors_female)
    final_test_error_male = np.mean(test_errors_male)
    
    print(f"\n🎉 简单优化结果!")
    print(f"=" * 80)
    print(f"📊 最终测试结果:")
    print(f"   测试误差 (总体): {final_test_error:.2f}mm")
    print(f"   测试误差 (女性): {final_test_error_female:.2f}mm")
    print(f"   测试误差 (男性): {final_test_error_male:.2f}mm")
    
    print(f"\n📈 优化效果对比:")
    print(f"   原始平衡模型: 4.18mm")
    print(f"   简单优化模型: {final_test_error:.2f}mm")
    improvement = (4.18 - final_test_error) / 4.18 * 100
    print(f"   性能变化: {improvement:.1f}%")
    
    if final_test_error < 4.18:
        print(f"✅ 优化成功!")
    else:
        print(f"⚠️ 需要进一步调整")
    
    return final_test_error

if __name__ == "__main__":
    main()
