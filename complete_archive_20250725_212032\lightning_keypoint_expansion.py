#!/usr/bin/env python3
"""
基于PyTorch Lightning的12→57关键点扩展网络
Lightning-based Keypoint Expansion Network: 12 → 57 keypoints
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping
from pytorch_lightning.loggers import TensorBoardLogger
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import json
import os

class KeypointExpansionDataset(Dataset):
    """关键点扩展数据集"""
    
    def __init__(self, input_12, target_57):
        self.input_12 = torch.FloatTensor(input_12)
        self.target_57 = torch.FloatTensor(target_57)
        
    def __len__(self):
        return len(self.input_12)
    
    def __getitem__(self, idx):
        return self.input_12[idx], self.target_57[idx]

class AnatomicalConstraintLayer(nn.Module):
    """解剖学约束层"""
    
    def __init__(self, constraint_weight=0.1):
        super().__init__()
        self.constraint_weight = constraint_weight
        
    def forward(self, keypoints_57):
        """应用解剖学约束"""
        # keypoints_57: [batch_size, 57, 3]
        
        # F1-F2对称性约束
        keypoints_57 = self.apply_symmetry_constraint(keypoints_57)
        
        return keypoints_57
    
    def apply_symmetry_constraint(self, keypoints):
        """应用F1-F2对称性约束"""
        f1_points = keypoints[:, 0:19, :]    # F1: 0-18
        f2_points = keypoints[:, 19:38, :]   # F2: 19-37
        f3_points = keypoints[:, 38:57, :]   # F3: 38-56
        
        # 计算对称轴
        f1_center = torch.mean(f1_points, dim=1, keepdim=True)
        f2_center = torch.mean(f2_points, dim=1, keepdim=True)
        
        # 轻微的对称性调整
        adjustment = (f2_center - f1_center) * self.constraint_weight
        adjustment[:, :, 0] *= -1  # X轴对称
        
        f1_adjusted = f1_points + adjustment
        f2_adjusted = f2_points - adjustment
        
        return torch.cat([f1_adjusted, f2_adjusted, f3_points], dim=1)

class LightningKeypointExpansion(pl.LightningModule):
    """基于Lightning的关键点扩展网络"""
    
    def __init__(self, 
                 input_dim=36, 
                 hidden_dim=512, 
                 output_dim=171,
                 learning_rate=0.001,
                 weight_decay=1e-5):
        super().__init__()
        
        # 保存超参数
        self.save_hyperparameters()
        
        # 网络架构
        self.input_projection = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 多层特征提取
        self.feature_layers = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.BatchNorm1d(hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Linear(hidden_dim * 2, hidden_dim * 2),
            nn.BatchNorm1d(hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 输出层
        self.output_layer = nn.Linear(hidden_dim, output_dim)
        
        # 解剖学约束层
        self.anatomical_constraints = AnatomicalConstraintLayer()
        
        # 损失函数
        self.mse_loss = nn.MSELoss()
        
    def forward(self, keypoints_12):
        """前向传播"""
        # keypoints_12: [batch_size, 12, 3]
        batch_size = keypoints_12.size(0)
        
        # 展平输入
        x = keypoints_12.view(batch_size, -1)  # [batch_size, 36]
        
        # 特征提取
        x = self.input_projection(x)
        x = self.feature_layers(x)
        
        # 生成57个关键点
        output = self.output_layer(x)
        keypoints_57 = output.view(batch_size, 57, 3)
        
        # 应用解剖学约束
        constrained_keypoints = self.anatomical_constraints(keypoints_57)
        
        return constrained_keypoints
    
    def compute_loss(self, predicted, target):
        """计算损失"""
        # 主要MSE损失
        mse_loss = self.mse_loss(predicted, target)
        
        # 解剖学约束损失
        constraint_loss = self.compute_anatomical_loss(predicted)
        
        # 总损失
        total_loss = mse_loss + 0.1 * constraint_loss
        
        return total_loss, mse_loss, constraint_loss
    
    def compute_anatomical_loss(self, keypoints_57):
        """计算解剖学约束损失"""
        # F1-F2对称性损失
        f1_points = keypoints_57[:, 0:19, :]
        f2_points = keypoints_57[:, 19:38, :]
        
        # 计算对称性损失（简化版本）
        f1_center = torch.mean(f1_points, dim=1)
        f2_center = torch.mean(f2_points, dim=1)
        
        # 期望F1和F2在X轴上对称
        symmetry_loss = torch.mean(torch.abs(f1_center[:, 0] + f2_center[:, 0]))
        
        return symmetry_loss
    
    def compute_metrics(self, predicted, target):
        """计算评估指标"""
        # 计算平均距离误差
        distances = torch.norm(predicted - target, dim=2)
        avg_error = torch.mean(distances)
        
        # 计算各区域误差
        f1_error = torch.mean(distances[:, 0:19])
        f2_error = torch.mean(distances[:, 19:38])
        f3_error = torch.mean(distances[:, 38:57])
        
        # 计算医疗级准确率
        accuracy_5mm = torch.mean((distances < 5.0).float()) * 100
        accuracy_10mm = torch.mean((distances < 10.0).float()) * 100
        
        return {
            'avg_error': avg_error,
            'f1_error': f1_error,
            'f2_error': f2_error,
            'f3_error': f3_error,
            'accuracy_5mm': accuracy_5mm,
            'accuracy_10mm': accuracy_10mm
        }
    
    def training_step(self, batch, batch_idx):
        """训练步骤"""
        input_12, target_57 = batch
        predicted_57 = self(input_12)
        
        # 计算损失
        total_loss, mse_loss, constraint_loss = self.compute_loss(predicted_57, target_57)
        
        # 计算指标
        metrics = self.compute_metrics(predicted_57, target_57)
        
        # 记录日志
        self.log('train_loss', total_loss, prog_bar=True)
        self.log('train_mse_loss', mse_loss)
        self.log('train_constraint_loss', constraint_loss)
        self.log('train_error', metrics['avg_error'], prog_bar=True)
        self.log('train_accuracy_5mm', metrics['accuracy_5mm'])
        
        return total_loss
    
    def validation_step(self, batch, batch_idx):
        """验证步骤"""
        input_12, target_57 = batch
        predicted_57 = self(input_12)
        
        # 计算损失
        total_loss, mse_loss, constraint_loss = self.compute_loss(predicted_57, target_57)
        
        # 计算指标
        metrics = self.compute_metrics(predicted_57, target_57)
        
        # 记录日志
        self.log('val_loss', total_loss, prog_bar=True)
        self.log('val_mse_loss', mse_loss)
        self.log('val_constraint_loss', constraint_loss)
        self.log('val_error', metrics['avg_error'], prog_bar=True)
        self.log('val_accuracy_5mm', metrics['accuracy_5mm'])
        self.log('val_f1_error', metrics['f1_error'])
        self.log('val_f2_error', metrics['f2_error'])
        self.log('val_f3_error', metrics['f3_error'])
        
        return total_loss
    
    def test_step(self, batch, batch_idx):
        """测试步骤"""
        input_12, target_57 = batch
        predicted_57 = self(input_12)
        
        # 计算指标
        metrics = self.compute_metrics(predicted_57, target_57)
        
        # 记录测试指标
        self.log('test_error', metrics['avg_error'])
        self.log('test_accuracy_5mm', metrics['accuracy_5mm'])
        self.log('test_accuracy_10mm', metrics['accuracy_10mm'])
        self.log('test_f1_error', metrics['f1_error'])
        self.log('test_f2_error', metrics['f2_error'])
        self.log('test_f3_error', metrics['f3_error'])
        
        return metrics
    
    def configure_optimizers(self):
        """配置优化器"""
        optimizer = torch.optim.Adam(
            self.parameters(), 
            lr=self.hparams.learning_rate,
            weight_decay=self.hparams.weight_decay
        )
        
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, 
            mode='min',
            factor=0.5,
            patience=10,
            verbose=True
        )
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'monitor': 'val_loss'
            }
        }

class KeypointExpansionDataModule(pl.LightningDataModule):
    """关键点扩展数据模块"""
    
    def __init__(self, batch_size=8, num_workers=4):
        super().__init__()
        self.batch_size = batch_size
        self.num_workers = num_workers
        
    def setup(self, stage=None):
        """设置数据"""
        # 加载数据
        data = np.load('expansion_training_keypoints.npz', allow_pickle=True)
        input_12 = data['input_12']
        target_57 = data['target_57']
        
        # 数据划分
        indices = np.arange(len(input_12))
        train_indices, test_indices = train_test_split(
            indices, test_size=0.2, random_state=42
        )
        train_indices, val_indices = train_test_split(
            train_indices, test_size=0.2, random_state=42
        )
        
        # 创建数据集
        self.train_dataset = KeypointExpansionDataset(
            input_12[train_indices], target_57[train_indices]
        )
        self.val_dataset = KeypointExpansionDataset(
            input_12[val_indices], target_57[val_indices]
        )
        self.test_dataset = KeypointExpansionDataset(
            input_12[test_indices], target_57[test_indices]
        )
        
        print(f"📋 数据划分:")
        print(f"   训练集: {len(self.train_dataset)} 样本")
        print(f"   验证集: {len(self.val_dataset)} 样本")
        print(f"   测试集: {len(self.test_dataset)} 样本")
    
    def train_dataloader(self):
        return DataLoader(
            self.train_dataset, 
            batch_size=self.batch_size, 
            shuffle=True,
            num_workers=self.num_workers,
            persistent_workers=True if self.num_workers > 0 else False
        )
    
    def val_dataloader(self):
        return DataLoader(
            self.val_dataset, 
            batch_size=self.batch_size, 
            shuffle=False,
            num_workers=self.num_workers,
            persistent_workers=True if self.num_workers > 0 else False
        )
    
    def test_dataloader(self):
        return DataLoader(
            self.test_dataset, 
            batch_size=self.batch_size, 
            shuffle=False,
            num_workers=self.num_workers,
            persistent_workers=True if self.num_workers > 0 else False
        )

def main():
    """主函数"""
    
    print("🎯 基于PyTorch Lightning的12→57关键点扩展网络")
    print("高效、稳定、可扩展的深度学习训练")
    print("=" * 80)
    
    # 设置随机种子
    pl.seed_everything(42)
    
    # 检查数据文件
    if not os.path.exists('expansion_training_keypoints.npz'):
        print("❌ 找不到训练数据文件: expansion_training_keypoints.npz")
        print("请先运行 build_57_keypoints_only.py 生成数据")
        return
    
    # 创建数据模块
    data_module = KeypointExpansionDataModule(batch_size=8, num_workers=2)
    
    # 创建模型
    model = LightningKeypointExpansion(
        input_dim=36,
        hidden_dim=512,
        output_dim=171,
        learning_rate=0.001,
        weight_decay=1e-5
    )
    
    print(f"🤖 模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 设置回调
    checkpoint_callback = ModelCheckpoint(
        dirpath='lightning_checkpoints',
        filename='expansion-{epoch:02d}-{val_loss:.6f}',
        monitor='val_loss',
        mode='min',
        save_top_k=3,
        save_last=True
    )
    
    early_stop_callback = EarlyStopping(
        monitor='val_loss',
        patience=20,
        mode='min',
        verbose=True
    )
    
    # 设置日志记录器
    logger = TensorBoardLogger('lightning_logs', name='keypoint_expansion')
    
    # 创建训练器 (使用CPU避免CUDA兼容性问题)
    trainer = pl.Trainer(
        max_epochs=20,       # 减少轮数用于测试
        accelerator='cpu',   # 强制使用CPU
        devices=1,           # 使用1个CPU
        logger=logger,
        callbacks=[checkpoint_callback, early_stop_callback],
        log_every_n_steps=5,
        val_check_interval=1.0,
        enable_progress_bar=True,
        enable_model_summary=True
    )
    
    print(f"🚀 开始训练...")
    print(f"   设备: {trainer.accelerator}")
    print(f"   最大轮数: {trainer.max_epochs}")
    print(f"   日志目录: {logger.log_dir}")
    
    # 训练模型
    trainer.fit(model, data_module)
    
    # 测试模型
    print(f"\n🔍 测试最佳模型...")
    trainer.test(model, data_module, ckpt_path='best')
    
    # 保存最终模型
    torch.save(model.state_dict(), 'lightning_expansion_final.pth')
    
    print(f"\n🎉 训练完成！")
    print(f"📋 生成的文件:")
    print(f"   - lightning_checkpoints/ (模型检查点)")
    print(f"   - lightning_logs/ (TensorBoard日志)")
    print(f"   - lightning_expansion_final.pth (最终模型)")
    
    print(f"\n📊 查看训练日志:")
    print(f"   tensorboard --logdir lightning_logs")
    
    print(f"\n🚀 下一步:")
    print(f"   1. 查看TensorBoard了解训练过程")
    print(f"   2. 评估扩展网络性能")
    print(f"   3. 集成到端到端57点检测系统")

if __name__ == "__main__":
    main()
