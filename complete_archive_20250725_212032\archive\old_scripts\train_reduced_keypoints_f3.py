#!/usr/bin/env python3
"""
使用减少关键点数量的F3数据集进行训练
验证"先搞好检测"的策略效果
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class ReducedKeypointsF3Dataset(Dataset):
    """使用减少关键点的F3数据集"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points
        self.augment = augment
        self.split = split
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        # 获取减少关键点的信息
        selected_indices = data.get('selected_indices', None)
        original_keypoint_count = data.get('original_keypoint_count', 19)
        reduction_percentage = data.get('reduction_percentage', '0%')
        
        print(f"📊 减少关键点数据集: {data_path}")
        print(f"📊 样本数量: {len(sample_ids)}")
        print(f"📊 点云密度: {len(point_clouds[0])} 点")
        print(f"📊 关键点: {keypoints.shape[1]}个 (原始{original_keypoint_count}个, 减少{reduction_percentage})")
        if selected_indices is not None:
            print(f"📊 选中的关键点索引: {list(selected_indices)}")
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
        
        print(f"   {split}: {len(self.sample_ids)} 样本")
    
    def __len__(self):
        return len(self.sample_ids)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        # 随机采样到目标点数 (使用简单随机采样作为基线)
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        # 保守的数据增强
        if self.augment and self.split == 'train':
            # 轻微旋转
            if np.random.random() < 0.7:
                angle = np.random.uniform(-0.08, 0.08)
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                point_cloud = point_cloud @ rotation.T
                keypoints = keypoints @ rotation.T
            
            # 小幅平移
            if np.random.random() < 0.6:
                translation = np.random.uniform(-0.4, 0.4, 3)
                point_cloud += translation
                keypoints += translation
            
            # 轻微缩放
            if np.random.random() < 0.5:
                scale = np.random.uniform(0.99, 1.01, 3)
                point_cloud *= scale
                keypoints *= scale
            
            # 轻微噪声
            if np.random.random() < 0.6:
                noise_level = np.random.choice([0.02, 0.03, 0.04])
                noise = np.random.normal(0, noise_level, point_cloud.shape)
                point_cloud += noise
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

class AdaptivePointNet(nn.Module):
    """自适应关键点数量的PointNet"""
    
    def __init__(self, num_keypoints: int):
        super(AdaptivePointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(0.3)
        
        print(f"🧠 自适应PointNet: {num_keypoints}个关键点")
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        x1 = torch.relu(self.bn1(self.conv1(x)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        
        x3_res = x3 + self.residual1(x1)
        
        x4 = torch.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = torch.relu(self.bn5(self.conv5(x4_res)))
        
        global_feat = torch.max(x5, 2)[0]
        
        x = torch.relu(self.bn_fc1(self.fc1(global_feat)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc2(self.fc2(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc3(self.fc3(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc4(self.fc4(x)))
        x = self.dropout(x)
        x = self.fc5(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

class ImprovedLoss(nn.Module):
    """改进损失函数"""
    
    def __init__(self, alpha=0.8, beta=0.2):
        super(ImprovedLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, pred, target):
        mse_loss = F.mse_loss(pred, target)
        smooth_l1_loss = F.smooth_l1_loss(pred, target)
        total_loss = self.alpha * mse_loss + self.beta * smooth_l1_loss
        return total_loss

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def train_reduced_keypoints(dataset_path, keypoint_count, epochs=150):
    """训练减少关键点的模型"""
    
    print(f"🚀 **减少关键点训练 - F3骶骨关键点检测**")
    print(f"🎯 **策略**: 使用{keypoint_count}个最稳定关键点")
    print(f"📊 **数据集**: {dataset_path}")
    print(f"📈 **目标**: 验证减少关键点的效果")
    print("=" * 80)
    
    set_seed(42)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 数据集
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    train_dataset = ReducedKeypointsF3Dataset(dataset_path, 'train', num_points=4096, 
                                            test_samples=test_samples, augment=True, seed=42)
    val_dataset = ReducedKeypointsF3Dataset(dataset_path, 'val', num_points=4096, 
                                          test_samples=test_samples, augment=False, seed=42)
    test_dataset = ReducedKeypointsF3Dataset(dataset_path, 'test', num_points=4096, 
                                           test_samples=test_samples, augment=False, seed=42)
    
    batch_size = 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    
    # 自适应模型
    model = AdaptivePointNet(num_keypoints=keypoint_count).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 模型参数: {total_params:,}")
    
    # 训练配置
    criterion = ImprovedLoss(alpha=0.8, beta=0.2)
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
    
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=12, min_lr=1e-6
    )
    
    num_epochs = epochs
    best_val_error = float('inf')
    patience = 20
    patience_counter = 0
    history = []
    min_delta = 0.005
    
    print(f"🎯 训练配置: {epochs}轮, 耐心{patience}, 最小改进{min_delta}mm")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 40)
        
        # 训练
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        for batch in train_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                        
            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue
        
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # 学习率调度
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 打印结果
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"5mm={train_metrics['within_5mm_percent']:.1f}%, 7mm={train_metrics['within_7mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"5mm={val_metrics['within_5mm_percent']:.1f}%, 7mm={val_metrics['within_7mm_percent']:.1f}%")
        print(f"学习率: {current_lr:.2e}")
        
        # 保存历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'learning_rate': current_lr
        })
        
        # 检查改进
        current_error = val_metrics['mean_distance']
        improvement = best_val_error - current_error
        
        if improvement > min_delta:
            best_val_error = current_error
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics,
                'keypoint_count': keypoint_count,
                'dataset_path': dataset_path
            }, f'best_reduced_{keypoint_count}kp_f3.pth')
            
            print(f"🎉 新最佳! 验证误差: {best_val_error:.3f}mm (改进{improvement:.3f}mm)")
            
            if best_val_error <= 5.0:
                print(f"🏆 **突破5mm目标!**")
            elif best_val_error < 7.631:
                print(f"✅ **优于保守基线!** 超越7.631mm")
            elif best_val_error < 8.543:
                print(f"✅ **优于随机基线!** 超越8.543mm")
        else:
            patience_counter += 1
            print(f"⏳ 无显著改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停触发")
            break
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    
    return model, test_loader, best_val_error, total_time, history, keypoint_count

def main():
    """主函数 - 测试不同的减少关键点策略"""
    
    print("🚀 **减少关键点策略验证实验**")
    print("🎯 **目标**: 验证'先搞好检测'的策略效果")
    print("=" * 80)
    
    # 测试不同的减少关键点配置
    configs = [
        {'dataset': 'f3_reduced_6kp_stable.npz', 'keypoints': 6, 'name': '快速验证'},
        {'dataset': 'f3_reduced_10kp_stable.npz', 'keypoints': 10, 'name': '平衡选择'},
        {'dataset': 'f3_reduced_12kp_stable.npz', 'keypoints': 12, 'name': '保守方案'},
    ]
    
    results = []
    
    for i, config in enumerate(configs, 1):
        print(f"\n{'='*80}")
        print(f"🧪 **配置 {i}/{len(configs)}**: {config['name']} ({config['keypoints']}个关键点)**")
        print(f"{'='*80}")
        
        try:
            # 训练模型
            model, test_loader, best_val_error, training_time, history, keypoint_count = train_reduced_keypoints(
                config['dataset'], config['keypoints'], epochs=150
            )
            
            print(f"\n🎯 **{config['name']}训练完成!**")
            print(f"   最佳验证误差: {best_val_error:.3f}mm")
            print(f"   训练时间: {training_time/60:.1f}分钟")
            print(f"   实际训练轮数: {len(history)}")
            
            # 保存结果
            result = {
                'config': config,
                'best_val_error': float(best_val_error),
                'training_time_minutes': float(training_time / 60),
                'actual_epochs': len(history),
                'history': history
            }
            results.append(result)
            
        except Exception as e:
            print(f"❌ 配置{config}训练失败: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    # 保存所有结果
    final_results = {
        'method': 'Reduced Keypoints Strategy',
        'results': results,
        'baselines': {
            'original_19kp_random': 8.543,
            'original_19kp_conservative': 7.631,
            'original_19kp_knn_optimized': 7.474
        }
    }
    
    with open('reduced_keypoints_results.json', 'w', encoding='utf-8') as f:
        json.dump(final_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 **所有结果已保存**: reduced_keypoints_results.json")
    
    # 总结对比
    print(f"\n🎉 **减少关键点策略验证完成!**")
    if results:
        print(f"\n📊 **性能对比总结**:")
        print(f"{'配置':<15} {'关键点':<8} {'验证误差':<10} {'训练时间':<10} {'vs原始19kp':<12}")
        print("-" * 65)
        
        for result in results:
            config = result['config']
            improvement = (7.474 - result['best_val_error']) / 7.474 * 100
            print(f"{config['name']:<15} {config['keypoints']:<8} {result['best_val_error']:<10.3f} "
                  f"{result['training_time_minutes']:<10.1f} {improvement:+.1f}%")
        
        best_result = min(results, key=lambda x: x['best_val_error'])
        print(f"\n🏆 **最佳配置**: {best_result['config']['name']}")
        print(f"🎯 **最佳验证误差**: {best_result['best_val_error']:.3f}mm")
        
        if best_result['best_val_error'] < 7.474:
            print(f"✅ **减少关键点策略成功!** 超越19关键点基线")
        else:
            print(f"💡 **策略有效但需优化** 接近19关键点性能")

if __name__ == "__main__":
    set_seed(42)
    main()
