<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="1280" height="720" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="url(#headerGrad)"/>
  <text x="640" y="50" text-anchor="middle" fill="white" 
        font-family="SimHei, Arial, sans-serif" font-size="36" font-weight="bold">
    核心创新：Penalty Dice Loss 的惩罚项机制
  </text>
  
  <!-- Main Innovation -->
  <rect x="50" y="100" width="1180" height="150" rx="15" fill="white" stroke="#ef4444" stroke-width="4"/>
  <text x="640" y="140" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="28" font-weight="bold">
    创新点：在传统Dice Loss基础上增加惩罚项
  </text>
  
  <!-- Formula comparison -->
  <rect x="100" y="170" width="480" height="60" rx="10" fill="#fef3c7" stroke="#f59e0b" stroke-width="2"/>
  <text x="340" y="195" text-anchor="middle" fill="#92400e" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    传统Dice Loss
  </text>
  <text x="340" y="220" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="14">
    L = 交叉熵损失 + (1 - Dice系数)
  </text>
  
  <!-- VS -->
  <text x="640" y="210" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="24" font-weight="bold">
    VS
  </text>
  
  <rect x="700" y="170" width="480" height="60" rx="10" fill="#f0fdf4" stroke="#22c55e" stroke-width="2"/>
  <text x="940" y="195" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    Penalty Dice Loss (我们的创新)
  </text>
  <text x="940" y="220" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="14">
    L = 交叉熵损失 + (1 - Dice系数) + MAX exp(惩罚项)
  </text>
  
  <!-- Core Innovation Detail -->
  <rect x="50" y="270" width="1180" height="200" rx="15" fill="white" stroke="#7c3aed" stroke-width="3"/>
  <text x="640" y="300" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="24" font-weight="bold">
    惩罚项的核心机制
  </text>
  
  <!-- Penalty term explanation -->
  <rect x="100" y="320" width="1080" height="120" rx="10" fill="#f3e8ff" stroke="#c084fc" stroke-width="1"/>
  <text x="640" y="350" text-anchor="middle" fill="#6b21a8" 
        font-family="SimHei, Arial, sans-serif" font-size="20" font-weight="bold">
    惩罚项 = MAX exp(αᵢ)
  </text>
  <text x="640" y="380" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="16">
    其中：αᵢ = (预测区域 ∪ 真实区域) / (预测区域 ∩ 真实区域)
  </text>
  <text x="640" y="410" text-anchor="middle" fill="#6b21a8" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    当区域完全遗漏时，αᵢ → ∞，exp(αᵢ) → ∞，产生巨大惩罚！
  </text>
  
  <!-- Three scenarios -->
  <rect x="50" y="490" width="1180" height="180" rx="15" fill="white" stroke="#10b981" stroke-width="3"/>
  <text x="640" y="520" text-anchor="middle" fill="#059669" 
        font-family="SimHei, Arial, sans-serif" font-size="22" font-weight="bold">
    三种情况下的惩罚机制
  </text>
  
  <!-- Scenario 1: Perfect detection -->
  <rect x="80" y="540" width="350" height="110" rx="10" fill="#f0fdf4" stroke="#22c55e" stroke-width="2"/>
  <text x="255" y="565" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    情况1：完美检测
  </text>
  <text x="90" y="590" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="13">
    预测区域 = 真实区域
  </text>
  <text x="90" y="610" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="13">
    αᵢ = 1，exp(αᵢ) = e ≈ 2.7
  </text>
  <text x="90" y="630" fill="#15803d" font-family="SimHei, Arial, sans-serif" font-size="13" font-weight="bold">
    惩罚很小，鼓励精确检测
  </text>
  
  <!-- Scenario 2: Partial detection -->
  <rect x="465" y="540" width="350" height="110" rx="10" fill="#fef3c7" stroke="#f59e0b" stroke-width="2"/>
  <text x="640" y="565" text-anchor="middle" fill="#d97706" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    情况2：部分检测
  </text>
  <text x="475" y="590" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="13">
    预测区域 ∩ 真实区域 > 0
  </text>
  <text x="475" y="610" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="13">
    αᵢ > 1，exp(αᵢ) 适中
  </text>
  <text x="475" y="630" fill="#d97706" font-family="SimHei, Arial, sans-serif" font-size="13" font-weight="bold">
    适度惩罚，推动改进
  </text>
  
  <!-- Scenario 3: Complete miss -->
  <rect x="850" y="540" width="350" height="110" rx="10" fill="#fef2f2" stroke="#ef4444" stroke-width="2"/>
  <text x="1025" y="565" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    情况3：完全遗漏
  </text>
  <text x="860" y="590" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="13">
    预测区域 ∩ 真实区域 = 0
  </text>
  <text x="860" y="610" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="13">
    αᵢ → ∞，exp(αᵢ) → ∞
  </text>
  <text x="860" y="630" fill="#dc2626" font-family="SimHei, Arial, sans-serif" font-size="13" font-weight="bold">
    巨大惩罚，强制检测！
  </text>
  
  <!-- Key insight -->
  <rect x="50" y="690" width="1180" height="25" rx="8" fill="#fef7ff" stroke="#a855f7" stroke-width="1"/>
  <text x="640" y="708" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    核心洞察：通过指数惩罚机制，彻底解决小区域遗漏问题，mIoU提升4.01%
  </text>
</svg>
