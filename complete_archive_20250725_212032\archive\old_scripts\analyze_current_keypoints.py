#!/usr/bin/env python3
"""
分析当前50K数据集的关键点情况
正确理解：F1、F2、F3是三个不同的解剖区域，每个都有19个关键点
"""

import numpy as np
import matplotlib.pyplot as plt
import json

def analyze_current_dataset():
    """分析当前数据集的关键点情况"""
    
    print("🔍 **当前50K数据集关键点分析**")
    print("=" * 60)
    
    # 加载数据
    data = np.load('high_quality_f3_dataset.npz', allow_pickle=True)
    
    sample_ids = data['sample_ids']
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    
    print(f"📊 数据集基本信息:")
    print(f"   样本数量: {len(sample_ids)}")
    print(f"   点云密度: {len(point_clouds[0])} 点/样本")
    print(f"   关键点数量: {keypoints.shape[1]} 个")
    print(f"   关键点维度: {keypoints.shape[2]}D")
    print(f"   数据集形状: {keypoints.shape}")
    
    # 分析关键点的空间分布
    print(f"\n🎯 **关键点空间分布分析**")
    print("=" * 40)
    
    # 计算所有关键点的统计信息
    all_keypoints = keypoints.reshape(-1, keypoints.shape[2])  # 展平所有关键点
    
    print(f"📊 整体空间范围:")
    for dim, axis in enumerate(['X', 'Y', 'Z']):
        min_val = np.min(all_keypoints[:, dim])
        max_val = np.max(all_keypoints[:, dim])
        mean_val = np.mean(all_keypoints[:, dim])
        std_val = np.std(all_keypoints[:, dim])
        
        print(f"   {axis}轴: [{min_val:8.3f}, {max_val:8.3f}] "
              f"均值={mean_val:8.3f} 标准差={std_val:6.3f}")
    
    # 分析每个关键点的位置特征
    print(f"\n📋 **各关键点详细分析**")
    print("=" * 40)
    
    keypoint_analysis = []
    
    for i in range(keypoints.shape[1]):
        kp_coords = keypoints[:, i, :]  # 所有样本的第i个关键点
        
        mean_pos = np.mean(kp_coords, axis=0)
        std_pos = np.std(kp_coords, axis=0)
        min_pos = np.min(kp_coords, axis=0)
        max_pos = np.max(kp_coords, axis=0)
        
        # 计算变异系数 (标准差/均值)，衡量相对稳定性
        cv = np.abs(std_pos / (mean_pos + 1e-8))  # 避免除零
        stability_score = 1.0 / (1.0 + np.mean(cv))  # 稳定性分数
        
        keypoint_info = {
            'index': i,
            'mean_position': mean_pos.tolist(),
            'std_position': std_pos.tolist(),
            'min_position': min_pos.tolist(),
            'max_position': max_pos.tolist(),
            'stability_score': float(stability_score),
            'coefficient_of_variation': cv.tolist()
        }
        
        keypoint_analysis.append(keypoint_info)
        
        print(f"关键点{i:2d}: 位置({mean_pos[0]:7.2f}, {mean_pos[1]:7.2f}, {mean_pos[2]:7.2f}) "
              f"标准差({std_pos[0]:.2f}, {std_pos[1]:.2f}, {std_pos[2]:.2f}) "
              f"稳定性={stability_score:.3f}")
    
    return keypoint_analysis

def analyze_keypoint_relationships():
    """分析关键点之间的关系"""
    
    print(f"\n🔗 **关键点关系分析**")
    print("=" * 40)
    
    data = np.load('high_quality_f3_dataset.npz', allow_pickle=True)
    keypoints = data['keypoints']
    
    # 计算关键点间的平均距离矩阵
    num_keypoints = keypoints.shape[1]
    distance_matrix = np.zeros((num_keypoints, num_keypoints))
    
    for sample_idx in range(keypoints.shape[0]):
        sample_kps = keypoints[sample_idx]
        for i in range(num_keypoints):
            for j in range(num_keypoints):
                if i != j:
                    dist = np.linalg.norm(sample_kps[i] - sample_kps[j])
                    distance_matrix[i, j] += dist
    
    # 平均距离
    distance_matrix /= keypoints.shape[0]
    
    print(f"📊 关键点间距离统计:")
    print(f"   最小间距: {np.min(distance_matrix[distance_matrix > 0]):.3f}")
    print(f"   最大间距: {np.max(distance_matrix):.3f}")
    print(f"   平均间距: {np.mean(distance_matrix[distance_matrix > 0]):.3f}")
    
    # 找出最近邻和最远邻
    print(f"\n🔍 关键点邻近关系:")
    for i in range(min(10, num_keypoints)):  # 只显示前10个
        distances = distance_matrix[i]
        distances[i] = np.inf  # 排除自己
        
        nearest_idx = np.argmin(distances)
        farthest_idx = np.argmax(distances[distances < np.inf])
        
        print(f"   关键点{i:2d}: 最近邻=关键点{nearest_idx:2d}(距离{distances[nearest_idx]:.2f}) "
              f"最远邻=关键点{farthest_idx:2d}(距离{distances[farthest_idx]:.2f})")
    
    return distance_matrix

def identify_keypoint_clusters():
    """识别关键点的聚类模式"""
    
    print(f"\n🎯 **关键点聚类分析**")
    print("=" * 40)
    
    data = np.load('high_quality_f3_dataset.npz', allow_pickle=True)
    keypoints = data['keypoints']
    
    # 使用第一个样本作为代表进行聚类分析
    representative_kps = keypoints[0]  # 取第一个样本
    
    print(f"📊 使用样本 {data['sample_ids'][0]} 作为代表进行聚类分析")
    
    # 简单的基于距离的聚类
    from sklearn.cluster import KMeans
    
    # 尝试不同的聚类数量
    for n_clusters in [2, 3, 4, 5]:
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        cluster_labels = kmeans.fit_predict(representative_kps)
        
        print(f"\n🔢 {n_clusters}聚类结果:")
        for cluster_id in range(n_clusters):
            cluster_points = np.where(cluster_labels == cluster_id)[0]
            print(f"   聚类{cluster_id}: 关键点{list(cluster_points)} ({len(cluster_points)}个)")
    
    return cluster_labels

def suggest_keypoint_reduction_strategies():
    """建议关键点减少策略"""
    
    print(f"\n💡 **关键点减少策略建议**")
    print("=" * 60)
    
    keypoint_analysis = analyze_current_dataset()
    
    # 按稳定性排序
    sorted_by_stability = sorted(keypoint_analysis, key=lambda x: x['stability_score'], reverse=True)
    
    print(f"\n🏆 **最稳定的关键点 (按稳定性排序)**:")
    for i, kp in enumerate(sorted_by_stability[:10]):
        print(f"   第{i+1:2d}名: 关键点{kp['index']:2d} (稳定性={kp['stability_score']:.3f})")
    
    # 建议不同的减少策略
    strategies = [
        {'name': '保留最稳定的12个关键点', 'count': 12, 'reduction': '37%'},
        {'name': '保留最稳定的10个关键点', 'count': 10, 'reduction': '47%'},
        {'name': '保留最稳定的8个关键点', 'count': 8, 'reduction': '58%'},
        {'name': '保留最稳定的6个关键点', 'count': 6, 'reduction': '68%'},
    ]
    
    print(f"\n📋 **推荐的减少策略**:")
    
    strategy_results = []
    
    for strategy in strategies:
        selected_indices = [kp['index'] for kp in sorted_by_stability[:strategy['count']]]
        selected_indices.sort()  # 按原始索引排序
        
        print(f"\n🎯 **{strategy['name']}** (减少{strategy['reduction']})")
        print(f"   选中的关键点索引: {selected_indices}")
        print(f"   训练加速预期: ~{strategy['reduction']}")
        print(f"   推荐用途: {'入门测试' if strategy['count'] <= 8 else '生产使用'}")
        
        strategy_results.append({
            'strategy': strategy,
            'selected_indices': selected_indices,
            'selected_keypoints': [sorted_by_stability[i] for i in range(strategy['count'])]
        })
    
    return strategy_results

def create_reduced_keypoint_datasets(strategy_results):
    """创建减少关键点的数据集"""
    
    print(f"\n🔧 **创建减少关键点数据集**")
    print("=" * 40)
    
    data = np.load('high_quality_f3_dataset.npz', allow_pickle=True)
    sample_ids = data['sample_ids']
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    
    created_datasets = []
    
    for strategy_result in strategy_results:
        strategy = strategy_result['strategy']
        selected_indices = strategy_result['selected_indices']
        
        # 提取选中的关键点
        reduced_keypoints = keypoints[:, selected_indices, :]
        
        # 创建文件名
        filename = f"f3_reduced_{strategy['count']}kp_stable.npz"
        
        # 保存数据集
        np.savez_compressed(
            filename,
            sample_ids=sample_ids,
            point_clouds=point_clouds,
            keypoints=reduced_keypoints,
            selected_indices=np.array(selected_indices),
            original_keypoint_count=keypoints.shape[1],
            reduction_percentage=strategy['reduction'],
            strategy_name=strategy['name']
        )
        
        print(f"✅ 已创建: {filename}")
        print(f"   原始关键点: {keypoints.shape[1]} → 减少后: {len(selected_indices)}")
        print(f"   减少比例: {strategy['reduction']}")
        
        created_datasets.append({
            'filename': filename,
            'keypoint_count': len(selected_indices),
            'selected_indices': selected_indices,
            'strategy': strategy
        })
    
    return created_datasets

def main():
    """主函数"""
    
    print("🎯 **F3骶骨关键点数据集分析**")
    print("🔍 **目标**: 了解当前19个F3关键点的分布，设计减少策略")
    print("=" * 80)
    
    try:
        # 基础分析
        keypoint_analysis = analyze_current_dataset()
        
        # 关系分析
        distance_matrix = analyze_keypoint_relationships()
        
        # 聚类分析
        cluster_labels = identify_keypoint_clusters()
        
        # 减少策略建议
        strategy_results = suggest_keypoint_reduction_strategies()
        
        # 创建减少关键点的数据集
        created_datasets = create_reduced_keypoint_datasets(strategy_results)
        
        # 保存完整分析结果
        analysis_results = {
            'dataset_info': {
                'original_keypoint_count': 19,
                'anatomical_region': 'F3 (骶骨)',
                'total_samples': len(keypoint_analysis)
            },
            'keypoint_analysis': keypoint_analysis,
            'reduction_strategies': strategy_results,
            'created_datasets': created_datasets,
            'recommendations': {
                'for_quick_testing': 'f3_reduced_6kp_stable.npz',
                'for_balanced_performance': 'f3_reduced_10kp_stable.npz', 
                'for_production': 'f3_reduced_12kp_stable.npz'
            }
        }
        
        with open('f3_keypoint_analysis_correct.json', 'w', encoding='utf-8') as f:
            json.dump(analysis_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 **完整分析结果已保存**: f3_keypoint_analysis_correct.json")
        
        print(f"\n🚀 **推荐的下一步行动**")
        print("=" * 50)
        print("1. 🎯 **快速验证**: 先用6个关键点测试 (68%减少)")
        print("   - 文件: f3_reduced_6kp_stable.npz")
        print("   - 预期: 训练速度大幅提升，精度可能有所下降")
        print()
        print("2. 🎯 **平衡选择**: 使用10个关键点 (47%减少)")
        print("   - 文件: f3_reduced_10kp_stable.npz") 
        print("   - 预期: 训练速度显著提升，精度损失较小")
        print()
        print("3. 🎯 **保守方案**: 使用12个关键点 (37%减少)")
        print("   - 文件: f3_reduced_12kp_stable.npz")
        print("   - 预期: 训练速度适度提升，精度基本保持")
        
        return analysis_results
        
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    results = main()
