#!/usr/bin/env python3
"""
Create F3 Dataset - Simple Version

Create F3 single component dataset with minimal operations to avoid segfaults.
"""

import numpy as np
import pandas as pd
from pathlib import Path
import h5py
import json
import struct
from sklearn.model_selection import train_test_split

def load_annotation_file(csv_path: str):
    """Load annotation CSV file with proper encoding"""
    try:
        df = pd.read_csv(csv_path, encoding='gbk')
    except:
        try:
            df = pd.read_csv(csv_path, encoding='utf-8')
        except:
            df = pd.read_csv(csv_path, encoding='latin-1')
    
    keypoints = df[['X', 'Y', 'Z']].values
    labels = df['label'].values.tolist()
    
    return keypoints, labels

def read_stl_binary_safe(stl_path: str, max_triangles: int = 50000):
    """Safe binary STL reader with limits to avoid memory issues"""
    try:
        with open(stl_path, 'rb') as f:
            # Skip header (80 bytes)
            f.read(80)
            
            # Read number of triangles (4 bytes)
            num_triangles = struct.unpack('<I', f.read(4))[0]
            
            # Limit triangles to avoid memory issues
            triangles_to_read = min(num_triangles, max_triangles)
            
            # Read vertices
            vertices = []
            
            for i in range(triangles_to_read):
                # Skip normal vector (3 floats = 12 bytes)
                f.read(12)
                
                # Read 3 vertices (9 floats = 36 bytes)
                for j in range(3):
                    x, y, z = struct.unpack('<fff', f.read(12))
                    vertices.append([x, y, z])
                
                # Skip attribute (2 bytes)
                f.read(2)
            
            vertices = np.array(vertices)
            
            # Simple deduplication
            if len(vertices) > 1000:
                # Sample to reduce size
                indices = np.random.choice(len(vertices), 1000, replace=False)
                vertices = vertices[indices]
            
            return vertices
            
    except Exception as e:
        print(f"      ❌ STL读取失败: {e}")
        return None

def process_f3_sample_safe(sample_id: str):
    """Safely process F3 sample"""
    
    print(f"   🔧 处理F3样本: {sample_id}")
    
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    annotations_dir = data_dir / "annotations"
    stl_dir = data_dir / "stl_models"
    
    # Load annotation
    csv_file = annotations_dir / f"{sample_id}-Table-XYZ.CSV"
    
    if not csv_file.exists():
        print(f"      ❌ 标注文件不存在")
        return None
    
    try:
        keypoints, labels = load_annotation_file(str(csv_file))
    except Exception as e:
        print(f"      ❌ 标注加载失败: {e}")
        return None
    
    # Extract F3 keypoints
    f3_indices = [i for i, label in enumerate(labels) if label.startswith('F_3')]
    
    if not f3_indices:
        print(f"      ❌ 没有F3关键点")
        return None
    
    f3_keypoints = keypoints[f3_indices]
    f3_labels = [labels[i] for i in f3_indices]
    
    # Load F3 STL
    f3_stl_file = stl_dir / f"{sample_id}-F_3.stl"
    
    if not f3_stl_file.exists():
        print(f"      ❌ F3 STL文件不存在")
        return None
    
    f3_vertices = read_stl_binary_safe(str(f3_stl_file))
    
    if f3_vertices is None:
        print(f"      ❌ F3 STL读取失败")
        return None
    
    # Simple quality check
    distances = []
    for kp in f3_keypoints:
        dists = np.linalg.norm(f3_vertices - kp, axis=1)
        min_dist = np.min(dists)
        distances.append(min_dist)
    
    mean_distance = np.mean(distances)
    within_1mm = np.sum(np.array(distances) <= 1.0) / len(distances) * 100
    
    print(f"      ✅ F3关键点: {len(f3_keypoints)}, 点云: {len(f3_vertices)}")
    print(f"      📊 表面距离: {mean_distance:.2f}mm, ≤1mm: {within_1mm:.1f}%")
    
    return {
        'sample_id': sample_id,
        'f3_keypoints': f3_keypoints,
        'f3_labels': f3_labels,
        'f3_indices': f3_indices,
        'point_cloud': f3_vertices,
        'mean_surface_distance': mean_distance,
        'within_1mm_percent': within_1mm
    }

def create_f3_dataset_simple():
    """Create F3 dataset with simple processing"""
    
    print("🏗️ **创建F3单部件数据集 (简化版)**")
    print("🎯 **基于验证的优秀STL-CSV对齐质量**")
    print("=" * 80)
    
    # Get valid XYZ samples
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    annotations_dir = data_dir / "annotations"
    
    xyz_files = list(annotations_dir.glob("*-Table-XYZ.CSV"))
    excluded_samples = {'600025', '600026', '600027'}
    
    valid_samples = []
    for csv_file in xyz_files:
        sample_id = csv_file.stem.split('-')[0]
        if sample_id not in excluded_samples:
            valid_samples.append(sample_id)
    
    print(f"📊 数据集统计:")
    print(f"   有效XYZ样本: {len(valid_samples)}")
    print(f"   目标: F3部件 (19个关键点)")
    
    # Process samples in small batches
    processed_samples = []
    failed_samples = []
    
    # Process first 20 samples to test
    test_samples = valid_samples[:20]
    
    for i, sample_id in enumerate(test_samples):
        print(f"\n进度: {i+1}/{len(test_samples)}")
        
        try:
            result = process_f3_sample_safe(sample_id)
            if result:
                processed_samples.append(result)
            else:
                failed_samples.append(sample_id)
        except Exception as e:
            print(f"      ❌ 处理失败: {e}")
            failed_samples.append(sample_id)
    
    print(f"\n📋 **F3数据集处理结果**:")
    print(f"   成功处理: {len(processed_samples)} 样本")
    print(f"   处理失败: {len(failed_samples)} 样本")
    
    if len(processed_samples) == 0:
        print(f"❌ 没有成功处理的样本")
        return None
    
    # Calculate statistics
    all_distances = [s['mean_surface_distance'] for s in processed_samples]
    all_within_1mm = [s['within_1mm_percent'] for s in processed_samples]
    
    print(f"\n📊 **F3数据集质量**:")
    print(f"   平均表面距离: {np.mean(all_distances):.2f}±{np.std(all_distances):.2f}mm")
    print(f"   平均1mm精度: {np.mean(all_within_1mm):.1f}±{np.std(all_within_1mm):.1f}%")
    
    return processed_samples

def save_f3_dataset_simple(processed_samples, output_dir="F3SingleComponent"):
    """Save F3 dataset simply"""
    
    print(f"\n💾 **保存F3数据集: {output_dir}**")
    
    if not processed_samples:
        print("❌ 没有可保存的样本")
        return
    
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Simple split
    sample_ids = [s['sample_id'] for s in processed_samples]
    
    if len(sample_ids) >= 3:
        train_size = int(len(sample_ids) * 0.7)
        val_size = int(len(sample_ids) * 0.15)
        
        train_ids = sample_ids[:train_size]
        val_ids = sample_ids[train_size:train_size+val_size]
        test_ids = sample_ids[train_size+val_size:]
    else:
        # Too few samples, put all in train
        train_ids = sample_ids
        val_ids = []
        test_ids = []
    
    splits = {'train': train_ids, 'val': val_ids, 'test': test_ids}
    
    print(f"   数据划分: 训练{len(train_ids)}, 验证{len(val_ids)}, 测试{len(test_ids)}")
    
    # Create directories
    for split_name in ['train', 'val', 'test']:
        (output_path / split_name).mkdir(exist_ok=True)
    
    # Save samples
    for sample in processed_samples:
        sample_id = sample['sample_id']
        
        # Determine split
        if sample_id in train_ids:
            split = 'train'
        elif sample_id in val_ids:
            split = 'val'
        else:
            split = 'test'
        
        # Save to H5 file
        output_file = output_path / split / f"{sample_id}.h5"
        
        try:
            with h5py.File(output_file, 'w') as f:
                f.create_dataset('keypoints', data=sample['f3_keypoints'])
                f.create_dataset('point_cloud', data=sample['point_cloud'].T)
                f.attrs['sample_id'] = sample_id
                f.attrs['component'] = 'F3'
                f.attrs['keypoints_count'] = len(sample['f3_keypoints'])
                f.attrs['mean_surface_distance'] = sample['mean_surface_distance']
                f.attrs['within_1mm_percent'] = sample['within_1mm_percent']
        except Exception as e:
            print(f"      ❌ 保存样本 {sample_id} 失败: {e}")
    
    # Save metadata
    metadata = {
        'dataset_name': 'F3SingleComponent',
        'component': 'F3_only',
        'total_samples': len(processed_samples),
        'keypoints_per_sample': 19,
        'splits': {k: len(v) for k, v in splits.items()},
        'quality_metrics': {
            'mean_surface_distance': float(np.mean([s['mean_surface_distance'] for s in processed_samples])),
            'mean_within_1mm_percent': float(np.mean([s['within_1mm_percent'] for s in processed_samples]))
        }
    }
    
    with open(output_path / 'dataset_metadata.json', 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"✅ F3数据集保存完成: {output_path}")
    
    return output_path

def main():
    """Main function"""
    
    try:
        # Create F3 dataset
        processed_samples = create_f3_dataset_simple()
        
        if processed_samples:
            # Save dataset
            dataset_path = save_f3_dataset_simple(processed_samples)
            
            print(f"\n🎉 **F3单部件数据集创建成功!**")
            print(f"📂 数据集路径: {dataset_path}")
            print(f"📊 样本数量: {len(processed_samples)}")
            print(f"🎯 关键点数: 19个F3解剖关键点")
            print(f"💎 基于验证的优秀STL-CSV对齐质量")
            
            return dataset_path
        else:
            print(f"❌ F3数据集创建失败")
            return None
            
    except Exception as e:
        print(f"❌ 创建过程出错: {e}")
        return None

if __name__ == "__main__":
    main()
