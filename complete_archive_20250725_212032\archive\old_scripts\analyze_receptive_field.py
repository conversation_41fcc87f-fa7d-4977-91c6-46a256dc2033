#!/usr/bin/env python3
"""
分析感受野对关键点检测的影响
特别是对F3-13 (Z最高点) 的影响
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import torch
import torch.nn as nn

def visualize_receptive_field_concept():
    """可视化感受野概念"""
    
    # 模拟一个F3骶骨点云
    np.random.seed(42)
    
    # 创建骶骨形状的点云
    theta = np.linspace(0, 2*np.pi, 100)
    z = np.linspace(0, 50, 50)
    
    points = []
    for z_val in z:
        # 骶骨形状：上宽下窄
        radius = 20 - z_val * 0.3
        n_points = max(10, int(50 - z_val * 0.5))
        
        for i in range(n_points):
            angle = np.random.uniform(0, 2*np.pi)
            r = np.random.uniform(0, radius)
            x = r * np.cos(angle) + np.random.normal(0, 1)
            y = r * np.sin(angle) + np.random.normal(0, 1)
            z_noise = z_val + np.random.normal(0, 0.5)
            points.append([x, y, z_noise])
    
    points = np.array(points)
    
    # F3-13应该在Z最高点附近
    z_max_idx = np.argmax(points[:, 2])
    f3_13_true = points[z_max_idx] + np.array([2, 1, 1])  # 稍微偏移
    
    # 模拟其他一些关键点
    other_keypoints = [
        points[np.argmin(points[:, 2])] + np.array([0, 0, -2]),  # F3-18 (Z最低)
        np.array([np.min(points[:, 0])-2, 0, 25]),  # 左边界
        np.array([np.max(points[:, 0])+2, 0, 25]),  # 右边界
    ]
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 小感受野 (半径5mm)
    ax1 = axes[0, 0]
    
    # 找到F3-13附近5mm内的点
    distances = np.linalg.norm(points - f3_13_true, axis=1)
    small_rf_mask = distances <= 5
    
    ax1.scatter(points[:, 0], points[:, 2], c='lightgray', s=10, alpha=0.5, label='All Points')
    ax1.scatter(points[small_rf_mask, 0], points[small_rf_mask, 2], 
               c='red', s=30, alpha=0.8, label='Small RF (5mm)')
    ax1.scatter(f3_13_true[0], f3_13_true[2], c='blue', s=100, marker='*', 
               label='F3-13 Target')
    
    # 画感受野范围
    circle = plt.Circle((f3_13_true[0], f3_13_true[2]), 5, 
                       fill=False, color='red', linestyle='--', linewidth=2)
    ax1.add_patch(circle)
    
    ax1.set_xlabel('X (mm)')
    ax1.set_ylabel('Z (mm)')
    ax1.set_title('Small Receptive Field (5mm)\nLimited Context')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.axis('equal')
    
    # 2. 大感受野 (半径15mm)
    ax2 = axes[0, 1]
    
    large_rf_mask = distances <= 15
    
    ax2.scatter(points[:, 0], points[:, 2], c='lightgray', s=10, alpha=0.5, label='All Points')
    ax2.scatter(points[large_rf_mask, 0], points[large_rf_mask, 2], 
               c='green', s=20, alpha=0.8, label='Large RF (15mm)')
    ax2.scatter(f3_13_true[0], f3_13_true[2], c='blue', s=100, marker='*', 
               label='F3-13 Target')
    
    # 画感受野范围
    circle = plt.Circle((f3_13_true[0], f3_13_true[2]), 15, 
                       fill=False, color='green', linestyle='--', linewidth=2)
    ax2.add_patch(circle)
    
    ax2.set_xlabel('X (mm)')
    ax2.set_ylabel('Z (mm)')
    ax2.set_title('Large Receptive Field (15mm)\nRich Context')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.axis('equal')
    
    # 3. 感受野大小对比
    ax3 = axes[1, 0]
    
    rf_sizes = [3, 5, 8, 10, 15, 20]
    context_points = []
    
    for rf_size in rf_sizes:
        mask = distances <= rf_size
        context_points.append(np.sum(mask))
    
    ax3.plot(rf_sizes, context_points, 'bo-', linewidth=2, markersize=8)
    ax3.set_xlabel('Receptive Field Size (mm)')
    ax3.set_ylabel('Number of Context Points')
    ax3.set_title('Context Points vs Receptive Field Size')
    ax3.grid(True, alpha=0.3)
    
    # 标注关键点
    ax3.axvline(x=5, color='red', linestyle='--', alpha=0.7, label='Small RF')
    ax3.axvline(x=15, color='green', linestyle='--', alpha=0.7, label='Large RF')
    ax3.legend()
    
    # 4. 为什么F3-13需要大感受野
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    explanation_text = """
Why F3-13 (Z-max) Needs Large Receptive Field:

Problem with Small RF (5mm):
• Only sees 3-5 nearby points
• Cannot distinguish global maximum
• May find local peaks instead
• Lacks anatomical context

Benefits of Large RF (15mm):
• Sees 20-30 context points
• Can compare with surrounding area
• Identifies true global maximum
• Understands anatomical structure

F3-13 Specific Issues:
• Z-max is a GLOBAL property
• Needs to compare with entire region
• Small RF sees only local neighborhood
• Large RF captures anatomical context

Current Model Analysis:
• Basic PointNet: Limited RF growth
• Conv1D layers: Local operations
• Need: Multi-scale feature aggregation
• Solution: Larger kernels or attention

Improvement Strategies:
1. Increase kernel sizes in conv layers
2. Add dilated convolutions
3. Use attention mechanisms
4. Multi-scale feature fusion
5. Global context integration
"""
    
    ax4.text(0.05, 0.95, explanation_text, transform=ax4.transAxes, fontsize=10,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    
    plt.suptitle('Receptive Field Analysis for F3-13 (Z-Maximum Point)\n'
                'Why Global Context Matters for Extreme Points', 
                fontsize=16, fontweight='bold')
    plt.tight_layout(rect=[0, 0, 1, 0.93])
    
    filename = 'receptive_field_analysis.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 感受野分析保存: {filename}")
    plt.close()

class LargeReceptiveFieldPointNet(nn.Module):
    """增大感受野的PointNet架构"""
    
    def __init__(self, input_dim=3, num_keypoints=19):
        super().__init__()
        
        # 第一层：大卷积核增加初始感受野
        self.conv1 = nn.Conv1d(input_dim, 64, kernel_size=3, padding=1)  # 增大kernel
        self.conv2 = nn.Conv1d(64, 128, kernel_size=3, padding=1)
        
        # 膨胀卷积增加感受野
        self.dilated_conv1 = nn.Conv1d(128, 256, kernel_size=3, padding=2, dilation=2)
        self.dilated_conv2 = nn.Conv1d(256, 512, kernel_size=3, padding=4, dilation=4)
        
        # 全局特征
        self.global_conv = nn.Conv1d(512, 1024, kernel_size=1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 多尺度特征融合
        self.multi_scale_fusion = nn.Conv1d(64 + 128 + 256 + 512 + 1024, 512, 1)
        self.fusion_bn = nn.BatchNorm1d(512)
        
        # 热力图回归头
        self.heatmap_head = nn.Sequential(
            nn.Conv1d(512, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Conv1d(256, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            
            nn.Conv1d(128, num_keypoints, 1)
        )
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        # x: [B, 3, N]
        
        # 多尺度特征提取
        x1 = torch.relu(self.bn1(self.conv1(x)))           # [B, 64, N]
        x2 = torch.relu(self.bn2(self.conv2(x1)))          # [B, 128, N]
        x3 = torch.relu(self.bn3(self.dilated_conv1(x2)))  # [B, 256, N] - 膨胀卷积
        x4 = torch.relu(self.bn4(self.dilated_conv2(x3)))  # [B, 512, N] - 更大膨胀
        x5 = self.bn5(self.global_conv(x4))                # [B, 1024, N]
        
        # 多尺度特征融合
        multi_scale = torch.cat([x1, x2, x3, x4, x5], dim=1)  # [B, 64+128+256+512+1024, N]
        fused = torch.relu(self.fusion_bn(self.multi_scale_fusion(multi_scale)))
        
        # 应用dropout
        fused = self.dropout(fused)
        
        # 生成热力图
        heatmaps = self.heatmap_head(fused)  # [B, 19, N]
        
        return heatmaps

def compare_receptive_field_architectures():
    """对比不同感受野架构的理论分析"""
    
    print("\n🔍 感受野架构对比分析:")
    print("=" * 60)
    
    architectures = {
        "Original PointNet": {
            "layers": ["Conv1d(3,64,1)", "Conv1d(64,128,1)", "Conv1d(128,256,1)", 
                      "Conv1d(256,512,1)", "Conv1d(512,1024,1)"],
            "effective_rf": "1 point (no spatial aggregation)",
            "pros": ["简单", "快速", "参数少"],
            "cons": ["感受野极小", "无空间上下文", "难以处理全局特征"]
        },
        
        "Large Kernel PointNet": {
            "layers": ["Conv1d(3,64,3)", "Conv1d(64,128,3)", "Conv1d(128,256,3)", 
                      "Conv1d(256,512,3)", "Conv1d(512,1024,3)"],
            "effective_rf": "~5-7 points",
            "pros": ["增加局部上下文", "更好的特征聚合"],
            "cons": ["仍然有限", "参数增加"]
        },
        
        "Dilated Conv PointNet": {
            "layers": ["Conv1d(3,64,1)", "Conv1d(64,128,3)", "DilatedConv1d(128,256,3,d=2)", 
                      "DilatedConv1d(256,512,3,d=4)", "Conv1d(512,1024,1)"],
            "effective_rf": "~15-20 points",
            "pros": ["大感受野", "参数效率高", "多尺度特征"],
            "cons": ["实现复杂度增加"]
        },
        
        "Multi-Scale Fusion": {
            "layers": ["多尺度特征提取", "特征融合", "全局上下文集成"],
            "effective_rf": "全局 (所有点)",
            "pros": ["最大感受野", "丰富上下文", "适合极值检测"],
            "cons": ["计算复杂", "内存需求大"]
        }
    }
    
    for name, info in architectures.items():
        print(f"\n{name}:")
        print(f"   架构: {' -> '.join(info['layers'])}")
        print(f"   有效感受野: {info['effective_rf']}")
        print(f"   优点: {', '.join(info['pros'])}")
        print(f"   缺点: {', '.join(info['cons'])}")
    
    print(f"\n💡 针对F3-13的建议:")
    print("1. 使用膨胀卷积增加感受野")
    print("2. 多尺度特征融合")
    print("3. 全局上下文集成")
    print("4. 注意力机制辅助")

def create_implementation_guide():
    """创建实现指南"""
    
    print(f"\n🛠️ 感受野改进实现指南:")
    print("=" * 60)
    
    guide = """
1. 立即可行的改进 (简单):
   • 将Conv1d的kernel_size从1改为3
   • 添加padding保持尺寸不变
   • 预期改进: 小幅提升局部特征

2. 中等难度改进 (推荐):
   • 添加膨胀卷积层
   • 使用不同的dilation率 (2, 4, 8)
   • 多尺度特征融合
   • 预期改进: 显著提升F3-13性能

3. 高级改进 (最佳):
   • 实现注意力机制
   • 全局特征池化
   • 自适应感受野
   • 预期改进: 大幅提升所有关键点

实现优先级:
1. 先试膨胀卷积 (性价比最高)
2. 再加多尺度融合
3. 最后考虑注意力机制

代码修改建议:
• 修改basic_19keypoints_system.py
• 替换BasicHeatmapPointNet19
• 使用LargeReceptiveFieldPointNet
"""
    
    print(guide)

def main():
    """主函数"""
    print("🔍 感受野分析 - 解决F3-13问题的关键")
    print("=" * 60)
    
    # 可视化感受野概念
    visualize_receptive_field_concept()
    
    # 对比不同架构
    compare_receptive_field_architectures()
    
    # 实现指南
    create_implementation_guide()
    
    print(f"\n🎯 总结:")
    print("✅ 感受野是解决F3-13问题的关键")
    print("✅ 当前模型感受野太小，无法识别全局最大值")
    print("✅ 膨胀卷积是最实用的解决方案")
    print("✅ 多尺度特征融合可以进一步提升性能")
    
    print(f"\n💡 下一步行动:")
    print("1. 实现LargeReceptiveFieldPointNet")
    print("2. 重新训练19关键点模型")
    print("3. 重点观察F3-13的性能改进")
    print("4. 对比感受野改进前后的效果")

if __name__ == "__main__":
    main()
