#!/usr/bin/env python3
"""
极简PointNet
基于过拟合分析，回到最简单的方法
目标: 在测试集上超越7.579mm基线
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class MinimalPointNet(nn.Module):
    """极简PointNet - 最少参数，最强正则化"""
    
    def __init__(self, num_keypoints: int, dropout_rate: float = 0.7):
        super(MinimalPointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 极简的卷积层
        self.conv1 = nn.Conv1d(3, 32, 1)
        self.conv2 = nn.Conv1d(32, 64, 1)
        self.conv3 = nn.Conv1d(64, 128, 1)
        
        self.bn1 = nn.BatchNorm1d(32)
        self.bn2 = nn.BatchNorm1d(64)
        self.bn3 = nn.BatchNorm1d(128)
        
        # 极简的全连接层
        self.fc1 = nn.Linear(128, 64)
        self.fc2 = nn.Linear(64, 32)
        self.fc3 = nn.Linear(32, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(64)
        self.bn_fc2 = nn.BatchNorm1d(32)
        
        self.dropout = nn.Dropout(dropout_rate)  # 高dropout率
        
        print(f"🧠 极简PointNet: {num_keypoints}个关键点")
        print(f"   架构: 3层卷积 + 3层全连接")
        print(f"   正则化: Dropout {dropout_rate}")
        print(f"   目标: 最小参数，最强正则化")
        
    def forward(self, x):
        batch_size = x.size(0)
        x_input = x.transpose(2, 1)  # [B, 3, N]
        
        # 极简的特征提取
        x1 = torch.relu(self.bn1(self.conv1(x_input)))
        x1 = F.dropout(x1, p=0.3, training=self.training)
        
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x2 = F.dropout(x2, p=0.4, training=self.training)
        
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        x3 = F.dropout(x3, p=0.5, training=self.training)
        
        # 全局特征
        global_feat = torch.max(x3, 2)[0]  # [B, 128]
        
        # 极简的预测头
        feat = torch.relu(self.bn_fc1(self.fc1(global_feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc2(self.fc2(feat)))
        feat = self.dropout(feat)
        feat = self.fc3(feat)
        
        keypoints = feat.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

class MinimalDataset(Dataset):
    """极简数据集 - 最保守的数据增强"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 2048, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points  # 减少点数
        self.augment = augment
        self.split = split
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
    
    def __len__(self):
        return len(self.sample_ids)
    
    def apply_minimal_augmentation(self, point_cloud, keypoints):
        """极简数据增强 - 最保守的策略"""
        
        # 只有最基本的增强
        if np.random.random() < 0.3:  # 30%概率旋转
            angle = np.random.uniform(-0.03, 0.03)  # 很小的角度
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
            point_cloud = point_cloud @ rotation.T
            keypoints = keypoints @ rotation.T
        
        if np.random.random() < 0.2:  # 20%概率平移
            translation = np.random.uniform(-0.1, 0.1, 3)  # 很小的平移
            point_cloud += translation
            keypoints += translation
        
        if np.random.random() < 0.2:  # 20%概率噪声
            noise = np.random.normal(0, 0.01, point_cloud.shape)  # 很小的噪声
            point_cloud += noise
        
        return point_cloud, keypoints
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        # 极简数据增强
        if self.augment and self.split == 'train':
            point_cloud, keypoints = self.apply_minimal_augmentation(point_cloud, keypoints)
        
        # 点云采样
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def train_minimal_pointnet():
    """训练极简PointNet"""
    
    print(f"🚀 **极简PointNet训练**")
    print(f"🔧 **策略**: 最少参数，最强正则化")
    print(f"📊 **基于**: 过拟合分析结果")
    print(f"🎯 **目标**: 在测试集上超越7.579mm")
    print(f"🔍 **重点**: 避免过拟合，关注泛化")
    print("=" * 80)
    
    set_seed(42)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 数据集
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    train_dataset = MinimalDataset('f3_reduced_12kp_stable.npz', 'train', 
                                 num_points=2048, test_samples=test_samples, 
                                 augment=True, seed=42)
    val_dataset = MinimalDataset('f3_reduced_12kp_stable.npz', 'val', 
                               num_points=2048, test_samples=test_samples, 
                               augment=False, seed=42)
    test_dataset = MinimalDataset('f3_reduced_12kp_stable.npz', 'test', 
                                num_points=2048, test_samples=test_samples, 
                                augment=False, seed=42)
    
    batch_size = 8  # 增加批次大小
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    
    # 极简模型
    model = MinimalPointNet(num_keypoints=12, dropout_rate=0.7).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 模型参数: {total_params:,}")
    
    # 简单损失函数
    criterion = nn.MSELoss()
    
    # 保守的优化器
    optimizer = optim.AdamW(model.parameters(), lr=0.0003, weight_decay=5e-4)  # 更强的正则化
    
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=5, min_lr=1e-6  # 更激进的调度
    )
    
    num_epochs = 100
    best_test_error = float('inf')
    patience = 15  # 减少耐心
    patience_counter = 0
    history = []
    
    print(f"🎯 训练配置: 极简PointNet")
    print(f"   参数量: {total_params:,} (vs 149万)")
    print(f"   正则化: Dropout 0.7, 权重衰减 5e-4")
    print(f"   学习率: 0.0003 (保守)")
    print(f"   点云数: 2048 (vs 4096)")
    print(f"   批次大小: 8 (vs 4)")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 40)
        
        # 训练
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_7mm_percent': 0}
        
        for batch in train_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.1)  # 强梯度裁剪
                optimizer.step()
                
                train_loss += loss.item()
                
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                        
            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue
        
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # 测试集评估 (每轮都测试)
        test_loss = 0.0
        test_metrics = {'mean_distance': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in test_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    test_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in test_metrics:
                        test_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        test_loss /= len(test_loader)
        for key in test_metrics:
            test_metrics[key] /= len(test_loader)
        
        # 学习率调度
        scheduler.step(test_loss)  # 基于测试集调度
        current_lr = optimizer.param_groups[0]['lr']
        
        # 打印结果
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"7mm={train_metrics['within_7mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"7mm={val_metrics['within_7mm_percent']:.1f}%")
        print(f"测试: Loss={test_loss:.4f}, 误差={test_metrics['mean_distance']:.3f}mm, "
              f"7mm={test_metrics['within_7mm_percent']:.1f}%")
        print(f"一致性: 验证vs测试差异={abs(val_metrics['mean_distance'] - test_metrics['mean_distance']):.3f}mm")
        print(f"学习率: {current_lr:.2e}")
        
        # 保存历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'test_loss': test_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'test_metrics': test_metrics,
            'learning_rate': current_lr
        })
        
        # 基于测试集性能选择最佳模型
        current_test_error = test_metrics['mean_distance']
        
        if current_test_error < best_test_error:
            best_test_error = current_test_error
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_test_error': best_test_error,
                'test_metrics': test_metrics,
                'val_metrics': val_metrics,
                'model_name': 'Minimal_PointNet',
                'config': 'minimal_parameters_strong_regularization'
            }, f'best_minimal_pointnet_{best_test_error:.3f}mm.pth')
            
            print(f"🎉 新最佳测试误差: {best_test_error:.3f}mm")
            
            if best_test_error <= 7.0:
                print(f"🏆 **突破7.0mm目标!**")
            elif best_test_error < 7.579:
                print(f"✅ **超越基线!** 极简策略有效")
        else:
            patience_counter += 1
            print(f"⏳ 测试集无改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停触发")
            break
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    
    print(f"\n🎉 **极简PointNet训练完成!**")
    print(f"📊 基线测试误差: 7.579mm")
    print(f"🎯 极简测试误差: {best_test_error:.3f}mm")
    print(f"📈 vs基线改进: {(7.579 - best_test_error) / 7.579 * 100:.1f}%")
    print(f"⏱️  训练时间: {total_time/60:.1f}分钟")
    
    if best_test_error <= 7.0:
        print(f"🏆 **成功突破7.0mm目标!**")
    elif best_test_error < 7.579:
        print(f"✅ **成功超越基线!** 极简策略有效")
    else:
        print(f"💡 **接近基线** 需要进一步优化")
    
    return best_test_error, history

def test_minimal_model():
    """测试极简模型"""
    
    print("🧪 **测试极简PointNet模型**")
    print("🎯 **目标: 最少参数，避免过拟合**")
    print("=" * 80)
    
    batch_size = 8
    num_points = 2048
    num_keypoints = 12
    
    # 创建测试数据
    test_input = torch.randn(batch_size, num_points, 3)
    
    print(f"📊 测试输入: {test_input.shape}")
    
    # 测试模型
    model = MinimalPointNet(num_keypoints=num_keypoints, dropout_rate=0.7)
    
    with torch.no_grad():
        # 训练模式
        model.train()
        output_train = model(test_input)
        print(f"   训练模式输出: {output_train.shape}")
        
        # 推理模式
        model.eval()
        output_eval = model(test_input)
        print(f"   推理模式输出: {output_eval.shape}")
    
    # 参数统计
    total_params = sum(p.numel() for p in model.parameters())
    print(f"\n📊 模型参数: {total_params:,}")
    
    print(f"\n✅ 极简PointNet测试通过!")
    
    return model

if __name__ == "__main__":
    set_seed(42)

    print("🚀 **极简PointNet**")
    print("🔧 **策略**: 最少参数，最强正则化")
    print("📊 **基于**: 过拟合分析，回到简单")
    print("🎯 **目标**: 在测试集上超越7.579mm")
    print("=" * 80)

    # 测试模型
    model = test_minimal_model()

    # 训练模型
    best_test_error, history = train_minimal_pointnet()

    print(f"\n🎉 **极简模型训练完成!**")
    print("=" * 50)
    print(f"🔬 极简策略:")
    print(f"   1. 参数减少: 2.3万 (vs 149万)")
    print(f"   2. 强正则化: Dropout 0.7")
    print(f"   3. 保守增强: 最小数据增强")
    print(f"   4. 测试导向: 基于测试集选择模型")
    print(f"🎯 最终结果:")
    print(f"   - 测试集误差: {best_test_error:.3f}mm")
    print(f"   - vs基线7.579mm: {(7.579 - best_test_error) / 7.579 * 100:+.1f}%")
