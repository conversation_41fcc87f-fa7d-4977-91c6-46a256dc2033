# PelvisKP Dataset Paper - LaTeX Template

这是一个符合Scientific Data期刊格式的骨盆关键点数据集论文LaTeX模板。

## 文件结构

```
├── pelvic_dataset_paper.tex    # 主论文文件
├── references.bib              # 参考文献文件
├── README_LaTeX.md            # 本说明文件
└── figures/                   # 图片文件夹（需要创建）
    ├── dataset_overview.png
    ├── annotation_quality.png
    └── benchmark_results.png
```

## 编译方法

### 方法1：使用pdflatex + bibtex
```bash
pdflatex pelvic_dataset_paper.tex
bibtex pelvic_dataset_paper
pdflatex pelvic_dataset_paper.tex
pdflatex pelvic_dataset_paper.tex
```

### 方法2：使用latexmk（推荐）
```bash
latexmk -pdf pelvic_dataset_paper.tex
```

## 需要填写的内容

### 1. 基本信息
- [ ] 作者姓名和单位
- [ ] 通讯作者邮箱
- [ ] 数据集具体数量（XXX处）
- [ ] 医院/机构名称
- [ ] IRB协议号
- [ ] 扫描设备型号和参数

### 2. 关键点定义
- [ ] 完善57个关键点的具体定义
- [ ] 每个关键点的临床意义
- [ ] 标注标准和参考文献

### 3. 实验数据
- [ ] 填写所有表格中的具体数值
- [ ] ICC值和置信区间
- [ ] MRE和标准差
- [ ] 基准实验结果

### 4. 图片
- [ ] 创建figures文件夹
- [ ] 准备数据集概览图
- [ ] 标注质量分析图
- [ ] 基准实验结果图

## 表格说明

### Table 1: Dataset Statistics
包含数据集的基本统计信息：
- 患者数量和人口统计学特征
- 图像参数
- 关键点分布

### Table 2: Reliability Analysis
包含标注质量评估：
- Inter-rater reliability (评估者间信度)
- Intra-rater reliability (评估者内信度)
- ICC值和MRE

### Table 3: Benchmark Results
包含基准实验结果：
- 不同方法的性能对比
- 各个解剖区域的结果
- 传统方法vs深度学习方法

## 图片说明

### Figure 1: Dataset Overview
- 展示三个解剖区域的典型样本
- 标注关键点的可视化
- 不同颜色表示不同类别的关键点

### Figure 2: Annotation Quality Analysis
- Inter-rater reliability分析
- 误差分布图
- Bland-Altman一致性图

### Figure 3: Benchmark Results
- 不同方法的性能对比
- 成功检测率曲线
- 定性结果展示

## Scientific Data期刊要求

### 格式要求
- 字体：10pt
- 页边距：2.5cm
- 行距：单倍行距
- 参考文献：Nature格式

### 内容要求
- Abstract: 200-250词
- 重点强调数据质量和可重现性
- 详细的方法描述
- 充分的技术验证
- 明确的使用说明

### 提交要求
- 主文稿（PDF）
- 补充材料
- 数据可用性声明
- 代码可用性声明

## 常见问题

### Q: 如何处理编译错误？
A: 确保安装了完整的LaTeX发行版（如TeX Live或MiKTeX），并安装了所需的包。

### Q: 图片无法显示？
A: 确保figures文件夹存在，并且图片文件名正确。可以先注释掉\includegraphics命令进行编译。

### Q: 参考文献格式不对？
A: 确保使用了正确的.bib文件，并按照Nature格式要求。

## 下一步工作

1. **数据收集完成后**：
   - 填写具体的数据统计
   - 计算标注质量指标
   - 运行基准实验

2. **图片制作**：
   - 使用医学图像可视化软件
   - 确保图片清晰度和标注可见性
   - 遵循期刊的图片格式要求

3. **最终检查**：
   - 语法和拼写检查
   - 数据一致性检查
   - 格式符合期刊要求

## 联系信息

如有问题，请联系：[你的邮箱]
