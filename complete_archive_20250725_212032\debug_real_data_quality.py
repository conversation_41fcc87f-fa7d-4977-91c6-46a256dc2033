#!/usr/bin/env python3
"""
调试真实数据质量问题
Debug real data quality issues
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def analyze_data_quality():
    """分析数据质量"""
    
    print("🔍 分析真实数据质量问题...")
    
    # 加载真实57点数据
    real_data = np.load('real_57_dataset_from_existing.npz', allow_pickle=True)
    real_pc = real_data['point_clouds']
    real_57kp = real_data['keypoints_57']
    real_12kp = real_data['keypoints_12']
    real_ids = real_data['sample_ids']
    
    print(f"✅ 真实数据:")
    print(f"   样本数: {len(real_ids)}")
    print(f"   点云: {real_pc.shape}")
    print(f"   57关键点: {real_57kp.shape}")
    print(f"   12关键点: {real_12kp.shape}")
    
    # 加载原始12点数据对比
    male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz', allow_pickle=True)
    orig_pc = male_data['point_clouds']
    orig_12kp = male_data['keypoints']
    orig_ids = male_data['sample_ids']
    
    print(f"\n✅ 原始12点数据:")
    print(f"   样本数: {len(orig_ids)}")
    print(f"   点云: {orig_pc.shape}")
    print(f"   12关键点: {orig_12kp.shape}")
    
    # 分析坐标范围
    print(f"\n📊 坐标范围对比:")
    
    # 真实数据范围
    real_pc_range = [np.ptp(real_pc, axis=(0,1))]
    real_57kp_range = [np.ptp(real_57kp, axis=(0,1))]
    real_12kp_range = [np.ptp(real_12kp, axis=(0,1))]
    
    print(f"   真实数据:")
    print(f"     点云范围: X={real_pc_range[0][0]:.1f}, Y={real_pc_range[0][1]:.1f}, Z={real_pc_range[0][2]:.1f}")
    print(f"     57关键点范围: X={real_57kp_range[0][0]:.1f}, Y={real_57kp_range[0][1]:.1f}, Z={real_57kp_range[0][2]:.1f}")
    print(f"     12关键点范围: X={real_12kp_range[0][0]:.1f}, Y={real_12kp_range[0][1]:.1f}, Z={real_12kp_range[0][2]:.1f}")
    
    # 原始数据范围
    orig_pc_range = [np.ptp(orig_pc, axis=(0,1))]
    orig_12kp_range = [np.ptp(orig_12kp, axis=(0,1))]
    
    print(f"   原始12点数据:")
    print(f"     点云范围: X={orig_pc_range[0][0]:.1f}, Y={orig_pc_range[0][1]:.1f}, Z={orig_pc_range[0][2]:.1f}")
    print(f"     12关键点范围: X={orig_12kp_range[0][0]:.1f}, Y={orig_12kp_range[0][1]:.1f}, Z={orig_12kp_range[0][2]:.1f}")
    
    # 检查相同样本的差异
    print(f"\n🔍 检查相同样本的差异:")
    
    # 找到共同样本
    common_ids = []
    for i, real_id in enumerate(real_ids):
        for j, orig_id in enumerate(orig_ids):
            if real_id == orig_id:
                common_ids.append((i, j, real_id))
                break
    
    print(f"   共同样本数: {len(common_ids)}")
    
    if common_ids:
        # 分析前3个共同样本
        for k, (real_idx, orig_idx, sample_id) in enumerate(common_ids[:3]):
            print(f"\n   样本 {sample_id}:")
            
            # 点云中心对比
            real_pc_center = np.mean(real_pc[real_idx], axis=0)
            orig_pc_center = np.mean(orig_pc[orig_idx], axis=0)
            
            print(f"     点云中心:")
            print(f"       真实: ({real_pc_center[0]:.1f}, {real_pc_center[1]:.1f}, {real_pc_center[2]:.1f})")
            print(f"       原始: ({orig_pc_center[0]:.1f}, {orig_pc_center[1]:.1f}, {orig_pc_center[2]:.1f})")
            
            # 12关键点对比
            real_12_center = np.mean(real_12kp[real_idx], axis=0)
            orig_12_center = np.mean(orig_12kp[orig_idx], axis=0)
            
            print(f"     12关键点中心:")
            print(f"       真实: ({real_12_center[0]:.1f}, {real_12_center[1]:.1f}, {real_12_center[2]:.1f})")
            print(f"       原始: ({orig_12_center[0]:.1f}, {orig_12_center[1]:.1f}, {orig_12_center[2]:.1f})")
            
            # 计算距离差异
            pc_distance = np.linalg.norm(real_pc_center - orig_pc_center)
            kp_distance = np.linalg.norm(real_12_center - orig_12_center)
            
            print(f"     中心距离差异:")
            print(f"       点云: {pc_distance:.1f}mm")
            print(f"       关键点: {kp_distance:.1f}mm")
    
    # 检查关键点与点云的相对位置
    print(f"\n🎯 检查关键点与点云的相对位置:")
    
    # 计算关键点到点云的最近距离
    sample_idx = 0
    pc_sample = real_pc[sample_idx]
    kp_sample = real_57kp[sample_idx]
    
    distances_to_surface = []
    for kp in kp_sample:
        dists = np.linalg.norm(pc_sample - kp, axis=1)
        min_dist = np.min(dists)
        distances_to_surface.append(min_dist)
    
    avg_distance = np.mean(distances_to_surface)
    max_distance = np.max(distances_to_surface)
    
    print(f"   样本 {real_ids[sample_idx]}:")
    print(f"     关键点到点云表面平均距离: {avg_distance:.2f}mm")
    print(f"     关键点到点云表面最大距离: {max_distance:.2f}mm")
    
    if avg_distance > 10:
        print(f"     ⚠️ 关键点距离点云表面太远！可能存在坐标系问题")
    
    return {
        'real_pc_range': real_pc_range[0],
        'real_57kp_range': real_57kp_range[0],
        'orig_pc_range': orig_pc_range[0],
        'orig_12kp_range': orig_12kp_range[0],
        'avg_distance_to_surface': avg_distance,
        'max_distance_to_surface': max_distance
    }

def visualize_sample_comparison():
    """可视化样本对比"""
    
    print(f"\n📊 生成可视化对比...")
    
    # 加载数据
    real_data = np.load('real_57_dataset_from_existing.npz', allow_pickle=True)
    real_pc = real_data['point_clouds']
    real_57kp = real_data['keypoints_57']
    real_12kp = real_data['keypoints_12']
    
    # 选择第一个样本
    sample_idx = 0
    pc = real_pc[sample_idx]
    kp_57 = real_57kp[sample_idx]
    kp_12 = real_12kp[sample_idx]
    
    # 下采样点云用于可视化
    pc_vis = pc[::100]  # 每100个点取1个
    
    # 创建3D图
    fig = plt.figure(figsize=(15, 5))
    
    # 子图1: 点云 + 57关键点
    ax1 = fig.add_subplot(131, projection='3d')
    ax1.scatter(pc_vis[:, 0], pc_vis[:, 1], pc_vis[:, 2], c='lightblue', s=1, alpha=0.3)
    ax1.scatter(kp_57[:, 0], kp_57[:, 1], kp_57[:, 2], c='red', s=50, alpha=0.8)
    ax1.set_title('Point Cloud + 57 Keypoints')
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')
    
    # 子图2: 点云 + 12关键点
    ax2 = fig.add_subplot(132, projection='3d')
    ax2.scatter(pc_vis[:, 0], pc_vis[:, 1], pc_vis[:, 2], c='lightblue', s=1, alpha=0.3)
    ax2.scatter(kp_12[:, 0], kp_12[:, 1], kp_12[:, 2], c='green', s=100, alpha=0.8)
    ax2.set_title('Point Cloud + 12 Keypoints')
    ax2.set_xlabel('X')
    ax2.set_ylabel('Y')
    ax2.set_zlabel('Z')
    
    # 子图3: 57关键点分布
    ax3 = fig.add_subplot(133, projection='3d')
    # F1区域 (0-18)
    ax3.scatter(kp_57[0:19, 0], kp_57[0:19, 1], kp_57[0:19, 2], c='red', s=50, label='F1', alpha=0.8)
    # F2区域 (19-37)
    ax3.scatter(kp_57[19:38, 0], kp_57[19:38, 1], kp_57[19:38, 2], c='green', s=50, label='F2', alpha=0.8)
    # F3区域 (38-56)
    ax3.scatter(kp_57[38:57, 0], kp_57[38:57, 1], kp_57[38:57, 2], c='blue', s=50, label='F3', alpha=0.8)
    ax3.set_title('57 Keypoints by Region')
    ax3.set_xlabel('X')
    ax3.set_ylabel('Y')
    ax3.set_zlabel('Z')
    ax3.legend()
    
    plt.tight_layout()
    plt.savefig('real_data_quality_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 可视化已保存: real_data_quality_analysis.png")

def main():
    """主函数"""
    
    print("🔍 调试真实数据质量问题")
    print("分析为什么真实数据性能比插值数据差")
    print("=" * 80)
    
    # 分析数据质量
    quality_info = analyze_data_quality()
    
    # 生成可视化
    visualize_sample_comparison()
    
    print(f"\n🎯 问题诊断:")
    
    # 判断问题类型
    if quality_info['avg_distance_to_surface'] > 20:
        print(f"❌ 主要问题: 坐标系不匹配")
        print(f"   关键点距离点云表面平均 {quality_info['avg_distance_to_surface']:.1f}mm")
        print(f"   建议: 需要坐标系对齐或重新处理数据")
    
    elif abs(quality_info['real_pc_range'][0] - quality_info['orig_pc_range'][0]) > 100:
        print(f"❌ 主要问题: 数据尺度不匹配")
        print(f"   真实数据范围: {quality_info['real_pc_range']}")
        print(f"   原始数据范围: {quality_info['orig_pc_range']}")
        print(f"   建议: 需要数据归一化")
    
    else:
        print(f"⚠️ 可能问题: 数据质量或模型架构")
        print(f"   建议: 检查标注质量或简化模型")
    
    print(f"\n💡 解决方案建议:")
    print(f"   1. 检查CSV标注的坐标系统")
    print(f"   2. 验证点云和关键点的对应关系")
    print(f"   3. 应用与12点数据相同的预处理")
    print(f"   4. 考虑渐进式训练 (12→19→57)")

if __name__ == "__main__":
    main()
