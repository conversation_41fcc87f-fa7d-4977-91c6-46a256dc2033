#!/usr/bin/env python3
"""
个别关键点彩虹靶子网格可视化
显示每个关键点的独立彩虹靶子效果，4x3网格布局
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps

# 关键点名称
KEYPOINT_NAMES = {
    0: "L-ASIS", 1: "R-ASIS", 2: "L-PSIS", 3: "R-PSIS",
    4: "L-IC", 5: "R-IC", 6: "SP", 7: "L-SIJ", 8: "R-SIJ",
    9: "L-IS", 10: "R-IS", 11: "CT"
}

def create_individual_rainbow_colormap():
    """创建个别关键点的彩虹色配色方案"""
    colors = [
        '#F8F8FF',  # 幽灵白
        '#E6E6FA',  # 薰衣草色
        '#9370DB',  # 中紫色
        '#4169E1',  # 皇家蓝
        '#00BFFF',  # 深天蓝
        '#00FFFF',  # 青色
        '#00FF7F',  # 春绿色
        '#ADFF2F',  # 绿黄色
        '#FFFF00',  # 黄色
        '#FFD700',  # 金色
        '#FFA500',  # 橙色
        '#FF4500',  # 橙红色
        '#FF0000',  # 红色
    ]
    return LinearSegmentedColormap.from_list('individual_rainbow', colors, N=256)

def create_individual_heatmap(point_cloud, pred_keypoint, sigma=8.0):
    """为单个关键点创建彩虹热力图"""
    
    # 计算到预测关键点的距离
    distances = np.linalg.norm(point_cloud - pred_keypoint, axis=1)
    
    # 使用高斯分布
    heatmap = np.exp(-distances**2 / (2 * sigma**2))
    
    # 增强对比度
    heatmap = np.power(heatmap, 0.8)
    
    # 归一化
    if np.max(heatmap) > 0:
        heatmap = heatmap / np.max(heatmap)
    
    return heatmap

def create_individual_keypoints_grid(point_cloud, true_keypoints, pred_keypoints, 
                                   confidences, sample_id):
    """创建个别关键点的4x3网格彩虹可视化"""
    
    print(f"🌈 Creating individual keypoints rainbow grid")
    
    rainbow_cmap = create_individual_rainbow_colormap()
    
    # 创建4x3的子图网格
    fig = plt.figure(figsize=(24, 32))
    
    for kp_idx in range(12):
        ax = fig.add_subplot(4, 3, kp_idx + 1, projection='3d')
        
        # 创建当前关键点的热力图
        individual_heatmap = create_individual_heatmap(
            point_cloud, pred_keypoints[kp_idx], sigma=8.0
        )
        
        # 采样点云
        if len(point_cloud) > 5000:
            sample_indices = np.random.choice(len(point_cloud), 5000, replace=False)
            display_pc = point_cloud[sample_indices]
            display_heatmap = individual_heatmap[sample_indices]
        else:
            display_pc = point_cloud
            display_heatmap = individual_heatmap
        
        # 显示所有点 - 使用彩虹色渐变
        
        # 1. 背景点 - 低置信度
        bg_mask = display_heatmap < 0.1
        if np.any(bg_mask):
            ax.scatter(display_pc[bg_mask, 0],
                      display_pc[bg_mask, 1],
                      display_pc[bg_mask, 2],
                      c=display_heatmap[bg_mask],
                      cmap=rainbow_cmap, s=0.3, alpha=0.2, vmin=0, vmax=1)
        
        # 2. 外环 - 紫蓝色
        outer_mask = (display_heatmap >= 0.1) & (display_heatmap < 0.3)
        if np.any(outer_mask):
            ax.scatter(display_pc[outer_mask, 0],
                      display_pc[outer_mask, 1],
                      display_pc[outer_mask, 2],
                      c=display_heatmap[outer_mask],
                      cmap=rainbow_cmap, s=1, alpha=0.5, vmin=0, vmax=1)
        
        # 3. 中环 - 青绿色
        middle_mask = (display_heatmap >= 0.3) & (display_heatmap < 0.6)
        if np.any(middle_mask):
            ax.scatter(display_pc[middle_mask, 0],
                      display_pc[middle_mask, 1],
                      display_pc[middle_mask, 2],
                      c=display_heatmap[middle_mask],
                      cmap=rainbow_cmap, s=2, alpha=0.7, vmin=0, vmax=1)
        
        # 4. 内环 - 黄橙红色
        inner_mask = display_heatmap >= 0.6
        if np.any(inner_mask):
            scatter = ax.scatter(display_pc[inner_mask, 0],
                               display_pc[inner_mask, 1],
                               display_pc[inner_mask, 2],
                               c=display_heatmap[inner_mask],
                               cmap=rainbow_cmap, s=4, alpha=0.9, vmin=0, vmax=1)
        
        # 5. 峰值点 - 靶心
        peak_mask = display_heatmap >= 0.9
        if np.any(peak_mask):
            ax.scatter(display_pc[peak_mask, 0],
                      display_pc[peak_mask, 1],
                      display_pc[peak_mask, 2],
                      c='white', s=20, marker='o', 
                      edgecolor='red', linewidth=2, alpha=1.0)
        
        # 6. 关键点标记
        true_kp = true_keypoints[kp_idx]
        pred_kp = pred_keypoints[kp_idx]
        
        # 真实关键点 - 大黑星
        ax.scatter(true_kp[0], true_kp[1], true_kp[2],
                  c='black', s=400, marker='*', 
                  edgecolor='white', linewidth=3, 
                  alpha=1.0, zorder=10)
        
        # 预测关键点 - 大红十字
        ax.scatter(pred_kp[0], pred_kp[1], pred_kp[2],
                  c='red', s=300, marker='x', 
                  linewidth=5, alpha=1.0, zorder=10)
        
        # 连接线
        ax.plot([true_kp[0], pred_kp[0]], 
                [true_kp[1], pred_kp[1]], 
                [true_kp[2], pred_kp[2]], 
                'k-', alpha=0.8, linewidth=3)
        
        # 计算误差
        error = np.linalg.norm(pred_kp - true_kp)
        
        # 设置坐标轴范围 - 以真实关键点为中心，显示更大范围
        center = true_kp  # 改为以真实关键点为中心
        range_size = 40   # 增大范围以显示更多上下文

        ax.set_xlim([center[0] - range_size, center[0] + range_size])
        ax.set_ylim([center[1] - range_size, center[1] + range_size])
        ax.set_zlim([center[2] - range_size, center[2] + range_size])
        
        # 设置标题
        ax.set_title(f'{KEYPOINT_NAMES[kp_idx]}\n'
                    f'Error: {error:.1f}mm\n'
                    f'Conf: {confidences[kp_idx]:.3f}',
                    fontsize=12, fontweight='bold', pad=15)
        
        # 坐标轴标签 - 小字体
        ax.set_xlabel('X', fontsize=8)
        ax.set_ylabel('Y', fontsize=8)
        ax.set_zlabel('Z', fontsize=8)
        ax.tick_params(labelsize=6)
        
        # 设置视角
        ax.view_init(elev=20, azim=45)
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息
        high_conf_points = np.sum(display_heatmap > 0.6)
        total_points = len(display_heatmap)
        
        stats_text = f'Hot: {high_conf_points}\nTotal: {total_points}'
        ax.text2D(0.02, 0.98, stats_text, transform=ax.transAxes, 
                 fontsize=8, verticalalignment='top',
                 bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.suptitle(f'🌈 Individual Keypoints Rainbow Grid 🌈\n'
                f'Sample {sample_id} - Each Keypoint with Individual Rainbow Target', 
                fontsize=20, fontweight='bold')
    
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    
    # 保存
    filename = f'individual_keypoints_rainbow_grid_{sample_id}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"   🌈 Individual keypoints rainbow grid saved: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🌈 Individual Keypoints Rainbow Grid Visualization")
    print("Display each keypoint with individual rainbow target effects")
    print("=" * 80)
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model.eval()
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # 选择一个样本进行展示
    sample_idx = 0
    sample_id = sample_ids[sample_idx]
    point_cloud = point_clouds[sample_idx]
    true_keypoints = keypoints[sample_idx]
    
    print(f"\n🌈 Processing sample: {sample_id}")
    print(f"📊 Point cloud size: {len(point_cloud)}")
    print(f"🎯 Number of keypoints: {len(true_keypoints)}")
    
    # 采样点云用于预测
    if len(point_cloud) > 8192:
        indices = np.random.choice(len(point_cloud), 8192, replace=False)
        pc_sampled = point_cloud[indices]
    else:
        pc_sampled = point_cloud
    
    # 预测关键点
    pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
    
    with torch.no_grad():
        pred_heatmaps = model(pc_tensor)
    
    pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T
    pred_keypoints, confidences = extract_keypoints_from_heatmaps(
        pred_heatmaps_np.T, pc_sampled
    )
    
    print(f"🎯 Predicted {len(pred_keypoints)} keypoints")
    
    # 创建个别关键点的彩虹网格可视化
    create_individual_keypoints_grid(
        point_cloud,  # 使用完整点云
        true_keypoints, 
        pred_keypoints,
        confidences, 
        sample_id
    )
    
    print(f"\n🎉 Individual Keypoints Rainbow Grid Complete!")
    print("✅ 12 individual rainbow targets")
    print("✅ 4x3 grid layout")
    print("✅ 5000 points per target")
    print("✅ Focused view on each keypoint")
    print("✅ Individual error and confidence")

if __name__ == "__main__":
    main()
