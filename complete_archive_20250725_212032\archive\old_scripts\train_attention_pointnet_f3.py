#!/usr/bin/env python3
"""
Train Attention PointNet for F3 Keypoint Detection

专门训练注意力机制增强的PointNet，目标突破5mm精度
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import time
import json
import gc
import random
import math

# 设置随机种子
def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class StrictF3Dataset(Dataset):
    """严格测试集隔离的数据集"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points
        self.augment = augment
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
        
        print(f"   {split}: {len(self.sample_ids)} 样本")
    
    def __len__(self):
        return len(self.sample_ids)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        if self.augment:
            # 医疗级数据增强
            angle = np.random.uniform(-0.1, 0.1)
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
            point_cloud = point_cloud @ rotation.T
            keypoints = keypoints @ rotation.T
            
            translation = np.random.uniform(-0.5, 0.5, 3)
            point_cloud += translation
            keypoints += translation
            
            noise = np.random.normal(0, 0.05, point_cloud.shape)
            point_cloud += noise
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

class AttentionPointNet(nn.Module):
    """注意力增强的PointNet"""
    
    def __init__(self, num_keypoints: int = 19):
        super(AttentionPointNet, self).__init__()
        
        # 点级特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 多头注意力机制 (兼容旧版PyTorch)
        self.attention = nn.MultiheadAttention(1024, 8, dropout=0.1)
        
        # 位置编码
        self.pos_encoding = nn.Parameter(torch.randn(1, 4096, 1024) * 0.02)
        
        # 全局特征处理
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        
        self.dropout = nn.Dropout(0.3)
        
        # 残差连接
        self.residual_conv = nn.Conv1d(512, 1024, 1)
        
    def forward(self, x):
        batch_size = x.size(0)
        num_points = x.size(1)
        x = x.transpose(2, 1)  # (batch, 3, points)
        
        # 点级特征提取
        x1 = torch.relu(self.bn1(self.conv1(x)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        x4 = torch.relu(self.bn4(self.conv4(x3)))
        x5 = torch.relu(self.bn5(self.conv5(x4)))  # (batch, 1024, points)
        
        # 残差连接
        x4_res = self.residual_conv(x4)  # (batch, 1024, points)
        x5 = x5 + x4_res
        
        # 准备注意力机制
        x = x5.transpose(1, 2)  # (batch, points, 1024)
        
        # 添加位置编码
        if num_points <= 4096:
            pos_enc = self.pos_encoding[:, :num_points, :]
            x = x + pos_enc
        
        # 多头注意力 (调整维度以兼容旧版PyTorch)
        x = x.transpose(0, 1)  # (points, batch, 1024)
        x_att, attention_weights = self.attention(x, x, x)
        x = x + x_att  # 残差连接
        x = x.transpose(0, 1)  # (batch, points, 1024)

        # 全局特征聚合 (使用注意力权重)
        attention_pooling = torch.mean(attention_weights, dim=1)  # (batch, points)
        attention_pooling = F.softmax(attention_pooling, dim=1).unsqueeze(-1)  # (batch, points, 1)

        # 加权全局特征
        global_feat = torch.sum(x * attention_pooling, dim=1)  # (batch, 1024)
        
        # 全局特征处理
        x = torch.relu(self.bn_fc1(self.fc1(global_feat)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc2(self.fc2(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc3(self.fc3(x)))
        x = self.dropout(x)
        x = self.fc4(x)
        
        return x.view(batch_size, 19, 3)

class AdaptiveLoss(nn.Module):
    """自适应损失函数"""
    
    def __init__(self, alpha=1.0, beta=0.5, gamma=0.1):
        super(AdaptiveLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma
    
    def forward(self, pred, target):
        # MSE Loss
        mse_loss = F.mse_loss(pred, target)
        
        # Smooth L1 Loss
        smooth_l1_loss = F.smooth_l1_loss(pred, target)
        
        # 距离一致性损失
        pred_dists = torch.cdist(pred, pred, p=2)
        target_dists = torch.cdist(target, target, p=2)
        dist_loss = F.mse_loss(pred_dists, target_dists)
        
        total_loss = (self.alpha * mse_loss + 
                     self.beta * smooth_l1_loss + 
                     self.gamma * dist_loss)
        
        return total_loss

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm
    }

def train_attention_pointnet():
    """训练注意力PointNet"""
    
    print("🚀 **训练注意力增强PointNet**")
    print("🎯 **目标: 突破5mm医疗精度**")
    print("=" * 70)
    
    set_seed(42)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 数据集
    dataset_path = "high_quality_f3_dataset.npz"
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    train_dataset = StrictF3Dataset(dataset_path, 'train', num_points=4096, 
                                  test_samples=test_samples, augment=True, seed=42)
    val_dataset = StrictF3Dataset(dataset_path, 'val', num_points=4096, 
                                test_samples=test_samples, augment=False, seed=42)
    test_dataset = StrictF3Dataset(dataset_path, 'test', num_points=4096, 
                                 test_samples=test_samples, augment=False, seed=42)
    
    batch_size = 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    
    # 模型
    model = AttentionPointNet(num_keypoints=19).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 注意力PointNet参数: {total_params:,}")
    
    # 损失函数和优化器
    criterion = AdaptiveLoss(alpha=1.0, beta=0.5, gamma=0.1)
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=25, T_mult=2, eta_min=1e-6
    )
    
    # 训练配置
    num_epochs = 150
    best_val_error = float('inf')
    patience = 25
    patience_counter = 0
    history = []
    
    print(f"🎯 开始训练 (目标: <5mm)")
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 50)
        
        # 训练
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_5mm_percent': 0}
        
        for batch in train_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                        
            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue
        
        scheduler.step()
        
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # 打印结果
        current_lr = optimizer.param_groups[0]['lr']
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"5mm={train_metrics['within_5mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"5mm={val_metrics['within_5mm_percent']:.1f}%")
        print(f"学习率: {current_lr:.2e}")
        
        # 保存历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'learning_rate': current_lr
        })
        
        # 检查改进
        current_error = val_metrics['mean_distance']
        if current_error < best_val_error:
            best_val_error = current_error
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics
            }, 'best_attention_pointnet_f3.pth')
            
            print(f"🎉 新最佳! 验证误差: {best_val_error:.3f}mm")
            
            if best_val_error <= 5.0:
                print(f"🏆 **达到5mm目标!**")
        else:
            patience_counter += 1
            print(f"⏳ 无改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停")
            break
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    
    # 测试评估
    print(f"\n🧪 **测试集评估**")
    checkpoint = torch.load('best_attention_pointnet_f3.pth')
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    test_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_1mm_percent': 0, 'within_3mm_percent': 0}
    all_distances = []
    
    with torch.no_grad():
        for batch in test_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            try:
                pred_keypoints = model(point_cloud)
                
                for i in range(len(batch['sample_id'])):
                    pred_single = pred_keypoints[i:i+1]
                    target_single = keypoints[i:i+1]
                    
                    metrics = calculate_metrics(pred_single, target_single)
                    all_distances.append(metrics['mean_distance'])
                
                batch_metrics = calculate_metrics(pred_keypoints, keypoints)
                for key in test_metrics:
                    if key in batch_metrics:
                        test_metrics[key] += batch_metrics[key]
                    
            except RuntimeError as e:
                continue
    
    for key in test_metrics:
        test_metrics[key] /= len(test_loader)
    
    print(f"📊 **注意力PointNet测试结果**")
    print(f"   测试误差: {test_metrics['mean_distance']:.3f}mm")
    print(f"   1mm精度: {test_metrics['within_1mm_percent']:.1f}%")
    print(f"   3mm精度: {test_metrics['within_3mm_percent']:.1f}%")
    print(f"   5mm精度: {test_metrics['within_5mm_percent']:.1f}%")
    print(f"   训练时间: {total_time/60:.1f}分钟")
    
    # 评估
    if test_metrics['mean_distance'] <= 5.0:
        print(f"🏆 **突破5mm目标!**")
    elif test_metrics['mean_distance'] <= 6.0:
        print(f"✅ **显著改进!** 接近5mm目标")
    elif test_metrics['mean_distance'] < 8.127:  # 比基线好
        print(f"✅ **性能提升!** 优于基线8.127mm")
    else:
        print(f"⚠️ **需要进一步优化**")
    
    # 保存结果
    results = {
        'model_name': 'Attention_PointNet_F3',
        'test_metrics': test_metrics,
        'best_val_error': best_val_error,
        'training_time_minutes': total_time / 60,
        'all_distances': all_distances,
        'training_history': history
    }
    
    with open('attention_pointnet_f3_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    return results

if __name__ == "__main__":
    try:
        results = train_attention_pointnet()
        
        print(f"\n🎉 **注意力PointNet训练完成!**")
        print(f"🎯 测试误差: {results['test_metrics']['mean_distance']:.3f}mm")
        print(f"📊 5mm精度: {results['test_metrics']['within_5mm_percent']:.1f}%")
        print(f"⏱️ 训练时间: {results['training_time_minutes']:.1f}分钟")
        
        # 与基线对比
        baseline_error = 8.127
        improvement = (baseline_error - results['test_metrics']['mean_distance']) / baseline_error * 100
        
        if improvement > 0:
            print(f"📈 **相比基线改进**: {improvement:.1f}%")
        else:
            print(f"📉 **相比基线**: {-improvement:.1f}% 下降")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
