#!/usr/bin/env python3
"""
Progressive Training for F3 Dataset

Gradually increase complexity based on successful minimal training.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import time
import json
import gc

class ProgressiveF3Dataset(Dataset):
    """Progressive dataset with configurable complexity"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 2048, 
                 max_samples: int = 50, augment: bool = False):
        
        self.num_points = num_points
        self.augment = augment
        
        # Load data
        data = np.load(data_path, allow_pickle=True)
        
        # Use more samples but still manageable
        point_clouds = data['point_clouds'][:max_samples]
        keypoints = data['keypoints'][:max_samples]
        
        # Split data
        train_size = int(0.7 * len(point_clouds))
        val_size = int(0.15 * len(point_clouds))
        
        if split == 'train':
            self.point_clouds = point_clouds[:train_size]
            self.keypoints = keypoints[:train_size]
        elif split == 'val':
            self.point_clouds = point_clouds[train_size:train_size+val_size]
            self.keypoints = keypoints[train_size:train_size+val_size]
        else:  # test
            self.point_clouds = point_clouds[train_size+val_size:]
            self.keypoints = keypoints[train_size+val_size:]
        
        print(f"   {split}: {len(self.point_clouds)} 样本, {num_points} 点")
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        # Smart downsampling
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        # Progressive augmentation
        if self.augment:
            # Rotation
            angle = np.random.uniform(-0.2, 0.2)  # ±11 degrees
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
            point_cloud = point_cloud @ rotation.T
            keypoints = keypoints @ rotation.T
            
            # Translation
            translation = np.random.uniform(-1.0, 1.0, 3)
            point_cloud += translation
            keypoints += translation
            
            # Noise
            noise = np.random.normal(0, 0.1, point_cloud.shape)
            point_cloud += noise
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints)
        }

class ProgressivePointNet(nn.Module):
    """Progressive PointNet with configurable complexity"""
    
    def __init__(self, complexity: str = 'medium'):
        super(ProgressivePointNet, self).__init__()
        
        if complexity == 'small':
            # Small model (like minimal)
            self.conv1 = nn.Conv1d(3, 32, 1)
            self.conv2 = nn.Conv1d(32, 64, 1)
            self.conv3 = nn.Conv1d(64, 128, 1)
            
            self.fc1 = nn.Linear(128, 64)
            self.fc2 = nn.Linear(64, 19 * 3)
            
        elif complexity == 'medium':
            # Medium model
            self.conv1 = nn.Conv1d(3, 64, 1)
            self.conv2 = nn.Conv1d(64, 128, 1)
            self.conv3 = nn.Conv1d(128, 256, 1)
            
            self.bn1 = nn.BatchNorm1d(64)
            self.bn2 = nn.BatchNorm1d(128)
            self.bn3 = nn.BatchNorm1d(256)
            
            self.fc1 = nn.Linear(256, 128)
            self.fc2 = nn.Linear(128, 64)
            self.fc3 = nn.Linear(64, 19 * 3)
            
        else:  # large
            # Large model
            self.conv1 = nn.Conv1d(3, 64, 1)
            self.conv2 = nn.Conv1d(64, 128, 1)
            self.conv3 = nn.Conv1d(128, 256, 1)
            self.conv4 = nn.Conv1d(256, 512, 1)
            
            self.bn1 = nn.BatchNorm1d(64)
            self.bn2 = nn.BatchNorm1d(128)
            self.bn3 = nn.BatchNorm1d(256)
            self.bn4 = nn.BatchNorm1d(512)
            
            self.fc1 = nn.Linear(512, 256)
            self.fc2 = nn.Linear(256, 128)
            self.fc3 = nn.Linear(128, 64)
            self.fc4 = nn.Linear(64, 19 * 3)
        
        self.complexity = complexity
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # (batch, 3, points)
        
        if self.complexity == 'small':
            x = torch.relu(self.conv1(x))
            x = torch.relu(self.conv2(x))
            x = torch.relu(self.conv3(x))
            
            x = torch.max(x, 2)[0]  # Global max pooling
            
            x = torch.relu(self.fc1(x))
            x = self.dropout(x)
            x = self.fc2(x)
            
        elif self.complexity == 'medium':
            x = torch.relu(self.bn1(self.conv1(x)))
            x = torch.relu(self.bn2(self.conv2(x)))
            x = torch.relu(self.bn3(self.conv3(x)))
            
            x = torch.max(x, 2)[0]  # Global max pooling
            
            x = torch.relu(self.fc1(x))
            x = self.dropout(x)
            x = torch.relu(self.fc2(x))
            x = self.dropout(x)
            x = self.fc3(x)
            
        else:  # large
            x = torch.relu(self.bn1(self.conv1(x)))
            x = torch.relu(self.bn2(self.conv2(x)))
            x = torch.relu(self.bn3(self.conv3(x)))
            x = torch.relu(self.bn4(self.conv4(x)))
            
            x = torch.max(x, 2)[0]  # Global max pooling
            
            x = torch.relu(self.fc1(x))
            x = self.dropout(x)
            x = torch.relu(self.fc2(x))
            x = self.dropout(x)
            x = torch.relu(self.fc3(x))
            x = self.dropout(x)
            x = self.fc4(x)
        
        return x.view(batch_size, 19, 3)

def calculate_metrics(pred, target):
    """Calculate comprehensive metrics"""
    distances = torch.norm(pred - target, dim=2)  # (batch, 19)
    avg_distances = torch.mean(distances, dim=1)  # (batch,)
    
    mean_dist = torch.mean(avg_distances).item()
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_10mm = (avg_distances <= 10.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'within_1mm_percent': within_1mm,
        'within_5mm_percent': within_5mm,
        'within_10mm_percent': within_10mm
    }

def train_progressive_model(num_points: int = 2048, batch_size: int = 4, 
                          complexity: str = 'medium', max_samples: int = 50,
                          num_epochs: int = 50):
    """Train progressive model with given configuration"""
    
    print(f"\n🚀 **渐进式训练配置**")
    print(f"   点云大小: {num_points}")
    print(f"   批处理大小: {batch_size}")
    print(f"   模型复杂度: {complexity}")
    print(f"   最大样本数: {max_samples}")
    print(f"   训练轮数: {num_epochs}")
    print("=" * 60)
    
    # Setup
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    # Clear cache
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # Dataset
    dataset_path = "high_quality_f3_dataset.npz"
    
    train_dataset = ProgressiveF3Dataset(dataset_path, 'train', num_points, max_samples, augment=True)
    val_dataset = ProgressiveF3Dataset(dataset_path, 'val', num_points, max_samples, augment=False)
    test_dataset = ProgressiveF3Dataset(dataset_path, 'test', num_points, max_samples, augment=False)
    
    # Data loaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    # Model
    model = ProgressivePointNet(complexity=complexity).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 模型参数: {total_params:,}")
    
    # Training setup
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=8, factor=0.8)
    
    # Training
    best_val_error = float('inf')
    patience = 15
    patience_counter = 0
    history = []
    
    print(f"\n🎯 开始训练...")
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        
        # Training
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_1mm_percent': 0, 'within_5mm_percent': 0}
        
        for batch in train_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                optimizer.step()
                
                train_loss += loss.item()
                
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                        
            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                continue
        
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # Validation
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_1mm_percent': 0, 'within_5mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    
                    val_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    print(f"❌ 验证批次失败: {e}")
                    continue
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        scheduler.step(val_metrics['mean_distance'])
        
        # Print results
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"1mm={train_metrics['within_1mm_percent']:.1f}%, 5mm={train_metrics['within_5mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"1mm={val_metrics['within_1mm_percent']:.1f}%, 5mm={val_metrics['within_5mm_percent']:.1f}%")
        
        # Save history
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics
        })
        
        # Check improvement
        current_error = val_metrics['mean_distance']
        if current_error < best_val_error:
            best_val_error = current_error
            patience_counter = 0
            torch.save(model.state_dict(), f'best_progressive_{complexity}_{num_points}pts.pth')
            print(f"🎉 新最佳: {best_val_error:.3f}mm")
        else:
            patience_counter += 1
            print(f"⏳ 无改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停")
            break
        
        # Force cleanup
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    
    # Test evaluation
    print(f"\n🧪 **测试评估**")
    model.load_state_dict(torch.load(f'best_progressive_{complexity}_{num_points}pts.pth'))
    model.eval()
    
    test_metrics = {'mean_distance': 0, 'within_1mm_percent': 0, 'within_5mm_percent': 0, 'within_10mm_percent': 0}
    test_count = 0
    
    with torch.no_grad():
        for batch in test_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            try:
                pred_keypoints = model(point_cloud)
                metrics = calculate_metrics(pred_keypoints, keypoints)
                
                for key in test_metrics:
                    test_metrics[key] += metrics[key]
                test_count += 1
                
            except RuntimeError as e:
                print(f"❌ 测试批次失败: {e}")
                continue
    
    if test_count > 0:
        for key in test_metrics:
            test_metrics[key] /= test_count
    
    print(f"📊 最终结果:")
    print(f"   测试误差: {test_metrics['mean_distance']:.3f}mm")
    print(f"   1mm精度: {test_metrics['within_1mm_percent']:.1f}%")
    print(f"   5mm精度: {test_metrics['within_5mm_percent']:.1f}%")
    print(f"   10mm精度: {test_metrics['within_10mm_percent']:.1f}%")
    print(f"   训练时间: {total_time/60:.1f}分钟")
    
    # Assessment
    if test_metrics['mean_distance'] <= 1.0:
        print(f"🏆 **优秀!** 达到医疗级精度 (<1mm)")
    elif test_metrics['mean_distance'] <= 5.0:
        print(f"✅ **良好!** 达到医疗可用精度 (<5mm)")
    elif test_metrics['mean_distance'] <= 10.0:
        print(f"⚠️ **可接受** 接近医疗精度要求")
    else:
        print(f"❌ **需要改进** 未达到医疗精度要求")
    
    return test_metrics, total_time

if __name__ == "__main__":
    # Progressive training configurations
    configs = [
        # (num_points, batch_size, complexity, max_samples, epochs)
        (2048, 2, 'medium', 50, 40),  # Start with manageable config
        (4096, 2, 'medium', 50, 40),  # Increase point density
        (4096, 4, 'medium', 50, 40),  # Increase batch size
        (4096, 4, 'large', 50, 40),   # Increase model complexity
    ]
    
    results = []
    
    for i, (num_points, batch_size, complexity, max_samples, epochs) in enumerate(configs):
        print(f"\n{'='*80}")
        print(f"🚀 **渐进式训练阶段 {i+1}/{len(configs)}**")
        print(f"{'='*80}")
        
        try:
            test_metrics, training_time = train_progressive_model(
                num_points=num_points,
                batch_size=batch_size,
                complexity=complexity,
                max_samples=max_samples,
                num_epochs=epochs
            )
            
            config_result = {
                'stage': i+1,
                'config': {
                    'num_points': num_points,
                    'batch_size': batch_size,
                    'complexity': complexity,
                    'max_samples': max_samples,
                    'epochs': epochs
                },
                'test_metrics': test_metrics,
                'training_time_minutes': training_time / 60
            }
            
            results.append(config_result)
            
            print(f"\n✅ **阶段 {i+1} 完成!**")
            print(f"🎯 测试误差: {test_metrics['mean_distance']:.3f}mm")
            print(f"📊 5mm精度: {test_metrics['within_5mm_percent']:.1f}%")
            
            # Stop if we achieve medical grade accuracy
            if test_metrics['mean_distance'] <= 1.0:
                print(f"🏆 **达到医疗级精度! 停止渐进式训练**")
                break
                
        except Exception as e:
            print(f"❌ 阶段 {i+1} 失败: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    # Save all results
    with open('progressive_training_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n🎉 **渐进式训练完成!**")
    print(f"📊 完成阶段: {len(results)}/{len(configs)}")
    
    if results:
        best_result = min(results, key=lambda x: x['test_metrics']['mean_distance'])
        print(f"🏆 **最佳结果**: 阶段{best_result['stage']}")
        print(f"   配置: {best_result['config']}")
        print(f"   测试误差: {best_result['test_metrics']['mean_distance']:.3f}mm")
        print(f"   5mm精度: {best_result['test_metrics']['within_5mm_percent']:.1f}%")
