"""
直接3D查看器
立即显示交互式3D可视化，无需保存文件
"""

import torch
import numpy as np
import plotly.graph_objects as go
import plotly.offline as pyo
from pathlib import Path

from save_best_model import BestSimplePointNet
from improved_data_loader import ImprovedDataLoader

class Direct3DViewer:
    """直接3D查看器"""
    
    def __init__(self, data_root="output/training_fixed"):
        self.data_root = data_root
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"🎮 直接3D查看器初始化")
        
    def load_and_predict(self):
        """加载模型并预测一个样本"""
        print("加载模型...")
        
        # 加载模型
        model = BestSimplePointNet(num_keypoints=57)
        model_path = "output/scale_corrected_training/best_baseline_model.pth"
        
        if Path(model_path).exists():
            checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"✅ 模型加载成功")
        
        model = model.to(self.device)
        model.eval()
        
        # 加载数据
        data_loader_manager = ImprovedDataLoader(
            data_root=self.data_root,
            batch_size=1,
            num_workers=0,
            num_points=512
        )
        
        _, val_loader = data_loader_manager.create_dataloaders(train_ratio=0.8)
        
        # 获取一个样本进行预测
        print("进行预测...")
        with torch.no_grad():
            for i, (point_cloud, keypoints) in enumerate(val_loader):
                if i >= 1:  # 只取第一个样本
                    break
                
                point_cloud = point_cloud.to(self.device)
                keypoints = keypoints.to(self.device)
                
                pred_keypoints = model(point_cloud)
                error = torch.norm(pred_keypoints - keypoints, dim=2).cpu().numpy()
                
                sample_data = {
                    'point_cloud': point_cloud.cpu().numpy()[0],
                    'ground_truth': keypoints.cpu().numpy()[0],
                    'prediction': pred_keypoints.cpu().numpy()[0],
                    'error': error[0],
                    'mean_error': np.mean(error[0]),
                    'accuracy_5mm': (error[0] <= 5.0).mean() * 100
                }
                
                print(f"✅ 预测完成: 误差 {sample_data['mean_error']:.2f}mm, 准确率 {sample_data['accuracy_5mm']:.1f}%")
                return sample_data
    
    def create_3d_plot(self, sample):
        """创建3D图表"""
        print("创建3D可视化...")
        
        pc = sample['point_cloud']
        gt = sample['ground_truth']
        pred = sample['prediction']
        error = sample['error']
        
        # 创建3D图表
        fig = go.Figure()
        
        # 1. 添加点云（半透明）
        fig.add_trace(go.Scatter3d(
            x=pc[:, 0], y=pc[:, 1], z=pc[:, 2],
            mode='markers',
            marker=dict(
                size=2,
                color='lightgray',
                opacity=0.2
            ),
            name='Point Cloud',
            hoverinfo='skip'
        ))
        
        # 2. 添加真实关键点（红色圆点）
        fig.add_trace(go.Scatter3d(
            x=gt[:, 0], y=gt[:, 1], z=gt[:, 2],
            mode='markers',
            marker=dict(
                size=10,
                color='red',
                symbol='circle',
                line=dict(width=2, color='darkred')
            ),
            name='Ground Truth',
            hovertemplate='<b>真实关键点 #%{text}</b><br>' +
                         'X: %{x:.2f}mm<br>' +
                         'Y: %{y:.2f}mm<br>' +
                         'Z: %{z:.2f}mm<extra></extra>',
            text=[f'{i}' for i in range(len(gt))]
        ))
        
        # 3. 添加预测关键点（根据误差着色）
        fig.add_trace(go.Scatter3d(
            x=pred[:, 0], y=pred[:, 1], z=pred[:, 2],
            mode='markers',
            marker=dict(
                size=10,
                color=error,
                colorscale='RdYlGn_r',  # 红-黄-绿反向
                cmin=0,
                cmax=8,
                symbol='diamond',
                line=dict(width=1, color='black'),
                colorbar=dict(
                    title=dict(text="误差 (mm)", side="right"),
                    tickmode="linear",
                    tick0=0,
                    dtick=1,
                    len=0.7
                )
            ),
            name='Prediction',
            hovertemplate='<b>预测关键点 #%{text}</b><br>' +
                         'X: %{x:.2f}mm<br>' +
                         'Y: %{y:.2f}mm<br>' +
                         'Z: %{z:.2f}mm<br>' +
                         '<b>误差: %{marker.color:.2f}mm</b><extra></extra>',
            text=[f'{i}' for i in range(len(pred))]
        ))
        
        # 4. 添加连接线（显示误差向量）
        for i in range(len(gt)):
            # 根据误差设置线条颜色和样式
            if error[i] <= 2:
                line_color = 'green'
                line_width = 2
                line_dash = 'solid'
            elif error[i] <= 5:
                line_color = 'orange'
                line_width = 3
                line_dash = 'solid'
            else:
                line_color = 'red'
                line_width = 4
                line_dash = 'dash'
            
            fig.add_trace(go.Scatter3d(
                x=[gt[i, 0], pred[i, 0]],
                y=[gt[i, 1], pred[i, 1]],
                z=[gt[i, 2], pred[i, 2]],
                mode='lines',
                line=dict(
                    color=line_color,
                    width=line_width,
                    dash=line_dash
                ),
                showlegend=False,
                hovertemplate=f'<b>关键点 #{i}</b><br>' +
                             f'误差: {error[i]:.2f}mm<extra></extra>'
            ))
        
        # 5. 设置布局
        fig.update_layout(
            title=dict(
                text=f'🎯 PointNet 骨盆关键点检测 - 3D交互式查看<br>' +
                     f'<sub>平均误差: {sample["mean_error"]:.2f}mm | ' +
                     f'5mm准确率: {sample["accuracy_5mm"]:.1f}% | ' +
                     f'关键点数量: {len(gt)}</sub>',
                x=0.5,
                font=dict(size=18)
            ),
            scene=dict(
                xaxis_title='X 坐标 (mm)',
                yaxis_title='Y 坐标 (mm)',
                zaxis_title='Z 坐标 (mm)',
                camera=dict(
                    eye=dict(x=1.5, y=1.5, z=1.5),
                    center=dict(x=0, y=0, z=0),
                    up=dict(x=0, y=0, z=1)
                ),
                aspectmode='cube',
                bgcolor='white',
                xaxis=dict(
                    backgroundcolor="rgb(230, 230,230)",
                    gridcolor="white",
                    showbackground=True,
                    zerolinecolor="white"
                ),
                yaxis=dict(
                    backgroundcolor="rgb(230, 230,230)",
                    gridcolor="white",
                    showbackground=True,
                    zerolinecolor="white"
                ),
                zaxis=dict(
                    backgroundcolor="rgb(230, 230,230)",
                    gridcolor="white",
                    showbackground=True,
                    zerolinecolor="white"
                )
            ),
            width=1200,
            height=800,
            margin=dict(l=0, r=0, t=80, b=0),
            legend=dict(
                x=0.02,
                y=0.98,
                bgcolor="rgba(255,255,255,0.8)",
                bordercolor="rgba(0,0,0,0.2)",
                borderwidth=1
            )
        )
        
        # 6. 添加注释
        fig.add_annotation(
            text="🎮 操作说明:<br>" +
                 "• 鼠标拖动旋转视角<br>" +
                 "• 滚轮缩放<br>" +
                 "• 双击重置视角<br>" +
                 "• 悬停查看详情<br>" +
                 "• 点击图例切换显示",
            xref="paper", yref="paper",
            x=0.02, y=0.02,
            xanchor="left", yanchor="bottom",
            showarrow=False,
            font=dict(size=12),
            bgcolor="rgba(255,255,255,0.8)",
            bordercolor="rgba(0,0,0,0.2)",
            borderwidth=1
        )
        
        return fig
    
    def show_3d_interactive(self):
        """显示交互式3D可视化"""
        print("🚀 启动直接3D查看器...")
        
        # 1. 加载数据并预测
        sample = self.load_and_predict()
        
        # 2. 创建3D图表
        fig = self.create_3d_plot(sample)
        
        # 3. 直接在浏览器中显示
        print("🌐 正在浏览器中打开3D可视化...")
        print("📊 性能统计:")
        print(f"  • 平均误差: {sample['mean_error']:.2f}mm")
        print(f"  • 5mm准确率: {sample['accuracy_5mm']:.1f}%")
        print(f"  • 关键点数量: {len(sample['ground_truth'])}")
        print(f"  • 最大误差: {np.max(sample['error']):.2f}mm")
        print(f"  • 最小误差: {np.min(sample['error']):.2f}mm")
        
        # 显示图表（会自动在浏览器中打开）
        pyo.plot(fig, auto_open=True)
        
        print("🎉 3D可视化已在浏览器中打开!")
        print("🎮 您现在可以:")
        print("  • 用鼠标拖动旋转3D视图")
        print("  • 用滚轮缩放")
        print("  • 双击重置视角")
        print("  • 悬停在点上查看详细信息")
        print("  • 点击图例显示/隐藏不同类型的点")
        
        return sample, fig

def create_multiple_samples_viewer():
    """创建多样本查看器"""
    print("🎮 创建多样本3D查看器...")
    
    viewer = Direct3DViewer()
    
    # 加载模型
    model = BestSimplePointNet(num_keypoints=57)
    model_path = "output/scale_corrected_training/best_baseline_model.pth"
    
    if Path(model_path).exists():
        checkpoint = torch.load(model_path, map_location=viewer.device, weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"✅ 模型加载成功")
    
    model = model.to(viewer.device)
    model.eval()
    
    # 加载数据
    data_loader_manager = ImprovedDataLoader(
        data_root=viewer.data_root,
        batch_size=1,
        num_workers=0,
        num_points=512
    )
    
    _, val_loader = data_loader_manager.create_dataloaders(train_ratio=0.8)
    
    # 获取3个样本
    samples = []
    print("进行预测...")
    with torch.no_grad():
        for i, (point_cloud, keypoints) in enumerate(val_loader):
            if i >= 3:  # 取3个样本
                break
            
            point_cloud = point_cloud.to(viewer.device)
            keypoints = keypoints.to(viewer.device)
            
            pred_keypoints = model(point_cloud)
            error = torch.norm(pred_keypoints - keypoints, dim=2).cpu().numpy()
            
            sample_data = {
                'id': i + 1,
                'point_cloud': point_cloud.cpu().numpy()[0],
                'ground_truth': keypoints.cpu().numpy()[0],
                'prediction': pred_keypoints.cpu().numpy()[0],
                'error': error[0],
                'mean_error': np.mean(error[0]),
                'accuracy_5mm': (error[0] <= 5.0).mean() * 100
            }
            
            samples.append(sample_data)
            print(f"样本 {i+1}: 误差 {sample_data['mean_error']:.2f}mm, 准确率 {sample_data['accuracy_5mm']:.1f}%")
    
    # 创建多子图
    from plotly.subplots import make_subplots
    
    fig = make_subplots(
        rows=1, cols=3,
        specs=[[{'type': 'scatter3d'}, {'type': 'scatter3d'}, {'type': 'scatter3d'}]],
        subplot_titles=[f'样本 {s["id"]} (误差: {s["mean_error"]:.2f}mm)' for s in samples],
        horizontal_spacing=0.02
    )
    
    # 为每个样本添加数据
    for idx, sample in enumerate(samples):
        col = idx + 1
        
        pc = sample['point_cloud']
        gt = sample['ground_truth']
        pred = sample['prediction']
        error = sample['error']
        
        # 添加点云
        fig.add_trace(
            go.Scatter3d(
                x=pc[:, 0], y=pc[:, 1], z=pc[:, 2],
                mode='markers',
                marker=dict(size=1, color='lightgray', opacity=0.1),
                name=f'点云_{idx+1}',
                showlegend=(idx == 0),
                hoverinfo='skip'
            ),
            row=1, col=col
        )
        
        # 添加真实关键点
        fig.add_trace(
            go.Scatter3d(
                x=gt[:, 0], y=gt[:, 1], z=gt[:, 2],
                mode='markers',
                marker=dict(size=6, color='red', symbol='circle'),
                name='真实关键点',
                showlegend=(idx == 0),
                hovertemplate=f'<b>样本{idx+1} 真实点</b><br>X: %{{x:.2f}}<br>Y: %{{y:.2f}}<br>Z: %{{z:.2f}}<extra></extra>'
            ),
            row=1, col=col
        )
        
        # 添加预测关键点
        fig.add_trace(
            go.Scatter3d(
                x=pred[:, 0], y=pred[:, 1], z=pred[:, 2],
                mode='markers',
                marker=dict(
                    size=6, 
                    color=error,
                    colorscale='RdYlGn_r',
                    cmin=0, cmax=8,
                    symbol='diamond',
                    showscale=(idx == 0),
                    colorbar=dict(title="误差 (mm)", len=0.5) if idx == 0 else None
                ),
                name='预测关键点',
                showlegend=(idx == 0),
                hovertemplate=f'<b>样本{idx+1} 预测点</b><br>X: %{{x:.2f}}<br>Y: %{{y:.2f}}<br>Z: %{{z:.2f}}<br>误差: %{{marker.color:.2f}}mm<extra></extra>'
            ),
            row=1, col=col
        )
    
    # 更新布局
    fig.update_layout(
        title=dict(
            text='🎯 PointNet 多样本3D对比查看器',
            x=0.5,
            font=dict(size=20)
        ),
        height=600,
        margin=dict(l=0, r=0, t=60, b=0),
        scene=dict(aspectmode='cube'),
        scene2=dict(aspectmode='cube'),
        scene3=dict(aspectmode='cube')
    )
    
    print("🌐 正在浏览器中打开多样本3D对比...")
    pyo.plot(fig, auto_open=True)
    
    print("🎉 多样本3D对比已在浏览器中打开!")
    return samples, fig

def main():
    """主函数"""
    print("选择查看模式:")
    print("1. 单样本详细3D查看")
    print("2. 多样本3D对比")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == "1":
            viewer = Direct3DViewer()
            sample, fig = viewer.show_3d_interactive()
        elif choice == "2":
            samples, fig = create_multiple_samples_viewer()
        else:
            print("默认显示单样本详细查看...")
            viewer = Direct3DViewer()
            sample, fig = viewer.show_3d_interactive()
            
    except KeyboardInterrupt:
        print("\n用户取消操作")
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        # 默认显示单样本
        viewer = Direct3DViewer()
        sample, fig = viewer.show_3d_interactive()

if __name__ == "__main__":
    main()
