#!/usr/bin/env python3
"""
模型偏差诊断工具
分析模型为什么将所有关键点预测到中心
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps

# 关键点名称
KEYPOINT_NAMES = {
    0: "L-ASIS", 1: "R-ASIS", 2: "L-PSIS", 3: "R-PSIS",
    4: "L-IC", 5: "R-IC", 6: "SP", 7: "L-SIJ", 8: "R-SIJ",
    9: "L-IS", 10: "R-IS", 11: "CT"
}

def analyze_model_predictions(point_cloud, true_keypoints, pred_keypoints, sample_id):
    """分析模型预测的偏差模式"""
    
    print(f"\n🔍 Model Bias Analysis for Sample {sample_id}")
    print("=" * 60)
    
    # 1. 计算点云中心
    pc_center = np.mean(point_cloud, axis=0)
    pc_std = np.std(point_cloud, axis=0)
    
    print(f"📊 Point Cloud Statistics:")
    print(f"   Center: [{pc_center[0]:.1f}, {pc_center[1]:.1f}, {pc_center[2]:.1f}]")
    print(f"   Std Dev: [{pc_std[0]:.1f}, {pc_std[1]:.1f}, {pc_std[2]:.1f}]")
    
    # 2. 分析真实关键点分布
    true_center = np.mean(true_keypoints, axis=0)
    true_std = np.std(true_keypoints, axis=0)
    true_range = np.max(true_keypoints, axis=0) - np.min(true_keypoints, axis=0)
    
    print(f"\n🎯 True Keypoints Statistics:")
    print(f"   Center: [{true_center[0]:.1f}, {true_center[1]:.1f}, {true_center[2]:.1f}]")
    print(f"   Std Dev: [{true_std[0]:.1f}, {true_std[1]:.1f}, {true_std[2]:.1f}]")
    print(f"   Range: [{true_range[0]:.1f}, {true_range[1]:.1f}, {true_range[2]:.1f}]")
    
    # 3. 分析预测关键点分布
    pred_center = np.mean(pred_keypoints, axis=0)
    pred_std = np.std(pred_keypoints, axis=0)
    pred_range = np.max(pred_keypoints, axis=0) - np.min(pred_keypoints, axis=0)
    
    print(f"\n🤖 Predicted Keypoints Statistics:")
    print(f"   Center: [{pred_center[0]:.1f}, {pred_center[1]:.1f}, {pred_center[2]:.1f}]")
    print(f"   Std Dev: [{pred_std[0]:.1f}, {pred_std[1]:.1f}, {pred_std[2]:.1f}]")
    print(f"   Range: [{pred_range[0]:.1f}, {pred_range[1]:.1f}, {pred_range[2]:.1f}]")
    
    # 4. 计算偏差指标
    center_bias = np.linalg.norm(pred_center - true_center)
    spread_ratio = np.mean(pred_std / true_std)
    
    print(f"\n⚠️ Bias Indicators:")
    print(f"   Center Bias: {center_bias:.1f}mm")
    print(f"   Spread Ratio: {spread_ratio:.3f} (should be ~1.0)")
    
    # 5. 分析每个关键点的偏差
    print(f"\n📋 Individual Keypoint Analysis:")
    errors = []
    center_distances_true = []
    center_distances_pred = []
    
    for i in range(len(true_keypoints)):
        error = np.linalg.norm(pred_keypoints[i] - true_keypoints[i])
        dist_true = np.linalg.norm(true_keypoints[i] - pc_center)
        dist_pred = np.linalg.norm(pred_keypoints[i] - pc_center)
        
        errors.append(error)
        center_distances_true.append(dist_true)
        center_distances_pred.append(dist_pred)
        
        print(f"   {KEYPOINT_NAMES[i]:8} | Error: {error:5.1f}mm | "
              f"True dist: {dist_true:5.1f}mm | Pred dist: {dist_pred:5.1f}mm")
    
    # 6. 诊断结论
    print(f"\n🔬 Diagnosis:")
    
    if spread_ratio < 0.3:
        print("   ❌ SEVERE COLLAPSE: Model predicts all points near center")
        print("   💡 Possible causes:")
        print("      - Learning rate too high")
        print("      - Loss function not working properly")
        print("      - Insufficient training data")
        print("      - Model architecture too simple")
    elif spread_ratio < 0.7:
        print("   ⚠️ MODERATE COLLAPSE: Model predictions too concentrated")
        print("   💡 Possible causes:")
        print("      - Regularization too strong")
        print("      - Training not converged")
    else:
        print("   ✅ SPREAD OK: Model predictions have reasonable spread")
    
    if center_bias > 20:
        print("   ❌ CENTER BIAS: Model systematically biased toward center")
        print("   💡 Possible causes:")
        print("      - Data preprocessing issues")
        print("      - Coordinate system problems")
        print("      - Model initialization bias")
    
    avg_error = np.mean(errors)
    if avg_error > 50:
        print("   ❌ HIGH ERROR: Average error too large")
        print("   💡 Model needs retraining or architecture changes")
    
    return {
        'center_bias': center_bias,
        'spread_ratio': spread_ratio,
        'avg_error': avg_error,
        'errors': errors,
        'pc_center': pc_center,
        'true_center': true_center,
        'pred_center': pred_center
    }

def create_bias_visualization(point_cloud, true_keypoints, pred_keypoints, 
                            analysis_results, sample_id):
    """创建偏差可视化"""
    
    fig = plt.figure(figsize=(20, 6))
    
    # 1. 3D散点图 - 显示偏差
    ax1 = fig.add_subplot(1, 3, 1, projection='3d')
    
    # 显示点云（采样）
    if len(point_cloud) > 3000:
        indices = np.random.choice(len(point_cloud), 3000, replace=False)
        display_pc = point_cloud[indices]
    else:
        display_pc = point_cloud
    
    ax1.scatter(display_pc[:, 0], display_pc[:, 1], display_pc[:, 2],
               c='lightgray', s=0.5, alpha=0.3)
    
    # 显示真实关键点
    ax1.scatter(true_keypoints[:, 0], true_keypoints[:, 1], true_keypoints[:, 2],
               c='green', s=100, marker='o', label='True', alpha=0.8)
    
    # 显示预测关键点
    ax1.scatter(pred_keypoints[:, 0], pred_keypoints[:, 1], pred_keypoints[:, 2],
               c='red', s=100, marker='x', label='Predicted', alpha=0.8)
    
    # 显示中心点
    pc_center = analysis_results['pc_center']
    ax1.scatter(pc_center[0], pc_center[1], pc_center[2],
               c='blue', s=200, marker='*', label='PC Center')
    
    # 连接线
    for i in range(len(true_keypoints)):
        ax1.plot([true_keypoints[i, 0], pred_keypoints[i, 0]],
                [true_keypoints[i, 1], pred_keypoints[i, 1]],
                [true_keypoints[i, 2], pred_keypoints[i, 2]],
                'k--', alpha=0.5)
    
    ax1.set_title(f'3D Bias Visualization\nSample {sample_id}')
    ax1.legend()
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')
    
    # 2. 误差分布图
    ax2 = fig.add_subplot(1, 3, 2)
    
    errors = analysis_results['errors']
    keypoint_names = [KEYPOINT_NAMES[i] for i in range(len(errors))]
    
    bars = ax2.bar(range(len(errors)), errors, color='red', alpha=0.7)
    ax2.set_xlabel('Keypoint')
    ax2.set_ylabel('Error (mm)')
    ax2.set_title(f'Individual Keypoint Errors\nAvg: {np.mean(errors):.1f}mm')
    ax2.set_xticks(range(len(errors)))
    ax2.set_xticklabels(keypoint_names, rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)
    
    # 添加平均线
    ax2.axhline(y=np.mean(errors), color='black', linestyle='--', 
               label=f'Average: {np.mean(errors):.1f}mm')
    ax2.legend()
    
    # 3. 中心距离比较
    ax3 = fig.add_subplot(1, 3, 3)
    
    # 计算到中心的距离
    true_distances = [np.linalg.norm(kp - pc_center) for kp in true_keypoints]
    pred_distances = [np.linalg.norm(kp - pc_center) for kp in pred_keypoints]
    
    x = np.arange(len(keypoint_names))
    width = 0.35
    
    ax3.bar(x - width/2, true_distances, width, label='True Distance', 
           color='green', alpha=0.7)
    ax3.bar(x + width/2, pred_distances, width, label='Predicted Distance', 
           color='red', alpha=0.7)
    
    ax3.set_xlabel('Keypoint')
    ax3.set_ylabel('Distance from Center (mm)')
    ax3.set_title('Distance from Point Cloud Center')
    ax3.set_xticks(x)
    ax3.set_xticklabels(keypoint_names, rotation=45, ha='right')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存
    filename = f'model_bias_analysis_{sample_id}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"\n📊 Bias analysis visualization saved: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🔍 Model Bias Diagnosis Tool")
    print("Analyzing why model predicts all keypoints near center")
    print("=" * 60)
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model.eval()
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # 分析多个样本
    for sample_idx in range(min(3, len(sample_ids))):
        sample_id = sample_ids[sample_idx]
        point_cloud = point_clouds[sample_idx]
        true_keypoints = keypoints[sample_idx]
        
        # 采样点云用于预测
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            pc_sampled = point_cloud[indices]
        else:
            pc_sampled = point_cloud
        
        # 预测关键点
        pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
        
        with torch.no_grad():
            pred_heatmaps = model(pc_tensor)
        
        pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T
        pred_keypoints, confidences = extract_keypoints_from_heatmaps(
            pred_heatmaps_np.T, pc_sampled
        )
        
        # 分析偏差
        analysis_results = analyze_model_predictions(
            point_cloud, true_keypoints, pred_keypoints, sample_id
        )
        
        # 创建可视化
        create_bias_visualization(
            point_cloud, true_keypoints, pred_keypoints, 
            analysis_results, sample_id
        )
    
    print(f"\n🎯 Recommendations:")
    print("1. Check data preprocessing - ensure coordinates are properly normalized")
    print("2. Verify loss function - make sure it encourages spatial diversity")
    print("3. Increase model capacity - current model might be too simple")
    print("4. Add regularization to prevent collapse to center")
    print("5. Check training data quality - ensure keypoints are correctly labeled")
    print("6. Consider using different initialization strategy")

if __name__ == "__main__":
    main()
