#!/usr/bin/env python3
"""
数据集质量检测
全面分析f3_reduced_12kp_stable.npz数据集的质量问题
检测数据异常、标注错误、分布问题等
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.decomposition import PCA
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler
import pandas as pd
import json
from scipy import stats
from scipy.spatial.distance import pdist, squareform
import warnings
warnings.filterwarnings('ignore')

class DatasetQualityAnalyzer:
    """数据集质量分析器"""
    
    def __init__(self, data_path):
        self.data_path = data_path
        self.load_data()
        print("🔍 数据集质量分析器初始化完成")
    
    def load_data(self):
        """加载数据"""
        print("📂 加载数据集...")
        data = np.load(self.data_path, allow_pickle=True)
        
        self.sample_ids = data['sample_ids']
        self.point_clouds = data['point_clouds']
        self.keypoints = data['keypoints']
        
        print(f"   样本数: {len(self.sample_ids)}")
        print(f"   点云形状: {self.point_clouds.shape}")
        print(f"   关键点形状: {self.keypoints.shape}")
    
    def basic_statistics(self):
        """基础统计分析"""
        print("\n📊 基础统计分析")
        print("=" * 50)
        
        stats = {}
        
        # 样本ID分析
        print("🏷️  样本ID分析:")
        print(f"   唯一样本数: {len(np.unique(self.sample_ids))}")
        print(f"   是否有重复: {'是' if len(self.sample_ids) != len(np.unique(self.sample_ids)) else '否'}")
        
        # 点云统计
        print("\n☁️  点云统计:")
        point_counts = [len(pc) for pc in self.point_clouds]
        print(f"   点数范围: {min(point_counts)} - {max(point_counts)}")
        print(f"   平均点数: {np.mean(point_counts):.1f}")
        print(f"   点数标准差: {np.std(point_counts):.1f}")
        print(f"   点数是否一致: {'是' if len(set(point_counts)) == 1 else '否'}")
        
        # 关键点统计
        print("\n🎯 关键点统计:")
        all_keypoints = np.concatenate(self.keypoints, axis=0)
        
        for axis, name in enumerate(['X', 'Y', 'Z']):
            axis_data = all_keypoints[:, axis]
            print(f"   {name}轴: [{axis_data.min():.3f}, {axis_data.max():.3f}], "
                  f"均值={axis_data.mean():.3f}, 标准差={axis_data.std():.3f}")
        
        # 关键点间距离
        print("\n📏 关键点间距离:")
        inter_distances = []
        for kps in self.keypoints:
            distances = pdist(kps)
            inter_distances.extend(distances)
        
        inter_distances = np.array(inter_distances)
        print(f"   距离范围: [{inter_distances.min():.3f}, {inter_distances.max():.3f}]")
        print(f"   平均距离: {inter_distances.mean():.3f}")
        print(f"   距离标准差: {inter_distances.std():.3f}")
        
        stats['point_counts'] = point_counts
        stats['keypoint_ranges'] = {
            'x': [all_keypoints[:, 0].min(), all_keypoints[:, 0].max()],
            'y': [all_keypoints[:, 1].min(), all_keypoints[:, 1].max()],
            'z': [all_keypoints[:, 2].min(), all_keypoints[:, 2].max()]
        }
        stats['inter_distances'] = inter_distances
        
        return stats
    
    def detect_outliers(self):
        """异常值检测"""
        print("\n🚨 异常值检测")
        print("=" * 50)
        
        outliers = {}
        
        # 1. 关键点位置异常
        print("🎯 关键点位置异常检测:")
        all_keypoints = np.concatenate(self.keypoints, axis=0)
        
        for axis, name in enumerate(['X', 'Y', 'Z']):
            axis_data = all_keypoints[:, axis]
            
            # Z-score方法
            z_scores = np.abs(stats.zscore(axis_data))
            outlier_indices = np.where(z_scores > 3)[0]
            
            # IQR方法
            Q1 = np.percentile(axis_data, 25)
            Q3 = np.percentile(axis_data, 75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            iqr_outliers = np.where((axis_data < lower_bound) | (axis_data > upper_bound))[0]
            
            print(f"   {name}轴: Z-score异常{len(outlier_indices)}个, IQR异常{len(iqr_outliers)}个")
            
            outliers[f'{name.lower()}_zscore'] = outlier_indices
            outliers[f'{name.lower()}_iqr'] = iqr_outliers
        
        # 2. 关键点间距离异常
        print("\n📏 关键点间距离异常:")
        distance_outliers = []
        
        for i, kps in enumerate(self.keypoints):
            distances = pdist(kps)
            
            # 检查是否有异常近或异常远的关键点
            if distances.min() < 5.0:  # 异常近
                distance_outliers.append((i, 'too_close', distances.min()))
            if distances.max() > 150.0:  # 异常远
                distance_outliers.append((i, 'too_far', distances.max()))
        
        print(f"   距离异常样本: {len(distance_outliers)}个")
        for sample_id, issue, value in distance_outliers:
            print(f"     样本{sample_id}: {issue} = {value:.3f}")
        
        outliers['distance_outliers'] = distance_outliers
        
        # 3. 点云密度异常
        print("\n☁️  点云密度异常:")
        density_outliers = []
        
        for i, pc in enumerate(self.point_clouds):
            # 计算点云的边界框体积
            ranges = np.ptp(pc, axis=0)  # 每个轴的范围
            volume = np.prod(ranges)
            density = len(pc) / volume if volume > 0 else 0
            
            # 检查密度异常
            if density < 100 or density > 10000:  # 经验阈值
                density_outliers.append((i, density))
        
        print(f"   密度异常样本: {len(density_outliers)}个")
        for sample_id, density in density_outliers:
            print(f"     样本{sample_id}: 密度 = {density:.1f}")
        
        outliers['density_outliers'] = density_outliers
        
        return outliers
    
    def consistency_check(self):
        """一致性检查"""
        print("\n🔄 数据一致性检查")
        print("=" * 50)
        
        consistency_issues = {}
        
        # 1. 点云和关键点的空间一致性
        print("🌐 空间一致性检查:")
        spatial_issues = []
        
        for i, (pc, kps) in enumerate(zip(self.point_clouds, self.keypoints)):
            # 检查关键点是否在点云范围内
            pc_min = pc.min(axis=0)
            pc_max = pc.max(axis=0)
            
            for j, kp in enumerate(kps):
                # 允许一定的容差
                tolerance = 5.0
                if not all(pc_min - tolerance <= kp) or not all(kp <= pc_max + tolerance):
                    spatial_issues.append((i, j, kp, pc_min, pc_max))
        
        print(f"   空间不一致: {len(spatial_issues)}个关键点")
        if len(spatial_issues) > 0:
            print("   前5个问题:")
            for issue in spatial_issues[:5]:
                sample_id, kp_id, kp, pc_min, pc_max = issue
                print(f"     样本{sample_id}关键点{kp_id}: {kp} 不在 [{pc_min}, {pc_max}] 范围内")
        
        consistency_issues['spatial_issues'] = spatial_issues
        
        # 2. 关键点配置一致性
        print("\n🎯 关键点配置一致性:")
        kp_configs = []
        
        for kps in self.keypoints:
            # 计算关键点的相对位置模式
            center = kps.mean(axis=0)
            relative_positions = kps - center
            
            # 计算特征向量 (距离中心的距离)
            distances_to_center = np.linalg.norm(relative_positions, axis=1)
            kp_configs.append(distances_to_center)
        
        kp_configs = np.array(kp_configs)
        
        # 检查配置的相似性
        config_similarities = []
        for i in range(len(kp_configs)):
            for j in range(i+1, len(kp_configs)):
                similarity = np.corrcoef(kp_configs[i], kp_configs[j])[0, 1]
                config_similarities.append(similarity)
        
        config_similarities = np.array(config_similarities)
        avg_similarity = np.mean(config_similarities)
        
        print(f"   关键点配置平均相似度: {avg_similarity:.3f}")
        print(f"   相似度标准差: {np.std(config_similarities):.3f}")
        
        if avg_similarity < 0.5:
            print("   ⚠️  关键点配置差异较大，可能存在标注不一致")
        
        consistency_issues['config_similarity'] = avg_similarity
        consistency_issues['config_std'] = np.std(config_similarities)
        
        return consistency_issues
    
    def data_distribution_analysis(self):
        """数据分布分析"""
        print("\n📈 数据分布分析")
        print("=" * 50)
        
        distribution_info = {}
        
        # 1. 关键点分布分析
        print("🎯 关键点分布分析:")
        all_keypoints = np.concatenate(self.keypoints, axis=0)
        
        # PCA分析
        pca = PCA(n_components=3)
        pca_result = pca.fit_transform(all_keypoints)
        
        print(f"   PCA解释方差比: {pca.explained_variance_ratio_}")
        print(f"   累积解释方差: {np.cumsum(pca.explained_variance_ratio_)}")
        
        # 聚类分析
        scaler = StandardScaler()
        scaled_keypoints = scaler.fit_transform(all_keypoints)
        
        dbscan = DBSCAN(eps=0.5, min_samples=5)
        clusters = dbscan.fit_predict(scaled_keypoints)
        
        n_clusters = len(set(clusters)) - (1 if -1 in clusters else 0)
        n_noise = list(clusters).count(-1)
        
        print(f"   DBSCAN聚类数: {n_clusters}")
        print(f"   噪声点数: {n_noise}")
        
        distribution_info['pca_variance_ratio'] = pca.explained_variance_ratio_
        distribution_info['n_clusters'] = n_clusters
        distribution_info['n_noise'] = n_noise
        
        # 2. 样本间相似性分析
        print("\n🔍 样本间相似性分析:")
        
        # 计算每个样本的特征向量
        sample_features = []
        for kps in self.keypoints:
            # 使用关键点间距离作为特征
            distances = pdist(kps)
            sample_features.append(distances)
        
        sample_features = np.array(sample_features)
        
        # 计算样本间相似性
        sample_similarities = []
        for i in range(len(sample_features)):
            for j in range(i+1, len(sample_features)):
                similarity = np.corrcoef(sample_features[i], sample_features[j])[0, 1]
                if not np.isnan(similarity):
                    sample_similarities.append(similarity)
        
        sample_similarities = np.array(sample_similarities)
        
        print(f"   样本间平均相似度: {np.mean(sample_similarities):.3f}")
        print(f"   相似度标准差: {np.std(sample_similarities):.3f}")
        print(f"   最高相似度: {np.max(sample_similarities):.3f}")
        print(f"   最低相似度: {np.min(sample_similarities):.3f}")
        
        # 检查是否有重复或近似重复的样本
        high_similarity_pairs = []
        for i in range(len(sample_features)):
            for j in range(i+1, len(sample_features)):
                similarity = np.corrcoef(sample_features[i], sample_features[j])[0, 1]
                if not np.isnan(similarity) and similarity > 0.95:
                    high_similarity_pairs.append((i, j, similarity))
        
        print(f"   高相似度样本对 (>0.95): {len(high_similarity_pairs)}个")
        for i, j, sim in high_similarity_pairs:
            print(f"     样本{i} vs 样本{j}: 相似度 = {sim:.4f}")
        
        distribution_info['sample_similarities'] = sample_similarities
        distribution_info['high_similarity_pairs'] = high_similarity_pairs
        
        return distribution_info
    
    def label_quality_assessment(self):
        """标注质量评估"""
        print("\n🏷️  标注质量评估")
        print("=" * 50)
        
        label_quality = {}
        
        # 1. 标注精度评估
        print("🎯 标注精度评估:")
        
        # 计算每个关键点的变异系数
        keypoint_variations = []
        for kp_idx in range(12):  # 12个关键点
            kp_positions = [kps[kp_idx] for kps in self.keypoints]
            kp_positions = np.array(kp_positions)
            
            # 计算每个轴的变异系数
            variations = []
            for axis in range(3):
                axis_data = kp_positions[:, axis]
                cv = np.std(axis_data) / np.abs(np.mean(axis_data)) if np.mean(axis_data) != 0 else np.inf
                variations.append(cv)
            
            keypoint_variations.append(variations)
            print(f"   关键点{kp_idx}: CV = [{variations[0]:.3f}, {variations[1]:.3f}, {variations[2]:.3f}]")
        
        keypoint_variations = np.array(keypoint_variations)
        avg_variation = np.mean(keypoint_variations)
        
        print(f"   平均变异系数: {avg_variation:.3f}")
        
        if avg_variation > 0.5:
            print("   ⚠️  标注变异较大，可能存在标注不一致")
        
        label_quality['keypoint_variations'] = keypoint_variations
        label_quality['avg_variation'] = avg_variation
        
        # 2. 解剖学合理性检查
        print("\n🔬 解剖学合理性检查:")
        
        anatomical_issues = []
        
        for i, kps in enumerate(self.keypoints):
            # 检查关键点的相对位置是否合理
            # 这里需要根据具体的解剖学知识来定义规则
            
            # 示例：检查是否有关键点重叠
            distances = pdist(kps)
            min_distance = distances.min()
            
            if min_distance < 1.0:  # 关键点过于接近
                anatomical_issues.append((i, 'too_close', min_distance))
            
            # 检查关键点是否形成合理的几何结构
            # 计算关键点的凸包体积
            try:
                from scipy.spatial import ConvexHull
                hull = ConvexHull(kps)
                volume = hull.volume
                
                if volume < 100:  # 体积过小
                    anatomical_issues.append((i, 'small_volume', volume))
                elif volume > 100000:  # 体积过大
                    anatomical_issues.append((i, 'large_volume', volume))
            except:
                anatomical_issues.append((i, 'convex_hull_error', 0))
        
        print(f"   解剖学异常: {len(anatomical_issues)}个样本")
        for sample_id, issue, value in anatomical_issues[:5]:
            print(f"     样本{sample_id}: {issue} = {value:.3f}")
        
        label_quality['anatomical_issues'] = anatomical_issues
        
        return label_quality
    
    def generate_report(self):
        """生成完整的质量报告"""
        print("\n📋 生成数据集质量报告")
        print("=" * 80)
        
        # 执行所有分析
        basic_stats = self.basic_statistics()
        outliers = self.detect_outliers()
        consistency = self.consistency_check()
        distribution = self.data_distribution_analysis()
        label_quality = self.label_quality_assessment()
        
        # 汇总报告
        report = {
            'dataset_path': self.data_path,
            'total_samples': len(self.sample_ids),
            'analysis_date': '2025-07-17',
            'basic_statistics': {
                'unique_samples': len(np.unique(self.sample_ids)),
                'point_count_range': [min(basic_stats['point_counts']), max(basic_stats['point_counts'])],
                'keypoint_ranges': basic_stats['keypoint_ranges'],
                'avg_inter_distance': float(np.mean(basic_stats['inter_distances']))
            },
            'quality_issues': {
                'outliers_detected': len(outliers.get('distance_outliers', [])),
                'spatial_inconsistencies': len(consistency.get('spatial_issues', [])),
                'config_similarity': consistency.get('config_similarity', 0),
                'high_similarity_pairs': len(distribution.get('high_similarity_pairs', [])),
                'anatomical_issues': len(label_quality.get('anatomical_issues', []))
            },
            'recommendations': []
        }
        
        # 生成建议
        print("\n💡 数据集质量评估结果:")
        
        total_issues = (
            len(outliers.get('distance_outliers', [])) +
            len(consistency.get('spatial_issues', [])) +
            len(distribution.get('high_similarity_pairs', [])) +
            len(label_quality.get('anatomical_issues', []))
        )
        
        print(f"   总体质量评分: {max(0, 100 - total_issues * 5)}/100")
        
        if len(outliers.get('distance_outliers', [])) > 0:
            print("   ⚠️  发现关键点距离异常")
            report['recommendations'].append("检查并修正关键点距离异常的样本")
        
        if len(consistency.get('spatial_issues', [])) > 0:
            print("   ⚠️  发现空间不一致问题")
            report['recommendations'].append("检查关键点与点云的空间对应关系")
        
        if consistency.get('config_similarity', 1) < 0.5:
            print("   ⚠️  关键点配置差异较大")
            report['recommendations'].append("统一关键点标注标准")
        
        if len(distribution.get('high_similarity_pairs', [])) > 0:
            print("   ⚠️  发现高相似度样本")
            report['recommendations'].append("检查是否存在重复样本")
        
        if label_quality.get('avg_variation', 0) > 0.5:
            print("   ⚠️  标注变异较大")
            report['recommendations'].append("提高标注一致性")
        
        # 特别检查：极简基线表现异常好的原因
        print(f"\n🔍 极简基线分析:")
        avg_variation = label_quality.get('avg_variation', 0)
        config_similarity = consistency.get('config_similarity', 0)
        
        if avg_variation < 0.3 and config_similarity > 0.8:
            print("   ✅ 关键点位置相对稳定，解释了极简基线的好表现")
            report['recommendations'].append("数据集可能适合简单的统计方法而非复杂模型")
        else:
            print("   ❓ 极简基线表现好的原因需要进一步分析")
        
        # 保存报告
        # 处理numpy类型以便JSON序列化
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            return obj
        
        # 递归转换所有numpy对象
        def recursive_convert(obj):
            if isinstance(obj, dict):
                return {k: recursive_convert(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [recursive_convert(v) for v in obj]
            else:
                return convert_numpy(obj)
        
        report_clean = recursive_convert(report)
        
        with open('dataset_quality_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_clean, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 质量报告已保存到: dataset_quality_report.json")
        
        return report

def main():
    """主函数"""
    print("🔍 **数据集质量检测**")
    print("📊 **全面分析f3_reduced_12kp_stable.npz**")
    print("🎯 **检测数据异常、标注错误、分布问题**")
    print("=" * 80)
    
    # 创建分析器
    analyzer = DatasetQualityAnalyzer('f3_reduced_12kp_stable.npz')
    
    # 生成完整报告
    report = analyzer.generate_report()
    
    print(f"\n🎉 **数据集质量检测完成!**")
    print(f"📋 详细报告已保存，请查看 dataset_quality_report.json")

if __name__ == "__main__":
    main()
