#!/usr/bin/env python3
"""
整理模型文件和实验结果
创建清晰的目录结构，便于管理和对比
"""

import os
import shutil
import json
import glob
from datetime import datetime

def create_directory_structure():
    """创建标准的目录结构"""
    
    directories = [
        "experiments",
        "experiments/baseline_models",
        "experiments/phase1_improvements", 
        "experiments/phase2_architectures",
        "experiments/results",
        "experiments/configs",
        "experiments/logs",
        "models",
        "models/best_models",
        "models/checkpoints",
        "data",
        "data/processed",
        "data/raw"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ 创建目录: {directory}")

def organize_model_files():
    """整理模型文件"""
    
    print("\n📁 **整理模型文件**")
    print("=" * 50)
    
    # 获取所有.pth文件
    pth_files = glob.glob("*.pth")
    
    if not pth_files:
        print("❌ 没有找到.pth模型文件")
        return
    
    # 分类模型文件
    model_categories = {
        "baseline": [],
        "phase1": [],
        "phase2": [],
        "other": []
    }
    
    for pth_file in pth_files:
        if "reduced_12kp" in pth_file or "12kp" in pth_file:
            if "improved" in pth_file or "conservative" in pth_file or "optimized" in pth_file:
                model_categories["phase1"].append(pth_file)
            elif "attention" in pth_file or "pyramid" in pth_file or "softmax" in pth_file:
                model_categories["phase2"].append(pth_file)
            else:
                model_categories["baseline"].append(pth_file)
        else:
            model_categories["other"].append(pth_file)
    
    # 移动文件到对应目录
    for category, files in model_categories.items():
        if not files:
            continue
            
        if category == "baseline":
            target_dir = "experiments/baseline_models"
        elif category == "phase1":
            target_dir = "experiments/phase1_improvements"
        elif category == "phase2":
            target_dir = "experiments/phase2_architectures"
        else:
            target_dir = "models/checkpoints"
        
        print(f"\n📂 {category.upper()} 模型:")
        for file in files:
            target_path = os.path.join(target_dir, file)
            shutil.move(file, target_path)
            print(f"   移动: {file} -> {target_path}")

def organize_result_files():
    """整理结果文件"""
    
    print("\n📊 **整理结果文件**")
    print("=" * 50)
    
    # 获取所有JSON结果文件
    json_files = glob.glob("*results*.json")
    
    for json_file in json_files:
        target_path = os.path.join("experiments/results", json_file)
        shutil.move(json_file, target_path)
        print(f"   移动: {json_file} -> {target_path}")

def organize_data_files():
    """整理数据文件"""
    
    print("\n💾 **整理数据文件**")
    print("=" * 50)
    
    # 获取所有.npz数据文件
    npz_files = glob.glob("*.npz")
    
    for npz_file in npz_files:
        if "reduced" in npz_file or "12kp" in npz_file:
            target_path = os.path.join("data/processed", npz_file)
        else:
            target_path = os.path.join("data/raw", npz_file)
        
        shutil.copy2(npz_file, target_path)  # 复制而不是移动，保留原文件
        print(f"   复制: {npz_file} -> {target_path}")

def create_experiment_summary():
    """创建实验总结"""
    
    print("\n📋 **创建实验总结**")
    print("=" * 50)
    
    summary = {
        "project": "Medical Point Cloud Keypoint Detection",
        "created": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "experiments": {
            "baseline": {
                "description": "12关键点基线模型",
                "best_result": "6.208mm",
                "status": "✅ 成功 - 当前最佳",
                "key_insights": [
                    "减少关键点数量从19到12个显著提升性能",
                    "稳定性选择策略有效",
                    "训练时间仅1.1分钟"
                ]
            },
            "phase1_improvements": {
                "description": "损失函数和超参数优化",
                "approaches": [
                    "Wing Loss + Focal Loss",
                    "保守Wing Loss改进", 
                    "超参数调优",
                    "最终优化组合"
                ],
                "best_result": "6.458mm (超参数调优)",
                "status": "❌ 未超越基线",
                "key_insights": [
                    "复杂损失函数在小数据集上容易过拟合",
                    "超参数调优空间有限",
                    "需要架构级改进"
                ]
            },
            "phase2_architectures": {
                "description": "架构级改进",
                "approaches": [
                    "Attention PointNet",
                    "Feature Pyramid PointNet", 
                    "Double SoftMax PointNet",
                    "Adaptive Double SoftMax PointNet"
                ],
                "best_result": "6.384mm (Feature Pyramid)",
                "status": "✅ 轻微改进",
                "key_insights": [
                    "Feature Pyramid PointNet表现最佳",
                    "注意力机制参数过多导致过拟合",
                    "双Softmax机制不适合当前数据规模"
                ]
            }
        },
        "current_best": {
            "model": "12关键点基线模型",
            "error": "6.208mm",
            "file": "experiments/baseline_models/best_reduced_12kp_f3.pth",
            "config": "12关键点 + 稳定性选择 + AdaptivePointNet"
        },
        "next_steps": [
            "在基线模型上应用双Softmax机制",
            "优化Feature Pyramid PointNet",
            "数据质量改进",
            "集成学习方法"
        ]
    }
    
    with open("experiments/experiment_summary.json", "w", encoding="utf-8") as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    print("✅ 实验总结已保存: experiments/experiment_summary.json")

def create_readme():
    """创建README文档"""
    
    readme_content = """# Medical Point Cloud Keypoint Detection

## 项目概述
医疗点云关键点检测项目，目标是实现5mm医疗级精度的关键点定位。

## 目录结构
```
├── experiments/                 # 实验相关文件
│   ├── baseline_models/        # 基线模型
│   ├── phase1_improvements/    # Phase 1改进模型
│   ├── phase2_architectures/   # Phase 2架构改进
│   ├── results/               # 实验结果JSON文件
│   ├── configs/               # 配置文件
│   └── logs/                  # 训练日志
├── models/                     # 模型文件
│   ├── best_models/           # 最佳模型
│   └── checkpoints/           # 检查点
├── data/                      # 数据文件
│   ├── processed/             # 处理后的数据
│   └── raw/                   # 原始数据
└── *.py                       # 训练和工具脚本
```

## 实验进展

### ✅ 基线模型 (当前最佳)
- **模型**: 12关键点 + 稳定性选择
- **性能**: 6.208mm验证误差
- **改进**: 相比19关键点提升27.4%
- **文件**: `experiments/baseline_models/best_reduced_12kp_f3.pth`

### ❌ Phase 1: 损失函数优化
- **尝试**: Wing Loss, Focal Loss, 超参数调优
- **最佳**: 6.458mm (超参数调优)
- **结论**: 复杂损失函数在小数据集上效果有限

### ✅ Phase 2: 架构改进
- **最佳**: Feature Pyramid PointNet (6.384mm)
- **发现**: 多层特征融合有效，注意力机制过拟合

## 下一步计划
1. 在基线模型上应用双Softmax机制
2. 优化Feature Pyramid PointNet
3. 数据质量改进
4. 目标：突破6mm，达到5.5mm医疗级精度

## 使用方法
```bash
# 训练基线模型
python train_reduced_keypoints_f3.py

# Phase 2架构对比
python train_phase2_attention.py

# 整理文件结构
python organize_models.py
```
"""
    
    with open("README.md", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("✅ README文档已创建: README.md")

def main():
    """主函数"""
    
    print("🧹 **整理项目文件结构**")
    print("🎯 **目标**: 创建清晰的目录结构，便于管理实验")
    print("=" * 80)
    
    # 创建目录结构
    create_directory_structure()
    
    # 整理各类文件
    organize_model_files()
    organize_result_files() 
    organize_data_files()
    
    # 创建文档
    create_experiment_summary()
    create_readme()
    
    print(f"\n🎉 **文件整理完成!**")
    print("=" * 50)
    print("📁 目录结构已标准化")
    print("📊 实验结果已分类整理") 
    print("📋 实验总结已生成")
    print("📖 README文档已创建")
    print("\n💡 **建议**: 查看 experiments/experiment_summary.json 了解详细实验进展")

if __name__ == "__main__":
    main()
