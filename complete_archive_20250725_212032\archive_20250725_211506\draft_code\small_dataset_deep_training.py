#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小数据集深度训练 - 针对96样本的优化训练策略
Small Dataset Deep Training - Optimized Training Strategy for 96 Samples
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split, KFold
import json
import matplotlib.pyplot as plt
import pandas as pd
import time
import os
from tqdm import tqdm
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置样式
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

class EnhancedMedicalKeypointDataset(Dataset):
    """增强的医疗关键点数据集 - 针对小数据集优化"""
    
    def __init__(self, point_clouds, keypoints, num_points=50000, keypoint_indices=None, 
                 augment=False, strong_augment=False):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.num_points = num_points
        self.keypoint_indices = keypoint_indices
        self.augment = augment
        self.strong_augment = strong_augment
        
        # 如果指定了关键点索引，选择对应的关键点
        if keypoint_indices is not None:
            self.keypoints = self.keypoints[:, keypoint_indices, :]
    
    def __len__(self):
        # 对于小数据集，可以通过数据增强增加有效样本数
        multiplier = 3 if self.strong_augment else 1
        return len(self.point_clouds) * multiplier
    
    def __getitem__(self, idx):
        # 对于增强的数据集，循环使用原始样本
        real_idx = idx % len(self.point_clouds)
        
        pc = self.point_clouds[real_idx].copy()
        kp = self.keypoints[real_idx].copy()
        
        # 重采样点云到指定数量
        if len(pc) != self.num_points:
            if len(pc) > self.num_points:
                indices = np.random.choice(len(pc), self.num_points, replace=False)
                pc = pc[indices]
            else:
                indices = np.random.choice(len(pc), self.num_points, replace=True)
                pc = pc[indices]
        
        # 数据增强
        if self.augment or self.strong_augment:
            # 基础增强
            # 1. 高斯噪声
            noise_std = 0.02 if self.strong_augment else 0.01
            pc += np.random.normal(0, noise_std, pc.shape)
            
            # 2. 随机旋转
            angle_range = 0.2 if self.strong_augment else 0.1
            angle = np.random.uniform(-angle_range, angle_range)
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([[cos_a, -sin_a, 0],
                                       [sin_a, cos_a, 0],
                                       [0, 0, 1]])
            pc = pc @ rotation_matrix.T
            kp = kp @ rotation_matrix.T
            
            # 3. 随机缩放
            scale_range = (0.9, 1.1) if self.strong_augment else (0.95, 1.05)
            scale = np.random.uniform(*scale_range)
            pc *= scale
            kp *= scale
            
            # 强增强额外技术
            if self.strong_augment:
                # 4. 点云dropout
                dropout_ratio = np.random.uniform(0.05, 0.15)
                keep_indices = np.random.choice(
                    len(pc), 
                    int(len(pc) * (1 - dropout_ratio)), 
                    replace=False
                )
                pc = pc[keep_indices]
                
                # 重新采样到目标数量
                if len(pc) < self.num_points:
                    indices = np.random.choice(len(pc), self.num_points, replace=True)
                    pc = pc[indices]
                
                # 5. 随机平移
                translation = np.random.normal(0, 0.01, 3)
                pc += translation
                kp += translation
        
        return torch.FloatTensor(pc), torch.FloatTensor(kp)

class SmallDatasetOptimizedModel(nn.Module):
    """针对小数据集优化的模型"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 特征提取网络 - 使用更多正则化
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 全局特征提取
        self.global_conv = nn.Conv1d(1024, 512, 1)
        self.global_bn = nn.BatchNorm1d(512)
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(512, 8, dropout=0.1, batch_first=True)
        
        # 回归网络 - 更多dropout防止过拟合
        self.fc1 = nn.Linear(512, 1024)
        self.fc2 = nn.Linear(1024, 512)
        self.fc3 = nn.Linear(512, 256)
        self.fc4 = nn.Linear(256, 128)
        self.fc5 = nn.Linear(128, num_keypoints * 3)
        
        # Dropout层 - 针对小数据集增加正则化
        self.dropout1 = nn.Dropout(0.5)
        self.dropout2 = nn.Dropout(0.4)
        self.dropout3 = nn.Dropout(0.3)
        self.dropout4 = nn.Dropout(0.2)
        
        # 残差连接
        self.residual_fc = nn.Linear(512, num_keypoints * 3)
        
        # 权重初始化
        self.apply(self._init_weights)
    
    def _init_weights(self, m):
        """权重初始化"""
        if isinstance(m, nn.Linear):
            torch.nn.init.xavier_uniform_(m.weight)
            if m.bias is not None:
                torch.nn.init.zeros_(m.bias)
        elif isinstance(m, nn.Conv1d):
            torch.nn.init.kaiming_uniform_(m.weight)
            if m.bias is not None:
                torch.nn.init.zeros_(m.bias)
    
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        x = F.relu(self.bn4(self.conv4(x)))
        x = F.relu(self.bn5(self.conv5(x)))
        
        # 全局特征
        global_feat = F.relu(self.global_bn(self.global_conv(x)))  # [B, 512, N]
        
        # 注意力机制
        global_feat_t = global_feat.transpose(1, 2)  # [B, N, 512]
        attended_feat, _ = self.attention(global_feat_t, global_feat_t, global_feat_t)
        attended_feat = attended_feat.transpose(1, 2)  # [B, 512, N]
        
        # 最大池化
        pooled_feat = torch.max(attended_feat, 2)[0]  # [B, 512]
        
        # 深度回归网络
        x = F.relu(self.fc1(pooled_feat))
        x = self.dropout1(x)
        x = F.relu(self.fc2(x))
        x = self.dropout2(x)
        x = F.relu(self.fc3(x))
        x = self.dropout3(x)
        x = F.relu(self.fc4(x))
        x = self.dropout4(x)
        main_output = self.fc5(x)
        
        # 残差连接
        residual_output = self.residual_fc(pooled_feat)
        
        # 组合输出
        final_output = main_output + 0.2 * residual_output
        
        return final_output.view(batch_size, self.num_keypoints, 3)

class SmallDatasetDeepTraining:
    """小数据集深度训练"""
    
    def __init__(self, device='cuda:3'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        logger.info(f"🖥️ 使用设备: {self.device}")
        
        # 针对小数据集的训练配置
        self.training_config = {
            'epochs': 200,  # 大幅增加训练轮数
            'batch_size': 4,  # 小批次适合小数据集
            'learning_rate': 0.0003,  # 更小的学习率
            'weight_decay': 1e-3,  # 更强的L2正则化
            'patience': 40,  # 更大的耐心
            'min_lr': 1e-7,
            'warmup_epochs': 10,
            'label_smoothing': 0.1  # 标签平滑
        }
        
        # 测试配置
        self.test_configs = [
            {'points': 10000, 'keypoints': 12, 'kp_indices': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 56]},
            {'points': 10000, 'keypoints': 28, 'kp_indices': list(range(0, 57, 2))},
        ]
        
        # 加载数据集
        self.load_dataset()
        
        self.results = []
    
    def load_dataset(self):
        """加载数据集"""
        logger.info("📥 加载小数据集...")
        
        data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
        self.point_clouds = data['point_clouds']
        self.keypoints_57 = data['keypoints_57']
        
        logger.info(f"✅ 数据集信息:")
        logger.info(f"   总样本数: {len(self.point_clouds)} (小数据集)")
        logger.info(f"   点云形状: {self.point_clouds.shape}")
        logger.info(f"   关键点形状: {self.keypoints_57.shape}")
        
        # 针对小数据集的数据划分策略
        indices = np.arange(len(self.point_clouds))
        
        # 使用更小的测试集比例，保留更多训练数据
        train_indices, test_val_indices = train_test_split(indices, test_size=0.3, random_state=42)
        val_indices, test_indices = train_test_split(test_val_indices, test_size=0.5, random_state=42)
        
        self.train_indices = train_indices
        self.val_indices = val_indices
        self.test_indices = test_indices
        
        logger.info(f"   训练集: {len(train_indices)} 样本 ({len(train_indices)/len(self.point_clouds)*100:.1f}%)")
        logger.info(f"   验证集: {len(val_indices)} 样本 ({len(val_indices)/len(self.point_clouds)*100:.1f}%)")
        logger.info(f"   测试集: {len(test_indices)} 样本 ({len(test_indices)/len(self.point_clouds)*100:.1f}%)")
    
    def create_datasets(self, config):
        """创建数据集"""
        
        num_points = config['points']
        keypoint_indices = config['kp_indices']
        
        # 创建训练集（强数据增强）
        train_dataset = EnhancedMedicalKeypointDataset(
            self.point_clouds[self.train_indices],
            self.keypoints_57[self.train_indices],
            num_points=num_points,
            keypoint_indices=keypoint_indices,
            strong_augment=True  # 小数据集使用强增强
        )
        
        # 创建验证集（轻微增强）
        val_dataset = EnhancedMedicalKeypointDataset(
            self.point_clouds[self.val_indices],
            self.keypoints_57[self.val_indices],
            num_points=num_points,
            keypoint_indices=keypoint_indices,
            augment=True  # 验证集也使用轻微增强
        )
        
        # 创建测试集（无增强）
        test_dataset = EnhancedMedicalKeypointDataset(
            self.point_clouds[self.test_indices],
            self.keypoints_57[self.test_indices],
            num_points=num_points,
            keypoint_indices=keypoint_indices,
            augment=False
        )
        
        return train_dataset, val_dataset, test_dataset
    
    def small_dataset_train_model(self, model, train_loader, val_loader, model_name, config_name):
        """针对小数据集的训练方法"""
        
        logger.info(f"🚀 开始小数据集深度训练 {model_name} - {config_name}")
        logger.info(f"   训练样本: {len(train_loader.dataset)} (增强后)")
        logger.info(f"   验证样本: {len(val_loader.dataset)} (增强后)")
        
        # 优化器 - 使用AdamW和更强的权重衰减
        optimizer = optim.AdamW(
            model.parameters(), 
            lr=self.training_config['learning_rate'],
            weight_decay=self.training_config['weight_decay']
        )
        
        # 学习率调度器 - 使用余弦退火
        scheduler = CosineAnnealingLR(
            optimizer, 
            T_max=self.training_config['epochs'],
            eta_min=self.training_config['min_lr']
        )
        
        # 损失函数 - 使用标签平滑
        criterion = nn.MSELoss()
        
        # 训练历史
        train_losses = []
        val_losses = []
        learning_rates = []
        
        best_val_loss = float('inf')
        patience_counter = 0
        best_epoch = 0
        
        logger.info(f"🎯 训练配置:")
        logger.info(f"   Epochs: {self.training_config['epochs']}")
        logger.info(f"   Batch Size: {self.training_config['batch_size']}")
        logger.info(f"   Learning Rate: {self.training_config['learning_rate']}")
        logger.info(f"   Weight Decay: {self.training_config['weight_decay']}")
        logger.info(f"   Patience: {self.training_config['patience']}")
        
        # 训练循环
        for epoch in range(self.training_config['epochs']):
            start_time = time.time()
            
            # 训练阶段
            model.train()
            train_loss = 0.0
            train_batches = 0
            
            train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{self.training_config["epochs"]} [Train]')
            
            for batch_pc, batch_kp in train_pbar:
                batch_pc = batch_pc.to(self.device)
                batch_kp = batch_kp.to(self.device)
                
                optimizer.zero_grad()
                pred_kp = model(batch_pc)
                loss = criterion(pred_kp, batch_kp)
                loss.backward()
                
                # 梯度裁剪 - 对小数据集很重要
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
                
                optimizer.step()
                
                train_loss += loss.item()
                train_batches += 1
                
                train_pbar.set_postfix({'Loss': f'{loss.item():.6f}'})
            
            avg_train_loss = train_loss / train_batches
            
            # 验证阶段
            model.eval()
            val_loss = 0.0
            val_batches = 0
            
            with torch.no_grad():
                val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{self.training_config["epochs"]} [Val]')
                
                for batch_pc, batch_kp in val_pbar:
                    batch_pc = batch_pc.to(self.device)
                    batch_kp = batch_kp.to(self.device)
                    
                    pred_kp = model(batch_pc)
                    loss = criterion(pred_kp, batch_kp)
                    val_loss += loss.item()
                    val_batches += 1
                    
                    val_pbar.set_postfix({'Loss': f'{loss.item():.6f}'})
            
            avg_val_loss = val_loss / val_batches
            
            # 更新学习率
            scheduler.step()
            current_lr = optimizer.param_groups[0]['lr']
            
            # 记录历史
            train_losses.append(avg_train_loss)
            val_losses.append(avg_val_loss)
            learning_rates.append(current_lr)
            
            epoch_time = time.time() - start_time
            
            # 早停检查
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                patience_counter = 0
                best_epoch = epoch
                best_model_state = model.state_dict().copy()
                
                logger.info(f"✅ Epoch {epoch+1}: 新的最佳验证损失 {avg_val_loss:.6f}")
            else:
                patience_counter += 1
            
            # 每20轮输出一次详细信息
            if (epoch + 1) % 20 == 0:
                logger.info(f"Epoch {epoch+1}/{self.training_config['epochs']}: "
                           f"Train Loss: {avg_train_loss:.6f}, "
                           f"Val Loss: {avg_val_loss:.6f}, "
                           f"LR: {current_lr:.2e}, "
                           f"Time: {epoch_time:.1f}s, "
                           f"Patience: {patience_counter}/{self.training_config['patience']}")
            
            # 早停
            if patience_counter >= self.training_config['patience']:
                logger.info(f"🛑 早停于epoch {epoch+1}, 最佳epoch: {best_epoch+1}")
                break
        
        # 加载最佳模型
        model.load_state_dict(best_model_state)
        
        # 保存训练历史
        training_history = {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'learning_rates': learning_rates,
            'best_epoch': best_epoch,
            'best_val_loss': best_val_loss,
            'total_epochs': epoch + 1
        }
        
        logger.info(f"🎯 {model_name} 训练完成:")
        logger.info(f"   最佳验证损失: {best_val_loss:.6f} (epoch {best_epoch+1})")
        logger.info(f"   总训练轮数: {epoch + 1}")
        logger.info(f"   训练时间: {sum([epoch_time]) * (epoch + 1) / 60:.1f} 分钟")
        
        return model, training_history
    
    def evaluate_model(self, model, test_loader):
        """评估模型"""
        
        model.eval()
        all_errors = []
        
        with torch.no_grad():
            for batch_pc, batch_kp in tqdm(test_loader, desc='评估中'):
                batch_pc = batch_pc.to(self.device)
                batch_kp = batch_kp.to(self.device)
                
                pred_kp = model(batch_pc)
                
                # 计算误差
                errors = torch.norm(pred_kp - batch_kp, dim=2)  # [B, num_keypoints]
                all_errors.extend(errors.cpu().numpy().flatten())
        
        all_errors = np.array(all_errors)
        
        return {
            'avg_error': np.mean(all_errors),
            'std_error': np.std(all_errors),
            'median_error': np.median(all_errors),
            'max_error': np.max(all_errors),
            'min_error': np.min(all_errors),
            'medical_rate': np.sum(all_errors <= 10) / len(all_errors) * 100,
            'excellent_rate': np.sum(all_errors <= 5) / len(all_errors) * 100,
            'precision_1mm': np.sum(all_errors <= 1) / len(all_errors) * 100
        }
    
    def run_single_experiment(self, config):
        """运行单个小数据集实验"""
        
        num_points = config['points']
        num_keypoints = config['keypoints']
        config_name = f"{num_keypoints}kp_{int(num_points/1000)}K"
        
        logger.info(f"\n🔄 小数据集深度训练实验: {config_name}")
        
        try:
            # 创建数据集
            train_dataset, val_dataset, test_dataset = self.create_datasets(config)
            
            # 创建数据加载器
            train_loader = DataLoader(
                train_dataset, 
                batch_size=self.training_config['batch_size'], 
                shuffle=True,
                num_workers=2,
                pin_memory=True
            )
            val_loader = DataLoader(
                val_dataset, 
                batch_size=self.training_config['batch_size'], 
                shuffle=False,
                num_workers=2,
                pin_memory=True
            )
            test_loader = DataLoader(
                test_dataset, 
                batch_size=self.training_config['batch_size'], 
                shuffle=False,
                num_workers=2,
                pin_memory=True
            )
            
            # 创建模型
            model = SmallDatasetOptimizedModel(num_points=num_points, num_keypoints=num_keypoints)
            model = model.to(self.device)
            
            param_count = sum(p.numel() for p in model.parameters())
            logger.info(f"📊 模型参数数量: {param_count/1e6:.2f}M")
            
            # 小数据集深度训练
            start_time = time.time()
            model, training_history = self.small_dataset_train_model(
                model, train_loader, val_loader, "SmallDatasetOptimized", config_name
            )
            training_time = time.time() - start_time
            
            # 评估模型
            logger.info("📊 开始模型评估...")
            results = self.evaluate_model(model, test_loader)
            
            # 添加训练信息
            results.update({
                'model': 'SmallDatasetOptimized',
                'config': config_name,
                'keypoints': num_keypoints,
                'points': num_points,
                'training_time': training_time,
                'num_params': param_count,
                'training_history': training_history,
                'dataset_size': len(self.point_clouds),
                'train_samples': len(self.train_indices),
                'val_samples': len(self.val_indices),
                'test_samples': len(self.test_indices)
            })
            
            logger.info(f"✅ 实验完成:")
            logger.info(f"   平均误差: {results['avg_error']:.2f}mm")
            logger.info(f"   医疗级达标率: {results['medical_rate']:.1f}%")
            logger.info(f"   训练时间: {training_time/60:.1f}分钟")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 实验失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def run_small_dataset_benchmark(self):
        """运行小数据集基准测试"""
        
        logger.info("\n🚀 开始小数据集深度训练基准测试...")
        logger.info("=" * 80)
        logger.info(f"数据集大小: {len(self.point_clouds)} 样本")
        logger.info(f"训练轮数: {self.training_config['epochs']} epochs")
        logger.info("=" * 80)
        
        for config in self.test_configs:
            result = self.run_single_experiment(config)
            
            if result:
                self.results.append(result)
        
        # 保存结果
        self.save_results()
        
        logger.info(f"\n✅ 小数据集深度训练基准测试完成！")
        return self.results
    
    def save_results(self):
        """保存结果"""
        
        # 保存为CSV（不包含训练历史）
        csv_results = []
        for result in self.results:
            csv_result = {k: v for k, v in result.items() if k != 'training_history'}
            csv_results.append(csv_result)
        
        df = pd.DataFrame(csv_results)
        df.to_csv('small_dataset_deep_training_results.csv', index=False)
        
        # 保存为JSON（包含完整信息）
        with open('small_dataset_deep_training_results.json', 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        logger.info("💾 结果已保存:")
        logger.info("   📄 small_dataset_deep_training_results.json")
        logger.info("   📊 small_dataset_deep_training_results.csv")

if __name__ == "__main__":
    print("🧪 小数据集深度训练")
    print("针对96样本的优化训练策略")
    print("=" * 80)
    
    # 创建小数据集深度训练
    benchmark = SmallDatasetDeepTraining()
    
    # 运行基准测试
    results = benchmark.run_small_dataset_benchmark()
    
    if results:
        print(f"\n📋 小数据集深度训练总结:")
        print(f"   🔬 数据集大小: 96 样本")
        print(f"   📊 训练轮数: 200 epochs")
        print(f"   📈 实验数: {len(results)} 个")
        
        best_result = min(results, key=lambda x: x['avg_error'])
        print(f"\n🏆 最佳结果:")
        print(f"   📊 配置: {best_result['config']}")
        print(f"   📊 平均误差: {best_result['avg_error']:.2f}mm")
        print(f"   📊 医疗级达标率: {best_result['medical_rate']:.1f}%")
        print(f"   📊 训练时间: {best_result['training_time']/60:.1f}分钟")
        print(f"   📊 最佳epoch: {best_result['training_history']['best_epoch']+1}")
    
    print(f"\n💡 小数据集优化技术:")
    print(f"   • 200轮充分训练 (vs 之前的80轮)")
    print(f"   • 强数据增强 (3倍有效样本)")
    print(f"   • 更强正则化 (dropout 0.5, weight_decay 1e-3)")
    print(f"   • 小批次训练 (batch_size 4)")
    print(f"   • 注意力机制和残差连接")
    print(f"   • 针对96样本优化的训练策略")
