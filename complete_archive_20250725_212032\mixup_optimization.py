#!/usr/bin/env python3
"""
Mixup模型精准优化
Mixup Model Precision Optimization
基于7.041mm验证误差的进一步优化，冲击6mm和5mm
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pathlib import Path
from datetime import datetime
import json

class EnhancedMixupPointNet(nn.Module):
    """增强Mixup PointNet"""
    
    def __init__(self, num_keypoints=19, dropout_rate=0.15):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 优化的特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(), nn.Dropout(dropout_rate * 0.5),
            nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(), nn.Dropout(dropout_rate * 0.7),
            nn.Conv1d(128, 256, 1), nn.<PERSON>chNorm1d(256), nn.ReL<PERSON>(), nn.Dropout(dropout_rate * 0.8),
            nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(), nn.Dropout(dropout_rate),
            nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 增强的关键点回归器
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(1024, 768),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(768, 512),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.7),
            nn.Linear(512, 384),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.5),
            nn.Linear(384, 256),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.3),
            nn.Linear(256, num_keypoints * 3)
        )
        
    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        # 特征提取
        features = self.feature_extractor(x)  # (B, 1024, N)
        global_feat = torch.max(features, dim=2)[0]  # (B, 1024)
        
        # 关键点预测
        keypoints = self.keypoint_regressor(global_feat)
        return keypoints.view(B, self.num_keypoints, 3)

class ConsistentMixupPointNet(nn.Module):
    """一致性Mixup PointNet - 结合最佳技术"""
    
    def __init__(self, num_keypoints=19):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 主网络
        self.main_net = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(), nn.Dropout(0.1),
            nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(), nn.Dropout(0.12),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(), nn.Dropout(0.15),
            nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(), nn.Dropout(0.18),
            nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 辅助网络 (用于一致性)
        self.aux_net = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(), nn.Dropout(0.08),
            nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(), nn.Dropout(0.1),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(), nn.Dropout(0.12),
            nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(), nn.Dropout(0.15),
            nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 共享回归器
        self.regressor = nn.Sequential(
            nn.Linear(1024, 640),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(640, 384),
            nn.ReLU(),
            nn.Dropout(0.15),
            nn.Linear(384, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, num_keypoints * 3)
        )
        
    def forward(self, point_cloud, return_aux=False):
        B, N, _ = point_cloud.shape
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        # 主网络
        main_features = self.main_net(x)  # (B, 1024, N)
        main_global = torch.max(main_features, dim=2)[0]  # (B, 1024)
        main_pred = self.regressor(main_global)
        main_pred = main_pred.view(B, self.num_keypoints, 3)
        
        if return_aux:
            # 辅助网络
            aux_features = self.aux_net(x)  # (B, 1024, N)
            aux_global = torch.max(aux_features, dim=2)[0]  # (B, 1024)
            aux_pred = self.regressor(aux_global)
            aux_pred = aux_pred.view(B, self.num_keypoints, 3)
            return main_pred, aux_pred
        
        return main_pred

class AdaptiveMixupPointNet(nn.Module):
    """自适应Mixup PointNet"""
    
    def __init__(self, num_keypoints=19):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(3, 80, 1), nn.BatchNorm1d(80), nn.ReLU(), nn.Dropout(0.08),
            nn.Conv1d(80, 160, 1), nn.BatchNorm1d(160), nn.ReLU(), nn.Dropout(0.1),
            nn.Conv1d(160, 320, 1), nn.BatchNorm1d(320), nn.ReLU(), nn.Dropout(0.12),
            nn.Conv1d(320, 640, 1), nn.BatchNorm1d(640), nn.ReLU(), nn.Dropout(0.15),
            nn.Conv1d(640, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 自适应权重网络
        self.adaptive_weights = nn.Sequential(
            nn.Linear(1024, 256),
            nn.ReLU(),
            nn.Linear(256, 64),
            nn.ReLU(),
            nn.Linear(64, 1),
            nn.Sigmoid()
        )
        
        # 关键点回归器
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, num_keypoints * 3)
        )
        
    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        # 特征提取
        features = self.feature_extractor(x)  # (B, 1024, N)
        global_feat = torch.max(features, dim=2)[0]  # (B, 1024)
        
        # 自适应权重
        confidence = self.adaptive_weights(global_feat)  # (B, 1)
        
        # 关键点预测
        keypoints = self.keypoint_regressor(global_feat)
        keypoints = keypoints.view(B, self.num_keypoints, 3)
        
        return keypoints, confidence

class MixupOptimizer:
    """Mixup优化器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def load_aligned_data(self):
        """加载对齐数据"""
        print("📦 加载F3对齐数据...")
        
        aligned_files = list(Path("data/processed").glob("f3_aligned_dataset_*.npz"))
        if not aligned_files:
            raise FileNotFoundError("未找到F3对齐数据集")
        
        latest_file = max(aligned_files, key=lambda x: x.stat().st_mtime)
        data = np.load(str(latest_file), allow_pickle=True)
        
        point_clouds = np.array(data['point_clouds'], dtype=np.float32)
        keypoints = np.array(data['keypoints'], dtype=np.float32)
        
        # 数据划分
        from sklearn.model_selection import train_test_split
        indices = np.arange(len(point_clouds))
        train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
        train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
        
        print(f"✅ 数据加载完成: {point_clouds.shape}")
        print(f"   训练: {len(train_indices)}, 验证: {len(val_indices)}, 测试: {len(test_indices)}")
        
        return self.data
    
    def advanced_mixup(self, point_clouds, keypoints, alpha=0.15, beta=0.25):
        """高级Mixup增强"""
        if len(point_clouds) < 2:
            return point_clouds, keypoints
        
        mixed_pcs = []
        mixed_kps = []
        
        for i in range(len(point_clouds)):
            # 原始数据
            mixed_pcs.append(point_clouds[i])
            mixed_kps.append(keypoints[i])
            
            # Mixup增强
            for _ in range(2):  # 每个样本生成2个mixup版本
                # 选择另一个样本
                j = np.random.choice([idx for idx in range(len(point_clouds)) if idx != i])
                
                # 自适应混合系数
                if np.random.random() < 0.7:
                    # 大部分时间使用保守的mixup
                    lam = np.random.beta(alpha, alpha)
                else:
                    # 偶尔使用更激进的mixup
                    lam = np.random.beta(beta, beta)
                
                # 确保lambda在合理范围内
                lam = np.clip(lam, 0.1, 0.9)
                
                # 混合点云和关键点
                mixed_pc = lam * point_clouds[i] + (1 - lam) * point_clouds[j]
                mixed_kp = lam * keypoints[i] + (1 - lam) * keypoints[j]
                
                mixed_pcs.append(mixed_pc)
                mixed_kps.append(mixed_kp)
        
        return mixed_pcs, mixed_kps
    
    def train_enhanced_mixup(self, epochs=150, lr=0.0003):
        """训练增强Mixup模型"""
        print(f"\n🎯 训练增强Mixup模型")
        print(f"   目标: 从7.041mm冲击6mm验证误差")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        model = EnhancedMixupPointNet(num_keypoints=19, dropout_rate=0.15).to(self.device)
        
        # 优化器配置
        optimizer = torch.optim.AdamW(
            model.parameters(), 
            lr=lr, 
            weight_decay=3e-5,
            betas=(0.9, 0.999)
        )
        
        # 学习率调度器
        scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer, T_0=25, T_mult=2, eta_min=1e-7
        )
        
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        patience = 0
        max_patience = 60
        
        for epoch in range(epochs):
            model.train()
            epoch_losses = []
            
            # 渐进式训练数据量
            if epoch < 50:
                k_shot = 16
            elif epoch < 100:
                k_shot = 18
            else:
                k_shot = 20
            
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                min(k_shot, len(self.data['train']['point_clouds'])), 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 高级Mixup增强
            mixed_pcs, mixed_kps = self.advanced_mixup(train_pcs, train_kps, alpha=0.15, beta=0.25)
            
            # 批次训练
            batch_size = 6
            for i in range(0, len(mixed_pcs), batch_size):
                batch_pcs = torch.FloatTensor(mixed_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(mixed_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                pred_kps = model(batch_pcs)
                loss = criterion(pred_kps, batch_kps)
                loss.backward()
                
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.8)
                optimizer.step()
                
                epoch_losses.append(loss.item())
                
                del batch_pcs, batch_kps, pred_kps, loss
                torch.cuda.empty_cache()
            
            scheduler.step()
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            
            # 验证
            if epoch % 5 == 0:
                val_error = self.evaluate_model(model, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                    patience = 0
                    
                    # 里程碑检查点
                    if val_error <= 6.5:
                        self.save_checkpoint(model, val_error, epoch, "approaching_6mm")
                        print(f"🎯 接近6mm！验证误差: {val_error:.3f}mm")
                    
                    if val_error <= 6.0:
                        self.save_checkpoint(model, val_error, epoch, "6mm_achieved")
                        print(f"🔥 6mm达成！验证误差: {val_error:.3f}mm")
                        
                    if val_error <= 5.5:
                        self.save_checkpoint(model, val_error, epoch, "approaching_medical")
                        print(f"🚀 接近医疗级！验证误差: {val_error:.3f}mm")
                        
                    if val_error <= 5.0:
                        self.save_checkpoint(model, val_error, epoch, "medical_grade")
                        print(f"🎉 医疗级达成！验证误差: {val_error:.3f}mm")
                else:
                    patience += 1
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm, "
                      f"LR={optimizer.param_groups[0]['lr']:.7f}, K={k_shot}, P={patience}")
                
                if patience >= max_patience:
                    print(f"早停在epoch {epoch}")
                    break
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, K={k_shot}")
        
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def train_consistent_mixup(self, epochs=120, lr=0.0003):
        """训练一致性Mixup模型"""
        print(f"\n🔄 训练一致性Mixup模型")
        print(f"   目标: 结合Mixup和一致性正则化")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        model = ConsistentMixupPointNet(num_keypoints=19).to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=2e-5)
        
        mse_criterion = nn.MSELoss()
        consistency_criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        
        for epoch in range(epochs):
            model.train()
            epoch_losses = []
            
            k_shot = min(18, len(self.data['train']['point_clouds']))
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                k_shot, 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # Mixup增强
            mixed_pcs, mixed_kps = self.advanced_mixup(train_pcs, train_kps, alpha=0.12, beta=0.2)
            
            # 批次训练
            batch_size = 6
            for i in range(0, len(mixed_pcs), batch_size):
                batch_pcs = torch.FloatTensor(mixed_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(mixed_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                
                # 主预测和辅助预测
                main_pred, aux_pred = model(batch_pcs, return_aux=True)
                
                # 主损失
                main_loss = mse_criterion(main_pred, batch_kps)
                
                # 一致性损失
                consistency_loss = consistency_criterion(main_pred, aux_pred)
                
                # 总损失
                total_loss = main_loss + 0.08 * consistency_loss
                
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_losses.append(total_loss.item())
                
                del batch_pcs, batch_kps, main_pred, aux_pred
                torch.cuda.empty_cache()
            
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            
            # 验证
            if epoch % 8 == 0:
                val_error = self.evaluate_model(model, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm")
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def train_adaptive_mixup(self, epochs=100, lr=0.0004):
        """训练自适应Mixup模型"""
        print(f"\n🎭 训练自适应Mixup模型")
        print(f"   目标: 自适应置信度加权")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        model = AdaptiveMixupPointNet(num_keypoints=19).to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-5)
        
        mse_criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        best_model_state = None
        
        for epoch in range(epochs):
            model.train()
            epoch_losses = []
            
            k_shot = min(17, len(self.data['train']['point_clouds']))
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                k_shot, 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # Mixup增强
            mixed_pcs, mixed_kps = self.advanced_mixup(train_pcs, train_kps, alpha=0.1, beta=0.2)
            
            # 批次训练
            batch_size = 5
            for i in range(0, len(mixed_pcs), batch_size):
                batch_pcs = torch.FloatTensor(mixed_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(mixed_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                
                # 预测和置信度
                pred_kps, confidence = model(batch_pcs)
                
                # 置信度加权损失
                sample_losses = []
                for j in range(len(batch_pcs)):
                    sample_loss = mse_criterion(pred_kps[j:j+1], batch_kps[j:j+1])
                    weighted_loss = sample_loss * confidence[j]
                    sample_losses.append(weighted_loss)
                
                total_loss = torch.mean(torch.stack(sample_losses))
                
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_losses.append(total_loss.item())
                
                del batch_pcs, batch_kps, pred_kps, confidence
                torch.cuda.empty_cache()
            
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_model_adaptive(model, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm")
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}")
        
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def evaluate_model(self, model, split='test'):
        """评估模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for i in range(0, len(pcs), 2):
                batch_pcs = torch.FloatTensor(pcs[i:i+2]).to(self.device)
                batch_kps = torch.FloatTensor(kps[i:i+2]).to(self.device)
                
                if hasattr(model, 'forward') and 'return_aux' in model.forward.__code__.co_varnames:
                    pred_kps = model(batch_pcs, return_aux=False)
                else:
                    pred_kps = model(batch_pcs)
                
                for j in range(len(batch_pcs)):
                    error = torch.mean(torch.norm(pred_kps[j] - batch_kps[j], dim=1))
                    total_error += error.item()
                    num_samples += 1
                
                del batch_pcs, batch_kps, pred_kps
                torch.cuda.empty_cache()
        
        return total_error / num_samples if num_samples > 0 else float('inf')
    
    def evaluate_model_adaptive(self, model, split='test'):
        """评估自适应模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for i in range(0, len(pcs), 2):
                batch_pcs = torch.FloatTensor(pcs[i:i+2]).to(self.device)
                batch_kps = torch.FloatTensor(kps[i:i+2]).to(self.device)
                
                pred_kps, _ = model(batch_pcs)
                
                for j in range(len(batch_pcs)):
                    error = torch.mean(torch.norm(pred_kps[j] - batch_kps[j], dim=1))
                    total_error += error.item()
                    num_samples += 1
                
                del batch_pcs, batch_kps, pred_kps
                torch.cuda.empty_cache()
        
        return total_error / num_samples if num_samples > 0 else float('inf')
    
    def save_checkpoint(self, model, val_error, epoch, tag=""):
        """保存检查点"""
        output_dir = Path("trained_models/mixup_optimization")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"mixup_opt_{val_error:.3f}mm_epoch{epoch}_{tag}_{timestamp}.pth"
        model_path = output_dir / filename
        
        torch.save({
            'model_state_dict': model.state_dict(),
            'validation_error': val_error,
            'epoch': epoch,
            'timestamp': timestamp,
            'baseline': '7.041mm_mixup_model',
            'target': '6mm_then_5mm'
        }, model_path)
        
        print(f"💾 检查点已保存: {model_path}")
        return model_path

def run_mixup_optimization():
    """运行Mixup优化实验"""
    print("🚀 Mixup模型精准优化实验")
    print("=" * 60)
    print("基线: Mixup模型 7.041mm验证误差")
    print("目标: 6mm验证误差 → 5mm医疗级精度")
    print()
    print("优化策略:")
    print("1. 增强Mixup模型 (更深回归器 + 自适应dropout)")
    print("2. 一致性Mixup模型 (Mixup + 一致性正则化)")
    print("3. 自适应Mixup模型 (置信度加权)")
    
    optimizer = MixupOptimizer()
    data = optimizer.load_aligned_data()
    
    results = {}
    
    # 1. 增强Mixup模型
    print(f"\n{'='*60}")
    try:
        enhanced_model, enhanced_val_error = optimizer.train_enhanced_mixup(epochs=150, lr=0.0003)
        enhanced_test_error = optimizer.evaluate_model(enhanced_model, 'test')
        results['enhanced_mixup'] = {
            'val_error': enhanced_val_error,
            'test_error': enhanced_test_error
        }
        print(f"✅ 增强Mixup完成: 验证={enhanced_val_error:.3f}mm, 测试={enhanced_test_error:.3f}mm")
    except Exception as e:
        print(f"❌ 增强Mixup失败: {e}")
        results['enhanced_mixup'] = {'val_error': float('inf'), 'test_error': float('inf')}
    
    # 2. 一致性Mixup模型
    print(f"\n{'='*60}")
    try:
        consistent_model, consistent_val_error = optimizer.train_consistent_mixup(epochs=120, lr=0.0003)
        consistent_test_error = optimizer.evaluate_model(consistent_model, 'test')
        results['consistent_mixup'] = {
            'val_error': consistent_val_error,
            'test_error': consistent_test_error
        }
        print(f"✅ 一致性Mixup完成: 验证={consistent_val_error:.3f}mm, 测试={consistent_test_error:.3f}mm")
    except Exception as e:
        print(f"❌ 一致性Mixup失败: {e}")
        results['consistent_mixup'] = {'val_error': float('inf'), 'test_error': float('inf')}
    
    # 3. 自适应Mixup模型
    print(f"\n{'='*60}")
    try:
        adaptive_model, adaptive_val_error = optimizer.train_adaptive_mixup(epochs=100, lr=0.0004)
        adaptive_test_error = optimizer.evaluate_model_adaptive(adaptive_model, 'test')
        results['adaptive_mixup'] = {
            'val_error': adaptive_val_error,
            'test_error': adaptive_test_error
        }
        print(f"✅ 自适应Mixup完成: 验证={adaptive_val_error:.3f}mm, 测试={adaptive_test_error:.3f}mm")
    except Exception as e:
        print(f"❌ 自适应Mixup失败: {e}")
        results['adaptive_mixup'] = {'val_error': float('inf'), 'test_error': float('inf')}
    
    # 结果汇总
    print(f"\n🏆 Mixup优化结果对比:")
    print("=" * 60)
    
    baseline_val = 7.041
    best_method = None
    best_val_error = float('inf')
    best_test_error = float('inf')
    
    print(f"基线Mixup模型:            验证={baseline_val:.3f}mm")
    
    for method, result in results.items():
        val_error = result['val_error']
        test_error = result['test_error']
        
        print(f"{method:20s}: 验证={val_error:.3f}mm, 测试={test_error:.3f}mm")
        
        if val_error < best_val_error:
            best_val_error = val_error
            best_test_error = test_error
            best_method = method
    
    print(f"\n🏆 最佳优化方法: {best_method}")
    print(f"   最佳验证误差: {best_val_error:.3f}mm")
    print(f"   对应测试误差: {best_test_error:.3f}mm")
    
    # 改进分析
    if best_val_error < baseline_val:
        val_improvement = (baseline_val - best_val_error) / baseline_val * 100
        print(f"🎉 验证误差改进: {val_improvement:.1f}%")
    else:
        val_degradation = (best_val_error - baseline_val) / baseline_val * 100
        print(f"⚠️ 验证误差下降: {val_degradation:.1f}%")
    
    # 目标达成评估
    print(f"\n🎯 目标达成评估:")
    print(f"6mm目标:                 {'✅ 达成' if best_val_error <= 6.0 else f'❌ 还需{best_val_error - 6.0:.3f}mm'}")
    print(f"5.5mm目标:               {'✅ 达成' if best_val_error <= 5.5 else f'❌ 还需{best_val_error - 5.5:.3f}mm'}")
    print(f"5mm医疗级:               {'🎉 达成' if best_val_error <= 5.0 else f'❌ 还需{best_val_error - 5.0:.3f}mm'}")
    
    # 状态评估
    if best_val_error <= 5.0:
        status = "🎉 医疗级精度达成"
    elif best_val_error <= 5.5:
        status = "🔥 极其接近医疗级"
    elif best_val_error <= 6.0:
        status = "🎯 6mm目标达成"
    elif best_val_error <= 6.5:
        status = "📈 接近6mm目标"
    elif best_val_error < baseline_val:
        status = "✅ 有效改进"
    else:
        status = "⚠️ 需要调整策略"
    
    print(f"\n状态: {status}")
    
    # 保存结果
    experiment_results = {
        "experiment_timestamp": datetime.now().isoformat(),
        "experiment_type": "mixup_optimization",
        "baseline_val_error": baseline_val,
        "target_6mm": 6.0,
        "target_medical": 5.0,
        "results": results,
        "best_method": best_method,
        "best_val_error": float(best_val_error),
        "best_test_error": float(best_test_error),
        "status": status,
        "targets_achieved": {
            "6mm": best_val_error <= 6.0,
            "5_5mm": best_val_error <= 5.5,
            "medical_5mm": best_val_error <= 5.0
        }
    }
    
    results_dir = Path("results/mixup_optimization")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = results_dir / f"mixup_optimization_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(experiment_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 实验结果已保存: {results_file}")
    
    return optimizer, results

if __name__ == "__main__":
    optimizer, results = run_mixup_optimization()
