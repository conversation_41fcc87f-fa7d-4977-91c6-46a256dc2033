#!/usr/bin/env python3
"""
Create F3 Dataset - Robust Version

Create F3 dataset with robust error handling and memory management.
"""

import numpy as np
import pandas as pd
from pathlib import Path
import json
import struct
import gc
import os

def load_annotation_file(csv_path: str):
    """Load annotation CSV file with proper encoding"""
    try:
        df = pd.read_csv(csv_path, encoding='gbk')
    except:
        try:
            df = pd.read_csv(csv_path, encoding='utf-8')
        except:
            df = pd.read_csv(csv_path, encoding='latin-1')
    
    keypoints = df[['X', 'Y', 'Z']].values
    labels = df['label'].values.tolist()
    
    return keypoints, labels

def read_stl_vertices_conservative(stl_path: str, max_vertices: int = 5000):
    """Conservative STL reading with memory limits"""
    try:
        with open(stl_path, 'rb') as f:
            # Skip header
            f.read(80)
            
            # Read triangle count
            num_triangles = struct.unpack('<I', f.read(4))[0]
            
            # Limit triangles to avoid memory issues
            triangles_to_read = min(num_triangles, max_vertices // 3)
            
            vertices = []
            
            for i in range(triangles_to_read):
                # Skip normal
                f.read(12)
                
                # Read 3 vertices
                for j in range(3):
                    x, y, z = struct.unpack('<fff', f.read(12))
                    vertices.append([x, y, z])
                
                # Skip attribute
                f.read(2)
                
                # Memory check every 1000 triangles
                if i % 1000 == 0:
                    gc.collect()
            
            vertices = np.array(vertices)
            
            # Remove duplicates conservatively
            if len(vertices) > 2000:
                # Sample to reduce size
                indices = np.random.choice(len(vertices), 2000, replace=False)
                vertices = vertices[indices]
            
            return vertices
            
    except Exception as e:
        print(f"      ❌ STL读取失败: {e}")
        return None

def process_single_f3_sample(sample_id: str):
    """Process single F3 sample with robust error handling"""
    
    print(f"   🔧 处理样本: {sample_id}")
    
    try:
        data_dir = Path("/home/<USER>/pjc/GCN/Data")
        annotations_dir = data_dir / "annotations"
        stl_dir = data_dir / "stl_models"
        
        # Load annotation
        csv_file = annotations_dir / f"{sample_id}-Table-XYZ.CSV"
        
        if not csv_file.exists():
            print(f"      ❌ 标注文件不存在")
            return None
        
        keypoints, labels = load_annotation_file(str(csv_file))
        
        # Extract F3 keypoints
        f3_indices = [i for i, label in enumerate(labels) if label.startswith('F_3')]
        
        if not f3_indices:
            print(f"      ❌ 没有F3关键点")
            return None
        
        f3_keypoints = keypoints[f3_indices]
        f3_labels = [labels[i] for i in f3_indices]
        
        # Load F3 STL
        f3_stl_file = stl_dir / f"{sample_id}-F_3.stl"
        
        if not f3_stl_file.exists():
            print(f"      ❌ F3 STL文件不存在")
            return None
        
        f3_vertices = read_stl_vertices_conservative(str(f3_stl_file))
        
        if f3_vertices is None or len(f3_vertices) == 0:
            print(f"      ❌ F3 STL读取失败")
            return None
        
        # Quality check
        distances = []
        for kp in f3_keypoints:
            dists = np.linalg.norm(f3_vertices - kp, axis=1)
            min_dist = np.min(dists)
            distances.append(min_dist)
        
        mean_distance = np.mean(distances)
        within_1mm = np.sum(np.array(distances) <= 1.0) / len(distances) * 100
        within_5mm = np.sum(np.array(distances) <= 5.0) / len(distances) * 100
        
        print(f"      ✅ 关键点: {len(f3_keypoints)}, 顶点: {len(f3_vertices)}")
        print(f"      📊 距离: {mean_distance:.2f}mm, ≤1mm: {within_1mm:.1f}%, ≤5mm: {within_5mm:.1f}%")
        
        # Create result
        result = {
            'sample_id': sample_id,
            'f3_keypoints': f3_keypoints.tolist(),
            'f3_labels': f3_labels,
            'f3_indices': f3_indices,
            'point_cloud': f3_vertices.tolist(),
            'quality_metrics': {
                'mean_surface_distance': float(mean_distance),
                'within_1mm_percent': float(within_1mm),
                'within_5mm_percent': float(within_5mm),
                'keypoints_count': len(f3_keypoints),
                'vertices_count': len(f3_vertices)
            }
        }
        
        # Force cleanup
        del keypoints, labels, f3_keypoints, f3_vertices, distances
        gc.collect()
        
        return result
        
    except Exception as e:
        print(f"      ❌ 处理失败: {e}")
        return None

def create_f3_dataset_batch():
    """Create F3 dataset in small batches"""
    
    print("🏗️ **创建F3单部件数据集 (批处理版)**")
    print("🎯 **使用保守的内存管理避免段错误**")
    print("=" * 80)
    
    # Get valid samples
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    annotations_dir = data_dir / "annotations"
    
    xyz_files = list(annotations_dir.glob("*-Table-XYZ.CSV"))
    excluded_samples = {'600025', '600026', '600027'}
    
    valid_samples = []
    for csv_file in xyz_files:
        sample_id = csv_file.stem.split('-')[0]
        if sample_id not in excluded_samples:
            valid_samples.append(sample_id)
    
    print(f"📊 数据集统计:")
    print(f"   有效样本: {len(valid_samples)}")
    print(f"   批处理大小: 5个样本")
    
    # Process in small batches
    batch_size = 5
    all_results = []
    
    for batch_start in range(0, len(valid_samples), batch_size):
        batch_end = min(batch_start + batch_size, len(valid_samples))
        batch_samples = valid_samples[batch_start:batch_end]
        
        print(f"\n📦 **批次 {batch_start//batch_size + 1}: 样本 {batch_start+1}-{batch_end}**")
        
        batch_results = []
        
        for sample_id in batch_samples:
            result = process_single_f3_sample(sample_id)
            if result:
                batch_results.append(result)
        
        print(f"   批次结果: {len(batch_results)}/{len(batch_samples)} 成功")
        
        # Save batch results immediately
        if batch_results:
            batch_file = f"f3_batch_{batch_start//batch_size + 1}.json"
            with open(batch_file, 'w') as f:
                json.dump(batch_results, f, indent=2)
            print(f"   💾 批次数据已保存: {batch_file}")
            
            all_results.extend(batch_results)
        
        # Force cleanup between batches
        gc.collect()
        
        # Continue processing all batches
        print(f"   累计处理: {len(all_results)} 样本")
    
    print(f"\n📋 **F3数据集创建完成**:")
    print(f"   总成功样本: {len(all_results)}")
    
    if all_results:
        # Calculate statistics
        distances = [r['quality_metrics']['mean_surface_distance'] for r in all_results]
        within_1mm = [r['quality_metrics']['within_1mm_percent'] for r in all_results]
        within_5mm = [r['quality_metrics']['within_5mm_percent'] for r in all_results]
        
        print(f"\n📊 **质量统计**:")
        print(f"   平均表面距离: {np.mean(distances):.2f}±{np.std(distances):.2f}mm")
        print(f"   平均1mm精度: {np.mean(within_1mm):.1f}±{np.std(within_1mm):.1f}%")
        print(f"   平均5mm精度: {np.mean(within_5mm):.1f}±{np.std(within_5mm):.1f}%")
        
        # Save final results
        with open('f3_dataset_complete.json', 'w') as f:
            json.dump(all_results, f, indent=2)
        
        print(f"✅ 完整数据集已保存: f3_dataset_complete.json")
        
        return all_results
    
    return None

def convert_to_simple_format(results):
    """Convert results to simple format for training"""
    
    if not results:
        return
    
    print(f"\n🔄 **转换为训练格式**")
    
    # Create simple directory structure
    output_dir = Path("F3SimpleDataset")
    output_dir.mkdir(exist_ok=True)
    
    # Simple train/val/test split
    n_samples = len(results)
    train_size = int(n_samples * 0.7)
    val_size = int(n_samples * 0.15)
    
    train_results = results[:train_size]
    val_results = results[train_size:train_size+val_size]
    test_results = results[train_size+val_size:]
    
    splits = {
        'train': train_results,
        'val': val_results,
        'test': test_results
    }
    
    for split_name, split_results in splits.items():
        if split_results:
            split_dir = output_dir / split_name
            split_dir.mkdir(exist_ok=True)
            
            for result in split_results:
                # Save as simple numpy files
                sample_id = result['sample_id']
                
                keypoints = np.array(result['f3_keypoints'])
                point_cloud = np.array(result['point_cloud'])
                
                np.save(split_dir / f"{sample_id}_keypoints.npy", keypoints)
                np.save(split_dir / f"{sample_id}_pointcloud.npy", point_cloud)
    
    # Save metadata
    metadata = {
        'dataset_name': 'F3SimpleDataset',
        'total_samples': len(results),
        'splits': {k: len(v) for k, v in splits.items()},
        'format': 'numpy_files',
        'keypoints_per_sample': 19,
        'component': 'F3_only'
    }
    
    with open(output_dir / 'metadata.json', 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"✅ 简单格式数据集已保存: {output_dir}")
    print(f"   训练: {len(train_results)}, 验证: {len(val_results)}, 测试: {len(test_results)}")

def main():
    """Main function with robust error handling"""
    
    try:
        # Create F3 dataset
        results = create_f3_dataset_batch()
        
        if results:
            # Convert to simple format
            convert_to_simple_format(results)
            
            print(f"\n🎉 **F3数据集创建成功!**")
            print(f"📊 样本数量: {len(results)}")
            print(f"🎯 关键点: 19个F3解剖关键点")
            print(f"💎 质量: 基于验证的优秀STL-CSV对齐")
            print(f"📁 格式: JSON + 简单numpy文件")
            
        else:
            print(f"❌ F3数据集创建失败")
            
    except Exception as e:
        print(f"❌ 创建过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
