#!/usr/bin/env python3
"""
详细样本分析
深入分析600051样本的具体特征，理解为什么它被标记为异常
"""

import numpy as np
import matplotlib.pyplot as plt

def analyze_sample_600051_details():
    """详细分析600051样本"""
    
    print("🔍 **600051样本详细特征分析**")
    print("深入理解为什么这个样本被算法标记为异常")
    print("=" * 80)
    
    # 加载数据
    data = np.load('f3_reduced_12kp_stable.npz', allow_pickle=True)
    sample_ids = data['sample_ids']
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    
    # 找到600051样本
    target_idx = None
    for i, sid in enumerate(sample_ids):
        if sid == '600051':
            target_idx = i
            break
    
    target_pc = point_clouds[target_idx]
    target_kp = keypoints[target_idx]
    
    print(f"📊 **基本信息**:")
    print(f"   样本ID: 600051")
    print(f"   索引: {target_idx}")
    print(f"   点云形状: {target_pc.shape}")
    print(f"   关键点形状: {target_kp.shape}")
    
    return target_idx, target_pc, target_kp

def compare_keypoint_patterns():
    """对比关键点模式"""
    
    target_idx, target_pc, target_kp = analyze_sample_600051_details()
    
    print(f"\n🔍 **关键点模式对比**:")
    
    # 加载所有数据
    data = np.load('f3_reduced_12kp_stable.npz', allow_pickle=True)
    sample_ids = data['sample_ids']
    keypoints = data['keypoints']
    
    # 计算所有样本的关键点间距离
    def compute_keypoint_distances(kp):
        """计算关键点间距离"""
        distances = []
        for i in range(12):
            for j in range(i+1, 12):
                dist = np.linalg.norm(kp[i] - kp[j])
                distances.append(dist)
        return np.array(distances)
    
    # 计算所有样本的距离模式
    all_distances = []
    for kp in keypoints:
        distances = compute_keypoint_distances(kp)
        all_distances.append(distances)
    
    all_distances = np.array(all_distances)  # [97, 66]
    
    # 600051的距离模式
    target_distances = all_distances[target_idx]
    
    # 计算统计信息
    mean_distances = np.mean(all_distances, axis=0)
    std_distances = np.std(all_distances, axis=0)
    
    # 计算600051的Z-score
    z_scores = (target_distances - mean_distances) / std_distances
    
    print(f"   关键点间距离数量: {len(target_distances)}")
    print(f"   600051距离统计:")
    print(f"     均值: {np.mean(target_distances):.2f}")
    print(f"     标准差: {np.std(target_distances):.2f}")
    print(f"     最小值: {np.min(target_distances):.2f}")
    print(f"     最大值: {np.max(target_distances):.2f}")
    
    print(f"   全体样本距离统计:")
    print(f"     均值: {np.mean(mean_distances):.2f}")
    print(f"     标准差: {np.mean(std_distances):.2f}")
    
    # 找出异常的距离对
    abnormal_pairs = []
    for i, z_score in enumerate(z_scores):
        if abs(z_score) > 2:  # 超过2个标准差
            # 找出对应的关键点对
            pair_idx = 0
            for p in range(12):
                for q in range(p+1, 12):
                    if pair_idx == i:
                        abnormal_pairs.append((p, q, z_score, target_distances[i]))
                        break
                    pair_idx += 1
    
    print(f"\n🚨 **异常的关键点距离对**:")
    if abnormal_pairs:
        for p, q, z_score, distance in abnormal_pairs:
            print(f"   关键点 {p}-{q}: 距离={distance:.2f}, Z-score={z_score:.2f}")
    else:
        print(f"   没有发现显著异常的距离对")
    
    return target_distances, mean_distances, std_distances, z_scores

def analyze_keypoint_positions():
    """分析关键点位置"""
    
    target_idx, target_pc, target_kp = analyze_sample_600051_details()
    
    print(f"\n📍 **关键点位置分析**:")
    
    # 加载所有数据
    data = np.load('f3_reduced_12kp_stable.npz', allow_pickle=True)
    keypoints = data['keypoints']
    
    # 中心化所有关键点
    centered_keypoints = []
    for kp in keypoints:
        kp_center = np.mean(kp, axis=0)
        kp_centered = kp - kp_center
        centered_keypoints.append(kp_centered)
    
    centered_keypoints = np.array(centered_keypoints)  # [97, 12, 3]
    
    # 600051的中心化关键点
    target_centered = centered_keypoints[target_idx]
    
    # 计算每个关键点位置的统计信息
    print(f"   600051中心化关键点位置:")
    for i in range(12):
        pos = target_centered[i]
        print(f"     关键点{i}: [{pos[0]:6.2f}, {pos[1]:6.2f}, {pos[2]:6.2f}]")
    
    # 计算每个关键点在所有样本中的分布
    print(f"\n   各关键点位置变异性:")
    for i in range(12):
        all_positions = centered_keypoints[:, i, :]  # [97, 3]
        position_std = np.std(all_positions, axis=0)
        target_position = target_centered[i]
        
        # 计算Z-score
        position_mean = np.mean(all_positions, axis=0)
        z_score = np.abs((target_position - position_mean) / position_std)
        
        max_z = np.max(z_score)
        if max_z > 2:
            print(f"     关键点{i}: 最大Z-score={max_z:.2f} ⚠️")
        else:
            print(f"     关键点{i}: 最大Z-score={max_z:.2f}")

def analyze_shape_characteristics():
    """分析形状特征"""
    
    target_idx, target_pc, target_kp = analyze_sample_600051_details()
    
    print(f"\n📐 **形状特征分析**:")
    
    # 加载所有数据
    data = np.load('f3_reduced_12kp_stable.npz', allow_pickle=True)
    keypoints = data['keypoints']
    
    def compute_shape_features(kp):
        """计算形状特征"""
        # 中心化
        kp_center = np.mean(kp, axis=0)
        kp_centered = kp - kp_center
        
        # 计算主轴长度 (PCA)
        from sklearn.decomposition import PCA
        pca = PCA()
        pca.fit(kp_centered)
        
        # 主成分方差 (形状的主轴长度)
        explained_variance = pca.explained_variance_
        
        # 形状比例
        aspect_ratios = [
            explained_variance[0] / explained_variance[1],  # 长宽比
            explained_variance[1] / explained_variance[2],  # 宽高比
            explained_variance[0] / explained_variance[2]   # 长高比
        ]
        
        # 体积估计 (关键点包围盒)
        ranges = np.max(kp_centered, axis=0) - np.min(kp_centered, axis=0)
        volume_estimate = np.prod(ranges)
        
        # 紧凑性 (球形度)
        distances_from_center = [np.linalg.norm(p) for p in kp_centered]
        compactness = np.std(distances_from_center) / np.mean(distances_from_center)
        
        return {
            'explained_variance': explained_variance,
            'aspect_ratios': aspect_ratios,
            'volume_estimate': volume_estimate,
            'compactness': compactness,
            'ranges': ranges
        }
    
    # 计算所有样本的形状特征
    all_features = []
    for kp in keypoints:
        features = compute_shape_features(kp)
        all_features.append(features)
    
    # 600051的形状特征
    target_features = all_features[target_idx]
    
    print(f"   600051形状特征:")
    print(f"     主成分方差: {target_features['explained_variance']}")
    print(f"     长宽比: {target_features['aspect_ratios'][0]:.2f}")
    print(f"     宽高比: {target_features['aspect_ratios'][1]:.2f}")
    print(f"     长高比: {target_features['aspect_ratios'][2]:.2f}")
    print(f"     体积估计: {target_features['volume_estimate']:.2f}")
    print(f"     紧凑性: {target_features['compactness']:.3f}")
    print(f"     各轴范围: {target_features['ranges']}")
    
    # 与其他样本对比
    print(f"\n   与其他样本对比:")
    
    # 长宽比对比
    all_aspect_ratios = [f['aspect_ratios'][0] for f in all_features]
    aspect_mean = np.mean(all_aspect_ratios)
    aspect_std = np.std(all_aspect_ratios)
    aspect_z = (target_features['aspect_ratios'][0] - aspect_mean) / aspect_std
    print(f"     长宽比: {target_features['aspect_ratios'][0]:.2f} (均值: {aspect_mean:.2f}, Z-score: {aspect_z:.2f})")
    
    # 紧凑性对比
    all_compactness = [f['compactness'] for f in all_features]
    comp_mean = np.mean(all_compactness)
    comp_std = np.std(all_compactness)
    comp_z = (target_features['compactness'] - comp_mean) / comp_std
    print(f"     紧凑性: {target_features['compactness']:.3f} (均值: {comp_mean:.3f}, Z-score: {comp_z:.2f})")
    
    # 体积对比
    all_volumes = [f['volume_estimate'] for f in all_features]
    vol_mean = np.mean(all_volumes)
    vol_std = np.std(all_volumes)
    vol_z = (target_features['volume_estimate'] - vol_mean) / vol_std
    print(f"     体积估计: {target_features['volume_estimate']:.2f} (均值: {vol_mean:.2f}, Z-score: {vol_z:.2f})")
    
    return target_features, all_features

def create_visual_comparison():
    """创建可视化对比"""
    
    target_idx, target_pc, target_kp = analyze_sample_600051_details()
    
    print(f"\n📊 **创建可视化对比**:")
    
    # 加载所有数据
    data = np.load('f3_reduced_12kp_stable.npz', allow_pickle=True)
    keypoints = data['keypoints']
    
    # 选择几个正常样本进行对比
    normal_indices = [0, 10, 20, 30]  # 选择几个正常样本
    
    fig = plt.figure(figsize=(15, 10))
    
    # 绘制600051
    ax1 = fig.add_subplot(2, 3, 1, projection='3d')
    kp = keypoints[target_idx]
    kp_center = np.mean(kp, axis=0)
    kp_centered = kp - kp_center
    
    ax1.scatter(kp_centered[:, 0], kp_centered[:, 1], kp_centered[:, 2], 
               c='red', s=50, alpha=0.8)
    for i, point in enumerate(kp_centered):
        ax1.text(point[0], point[1], point[2], str(i), fontsize=8)
    ax1.set_title('600051 (异常样本)')
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')
    
    # 绘制正常样本
    for i, normal_idx in enumerate(normal_indices):
        ax = fig.add_subplot(2, 3, i+2, projection='3d')
        kp = keypoints[normal_idx]
        kp_center = np.mean(kp, axis=0)
        kp_centered = kp - kp_center
        
        ax.scatter(kp_centered[:, 0], kp_centered[:, 1], kp_centered[:, 2], 
                  c='blue', s=50, alpha=0.8)
        for j, point in enumerate(kp_centered):
            ax.text(point[0], point[1], point[2], str(j), fontsize=8)
        ax.set_title(f'样本{normal_idx} (正常)')
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
    
    plt.tight_layout()
    plt.savefig('sample_600051_comparison.png', dpi=300, bbox_inches='tight')
    print(f"   📊 可视化对比图已保存: sample_600051_comparison.png")
    plt.close()

def final_assessment():
    """最终评估"""
    
    print(f"\n🎯 **600051样本最终评估**:")
    
    # 综合所有分析结果
    target_distances, mean_distances, std_distances, z_scores = compare_keypoint_patterns()
    analyze_keypoint_positions()
    target_features, all_features = analyze_shape_characteristics()
    create_visual_comparison()
    
    print(f"\n📋 **综合评估结果**:")
    
    # 检查各种异常指标
    issues = []
    
    # 1. 距离模式异常
    extreme_z_scores = np.abs(z_scores) > 2
    if np.any(extreme_z_scores):
        issues.append(f"关键点距离模式异常: {np.sum(extreme_z_scores)}个距离对超过2σ")
    
    # 2. 形状比例异常
    all_aspect_ratios = [f['aspect_ratios'][0] for f in all_features]
    aspect_mean = np.mean(all_aspect_ratios)
    aspect_std = np.std(all_aspect_ratios)
    aspect_z = abs((target_features['aspect_ratios'][0] - aspect_mean) / aspect_std)
    
    if aspect_z > 2:
        issues.append(f"长宽比异常: Z-score={aspect_z:.2f}")
    
    # 3. 紧凑性异常
    all_compactness = [f['compactness'] for f in all_features]
    comp_mean = np.mean(all_compactness)
    comp_std = np.std(all_compactness)
    comp_z = abs((target_features['compactness'] - comp_mean) / comp_std)
    
    if comp_z > 2:
        issues.append(f"紧凑性异常: Z-score={comp_z:.2f}")
    
    print(f"   发现的问题:")
    if issues:
        for issue in issues:
            print(f"     • {issue}")
    else:
        print(f"     • 没有发现显著的统计学异常")
    
    print(f"\n💡 **最终建议**:")
    
    if len(issues) == 0:
        print(f"   ✅ **保留样本600051**")
        print(f"   ✅ 虽然被算法标记为异常，但统计分析未发现显著问题")
        print(f"   ✅ 可能是算法的误报，或者是正常的生物学变异")
        print(f"   ✅ 您的人工检查结果更可信")
    elif len(issues) <= 1:
        print(f"   ⚠️ **谨慎保留样本600051**")
        print(f"   ⚠️ 发现轻微异常，但可能在可接受范围内")
        print(f"   ⚠️ 建议标记并监控训练效果")
    else:
        print(f"   🚨 **考虑移除样本600051**")
        print(f"   🚨 发现多项异常，可能确实存在问题")
        print(f"   🚨 但仍建议结合医学专家意见")

def main():
    """主函数"""
    
    print("🔍 **600051样本深度分析**")
    print("🎯 **目标: 理解为什么这个样本被标记为异常**")
    print("=" * 80)
    
    # 详细分析
    analyze_sample_600051_details()
    compare_keypoint_patterns()
    analyze_keypoint_positions()
    analyze_shape_characteristics()
    create_visual_comparison()
    final_assessment()
    
    print(f"\n🎉 **分析完成!**")
    print(f"📊 已生成详细的特征分析和可视化对比")
    print(f"💡 建议结合人工检查和统计分析做最终决定")
    print(f"🔍 如果人工检查认为正常，那很可能是算法误报")

if __name__ == "__main__":
    main()
