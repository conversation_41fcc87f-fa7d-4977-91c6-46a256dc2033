#!/usr/bin/env python3
"""
Test Dataset Loading

Simple test to check if our dataset can be loaded without issues.
"""

import numpy as np
import torch
from pathlib import Path
import gc

def test_basic_loading():
    """Test basic dataset loading"""
    
    print("🧪 **测试数据集基本加载**")
    
    dataset_path = "high_quality_f3_dataset.npz"
    
    if not Path(dataset_path).exists():
        print(f"❌ 数据集文件不存在: {dataset_path}")
        return False
    
    try:
        print("📦 加载数据集...")
        data = np.load(dataset_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        centers = data['centers']
        
        print(f"✅ 数据集加载成功:")
        print(f"   样本数: {len(sample_ids)}")
        print(f"   点云形状: {point_clouds.shape}")
        print(f"   关键点形状: {keypoints.shape}")
        print(f"   中心点形状: {centers.shape}")
        
        # Test memory usage
        total_size = (point_clouds.nbytes + keypoints.nbytes + centers.nbytes) / (1024**3)
        print(f"   总内存使用: {total_size:.2f} GB")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据集加载失败: {e}")
        return False

def test_single_sample():
    """Test loading and processing a single sample"""
    
    print("\n🧪 **测试单样本处理**")
    
    try:
        data = np.load("high_quality_f3_dataset.npz", allow_pickle=True)
        
        # Get first sample
        point_cloud = data['point_clouds'][0]  # (50000, 3)
        keypoints = data['keypoints'][0]       # (19, 3)
        
        print(f"📊 单样本信息:")
        print(f"   点云形状: {point_cloud.shape}")
        print(f"   关键点形状: {keypoints.shape}")
        print(f"   点云范围: [{np.min(point_cloud):.2f}, {np.max(point_cloud):.2f}]")
        print(f"   关键点范围: [{np.min(keypoints):.2f}, {np.max(keypoints):.2f}]")
        
        # Test downsampling
        print("\n🔧 测试下采样...")
        target_points = 4096
        
        if len(point_cloud) > target_points:
            indices = np.random.choice(len(point_cloud), target_points, replace=False)
            sampled_pc = point_cloud[indices]
        else:
            sampled_pc = point_cloud
        
        print(f"   下采样后形状: {sampled_pc.shape}")
        
        # Test tensor conversion
        print("\n🔧 测试张量转换...")
        pc_tensor = torch.FloatTensor(sampled_pc)
        kp_tensor = torch.FloatTensor(keypoints)
        
        print(f"   点云张量形状: {pc_tensor.shape}")
        print(f"   关键点张量形状: {kp_tensor.shape}")
        
        # Test GPU transfer (if available)
        if torch.cuda.is_available():
            print("\n🔧 测试GPU传输...")
            device = torch.device('cuda:1')
            pc_gpu = pc_tensor.to(device)
            kp_gpu = kp_tensor.to(device)
            
            print(f"   GPU点云形状: {pc_gpu.shape}")
            print(f"   GPU关键点形状: {kp_gpu.shape}")
            
            # Clean up GPU memory
            del pc_gpu, kp_gpu
            torch.cuda.empty_cache()
        
        print("✅ 单样本处理成功")
        return True
        
    except Exception as e:
        print(f"❌ 单样本处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_processing():
    """Test batch processing"""
    
    print("\n🧪 **测试批处理**")
    
    try:
        data = np.load("high_quality_f3_dataset.npz", allow_pickle=True)
        
        # Get first 4 samples
        batch_size = 4
        point_clouds = data['point_clouds'][:batch_size]  # (4, 50000, 3)
        keypoints = data['keypoints'][:batch_size]        # (4, 19, 3)
        
        print(f"📊 批处理信息:")
        print(f"   批大小: {batch_size}")
        print(f"   点云批形状: {point_clouds.shape}")
        print(f"   关键点批形状: {keypoints.shape}")
        
        # Downsample each sample in batch
        target_points = 4096
        sampled_batch = []
        
        for i in range(batch_size):
            pc = point_clouds[i]
            if len(pc) > target_points:
                indices = np.random.choice(len(pc), target_points, replace=False)
                sampled_pc = pc[indices]
            else:
                sampled_pc = pc
            sampled_batch.append(sampled_pc)
        
        sampled_batch = np.stack(sampled_batch)  # (4, 4096, 3)
        
        print(f"   下采样批形状: {sampled_batch.shape}")
        
        # Convert to tensors
        pc_batch_tensor = torch.FloatTensor(sampled_batch)
        kp_batch_tensor = torch.FloatTensor(keypoints)
        
        print(f"   点云批张量: {pc_batch_tensor.shape}")
        print(f"   关键点批张量: {kp_batch_tensor.shape}")
        
        # Test memory usage
        memory_usage = (pc_batch_tensor.numel() + kp_batch_tensor.numel()) * 4 / (1024**2)
        print(f"   批内存使用: {memory_usage:.2f} MB")
        
        print("✅ 批处理成功")
        return True
        
    except Exception as e:
        print(f"❌ 批处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_model():
    """Test a very simple model forward pass"""
    
    print("\n🧪 **测试简单模型前向传播**")
    
    try:
        # Create a very simple model
        class TinyPointNet(torch.nn.Module):
            def __init__(self):
                super(TinyPointNet, self).__init__()
                self.conv1 = torch.nn.Conv1d(3, 64, 1)
                self.conv2 = torch.nn.Conv1d(64, 128, 1)
                self.fc = torch.nn.Linear(128, 19 * 3)
            
            def forward(self, x):
                # x: (batch, points, 3) -> (batch, 3, points)
                x = x.transpose(2, 1)
                x = torch.relu(self.conv1(x))
                x = torch.relu(self.conv2(x))
                x = torch.max(x, 2)[0]  # Global max pooling
                x = self.fc(x)
                return x.view(-1, 19, 3)
        
        # Create model
        model = TinyPointNet()
        
        if torch.cuda.is_available():
            device = torch.device('cuda:1')
            model = model.to(device)
        else:
            device = torch.device('cpu')
        
        print(f"🧠 模型创建成功，设备: {device}")
        
        # Create dummy input
        batch_size = 2
        num_points = 1024  # Very small for testing
        dummy_input = torch.randn(batch_size, num_points, 3).to(device)
        
        print(f"📊 测试输入形状: {dummy_input.shape}")
        
        # Forward pass
        with torch.no_grad():
            output = model(dummy_input)
        
        print(f"📊 输出形状: {output.shape}")
        print("✅ 简单模型测试成功")
        
        # Clean up
        del model, dummy_input, output
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        return True
        
    except Exception as e:
        print(f"❌ 简单模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    
    print("🚀 **数据集和模型测试套件**")
    print("=" * 60)
    
    tests = [
        ("基本加载", test_basic_loading),
        ("单样本处理", test_single_sample),
        ("批处理", test_batch_processing),
        ("简单模型", test_simple_model)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 运行测试: {test_name}")
        print(f"{'='*60}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            print(f"💥 {test_name} 测试崩溃: {e}")
            results.append((test_name, False))
        
        # Force cleanup
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    # Summary
    print(f"\n{'='*60}")
    print("📋 **测试总结**")
    print(f"{'='*60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过! 可以开始训练模型")
    else:
        print("⚠️ 部分测试失败，需要调试")

if __name__ == "__main__":
    main()
