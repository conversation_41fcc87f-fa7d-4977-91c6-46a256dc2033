#!/usr/bin/env python3
"""
简化模型 + 增强数据增强训练脚本
目标: 从6.048mm突破到5.5mm
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random
from simplified_ensemble_enhanced_augmentation import SimplifiedEnsemblePointNet, EnhancedAugmentationDataset, calculate_metrics

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class AdaptiveLoss(nn.Module):
    """自适应损失函数"""
    
    def __init__(self, alpha=0.7, beta=0.3):
        super(AdaptiveLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, pred, target):
        mse_loss = F.mse_loss(pred, target)
        smooth_l1_loss = F.smooth_l1_loss(pred, target)
        total_loss = self.alpha * mse_loss + self.beta * smooth_l1_loss
        return total_loss

def train_simplified_enhanced():
    """训练简化模型 + 增强数据增强"""
    
    print(f"🚀 **简化模型 + 增强数据增强训练**")
    print(f"🔧 **简化**: 2个集成模块，减少参数")
    print(f"📈 **增强**: 6种数据增强策略")
    print(f"🎯 **基础**: 6.048mm优化版本")
    print(f"📊 **目标**: 突破5.5mm，接近5.829mm")
    print("=" * 80)
    
    set_seed(42)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 数据集 (使用增强数据增强)
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    train_dataset = EnhancedAugmentationDataset('f3_reduced_12kp_stable.npz', 'train', 
                                              num_points=4096, test_samples=test_samples, 
                                              augment=True, seed=42)
    val_dataset = EnhancedAugmentationDataset('f3_reduced_12kp_stable.npz', 'val', 
                                            num_points=4096, test_samples=test_samples, 
                                            augment=False, seed=42)
    
    batch_size = 4  # 保持成功配置
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
    
    # 简化模型
    model = SimplifiedEnsemblePointNet(num_keypoints=12, dropout_rate=0.2, num_ensembles=2).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 模型参数: {total_params:,}")
    
    # 损失函数
    criterion = AdaptiveLoss(alpha=0.7, beta=0.3)
    
    # 优化器 (稍微调整学习率)
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)  # 稍微提高学习率
    
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.8, patience=10, min_lr=1e-6  # 更敏感的调度
    )
    
    num_epochs = 120  # 增加训练轮数
    best_val_error = float('inf')
    patience = 25  # 增加耐心
    patience_counter = 0
    history = []
    min_delta = 0.003  # 更小的改进阈值
    
    print(f"🎯 训练配置: 简化集成双Softmax + 增强数据增强")
    print(f"   集成模块: 2个 (简化)")
    print(f"   数据增强: 6种策略 (旋转+平移+缩放+噪声+采样+扰动)")
    print(f"   候选点数: 128 (减少)")
    print(f"   学习率: 0.001 (稍微提高)")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 40)
        
        # 训练
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        for batch in train_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                        
            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue
        
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # 验证 (使用简化的集成双Softmax精细化)
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)  # 推理时使用简化集成双Softmax
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # 学习率调度
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 打印结果
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"5mm={train_metrics['within_5mm_percent']:.1f}%, 7mm={train_metrics['within_7mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"5mm={val_metrics['within_5mm_percent']:.1f}%, 7mm={val_metrics['within_7mm_percent']:.1f}%")
        print(f"学习率: {current_lr:.2e}")
        
        # 保存历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'learning_rate': current_lr
        })
        
        # 检查改进
        current_error = val_metrics['mean_distance']
        improvement = best_val_error - current_error
        
        if improvement > min_delta:
            best_val_error = current_error
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics,
                'model_name': 'Simplified_Ensemble_Enhanced_Augmentation',
                'config': 'simplified_2_modules_enhanced_augmentation'
            }, f'best_simplified_enhanced_{best_val_error:.3f}mm.pth')
            
            print(f"🎉 新最佳! 验证误差: {best_val_error:.3f}mm (改进{improvement:.3f}mm)")
            
            if best_val_error <= 5.0:
                print(f"🏆 **突破5.0mm医疗级目标!**")
            elif best_val_error < 5.5:
                print(f"🎯 **突破5.5mm目标!** 达到医疗级精度")
            elif best_val_error < 5.829:
                print(f"✅ **超越理论基线!** 简化+增强有效")
            elif best_val_error < 6.048:
                print(f"✅ **超越优化版本!** 简化策略有效")
        else:
            patience_counter += 1
            print(f"⏳ 无显著改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停触发")
            break
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    
    # 保存结果
    results = {
        'method': 'Simplified Ensemble + Enhanced Augmentation',
        'baseline_error': 6.048,
        'best_val_error': float(best_val_error),
        'improvement': float((6.048 - best_val_error) / 6.048 * 100),
        'training_time_minutes': float(total_time / 60),
        'epochs_trained': len(history),
        'history': history,
        'simplified_config': {
            'num_ensembles': 2,
            'threshold_ratios': [0.12, 0.18],
            'temperatures': [1.8, 2.2],
            'weight_ratios': [0.7, 0.8],
            'candidate_points': 128,
            'enhanced_augmentation': {
                'rotation': '80% (multi-axis)',
                'translation': '70% (enhanced range)',
                'scaling': '60% (anisotropic)',
                'noise': '70% (gaussian+uniform)',
                'sampling': '40% (dropout)',
                'perturbation': '30% (local)'
            }
        }
    }
    
    with open('simplified_enhanced_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 **简化模型+增强数据增强训练完成!**")
    print(f"📊 优化版本基线: 6.048mm")
    print(f"🎯 简化+增强最佳: {best_val_error:.3f}mm")
    print(f"📈 改进幅度: {(6.048 - best_val_error) / 6.048 * 100:.1f}%")
    print(f"⏱️  训练时间: {total_time/60:.1f}分钟")
    
    if best_val_error < 5.0:
        print(f"🏆 **成功突破5.0mm医疗级目标!**")
    elif best_val_error < 5.5:
        print(f"🎯 **成功突破5.5mm目标!** 达到医疗级精度")
    elif best_val_error < 5.829:
        print(f"✅ **成功超越理论基线!** 简化+增强策略有效")
    elif best_val_error < 6.048:
        print(f"✅ **成功超越优化版本!** 简化策略有效")
    else:
        print(f"💡 **接近基线性能** 需要进一步调优")
    
    print(f"\n🔬 **策略分析**:")
    print(f"   - 模型简化: 减少过拟合风险")
    print(f"   - 数据增强: 提高泛化能力")
    print(f"   - 参数优化: 更适合小数据集")
    
    return best_val_error, results

if __name__ == "__main__":
    set_seed(42)
    best_error, results = train_simplified_enhanced()
