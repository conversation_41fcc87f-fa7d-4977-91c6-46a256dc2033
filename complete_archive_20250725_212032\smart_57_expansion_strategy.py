#!/usr/bin/env python3
"""
智能57点扩展策略
Smart 57-point expansion strategy based on proven successful 12-point model
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import json
import os
from tqdm import tqdm

def load_proven_12point_data():
    """加载已验证有效的12点数据集"""
    
    print("📊 加载已验证有效的12点数据集...")
    
    try:
        # 加载男性数据
        male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz', allow_pickle=True)
        male_pc = male_data['point_clouds']
        male_kp = male_data['keypoints']
        
        # 加载女性数据
        female_data = np.load('archive/old_experiments/f3_reduced_12kp_female.npz', allow_pickle=True)
        female_pc = female_data['point_clouds']
        female_kp = female_data['keypoints']
        
        print(f"✅ 原始成功数据集:")
        print(f"   男性: 点云{male_pc.shape}, 关键点{male_kp.shape}")
        print(f"   女性: 点云{female_pc.shape}, 关键点{female_kp.shape}")
        
        # 合并数据
        all_pc = np.vstack([male_pc, female_pc])
        all_kp = np.vstack([male_kp, female_kp])
        
        # 创建性别标签
        gender_labels = ['male'] * len(male_pc) + ['female'] * len(female_pc)
        
        print(f"📋 合并后数据:")
        print(f"   总样本: {len(all_pc)}")
        print(f"   点云: {all_pc.shape}")
        print(f"   12关键点: {all_kp.shape}")
        
        return all_pc, all_kp, gender_labels
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None, None, None

def load_57point_annotations():
    """加载57点标注数据"""
    
    print("📊 加载57点标注数据...")
    
    try:
        data = np.load('expansion_training_keypoints.npz', allow_pickle=True)
        keypoints_57 = data['target_57']
        keypoints_12 = data['input_12']
        sample_ids = data['sample_ids']
        
        print(f"✅ 57点标注数据:")
        print(f"   样本数: {len(sample_ids)}")
        print(f"   57关键点: {keypoints_57.shape}")
        print(f"   12关键点: {keypoints_12.shape}")
        
        return keypoints_57, keypoints_12, sample_ids
        
    except Exception as e:
        print(f"❌ 57点数据加载失败: {e}")
        return None, None, None

def create_expansion_mapping():
    """创建12→57扩展映射"""
    
    # 12点到57点的映射关系
    mapping_12_to_57 = {
        0: 0,   # F1-1 -> 原始索引0
        1: 1,   # F1-2 -> 原始索引1
        2: 2,   # F1-3 -> 原始索引2
        3: 12,  # F1-13 -> 原始索引12
        4: 19,  # F2-1 -> 原始索引19
        5: 20,  # F2-2 -> 原始索引20
        6: 21,  # F2-3 -> 原始索引21
        7: 31,  # F2-13 -> 原始索引31
        8: 38,  # F3-1 -> 原始索引38
        9: 52,  # F3-15 -> 原始索引52
        10: 50, # F3-13 -> 原始索引50
        11: 51, # F3-14 -> 原始索引51
    }
    
    # 57点的区域分组
    region_groups = {
        'F1': list(range(0, 19)),    # F1区域: 0-18
        'F2': list(range(19, 38)),   # F2区域: 19-37
        'F3': list(range(38, 57))    # F3区域: 38-56
    }
    
    # 12点在各区域的分布
    region_12_points = {
        'F1': [0, 1, 2, 3],      # 对应mapping中的0,1,2,3
        'F2': [4, 5, 6, 7],      # 对应mapping中的4,5,6,7
        'F3': [8, 9, 10, 11]     # 对应mapping中的8,9,10,11
    }
    
    return mapping_12_to_57, region_groups, region_12_points

class SmartExpansionStrategy:
    """智能扩展策略"""
    
    def __init__(self):
        self.mapping_12_to_57, self.region_groups, self.region_12_points = create_expansion_mapping()
        
    def strategy_1_direct_mapping(self, proven_12kp, target_57kp):
        """策略1: 直接映射 - 使用已验证的12点数据"""
        
        print("🎯 策略1: 直接映射扩展")
        
        # 找到最匹配的57点样本
        matched_pairs = []
        
        for i, kp_12 in enumerate(proven_12kp):
            best_match_idx = -1
            best_distance = float('inf')
            
            # 在57点数据中找最匹配的样本
            for j, kp_57 in enumerate(target_57kp):
                # 提取57点中对应的12个点
                extracted_12 = np.zeros((12, 3))
                for k in range(12):
                    original_idx = self.mapping_12_to_57[k]
                    if original_idx < len(kp_57):
                        extracted_12[k] = kp_57[original_idx]
                
                # 计算距离
                distance = np.mean(np.linalg.norm(kp_12 - extracted_12, axis=1))
                
                if distance < best_distance:
                    best_distance = distance
                    best_match_idx = j
            
            if best_match_idx != -1 and best_distance < 50:  # 50mm阈值
                matched_pairs.append((i, best_match_idx, best_distance))
        
        print(f"   找到 {len(matched_pairs)} 个匹配对")
        print(f"   平均匹配距离: {np.mean([d for _, _, d in matched_pairs]):.2f}mm")
        
        return matched_pairs
    
    def strategy_2_interpolation(self, proven_12kp):
        """策略2: 插值扩展 - 基于解剖学知识插值"""
        
        print("🎯 策略2: 解剖学插值扩展")
        
        expanded_57kp = []
        
        for kp_12 in proven_12kp:
            kp_57 = np.zeros((57, 3))
            
            # 首先放置已知的12个点
            for i in range(12):
                target_idx = self.mapping_12_to_57[i]
                kp_57[target_idx] = kp_12[i]
            
            # 为每个区域进行插值
            for region_name, region_indices in self.region_groups.items():
                region_12_idx = self.region_12_points[region_name]
                
                # 获取该区域已知的12点中的点
                known_points = []
                known_indices = []
                
                for idx_12 in region_12_idx:
                    idx_57 = self.mapping_12_to_57[idx_12]
                    known_points.append(kp_57[idx_57])
                    known_indices.append(idx_57)
                
                # 对该区域的其他点进行插值
                self.interpolate_region_points(kp_57, region_indices, known_points, known_indices)
            
            expanded_57kp.append(kp_57)
        
        return np.array(expanded_57kp)
    
    def interpolate_region_points(self, kp_57, region_indices, known_points, known_indices):
        """对区域内的点进行插值"""
        
        known_points = np.array(known_points)
        
        # 计算区域中心
        region_center = np.mean(known_points, axis=0)
        
        # 为未知点生成坐标
        for idx in region_indices:
            if idx not in known_indices:
                # 简单的插值策略：在已知点周围生成
                if len(known_points) >= 2:
                    # 在最近的两个已知点之间插值
                    distances = [np.linalg.norm(region_center - kp) for kp in known_points]
                    closest_idx = np.argmin(distances)
                    
                    # 添加一些随机偏移
                    offset = np.random.normal(0, 5, 3)  # 5mm标准差
                    kp_57[idx] = known_points[closest_idx] + offset
                else:
                    # 如果已知点太少，使用区域中心加偏移
                    offset = np.random.normal(0, 10, 3)  # 10mm标准差
                    kp_57[idx] = region_center + offset
    
    def strategy_3_learning_based(self, proven_pc, proven_12kp, target_57kp):
        """策略3: 学习基础扩展 - 训练扩展网络"""
        
        print("🎯 策略3: 学习基础扩展")
        
        # 找到匹配的训练对
        matched_pairs = self.strategy_1_direct_mapping(proven_12kp, target_57kp)
        
        if len(matched_pairs) < 10:
            print("⚠️ 匹配样本太少，无法训练学习模型")
            return None
        
        # 准备训练数据
        train_12kp = []
        train_57kp = []
        train_pc = []
        
        for proven_idx, target_idx, distance in matched_pairs:
            train_12kp.append(proven_12kp[proven_idx])
            train_57kp.append(target_57kp[target_idx])
            train_pc.append(proven_pc[proven_idx])
        
        train_12kp = np.array(train_12kp)
        train_57kp = np.array(train_57kp)
        train_pc = np.array(train_pc)
        
        print(f"   训练数据: {len(train_12kp)} 个样本")
        
        return train_12kp, train_57kp, train_pc

def evaluate_expansion_quality(original_12kp, expanded_57kp, target_57kp=None):
    """评估扩展质量"""
    
    print("🔍 评估扩展质量...")
    
    # 从扩展的57点中提取12点，验证一致性
    mapping_12_to_57, _, _ = create_expansion_mapping()
    
    extracted_12kp = np.zeros((len(expanded_57kp), 12, 3))
    for i in range(len(expanded_57kp)):
        for j in range(12):
            target_idx = mapping_12_to_57[j]
            extracted_12kp[i, j] = expanded_57kp[i, target_idx]
    
    # 计算12点一致性误差
    consistency_errors = []
    for i in range(len(original_12kp)):
        error = np.mean(np.linalg.norm(original_12kp[i] - extracted_12kp[i], axis=1))
        consistency_errors.append(error)
    
    avg_consistency_error = np.mean(consistency_errors)
    
    print(f"📊 扩展质量评估:")
    print(f"   12点一致性误差: {avg_consistency_error:.2f}mm")
    
    # 如果有目标57点，计算准确性
    if target_57kp is not None:
        accuracy_errors = []
        for i in range(min(len(expanded_57kp), len(target_57kp))):
            error = np.mean(np.linalg.norm(expanded_57kp[i] - target_57kp[i], axis=1))
            accuracy_errors.append(error)
        
        avg_accuracy_error = np.mean(accuracy_errors)
        print(f"   57点准确性误差: {avg_accuracy_error:.2f}mm")
        
        return avg_consistency_error, avg_accuracy_error
    
    return avg_consistency_error, None

def main():
    """主函数"""
    
    print("🎯 智能57点扩展策略")
    print("基于已验证成功的12点模型进行智能扩展")
    print("=" * 80)
    
    # 步骤1: 加载已验证有效的12点数据
    proven_pc, proven_12kp, gender_labels = load_proven_12point_data()
    
    if proven_pc is None:
        print("❌ 无法加载已验证的12点数据")
        return
    
    # 步骤2: 加载57点标注数据
    target_57kp, target_12kp, sample_ids = load_57point_annotations()
    
    if target_57kp is None:
        print("❌ 无法加载57点标注数据")
        return
    
    # 步骤3: 创建扩展策略
    strategy = SmartExpansionStrategy()
    
    # 策略1: 直接映射
    matched_pairs = strategy.strategy_1_direct_mapping(proven_12kp, target_57kp)
    
    # 策略2: 插值扩展
    interpolated_57kp = strategy.strategy_2_interpolation(proven_12kp)
    
    # 策略3: 学习基础扩展
    learning_data = strategy.strategy_3_learning_based(proven_pc, proven_12kp, target_57kp)
    
    # 评估扩展质量
    print(f"\n📊 策略评估:")
    
    # 评估插值策略
    consistency_error, accuracy_error = evaluate_expansion_quality(
        proven_12kp, interpolated_57kp, target_57kp
    )
    
    # 保存结果
    results = {
        'strategy_1_matches': len(matched_pairs),
        'strategy_2_consistency_error': consistency_error,
        'strategy_2_accuracy_error': accuracy_error,
        'strategy_3_available': learning_data is not None,
        'proven_data_samples': len(proven_12kp),
        'target_data_samples': len(target_57kp)
    }
    
    with open('smart_expansion_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # 保存扩展的57点数据
    np.savez('smart_expanded_57_dataset.npz',
             proven_point_clouds=proven_pc,
             proven_12_keypoints=proven_12kp,
             interpolated_57_keypoints=interpolated_57kp,
             gender_labels=gender_labels,
             matched_pairs=matched_pairs)
    
    print(f"\n🎉 智能扩展策略完成！")
    print(f"📋 生成的文件:")
    print(f"   - smart_expansion_results.json (策略评估结果)")
    print(f"   - smart_expanded_57_dataset.npz (扩展的57点数据集)")
    
    print(f"\n💡 推荐策略:")
    if len(matched_pairs) > 20:
        print(f"   ✅ 策略1+3: 直接映射+学习扩展 ({len(matched_pairs)}个匹配对)")
    elif consistency_error < 20:
        print(f"   ✅ 策略2: 解剖学插值扩展 (一致性误差: {consistency_error:.2f}mm)")
    else:
        print(f"   ⚠️ 需要改进数据匹配或插值算法")
    
    print(f"\n🚀 下一步:")
    print(f"   1. 基于推荐策略训练57点模型")
    print(f"   2. 在已验证的点云数据上测试")
    print(f"   3. 与12点模型性能对比")

if __name__ == "__main__":
    main()
