#!/usr/bin/env python3
"""
验证误差尺度
Verify Error Scale
检查报告的误差是否是归一化后的误差，还是真实的物理误差
"""

import numpy as np
import torch
import sys
import os
from sklearn.model_selection import train_test_split

# 添加原始代码路径
sys.path.insert(0, os.path.abspath("archive/old_scripts"))

def analyze_data_scales():
    """分析数据尺度"""
    
    print("🔍 分析数据尺度和误差计算")
    print("=" * 80)
    
    datasets_to_check = [
        {
            'name': '历史基准数据集',
            'file': 'archive/old_experiments/f3_reduced_12kp_stable.npz',
            'kp_key': 'keypoints',
            'expected_error': 6.067
        },
        {
            'name': '医学级12点数据集',
            'file': 'medical_grade_progressive_12_dataset.npz',
            'kp_key': 'keypoints',
            'reported_error': 1.419
        },
        {
            'name': '医学级57点数据集',
            'file': 'medical_grade_full_57_dataset.npz',
            'kp_key': 'keypoints_57',
            'reported_error': 1.214
        }
    ]
    
    scale_analysis = {}
    
    for dataset_info in datasets_to_check:
        print(f"\n📊 分析 {dataset_info['name']}")
        print("-" * 50)
        
        try:
            data = np.load(dataset_info['file'], allow_pickle=True)
            point_clouds = data['point_clouds']
            keypoints = data[dataset_info['kp_key']]
            
            print(f"   样本数: {len(point_clouds)}")
            print(f"   关键点形状: {keypoints.shape}")
            print(f"   点云形状: {point_clouds.shape}")
            
            # 分析关键点尺度
            kp_flat = keypoints.reshape(-1, 3)
            kp_min = np.min(kp_flat, axis=0)
            kp_max = np.max(kp_flat, axis=0)
            kp_range = kp_max - kp_min
            kp_center = np.mean(kp_flat, axis=0)
            kp_std = np.std(kp_flat)
            
            print(f"\n   关键点统计:")
            print(f"     范围: [{kp_min[0]:.3f}, {kp_min[1]:.3f}, {kp_min[2]:.3f}] 到")
            print(f"           [{kp_max[0]:.3f}, {kp_max[1]:.3f}, {kp_max[2]:.3f}]")
            print(f"     尺度范围: [{kp_range[0]:.3f}, {kp_range[1]:.3f}, {kp_range[2]:.3f}]")
            print(f"     中心: [{kp_center[0]:.3f}, {kp_center[1]:.3f}, {kp_center[2]:.3f}]")
            print(f"     标准差: {kp_std:.3f}")
            
            # 分析点云尺度
            pc_flat = point_clouds.reshape(-1, 3)
            pc_min = np.min(pc_flat, axis=0)
            pc_max = np.max(pc_flat, axis=0)
            pc_range = pc_max - pc_min
            pc_center = np.mean(pc_flat, axis=0)
            pc_std = np.std(pc_flat)
            
            print(f"\n   点云统计:")
            print(f"     范围: [{pc_min[0]:.3f}, {pc_min[1]:.3f}, {pc_min[2]:.3f}] 到")
            print(f"           [{pc_max[0]:.3f}, {pc_max[1]:.3f}, {pc_max[2]:.3f}]")
            print(f"     尺度范围: [{pc_range[0]:.3f}, {pc_range[1]:.3f}, {pc_range[2]:.3f}]")
            print(f"     中心: [{pc_center[0]:.3f}, {pc_center[1]:.3f}, {pc_center[2]:.3f}]")
            print(f"     标准差: {pc_std:.3f}")
            
            # 判断是否归一化
            is_normalized = abs(kp_std - 1.0) < 0.1 and abs(pc_std - 1.0) < 0.1
            is_centered = np.linalg.norm(kp_center) < 0.1 and np.linalg.norm(pc_center) < 0.1
            
            print(f"\n   数据状态:")
            print(f"     是否归一化: {'✅ 是' if is_normalized else '❌ 否'}")
            print(f"     是否中心化: {'✅ 是' if is_centered else '❌ 否'}")
            
            if 'reported_error' in dataset_info:
                print(f"     报告误差: {dataset_info['reported_error']:.3f}")
                if is_normalized:
                    print(f"     ⚠️ 这是归一化后的误差！")
                else:
                    print(f"     ✅ 这是真实物理误差")
            
            scale_analysis[dataset_info['name']] = {
                'kp_std': kp_std,
                'pc_std': pc_std,
                'kp_center': kp_center,
                'pc_center': pc_center,
                'is_normalized': is_normalized,
                'is_centered': is_centered,
                'kp_range': kp_range,
                'pc_range': pc_range
            }
            
        except Exception as e:
            print(f"   ❌ 分析失败: {e}")
    
    return scale_analysis

def calculate_real_world_error():
    """计算真实世界误差"""
    
    print(f"\n🔧 计算真实世界误差")
    print("=" * 80)
    
    # 1. 找到原始数据的尺度信息
    print(f"📊 查找原始数据尺度信息...")
    
    try:
        # 加载原始医学数据处理报告
        import json
        with open('medical_grade_dataset_report.json', 'r') as f:
            report = json.load(f)
        
        print(f"✅ 找到医学级数据集报告")
        
    except:
        print(f"❌ 未找到数据集报告，尝试重新计算尺度...")
    
    # 2. 从原始数据计算尺度因子
    print(f"\n📊 从原始数据计算尺度因子...")
    
    try:
        # 加载一个原始样本来估算尺度
        from pathlib import Path
        import pandas as pd
        
        data_root = Path("/home/<USER>/pjc/GCN/data/Data")
        annotations_dir = data_root / "annotations"
        
        # 找一个样本文件
        sample_file = None
        for file in annotations_dir.glob("*-Table-XYZ.CSV"):
            sample_file = file
            break
        
        if sample_file:
            print(f"   分析原始样本: {sample_file.name}")
            
            # 读取原始标注
            try:
                df = pd.read_csv(sample_file, encoding='gbk')
            except:
                df = pd.read_csv(sample_file, encoding='utf-8')
            
            # 提取坐标
            coords = df[['X', 'Y', 'Z']].values
            
            # 计算原始尺度
            original_std = np.std(coords)
            original_range = np.max(coords) - np.min(coords)
            original_center = np.mean(coords, axis=0)
            
            print(f"   原始数据统计:")
            print(f"     标准差: {original_std:.2f}mm")
            print(f"     范围: {original_range:.2f}mm")
            print(f"     中心: [{original_center[0]:.1f}, {original_center[1]:.1f}, {original_center[2]:.1f}]mm")
            
            # 从医学级数据集创建时的信息推断尺度因子
            # 根据之前的输出，原始数据标准差约为78.99mm
            estimated_original_std = 78.99
            
            print(f"\n   尺度因子估算:")
            print(f"     估算原始标准差: {estimated_original_std:.2f}mm")
            print(f"     归一化后标准差: ~1.0")
            print(f"     尺度因子: {estimated_original_std:.2f}")
            
            return estimated_original_std
            
        else:
            print(f"   ❌ 未找到原始样本文件")
            return None
            
    except Exception as e:
        print(f"   ❌ 计算失败: {e}")
        return None

def recalculate_true_errors(scale_factor):
    """重新计算真实误差"""
    
    print(f"\n🔧 重新计算真实误差")
    print("=" * 80)
    
    if scale_factor is None:
        print(f"❌ 无尺度因子，无法计算真实误差")
        return
    
    print(f"📊 使用尺度因子: {scale_factor:.2f}mm")
    
    # 报告的归一化误差
    reported_errors = {
        '12点医学级': 1.419,
        '15点医学级': 1.380,
        '19点医学级': 1.309,
        '57点医学级': 1.214
    }
    
    print(f"\n📊 真实误差计算:")
    print(f"{'数据集':<15} {'归一化误差':<12} {'真实误差(mm)':<15} {'医学评估'}")
    print("-" * 65)
    
    for dataset, norm_error in reported_errors.items():
        real_error = norm_error * scale_factor
        
        # 医学评估
        if real_error < 2.0:
            assessment = "优秀"
        elif real_error < 5.0:
            assessment = "良好"
        elif real_error < 10.0:
            assessment = "可接受"
        else:
            assessment = "需改进"
        
        print(f"{dataset:<15} {norm_error:<12.3f} {real_error:<15.2f} {assessment}")
    
    # 与历史基准对比
    print(f"\n📊 与历史基准对比:")
    historical_12_error = 6.067  # 历史12点误差
    current_12_real = reported_errors['12点医学级'] * scale_factor
    
    improvement = (historical_12_error - current_12_real) / historical_12_error * 100
    
    print(f"   历史12点基准: {historical_12_error:.3f}mm")
    print(f"   当前12点真实: {current_12_real:.2f}mm")
    print(f"   真实改进幅度: {improvement:+.1f}%")
    
    if improvement > 0:
        print(f"   ✅ 确实有改进")
    else:
        print(f"   ❌ 实际性能下降")
    
    return {dataset: error * scale_factor for dataset, error in reported_errors.items()}

def verify_with_direct_calculation():
    """通过直接计算验证"""
    
    print(f"\n🔧 直接计算验证")
    print("=" * 80)
    
    print(f"📊 加载医学级数据集进行直接验证...")
    
    try:
        # 加载医学级12点数据集
        data = np.load('medical_grade_progressive_12_dataset.npz', allow_pickle=True)
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        print(f"   数据形状: PC{point_clouds.shape}, KP{keypoints.shape}")
        
        # 检查数据范围
        kp_std = np.std(keypoints)
        kp_range = np.max(keypoints) - np.min(keypoints)
        
        print(f"   关键点标准差: {kp_std:.3f}")
        print(f"   关键点范围: {kp_range:.3f}")
        
        if abs(kp_std - 1.0) < 0.1:
            print(f"   ⚠️ 确认：这是归一化后的数据")
            print(f"   📊 报告的1.419mm是归一化误差，不是真实物理误差")
        else:
            print(f"   ✅ 这是原始尺度的数据")
            print(f"   📊 报告的1.419mm是真实物理误差")
        
        # 计算样本间关键点距离作为参考
        sample1_kp = keypoints[0]
        sample2_kp = keypoints[1] if len(keypoints) > 1 else keypoints[0]
        
        inter_kp_distances = []
        for i in range(len(sample1_kp)):
            for j in range(i+1, len(sample1_kp)):
                dist = np.linalg.norm(sample1_kp[i] - sample1_kp[j])
                inter_kp_distances.append(dist)
        
        avg_inter_kp_dist = np.mean(inter_kp_distances)
        print(f"\n   样本内关键点平均距离: {avg_inter_kp_dist:.3f}")
        
        if avg_inter_kp_dist < 2.0:
            print(f"   💡 关键点间距离过小，确认是归一化数据")
        else:
            print(f"   💡 关键点间距离合理，可能是真实尺度")
        
    except Exception as e:
        print(f"   ❌ 验证失败: {e}")

def main():
    """主函数"""
    
    print("🔍 验证误差尺度")
    print("检查报告的误差是归一化误差还是真实物理误差")
    print("=" * 80)
    
    # 1. 分析数据尺度
    scale_analysis = analyze_data_scales()
    
    # 2. 计算真实世界误差
    scale_factor = calculate_real_world_error()
    
    # 3. 重新计算真实误差
    if scale_factor:
        true_errors = recalculate_true_errors(scale_factor)
    
    # 4. 直接验证
    verify_with_direct_calculation()
    
    print(f"\n🎯 结论:")
    print(f"   ⚠️ 您的质疑完全正确！")
    print(f"   📊 报告的1.4mm误差是归一化后的误差")
    print(f"   📏 真实物理误差约为: 1.4 × 78.99 ≈ 110mm")
    print(f"   ❌ 这个性能实际上很差，不是医学级精度")
    
    print(f"\n💡 问题根源:")
    print(f"   1. 医学级数据集被过度归一化")
    print(f"   2. 误差计算在归一化空间进行")
    print(f"   3. 没有转换回真实物理单位")
    print(f"   4. 导致了误导性的优秀结果")
    
    print(f"\n🔧 修复建议:")
    print(f"   1. 保持原始物理尺度进行训练")
    print(f"   2. 或在归一化空间训练后转换误差到物理单位")
    print(f"   3. 建立正确的误差评估流程")
    print(f"   4. 验证真实的医学应用性能")

if __name__ == "__main__":
    main()
