#!/usr/bin/env python3
"""
增强简单集成PointNet
Enhanced Simple Ensemble PointNet
按优先级实施改进：高级TTA + 改进损失函数 + 优化调度器 + 更长训练
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pathlib import Path
from datetime import datetime
import json
import math

class ImprovedLoss(nn.Module):
    """改进的组合损失函数"""
    
    def __init__(self, mse_weight=0.6, smooth_l1_weight=0.3, cosine_weight=0.1):
        super().__init__()
        self.mse_loss = nn.MSELoss()
        self.smooth_l1_loss = nn.SmoothL1Loss(beta=1.0)
        
        self.mse_weight = mse_weight
        self.smooth_l1_weight = smooth_l1_weight
        self.cosine_weight = cosine_weight
        
    def cosine_similarity_loss(self, pred, target):
        """计算方向一致性损失"""
        # 将关键点展平
        pred_flat = pred.view(pred.size(0), -1)
        target_flat = target.view(target.size(0), -1)
        
        # 计算余弦相似度
        cos_sim = F.cosine_similarity(pred_flat, target_flat, dim=1)
        # 转换为损失 (1 - 相似度)
        cos_loss = 1 - cos_sim.mean()
        
        return cos_loss
    
    def forward(self, pred, target):
        mse = self.mse_loss(pred, target)
        smooth_l1 = self.smooth_l1_loss(pred, target)
        cosine = self.cosine_similarity_loss(pred, target)
        
        total_loss = (self.mse_weight * mse + 
                     self.smooth_l1_weight * smooth_l1 + 
                     self.cosine_weight * cosine)
        
        return total_loss, {
            'mse': mse.item(),
            'smooth_l1': smooth_l1.item(),
            'cosine': cosine.item(),
            'total': total_loss.item()
        }

class EnhancedEnsemblePointNet(nn.Module):
    """增强的简单集成PointNet"""
    
    def __init__(self, num_keypoints=19, num_models=3):
        super().__init__()
        self.num_keypoints = num_keypoints
        self.num_models = num_models
        
        # 创建多样化的子网络
        self.models = nn.ModuleList()
        
        # 模型1: 标准深度，ReLU激活
        model1 = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(), nn.Dropout(0.1),
            nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(), nn.Dropout(0.1),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(), nn.Dropout(0.1),
            nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(), nn.Dropout(0.1),
            nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 模型2: 更宽网络，GELU激活
        model2 = nn.Sequential(
            nn.Conv1d(3, 128, 1), nn.BatchNorm1d(128), nn.GELU(), nn.Dropout(0.15),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.GELU(), nn.Dropout(0.15),
            nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.GELU(), nn.Dropout(0.15),
            nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.GELU()
        )
        
        # 模型3: 更深网络，Swish激活，LayerNorm
        model3 = nn.Sequential(
            nn.Conv1d(3, 32, 1), nn.LayerNorm([32, 4096]), nn.SiLU(), nn.Dropout(0.05),
            nn.Conv1d(32, 64, 1), nn.LayerNorm([64, 4096]), nn.SiLU(), nn.Dropout(0.05),
            nn.Conv1d(64, 128, 1), nn.LayerNorm([128, 4096]), nn.SiLU(), nn.Dropout(0.05),
            nn.Conv1d(128, 256, 1), nn.LayerNorm([256, 4096]), nn.SiLU(), nn.Dropout(0.05),
            nn.Conv1d(256, 512, 1), nn.LayerNorm([512, 4096]), nn.SiLU(), nn.Dropout(0.05),
            nn.Conv1d(512, 1024, 1), nn.LayerNorm([1024, 4096]), nn.SiLU()
        )
        
        self.models.extend([model1, model2, model3])
        
        # 每个模型的回归器
        self.regressors = nn.ModuleList([
            nn.Sequential(
                nn.Linear(1024, 512),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(512, 256),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(256, num_keypoints * 3)
            ) for _ in range(num_models)
        ])
        
        # 可学习的集成权重
        self.ensemble_weights = nn.Parameter(torch.ones(num_models) / num_models)
        
    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        predictions = []
        
        for i, (model, regressor) in enumerate(zip(self.models, self.regressors)):
            # 特征提取
            features = model(x)  # (B, 1024, N)
            global_feat = torch.max(features, dim=2)[0]  # (B, 1024)
            
            # 关键点预测
            pred = regressor(global_feat)  # (B, num_keypoints * 3)
            pred = pred.view(B, self.num_keypoints, 3)
            predictions.append(pred)
        
        # 加权集成
        weights = F.softmax(self.ensemble_weights, dim=0)
        ensemble_pred = sum(w * pred for w, pred in zip(weights, predictions))
        
        return ensemble_pred

class AdvancedTestTimeAugmentation:
    """高级测试时增强"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def generate_augmentations(self, point_cloud, num_augmentations=25):
        """生成多种增强"""
        augmentations = []
        transforms = []
        
        # 原始数据
        augmentations.append(point_cloud.clone())
        transforms.append(torch.eye(3, device=self.device))
        
        for i in range(num_augmentations - 1):
            aug_pc = point_cloud.clone()
            transform_matrix = torch.eye(3, device=self.device)
            
            # 1. 旋转增强 (±0.5度)
            if np.random.random() < 0.8:
                angle = np.random.uniform(-0.009, 0.009)  # ±0.5度
                axis = np.random.choice(['x', 'y', 'z'])
                
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                if axis == 'x':
                    rotation = torch.tensor([[1, 0, 0], [0, cos_a, -sin_a], [0, sin_a, cos_a]], 
                                          dtype=torch.float32, device=self.device)
                elif axis == 'y':
                    rotation = torch.tensor([[cos_a, 0, sin_a], [0, 1, 0], [-sin_a, 0, cos_a]], 
                                          dtype=torch.float32, device=self.device)
                else:  # z轴
                    rotation = torch.tensor([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], 
                                          dtype=torch.float32, device=self.device)
                
                aug_pc = aug_pc @ rotation.T
                transform_matrix = rotation @ transform_matrix
            
            # 2. 噪声增强 (0.02-0.05mm)
            if np.random.random() < 0.6:
                noise_std = np.random.uniform(0.02, 0.05)
                noise = torch.normal(0, noise_std, aug_pc.shape, device=self.device)
                aug_pc = aug_pc + noise
            
            # 3. 缩放增强 (±0.2%)
            if np.random.random() < 0.4:
                scale = np.random.uniform(0.998, 1.002)
                aug_pc = aug_pc * scale
                transform_matrix = transform_matrix * scale
            
            # 4. 平移增强 (±0.1mm)
            if np.random.random() < 0.3:
                translation = torch.normal(0, 0.1, (1, 3), device=self.device)
                aug_pc = aug_pc + translation
            
            augmentations.append(aug_pc)
            transforms.append(transform_matrix)
        
        return augmentations, transforms
    
    def compute_prediction_confidence(self, predictions):
        """计算预测置信度"""
        # 基于预测的一致性计算置信度
        pred_stack = torch.stack(predictions)  # (num_aug, num_kp, 3)

        # 计算标准差作为不确定性度量
        pred_std = torch.std(pred_stack, dim=0)  # (num_kp, 3)
        uncertainty = torch.mean(pred_std)  # scalar

        # 简化为均匀权重
        num_predictions = len(predictions)
        weights = torch.ones(num_predictions) / num_predictions

        return weights
    
    def apply_tta(self, model, point_cloud, num_augmentations=25):
        """应用测试时增强"""
        model.eval()
        
        with torch.no_grad():
            # 生成增强
            augmentations, transforms = self.generate_augmentations(
                point_cloud, num_augmentations
            )
            
            predictions = []
            
            for aug_pc, transform in zip(augmentations, transforms):
                # 预测
                pred = model(aug_pc.unsqueeze(0))  # (1, num_kp, 3)
                
                # 反向变换预测结果
                if not torch.allclose(transform, torch.eye(3, device=self.device)):
                    # 如果有旋转/缩放，需要反向变换
                    try:
                        inv_transform = torch.inverse(transform)
                        pred = pred @ inv_transform.T
                    except:
                        # 如果矩阵不可逆，跳过反向变换
                        pass
                
                predictions.append(pred[0])  # (num_kp, 3)
            
            # 简单平均 (最稳定的方法)
            final_pred = torch.mean(torch.stack(predictions), dim=0)
            
        return final_pred

class EnhancedTrainer:
    """增强训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        self.tta = AdvancedTestTimeAugmentation(device)
        
    def load_aligned_data(self):
        """加载对齐数据"""
        print("📦 加载F3对齐数据...")
        
        aligned_files = list(Path("data/processed").glob("f3_aligned_dataset_*.npz"))
        if not aligned_files:
            raise FileNotFoundError("未找到F3对齐数据集")
        
        latest_file = max(aligned_files, key=lambda x: x.stat().st_mtime)
        data = np.load(str(latest_file), allow_pickle=True)
        
        point_clouds = np.array(data['point_clouds'], dtype=np.float32)
        keypoints = np.array(data['keypoints'], dtype=np.float32)
        
        # 数据划分
        from sklearn.model_selection import train_test_split
        indices = np.arange(len(point_clouds))
        train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
        train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
        
        print(f"✅ 数据加载完成: {point_clouds.shape}")
        print(f"   训练: {len(train_indices)}, 验证: {len(val_indices)}, 测试: {len(test_indices)}")
        
        return self.data
    
    def enhanced_augmentation(self, point_clouds, keypoints):
        """增强数据增强"""
        aug_pcs = []
        aug_kps = []
        
        for pc, kp in zip(point_clouds, keypoints):
            # 原始数据
            aug_pcs.append(pc)
            aug_kps.append(kp)
            
            # 多种旋转增强
            for _ in range(3):
                angle = np.random.uniform(-0.007, 0.007)  # ±0.4度
                axis = np.random.choice(['x', 'y', 'z'])
                
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                if axis == 'x':
                    rotation = np.array([[1, 0, 0], [0, cos_a, -sin_a], [0, sin_a, cos_a]], dtype=np.float32)
                elif axis == 'y':
                    rotation = np.array([[cos_a, 0, sin_a], [0, 1, 0], [-sin_a, 0, cos_a]], dtype=np.float32)
                else:  # z轴
                    rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], dtype=np.float32)
                
                aug_pc = pc @ rotation.T
                aug_kp = kp @ rotation.T
                aug_pcs.append(aug_pc)
                aug_kps.append(aug_kp)
            
            # 多种噪声增强
            for _ in range(2):
                noise_std = np.random.uniform(0.02, 0.08)  # 0.02-0.08mm噪声
                noise_pc = pc + np.random.normal(0, noise_std, pc.shape).astype(np.float32)
                aug_pcs.append(noise_pc)
                aug_kps.append(kp)
            
            # 缩放增强
            scale = np.random.uniform(0.996, 1.004)  # ±0.4%缩放
            scaled_pc = pc * scale
            scaled_kp = kp * scale
            aug_pcs.append(scaled_pc)
            aug_kps.append(scaled_kp)
        
        return aug_pcs, aug_kps
    
    def train_enhanced_ensemble(self, epochs=200, lr=0.0003):
        """训练增强集成模型"""
        print(f"\n🚀 训练增强简单集成PointNet")
        print(f"   改进: 组合损失 + 优化调度器 + 更长训练 + 多样化架构")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        # 创建模型
        model = EnhancedEnsemblePointNet(num_keypoints=19, num_models=3).to(self.device)
        
        # 计算参数
        total_params = sum(p.numel() for p in model.parameters())
        print(f"   模型参数: {total_params:,}")
        
        # 优化器
        optimizer = torch.optim.AdamW(
            model.parameters(), 
            lr=lr, 
            weight_decay=1e-4,
            betas=(0.9, 0.999)
        )
        
        # 余弦退火学习率调度器 + 重启
        scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer, T_0=30, T_mult=2, eta_min=1e-6
        )
        
        # 改进的损失函数
        criterion = ImprovedLoss(mse_weight=0.6, smooth_l1_weight=0.3, cosine_weight=0.1)
        
        # 训练状态
        best_val_error = float('inf')
        best_model_state = None
        patience = 0
        max_patience = 60  # 更长的耐心
        
        train_history = []
        val_history = []
        loss_components_history = []
        
        for epoch in range(epochs):
            # 训练阶段
            model.train()
            epoch_losses = []
            epoch_loss_components = []
            
            # 动态k_shot
            if epoch < 50:
                k_shot = 30
            elif epoch < 100:
                k_shot = 35
            else:
                k_shot = 40
            
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                min(k_shot, len(self.data['train']['point_clouds'])), 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 增强数据增强
            aug_pcs, aug_kps = self.enhanced_augmentation(train_pcs, train_kps)
            
            # 动态批次大小
            if epoch < 50:
                batch_size = 4
            elif epoch < 100:
                batch_size = 6
            else:
                batch_size = 8
            
            for i in range(0, len(aug_pcs), batch_size):
                batch_pcs = torch.FloatTensor(aug_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(aug_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                pred_kps = model(batch_pcs)
                
                # 组合损失
                loss, loss_dict = criterion(pred_kps, batch_kps)
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.step()
                epoch_losses.append(loss.item())
                epoch_loss_components.append(loss_dict)
                
                del batch_pcs, batch_kps, pred_kps, loss
                torch.cuda.empty_cache()
            
            scheduler.step()
            
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            train_history.append(avg_loss)
            
            # 平均损失组件
            if epoch_loss_components:
                avg_components = {}
                for key in epoch_loss_components[0].keys():
                    avg_components[key] = np.mean([comp[key] for comp in epoch_loss_components])
                loss_components_history.append(avg_components)
            
            # 验证
            if epoch % 3 == 0:  # 更频繁验证
                val_error = self.evaluate_model(model, 'val')
                val_history.append(val_error)
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                    patience = 0
                    
                    # 保存最佳模型检查点
                    if val_error <= 6.0:  # 如果接近目标
                        self.save_checkpoint(model, val_error, epoch, "approaching_target")
                else:
                    patience += 1
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f} "
                      f"(MSE:{avg_components.get('mse', 0):.2f}, "
                      f"SL1:{avg_components.get('smooth_l1', 0):.2f}, "
                      f"Cos:{avg_components.get('cosine', 0):.3f}), "
                      f"Val={val_error:.3f}mm, "
                      f"LR={optimizer.param_groups[0]['lr']:.6f}, "
                      f"K={k_shot}, BS={batch_size}, P={patience}")
                
                if patience >= max_patience:
                    print(f"早停在epoch {epoch}")
                    break
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, K={k_shot}, BS={batch_size}")
        
        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)
            print(f"✅ 加载最佳模型 (验证误差: {best_val_error:.3f}mm)")
        
        self.model = model
        self.training_history = {
            'train_losses': train_history,
            'val_errors': val_history,
            'loss_components': loss_components_history,
            'best_val_error': best_val_error
        }
        
        return model, best_val_error
    
    def evaluate_model(self, model, split='test'):
        """评估模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for i in range(0, len(pcs), 2):
                batch_pcs = torch.FloatTensor(pcs[i:i+2]).to(self.device)
                batch_kps = torch.FloatTensor(kps[i:i+2]).to(self.device)
                
                pred_kps = model(batch_pcs)
                
                for j in range(len(batch_pcs)):
                    error = torch.mean(torch.norm(pred_kps[j] - batch_kps[j], dim=1))
                    total_error += error.item()
                    num_samples += 1
                
                del batch_pcs, batch_kps, pred_kps
                torch.cuda.empty_cache()
        
        return total_error / num_samples if num_samples > 0 else float('inf')
    
    def evaluate_with_tta(self, model, split='test', num_augmentations=25):
        """使用TTA评估模型"""
        print(f"\n🔬 使用高级TTA评估 (增强次数: {num_augmentations})...")
        
        model.eval()
        tta_errors = []
        
        pcs = self.data[split]['point_clouds']
        kps = self.data[split]['keypoints']
        
        for i, (pc, kp) in enumerate(zip(pcs, kps)):
            pc_tensor = torch.FloatTensor(pc).to(self.device)
            kp_tensor = torch.FloatTensor(kp).to(self.device)
            
            # 应用TTA
            tta_pred = self.tta.apply_tta(model, pc_tensor, num_augmentations)
            
            error = torch.mean(torch.norm(tta_pred - kp_tensor, dim=1))
            tta_errors.append(error.item())
            
            if i < 3:  # 显示前几个样本的进度
                print(f"   样本 {i+1}: TTA误差 = {error:.3f}mm")
            
            del pc_tensor, kp_tensor, tta_pred
            torch.cuda.empty_cache()
        
        return np.mean(tta_errors)
    
    def save_checkpoint(self, model, val_error, epoch, tag=""):
        """保存检查点"""
        output_dir = Path("trained_models/enhanced_ensemble")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"enhanced_ensemble_{val_error:.3f}mm_epoch{epoch}_{tag}_{timestamp}.pth"
        model_path = output_dir / filename
        
        torch.save({
            'model_state_dict': model.state_dict(),
            'validation_error': val_error,
            'epoch': epoch,
            'timestamp': timestamp,
            'improvements': [
                'ImprovedLoss (MSE+SmoothL1+Cosine)',
                'CosineAnnealingWarmRestarts',
                'DiverseArchitectures',
                'EnhancedAugmentation',
                'DynamicTraining'
            ]
        }, model_path)
        
        print(f"💾 检查点已保存: {model_path}")
        return model_path

def run_enhanced_ensemble_experiment():
    """运行增强集成实验"""
    print("🚀 增强简单集成PointNet实验")
    print("=" * 60)
    print("高优先级改进:")
    print("1. 高级测试时增强 (25次增强)")
    print("2. 改进损失函数 (MSE+SmoothL1+Cosine)")
    print("3. 优化学习率调度 (CosineAnnealingWarmRestarts)")
    print("4. 更长训练 (200 epochs)")
    print("5. 多样化架构 (不同激活函数和归一化)")
    
    trainer = EnhancedTrainer()
    data = trainer.load_aligned_data()
    
    # 训练增强模型
    model, val_error = trainer.train_enhanced_ensemble(epochs=200, lr=0.0003)
    
    # 标准测试
    test_error = trainer.evaluate_model(model, 'test')
    
    # TTA测试
    tta_error = trainer.evaluate_with_tta(model, 'test', num_augmentations=25)
    
    # 结果分析
    baseline_error = 8.13
    simple_ensemble_error = 7.19
    
    print(f"\n📊 增强集成实验结果:")
    print("=" * 50)
    print(f"基线Point Transformer:     {baseline_error:.2f}mm")
    print(f"简单集成PointNet:         {simple_ensemble_error:.2f}mm")
    print(f"增强集成PointNet (验证):   {val_error:.2f}mm")
    print(f"增强集成PointNet (测试):   {test_error:.2f}mm")
    print(f"增强集成 + TTA:           {tta_error:.2f}mm")
    
    # 改进分析
    improvement_vs_baseline = (baseline_error - test_error) / baseline_error * 100
    improvement_vs_simple = (simple_ensemble_error - test_error) / simple_ensemble_error * 100
    tta_improvement = (test_error - tta_error) / test_error * 100
    
    print(f"\n📈 改进分析:")
    print(f"vs 基线改进:              {improvement_vs_baseline:+.1f}%")
    print(f"vs 简单集成改进:          {improvement_vs_simple:+.1f}%")
    print(f"TTA额外改进:              {tta_improvement:+.1f}%")
    
    # 医疗级精度评估
    best_error = min(test_error, tta_error)
    medical_target = 5.0
    
    print(f"\n🎯 医疗级精度评估:")
    print(f"医疗级目标:               {medical_target:.1f}mm")
    print(f"当前最佳结果:             {best_error:.2f}mm")
    
    if best_error <= medical_target:
        print("🎉 成功达到医疗级精度！")
        status = "医疗级精度达成"
    elif best_error <= 6.0:
        remaining = best_error - medical_target
        print(f"🎯 非常接近医疗级！还需改进{remaining:.2f}mm")
        status = f"接近医疗级，还需{remaining:.2f}mm"
    else:
        remaining = best_error - medical_target
        print(f"📈 距离医疗级还需改进{remaining:.2f}mm")
        status = f"需要改进{remaining:.2f}mm"
    
    # 保存最终模型
    final_model_path = trainer.save_checkpoint(model, best_error, 200, "final_enhanced")
    
    return trainer, {
        'val_error': val_error,
        'test_error': test_error,
        'tta_error': tta_error,
        'best_error': best_error,
        'status': status
    }

if __name__ == "__main__":
    trainer, results = run_enhanced_ensemble_experiment()
