# F3模型实验存档

## 📋 实验总结

本存档包含了F3单部件关键点检测模型的完整实验过程，从数据集创建到根因分析。

## 🎯 关键发现

**数据集存在根本性质量问题:**
- F1/F2: 110-126mm STL-CSV对齐偏移 (完全不可用)
- F3: 35-45mm STL-CSV对齐偏移 (相对较好但仍有问题)
- 根因: STL文件与CSV标注的系统性坐标系偏移

**模型性能受限于数据质量:**
- 最佳F3性能: 17.04mm
- 医疗级目标: <10mm
- 结论: 问题在数据质量，不在模型架构

## 📁 目录结构

- `01_Dataset_Creation/`: F3数据集创建脚本和数据
- `02_Model_Training/`: 模型训练脚本和结果
- `03_Optimization_Attempts/`: 模型优化尝试
- `04_Analysis_Results/`: 数据质量分析结果
- `05_Key_Findings/`: 关键发现和总结

## 🎓 学术价值

这个实验虽然没有达到预期的模型性能，但发现了医疗数据集的重要质量问题，
为医疗AI数据集建设提供了宝贵经验和方法论贡献。

## 📝 论文方向

从"高质量医疗关键点数据集"转向"医疗数据集质量评估与挑战分析"，
重点展示数据质量对模型性能的决定性影响。

---
存档时间: 2025-07-16 16:20:39
