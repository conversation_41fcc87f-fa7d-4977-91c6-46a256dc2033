#!/usr/bin/env python3
"""
Keypoint-Focused Sampling Strategy for F3 Keypoint Detection

基于8.543mm随机采样基线，实施关键点附近密集采样策略
核心思想：关键点附近的几何信息对预测最重要
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import time
import json
import gc
import random

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class KeypointFocusedSampler:
    """关键点聚焦采样器 - 在关键点附近密集采样"""
    
    def __init__(self, target_points=4096):
        self.target_points = target_points
        
    def sample_near_keypoints(self, points, keypoints, num_samples, radius_factor=2.0):
        """在关键点附近密集采样"""
        if num_samples <= 0:
            return np.array([], dtype=int)
        
        # 计算每个点到所有关键点的最小距离
        min_distances = []
        for point in points:
            distances_to_kps = [np.linalg.norm(point - kp) for kp in keypoints]
            min_distances.append(min(distances_to_kps))
        
        min_distances = np.array(min_distances)
        
        # 计算自适应半径 - 基于关键点间的平均距离
        kp_distances = []
        for i in range(len(keypoints)):
            for j in range(i+1, len(keypoints)):
                kp_distances.append(np.linalg.norm(keypoints[i] - keypoints[j]))
        
        if kp_distances:
            avg_kp_distance = np.mean(kp_distances)
            sampling_radius = avg_kp_distance * radius_factor
        else:
            sampling_radius = 5.0  # 默认半径
        
        # 距离越近，权重越高（指数衰减）
        weights = np.exp(-min_distances / sampling_radius)
        weights = weights / np.sum(weights)
        
        # 加权采样
        selected_indices = np.random.choice(
            len(points), 
            size=min(num_samples, len(points)), 
            replace=False, 
            p=weights
        )
        
        return selected_indices
    
    def sample_uniform_grid(self, points, num_samples):
        """均匀网格采样 - 保持整体几何结构"""
        if num_samples <= 0:
            return np.array([], dtype=int)
        
        if num_samples >= len(points):
            return np.arange(len(points))
        
        # 简单的均匀间隔采样
        step = max(1, len(points) // num_samples)
        indices = np.arange(0, len(points), step)[:num_samples]
        
        # 如果不够，随机补充
        if len(indices) < num_samples:
            remaining = num_samples - len(indices)
            all_indices = set(range(len(points)))
            used_indices = set(indices)
            available_indices = list(all_indices - used_indices)
            
            if available_indices:
                additional = np.random.choice(
                    available_indices, 
                    size=min(remaining, len(available_indices)), 
                    replace=False
                )
                indices = np.concatenate([indices, additional])
        
        return indices
    
    def keypoint_focused_sample(self, points, keypoints, focus_ratio=0.7):
        """关键点聚焦采样主函数"""
        total_points = len(points)
        
        if total_points <= self.target_points:
            return np.arange(total_points)
        
        # 分配采样数量
        keypoint_samples = int(focus_ratio * self.target_points)  # 70% 关键点附近
        uniform_samples = self.target_points - keypoint_samples    # 30% 均匀分布
        
        print(f"🎯 关键点聚焦采样: 关键点附近{keypoint_samples}, 均匀分布{uniform_samples}")
        
        # 执行关键点聚焦采样
        keypoint_indices = self.sample_near_keypoints(points, keypoints, keypoint_samples)
        
        # 排除已选择的点，进行均匀采样
        remaining_mask = np.ones(total_points, dtype=bool)
        remaining_mask[keypoint_indices] = False
        remaining_points = points[remaining_mask]
        remaining_original_indices = np.where(remaining_mask)[0]
        
        if len(remaining_points) > 0:
            uniform_indices_local = self.sample_uniform_grid(remaining_points, uniform_samples)
            uniform_indices = remaining_original_indices[uniform_indices_local]
        else:
            uniform_indices = np.array([], dtype=int)
        
        # 合并所有采样结果
        all_indices = np.concatenate([keypoint_indices, uniform_indices])
        
        # 确保数量正确
        if len(all_indices) < self.target_points:
            remaining_mask = np.ones(total_points, dtype=bool)
            remaining_mask[all_indices] = False
            remaining_indices = np.where(remaining_mask)[0]
            
            if len(remaining_indices) > 0:
                additional_needed = self.target_points - len(all_indices)
                additional_indices = np.random.choice(
                    remaining_indices, 
                    size=min(additional_needed, len(remaining_indices)), 
                    replace=False
                )
                all_indices = np.concatenate([all_indices, additional_indices])
        
        return all_indices[:self.target_points]

class KeypointFocusedF3Dataset(Dataset):
    """使用关键点聚焦采样的F3数据集"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42,
                 focus_ratio: float = 0.7):
        
        self.num_points = num_points
        self.augment = augment
        self.split = split
        self.focus_ratio = focus_ratio
        
        # 初始化关键点聚焦采样器
        self.sampler = KeypointFocusedSampler(target_points=num_points)
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        print(f"📊 原始数据: {len(sample_ids)} 样本")
        print(f"📊 点云密度: {len(point_clouds[0])} 点 (50K高质量)")
        print(f"🎯 关键点聚焦比例: {focus_ratio*100:.0f}%")
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
        
        print(f"   {split}: {len(self.sample_ids)} 样本")
    
    def __len__(self):
        return len(self.sample_ids)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()  # 50K点云
        keypoints = self.keypoints[idx].copy()
        
        # 关键点聚焦采样到目标点数
        if len(point_cloud) > self.num_points:
            selected_indices = self.sampler.keypoint_focused_sample(
                point_cloud, keypoints, focus_ratio=self.focus_ratio
            )
            point_cloud = point_cloud[selected_indices]
        
        # 保守的数据增强
        if self.augment and self.split == 'train':
            # 轻微旋转
            if np.random.random() < 0.7:
                angle = np.random.uniform(-0.08, 0.08)  # ±4.6度
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                point_cloud = point_cloud @ rotation.T
                keypoints = keypoints @ rotation.T
            
            # 小幅平移
            if np.random.random() < 0.6:
                translation = np.random.uniform(-0.4, 0.4, 3)  # ±0.4mm
                point_cloud += translation
                keypoints += translation
            
            # 轻微缩放
            if np.random.random() < 0.5:
                scale = np.random.uniform(0.99, 1.01, 3)  # ±1%
                point_cloud *= scale
                keypoints *= scale
            
            # 轻微噪声
            if np.random.random() < 0.6:
                noise_level = np.random.choice([0.02, 0.03, 0.04])
                noise = np.random.normal(0, noise_level, point_cloud.shape)
                point_cloud += noise
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

# 使用之前成功的保守PointNet架构
class ConservativePointNet(nn.Module):
    """保守优化PointNet - 保持成功架构不变"""
    
    def __init__(self, num_keypoints: int = 19):
        super(ConservativePointNet, self).__init__()
        
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        x1 = torch.relu(self.bn1(self.conv1(x)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        
        x3_res = x3 + self.residual1(x1)
        
        x4 = torch.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = torch.relu(self.bn5(self.conv5(x4_res)))
        
        global_feat = torch.max(x5, 2)[0]
        
        x = torch.relu(self.bn_fc1(self.fc1(global_feat)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc2(self.fc2(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc3(self.fc3(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc4(self.fc4(x)))
        x = self.dropout(x)
        x = self.fc5(x)
        
        return x.view(batch_size, 19, 3)

class ImprovedLoss(nn.Module):
    """改进损失函数"""
    
    def __init__(self, alpha=0.8, beta=0.2):
        super(ImprovedLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, pred, target):
        mse_loss = F.mse_loss(pred, target)
        smooth_l1_loss = F.smooth_l1_loss(pred, target)
        total_loss = self.alpha * mse_loss + self.beta * smooth_l1_loss
        return total_loss

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def train_keypoint_focused_sampling(focus_ratio=0.7):
    """训练关键点聚焦采样模型"""

    print("🚀 **关键点聚焦采样训练 - F3关键点检测**")
    print("🎯 **策略: 在关键点附近密集采样，充分利用局部几何信息**")
    print(f"📊 **聚焦比例: {focus_ratio*100:.0f}%关键点附近 + {(1-focus_ratio)*100:.0f}%均匀分布**")
    print("📈 **基线: 8.543mm (随机采样) → 目标: <7.631mm (保守基线)**")
    print("=" * 80)

    set_seed(42)

    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")

    if torch.cuda.is_available():
        torch.cuda.empty_cache()

    # 数据集
    dataset_path = "high_quality_f3_dataset.npz"
    test_samples = ['600114', '600115', '600116', '600117', '600118',
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']

    train_dataset = KeypointFocusedF3Dataset(dataset_path, 'train', num_points=4096,
                                           test_samples=test_samples, augment=True,
                                           seed=42, focus_ratio=focus_ratio)
    val_dataset = KeypointFocusedF3Dataset(dataset_path, 'val', num_points=4096,
                                         test_samples=test_samples, augment=False,
                                         seed=42, focus_ratio=focus_ratio)
    test_dataset = KeypointFocusedF3Dataset(dataset_path, 'test', num_points=4096,
                                          test_samples=test_samples, augment=False,
                                          seed=42, focus_ratio=focus_ratio)

    batch_size = 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)

    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")

    # 模型
    model = ConservativePointNet(num_keypoints=19).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 关键点聚焦PointNet参数: {total_params:,}")

    # 训练配置
    criterion = ImprovedLoss(alpha=0.8, beta=0.2)
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)

    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer, max_lr=0.0016, epochs=80, steps_per_epoch=len(train_loader),
        pct_start=0.1, anneal_strategy='cos', div_factor=20, final_div_factor=100
    )

    num_epochs = 80
    best_val_error = float('inf')
    patience = 20
    patience_counter = 0
    history = []

    print(f"🎯 开始关键点聚焦训练")
    start_time = time.time()

    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 40)

        # 训练
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}

        for batch in train_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)

            optimizer.zero_grad()

            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)

                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                scheduler.step()

                train_loss += loss.item()

                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]

            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue

        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)

        # 验证
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}

        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)

                try:
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()

                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]

                except RuntimeError as e:
                    continue

        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)

        # 打印结果
        current_lr = optimizer.param_groups[0]['lr']
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"5mm={train_metrics['within_5mm_percent']:.1f}%, 7mm={train_metrics['within_7mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"5mm={val_metrics['within_5mm_percent']:.1f}%, 7mm={val_metrics['within_7mm_percent']:.1f}%")
        print(f"学习率: {current_lr:.2e}")

        # 保存历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'learning_rate': current_lr
        })

        # 检查改进
        current_error = val_metrics['mean_distance']
        if current_error < best_val_error:
            best_val_error = current_error
            patience_counter = 0

            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics,
                'focus_ratio': focus_ratio
            }, f'best_keypoint_focused_f3_{focus_ratio:.1f}.pth')

            print(f"🎉 新最佳! 验证误差: {best_val_error:.3f}mm")

            if best_val_error <= 5.0:
                print(f"🏆 **突破5mm目标!**")
            elif best_val_error < 7.631:
                print(f"✅ **优于保守基线!** 超越7.631mm")
            elif best_val_error < 8.543:
                print(f"✅ **优于随机基线!** 超越8.543mm")
        else:
            patience_counter += 1
            print(f"⏳ 无改善 ({patience_counter}/{patience})")

        if patience_counter >= patience:
            print("🛑 早停")
            break

        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

    total_time = time.time() - start_time

    return model, test_loader, best_val_error, total_time, history, focus_ratio

def main():
    """主函数 - 测试不同的聚焦比例"""

    print("🚀 **关键点聚焦采样实验 - F3关键点检测**")
    print("🎯 **目标: 验证关键点附近密集采样的效果**")
    print("=" * 80)

    # 测试不同的聚焦比例
    focus_ratios = [0.7]  # 先测试70%聚焦比例

    results = []

    for focus_ratio in focus_ratios:
        print(f"\n{'='*80}")
        print(f"🧪 **测试聚焦比例: {focus_ratio*100:.0f}%**")
        print(f"{'='*80}")

        try:
            # 训练模型
            model, test_loader, best_val_error, training_time, history, ratio = train_keypoint_focused_sampling(focus_ratio)

            print(f"\n🎯 **聚焦比例{ratio*100:.0f}%训练完成!**")
            print(f"   最佳验证误差: {best_val_error:.3f}mm")
            print(f"   训练时间: {training_time/60:.1f}分钟")

            # 测试评估
            device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')

            checkpoint = torch.load(f'best_keypoint_focused_f3_{ratio:.1f}.pth', map_location=device)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval()

            test_metrics = {'mean_distance': 0, 'std_distance': 0,
                           'within_1mm_percent': 0, 'within_3mm_percent': 0,
                           'within_5mm_percent': 0, 'within_7mm_percent': 0}

            num_batches = 0

            with torch.no_grad():
                for batch in test_loader:
                    point_cloud = batch['point_cloud'].to(device)
                    keypoints = batch['keypoints'].to(device)

                    try:
                        pred_keypoints = model(point_cloud)
                        metrics = calculate_metrics(pred_keypoints, keypoints)

                        for key in test_metrics:
                            test_metrics[key] += metrics[key]
                        num_batches += 1

                    except RuntimeError as e:
                        continue

            # 平均测试指标
            for key in test_metrics:
                test_metrics[key] /= num_batches

            print(f"\n📊 **聚焦比例{ratio*100:.0f}%测试结果**")
            print(f"   测试误差: {test_metrics['mean_distance']:.3f}±{test_metrics['std_distance']:.3f}mm")
            print(f"   5mm精度: {test_metrics['within_5mm_percent']:.1f}%")
            print(f"   7mm精度: {test_metrics['within_7mm_percent']:.1f}%")

            # 与基线对比
            random_baseline = 8.543
            conservative_baseline = 7.631

            improvement_vs_random = (random_baseline - test_metrics['mean_distance']) / random_baseline * 100
            improvement_vs_conservative = (conservative_baseline - test_metrics['mean_distance']) / conservative_baseline * 100

            print(f"\n📈 **性能对比**")
            print(f"   vs随机基线(8.543mm): {improvement_vs_random:+.1f}%")
            print(f"   vs保守基线(7.631mm): {improvement_vs_conservative:+.1f}%")

            # 评估效果
            if test_metrics['mean_distance'] <= 5.0:
                print(f"🏆 **突破5mm目标!** 关键点聚焦采样大成功!")
            elif test_metrics['mean_distance'] < conservative_baseline:
                print(f"✅ **优于保守基线!** 关键点聚焦采样有效")
            elif test_metrics['mean_distance'] < random_baseline:
                print(f"✅ **优于随机基线!** 关键点聚焦采样有一定效果")
            else:
                print(f"⚠️ **效果不佳** 需要调整策略")

            # 保存结果
            result = {
                'focus_ratio': ratio,
                'best_val_error': best_val_error,
                'test_metrics': test_metrics,
                'training_time_minutes': training_time / 60,
                'improvement_vs_random_percent': improvement_vs_random,
                'improvement_vs_conservative_percent': improvement_vs_conservative,
                'history': history
            }
            results.append(result)

        except Exception as e:
            print(f"❌ 聚焦比例{focus_ratio*100:.0f}%训练失败: {e}")
            continue

    # 保存所有结果
    final_results = {
        'method': 'Keypoint Focused Sampling',
        'focus_ratios_tested': focus_ratios,
        'results': results,
        'baselines': {
            'random_sampling': 8.543,
            'conservative_baseline': 7.631
        }
    }

    with open('keypoint_focused_sampling_results.json', 'w', encoding='utf-8') as f:
        json.dump(final_results, f, indent=2, ensure_ascii=False)

    print(f"\n💾 **所有结果已保存**: keypoint_focused_sampling_results.json")

    # 总结
    print(f"\n🎉 **关键点聚焦采样实验完成!**")
    if results:
        best_result = min(results, key=lambda x: x['test_metrics']['mean_distance'])
        print(f"🏆 最佳聚焦比例: {best_result['focus_ratio']*100:.0f}%")
        print(f"🎯 最佳测试误差: {best_result['test_metrics']['mean_distance']:.3f}mm")
        print(f"📈 vs随机基线改进: {best_result['improvement_vs_random_percent']:.1f}%")

        if best_result['test_metrics']['mean_distance'] < 7.631:
            print(f"✅ **关键点聚焦采样策略成功!**")
        else:
            print(f"💡 **需要进一步优化采样策略**")

if __name__ == "__main__":
    set_seed(42)
    main()
