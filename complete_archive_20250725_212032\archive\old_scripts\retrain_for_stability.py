#!/usr/bin/env python3
"""
重新训练以提高稳定性
使用改进的训练策略和损失函数
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from heatmap_keypoint_system import HeatmapPointNet
import matplotlib.pyplot as plt

class StabilityFocusedDataset(Dataset):
    """专注于稳定性的数据集"""
    
    def __init__(self, point_clouds, keypoints, sample_ids, augment=True):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.sample_ids = sample_ids
        self.augment = augment
    
    def __len__(self):
        return len(self.point_clouds) * (5 if self.augment else 1)
    
    def __getitem__(self, idx):
        # 原始索引
        original_idx = idx % len(self.point_clouds)
        aug_type = idx // len(self.point_clouds)
        
        point_cloud = self.point_clouds[original_idx].copy()
        keypoints = self.keypoints[original_idx].copy()
        
        # 数据增强
        if self.augment and aug_type > 0:
            point_cloud, keypoints = self._augment_data(point_cloud, keypoints, aug_type)
        
        # 采样点云
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            point_cloud = point_cloud[indices]
        
        # 创建热力图目标
        heatmap_targets = self._create_heatmap_targets(keypoints, point_cloud, sigma=8.0)
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud).transpose(0, 1),  # [3, N]
            'heatmap_targets': torch.FloatTensor(heatmap_targets),  # [12, N]
            'keypoints': torch.FloatTensor(keypoints),  # [12, 3]
            'sample_id': self.sample_ids[original_idx]
        }
    
    def _augment_data(self, pc, kp, aug_type):
        """数据增强"""
        if aug_type == 1:
            # 旋转
            angle = np.random.uniform(-10, 10) * np.pi / 180
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            pc = pc @ rotation_matrix.T
            kp = kp @ rotation_matrix.T
            
        elif aug_type == 2:
            # 缩放
            scale = np.random.uniform(0.95, 1.05)
            pc = pc * scale
            kp = kp * scale
            
        elif aug_type == 3:
            # 平移
            translation = np.random.uniform(-2, 2, 3)
            pc = pc + translation
            kp = kp + translation
            
        elif aug_type == 4:
            # 噪声
            noise = np.random.normal(0, 0.2, pc.shape)
            pc = pc + noise
        
        return pc, kp

    def _create_heatmap_targets(self, keypoints, point_cloud, sigma=8.0):
        """创建热力图目标"""
        num_points = len(point_cloud)
        num_keypoints = len(keypoints)
        heatmaps = np.zeros((num_keypoints, num_points))

        for kp_idx, keypoint in enumerate(keypoints):
            # 计算每个点到关键点的距离
            distances = np.linalg.norm(point_cloud - keypoint, axis=1)
            # 生成高斯分布
            heatmaps[kp_idx] = np.exp(-distances**2 / (2 * sigma**2))

        return heatmaps

class StabilityLoss(nn.Module):
    """专注于稳定性的损失函数"""
    
    def __init__(self, alpha=1.0, beta=0.5, gamma=0.3):
        super().__init__()
        self.alpha = alpha  # 主要损失权重
        self.beta = beta    # 一致性损失权重
        self.gamma = gamma  # 置信度损失权重
        self.mse_loss = nn.MSELoss()
        
    def forward(self, pred_heatmaps, target_heatmaps, pred_keypoints=None, target_keypoints=None):
        # 1. 主要热力图损失
        main_loss = self.mse_loss(pred_heatmaps, target_heatmaps)
        
        # 2. 一致性损失 - 鼓励预测的平滑性
        consistency_loss = 0
        if pred_keypoints is not None and target_keypoints is not None:
            # 计算预测关键点与目标的距离
            distances = torch.norm(pred_keypoints - target_keypoints, dim=-1)
            # 鼓励所有关键点误差相近（减少不稳定性）
            consistency_loss = torch.std(distances)
        
        # 3. 置信度损失 - 鼓励高置信度预测
        confidence_loss = 0
        if pred_heatmaps.max() > 0:
            # 鼓励热力图有明确的峰值
            max_values = torch.max(pred_heatmaps.view(pred_heatmaps.size(0), pred_heatmaps.size(1), -1), dim=-1)[0]
            confidence_loss = -torch.mean(max_values)  # 负号因为我们想最大化置信度
        
        total_loss = (self.alpha * main_loss + 
                     self.beta * consistency_loss + 
                     self.gamma * confidence_loss)
        
        return total_loss, main_loss, consistency_loss, confidence_loss

def train_stable_model(train_loader, val_loader, device, num_epochs=50):
    """训练稳定性增强模型"""
    
    # 创建模型
    model = HeatmapPointNet().to(device)
    
    # 尝试加载预训练权重
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        print("✅ Loaded pretrained weights")
    except:
        print("⚠️ Training from scratch")
    
    # 优化器和调度器
    optimizer = optim.AdamW(model.parameters(), lr=0.0001, weight_decay=0.01)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=10, factor=0.5)
    
    # 损失函数
    criterion = StabilityLoss(alpha=1.0, beta=0.3, gamma=0.2)
    
    # 训练历史
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    
    print(f"🚀 Starting stability-focused training for {num_epochs} epochs")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0
        train_main_loss = 0
        train_consistency_loss = 0
        train_confidence_loss = 0
        
        for batch_idx, batch in enumerate(train_loader):
            point_clouds = batch['point_cloud'].to(device)  # [B, 3, N]
            heatmap_targets = batch['heatmap_targets'].to(device)  # [B, 12, N]
            keypoint_targets = batch['keypoints'].to(device)  # [B, 12, 3]
            
            optimizer.zero_grad()
            
            # 前向传播
            pred_heatmaps = model(point_clouds)  # [B, 12, N]
            
            # 从热力图提取关键点（简化版）
            pred_keypoints = []
            for b in range(point_clouds.size(0)):
                pc_np = point_clouds[b].transpose(0, 1).cpu().numpy()  # [N, 3]
                heatmap_np = pred_heatmaps[b].detach().cpu().numpy()  # [12, N]
                
                batch_keypoints = []
                for kp_idx in range(12):
                    max_idx = np.argmax(heatmap_np[kp_idx])
                    batch_keypoints.append(pc_np[max_idx])
                
                pred_keypoints.append(batch_keypoints)
            
            pred_keypoints = torch.FloatTensor(pred_keypoints).to(device)  # [B, 12, 3]
            
            # 计算损失
            total_loss, main_loss, consistency_loss, confidence_loss = criterion(
                pred_heatmaps, heatmap_targets, pred_keypoints, keypoint_targets
            )
            
            # 反向传播
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += total_loss.item()
            train_main_loss += main_loss.item()
            train_consistency_loss += consistency_loss.item()
            train_confidence_loss += confidence_loss.item()
        
        # 验证阶段
        model.eval()
        val_loss = 0
        with torch.no_grad():
            for batch in val_loader:
                point_clouds = batch['point_cloud'].to(device)
                heatmap_targets = batch['heatmap_targets'].to(device)
                keypoint_targets = batch['keypoints'].to(device)
                
                pred_heatmaps = model(point_clouds)
                
                # 简化的关键点提取
                pred_keypoints = []
                for b in range(point_clouds.size(0)):
                    pc_np = point_clouds[b].transpose(0, 1).cpu().numpy()
                    heatmap_np = pred_heatmaps[b].cpu().numpy()
                    
                    batch_keypoints = []
                    for kp_idx in range(12):
                        max_idx = np.argmax(heatmap_np[kp_idx])
                        batch_keypoints.append(pc_np[max_idx])
                    
                    pred_keypoints.append(batch_keypoints)
                
                pred_keypoints = torch.FloatTensor(pred_keypoints).to(device)
                
                total_loss, _, _, _ = criterion(
                    pred_heatmaps, heatmap_targets, pred_keypoints, keypoint_targets
                )
                val_loss += total_loss.item()
        
        # 计算平均损失
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            torch.save(model.state_dict(), 'best_stable_model.pth')
            print(f"✅ Saved best model at epoch {epoch+1}")
        
        # 打印进度
        if (epoch + 1) % 5 == 0:
            print(f"Epoch {epoch+1}/{num_epochs}:")
            print(f"  Train Loss: {avg_train_loss:.4f}")
            print(f"  Val Loss: {avg_val_loss:.4f}")
            print(f"  Main: {train_main_loss/len(train_loader):.4f}")
            print(f"  Consistency: {train_consistency_loss/len(train_loader):.4f}")
            print(f"  Confidence: {train_confidence_loss/len(train_loader):.4f}")
            print(f"  LR: {optimizer.param_groups[0]['lr']:.6f}")
    
    return model, train_losses, val_losses

def main():
    """主函数"""
    print("🔧 Retraining for Improved Stability")
    print("Using enhanced training strategies and loss functions")
    print("=" * 60)
    
    # 加载数据
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    print(f"📊 Dataset: {len(sample_ids)} samples")
    
    # 数据分割
    n_samples = len(sample_ids)
    train_size = int(0.7 * n_samples)
    val_size = int(0.15 * n_samples)
    
    train_indices = list(range(train_size))
    val_indices = list(range(train_size, train_size + val_size))
    
    # 创建数据集
    train_dataset = StabilityFocusedDataset(
        point_clouds[train_indices], keypoints[train_indices], 
        sample_ids[train_indices], augment=True
    )
    
    val_dataset = StabilityFocusedDataset(
        point_clouds[val_indices], keypoints[val_indices], 
        sample_ids[val_indices], augment=False
    )
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False, num_workers=2)
    
    print(f"📊 Training samples: {len(train_dataset)} (with augmentation)")
    print(f"📊 Validation samples: {len(val_dataset)}")
    
    # 训练模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 Using device: {device}")
    
    model, train_losses, val_losses = train_stable_model(
        train_loader, val_loader, device, num_epochs=30
    )
    
    # 绘制训练曲线
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='Train Loss', alpha=0.8)
    plt.plot(val_losses, label='Validation Loss', alpha=0.8)
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Stability-Focused Training Progress')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('stability_training_progress.png', dpi=300, bbox_inches='tight')
    print("📊 Training progress saved: stability_training_progress.png")
    plt.close()
    
    print(f"\n🎉 Stability-focused training completed!")
    print("✅ Best model saved as: best_stable_model.pth")
    print("💡 Next steps:")
    print("   1. Test the new model with enhanced_stable_predictor.py")
    print("   2. Compare performance with original model")
    print("   3. Fine-tune hyperparameters if needed")

if __name__ == "__main__":
    main()
