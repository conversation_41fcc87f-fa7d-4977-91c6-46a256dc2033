#!/usr/bin/env python3
"""
Create High Density Dataset for Medical Keypoint Detection

Test 50,000 point sampling vs 4,096 point sampling to evaluate impact on quality.
"""

import numpy as np
import pandas as pd
from pathlib import Path
import json
import struct
import gc
import os

def load_annotation_file_robust(csv_path: str):
    """Robustly load annotation CSV file handling various format issues"""
    
    # Try different encodings
    encodings = ['gbk', 'utf-8', 'latin-1', 'cp1252']
    
    first_line = None
    working_encoding = None
    
    # Find working encoding
    for encoding in encodings:
        try:
            with open(csv_path, 'r', encoding=encoding) as f:
                first_line = f.readline().strip()
                working_encoding = encoding
                break
        except UnicodeDecodeError:
            continue
    
    if first_line is None:
        print(f"   ❌ 无法读取文件，所有编码都失败")
        return None, None
    
    has_header = 'label' in first_line.lower() and ('x' in first_line.lower() or 'X' in first_line)
    
    try:
        if has_header:
            df = pd.read_csv(csv_path, encoding=working_encoding)
        else:
            df = pd.read_csv(csv_path, encoding=working_encoding, header=None)
            
            if df.shape[1] >= 9:
                df.columns = ['label', 'X', 'Y', 'Z', 'defined', 'selected', 'visible', 'locked', 'description']
            elif df.shape[1] >= 4:
                df.columns = ['label', 'X', 'Y', 'Z'] + [f'col_{i}' for i in range(4, df.shape[1])]
            else:
                print(f"   ❌ 列数不足: {df.shape[1]}")
                return None, None
    
    except Exception as e:
        print(f"   ❌ 文件读取失败: {e}")
        return None, None
    
    if 'X' not in df.columns or 'Y' not in df.columns or 'Z' not in df.columns:
        print(f"   ❌ 缺少坐标列")
        return None, None
    
    if 'label' not in df.columns:
        print(f"   ❌ 缺少标签列")
        return None, None
    
    keypoints = df[['X', 'Y', 'Z']].values
    labels = df['label'].values.tolist()
    
    if len(keypoints) == 0:
        print(f"   ❌ 没有关键点数据")
        return None, None
    
    if np.any(np.isnan(keypoints)) or np.any(np.isinf(keypoints)):
        print(f"   ❌ 坐标数据包含无效值")
        return None, None
    
    return keypoints, labels

def read_stl_binary_complete_high_quality(stl_path: str):
    """Read complete STL file with highest quality"""
    try:
        with open(stl_path, 'rb') as f:
            f.read(80)  # Skip header
            num_triangles = struct.unpack('<I', f.read(4))[0]
            
            vertices = []
            for i in range(num_triangles):
                f.read(12)  # Skip normal
                for j in range(3):
                    x, y, z = struct.unpack('<fff', f.read(12))
                    vertices.append([x, y, z])
                f.read(2)  # Skip attribute
            
            vertices = np.array(vertices)
            unique_vertices = np.unique(vertices, axis=0)
            del vertices
            gc.collect()
            
            return unique_vertices
            
    except Exception as e:
        print(f"   ❌ STL读取失败: {e}")
        return None

def separate_keypoints_by_region(keypoints, labels):
    """Separate keypoints by F1/F2/F3 regions based on labels"""
    
    f1_keypoints = []
    f2_keypoints = []
    f3_keypoints = []
    
    for i, label in enumerate(labels):
        if isinstance(label, str):
            label_upper = label.upper()
            if 'F_1' in label_upper or 'F1' in label_upper:
                f1_keypoints.append(keypoints[i])
            elif 'F_2' in label_upper or 'F2' in label_upper:
                f2_keypoints.append(keypoints[i])
            elif 'F_3' in label_upper or 'F3' in label_upper:
                f3_keypoints.append(keypoints[i])
    
    return {
        'F1': np.array(f1_keypoints) if f1_keypoints else np.array([]).reshape(0, 3),
        'F2': np.array(f2_keypoints) if f2_keypoints else np.array([]).reshape(0, 3),
        'F3': np.array(f3_keypoints) if f3_keypoints else np.array([]).reshape(0, 3)
    }

def process_sample_with_density_comparison(sample_id, target_region='F3'):
    """Process sample with both 4K and 50K point sampling for comparison"""
    
    print(f"🔧 处理样本 {sample_id} ({target_region}) - 密度对比测试")
    
    data_dir = "/home/<USER>/pjc/GCN/Data"
    data_path = Path(data_dir)
    annotations_dir = data_path / "annotations"
    stl_dir = data_path / "stl_models"
    
    # Load annotation
    csv_file = annotations_dir / f"{sample_id}-Table-XYZ.CSV"
    
    if not csv_file.exists():
        print(f"   ❌ 标注文件不存在")
        return None
    
    keypoints, labels = load_annotation_file_robust(str(csv_file))
    
    if keypoints is None:
        return None
    
    print(f"   ✅ 加载了 {len(keypoints)} 个关键点")
    
    # Separate keypoints
    regions = separate_keypoints_by_region(keypoints, labels)
    
    if len(regions[target_region]) == 0:
        print(f"   ❌ {target_region}关键点为空")
        return None
    
    # Load STL
    stl_file = stl_dir / f"{sample_id}-F_{target_region[-1]}.stl"
    
    if not stl_file.exists():
        print(f"   ❌ {target_region} STL文件不存在")
        return None
    
    print(f"   📖 读取完整{target_region} STL文件...")
    vertices = read_stl_binary_complete_high_quality(str(stl_file))
    
    if vertices is None:
        return None
    
    print(f"   ✅ {target_region} STL: {len(vertices)} 个唯一顶点")
    
    region_keypoints = regions[target_region]
    
    # Test different sampling densities
    sampling_tests = [
        {'name': '4K点', 'points': 4096},
        {'name': '50K点', 'points': 50000}
    ]
    
    results = {}
    
    for test in sampling_tests:
        print(f"   🎯 测试 {test['name']} 采样...")
        
        target_points = test['points']
        
        # Sample point cloud
        if len(vertices) > target_points:
            indices = np.random.choice(len(vertices), target_points, replace=False)
            sampled_vertices = vertices[indices].copy()
        else:
            # If not enough vertices, duplicate
            indices = np.random.choice(len(vertices), target_points, replace=True)
            sampled_vertices = vertices[indices].copy()
        
        # Calculate alignment quality with sampled point cloud
        distances = []
        for kp in region_keypoints:
            dists = np.linalg.norm(sampled_vertices - kp, axis=1)
            min_dist = np.min(dists)
            distances.append(min_dist)
        
        distances = np.array(distances)
        mean_dist = np.mean(distances)
        within_1mm = np.sum(distances <= 1.0) / len(distances) * 100
        within_5mm = np.sum(distances <= 5.0) / len(distances) * 100
        
        # Calculate surface coverage (average nearest neighbor distance)
        if len(sampled_vertices) > 1000:  # Sample for efficiency
            sample_indices = np.random.choice(len(sampled_vertices), 1000, replace=False)
            sample_points = sampled_vertices[sample_indices]
        else:
            sample_points = sampled_vertices
        
        nn_distances = []
        for point in sample_points:
            dists = np.linalg.norm(sampled_vertices - point, axis=1)
            dists = dists[dists > 0]  # Remove self-distance
            if len(dists) > 0:
                nn_distances.append(np.min(dists))
        
        avg_nn_dist = np.mean(nn_distances) if nn_distances else 0
        
        results[test['name']] = {
            'target_points': target_points,
            'actual_points': len(sampled_vertices),
            'sampling_ratio': len(sampled_vertices) / len(vertices) * 100,
            'mean_distance': float(mean_dist),
            'within_1mm_percent': float(within_1mm),
            'within_5mm_percent': float(within_5mm),
            'avg_nn_distance': float(avg_nn_dist)
        }
        
        print(f"      采样比例: {len(sampled_vertices) / len(vertices) * 100:.1f}%")
        print(f"      平均距离: {mean_dist:.3f}mm")
        print(f"      ≤1mm: {within_1mm:.1f}%")
        print(f"      ≤5mm: {within_5mm:.1f}%")
        print(f"      平均最近邻距离: {avg_nn_dist:.3f}mm")
        
        del sampled_vertices, distances
        gc.collect()
    
    # Create final result
    result = {
        'sample_id': sample_id,
        'stl_vertices_count': len(vertices),
        'keypoints_count': len(region_keypoints),
        'density_comparison': results
    }
    
    del vertices
    gc.collect()
    
    print(f"   ✅ 密度对比测试完成")
    return result

def test_sampling_density_impact():
    """Test impact of sampling density on a subset of samples"""
    
    print("🧪 **测试点云采样密度对数据质量的影响**")
    print("🎯 **对比 4K点 vs 50K点 采样效果**")
    print("=" * 80)
    
    # Test on first 10 samples for efficiency
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    annotations_dir = data_dir / "annotations"
    
    xyz_files = list(annotations_dir.glob("*-Table-XYZ.CSV"))
    excluded_samples = {'600025', '600026', '600027'}
    
    valid_sample_ids = []
    for csv_file in xyz_files:
        sample_id = csv_file.stem.split('-')[0]
        if sample_id not in excluded_samples:
            valid_sample_ids.append(sample_id)
    
    # Test first 10 samples
    test_samples = valid_sample_ids[:10]
    
    print(f"📂 测试样本: {test_samples}")
    
    all_results = []
    
    for i, sample_id in enumerate(test_samples):
        print(f"\n📦 测试样本 {i+1}/{len(test_samples)}: {sample_id}")
        
        result = process_sample_with_density_comparison(sample_id, 'F3')
        
        if result is not None:
            all_results.append(result)
        
        gc.collect()
    
    # Analyze results
    print(f"\n📊 **密度对比分析结果**")
    print(f"   成功测试样本: {len(all_results)}")
    
    if all_results:
        # Calculate statistics for each density
        densities = ['4K点', '50K点']
        
        for density in densities:
            distances = [r['density_comparison'][density]['mean_distance'] for r in all_results]
            within_1mm = [r['density_comparison'][density]['within_1mm_percent'] for r in all_results]
            within_5mm = [r['density_comparison'][density]['within_5mm_percent'] for r in all_results]
            nn_distances = [r['density_comparison'][density]['avg_nn_distance'] for r in all_results]
            sampling_ratios = [r['density_comparison'][density]['sampling_ratio'] for r in all_results]
            
            print(f"\n   📈 {density} 统计:")
            print(f"      平均采样比例: {np.mean(sampling_ratios):.1f}%")
            print(f"      平均对齐距离: {np.mean(distances):.3f}±{np.std(distances):.3f}mm")
            print(f"      平均1mm精度: {np.mean(within_1mm):.1f}%")
            print(f"      平均5mm精度: {np.mean(within_5mm):.1f}%")
            print(f"      平均最近邻距离: {np.mean(nn_distances):.3f}mm")
        
        # Compare improvements
        dist_4k = [r['density_comparison']['4K点']['mean_distance'] for r in all_results]
        dist_50k = [r['density_comparison']['50K点']['mean_distance'] for r in all_results]
        
        improvements = [(d4k - d50k) / d4k * 100 for d4k, d50k in zip(dist_4k, dist_50k)]
        
        print(f"\n   🎯 **50K点相比4K点的改善:**")
        print(f"      平均精度提升: {np.mean(improvements):.1f}%")
        print(f"      改善范围: {np.min(improvements):.1f}% 到 {np.max(improvements):.1f}%")
        
        # Save results
        summary = {
            'test_name': 'Sampling_Density_Impact_Analysis',
            'test_date': str(pd.Timestamp.now()),
            'test_samples': len(all_results),
            'density_comparison': {
                '4K_points': {
                    'avg_distance': float(np.mean(dist_4k)),
                    'std_distance': float(np.std(dist_4k)),
                    'avg_1mm_percent': float(np.mean([r['density_comparison']['4K点']['within_1mm_percent'] for r in all_results])),
                    'avg_5mm_percent': float(np.mean([r['density_comparison']['4K点']['within_5mm_percent'] for r in all_results]))
                },
                '50K_points': {
                    'avg_distance': float(np.mean(dist_50k)),
                    'std_distance': float(np.std(dist_50k)),
                    'avg_1mm_percent': float(np.mean([r['density_comparison']['50K点']['within_1mm_percent'] for r in all_results])),
                    'avg_5mm_percent': float(np.mean([r['density_comparison']['50K点']['within_5mm_percent'] for r in all_results]))
                }
            },
            'improvement_analysis': {
                'avg_improvement_percent': float(np.mean(improvements)),
                'min_improvement_percent': float(np.min(improvements)),
                'max_improvement_percent': float(np.max(improvements))
            },
            'detailed_results': all_results
        }
        
        with open('sampling_density_analysis.json', 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\n   ✅ 分析结果已保存: sampling_density_analysis.json")
        
        return summary
    
    return None

if __name__ == "__main__":
    # Test sampling density impact
    result = test_sampling_density_impact()
    
    if result:
        print(f"\n🎉 **采样密度分析完成!**")
        print(f"📊 结果: 50K点采样平均提升 {result['improvement_analysis']['avg_improvement_percent']:.1f}% 精度")
        print(f"💡 建议: 基于结果决定是否使用50K点采样创建完整数据集")
    else:
        print(f"\n❌ **采样密度分析失败**")
