#!/usr/bin/env python3
"""
修复的归一化57点模型
Fixed normalized 57-point model
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import json
from tqdm import tqdm

def normalize_data(point_clouds, keypoints_57):
    """数据归一化 - 解决尺度问题"""
    
    print("🔧 执行数据归一化...")
    
    normalized_pc = []
    normalized_kp = []
    scalers = []
    
    for i in range(len(point_clouds)):
        pc = point_clouds[i].copy()
        kp = keypoints_57[i].copy()
        
        # 合并点云和关键点进行统一归一化
        combined_data = np.vstack([pc, kp])
        
        # 使用StandardScaler归一化
        scaler = StandardScaler()
        combined_normalized = scaler.fit_transform(combined_data)
        
        # 分离归一化后的数据
        pc_normalized = combined_normalized[:len(pc)]
        kp_normalized = combined_normalized[len(pc):]
        
        normalized_pc.append(pc_normalized)
        normalized_kp.append(kp_normalized)
        scalers.append(scaler)
        
        if i == 0:
            print(f"   样本 {i} 归一化:")
            print(f"     原始点云范围: {np.ptp(pc, axis=0)}")
            print(f"     归一化后范围: {np.ptp(pc_normalized, axis=0)}")
            print(f"     原始关键点标准差: {np.std(kp, axis=0)}")
            print(f"     归一化后标准差: {np.std(kp_normalized, axis=0)}")
    
    normalized_pc = np.array(normalized_pc)
    normalized_kp = np.array(normalized_kp)
    
    print(f"✅ 数据归一化完成:")
    print(f"   归一化后点云: {normalized_pc.shape}")
    print(f"   归一化后关键点: {normalized_kp.shape}")
    
    return normalized_pc, normalized_kp, scalers

class AdaptivePointNet57(nn.Module):
    """基于成功12点架构的57点模型"""
    
    def __init__(self, num_keypoints=57, dropout_rate=0.3):
        super(AdaptivePointNet57, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 残差连接
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        # 回归头
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        # 批归一化
        self.fc_bn1 = nn.BatchNorm1d(512)
        self.fc_bn2 = nn.BatchNorm1d(256)
        self.fc_bn3 = nn.BatchNorm1d(128)
        self.fc_bn4 = nn.BatchNorm1d(64)
        
        # Dropout
        self.dropout = nn.Dropout(dropout_rate)
        
        # 权重初始化
        self._initialize_weights()
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"🏗️ AdaptivePointNet57: {total_params:,} 参数")
    
    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取 + 残差连接
        x1 = F.relu(self.bn1(self.conv1(x)))
        x2 = F.relu(self.bn2(self.conv2(x1)))
        x3 = F.relu(self.bn3(self.conv3(x2)))
        x3_res = x3 + self.residual1(x1)
        
        x4 = F.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = F.relu(self.bn5(self.conv5(x4_res)))
        
        # 全局最大池化
        global_feat = torch.max(x5, 2)[0]
        
        # 回归
        x = F.relu(self.fc_bn1(self.fc1(global_feat)))
        x = self.dropout(x)
        x = F.relu(self.fc_bn2(self.fc2(x)))
        x = self.dropout(x)
        x = F.relu(self.fc_bn3(self.fc3(x)))
        x = self.dropout(x)
        x = F.relu(self.fc_bn4(self.fc4(x)))
        x = self.dropout(x)
        
        keypoints = self.fc5(x)
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

class Dataset57(Dataset):
    def __init__(self, point_clouds, keypoints):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def train_normalized_model(model, train_loader, val_loader, epochs=100, device='cuda'):
    """训练归一化模型"""
    
    print(f"🚀 训练归一化57点模型...")
    
    model = model.to(device)
    
    # 更合理的学习率
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.8, patience=10, min_lr=1e-6
    )
    
    criterion = nn.MSELoss()
    
    history = {'train_loss': [], 'val_loss': [], 'train_error': [], 'val_error': []}
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 20
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            train_loss += loss.item()
            
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                val_loss += loss.item()
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_error'].append(train_error)
        history['val_error'].append(val_error)
        
        scheduler.step(val_loss)
        
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_normalized_57_model.pth')
        else:
            patience_counter += 1
        
        current_lr = optimizer.param_groups[0]['lr']
        print(f"Epoch {epoch+1:3d}: "
              f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
              f"Train Error: {train_error:.4f}, Val Error: {val_error:.4f}, "
              f"LR: {current_lr:.2e}")
        
        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    model.load_state_dict(torch.load('best_normalized_57_model.pth'))
    return history

def denormalize_predictions(predictions, scalers):
    """反归一化预测结果"""
    denormalized = []
    
    for i, (pred, scaler) in enumerate(zip(predictions, scalers)):
        # 创建虚拟点云用于反归一化
        dummy_pc = np.zeros((50000, 3))
        combined = np.vstack([dummy_pc, pred])
        
        # 反归一化
        combined_denorm = scaler.inverse_transform(combined)
        pred_denorm = combined_denorm[50000:]
        
        denormalized.append(pred_denorm)
    
    return np.array(denormalized)

def main():
    print("🎯 修复的归一化57点模型")
    print("解决数据尺度问题")
    print("=" * 80)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载数据
    data = np.load('unified_pelvis_57_dataset.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints_57 = data['keypoints_57']
    sample_ids = data['sample_ids']
    
    print(f"📊 原始数据: {len(sample_ids)} 个样本")
    
    # 数据归一化
    normalized_pc, normalized_kp, scalers = normalize_data(point_clouds, keypoints_57)
    
    # 数据划分
    indices = np.arange(len(normalized_pc))
    train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
    train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
    
    # 创建数据集
    train_dataset = Dataset57(normalized_pc[train_indices], normalized_kp[train_indices])
    val_dataset = Dataset57(normalized_pc[val_indices], normalized_kp[val_indices])
    test_dataset = Dataset57(normalized_pc[test_indices], normalized_kp[test_indices])
    
    # 数据加载器
    batch_size = 8
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    
    print(f"📋 数据划分: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    
    # 创建和训练模型
    model = AdaptivePointNet57(num_keypoints=57, dropout_rate=0.3)
    history = train_normalized_model(model, train_loader, val_loader, epochs=100, device=device)
    
    # 测试模型
    print(f"\n🔍 测试模型性能...")
    model.eval()
    test_predictions = []
    test_targets = []
    test_indices_list = []

    with torch.no_grad():
        for i, (batch_pc, batch_kp) in enumerate(test_loader):
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)

            predicted = model(batch_pc)

            test_predictions.append(predicted.cpu().numpy())
            test_targets.append(batch_kp.cpu().numpy())

            # 记录对应的原始索引
            start_idx = i * batch_size
            end_idx = min(start_idx + batch_size, len(test_indices))
            test_indices_list.extend(test_indices[start_idx:end_idx])

    # 合并预测结果
    test_predictions = np.vstack(test_predictions)
    test_targets = np.vstack(test_targets)

    # 反归一化到真实尺度
    print(f"🔄 反归一化预测结果...")

    real_predictions = []
    real_targets = []

    for i, orig_idx in enumerate(test_indices_list):
        if i < len(test_predictions):
            # 反归一化预测
            pred_norm = test_predictions[i]
            target_norm = test_targets[i]

            # 使用对应的scaler
            scaler = scalers[orig_idx]

            # 创建虚拟数据进行反归一化
            dummy_pc = np.zeros((50000, 3))

            # 预测结果反归一化
            combined_pred = np.vstack([dummy_pc, pred_norm])
            combined_pred_denorm = scaler.inverse_transform(combined_pred)
            pred_real = combined_pred_denorm[50000:]

            # 目标结果反归一化
            combined_target = np.vstack([dummy_pc, target_norm])
            combined_target_denorm = scaler.inverse_transform(combined_target)
            target_real = combined_target_denorm[50000:]

            real_predictions.append(pred_real)
            real_targets.append(target_real)

    real_predictions = np.array(real_predictions)
    real_targets = np.array(real_targets)

    # 计算真实误差
    print(f"📊 计算真实误差...")

    total_error = 0.0
    region_errors = {'F1': [], 'F2': [], 'F3': []}

    for i in range(len(real_predictions)):
        pred = real_predictions[i]
        target = real_targets[i]

        # 计算每个关键点的误差
        distances = np.linalg.norm(pred - target, axis=1)
        total_error += np.mean(distances)

        # 分区域计算
        f1_distances = distances[0:19]
        f2_distances = distances[19:38]
        f3_distances = distances[38:57]

        region_errors['F1'].extend(f1_distances)
        region_errors['F2'].extend(f2_distances)
        region_errors['F3'].extend(f3_distances)

    avg_error = total_error / len(real_predictions)

    print(f"\n🎯 最终真实误差结果:")
    print(f"   整体平均误差: {avg_error:.2f}mm")

    for region, errors in region_errors.items():
        if errors:
            mean_error = np.mean(errors)
            std_error = np.std(errors)
            max_error = np.max(errors)
            print(f"   {region}区域: {mean_error:.2f}±{std_error:.2f}mm (最大: {max_error:.2f}mm)")

    # 计算医疗级准确率
    all_errors = []
    for errors in region_errors.values():
        all_errors.extend(errors)

    if all_errors:
        accuracy_5mm = np.mean(np.array(all_errors) < 5.0) * 100
        accuracy_10mm = np.mean(np.array(all_errors) < 10.0) * 100

        print(f"   医疗级准确率:")
        print(f"     <5mm: {accuracy_5mm:.1f}%")
        print(f"     <10mm: {accuracy_10mm:.1f}%")

    print(f"\n📊 性能对比:")
    print(f"   12点模型: 6.208mm")
    print(f"   简单57点模型: 15.50mm")
    print(f"   归一化57点模型: {avg_error:.2f}mm")

    if avg_error < 10.0:
        print(f"✅ 归一化57点模型性能优秀！")
        print(f"💡 数据归一化是关键因素")
    elif avg_error < 15.0:
        print(f"✅ 归一化57点模型有显著提升！")
    else:
        print(f"⚠️ 仍需进一步优化")

    print(f"\n🎉 训练和测试完成！")

if __name__ == "__main__":
    main()
