#!/usr/bin/env python3
"""
高级小样本学习救援方法集合 - 拯救小数据集
Advanced Few-Shot Learning Rescue Methods - Save Small Datasets
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import random
from pathlib import Path
import json
from datetime import datetime
from sklearn.model_selection import train_test_split

class MatchingNetworks(nn.Module):
    """匹配网络 - 基于注意力的小样本学习"""
    
    def __init__(self, feature_dim=256, hidden_dim=128):
        super().__init__()
        
        # 特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Linear(3, 64),
            nn.ReLU(),
            nn.Linear(64, 128),
            nn.<PERSON>LU(),
            nn.Linear(128, feature_dim),
            nn.ReLU()
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(feature_dim, num_heads=8, batch_first=True)
        
        # 关键点回归器
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(feature_dim, 512),
            nn.<PERSON><PERSON>(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.<PERSON>(),
            nn.Linear(256, 19 * 3)
        )
        
    def forward(self, support_set, query_set):
        """
        Args:
            support_set: (B_s, N, 3) 支持集点云
            query_set: (B_q, N, 3) 查询集点云
        """
        # 提取特征
        support_features = []
        for pc in support_set:
            pc_flat = pc.view(-1, 3)
            features = self.feature_extractor(pc_flat)
            global_feature = torch.max(features, dim=0)[0]
            support_features.append(global_feature)
        
        support_features = torch.stack(support_features)  # (B_s, feature_dim)
        
        query_features = []
        for pc in query_set:
            pc_flat = pc.view(-1, 3)
            features = self.feature_extractor(pc_flat)
            global_feature = torch.max(features, dim=0)[0]
            query_features.append(global_feature)
        
        query_features = torch.stack(query_features)  # (B_q, feature_dim)
        
        # 注意力匹配
        attended_features, _ = self.attention(
            query_features.unsqueeze(1), 
            support_features.unsqueeze(0).repeat(len(query_features), 1, 1),
            support_features.unsqueeze(0).repeat(len(query_features), 1, 1)
        )
        
        attended_features = attended_features.squeeze(1)
        
        # 预测关键点
        keypoints = self.keypoint_regressor(attended_features)
        return keypoints.view(-1, 19, 3)

class RelationNetwork(nn.Module):
    """关系网络 - 学习相似性度量"""
    
    def __init__(self, feature_dim=256):
        super().__init__()
        
        # 特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Linear(3, 64),
            nn.ReLU(),
            nn.Linear(64, 128),
            nn.ReLU(),
            nn.Linear(128, feature_dim),
            nn.ReLU()
        )
        
        # 关系模块
        self.relation_module = nn.Sequential(
            nn.Linear(feature_dim * 2, 512),
            nn.ReLU(),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, 1)
        )
        
        # 关键点回归器
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(feature_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, 19 * 3)
        )
        
    def forward(self, support_set, support_labels, query_set):
        # 提取支持集特征
        support_features = []
        for pc in support_set:
            pc_flat = pc.view(-1, 3)
            features = self.feature_extractor(pc_flat)
            global_feature = torch.max(features, dim=0)[0]
            support_features.append(global_feature)
        
        support_features = torch.stack(support_features)
        
        # 提取查询集特征
        query_features = []
        for pc in query_set:
            pc_flat = pc.view(-1, 3)
            features = self.feature_extractor(pc_flat)
            global_feature = torch.max(features, dim=0)[0]
            query_features.append(global_feature)
        
        query_features = torch.stack(query_features)
        
        # 计算关系分数并加权
        weighted_features = []
        for query_feat in query_features:
            relations = []
            for support_feat in support_features:
                combined = torch.cat([query_feat, support_feat], dim=0)
                relation_score = self.relation_module(combined)
                relations.append(relation_score)
            
            relations = torch.stack(relations)
            weights = F.softmax(relations, dim=0)
            
            weighted_feat = torch.sum(weights * support_features, dim=0)
            weighted_features.append(weighted_feat)
        
        weighted_features = torch.stack(weighted_features)
        
        # 预测关键点
        keypoints = self.keypoint_regressor(weighted_features)
        return keypoints.view(-1, 19, 3)

class MixupAugmentation:
    """Mixup数据增强 - 混合样本生成"""
    
    def __init__(self, alpha=0.2):
        self.alpha = alpha
        
    def mixup(self, pc1, kp1, pc2, kp2):
        """混合两个样本"""
        lam = np.random.beta(self.alpha, self.alpha)
        
        # 确保点云有相同的点数
        min_points = min(len(pc1), len(pc2))
        if len(pc1) > min_points:
            indices1 = np.random.choice(len(pc1), min_points, replace=False)
            pc1 = pc1[indices1]
        if len(pc2) > min_points:
            indices2 = np.random.choice(len(pc2), min_points, replace=False)
            pc2 = pc2[indices2]
        
        mixed_pc = lam * pc1 + (1 - lam) * pc2
        mixed_kp = lam * kp1 + (1 - lam) * kp2
        
        return mixed_pc, mixed_kp, lam

class CutMixAugmentation:
    """CutMix数据增强 - 点云区域替换"""
    
    def __init__(self, alpha=1.0):
        self.alpha = alpha
        
    def cutmix(self, pc1, kp1, pc2, kp2):
        """CutMix两个点云"""
        lam = np.random.beta(self.alpha, self.alpha)
        
        # 随机选择要替换的点
        num_points = min(len(pc1), len(pc2))
        num_cut = int(num_points * (1 - lam))
        
        cut_indices = np.random.choice(num_points, num_cut, replace=False)
        
        mixed_pc = pc1[:num_points].copy()
        mixed_pc[cut_indices] = pc2[:num_points][cut_indices]
        
        # 关键点按比例混合
        mixed_kp = lam * kp1 + (1 - lam) * kp2
        
        return mixed_pc, mixed_kp, lam

class ContrastiveLearningPretrainer:
    """对比学习预训练器"""
    
    def __init__(self, model, temperature=0.1, device='cuda'):
        self.model = model
        self.temperature = temperature
        self.device = device
        
    def create_augmented_views(self, point_cloud):
        """创建增强视图"""
        views = []
        
        # 视图1: 旋转
        angle = np.random.uniform(-0.1, 0.1)
        cos_a, sin_a = np.cos(angle), np.sin(angle)
        rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
        view1 = point_cloud @ rotation.T
        views.append(view1)
        
        # 视图2: 噪声
        noise = np.random.normal(0, 1.0, point_cloud.shape)
        view2 = point_cloud + noise
        views.append(view2)
        
        # 视图3: 随机dropout
        keep_ratio = np.random.uniform(0.7, 0.9)
        num_keep = int(len(point_cloud) * keep_ratio)
        indices = np.random.choice(len(point_cloud), num_keep, replace=False)
        view3 = point_cloud[indices]
        # 补齐到原始长度
        if len(view3) < len(point_cloud):
            extra_indices = np.random.choice(len(view3), len(point_cloud) - len(view3), replace=True)
            view3 = np.vstack([view3, view3[extra_indices]])
        views.append(view3)
        
        return views
    
    def contrastive_loss(self, z1, z2):
        """对比损失"""
        z1 = F.normalize(z1, dim=1)
        z2 = F.normalize(z2, dim=1)
        
        batch_size = z1.size(0)
        
        # 计算相似度矩阵
        similarity_matrix = torch.matmul(z1, z2.T) / self.temperature
        
        # 正样本在对角线上
        labels = torch.arange(batch_size).to(self.device)
        
        loss = F.cross_entropy(similarity_matrix, labels)
        return loss

class DataSynthesizer:
    """数据合成器 - 生成新样本"""
    
    def __init__(self):
        self.interpolation_methods = [
            self.linear_interpolation,
            self.spherical_interpolation,
            self.bezier_interpolation
        ]
        
    def linear_interpolation(self, pc1, kp1, pc2, kp2, t=0.5):
        """线性插值"""
        new_pc = (1 - t) * pc1 + t * pc2
        new_kp = (1 - t) * kp1 + t * kp2
        return new_pc, new_kp
    
    def spherical_interpolation(self, pc1, kp1, pc2, kp2, t=0.5):
        """球面插值"""
        # 简化的球面插值
        omega = np.arccos(np.clip(np.sum(pc1 * pc2, axis=1) / 
                                 (np.linalg.norm(pc1, axis=1) * np.linalg.norm(pc2, axis=1)), -1, 1))
        
        sin_omega = np.sin(omega)
        sin_omega[sin_omega == 0] = 1e-8  # 避免除零
        
        a = np.sin((1 - t) * omega) / sin_omega
        b = np.sin(t * omega) / sin_omega
        
        new_pc = a[:, np.newaxis] * pc1 + b[:, np.newaxis] * pc2
        new_kp = (1 - t) * kp1 + t * kp2  # 关键点仍用线性插值
        
        return new_pc, new_kp
    
    def bezier_interpolation(self, pc1, kp1, pc2, kp2, t=0.5):
        """贝塞尔插值"""
        # 二次贝塞尔曲线
        control_point = (pc1 + pc2) / 2 + np.random.normal(0, 0.1, pc1.shape)
        
        new_pc = (1 - t)**2 * pc1 + 2 * (1 - t) * t * control_point + t**2 * pc2
        new_kp = (1 - t) * kp1 + t * kp2
        
        return new_pc, new_kp
    
    def generate_synthetic_samples(self, point_clouds, keypoints, num_synthetic=50):
        """生成合成样本"""
        synthetic_pcs = []
        synthetic_kps = []
        
        for _ in range(num_synthetic):
            # 随机选择两个样本
            idx1, idx2 = np.random.choice(len(point_clouds), 2, replace=False)
            pc1, kp1 = point_clouds[idx1], keypoints[idx1]
            pc2, kp2 = point_clouds[idx2], keypoints[idx2]
            
            # 随机选择插值方法
            method = np.random.choice(self.interpolation_methods)
            t = np.random.uniform(0.2, 0.8)  # 避免过于接近原始样本
            
            new_pc, new_kp = method(pc1, kp1, pc2, kp2, t)
            synthetic_pcs.append(new_pc)
            synthetic_kps.append(new_kp)
        
        return np.array(synthetic_pcs), np.array(synthetic_kps)

class EnsembleFewShotLearner:
    """集成小样本学习器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        self.models = []
        
    def create_diverse_models(self):
        """创建多样化的模型"""
        # 模型1: 匹配网络
        model1 = MatchingNetworks()
        
        # 模型2: 关系网络  
        model2 = RelationNetwork()
        
        # 模型3: 原型网络 (从之前的代码导入)
        from optimized_few_shot_final import OptimizedKeypointNet
        model3 = OptimizedKeypointNet()
        
        self.models = [model1.to(self.device), model2.to(self.device), model3.to(self.device)]
        return self.models
    
    def ensemble_predict(self, point_cloud):
        """集成预测"""
        predictions = []
        
        for model in self.models:
            model.eval()
            with torch.no_grad():
                if isinstance(model, MatchingNetworks):
                    # 匹配网络需要支持集，这里简化处理
                    pred = model.keypoint_regressor(
                        model.feature_extractor(point_cloud.view(-1, 3)).max(0)[0].unsqueeze(0)
                    )
                elif isinstance(model, RelationNetwork):
                    # 关系网络也简化处理
                    pred = model.keypoint_regressor(
                        model.feature_extractor(point_cloud.view(-1, 3)).max(0)[0].unsqueeze(0)
                    )
                else:
                    pred = model(point_cloud.unsqueeze(0))
                
                predictions.append(pred.squeeze(0))
        
        # 平均集成
        ensemble_pred = torch.stack(predictions).mean(dim=0)
        return ensemble_pred

def create_rescue_plan():
    """创建小数据集救援计划"""
    
    rescue_plan = {
        "phase_1_data_augmentation": {
            "methods": [
                "Mixup - 样本混合生成",
                "CutMix - 点云区域替换", 
                "对比学习预训练",
                "数据合成 - 插值生成新样本"
            ],
            "expected_improvement": "20-30%",
            "implementation_time": "1-2天"
        },
        
        "phase_2_advanced_models": {
            "methods": [
                "匹配网络 - 注意力机制",
                "关系网络 - 相似性学习",
                "集成学习 - 多模型融合"
            ],
            "expected_improvement": "15-25%", 
            "implementation_time": "2-3天"
        },
        
        "phase_3_domain_specific": {
            "methods": [
                "医疗先验知识注入",
                "解剖约束优化",
                "多任务学习",
                "知识蒸馏"
            ],
            "expected_improvement": "10-20%",
            "implementation_time": "3-4天"
        },
        
        "emergency_measures": {
            "methods": [
                "数据清洗和质量提升",
                "标注一致性检查", 
                "外部数据源整合",
                "半监督学习"
            ],
            "expected_improvement": "30-50%",
            "implementation_time": "1周"
        }
    }
    
    return rescue_plan

if __name__ == "__main__":
    print("🆘 小数据集救援方案")
    print("=" * 50)
    
    plan = create_rescue_plan()
    
    for phase, details in plan.items():
        print(f"\n📋 {phase.replace('_', ' ').title()}:")
        print(f"   方法: {details['methods']}")
        print(f"   预期改进: {details['expected_improvement']}")
        print(f"   实施时间: {details['implementation_time']}")
    
    print(f"\n💡 建议优先级:")
    print(f"1. 🚨 紧急措施 - 数据质量提升")
    print(f"2. 🔧 阶段1 - 数据增强")
    print(f"3. 🤖 阶段2 - 高级模型")
    print(f"4. 🏥 阶段3 - 医疗专用优化")
    
    print(f"\n🎯 目标: 从21mm误差降低到<5mm医疗应用水平")
