#!/usr/bin/env python3
"""
分析F3关键点分布，设计平均挑选F1、F2、F3关键点的策略
"""

import numpy as np
import matplotlib.pyplot as plt
import json

def analyze_f3_keypoints():
    """分析F3关键点分布"""
    
    print("🔍 **F3关键点分布分析**")
    print("=" * 60)
    
    # 加载数据
    data = np.load('high_quality_f3_dataset.npz', allow_pickle=True)
    
    sample_ids = data['sample_ids']
    keypoints = data['keypoints']
    
    print(f"📊 数据集信息:")
    print(f"   样本数量: {len(sample_ids)}")
    print(f"   关键点数量: {keypoints.shape[1]} 个")
    print(f"   关键点维度: {keypoints.shape[2]}D")
    
    # 分析关键点命名规律
    # 根据医疗点云的常见命名规律，F3关键点通常按解剖区域分组
    print(f"\n🎯 **F3关键点分析**")
    print(f"   当前F3关键点总数: {keypoints.shape[1]} 个")
    
    # 假设F3关键点按照解剖区域分布
    # F1区域: 通常是前部关键点 (约6-7个)
    # F2区域: 通常是中部关键点 (约6-7个) 
    # F3区域: 通常是后部关键点 (约6-7个)
    
    total_keypoints = keypoints.shape[1]
    
    # 平均分配策略
    points_per_region = total_keypoints // 3
    remainder = total_keypoints % 3
    
    f1_count = points_per_region + (1 if remainder > 0 else 0)
    f2_count = points_per_region + (1 if remainder > 1 else 0)
    f3_count = points_per_region
    
    print(f"\n📋 **平均分配方案**:")
    print(f"   F1区域关键点: {f1_count} 个 (索引 0-{f1_count-1})")
    print(f"   F2区域关键点: {f2_count} 个 (索引 {f1_count}-{f1_count+f2_count-1})")
    print(f"   F3区域关键点: {f3_count} 个 (索引 {f1_count+f2_count}-{total_keypoints-1})")
    
    # 计算每个关键点的统计信息
    print(f"\n📊 **关键点位置统计**:")
    
    keypoint_stats = []
    for i in range(total_keypoints):
        kp_coords = keypoints[:, i, :]  # 所有样本的第i个关键点
        
        mean_pos = np.mean(kp_coords, axis=0)
        std_pos = np.std(kp_coords, axis=0)
        
        # 确定区域
        if i < f1_count:
            region = "F1"
        elif i < f1_count + f2_count:
            region = "F2"
        else:
            region = "F3"
        
        keypoint_stats.append({
            'index': i,
            'region': region,
            'mean_position': mean_pos.tolist(),
            'std_position': std_pos.tolist(),
            'mean_x': float(mean_pos[0]),
            'mean_y': float(mean_pos[1]),
            'mean_z': float(mean_pos[2])
        })
        
        print(f"   关键点{i:2d} ({region}): 位置({mean_pos[0]:6.2f}, {mean_pos[1]:6.2f}, {mean_pos[2]:6.2f}) "
              f"标准差({std_pos[0]:.2f}, {std_pos[1]:.2f}, {std_pos[2]:.2f})")
    
    return keypoint_stats, f1_count, f2_count, f3_count

def design_reduced_keypoint_strategies():
    """设计减少关键点数量的策略"""
    
    print(f"\n🎯 **减少关键点数量策略设计**")
    print("=" * 60)
    
    keypoint_stats, f1_count, f2_count, f3_count = analyze_f3_keypoints()
    
    # 策略1: 每个区域选择最稳定的关键点
    def select_most_stable_points(stats, region, count):
        """选择指定区域最稳定的关键点"""
        region_points = [kp for kp in stats if kp['region'] == region]
        
        # 计算稳定性分数 (标准差越小越稳定)
        for kp in region_points:
            std_sum = sum(kp['std_position'])
            kp['stability_score'] = 1.0 / (1.0 + std_sum)  # 稳定性分数
        
        # 按稳定性排序，选择最稳定的
        region_points.sort(key=lambda x: x['stability_score'], reverse=True)
        return region_points[:count]
    
    # 策略2: 每个区域选择空间分布最均匀的关键点
    def select_spatially_distributed_points(stats, region, count):
        """选择指定区域空间分布最均匀的关键点"""
        region_points = [kp for kp in stats if kp['region'] == region]
        
        if len(region_points) <= count:
            return region_points
        
        # 简单的空间分布策略：按X坐标排序，均匀选择
        region_points.sort(key=lambda x: x['mean_x'])
        
        selected = []
        step = len(region_points) / count
        for i in range(count):
            idx = int(i * step)
            selected.append(region_points[idx])
        
        return selected
    
    # 不同的减少策略
    strategies = [
        {
            'name': '每区域2个关键点 (总计6个)',
            'f1_count': 2,
            'f2_count': 2,
            'f3_count': 2,
            'total': 6
        },
        {
            'name': '每区域3个关键点 (总计9个)',
            'f1_count': 3,
            'f2_count': 3,
            'f3_count': 3,
            'total': 9
        },
        {
            'name': '每区域4个关键点 (总计12个)',
            'f1_count': 4,
            'f2_count': 4,
            'f3_count': 4,
            'total': 12
        },
        {
            'name': '不均匀分配 (F1:3, F2:4, F3:3, 总计10个)',
            'f1_count': 3,
            'f2_count': 4,
            'f3_count': 3,
            'total': 10
        }
    ]
    
    results = []
    
    for strategy in strategies:
        print(f"\n📋 **{strategy['name']}**")
        
        # 使用稳定性策略选择关键点
        f1_stable = select_most_stable_points(keypoint_stats, 'F1', strategy['f1_count'])
        f2_stable = select_most_stable_points(keypoint_stats, 'F2', strategy['f2_count'])
        f3_stable = select_most_stable_points(keypoint_stats, 'F3', strategy['f3_count'])
        
        # 使用空间分布策略选择关键点
        f1_spatial = select_spatially_distributed_points(keypoint_stats, 'F1', strategy['f1_count'])
        f2_spatial = select_spatially_distributed_points(keypoint_stats, 'F2', strategy['f2_count'])
        f3_spatial = select_spatially_distributed_points(keypoint_stats, 'F3', strategy['f3_count'])
        
        stable_indices = [kp['index'] for kp in f1_stable + f2_stable + f3_stable]
        spatial_indices = [kp['index'] for kp in f1_spatial + f2_spatial + f3_spatial]
        
        print(f"   稳定性策略选择的关键点索引: {sorted(stable_indices)}")
        print(f"   空间分布策略选择的关键点索引: {sorted(spatial_indices)}")
        
        strategy_result = {
            'strategy': strategy,
            'stable_selection': {
                'indices': sorted(stable_indices),
                'f1_indices': [kp['index'] for kp in f1_stable],
                'f2_indices': [kp['index'] for kp in f2_stable],
                'f3_indices': [kp['index'] for kp in f3_stable]
            },
            'spatial_selection': {
                'indices': sorted(spatial_indices),
                'f1_indices': [kp['index'] for kp in f1_spatial],
                'f2_indices': [kp['index'] for kp in f2_spatial],
                'f3_indices': [kp['index'] for kp in f3_spatial]
            }
        }
        
        results.append(strategy_result)
    
    return results

def create_reduced_datasets(strategies):
    """创建减少关键点数量的数据集"""
    
    print(f"\n🔧 **创建减少关键点数量的数据集**")
    print("=" * 60)
    
    # 加载原始数据
    data = np.load('high_quality_f3_dataset.npz', allow_pickle=True)
    sample_ids = data['sample_ids']
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    
    for i, strategy_result in enumerate(strategies):
        strategy = strategy_result['strategy']
        
        # 为每种选择方法创建数据集
        for selection_type in ['stable_selection', 'spatial_selection']:
            selection = strategy_result[selection_type]
            selected_indices = selection['indices']
            
            # 提取选中的关键点
            reduced_keypoints = keypoints[:, selected_indices, :]
            
            # 创建文件名
            method_name = "stable" if selection_type == 'stable_selection' else "spatial"
            filename = f"reduced_f3_dataset_{strategy['total']}kp_{method_name}.npz"
            
            # 保存数据集
            np.savez_compressed(
                filename,
                sample_ids=sample_ids,
                point_clouds=point_clouds,
                keypoints=reduced_keypoints,
                selected_indices=np.array(selected_indices),
                original_total=keypoints.shape[1],
                strategy_info=strategy
            )
            
            print(f"✅ 已创建: {filename}")
            print(f"   关键点数量: {len(selected_indices)} (原始: {keypoints.shape[1]})")
            print(f"   选择策略: {method_name}")
            print(f"   选中索引: {selected_indices}")
    
    # 推荐最佳策略
    print(f"\n💡 **推荐策略**")
    print("=" * 40)
    print("基于医疗关键点检测的经验，推荐以下策略：")
    print()
    print("🥇 **首选**: 每区域3个关键点 (总计9个) - 稳定性策略")
    print("   - 保持足够的解剖信息")
    print("   - 减少50%的关键点数量")
    print("   - 训练更快，更容易收敛")
    print()
    print("🥈 **备选**: 每区域2个关键点 (总计6个) - 稳定性策略") 
    print("   - 最大程度减少关键点")
    print("   - 训练最快")
    print("   - 可能丢失一些细节信息")
    print()
    print("🥉 **保守**: 每区域4个关键点 (总计12个) - 稳定性策略")
    print("   - 保留更多解剖信息")
    print("   - 仍有显著的训练加速")

def main():
    """主函数"""
    
    print("🎯 **F3关键点分析与减少策略设计**")
    print("🔍 **目标**: 平均挑选F1、F2、F3关键点，减少总数量，提高检测精度")
    print("=" * 80)
    
    try:
        # 分析关键点分布
        strategies = design_reduced_keypoint_strategies()
        
        # 创建减少关键点的数据集
        create_reduced_datasets(strategies)
        
        # 保存分析结果
        analysis_results = {
            'original_keypoints': 19,
            'strategies': strategies,
            'recommendations': {
                'primary': {
                    'name': '每区域3个关键点 (总计9个) - 稳定性策略',
                    'filename': 'reduced_f3_dataset_9kp_stable.npz',
                    'keypoints': 9,
                    'reduction': '53%'
                },
                'alternative': {
                    'name': '每区域2个关键点 (总计6个) - 稳定性策略',
                    'filename': 'reduced_f3_dataset_6kp_stable.npz',
                    'keypoints': 6,
                    'reduction': '68%'
                },
                'conservative': {
                    'name': '每区域4个关键点 (总计12个) - 稳定性策略',
                    'filename': 'reduced_f3_dataset_12kp_stable.npz',
                    'keypoints': 12,
                    'reduction': '37%'
                }
            }
        }
        
        with open('f3_keypoint_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(analysis_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 **分析结果已保存**: f3_keypoint_analysis.json")
        
        print(f"\n🚀 **下一步建议**")
        print("=" * 40)
        print("1. 先用9个关键点的数据集训练，验证效果")
        print("2. 如果效果好，可以尝试6个关键点")
        print("3. 如果效果不佳，使用12个关键点的保守方案")
        print("4. 对比不同关键点数量的训练速度和精度")
        
        return analysis_results
        
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    results = main()
