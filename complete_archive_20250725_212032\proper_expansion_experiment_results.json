{"experiment_type": "proper_dataset_expansion", "key_improvements": ["解决了数据泄露问题", "使用真实的高性能模型架构", "正确的训练/测试分割", "保守的数据增强策略"], "results": [{"stage": "baseline_female", "model_type": "female_optimized", "train_samples": 20, "test_samples": 5, "avg_error": 19.539905548095703, "accuracy_5mm": 0.0, "accuracy_10mm": 0.0, "medical_grade": false, "excellent_grade": false}, {"stage": "baseline_male", "model_type": "mutual_assistance", "train_samples": 57, "test_samples": 15, "avg_error": 5.8392791748046875, "accuracy_5mm": 26.666666666666668, "accuracy_10mm": 100.0, "medical_grade": true, "excellent_grade": false}, {"stage": "stage1_female", "model_type": "female_optimized", "train_samples": 30, "test_samples": 5, "avg_error": 19.826566696166992, "accuracy_5mm": 0.0, "accuracy_10mm": 0.0, "medical_grade": false, "excellent_grade": false}, {"stage": "stage1_male", "model_type": "mutual_assistance", "train_samples": 80, "test_samples": 15, "avg_error": 5.837949275970459, "accuracy_5mm": 26.666666666666668, "accuracy_10mm": 100.0, "medical_grade": true, "excellent_grade": false}, {"stage": "stage2_female", "model_type": "female_optimized", "train_samples": 40, "test_samples": 5, "avg_error": 12.024542808532715, "accuracy_5mm": 0.0, "accuracy_10mm": 0.0, "medical_grade": false, "excellent_grade": false}, {"stage": "stage2_male", "model_type": "mutual_assistance", "train_samples": 100, "test_samples": 15, "avg_error": 5.6909332275390625, "accuracy_5mm": 26.666666666666668, "accuracy_10mm": 100.0, "medical_grade": true, "excellent_grade": false}, {"stage": "stage3_female", "model_type": "female_optimized", "train_samples": 50, "test_samples": 5, "avg_error": 9.984426498413086, "accuracy_5mm": 0.0, "accuracy_10mm": 40.0, "medical_grade": true, "excellent_grade": false}, {"stage": "stage3_male", "model_type": "mutual_assistance", "train_samples": 120, "test_samples": 15, "avg_error": 5.650357723236084, "accuracy_5mm": 26.666666666666668, "accuracy_10mm": 100.0, "medical_grade": true, "excellent_grade": false}], "summary": {"total_experiments": 8, "architectures_used": ["MutualAssistanceNet", "FemaleOptimizedNet"], "data_leakage_prevented": true}, "timestamp": "2025-07-25"}