#!/usr/bin/env python3
"""
人工检查友好的异常报告
Human-Readable Anomaly Report for Manual Review
"""

import numpy as np
import json
from pathlib import Path
from datetime import datetime

class HumanReadableAnomalyReporter:
    """人工检查友好的异常报告器"""
    
    def __init__(self, data_path='data/raw/high_quality_f3_dataset.npz'):
        self.data_path = data_path
        self.load_data()
        
    def load_data(self):
        """加载数据"""
        print(f"📦 加载数据: {self.data_path}")
        
        data = np.load(self.data_path, allow_pickle=True)
        self.sample_ids = data['sample_ids']
        self.point_clouds = data['point_clouds']
        self.keypoints = data['keypoints']
        
        print(f"✅ 数据加载完成: {len(self.sample_ids)} 样本")
        
    def calculate_basic_statistics(self):
        """计算基础统计信息"""
        print("\n📊 计算基础统计信息...")
        
        # 关键点间距离统计
        all_distances = []
        for kps in self.keypoints:
            for i in range(len(kps)):
                for j in range(i+1, len(kps)):
                    dist = np.linalg.norm(kps[i] - kps[j])
                    all_distances.append(dist)
        
        all_distances = np.array(all_distances)
        
        # 点云统计
        pc_sizes = [len(pc) for pc in self.point_clouds]
        pc_bounds = []
        for pc in self.point_clouds:
            bounds = np.max(pc, axis=0) - np.min(pc, axis=0)
            pc_bounds.append(bounds)
        pc_bounds = np.array(pc_bounds)
        
        stats = {
            'keypoint_distances': {
                'mean': np.mean(all_distances),
                'std': np.std(all_distances),
                'min': np.min(all_distances),
                'max': np.max(all_distances),
                'median': np.median(all_distances)
            },
            'point_cloud_sizes': {
                'mean': np.mean(pc_sizes),
                'std': np.std(pc_sizes),
                'min': np.min(pc_sizes),
                'max': np.max(pc_sizes)
            },
            'point_cloud_bounds': {
                'mean_x': np.mean(pc_bounds[:, 0]),
                'mean_y': np.mean(pc_bounds[:, 1]),
                'mean_z': np.mean(pc_bounds[:, 2]),
                'std_x': np.std(pc_bounds[:, 0]),
                'std_y': np.std(pc_bounds[:, 1]),
                'std_z': np.std(pc_bounds[:, 2])
            }
        }
        
        return stats
    
    def identify_suspicious_samples(self):
        """识别可疑样本"""
        print("\n🔍 识别可疑样本...")
        
        stats = self.calculate_basic_statistics()
        suspicious_samples = []
        
        for i, (sample_id, pc, kps) in enumerate(zip(self.sample_ids, self.point_clouds, self.keypoints)):
            issues = []
            
            # 检查点云大小
            pc_size = len(pc)
            expected_size = stats['point_cloud_sizes']['mean']
            size_threshold = 2 * stats['point_cloud_sizes']['std']
            
            if abs(pc_size - expected_size) > size_threshold:
                issues.append({
                    'type': '点云大小异常',
                    'severity': 'high' if abs(pc_size - expected_size) > 3 * stats['point_cloud_sizes']['std'] else 'medium',
                    'details': f"点数: {pc_size}, 期望: {expected_size:.0f}±{stats['point_cloud_sizes']['std']:.0f}",
                    'suggestion': '检查点云采样是否正确'
                })
            
            # 检查点云尺寸
            bounds = np.max(pc, axis=0) - np.min(pc, axis=0)
            expected_bounds = [stats['point_cloud_bounds']['mean_x'], 
                             stats['point_cloud_bounds']['mean_y'], 
                             stats['point_cloud_bounds']['mean_z']]
            bounds_std = [stats['point_cloud_bounds']['std_x'],
                         stats['point_cloud_bounds']['std_y'],
                         stats['point_cloud_bounds']['std_z']]
            
            for dim, (actual, expected, std_val) in enumerate(zip(bounds, expected_bounds, bounds_std)):
                if abs(actual - expected) > 2 * std_val:
                    dim_name = ['X', 'Y', 'Z'][dim]
                    issues.append({
                        'type': f'{dim_name}轴尺寸异常',
                        'severity': 'medium',
                        'details': f"{dim_name}轴范围: {actual:.1f}, 期望: {expected:.1f}±{std_val:.1f}",
                        'suggestion': f'检查{dim_name}轴坐标是否正确'
                    })
            
            # 检查关键点分布
            kp_center = np.mean(kps, axis=0)
            pc_center = np.mean(pc, axis=0)
            center_distance = np.linalg.norm(kp_center - pc_center)
            
            if center_distance > 10.0:  # 10mm阈值
                issues.append({
                    'type': '关键点-点云中心偏移',
                    'severity': 'high' if center_distance > 20.0 else 'medium',
                    'details': f"偏移距离: {center_distance:.1f}mm",
                    'suggestion': '检查关键点和点云是否在同一坐标系'
                })
            
            # 检查关键点间距离
            sample_distances = []
            for j in range(len(kps)):
                for k in range(j+1, len(kps)):
                    dist = np.linalg.norm(kps[j] - kps[k])
                    sample_distances.append(dist)
            
            sample_mean_dist = np.mean(sample_distances)
            global_mean_dist = stats['keypoint_distances']['mean']
            global_std_dist = stats['keypoint_distances']['std']
            
            if abs(sample_mean_dist - global_mean_dist) > 2 * global_std_dist:
                scale_factor = sample_mean_dist / global_mean_dist
                issues.append({
                    'type': '关键点整体尺度异常',
                    'severity': 'high' if abs(scale_factor - 1.0) > 0.5 else 'medium',
                    'details': f"平均距离: {sample_mean_dist:.1f}mm, 期望: {global_mean_dist:.1f}mm, 缩放比: {scale_factor:.2f}",
                    'suggestion': '检查标注尺度是否正确，可能需要重新标注'
                })
            
            # 检查极端距离
            min_dist = np.min(sample_distances)
            max_dist = np.max(sample_distances)
            
            if min_dist < 1.0:  # 关键点过近
                issues.append({
                    'type': '关键点过近',
                    'severity': 'high',
                    'details': f"最小距离: {min_dist:.1f}mm",
                    'suggestion': '检查是否有重复标注的关键点'
                })
            
            if max_dist > stats['keypoint_distances']['max'] * 0.8:  # 关键点过远
                issues.append({
                    'type': '关键点过远',
                    'severity': 'medium',
                    'details': f"最大距离: {max_dist:.1f}mm",
                    'suggestion': '检查是否有标注错误的关键点'
                })
            
            if issues:
                suspicious_samples.append({
                    'sample_id': sample_id,
                    'sample_index': i,
                    'issue_count': len(issues),
                    'issues': issues
                })
        
        return suspicious_samples
    
    def generate_human_readable_report(self):
        """生成人工检查友好的报告"""
        print("\n📋 生成人工检查报告...")
        
        stats = self.calculate_basic_statistics()
        suspicious_samples = self.identify_suspicious_samples()
        
        # 按问题严重程度排序
        suspicious_samples.sort(key=lambda x: (
            sum(1 for issue in x['issues'] if issue['severity'] == 'high') * 10 +
            sum(1 for issue in x['issues'] if issue['severity'] == 'medium')
        ), reverse=True)
        
        # 生成报告
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("🔍 医疗关键点数据集异常检查报告")
        report_lines.append("=" * 80)
        report_lines.append(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"📊 总样本数: {len(self.sample_ids)}")
        report_lines.append(f"⚠️  可疑样本数: {len(suspicious_samples)} ({len(suspicious_samples)/len(self.sample_ids)*100:.1f}%)")
        report_lines.append("")
        
        # 数据集统计摘要
        report_lines.append("📊 数据集统计摘要:")
        report_lines.append("-" * 40)
        report_lines.append(f"关键点间距离: {stats['keypoint_distances']['mean']:.1f}±{stats['keypoint_distances']['std']:.1f}mm")
        report_lines.append(f"  范围: {stats['keypoint_distances']['min']:.1f} - {stats['keypoint_distances']['max']:.1f}mm")
        report_lines.append(f"点云大小: {stats['point_cloud_sizes']['mean']:.0f}±{stats['point_cloud_sizes']['std']:.0f} 点")
        report_lines.append(f"  范围: {stats['point_cloud_sizes']['min']} - {stats['point_cloud_sizes']['max']} 点")
        report_lines.append(f"点云尺寸 (X×Y×Z): {stats['point_cloud_bounds']['mean_x']:.1f}×{stats['point_cloud_bounds']['mean_y']:.1f}×{stats['point_cloud_bounds']['mean_z']:.1f}mm")
        report_lines.append("")
        
        # 可疑样本详情
        report_lines.append("🚨 可疑样本详细列表:")
        report_lines.append("-" * 80)
        
        for i, sample in enumerate(suspicious_samples[:20]):  # 只显示前20个最严重的
            high_issues = sum(1 for issue in sample['issues'] if issue['severity'] == 'high')
            medium_issues = sum(1 for issue in sample['issues'] if issue['severity'] == 'medium')
            
            report_lines.append(f"{i+1:2d}. 样本 {sample['sample_id']} (索引: {sample['sample_index']})")
            report_lines.append(f"    问题数量: {sample['issue_count']} (高危: {high_issues}, 中危: {medium_issues})")
            
            for j, issue in enumerate(sample['issues']):
                severity_icon = "🔴" if issue['severity'] == 'high' else "🟡"
                report_lines.append(f"    {severity_icon} {issue['type']}: {issue['details']}")
                report_lines.append(f"       建议: {issue['suggestion']}")
            
            report_lines.append("")
        
        if len(suspicious_samples) > 20:
            report_lines.append(f"... 还有 {len(suspicious_samples) - 20} 个可疑样本未显示")
            report_lines.append("")
        
        # 总体建议
        report_lines.append("💡 总体检查建议:")
        report_lines.append("-" * 40)
        
        high_priority_samples = [s for s in suspicious_samples if any(issue['severity'] == 'high' for issue in s['issues'])]
        medium_priority_samples = [s for s in suspicious_samples if s not in high_priority_samples]
        
        report_lines.append(f"1. 🔴 高优先级: {len(high_priority_samples)} 个样本需要立即检查")
        report_lines.append("   - 重点检查关键点标注是否正确")
        report_lines.append("   - 验证坐标系是否一致")
        report_lines.append("   - 考虑重新标注或移除")
        report_lines.append("")
        
        report_lines.append(f"2. 🟡 中优先级: {len(medium_priority_samples)} 个样本建议检查")
        report_lines.append("   - 检查数据预处理是否正确")
        report_lines.append("   - 验证标注精度")
        report_lines.append("   - 可考虑保留但需要注意")
        report_lines.append("")
        
        report_lines.append("3. 🎯 改进建议:")
        report_lines.append("   - 建立标注质量控制流程")
        report_lines.append("   - 统一坐标系和尺度标准")
        report_lines.append("   - 增加标注一致性检查")
        report_lines.append("   - 考虑引入多人标注验证")
        
        # 保存报告
        report_dir = Path("results/human_readable_reports")
        report_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = report_dir / f"human_readable_anomaly_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        # 打印报告
        for line in report_lines:
            print(line)
        
        print(f"\n💾 人工检查报告已保存: {report_file}")
        
        # 生成高优先级样本列表
        high_priority_file = report_dir / f"high_priority_samples_{timestamp}.txt"
        with open(high_priority_file, 'w', encoding='utf-8') as f:
            f.write("高优先级需要检查的样本列表:\n")
            f.write("=" * 40 + "\n")
            for sample in high_priority_samples:
                f.write(f"样本ID: {sample['sample_id']} (索引: {sample['sample_index']})\n")
                for issue in sample['issues']:
                    if issue['severity'] == 'high':
                        f.write(f"  - {issue['type']}: {issue['details']}\n")
                f.write("\n")
        
        print(f"💾 高优先级样本列表已保存: {high_priority_file}")
        
        return suspicious_samples, stats

def main():
    """主函数"""
    
    print("🔍 生成人工检查友好的异常报告")
    print("=" * 60)
    
    # 创建报告器
    reporter = HumanReadableAnomalyReporter()
    
    # 生成报告
    suspicious_samples, stats = reporter.generate_human_readable_report()
    
    print(f"\n🎯 检查重点:")
    print(f"1. 重点关注前10个样本，它们问题最严重")
    print(f"2. 检查关键点标注是否在正确位置")
    print(f"3. 验证点云和关键点坐标系是否一致")
    print(f"4. 确认异常样本是否需要重新标注")
    
    return suspicious_samples, stats

if __name__ == "__main__":
    suspicious_samples, stats = main()
