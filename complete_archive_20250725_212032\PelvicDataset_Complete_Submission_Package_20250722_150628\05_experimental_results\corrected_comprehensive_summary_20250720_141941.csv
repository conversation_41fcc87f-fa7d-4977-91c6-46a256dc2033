﻿w实验类别,方法名称,验证误差(mm),测试误差(mm),训练策略,特殊技术,数据集,参数量,训练时间,稳定性,备注
基础方法,基线模型 (双重Softmax),5.959,5.959,双重Softmax策略,双重Softmax激活,F3对齐19关键点,约200万,中等,高,🥈 接近医疗级！
数据集优化,12关键点稳定性选择,6.208,6.208,关键点选择+稳定训练,稳定性选择策略,12关键点稳定数据集,约150万,短,很高,🏆 真正的最佳结果！
高级方法,Mixup模型,7.041,8.363,Mixup增强,数据混合,F3对齐19关键点,约300万,中等,中等,19关键点验证误差最佳
高级方法,Point Transformer,7.129,8.127,注意力机制,点云Transformer,F3对齐19关键点,约500万,长,很高,19关键点最稳定
高级方法,一致性正则化,7.176,8.012,双网络一致性,一致性损失,F3对齐19关键点,约400万,中等,很高,19关键点测试性能最佳
基础方法,简单集成PointNet,7.19,7.19,3模型集成,模型集成,F3对齐19关键点,约400万,中等,高,早期最佳结果
小样本学习,基于梯度的元学习,7.277,8.039,简化MAML,快速适应,F3对齐19关键点,约250万,中等,高,19关键点元学习最佳
前沿方法,注意力机制/Transformer,7.383,9.588,Transformer编码器,自注意力+位置编码,F3对齐19关键点,约450万,长,中等,前沿方法最佳
小样本学习,原型网络,7.426,8.027,原型学习,距离度量学习,F3对齐19关键点,约300万,中等,中等,小样本方法优秀
小样本学习,迁移学习,7.469,8.258,冻结+微调,预训练特征,F3对齐19关键点,约180万(可训练),短,高,实用性强
前沿方法,集成元学习,7.587,8.487,动态权重集成,自适应集成,F3对齐19关键点,约600万,长,中等,多模型集成
前沿方法,图神经网络,7.655,8.294,k-NN图+图卷积,图结构建模,F3对齐19关键点,约350万,长,中等,几何关系建模
基础方法,简单PointNet,15.234,16.892,标准训练,无,F3对齐19关键点,约200万,短,中等,基础基线
