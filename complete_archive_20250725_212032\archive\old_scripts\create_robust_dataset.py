#!/usr/bin/env python3
"""
Create Robust Dataset for Medical Keypoint Detection

Handle various CSV format issues and create a larger, more robust dataset.
"""

import numpy as np
import pandas as pd
from pathlib import Path
import json
import struct
import gc
import os

def load_annotation_file_robust(csv_path: str):
    """Robustly load annotation CSV file handling various format issues"""

    # Try different encodings
    encodings = ['gbk', 'utf-8', 'latin-1', 'cp1252']

    first_line = None
    working_encoding = None

    # Find working encoding
    for encoding in encodings:
        try:
            with open(csv_path, 'r', encoding=encoding) as f:
                first_line = f.readline().strip()
                working_encoding = encoding
                break
        except UnicodeDecodeError:
            continue

    if first_line is None:
        print(f"   ❌ 无法读取文件，所有编码都失败")
        return None, None

    has_header = 'label' in first_line.lower() and ('x' in first_line.lower() or 'X' in first_line)

    try:
        if has_header:
            # Standard format with header
            df = pd.read_csv(csv_path, encoding=working_encoding)
        else:
            # No header format - need to add column names
            df = pd.read_csv(csv_path, encoding=working_encoding, header=None)

            # Determine column structure based on number of columns
            if df.shape[1] >= 9:
                # Full format: label,X,Y,Z,defined,selected,visible,locked,description
                df.columns = ['label', 'X', 'Y', 'Z', 'defined', 'selected', 'visible', 'locked', 'description']
            elif df.shape[1] >= 4:
                # Minimal format: label,X,Y,Z
                df.columns = ['label', 'X', 'Y', 'Z'] + [f'col_{i}' for i in range(4, df.shape[1])]
            else:
                print(f"   ❌ 列数不足: {df.shape[1]}")
                return None, None

    except Exception as e:
        print(f"   ❌ 文件读取失败: {e}")
        return None, None
    
    # Validate required columns
    if 'X' not in df.columns or 'Y' not in df.columns or 'Z' not in df.columns:
        print(f"   ❌ 缺少坐标列")
        return None, None
    
    if 'label' not in df.columns:
        print(f"   ❌ 缺少标签列")
        return None, None
    
    # Extract data
    keypoints = df[['X', 'Y', 'Z']].values
    labels = df['label'].values.tolist()
    
    # Validate data
    if len(keypoints) == 0:
        print(f"   ❌ 没有关键点数据")
        return None, None
    
    # Check for valid coordinates
    if np.any(np.isnan(keypoints)) or np.any(np.isinf(keypoints)):
        print(f"   ❌ 坐标数据包含无效值")
        return None, None
    
    return keypoints, labels

def read_stl_binary_chunked(stl_path: str, max_vertices=200000):
    """Read STL file in chunks with memory limit"""
    try:
        with open(stl_path, 'rb') as f:
            f.read(80)  # Skip header
            num_triangles = struct.unpack('<I', f.read(4))[0]
            
            # Calculate how many triangles to read
            max_triangles = max_vertices // 3
            triangles_to_read = min(num_triangles, max_triangles)
            
            vertices = []
            chunk_size = 5000
            
            for chunk_start in range(0, triangles_to_read, chunk_size):
                chunk_end = min(chunk_start + chunk_size, triangles_to_read)
                chunk_vertices = []
                
                for i in range(chunk_start, chunk_end):
                    f.read(12)  # Skip normal
                    for j in range(3):
                        x, y, z = struct.unpack('<fff', f.read(12))
                        chunk_vertices.append([x, y, z])
                    f.read(2)  # Skip attribute
                
                vertices.extend(chunk_vertices)
                del chunk_vertices
                gc.collect()
            
            vertices = np.array(vertices)
            
            # Remove duplicates efficiently
            if len(vertices) > 50000:
                # For large arrays, use a sampling approach
                indices = np.random.choice(len(vertices), 50000, replace=False)
                vertices = vertices[indices]
            
            unique_vertices = np.unique(vertices, axis=0)
            del vertices
            gc.collect()
            
            return unique_vertices
            
    except Exception as e:
        print(f"   ❌ STL读取失败: {e}")
        return None

def separate_keypoints_by_region(keypoints, labels):
    """Separate keypoints by F1/F2/F3 regions based on labels"""
    
    f1_keypoints = []
    f2_keypoints = []
    f3_keypoints = []
    
    for i, label in enumerate(labels):
        if isinstance(label, str):
            label_upper = label.upper()
            if 'F_1' in label_upper or 'F1' in label_upper:
                f1_keypoints.append(keypoints[i])
            elif 'F_2' in label_upper or 'F2' in label_upper:
                f2_keypoints.append(keypoints[i])
            elif 'F_3' in label_upper or 'F3' in label_upper:
                f3_keypoints.append(keypoints[i])
    
    return {
        'F1': np.array(f1_keypoints) if f1_keypoints else np.array([]).reshape(0, 3),
        'F2': np.array(f2_keypoints) if f2_keypoints else np.array([]).reshape(0, 3),
        'F3': np.array(f3_keypoints) if f3_keypoints else np.array([]).reshape(0, 3)
    }

def validate_sample_completeness(sample_id, data_dir):
    """Check if sample has all required files"""
    
    data_path = Path(data_dir)
    annotations_dir = data_path / "annotations"
    stl_dir = data_path / "stl_models"
    
    # Check annotation file
    csv_file = annotations_dir / f"{sample_id}-Table-XYZ.CSV"
    if not csv_file.exists():
        return False, "缺少标注文件"
    
    # Check STL files
    missing_stl = []
    for region in ['F1', 'F2', 'F3']:
        stl_file = stl_dir / f"{sample_id}-F_{region[-1]}.stl"
        if not stl_file.exists():
            missing_stl.append(region)
    
    if missing_stl:
        return False, f"缺少STL文件: {missing_stl}"
    
    return True, "完整"

def process_single_sample_robust(sample_id, target_region='F3'):
    """Process single sample with robust error handling"""
    
    print(f"🔧 处理样本 {sample_id} ({target_region})")
    
    data_dir = "/home/<USER>/pjc/GCN/Data"
    data_path = Path(data_dir)
    annotations_dir = data_path / "annotations"
    stl_dir = data_path / "stl_models"
    
    # Validate completeness
    is_complete, status = validate_sample_completeness(sample_id, data_dir)
    if not is_complete:
        print(f"   ❌ {status}")
        return None
    
    # Load annotation with robust handling
    csv_file = annotations_dir / f"{sample_id}-Table-XYZ.CSV"
    keypoints, labels = load_annotation_file_robust(str(csv_file))
    
    if keypoints is None:
        return None
    
    print(f"   ✅ 加载了 {len(keypoints)} 个关键点")
    
    # Separate keypoints
    regions = separate_keypoints_by_region(keypoints, labels)
    
    # Check target region
    if len(regions[target_region]) == 0:
        print(f"   ❌ {target_region}关键点为空")
        return None
    
    # Load target region STL
    stl_file = stl_dir / f"{sample_id}-F_{target_region[-1]}.stl"
    vertices = read_stl_binary_chunked(str(stl_file))
    
    if vertices is None:
        return None
    
    print(f"   ✅ {target_region} STL: {len(vertices)} 个顶点")
    
    # Calculate alignment quality
    region_keypoints = regions[target_region]
    distances = []
    
    for kp in region_keypoints:
        dists = np.linalg.norm(vertices - kp, axis=1)
        min_dist = np.min(dists)
        distances.append(min_dist)
    
    distances = np.array(distances)
    mean_dist = np.mean(distances)
    within_1mm = np.sum(distances <= 1.0) / len(distances) * 100
    within_5mm = np.sum(distances <= 5.0) / len(distances) * 100
    
    print(f"   📊 {target_region}对齐: {mean_dist:.2f}mm平均, {within_5mm:.1f}% ≤5mm")
    
    # Sample point cloud
    target_points = 4096
    if len(vertices) > target_points:
        indices = np.random.choice(len(vertices), target_points, replace=False)
        sampled_vertices = vertices[indices].copy()
    else:
        indices = np.random.choice(len(vertices), target_points, replace=True)
        sampled_vertices = vertices[indices].copy()
    
    # Center the data
    kp_center = np.mean(region_keypoints, axis=0)
    centered_pc = sampled_vertices - kp_center
    centered_kps = region_keypoints - kp_center
    
    result = {
        'sample_id': sample_id,
        'point_cloud': centered_pc.astype(np.float32),
        'keypoints': centered_kps.astype(np.float32),
        'center': kp_center.astype(np.float32),
        'alignment_quality': {
            'mean_distance': float(mean_dist),
            'within_1mm_percent': float(within_1mm),
            'within_5mm_percent': float(within_5mm)
        }
    }
    
    # Cleanup
    del vertices, sampled_vertices, distances
    del centered_pc, centered_kps
    gc.collect()
    
    print(f"   ✅ 处理成功")
    return result

def create_large_f3_dataset():
    """Create large F3 dataset with robust processing"""
    
    print("🏗️ **创建大型F3数据集 (健壮版本)**")
    print("=" * 70)
    
    # Get all available samples
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    annotations_dir = data_dir / "annotations"
    
    xyz_files = list(annotations_dir.glob("*-Table-XYZ.CSV"))
    excluded_samples = {'600025', '600026', '600027'}  # Known LPS format
    
    valid_sample_ids = []
    for csv_file in xyz_files:
        sample_id = csv_file.stem.split('-')[0]
        if sample_id not in excluded_samples:
            valid_sample_ids.append(sample_id)
    
    print(f"📂 找到 {len(valid_sample_ids)} 个候选样本")
    
    # Process samples in batches
    batch_size = 10
    all_samples = []
    failed_samples = []
    
    for i in range(0, len(valid_sample_ids), batch_size):
        batch_ids = valid_sample_ids[i:i+batch_size]
        print(f"\n📦 处理批次 {i//batch_size + 1}/{(len(valid_sample_ids)-1)//batch_size + 1}: {len(batch_ids)} 样本")
        
        batch_results = []
        for sample_id in batch_ids:
            result = process_single_sample_robust(sample_id, 'F3')
            if result is not None:
                batch_results.append(result)
            else:
                failed_samples.append(sample_id)

        all_samples.extend(batch_results)
        batch_count = len(batch_results)

        # Cleanup after each batch
        del batch_results
        gc.collect()

        print(f"   📊 批次结果: {batch_count} 成功")
        print(f"   📈 累计成功: {len(all_samples)}")
        
        # Stop if we have enough samples
        if len(all_samples) >= 50:
            print(f"   🎯 已达到目标样本数，停止处理")
            break
    
    print(f"\n📊 **最终结果**")
    print(f"   成功样本: {len(all_samples)}")
    print(f"   失败样本: {len(failed_samples)}")
    
    if failed_samples:
        print(f"   失败列表: {failed_samples[:10]}{'...' if len(failed_samples) > 10 else ''}")
    
    return all_samples, failed_samples

def save_large_dataset(samples, failed_samples):
    """Save large dataset with quality statistics"""

    if len(samples) < 10:
        print(f"❌ 样本数量不足 ({len(samples)})，无法创建数据集")
        return None

    print(f"\n💾 **保存大型F3数据集**")

    # Calculate quality statistics
    qualities = [s['alignment_quality'] for s in samples]
    avg_dist = np.mean([q['mean_distance'] for q in qualities])
    avg_1mm = np.mean([q['within_1mm_percent'] for q in qualities])
    avg_5mm = np.mean([q['within_5mm_percent'] for q in qualities])

    print(f"   📈 质量统计:")
    print(f"      平均对齐距离: {avg_dist:.3f}mm")
    print(f"      平均1mm精度: {avg_1mm:.1f}%")
    print(f"      平均5mm精度: {avg_5mm:.1f}%")

    # Create metadata
    dataset_info = {
        'dataset_name': 'Large_F3_Robust_Dataset',
        'creation_date': str(pd.Timestamp.now()),
        'total_samples': len(samples),
        'failed_samples': len(failed_samples),
        'quality_stats': {
            'avg_distance_mm': float(avg_dist),
            'avg_1mm_percent': float(avg_1mm),
            'avg_5mm_percent': float(avg_5mm)
        },
        'sample_details': [
            {
                'sample_id': s['sample_id'],
                'alignment_quality': s['alignment_quality']
            }
            for s in samples
        ],
        'failed_sample_ids': failed_samples
    }

    # Save metadata
    with open('large_f3_dataset_info.json', 'w') as f:
        json.dump(dataset_info, f, indent=2)

    # Save training data
    np.savez_compressed('large_f3_dataset.npz',
                       sample_ids=[s['sample_id'] for s in samples],
                       point_clouds=np.stack([s['point_cloud'] for s in samples]),
                       keypoints=np.stack([s['keypoints'] for s in samples]),
                       centers=np.stack([s['center'] for s in samples]))

    print(f"   ✅ 数据已保存:")
    print(f"      📄 large_f3_dataset_info.json")
    print(f"      📦 large_f3_dataset.npz")
    print(f"      📊 {len(samples)} 个样本，每个4096点云+19关键点")

    return 'large_f3_dataset.npz'

def test_large_dataset(dataset_path):
    """Test loading the large dataset"""

    print(f"\n🧪 **测试大型数据集加载**")

    try:
        # Load data
        data = np.load(dataset_path, allow_pickle=True)

        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        centers = data['centers']

        print(f"   ✅ 成功加载数据:")
        print(f"      样本数: {len(sample_ids)}")
        print(f"      点云形状: {point_clouds.shape}")
        print(f"      关键点形状: {keypoints.shape}")
        print(f"      中心点形状: {centers.shape}")

        # Validate data integrity
        print(f"   🔍 数据完整性检查:")

        # Check for NaN or Inf
        if np.any(np.isnan(point_clouds)) or np.any(np.isinf(point_clouds)):
            print(f"      ❌ 点云数据包含无效值")
            return False

        if np.any(np.isnan(keypoints)) or np.any(np.isinf(keypoints)):
            print(f"      ❌ 关键点数据包含无效值")
            return False

        # Check data ranges
        pc_range = np.ptp(point_clouds, axis=(0,1))
        kp_range = np.ptp(keypoints, axis=(0,1))

        print(f"      点云范围: [{pc_range[0]:.1f}, {pc_range[1]:.1f}, {pc_range[2]:.1f}]")
        print(f"      关键点范围: [{kp_range[0]:.1f}, {kp_range[1]:.1f}, {kp_range[2]:.1f}]")

        print(f"   ✅ 数据完整性验证通过")

        # Show sample distribution
        print(f"   📋 样本分布:")
        for i in range(min(5, len(sample_ids))):
            print(f"      {sample_ids[i]}: 点云{point_clouds[i].shape}, 关键点{keypoints[i].shape}")

        if len(sample_ids) > 5:
            print(f"      ... 还有 {len(sample_ids)-5} 个样本")

        return True

    except Exception as e:
        print(f"   ❌ 加载失败: {e}")
        return False

if __name__ == "__main__":
    # Create large F3 dataset
    samples, failed = create_large_f3_dataset()

    if samples:
        # Save dataset
        dataset_path = save_large_dataset(samples, failed)

        if dataset_path:
            # Test loading
            success = test_large_dataset(dataset_path)

            if success:
                print(f"\n🎉 **大型F3数据集创建成功!**")
                print(f"📁 文件: {dataset_path}")
                print(f"📊 样本数: {len(samples)}")
                print(f"🎯 用途: F3关键点检测模型训练")
                print(f"💡 下一步: 使用此数据集训练基线模型")
            else:
                print(f"\n❌ **数据集验证失败**")
        else:
            print(f"\n❌ **数据集保存失败**")
    else:
        print(f"\n❌ **没有成功处理的样本**")
