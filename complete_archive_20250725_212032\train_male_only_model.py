#!/usr/bin/env python3
"""
训练男性专用模型
Train Male-Only Model
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import os
from tqdm import tqdm

class HeatmapRegressionNet(nn.Module):
    """Heatmap回归网络 (与成功的女性模型相同架构)"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super(HeatmapRegressionNet, self).__init__()
        
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 点云特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        # 全局特征
        self.global_conv = nn.Conv1d(512, 1024, 1)
        
        # 特征融合
        self.fusion_conv1 = nn.Conv1d(1024 + 256, 512, 1)
        self.fusion_conv2 = nn.Conv1d(512, 256, 1)
        
        # Heatmap生成
        self.heatmap_conv1 = nn.Conv1d(256, 128, 1)
        self.heatmap_conv2 = nn.Conv1d(128, 64, 1)
        self.heatmap_conv3 = nn.Conv1d(64, num_keypoints, 1)
        
        # 激活函数和正则化
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.3)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        # 点云特征提取
        x1 = self.relu(self.bn1(self.conv1(x)))
        x2 = self.relu(self.bn2(self.conv2(x1)))
        x3 = self.relu(self.bn3(self.conv3(x2)))
        x4 = self.relu(self.bn4(self.conv4(x3)))
        
        # 全局特征
        global_feat = self.relu(self.global_conv(x4))
        global_feat = torch.max(global_feat, 2, keepdim=True)[0]
        
        # 扩展全局特征
        global_feat_expanded = global_feat.repeat(1, 1, self.num_points)
        
        # 融合局部和全局特征
        combined_feat = torch.cat([x3, global_feat_expanded], 1)
        
        # 特征融合
        fused = self.relu(self.fusion_conv1(combined_feat))
        fused = self.dropout(fused)
        fused = self.relu(self.fusion_conv2(fused))
        
        # 生成热图
        heatmap = self.relu(self.heatmap_conv1(fused))
        heatmap = self.relu(self.heatmap_conv2(heatmap))
        heatmap = self.heatmap_conv3(heatmap)
        
        # 转置并应用softmax
        heatmap = heatmap.transpose(2, 1)
        
        # 对每个关键点的热图进行softmax
        heatmap_list = []
        for i in range(self.num_keypoints):
            hm_i = torch.softmax(heatmap[:, :, i], dim=1)
            heatmap_list.append(hm_i.unsqueeze(2))
        
        final_heatmap = torch.cat(heatmap_list, dim=2)
        
        return final_heatmap

def heatmap_loss_function(pred_heatmap, target_heatmap):
    """热图损失函数"""
    
    # 检查并调整target_heatmap的维度
    if len(target_heatmap.shape) == 3 and target_heatmap.shape[1] == 12:
        target_heatmap = target_heatmap.transpose(1, 2)
    
    # KL散度损失
    kl_loss = nn.KLDivLoss(reduction='batchmean')
    
    total_loss = 0
    batch_size, num_points, num_keypoints = pred_heatmap.shape
    
    for i in range(num_keypoints):
        log_pred = torch.log(pred_heatmap[:, :, i] + 1e-8)
        loss_i = kl_loss(log_pred, target_heatmap[:, :, i])
        total_loss += loss_i
    
    return total_loss / num_keypoints

def extract_keypoints_from_heatmap(heatmap, point_cloud):
    """从热图中提取关键点坐标"""
    
    batch_size, num_points, num_keypoints = heatmap.shape
    keypoints = torch.zeros(batch_size, num_keypoints, 3)
    
    for b in range(batch_size):
        for k in range(num_keypoints):
            weights = heatmap[b, :, k]
            weighted_coords = point_cloud[b] * weights.unsqueeze(1)
            keypoint = torch.sum(weighted_coords, dim=0) / torch.sum(weights)
            keypoints[b, k] = keypoint
    
    return keypoints

def load_male_dataset():
    """加载男性数据集"""
    
    print("📊 加载男性增强数据集...")
    
    # 加载男性数据
    male_data = np.load("f3_reduced_12kp_male_augmented.npz", allow_pickle=True)
    male_pc = male_data['point_clouds']
    male_kp = male_data['keypoints']
    
    print(f"✅ 男性数据: {len(male_pc)}个样本")
    
    # 生成热图
    print("🔥 生成热图...")
    male_hm = []
    for i in tqdm(range(len(male_pc)), desc="生成热图"):
        hm = generate_heatmap_from_keypoints(male_kp[i], male_pc[i])
        male_hm.append(hm)
    male_hm = np.array(male_hm)
    
    print(f"📊 男性数据集:")
    print(f"   总样本: {len(male_pc)}")
    print(f"   点云形状: {male_pc.shape}")
    print(f"   关键点形状: {male_kp.shape}")
    print(f"   热图形状: {male_hm.shape}")
    
    return male_pc, male_kp, male_hm

def generate_heatmap_from_keypoints(keypoints, point_cloud, sigma=5.0):
    """从关键点生成热图"""
    heatmaps = []
    
    for kp in keypoints:
        distances = np.linalg.norm(point_cloud - kp, axis=1)
        heatmap = np.exp(-distances**2 / (2 * sigma**2))
        
        if np.sum(heatmap) > 0:
            heatmap = heatmap / np.sum(heatmap)
        
        heatmaps.append(heatmap)
    
    return np.array(heatmaps)

def train_male_only_model():
    """训练男性专用模型"""
    
    print("🚀 开始训练男性专用Heatmap模型")
    print("🎯 目标: 达到与女性专用模型相似的精度")
    print("=" * 80)
    
    # 加载数据
    male_pc, male_kp, male_hm = load_male_dataset()
    
    # 数据分割
    n_samples = len(male_pc)
    n_train = int(n_samples * 0.7)
    n_val = int(n_samples * 0.15)
    
    indices = np.random.permutation(n_samples)
    train_indices = indices[:n_train]
    val_indices = indices[n_train:n_train + n_val]
    test_indices = indices[n_train + n_val:]
    
    train_pc, train_kp, train_hm = male_pc[train_indices], male_kp[train_indices], male_hm[train_indices]
    val_pc, val_kp, val_hm = male_pc[val_indices], male_kp[val_indices], male_hm[val_indices]
    test_pc, test_kp, test_hm = male_pc[test_indices], male_kp[test_indices], male_hm[test_indices]
    
    print(f"📋 数据分割:")
    print(f"   训练集: {len(train_indices)}个样本 (70%)")
    print(f"   验证集: {len(val_indices)}个样本 (15%)")
    print(f"   测试集: {len(test_indices)}个样本 (15%)")
    
    # 设备设置
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ 使用设备: {device}")
    
    # 模型初始化 (与女性模型相同架构)
    model = HeatmapRegressionNet(num_points=50000, num_keypoints=12)
    model = model.to(device)
    
    print(f"🏗️ 男性专用模型架构:")
    print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"   架构: 与成功的女性模型相同")
    
    # 优化器 (使用成功的配置)
    optimizer = optim.Adam(model.parameters(), lr=0.0005, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=15, gamma=0.7)
    
    # 训练参数
    num_epochs = 40
    batch_size = 4
    best_val_error = float('inf')
    patience = 10
    patience_counter = 0
    
    print(f"🎯 训练参数:")
    print(f"   训练轮数: {num_epochs}")
    print(f"   批次大小: {batch_size}")
    print(f"   学习率: 0.0005")
    print(f"   优化器: Adam")
    
    print(f"\n🚀 开始训练...")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        n_train_batches = len(train_pc) // batch_size
        
        train_pbar = tqdm(range(n_train_batches), desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for i in train_pbar:
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(train_pc))
            
            batch_pc = torch.FloatTensor(train_pc[start_idx:end_idx]).to(device)
            batch_hm = torch.FloatTensor(train_hm[start_idx:end_idx]).to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            pred_hm = model(batch_pc)
            loss = heatmap_loss_function(pred_hm, batch_hm)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_pbar.set_postfix({'Loss': f'{loss.item():.4f}'})
        
        avg_train_loss = train_loss / n_train_batches
        
        # 验证阶段
        model.eval()
        val_errors = []
        
        with torch.no_grad():
            n_val_batches = len(val_pc) // batch_size + (1 if len(val_pc) % batch_size > 0 else 0)
            
            for i in range(n_val_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(val_pc))
                
                batch_pc = torch.FloatTensor(val_pc[start_idx:end_idx]).to(device)
                batch_kp = torch.FloatTensor(val_kp[start_idx:end_idx]).to(device)
                
                pred_hm = model(batch_pc)
                pred_kp = extract_keypoints_from_heatmap(pred_hm.cpu(), batch_pc.cpu())
                
                for j in range(len(batch_kp)):
                    error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j].cpu(), dim=1))
                    val_errors.append(error.item())
        
        avg_val_error = np.mean(val_errors)
        
        # 学习率调度
        scheduler.step()
        
        print(f"\nEpoch {epoch+1}/{num_epochs}:")
        print(f"  训练损失: {avg_train_loss:.4f}")
        print(f"  验证误差: {avg_val_error:.2f}mm")
        print(f"  学习率: {scheduler.get_last_lr()[0]:.6f}")
        
        # 早停和模型保存
        if avg_val_error < best_val_error:
            best_val_error = avg_val_error
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'val_error': avg_val_error,
            }, 'best_male_only_model.pth')
            print(f"  ✅ 保存最佳模型 (验证误差: {avg_val_error:.2f}mm)")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f"  ⏹️ 早停触发 (耐心: {patience})")
                break
    
    return test_pc, test_kp

def main():
    """主函数"""
    
    print("🚀 训练男性专用模型")
    print("🎯 目标: 达到与女性专用模型(2.88mm)相似的精度")
    print("💡 策略: 使用相同的成功架构和训练配置")
    print("=" * 80)
    
    # 训练男性专用模型
    test_pc, test_kp = train_male_only_model()
    
    print(f"\n🧪 测试男性专用模型...")
    
    # 加载最佳模型进行测试
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    model = HeatmapRegressionNet(num_points=50000, num_keypoints=12)
    
    checkpoint = torch.load('best_male_only_model.pth')
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    # 测试
    test_errors = []
    
    batch_size = 4
    with torch.no_grad():
        n_test_batches = len(test_pc) // batch_size + (1 if len(test_pc) % batch_size > 0 else 0)
        
        for i in range(n_test_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(test_pc))
            
            batch_pc = torch.FloatTensor(test_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(test_kp[start_idx:end_idx]).to(device)
            
            pred_hm = model(batch_pc)
            pred_kp = extract_keypoints_from_heatmap(pred_hm.cpu(), batch_pc.cpu())
            
            for j in range(len(batch_kp)):
                error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j].cpu(), dim=1))
                test_errors.append(error.item())
    
    final_test_error = np.mean(test_errors)
    
    print(f"\n🎉 男性专用模型结果!")
    print(f"=" * 80)
    print(f"📊 最终测试结果:")
    print(f"   男性专用模型测试误差: {final_test_error:.2f}mm")
    
    print(f"\n📈 与其他模型对比:")
    print(f"   女性专用模型: 2.88mm")
    print(f"   男性专用模型: {final_test_error:.2f}mm")
    print(f"   简单优化模型 (男性): 4.74mm")
    print(f"   原始平衡模型 (男性): 4.86mm")
    
    # 性能分析
    if final_test_error < 3.0:
        print(f"🏆 优秀! 男性专用模型达到了与女性专用模型相似的精度!")
    elif final_test_error < 4.0:
        print(f"✅ 良好! 男性专用模型表现优于混合模型!")
    elif final_test_error < 5.0:
        print(f"👍 达标! 男性专用模型达到医疗级精度!")
    else:
        print(f"⚠️ 需要进一步优化")
    
    # 计算改进幅度
    improvement_vs_mixed = (4.74 - final_test_error) / 4.74 * 100
    print(f"\n💡 性能提升:")
    print(f"   相比混合模型男性性能: {improvement_vs_mixed:.1f}%提升")
    
    if final_test_error < 5.0:
        print(f"✅ 达到医疗级精度 (<5mm)")
    
    return final_test_error

if __name__ == "__main__":
    main()
