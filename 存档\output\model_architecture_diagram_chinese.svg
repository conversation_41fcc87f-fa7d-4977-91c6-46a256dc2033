<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1200" height="800" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">
    改进医疗PointNet架构
  </text>
  
  <!-- 输入数据部分 -->
  <g id="input-section">
    <!-- 原始数据 -->
    <rect x="50" y="80" width="120" height="60" fill="#bdc3c7" stroke="#95a5a6" stroke-width="2" rx="5"/>
    <text x="110" y="105" text-anchor="middle" font-family="Sim<PERSON><PERSON>, <PERSON><PERSON>, sans-serif" font-size="12" fill="#2c3e50">STL文件</text>
    <text x="110" y="120" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="12" fill="#2c3e50">点云数据</text>
    
    <rect x="50" y="160" width="120" height="60" fill="#bdc3c7" stroke="#95a5a6" stroke-width="2" rx="5"/>
    <text x="110" y="185" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="12" fill="#2c3e50">CSV文件</text>
    <text x="110" y="200" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="12" fill="#2c3e50">关键点标注</text>
    
    <!-- 改进：坐标系对齐 -->
    <rect x="220" y="120" width="140" height="80" fill="#3498db" stroke="#2980b9" stroke-width="3" rx="5"/>
    <text x="290" y="140" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold" fill="white">坐标系</text>
    <text x="290" y="155" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold" fill="white">一致性对齐</text>
    <text x="290" y="175" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" fill="#ecf0f1">✓ 解剖参考点</text>
    <text x="290" y="190" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" fill="#ecf0f1">✓ 智能归一化</text>
    
    <!-- 箭头 -->
    <path d="M 170 110 L 210 140" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
    <path d="M 170 190 L 210 170" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- 数据增强部分 -->
  <g id="augmentation-section">
    <rect x="400" y="120" width="140" height="80" fill="#e74c3c" stroke="#c0392b" stroke-width="3" rx="5"/>
    <text x="470" y="140" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold" fill="white">增强数据</text>
    <text x="470" y="155" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold" fill="white">增广策略</text>
    <text x="470" y="175" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" fill="#ecf0f1">✓ 4倍数据扩增</text>
    <text x="470" y="190" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" fill="#ecf0f1">✓ 医疗场景模拟</text>
    
    <!-- 箭头 -->
    <path d="M 360 160 L 390 160" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- PointNet 特征提取器 (原始部分) -->
  <g id="pointnet-backbone">
    <rect x="580" y="80" width="160" height="120" fill="#95a5a6" stroke="#7f8c8d" stroke-width="2" rx="5"/>
    <text x="660" y="105" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold" fill="white">PointNet骨干网络</text>
    <text x="660" y="125" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="11" fill="#ecf0f1">(原始架构)</text>
    
    <!-- 内部结构 -->
    <rect x="590" y="140" width="35" height="20" fill="#7f8c8d" rx="2"/>
    <text x="607" y="153" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="9" fill="white">卷积</text>
    
    <rect x="635" y="140" width="35" height="20" fill="#7f8c8d" rx="2"/>
    <text x="652" y="153" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="9" fill="white">卷积</text>
    
    <rect x="680" y="140" width="35" height="20" fill="#7f8c8d" rx="2"/>
    <text x="697" y="153" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="9" fill="white">池化</text>
    
    <rect x="610" y="170" width="80" height="20" fill="#7f8c8d" rx="2"/>
    <text x="650" y="183" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="9" fill="white">全局特征</text>
    
    <!-- 箭头 -->
    <path d="M 540 160 L 570 160" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- 改进：特征增强模块 -->
  <g id="feature-enhancement">
    <rect x="580" y="230" width="160" height="60" fill="#9b59b6" stroke="#8e44ad" stroke-width="3" rx="5"/>
    <text x="660" y="250" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold" fill="white">特征增强模块</text>
    <text x="660" y="270" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" fill="#ecf0f1">✓ 多尺度特征</text>
    <text x="660" y="285" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" fill="#ecf0f1">✓ 解剖注意力</text>
  </g>
  
  <!-- 改进：双头预测结构 -->
  <g id="dual-head">
    <!-- 关键点预测头 -->
    <rect x="800" y="80" width="140" height="80" fill="#27ae60" stroke="#229954" stroke-width="3" rx="5"/>
    <text x="870" y="105" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold" fill="white">关键点预测头</text>
    <text x="870" y="125" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" fill="#ecf0f1">✓ 57个关键点坐标</text>
    <text x="870" y="140" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" fill="#ecf0f1">✓ 医疗重要性加权</text>
    <text x="870" y="155" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" fill="#ecf0f1">输出: [57, 3]</text>
    
    <!-- 坐标范围预测头 -->
    <rect x="800" y="180" width="140" height="80" fill="#f39c12" stroke="#e67e22" stroke-width="3" rx="5"/>
    <text x="870" y="205" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold" fill="white">范围预测头</text>
    <text x="870" y="225" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" fill="#ecf0f1">✓ 空间约束</text>
    <text x="870" y="240" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" fill="#ecf0f1">✓ 边界预测</text>
    <text x="870" y="255" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" fill="#ecf0f1">输出: [6]</text>
    
    <!-- 箭头 -->
    <path d="M 740 120 L 790 120" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
    <path d="M 740 220 L 790 220" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- 改进：多任务损失函数 -->
  <g id="loss-function">
    <rect x="980" y="120" width="160" height="100" fill="#e67e22" stroke="#d35400" stroke-width="3" rx="5"/>
    <text x="1060" y="145" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold" fill="white">多任务损失函数</text>
    
    <!-- 损失组件 -->
    <rect x="990" y="160" width="65" height="18" fill="#d35400" rx="2"/>
    <text x="1022" y="172" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="8" fill="white">关键点损失</text>
    
    <rect x="1065" y="160" width="65" height="18" fill="#d35400" rx="2"/>
    <text x="1097" y="172" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="8" fill="white">范围损失</text>
    
    <rect x="990" y="185" width="65" height="18" fill="#d35400" rx="2"/>
    <text x="1022" y="197" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="8" fill="white">一致性损失</text>
    
    <rect x="1065" y="185" width="65" height="18" fill="#d35400" rx="2"/>
    <text x="1097" y="197" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="8" fill="white">医疗加权</text>
    
    <!-- 箭头 -->
    <path d="M 940 130 L 970 140" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
    <path d="M 940 230 L 970 200" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- 后处理校正部分 -->
  <g id="post-processing">
    <rect x="400" y="350" width="700" height="120" fill="#1abc9c" stroke="#16a085" stroke-width="3" rx="5"/>
    <text x="750" y="375" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold" fill="white">综合对齐校正</text>
    
    <!-- 四步校正 -->
    <rect x="420" y="390" width="130" height="60" fill="#16a085" rx="3"/>
    <text x="485" y="410" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" font-weight="bold" fill="white">步骤1: 全局</text>
    <text x="485" y="425" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" font-weight="bold" fill="white">质心对齐</text>
    <text x="485" y="440" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="9" fill="#ecf0f1">改善16%</text>
    
    <rect x="570" y="390" width="130" height="60" fill="#16a085" rx="3"/>
    <text x="635" y="410" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" font-weight="bold" fill="white">步骤2: 对称</text>
    <text x="635" y="425" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" font-weight="bold" fill="white">区域校正</text>
    <text x="635" y="440" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="9" fill="#ecf0f1">改善26%</text>
    
    <rect x="720" y="390" width="130" height="60" fill="#16a085" rx="3"/>
    <text x="785" y="410" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" font-weight="bold" fill="white">步骤3: 边缘-中心</text>
    <text x="785" y="425" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" font-weight="bold" fill="white">对齐</text>
    <text x="785" y="440" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="9" fill="#ecf0f1">改善19%</text>
    
    <rect x="870" y="390" width="130" height="60" fill="#16a085" rx="3"/>
    <text x="935" y="410" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" font-weight="bold" fill="white">步骤4: 精细</text>
    <text x="935" y="425" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="10" font-weight="bold" fill="white">调整</text>
    <text x="935" y="440" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="9" fill="#ecf0f1">最终优化</text>
    
    <!-- 连接箭头 -->
    <path d="M 550 420 L 560 420" stroke="white" stroke-width="2" marker-end="url(#whitearrow)"/>
    <path d="M 700 420 L 710 420" stroke="white" stroke-width="2" marker-end="url(#whitearrow)"/>
    <path d="M 850 420 L 860 420" stroke="white" stroke-width="2" marker-end="url(#whitearrow)"/>
  </g>
  
  <!-- 最终输出 -->
  <g id="final-output">
    <rect x="450" y="520" width="300" height="80" fill="#2c3e50" stroke="#34495e" stroke-width="2" rx="5"/>
    <text x="600" y="545" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold" fill="white">最终输出</text>
    <text x="600" y="570" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="14" fill="#ecf0f1">57个高精度关键点</text>
    <text x="600" y="590" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="12" fill="#1abc9c">平均误差0.63mm</text>
  </g>
  
  <!-- 性能对比 -->
  <g id="performance-comparison">
    <rect x="50" y="650" width="1100" height="120" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="5"/>
    <text x="600" y="675" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold" fill="#2c3e50">性能演进</text>
    
    <!-- 性能条 -->
    <rect x="80" y="690" width="200" height="25" fill="#e74c3c" rx="3"/>
    <text x="180" y="707" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="12" fill="white">基线: 4.5mm</text>
    
    <rect x="300" y="690" width="160" height="25" fill="#f39c12" rx="3"/>
    <text x="380" y="707" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="12" fill="white">+坐标系: 3.72mm</text>
    
    <rect x="480" y="690" width="140" height="25" fill="#27ae60" rx="3"/>
    <text x="550" y="707" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="12" fill="white">+架构: 3.21mm</text>
    
    <rect x="640" y="690" width="120" height="25" fill="#3498db" rx="3"/>
    <text x="700" y="707" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="12" fill="white">+增强: 2.02mm</text>
    
    <rect x="780" y="690" width="100" height="25" fill="#1abc9c" rx="3"/>
    <text x="830" y="707" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="12" fill="white">+校正: 0.63mm</text>
    
    <!-- 改善百分比 -->
    <text x="380" y="735" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="11" fill="#27ae60">↓17%</text>
    <text x="550" y="735" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="11" fill="#27ae60">↓14%</text>
    <text x="700" y="735" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="11" fill="#27ae60">↓37%</text>
    <text x="830" y="735" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="11" fill="#27ae60">↓69%</text>
    
    <text x="1000" y="707" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold" fill="#27ae60">总体改善86%</text>
  </g>
  
  <!-- 图例 -->
  <g id="legend">
    <rect x="50" y="300" width="300" height="120" fill="white" stroke="#bdc3c7" stroke-width="1" rx="5"/>
    <text x="200" y="320" text-anchor="middle" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold" fill="#2c3e50">图例</text>
    
    <rect x="70" y="330" width="15" height="15" fill="#95a5a6"/>
    <text x="95" y="342" font-family="SimHei, Arial, sans-serif" font-size="11" fill="#2c3e50">原始PointNet组件</text>
    
    <rect x="70" y="350" width="15" height="15" fill="#3498db"/>
    <text x="95" y="362" font-family="SimHei, Arial, sans-serif" font-size="11" fill="#2c3e50">坐标系改进</text>
    
    <rect x="70" y="370" width="15" height="15" fill="#27ae60"/>
    <text x="95" y="382" font-family="SimHei, Arial, sans-serif" font-size="11" fill="#2c3e50">架构增强</text>
    
    <rect x="70" y="390" width="15" height="15" fill="#1abc9c"/>
    <text x="95" y="402" font-family="SimHei, Arial, sans-serif" font-size="11" fill="#2c3e50">后处理校正</text>
  </g>
  
  <!-- 箭头定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
    <marker id="whitearrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="white"/>
    </marker>
  </defs>
  
  <!-- 连接线 -->
  <path d="M 750 290 L 750 340" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
  <path d="M 750 470 L 600 510" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
</svg>
