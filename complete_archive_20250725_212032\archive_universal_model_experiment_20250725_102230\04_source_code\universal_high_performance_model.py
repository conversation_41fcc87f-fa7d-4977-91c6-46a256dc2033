#!/usr/bin/env python3
"""
通用高性能模型
Universal High-Performance Model
基于MutualAssistanceNet的通用架构，适用于男女数据
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import json
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

# 导入基础架构
from keypoint_mutual_assistance import MutualAssistanceNet, AnatomicalConstraintLoss

class UniversalMutualAssistanceNet(nn.Module):
    """通用相互辅助网络 - 基于成功的男性架构"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super().__init__()
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 基础特征提取 (来自成功的MutualAssistanceNet)
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.<PERSON><PERSON><PERSON>(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Conv1d(256, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
        )
        
        # 性别自适应特征层 (轻量级)
        self.gender_adaptive = nn.Sequential(
            nn.Conv1d(512, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.1)  # 轻微dropout
        )
        
        # 全局特征聚合
        self.global_aggregation = nn.Sequential(
            nn.Conv1d(512, 1024, 1),
            nn.BatchNorm1d(1024),
            nn.ReLU(),
        )
        
        # 初始关键点预测
        self.initial_predictor = nn.Sequential(
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, num_keypoints * 3)
        )
        
        # 相互辅助模块 (核心创新)
        self.mutual_assistance = MutualAssistanceModule(num_keypoints)
        
        # 最终精化模块
        self.refinement = nn.Sequential(
            nn.Linear(num_keypoints * 3, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Linear(128, num_keypoints * 3)
        )
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取
        features = self.feature_extractor(x)  # [B, 512, N]
        
        # 性别自适应
        adapted_features = self.gender_adaptive(features)  # [B, 512, N]
        
        # 全局特征
        global_features = self.global_aggregation(adapted_features)  # [B, 1024, N]
        global_feat = torch.max(global_features, 2)[0]  # [B, 1024]
        
        # 初始预测
        initial_kp = self.initial_predictor(global_feat)  # [B, num_keypoints*3]
        initial_kp = initial_kp.view(batch_size, self.num_keypoints, 3)
        
        # 相互辅助
        assisted_kp = self.mutual_assistance(initial_kp, global_feat)
        
        # 最终精化
        refined_input = assisted_kp.view(batch_size, -1)
        refinement = self.refinement(refined_input)
        refinement = refinement.view(batch_size, self.num_keypoints, 3)
        
        # 残差连接
        final_kp = assisted_kp + refinement
        
        return final_kp, assisted_kp, initial_kp

class MutualAssistanceModule(nn.Module):
    """相互辅助模块 - 关键点之间相互帮助定位"""
    
    def __init__(self, num_keypoints):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        # 关键点关系建模
        self.relation_net = nn.Sequential(
            nn.Linear(num_keypoints * 3, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Linear(128, num_keypoints * num_keypoints)  # 关系矩阵
        )
        
        # 辅助预测网络
        self.assistance_net = nn.Sequential(
            nn.Linear(1024 + num_keypoints * 3, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Linear(256, num_keypoints * 3)
        )
        
    def forward(self, initial_kp, global_feat):
        batch_size = initial_kp.size(0)
        
        # 计算关键点关系
        kp_flat = initial_kp.view(batch_size, -1)
        relations = self.relation_net(kp_flat)
        relations = relations.view(batch_size, self.num_keypoints, self.num_keypoints)
        relations = torch.softmax(relations, dim=-1)
        
        # 相互辅助
        assisted_features = torch.bmm(relations, initial_kp)  # [B, num_kp, 3]
        
        # 结合全局特征进行辅助预测
        combined_input = torch.cat([global_feat, kp_flat], dim=1)
        assistance = self.assistance_net(combined_input)
        assistance = assistance.view(batch_size, self.num_keypoints, 3)
        
        # 融合初始预测和辅助预测
        assisted_kp = initial_kp + 0.3 * assistance + 0.2 * assisted_features
        
        return assisted_kp

class UniversalConstraintLoss(nn.Module):
    """通用约束损失函数 - 适用于男女数据"""
    
    def __init__(self, alpha=1.0, beta=0.3, gamma=0.2, delta=0.2):
        super().__init__()
        self.alpha = alpha  # 基础MSE权重
        self.beta = beta    # 距离约束权重
        self.gamma = gamma  # 对称性约束权重
        self.delta = delta  # 解剖学约束权重
        
    def forward(self, predicted, target):
        batch_size = predicted.size(0)
        
        # 1. 基础MSE损失
        mse_loss = nn.MSELoss()(predicted, target)
        
        # 2. 距离约束损失 (关键点间距离应该合理)
        distance_loss = self.compute_distance_constraint(predicted, target)
        
        # 3. 对称性约束损失 (左右对称的关键点)
        symmetry_loss = self.compute_symmetry_constraint(predicted, target)
        
        # 4. 解剖学约束损失 (解剖学合理性)
        anatomical_loss = self.compute_anatomical_constraint(predicted, target)
        
        # 总损失
        total_loss = (self.alpha * mse_loss + 
                     self.beta * distance_loss + 
                     self.gamma * symmetry_loss + 
                     self.delta * anatomical_loss)
        
        return total_loss
    
    def compute_distance_constraint(self, predicted, target):
        """距离约束"""
        # 计算预测和真实的关键点间距离
        pred_distances = torch.cdist(predicted, predicted, p=2)
        target_distances = torch.cdist(target, target, p=2)
        
        # 距离应该保持一致
        distance_loss = nn.MSELoss()(pred_distances, target_distances)
        return distance_loss
    
    def compute_symmetry_constraint(self, predicted, target):
        """对称性约束 (简化版)"""
        # 假设前6个点是左侧，后6个点是右侧
        if predicted.size(1) >= 12:
            left_pred = predicted[:, :6, :]
            right_pred = predicted[:, 6:12, :]
            left_target = target[:, :6, :]
            right_target = target[:, 6:12, :]
            
            # 对称性损失
            pred_sym_loss = nn.MSELoss()(left_pred[:, :, [0, 2]], -right_pred[:, :, [0, 2]])
            target_sym_loss = nn.MSELoss()(left_target[:, :, [0, 2]], -right_target[:, :, [0, 2]])
            
            return abs(pred_sym_loss - target_sym_loss)
        else:
            return torch.tensor(0.0, device=predicted.device)
    
    def compute_anatomical_constraint(self, predicted, target):
        """解剖学约束"""
        # 确保关键点在合理的解剖学范围内
        # 这里使用简化的范围约束
        anatomical_loss = torch.tensor(0.0, device=predicted.device)
        
        # 检查关键点是否在合理范围内
        for i in range(predicted.size(1)):
            # 每个关键点应该在合理的3D空间范围内
            point_range_loss = torch.clamp(torch.norm(predicted[:, i, :], dim=1) - 200, min=0)
            anatomical_loss += torch.mean(point_range_loss)
        
        return anatomical_loss

class UniversalModelTrainer:
    """通用模型训练器"""
    
    def __init__(self, device='cuda:1'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        self.training_history = []
        
    def load_combined_data(self):
        """加载合并的男女数据"""
        print("📥 加载合并的男女数据")
        print("=" * 50)
        
        try:
            # 加载女性数据
            female_data = np.load('archive/old_experiments/f3_reduced_12kp_female.npz')
            female_pc = female_data['point_clouds']
            female_kp = female_data['keypoints']
            
            # 加载男性数据
            male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz')
            male_pc = male_data['point_clouds']
            male_kp = male_data['keypoints']
            
            # 合并数据
            all_pc = np.vstack([female_pc, male_pc])
            all_kp = np.vstack([female_kp, male_kp])
            
            # 创建性别标签 (0=女性, 1=男性)
            gender_labels = np.concatenate([
                np.zeros(len(female_pc)),
                np.ones(len(male_pc))
            ])
            
            print(f"✅ 数据加载成功:")
            print(f"   女性: {len(female_pc)}样本")
            print(f"   男性: {len(male_pc)}样本")
            print(f"   总计: {len(all_pc)}样本")
            
            return all_pc, all_kp, gender_labels, female_pc, female_kp, male_pc, male_kp
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return None
    
    def train_universal_model(self, train_pc, train_kp, test_pc, test_kp, 
                            test_female_pc, test_female_kp, test_male_pc, test_male_kp):
        """训练通用模型"""
        print(f"\n🎯 训练通用高性能模型")
        print("=" * 50)
        
        # 创建通用模型
        model = UniversalMutualAssistanceNet(num_points=50000, num_keypoints=12).to(self.device)
        criterion = UniversalConstraintLoss(alpha=1.0, beta=0.3, gamma=0.2, delta=0.2)
        optimizer = optim.AdamW(model.parameters(), lr=0.0001, weight_decay=1e-4)
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=100, eta_min=1e-6)
        
        print(f"🏗️ 通用模型:")
        print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
        print(f"   训练样本: {len(train_pc)}")
        print(f"   测试样本: {len(test_pc)}")
        
        # 转换为张量
        train_pc_tensor = torch.FloatTensor(train_pc).to(self.device)
        train_kp_tensor = torch.FloatTensor(train_kp).to(self.device)
        test_pc_tensor = torch.FloatTensor(test_pc).to(self.device)
        test_kp_tensor = torch.FloatTensor(test_kp).to(self.device)
        
        # 创建数据加载器 (确保batch_size不会导致单样本批次)
        batch_size = min(4, len(train_pc) // 4) if len(train_pc) >= 8 else 2
        train_dataset = TensorDataset(train_pc_tensor, train_kp_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        
        # 训练循环
        model.train()
        best_loss = float('inf')
        patience = 0
        
        for epoch in range(100):
            epoch_loss = 0.0
            
            for batch_pc, batch_kp in train_loader:
                optimizer.zero_grad()
                
                final_kp, assisted_kp, initial_kp = model(batch_pc)
                loss = criterion(final_kp, batch_kp)
                
                loss.backward()
                optimizer.step()
                epoch_loss += loss.item()
            
            scheduler.step()
            avg_loss = epoch_loss / len(train_loader)
            
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience = 0
                torch.save(model.state_dict(), 'best_universal_model.pth')
            else:
                patience += 1
                if patience >= 15:
                    print(f"早停于epoch {epoch+1}")
                    break
            
            if epoch % 20 == 0:
                print(f"Epoch {epoch+1}: Loss = {avg_loss:.6f}")
        
        # 加载最佳模型并测试
        model.load_state_dict(torch.load('best_universal_model.pth'))
        model.eval()
        
        # 整体测试
        with torch.no_grad():
            final_kp, _, _ = model(test_pc_tensor)
            test_errors = torch.norm(final_kp - test_kp_tensor, dim=2)
            overall_error = torch.mean(test_errors).item()
            
            # 分性别测试
            if len(test_female_pc) > 0:
                female_pc_tensor = torch.FloatTensor(test_female_pc).to(self.device)
                female_kp_tensor = torch.FloatTensor(test_female_kp).to(self.device)
                female_pred, _, _ = model(female_pc_tensor)
                female_errors = torch.norm(female_pred - female_kp_tensor, dim=2)
                female_error = torch.mean(female_errors).item()
            else:
                female_error = 0
            
            if len(test_male_pc) > 0:
                male_pc_tensor = torch.FloatTensor(test_male_pc).to(self.device)
                male_kp_tensor = torch.FloatTensor(test_male_kp).to(self.device)
                male_pred, _, _ = model(male_pc_tensor)
                male_errors = torch.norm(male_pred - male_kp_tensor, dim=2)
                male_error = torch.mean(male_errors).item()
            else:
                male_error = 0
            
            # 计算准确率
            sample_errors = torch.mean(test_errors, dim=1)
            errors_5mm = torch.sum(sample_errors <= 5.0).item()
            errors_10mm = torch.sum(sample_errors <= 10.0).item()
            
            acc_5mm = (errors_5mm / len(test_pc)) * 100
            acc_10mm = (errors_10mm / len(test_pc)) * 100
        
        result = {
            'model_type': 'universal_mutual_assistance',
            'train_samples': len(train_pc),
            'test_samples': len(test_pc),
            'overall_error': overall_error,
            'female_error': female_error,
            'male_error': male_error,
            'accuracy_5mm': acc_5mm,
            'accuracy_10mm': acc_10mm,
            'medical_grade': overall_error <= 10.0,
            'excellent_grade': overall_error <= 5.0
        }
        
        print(f"\n📊 通用模型结果:")
        print(f"   训练样本: {result['train_samples']}")
        print(f"   测试样本: {result['test_samples']}")
        print(f"   整体误差: {result['overall_error']:.2f}mm")
        print(f"   女性误差: {result['female_error']:.2f}mm")
        print(f"   男性误差: {result['male_error']:.2f}mm")
        print(f"   5mm准确率: {result['accuracy_5mm']:.1f}%")
        print(f"   10mm准确率: {result['accuracy_10mm']:.1f}%")
        print(f"   医疗级达标: {'✅' if result['medical_grade'] else '❌'}")
        print(f"   优秀级达标: {'✅' if result['excellent_grade'] else '❌'}")
        
        return result
    
    def run_universal_experiment(self):
        """运行通用模型实验"""
        print("🚀 开始通用高性能模型实验")
        print("基于成功的MutualAssistanceNet架构")
        print("=" * 70)
        
        # 加载数据
        data_result = self.load_combined_data()
        if data_result is None:
            print("❌ 数据加载失败，退出")
            return
        
        all_pc, all_kp, gender_labels, female_pc, female_kp, male_pc, male_kp = data_result
        
        # 数据分割 (保持性别比例)
        train_pc, test_pc, train_kp, test_kp = train_test_split(
            all_pc, all_kp, test_size=0.2, random_state=42, stratify=gender_labels)
        
        # 分离测试集中的男女数据
        test_gender = gender_labels[len(train_pc):]
        test_female_indices = test_gender == 0
        test_male_indices = test_gender == 1
        
        test_female_pc = test_pc[test_female_indices] if np.any(test_female_indices) else np.array([])
        test_female_kp = test_kp[test_female_indices] if np.any(test_female_indices) else np.array([])
        test_male_pc = test_pc[test_male_indices] if np.any(test_male_indices) else np.array([])
        test_male_kp = test_kp[test_male_indices] if np.any(test_male_indices) else np.array([])
        
        print(f"📊 数据分割:")
        print(f"   训练: {len(train_pc)}样本")
        print(f"   测试: {len(test_pc)}样本 (女性{len(test_female_pc)}, 男性{len(test_male_pc)})")
        
        # 训练通用模型
        result = self.train_universal_model(
            train_pc, train_kp, test_pc, test_kp,
            test_female_pc, test_female_kp, test_male_pc, test_male_kp)
        
        # 保存结果
        with open('universal_model_results.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 通用模型结果已保存到 universal_model_results.json")
        return result

def main():
    """主函数"""
    print("🎯 通用高性能模型实验")
    print("Universal High-Performance Model Experiment")
    print("=" * 60)
    
    # 创建训练器
    trainer = UniversalModelTrainer()
    
    # 运行通用模型实验
    result = trainer.run_universal_experiment()
    
    if result:
        print(f"\n🎉 通用模型实验完成:")
        print(f"✅ 整体性能: {result['overall_error']:.2f}mm")
        print(f"✅ 女性性能: {result['female_error']:.2f}mm")
        print(f"✅ 男性性能: {result['male_error']:.2f}mm")
        print(f"🎯 性别平衡度: {abs(result['female_error'] - result['male_error']):.2f}mm差异")
    else:
        print("❌ 实验过程中出现问题")

if __name__ == "__main__":
    main()
