#!/usr/bin/env python3
"""
稳健集成双Softmax训练脚本
基于测试集分析，专注于提高泛化能力
目标: 从7.579mm优化到6.0mm以下，确保验证集和测试集一致性
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random
from improved_ensemble_robust import RobustEnsemblePointNet, RobustAugmentationDataset, calculate_metrics

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class RobustLoss(nn.Module):
    """稳健损失函数 - 减少对异常值的敏感性"""
    
    def __init__(self, alpha=0.6, beta=0.4, huber_delta=1.0):
        super(RobustLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
        self.huber_delta = huber_delta
    
    def forward(self, pred, target):
        # 使用Huber损失减少对异常值的敏感性
        huber_loss = F.huber_loss(pred, target, delta=self.huber_delta)
        smooth_l1_loss = F.smooth_l1_loss(pred, target)
        total_loss = self.alpha * huber_loss + self.beta * smooth_l1_loss
        return total_loss

def train_robust_ensemble():
    """训练稳健集成双Softmax模型"""
    
    print(f"🚀 **稳健集成双Softmax训练**")
    print(f"🔧 **改进**: 增强正则化 + 稳健损失")
    print(f"📊 **基于**: 测试集7.579mm分析结果")
    print(f"🎯 **目标**: 优化到6.0mm以下，确保泛化能力")
    print(f"🔍 **重点**: 验证集和测试集性能一致性")
    print("=" * 80)
    
    set_seed(42)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 数据集 (使用稳健数据增强)
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    train_dataset = RobustAugmentationDataset('f3_reduced_12kp_stable.npz', 'train', 
                                            num_points=4096, test_samples=test_samples, 
                                            augment=True, seed=42)
    val_dataset = RobustAugmentationDataset('f3_reduced_12kp_stable.npz', 'val', 
                                          num_points=4096, test_samples=test_samples, 
                                          augment=False, seed=42)
    
    # 测试集 (用于最终验证)
    test_dataset = RobustAugmentationDataset('f3_reduced_12kp_stable.npz', 'test', 
                                           num_points=4096, test_samples=test_samples, 
                                           augment=False, seed=42)
    
    batch_size = 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    
    # 稳健模型
    model = RobustEnsemblePointNet(num_keypoints=12, dropout_rate=0.4, num_ensembles=3).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 模型参数: {total_params:,}")
    
    # 稳健损失函数
    criterion = RobustLoss(alpha=0.6, beta=0.4, huber_delta=1.0)
    
    # 优化器 (更保守的学习率)
    optimizer = optim.AdamW(model.parameters(), lr=0.0005, weight_decay=2e-4)  # 更强的正则化
    
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.8, patience=8, min_lr=1e-6  # 更敏感的调度
    )
    
    num_epochs = 150  # 增加训练轮数
    best_val_error = float('inf')
    best_test_error = float('inf')
    patience = 30  # 增加耐心
    patience_counter = 0
    history = []
    min_delta = 0.002  # 更小的改进阈值
    
    print(f"🎯 训练配置: 稳健集成双Softmax")
    print(f"   正则化: Dropout 0.4, 权重衰减 2e-4")
    print(f"   损失函数: Huber(0.6) + SmoothL1(0.4)")
    print(f"   学习率: 0.0005 (保守)")
    print(f"   集成模块: 3个 (稳健参数)")
    print(f"   候选点数: 200 (减少过拟合)")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 40)
        
        # 训练
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        for batch in train_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)  # 更强的梯度裁剪
                optimizer.step()
                
                train_loss += loss.item()
                
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                        
            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue
        
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # 验证 (使用稳健的集成双Softmax精细化)
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)  # 推理时使用稳健集成双Softmax
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # 每10轮在测试集上评估一次
        test_metrics = None
        if epoch % 10 == 0 or epoch == num_epochs - 1:
            test_loss = 0.0
            test_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
            
            with torch.no_grad():
                for batch in test_loader:
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                    
                    point_cloud = batch['point_cloud'].to(device)
                    keypoints = batch['keypoints'].to(device)
                    
                    try:
                        pred_keypoints = model(point_cloud)
                        loss = criterion(pred_keypoints, keypoints)
                        test_loss += loss.item()
                        
                        metrics = calculate_metrics(pred_keypoints, keypoints)
                        for key in test_metrics:
                            test_metrics[key] += metrics[key]
                            
                    except RuntimeError as e:
                        continue
            
            test_loss /= len(test_loader)
            for key in test_metrics:
                test_metrics[key] /= len(test_loader)
        
        # 学习率调度
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 打印结果
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"5mm={train_metrics['within_5mm_percent']:.1f}%, 7mm={train_metrics['within_7mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"5mm={val_metrics['within_5mm_percent']:.1f}%, 7mm={val_metrics['within_7mm_percent']:.1f}%")
        
        if test_metrics:
            print(f"测试: Loss={test_loss:.4f}, 误差={test_metrics['mean_distance']:.3f}mm, "
                  f"5mm={test_metrics['within_5mm_percent']:.1f}%, 7mm={test_metrics['within_7mm_percent']:.1f}%")
            print(f"一致性: 验证vs测试差异={abs(val_metrics['mean_distance'] - test_metrics['mean_distance']):.3f}mm")
        
        print(f"学习率: {current_lr:.2e}")
        
        # 保存历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'test_metrics': test_metrics,
            'learning_rate': current_lr
        })
        
        # 检查改进 (同时考虑验证集和测试集)
        current_val_error = val_metrics['mean_distance']
        current_test_error = test_metrics['mean_distance'] if test_metrics else float('inf')
        
        # 以验证集为主，但监控测试集一致性
        improvement = best_val_error - current_val_error
        
        if improvement > min_delta:
            best_val_error = current_val_error
            if test_metrics:
                best_test_error = current_test_error
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'best_test_error': best_test_error,
                'val_metrics': val_metrics,
                'test_metrics': test_metrics,
                'model_name': 'Robust_Ensemble_Double_SoftMax',
                'config': 'robust_generalization_focused'
            }, f'best_robust_ensemble_{best_val_error:.3f}mm.pth')
            
            print(f"🎉 新最佳! 验证误差: {best_val_error:.3f}mm (改进{improvement:.3f}mm)")
            if test_metrics:
                print(f"   对应测试误差: {best_test_error:.3f}mm")
                consistency = abs(best_val_error - best_test_error)
                if consistency < 1.0:
                    print(f"✅ 良好一致性: 差异{consistency:.3f}mm")
                else:
                    print(f"⚠️  一致性待改进: 差异{consistency:.3f}mm")
            
            if best_val_error <= 6.0:
                print(f"🎯 **达到6.0mm目标!**")
                if test_metrics and best_test_error <= 6.5:
                    print(f"🏆 **测试集也表现良好!**")
        else:
            patience_counter += 1
            print(f"⏳ 无显著改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停触发")
            break
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    
    # 保存结果
    results = {
        'method': 'Robust Ensemble Double SoftMax',
        'baseline_test_error': 7.579,
        'best_val_error': float(best_val_error),
        'best_test_error': float(best_test_error),
        'improvement_vs_baseline': float((7.579 - best_test_error) / 7.579 * 100),
        'val_test_consistency': float(abs(best_val_error - best_test_error)),
        'training_time_minutes': float(total_time / 60),
        'epochs_trained': len(history),
        'history': history,
        'robust_config': {
            'dropout_rate': 0.4,
            'weight_decay': 2e-4,
            'learning_rate': 0.0005,
            'loss_function': 'Huber(0.6) + SmoothL1(0.4)',
            'num_ensembles': 3,
            'candidate_points': 200,
            'augmentation': 'conservative'
        }
    }
    
    with open('robust_ensemble_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 **稳健集成双Softmax训练完成!**")
    print(f"📊 基线测试误差: 7.579mm")
    print(f"🎯 稳健验证误差: {best_val_error:.3f}mm")
    print(f"🎯 稳健测试误差: {best_test_error:.3f}mm")
    print(f"📈 vs基线改进: {(7.579 - best_test_error) / 7.579 * 100:.1f}%")
    print(f"🔍 一致性检查: {abs(best_val_error - best_test_error):.3f}mm")
    print(f"⏱️  训练时间: {total_time/60:.1f}分钟")
    
    if best_test_error <= 6.0:
        print(f"🏆 **成功达到6.0mm目标!**")
    elif best_test_error < 7.0:
        print(f"🎯 **显著改进!** 接近目标")
    elif best_test_error < 7.579:
        print(f"✅ **有所改进!** 稳健策略有效")
    else:
        print(f"💡 **需要进一步优化** 稳健策略")
    
    return best_val_error, best_test_error, results

if __name__ == "__main__":
    set_seed(42)
    best_val, best_test, results = train_robust_ensemble()
