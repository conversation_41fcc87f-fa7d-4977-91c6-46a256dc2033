#!/usr/bin/env python3
"""
测试高级增强数据集的效果
Test advanced enhanced dataset performance
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
import json
from tqdm import tqdm

class AdaptivePointNet57(nn.Module):
    """自适应PointNet57"""
    
    def __init__(self, num_keypoints=57, dropout_rate=0.25):
        super(AdaptivePointNet57, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 残差连接
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        # 回归头
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, num_keypoints * 3)
        
        # 批归一化
        self.fc_bn1 = nn.BatchNorm1d(512)
        self.fc_bn2 = nn.BatchNorm1d(256)
        self.fc_bn3 = nn.BatchNorm1d(128)
        
        # Dropout
        self.dropout = nn.Dropout(dropout_rate)
        
        # 权重初始化
        self._initialize_weights()
        
    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取 + 残差连接
        x1 = F.relu(self.bn1(self.conv1(x)))
        x2 = F.relu(self.bn2(self.conv2(x1)))
        x3 = F.relu(self.bn3(self.conv3(x2)))
        x3_res = x3 + self.residual1(x1)
        
        x4 = F.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = F.relu(self.bn5(self.conv5(x4_res)))
        
        # 全局最大池化
        global_feat = torch.max(x5, 2)[0]
        
        # 回归
        x = F.relu(self.fc_bn1(self.fc1(global_feat)))
        x = self.dropout(x)
        x = F.relu(self.fc_bn2(self.fc2(x)))
        x = self.dropout(x)
        x = F.relu(self.fc_bn3(self.fc3(x)))
        x = self.dropout(x)
        
        keypoints = self.fc4(x)
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

class EnhancedDataset57(Dataset):
    def __init__(self, point_clouds, keypoints):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def train_enhanced_model(model, train_loader, val_loader, epochs=120, device='cuda'):
    """训练增强模型"""
    
    print(f"🚀 训练高级增强模型...")
    
    model = model.to(device)
    
    # 优化器配置
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=30, T_mult=2, eta_min=1e-7
    )
    
    criterion = nn.MSELoss()
    
    history = {'train_loss': [], 'val_loss': [], 'train_error': [], 'val_error': []}
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 25
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
            
            optimizer.step()
            
            train_loss += loss.item()
            
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                val_loss += loss.item()
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_error'].append(train_error)
        history['val_error'].append(val_error)
        
        scheduler.step()
        
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_advanced_enhanced_model.pth')
        else:
            patience_counter += 1
        
        current_lr = optimizer.param_groups[0]['lr']
        
        if epoch % 10 == 0 or epoch < 5:
            print(f"Epoch {epoch+1:3d}: "
                  f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
                  f"Train Error: {train_error:.4f}, Val Error: {val_error:.4f}, "
                  f"LR: {current_lr:.2e}")
        
        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    model.load_state_dict(torch.load('best_advanced_enhanced_model.pth'))
    return history

def test_enhanced_model(model, test_loader, scalers, test_indices, device='cuda'):
    """测试增强模型"""
    
    print("🔍 测试高级增强模型...")
    
    model = model.to(device)
    model.eval()
    
    test_predictions = []
    test_targets = []
    
    with torch.no_grad():
        for batch_pc, batch_kp in test_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            predicted = model(batch_pc)
            
            test_predictions.append(predicted.cpu().numpy())
            test_targets.append(batch_kp.cpu().numpy())
    
    test_predictions = np.vstack(test_predictions)
    test_targets = np.vstack(test_targets)
    
    # 反归一化
    real_predictions = []
    real_targets = []
    
    for i, orig_idx in enumerate(test_indices):
        if i < len(test_predictions):
            pred_norm = test_predictions[i]
            target_norm = test_targets[i]
            
            scaler = scalers[orig_idx]
            
            # 反归一化
            dummy_pc = np.zeros((50000, 3))
            
            combined_pred = np.vstack([dummy_pc, pred_norm])
            combined_pred_denorm = scaler.inverse_transform(combined_pred)
            pred_real = combined_pred_denorm[50000:]
            
            combined_target = np.vstack([dummy_pc, target_norm])
            combined_target_denorm = scaler.inverse_transform(combined_target)
            target_real = combined_target_denorm[50000:]
            
            real_predictions.append(pred_real)
            real_targets.append(target_real)
    
    real_predictions = np.array(real_predictions)
    real_targets = np.array(real_targets)
    
    # 计算误差
    total_error = 0.0
    region_errors = {'F1': [], 'F2': [], 'F3': []}
    all_errors = []
    
    for i in range(len(real_predictions)):
        pred = real_predictions[i]
        target = real_targets[i]
        
        distances = np.linalg.norm(pred - target, axis=1)
        total_error += np.mean(distances)
        all_errors.extend(distances)
        
        # 分区域
        region_errors['F1'].extend(distances[0:19])
        region_errors['F2'].extend(distances[19:38])
        region_errors['F3'].extend(distances[38:57])
    
    avg_error = total_error / len(real_predictions)
    
    # 计算准确率
    accuracy_5mm = np.mean(np.array(all_errors) < 5.0) * 100
    accuracy_10mm = np.mean(np.array(all_errors) < 10.0) * 100
    
    print(f"\n🎯 高级增强模型结果:")
    print(f"   整体平均误差: {avg_error:.2f}mm")
    
    for region, errors in region_errors.items():
        if errors:
            mean_error = np.mean(errors)
            std_error = np.std(errors)
            print(f"   {region}区域: {mean_error:.2f}±{std_error:.2f}mm")
    
    print(f"   医疗级准确率:")
    print(f"     <5mm: {accuracy_5mm:.1f}%")
    print(f"     <10mm: {accuracy_10mm:.1f}%")
    
    return avg_error, region_errors, accuracy_5mm, accuracy_10mm

def main():
    """主函数"""
    
    print("🎯 测试高级增强数据集效果")
    print("验证解剖学一致性分析 + 智能数据增强的效果")
    print("=" * 80)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载高级增强数据集
    print("📊 加载高级增强数据集...")
    data = np.load('enhanced_high_quality_57_dataset.npz', allow_pickle=True)
    
    point_clouds = data['point_clouds']
    keypoints_57 = data['keypoints_57']
    sample_ids = data['sample_ids']
    train_indices = data['train_indices']
    val_indices = data['val_indices']
    test_indices = data['test_indices']
    
    print(f"✅ 数据集加载完成:")
    print(f"   总样本: {len(sample_ids)}")
    print(f"   训练集: {len(train_indices)} (包含增强)")
    print(f"   验证集: {len(val_indices)} (仅原始)")
    print(f"   测试集: {len(test_indices)} (仅原始)")
    
    # 数据归一化
    print("🔧 执行数据归一化...")
    normalized_pc = []
    normalized_kp = []
    scalers = []
    
    for i in range(len(point_clouds)):
        pc = point_clouds[i].copy()
        kp = keypoints_57[i].copy()
        
        combined_data = np.vstack([pc, kp])
        scaler = StandardScaler()
        combined_normalized = scaler.fit_transform(combined_data)
        
        pc_normalized = combined_normalized[:len(pc)]
        kp_normalized = combined_normalized[len(pc):]
        
        normalized_pc.append(pc_normalized)
        normalized_kp.append(kp_normalized)
        scalers.append(scaler)
    
    normalized_pc = np.array(normalized_pc)
    normalized_kp = np.array(normalized_kp)
    
    # 创建数据集
    train_dataset = EnhancedDataset57(
        normalized_pc[train_indices], 
        normalized_kp[train_indices]
    )
    val_dataset = EnhancedDataset57(
        normalized_pc[val_indices], 
        normalized_kp[val_indices]
    )
    test_dataset = EnhancedDataset57(
        normalized_pc[test_indices], 
        normalized_kp[test_indices]
    )
    
    # 数据加载器
    batch_size = 8
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    
    print(f"📋 数据加载器创建完成")
    
    # 创建自适应模型
    model = AdaptivePointNet57(num_keypoints=57, dropout_rate=0.25)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🏗️ AdaptivePointNet57: {total_params:,} 参数")
    
    # 训练模型
    history = train_enhanced_model(model, train_loader, val_loader, epochs=120, device=device)
    
    # 测试模型
    avg_error, region_errors, acc_5mm, acc_10mm = test_enhanced_model(
        model, test_loader, scalers, test_indices, device=device
    )
    
    print(f"\n📊 性能对比总结:")
    print(f"   Unified数据集: 16.71mm")
    print(f"   高质量数据集: 15.49mm")
    print(f"   高级增强数据集: {avg_error:.2f}mm")
    
    # 计算改进幅度
    improvement_vs_unified = (16.71 - avg_error) / 16.71 * 100
    improvement_vs_high_quality = (15.49 - avg_error) / 15.49 * 100
    
    print(f"\n💡 改进幅度:")
    print(f"   相比Unified: {improvement_vs_unified:+.1f}%")
    print(f"   相比高质量: {improvement_vs_high_quality:+.1f}%")
    
    # 保存结果
    results = {
        'avg_error': float(avg_error),
        'region_errors': {k: float(np.mean(v)) for k, v in region_errors.items()},
        'accuracy_5mm': float(acc_5mm),
        'accuracy_10mm': float(acc_10mm),
        'improvement_vs_unified': float(improvement_vs_unified),
        'improvement_vs_high_quality': float(improvement_vs_high_quality),
        'training_history': history,
        'enhancement_features': [
            'anatomical_consistency_analysis',
            'intelligent_outlier_detection',
            'stratified_data_splitting',
            'conservative_medical_augmentation',
            'adaptive_model_architecture'
        ]
    }
    
    with open('advanced_enhanced_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    if avg_error < 12.0:
        print(f"\n🎉 高级增强数据集效果优秀！")
        print(f"💡 证明了深度数据集改进策略的有效性")
    elif avg_error < 15.0:
        print(f"\n✅ 高级增强数据集有显著提升！")
    else:
        print(f"\n⚠️ 仍需进一步优化")
    
    print(f"\n💾 详细结果已保存: advanced_enhanced_results.json")
    print(f"🎉 高级数据集改进验证完成！")

if __name__ == "__main__":
    main()
