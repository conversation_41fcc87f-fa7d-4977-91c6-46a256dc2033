#!/usr/bin/env python3
"""
彩虹靶子可视化
显示所有点云，使用彩虹色渐变的靶子效果
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps

# 关键点名称
KEYPOINT_NAMES = {
    0: "L-ASIS", 1: "R-ASIS", 2: "L-PSIS", 3: "R-PSIS",
    4: "L-IC", 5: "R-IC", 6: "SP", 7: "L-SIJ", 8: "R-SIJ",
    9: "L-IS", 10: "R-IS", 11: "CT"
}

def create_rainbow_colormap():
    """创建彩虹色靶子配色方案"""
    # 彩虹色序列：紫→蓝→青→绿→黄→橙→红
    colors = [
        '#E6E6FA',  # 薰衣草色 (背景)
        '#9370DB',  # 中紫色
        '#4169E1',  # 皇家蓝
        '#00BFFF',  # 深天蓝
        '#00FFFF',  # 青色
        '#00FF7F',  # 春绿色
        '#ADFF2F',  # 绿黄色
        '#FFFF00',  # 黄色
        '#FFD700',  # 金色
        '#FFA500',  # 橙色
        '#FF4500',  # 橙红色
        '#FF0000',  # 红色 (靶心)
    ]
    return LinearSegmentedColormap.from_list('rainbow_target', colors, N=256)

def create_rainbow_heatmap(point_cloud, pred_keypoint, sigma=10.0):
    """创建彩虹色热力图"""
    
    # 计算到预测关键点的距离
    distances = np.linalg.norm(point_cloud - pred_keypoint, axis=1)
    
    # 使用高斯分布
    heatmap = np.exp(-distances**2 / (2 * sigma**2))
    
    # 轻微增强对比度，保持平滑渐变
    heatmap = np.power(heatmap, 0.7)  # 轻微压缩，增强彩虹效果
    
    # 归一化到[0,1]
    if np.max(heatmap) > 0:
        heatmap = heatmap / np.max(heatmap)
    
    return heatmap

def create_rainbow_visualization(point_cloud, true_keypoint, pred_keypoint, 
                               confidence, kp_idx, sample_id):
    """创建彩虹靶子可视化"""
    
    print(f"🌈 Creating rainbow target visualization for {KEYPOINT_NAMES[kp_idx]}")
    
    rainbow_cmap = create_rainbow_colormap()
    
    fig = plt.figure(figsize=(20, 6))
    
    # 三种彩虹靶子效果
    sigma_values = [15.0, 10.0, 7.0]  # 不同的聚焦程度
    titles = ["Wide Rainbow", "Balanced Rainbow", "Focused Rainbow"]
    
    for i, (sigma, title) in enumerate(zip(sigma_values, titles)):
        ax = fig.add_subplot(1, 3, i+1, projection='3d')
        
        # 创建彩虹热力图
        rainbow_heatmap = create_rainbow_heatmap(point_cloud, pred_keypoint, sigma)
        
        # 随机采样保持合理密度，但显示更多点
        if len(point_cloud) > 6000:
            sample_indices = np.random.choice(len(point_cloud), 6000, replace=False)
            display_pc = point_cloud[sample_indices]
            display_heatmap = rainbow_heatmap[sample_indices]
        else:
            display_pc = point_cloud
            display_heatmap = rainbow_heatmap
        
        # 显示所有点 - 使用彩虹色渐变
        # 根据置信度调整点的大小，但都显示出来
        
        # 1. 低置信度点 - 小一点但可见
        low_mask = display_heatmap < 0.3
        if np.any(low_mask):
            ax.scatter(display_pc[low_mask, 0],
                      display_pc[low_mask, 1],
                      display_pc[low_mask, 2],
                      c=display_heatmap[low_mask],
                      cmap=rainbow_cmap, s=1, alpha=0.6, vmin=0, vmax=1)
        
        # 2. 中等置信度点 - 中等大小
        medium_mask = (display_heatmap >= 0.3) & (display_heatmap < 0.6)
        if np.any(medium_mask):
            ax.scatter(display_pc[medium_mask, 0],
                      display_pc[medium_mask, 1],
                      display_pc[medium_mask, 2],
                      c=display_heatmap[medium_mask],
                      cmap=rainbow_cmap, s=3, alpha=0.8, vmin=0, vmax=1)
        
        # 3. 高置信度点 - 大一点
        high_mask = display_heatmap >= 0.6
        if np.any(high_mask):
            scatter = ax.scatter(display_pc[high_mask, 0],
                               display_pc[high_mask, 1],
                               display_pc[high_mask, 2],
                               c=display_heatmap[high_mask],
                               cmap=rainbow_cmap, s=8, alpha=0.9, vmin=0, vmax=1)
        
        # 4. 峰值点 - 特别标记靶心
        peak_mask = display_heatmap >= 0.9
        if np.any(peak_mask):
            ax.scatter(display_pc[peak_mask, 0],
                      display_pc[peak_mask, 1],
                      display_pc[peak_mask, 2],
                      c='white', s=50, marker='o', 
                      edgecolor='red', linewidth=3, alpha=1.0)
        
        # 5. 关键点标记
        # 真实关键点 - 大黑星
        ax.scatter(true_keypoint[0], true_keypoint[1], true_keypoint[2],
                  c='black', s=600, marker='*', edgecolor='white', 
                  linewidth=4, alpha=1.0, label='Ground Truth', zorder=10)
        
        # 预测关键点 - 大红十字
        ax.scatter(pred_keypoint[0], pred_keypoint[1], pred_keypoint[2],
                  c='red', s=500, marker='x', linewidth=6, 
                  alpha=1.0, label='Predicted', zorder=10)
        
        # 连接线显示误差
        ax.plot([true_keypoint[0], pred_keypoint[0]], 
                [true_keypoint[1], pred_keypoint[1]], 
                [true_keypoint[2], pred_keypoint[2]], 
                'k-', alpha=0.8, linewidth=5)
        
        # 计算误差
        error = np.linalg.norm(pred_keypoint - true_keypoint)
        
        # 设置坐标轴范围 - 显示完整点云
        pc_min = np.min(point_cloud, axis=0)
        pc_max = np.max(point_cloud, axis=0)
        margin = 10
        
        ax.set_xlim([pc_min[0] - margin, pc_max[0] + margin])
        ax.set_ylim([pc_min[1] - margin, pc_max[1] + margin])
        ax.set_zlim([pc_min[2] - margin, pc_max[2] + margin])
        
        # 设置标题
        ax.set_title(f'{title}\n{KEYPOINT_NAMES[kp_idx]}\n'
                    f'σ: {sigma:.1f}mm | Error: {error:.1f}mm',
                    fontsize=14, fontweight='bold', pad=20)
        
        # 坐标轴标签
        ax.set_xlabel('X (mm)', fontsize=12)
        ax.set_ylabel('Y (mm)', fontsize=12)
        ax.set_zlabel('Z (mm)', fontsize=12)
        ax.tick_params(labelsize=10)
        
        # 设置视角
        ax.view_init(elev=25, azim=45)
        ax.grid(True, alpha=0.3)
        
        # 添加图例
        if i == 0:
            ax.legend(loc='upper right', fontsize=12)
        
        # 添加彩虹统计信息
        color_ranges = [
            ('Purple-Blue', np.sum(display_heatmap < 0.2)),
            ('Blue-Cyan', np.sum((display_heatmap >= 0.2) & (display_heatmap < 0.4))),
            ('Cyan-Green', np.sum((display_heatmap >= 0.4) & (display_heatmap < 0.6))),
            ('Green-Yellow', np.sum((display_heatmap >= 0.6) & (display_heatmap < 0.8))),
            ('Yellow-Red', np.sum(display_heatmap >= 0.8))
        ]
        
        stats_text = '\n'.join([f'{color}: {count}' for color, count in color_ranges])
        ax.text2D(0.02, 0.98, stats_text, transform=ax.transAxes, 
                 fontsize=9, verticalalignment='top',
                 bbox=dict(boxstyle='round', facecolor='white', alpha=0.9))
    
    # 添加彩虹色条
    if 'scatter' in locals():
        cbar = plt.colorbar(scatter, ax=fig.get_axes(), shrink=0.8, aspect=30)
        cbar.set_label('Rainbow Confidence (Purple→Blue→Cyan→Green→Yellow→Red)', fontsize=12)
    
    plt.suptitle(f'Rainbow Target Visualization - {KEYPOINT_NAMES[kp_idx]} (Sample {sample_id})\n'
                f'All Points Displayed with Rainbow Color Gradient', 
                fontsize=16, fontweight='bold')
    
    plt.tight_layout(rect=[0, 0, 0.95, 0.9])
    
    # 保存
    filename = f'rainbow_target_{sample_id}_kp{kp_idx}_{KEYPOINT_NAMES[kp_idx]}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"   🌈 Rainbow target visualization saved: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🌈 Rainbow Target Visualization")
    print("All points displayed with rainbow color gradient effect")
    print("=" * 80)
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model.eval()
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # 选择一个样本进行展示
    sample_idx = 0
    sample_id = sample_ids[sample_idx]
    point_cloud = point_clouds[sample_idx]
    true_keypoints = keypoints[sample_idx]
    
    print(f"\n🌈 Processing sample: {sample_id}")
    
    # 采样点云用于预测
    if len(point_cloud) > 8192:
        indices = np.random.choice(len(point_cloud), 8192, replace=False)
        pc_sampled = point_cloud[indices]
    else:
        pc_sampled = point_cloud
    
    # 预测关键点
    pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
    
    with torch.no_grad():
        pred_heatmaps = model(pc_tensor)
    
    pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T
    pred_keypoints, confidences = extract_keypoints_from_heatmaps(
        pred_heatmaps_np.T, pc_sampled
    )
    
    # 创建彩虹靶子可视化
    demo_keypoints = [0, 6, 11]  # L-ASIS, SP, CT
    
    for kp_idx in demo_keypoints:
        create_rainbow_visualization(
            point_cloud,  # 使用完整点云
            true_keypoints[kp_idx], 
            pred_keypoints[kp_idx],
            confidences[kp_idx], 
            kp_idx, 
            sample_id
        )
    
    print(f"\n🎉 Rainbow Target Visualization Complete!")
    print("✅ All points displayed")
    print("✅ Rainbow color gradient")
    print("✅ Purple→Blue→Cyan→Green→Yellow→Red")
    print("✅ Beautiful target effect")

if __name__ == "__main__":
    main()
