#!/usr/bin/env python3
"""
复现历史12点模型
Reproduce Historical 12-point Model
尝试复现达到5.371mm的历史最佳12点模型
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import json
from tqdm import tqdm

class HistoricalAdaptivePointNet(nn.Module):
    """历史自适应PointNet - 尝试复现5.371mm的架构"""
    
    def __init__(self, num_keypoints=12, dropout_rate=0.1):
        super(HistoricalAdaptivePointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 基础特征提取 - 参考历史最佳配置
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 双Softmax架构 - 历史最佳特征
        self.attention1 = nn.Linear(1024, 512)
        self.attention2 = nn.Linear(512, 256)
        self.attention_out = nn.Linear(256, 1)
        
        # 主回归分支
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, num_keypoints * 3)
        
        # 辅助回归分支
        self.aux_fc1 = nn.Linear(1024, 256)
        self.aux_fc2 = nn.Linear(256, 128)
        self.aux_fc3 = nn.Linear(128, num_keypoints * 3)
        
        # 批归一化
        self.fc_bn1 = nn.BatchNorm1d(512)
        self.fc_bn2 = nn.BatchNorm1d(256)
        self.fc_bn3 = nn.BatchNorm1d(128)
        
        self.aux_bn1 = nn.BatchNorm1d(256)
        self.aux_bn2 = nn.BatchNorm1d(128)
        
        # Dropout
        self.dropout = nn.Dropout(dropout_rate)
        
        # 权重初始化
        self._initialize_weights()
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"🏗️ HistoricalAdaptivePointNet({num_keypoints}点): {total_params:,} 参数")
    
    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        x = F.relu(self.bn4(self.conv4(x)))
        x = F.relu(self.bn5(self.conv5(x)))
        
        # 全局最大池化
        global_feat = torch.max(x, 2)[0]  # [B, 1024]
        
        # 注意力机制
        attention = F.relu(self.attention1(global_feat))
        attention = F.relu(self.attention2(attention))
        attention_weight = torch.sigmoid(self.attention_out(attention))  # [B, 1]
        
        # 主回归分支
        main_x = F.relu(self.fc_bn1(self.fc1(global_feat)))
        main_x = self.dropout(main_x)
        main_x = F.relu(self.fc_bn2(self.fc2(main_x)))
        main_x = self.dropout(main_x)
        main_x = F.relu(self.fc_bn3(self.fc3(main_x)))
        main_x = self.dropout(main_x)
        main_keypoints = self.fc4(main_x)
        
        # 辅助回归分支
        aux_x = F.relu(self.aux_bn1(self.aux_fc1(global_feat)))
        aux_x = self.dropout(aux_x)
        aux_x = F.relu(self.aux_bn2(self.aux_fc2(aux_x)))
        aux_x = self.dropout(aux_x)
        aux_keypoints = self.aux_fc3(aux_x)
        
        # 双Softmax融合
        main_keypoints = main_keypoints.view(batch_size, self.num_keypoints, 3)
        aux_keypoints = aux_keypoints.view(batch_size, self.num_keypoints, 3)
        
        # 加权融合
        attention_weight = attention_weight.unsqueeze(1).unsqueeze(2)  # [B, 1, 1]
        final_keypoints = attention_weight * main_keypoints + (1 - attention_weight) * aux_keypoints
        
        return final_keypoints, main_keypoints, aux_keypoints

class EnsembleLoss(nn.Module):
    """集成损失函数 - 模拟历史最佳训练策略"""
    
    def __init__(self, main_weight=0.6, aux_weight=0.3, ensemble_weight=0.1):
        super(EnsembleLoss, self).__init__()
        self.main_weight = main_weight
        self.aux_weight = aux_weight
        self.ensemble_weight = ensemble_weight
        
    def forward(self, final_pred, main_pred, aux_pred, target):
        main_loss = F.mse_loss(main_pred, target)
        aux_loss = F.mse_loss(aux_pred, target)
        ensemble_loss = F.mse_loss(final_pred, target)
        
        total_loss = (self.main_weight * main_loss + 
                     self.aux_weight * aux_loss + 
                     self.ensemble_weight * ensemble_loss)
        
        return total_loss

def create_medical_12point_subset():
    """创建医学意义的12点子集"""
    
    # 基于医学解剖学知识选择关键的12个点
    # 每个区域选择4个最重要的解剖标志点
    medical_12_subset = {
        'F1': [0, 5, 10, 15],    # F1区域的关键解剖点
        'F2': [19, 24, 29, 34],  # F2区域的关键解剖点  
        'F3': [38, 43, 48, 53]   # F3区域的关键解剖点
    }
    
    # 转换为全局索引
    global_indices = []
    for region_indices in medical_12_subset.values():
        global_indices.extend(region_indices)
    
    print(f"🏥 医学12点子集:")
    print(f"   F1区域: {medical_12_subset['F1']}")
    print(f"   F2区域: {medical_12_subset['F2']}")
    print(f"   F3区域: {medical_12_subset['F3']}")
    print(f"   全局索引: {global_indices}")
    
    return global_indices

def train_historical_model(model, train_loader, val_loader, epochs=150, device='cuda'):
    """训练历史风格模型"""
    
    print(f"🚀 训练历史风格12点模型...")
    print(f"   双Softmax + 集成损失 + 历史最佳超参数")
    
    model = model.to(device)
    
    # 历史最佳超参数配置
    optimizer = optim.Adam(model.parameters(), lr=0.0008, weight_decay=5e-5)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=30, gamma=0.8)
    
    criterion = EnsembleLoss(main_weight=0.6, aux_weight=0.3, ensemble_weight=0.1)
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 35
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            final_pred, main_pred, aux_pred = model(batch_pc)
            loss = criterion(final_pred, main_pred, aux_pred, batch_kp)
            loss.backward()
            
            # 历史风格的梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            
            with torch.no_grad():
                distances = torch.norm(final_pred - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                final_pred, main_pred, aux_pred = model(batch_pc)
                loss = criterion(final_pred, main_pred, aux_pred, batch_kp)
                
                val_loss += loss.item()
                distances = torch.norm(final_pred - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        scheduler.step()
        
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_historical_12point_model.pth')
        else:
            patience_counter += 1
        
        if epoch % 15 == 0 or epoch < 5:
            current_lr = optimizer.param_groups[0]['lr']
            print(f"Epoch {epoch+1:3d}: "
                  f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
                  f"Train Error: {train_error:.4f}, Val Error: {val_error:.4f}, "
                  f"LR: {current_lr:.2e}")
        
        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    model.load_state_dict(torch.load('best_historical_12point_model.pth'))
    return model

def test_historical_model(model, test_loader, scalers, test_indices, device='cuda'):
    """测试历史风格模型"""
    
    print("🔍 测试历史风格12点模型...")
    
    model = model.to(device)
    model.eval()
    
    test_predictions = []
    test_targets = []
    
    with torch.no_grad():
        for batch_pc, batch_kp in test_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            final_pred, _, _ = model(batch_pc)
            
            test_predictions.append(final_pred.cpu().numpy())
            test_targets.append(batch_kp.cpu().numpy())
    
    test_predictions = np.vstack(test_predictions)
    test_targets = np.vstack(test_targets)
    
    # 反归一化
    real_predictions = []
    real_targets = []
    
    for i, orig_idx in enumerate(test_indices):
        if i < len(test_predictions):
            pred_norm = test_predictions[i]
            target_norm = test_targets[i]
            
            scaler_info = scalers[orig_idx]
            scaler = scaler_info['scaler']
            center = scaler_info['center']
            
            # 反归一化
            dummy_pc = np.zeros((50000, 3))
            
            combined_pred = np.vstack([dummy_pc, pred_norm])
            combined_pred_denorm = scaler.inverse_transform(combined_pred)
            pred_real = combined_pred_denorm[50000:] + center
            
            combined_target = np.vstack([dummy_pc, target_norm])
            combined_target_denorm = scaler.inverse_transform(combined_target)
            target_real = combined_target_denorm[50000:] + center
            
            real_predictions.append(pred_real)
            real_targets.append(target_real)
    
    real_predictions = np.array(real_predictions)
    real_targets = np.array(real_targets)
    
    # 计算误差
    total_error = 0.0
    all_errors = []
    
    for i in range(len(real_predictions)):
        pred = real_predictions[i]
        target = real_targets[i]
        
        distances = np.linalg.norm(pred - target, axis=1)
        total_error += np.mean(distances)
        all_errors.extend(distances)
    
    avg_error = total_error / len(real_predictions)
    
    # 计算准确率
    accuracy_5mm = np.mean(np.array(all_errors) < 5.0) * 100
    accuracy_6mm = np.mean(np.array(all_errors) < 6.0) * 100
    accuracy_8mm = np.mean(np.array(all_errors) < 8.0) * 100
    
    return avg_error, accuracy_5mm, accuracy_6mm, accuracy_8mm

def main():
    """主函数"""
    
    print("🎯 复现历史12点模型")
    print("尝试复现达到5.371mm的历史最佳12点模型")
    print("=" * 80)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载数据集
    print("📊 加载高质量数据集...")
    data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints_57 = data['keypoints_57']
    sample_ids = data['sample_ids']
    
    # 创建医学意义的12点子集
    medical_12_indices = create_medical_12point_subset()
    
    # 创建子集归一化
    from test_keypoint_subsets import create_subset_normalization, SubsetDataset
    
    normalized_pc, normalized_kp, scalers = create_subset_normalization(
        point_clouds, keypoints_57, medical_12_indices
    )
    
    # 数据划分
    indices = np.arange(len(normalized_pc))
    train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
    train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
    
    # 创建数据集
    train_dataset = SubsetDataset(normalized_pc[train_indices], normalized_kp[train_indices], 
                                range(12))
    val_dataset = SubsetDataset(normalized_pc[val_indices], normalized_kp[val_indices], 
                              range(12))
    test_dataset = SubsetDataset(normalized_pc[test_indices], normalized_kp[test_indices], 
                               range(12))
    
    # 数据加载器
    batch_size = 8
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    
    print(f"📋 数据划分: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    
    # 创建历史风格模型
    model = HistoricalAdaptivePointNet(num_keypoints=12, dropout_rate=0.1)
    
    # 训练模型
    model = train_historical_model(model, train_loader, val_loader, epochs=150, device=device)
    
    # 测试模型
    avg_error, acc_5mm, acc_6mm, acc_8mm = test_historical_model(
        model, test_loader, scalers, test_indices, device=device
    )
    
    print(f"\n🎯 历史风格12点模型结果:")
    print(f"   平均误差: {avg_error:.2f}mm")
    print(f"   <5mm准确率: {acc_5mm:.1f}%")
    print(f"   <6mm准确率: {acc_6mm:.1f}%")
    print(f"   <8mm准确率: {acc_8mm:.1f}%")
    
    print(f"\n📊 与历史最佳对比:")
    print(f"   历史最佳: 5.371mm")
    print(f"   我们的结果: {avg_error:.2f}mm")
    print(f"   差距: {avg_error - 5.371:.2f}mm")
    
    if avg_error < 6.0:
        print(f"\n🎉 成功！接近历史最佳性能！")
        print(f"💡 证明了历史架构的有效性")
    elif avg_error < 8.0:
        print(f"\n✅ 良好！显著优于57点模型")
    else:
        print(f"\n⚠️ 仍需改进")
    
    # 保存结果
    results = {
        'avg_error': float(avg_error),
        'accuracy_5mm': float(acc_5mm),
        'accuracy_6mm': float(acc_6mm),
        'accuracy_8mm': float(acc_8mm),
        'historical_target': 5.371,
        'gap_to_historical': float(avg_error - 5.371),
        'medical_12_indices': medical_12_indices,
        'architecture_features': [
            'dual_softmax_ensemble',
            'attention_mechanism',
            'auxiliary_regression_branch',
            'ensemble_loss_function',
            'historical_hyperparameters'
        ]
    }
    
    with open('historical_12point_reproduction_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 详细结果已保存: historical_12point_reproduction_results.json")
    print(f"🎉 历史12点模型复现完成！")

if __name__ == "__main__":
    main()
