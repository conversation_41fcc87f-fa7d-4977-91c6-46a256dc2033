#!/usr/bin/env python3
"""
数据集改进管道
Dataset Improvement Pipeline - Focus on data quality over model complexity
"""

import numpy as np
import pandas as pd
import os
import glob
from pathlib import Path
from tqdm import tqdm
import json
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from sklearn.preprocessing import StandardScaler
import open3d as o3d

class DatasetQualityAnalyzer:
    """数据集质量分析器"""
    
    def __init__(self, data_dir="/home/<USER>/pjc/GCN/data/Data"):
        self.data_dir = data_dir
        self.quality_metrics = {}
        
    def analyze_annotation_consistency(self):
        """分析标注一致性"""
        
        print("🔍 分析标注一致性...")
        
        csv_dir = Path(self.data_dir) / "annotations"
        csv_files = glob.glob(f"{csv_dir}/*-Table-*.CSV")
        
        sample_stats = []
        coordinate_systems = []
        
        for csv_file in tqdm(csv_files, desc="分析CSV文件"):
            try:
                # 尝试不同编码
                for encoding in ['gbk', 'utf-8', 'latin-1']:
                    try:
                        df = pd.read_csv(csv_file, encoding=encoding)
                        break
                    except:
                        continue
                
                if len(df) != 57:
                    continue
                
                sample_id = Path(csv_file).stem.split('-')[0]
                keypoints = df[['X', 'Y', 'Z']].values.astype(np.float32)
                
                # 分析坐标系特征
                coord_range = np.ptp(keypoints, axis=0)
                coord_center = np.mean(keypoints, axis=0)
                coord_std = np.std(keypoints, axis=0)
                
                sample_stats.append({
                    'sample_id': sample_id,
                    'coord_range': coord_range,
                    'coord_center': coord_center,
                    'coord_std': coord_std,
                    'file_path': csv_file
                })
                
                # 判断坐标系类型
                if coord_range[2] > coord_range[0] and coord_range[2] > coord_range[1]:
                    coord_type = 'Z_dominant'  # Z轴主导
                elif abs(coord_center[2]) > 100:
                    coord_type = 'Z_offset'    # Z轴偏移
                else:
                    coord_type = 'balanced'    # 平衡
                
                coordinate_systems.append(coord_type)
                
            except Exception as e:
                print(f"⚠️ 处理文件失败 {csv_file}: {e}")
                continue
        
        # 统计坐标系分布
        coord_system_counts = {}
        for cs in coordinate_systems:
            coord_system_counts[cs] = coord_system_counts.get(cs, 0) + 1
        
        print(f"📊 标注一致性分析结果:")
        print(f"   有效样本: {len(sample_stats)}")
        print(f"   坐标系分布: {coord_system_counts}")
        
        # 分析坐标范围的一致性
        if sample_stats:
            all_ranges = np.array([s['coord_range'] for s in sample_stats])
            all_centers = np.array([s['coord_center'] for s in sample_stats])
            
            range_std = np.std(all_ranges, axis=0)
            center_std = np.std(all_centers, axis=0)
            
            print(f"   坐标范围标准差: X={range_std[0]:.1f}, Y={range_std[1]:.1f}, Z={range_std[2]:.1f}")
            print(f"   坐标中心标准差: X={center_std[0]:.1f}, Y={center_std[1]:.1f}, Z={center_std[2]:.1f}")
            
            # 识别异常样本
            outliers = []
            for i, stats in enumerate(sample_stats):
                range_z_score = abs((stats['coord_range'] - np.mean(all_ranges, axis=0)) / (range_std + 1e-6))
                if np.any(range_z_score > 3):  # 3-sigma规则
                    outliers.append(stats['sample_id'])
            
            print(f"   异常样本: {len(outliers)} 个")
            if outliers:
                print(f"     {outliers[:5]}...")  # 显示前5个
        
        self.quality_metrics['annotation_consistency'] = {
            'valid_samples': len(sample_stats),
            'coordinate_systems': coord_system_counts,
            'outliers': outliers if 'outliers' in locals() else [],
            'sample_stats': sample_stats
        }
        
        return sample_stats
    
    def analyze_stl_point_cloud_quality(self):
        """分析STL点云质量"""
        
        print("🔍 分析STL点云质量...")
        
        stl_dir = Path(self.data_dir) / "stl_models"
        stl_files = glob.glob(f"{stl_dir}/*.stl")
        
        stl_stats = []
        
        for stl_file in tqdm(stl_files, desc="分析STL文件"):
            try:
                mesh = o3d.io.read_triangle_mesh(stl_file)
                
                if len(mesh.vertices) == 0:
                    continue
                
                sample_id = Path(stl_file).stem.split('-')[0]
                region = Path(stl_file).stem.split('-')[1] if '-' in Path(stl_file).stem else 'unknown'
                
                # 网格质量指标
                vertices = np.asarray(mesh.vertices)
                triangles = np.asarray(mesh.triangles)
                
                vertex_count = len(vertices)
                triangle_count = len(triangles)
                
                # 几何特征
                bbox_size = np.ptp(vertices, axis=0)
                volume = mesh.get_volume() if mesh.is_watertight() else 0
                surface_area = mesh.get_surface_area()
                
                # 网格质量
                mesh.remove_duplicated_vertices()
                mesh.remove_degenerate_triangles()
                mesh.remove_unreferenced_vertices()
                
                quality_score = vertex_count / (triangle_count + 1)  # 简单质量指标
                
                stl_stats.append({
                    'sample_id': sample_id,
                    'region': region,
                    'vertex_count': vertex_count,
                    'triangle_count': triangle_count,
                    'bbox_size': bbox_size,
                    'volume': volume,
                    'surface_area': surface_area,
                    'quality_score': quality_score,
                    'file_path': stl_file
                })
                
            except Exception as e:
                print(f"⚠️ 处理STL文件失败 {stl_file}: {e}")
                continue
        
        print(f"📊 STL质量分析结果:")
        print(f"   有效STL文件: {len(stl_stats)}")
        
        if stl_stats:
            # 按区域统计
            region_counts = {}
            for stats in stl_stats:
                region = stats['region']
                region_counts[region] = region_counts.get(region, 0) + 1
            
            print(f"   区域分布: {region_counts}")
            
            # 质量统计
            quality_scores = [s['quality_score'] for s in stl_stats]
            vertex_counts = [s['vertex_count'] for s in stl_stats]
            
            print(f"   平均质量分数: {np.mean(quality_scores):.2f}")
            print(f"   平均顶点数: {np.mean(vertex_counts):.0f}")
        
        self.quality_metrics['stl_quality'] = {
            'valid_stl_files': len(stl_stats),
            'region_distribution': region_counts if 'region_counts' in locals() else {},
            'stl_stats': stl_stats
        }
        
        return stl_stats
    
    def analyze_keypoint_surface_alignment(self):
        """分析关键点与表面对齐质量"""
        
        print("🔍 分析关键点与表面对齐质量...")
        
        annotation_stats = self.quality_metrics.get('annotation_consistency', {}).get('sample_stats', [])
        stl_stats = self.quality_metrics.get('stl_quality', {}).get('stl_stats', [])
        
        alignment_results = []
        
        # 按样本ID匹配CSV和STL
        sample_groups = {}
        for ann_stat in annotation_stats:
            sample_id = ann_stat['sample_id']
            if sample_id not in sample_groups:
                sample_groups[sample_id] = {'annotation': ann_stat, 'stl_files': []}
        
        for stl_stat in stl_stats:
            sample_id = stl_stat['sample_id']
            if sample_id in sample_groups:
                sample_groups[sample_id]['stl_files'].append(stl_stat)
        
        # 分析对齐质量
        for sample_id, group in tqdm(sample_groups.items(), desc="分析对齐质量"):
            if 'annotation' not in group or len(group['stl_files']) == 0:
                continue
            
            try:
                # 加载关键点
                csv_file = group['annotation']['file_path']
                df = pd.read_csv(csv_file, encoding='gbk')
                keypoints = df[['X', 'Y', 'Z']].values.astype(np.float32)
                
                # 合并所有STL文件的点云
                all_points = []
                for stl_stat in group['stl_files']:
                    mesh = o3d.io.read_triangle_mesh(stl_stat['file_path'])
                    if len(mesh.vertices) > 0:
                        # 采样点云
                        pcd = mesh.sample_points_uniformly(number_of_points=10000)
                        points = np.asarray(pcd.points)
                        all_points.append(points)
                
                if not all_points:
                    continue
                
                combined_points = np.vstack(all_points)
                
                # 计算关键点到表面的距离
                distances_to_surface = []
                for kp in keypoints:
                    dists = np.linalg.norm(combined_points - kp, axis=1)
                    min_dist = np.min(dists)
                    distances_to_surface.append(min_dist)
                
                avg_distance = np.mean(distances_to_surface)
                max_distance = np.max(distances_to_surface)
                
                # 计算对齐质量分数
                alignment_score = 1.0 / (1.0 + avg_distance / 10.0)  # 归一化到0-1
                
                alignment_results.append({
                    'sample_id': sample_id,
                    'avg_distance_to_surface': avg_distance,
                    'max_distance_to_surface': max_distance,
                    'alignment_score': alignment_score,
                    'keypoint_count': len(keypoints),
                    'stl_count': len(group['stl_files'])
                })
                
            except Exception as e:
                print(f"⚠️ 分析样本 {sample_id} 失败: {e}")
                continue
        
        print(f"📊 对齐质量分析结果:")
        print(f"   分析样本数: {len(alignment_results)}")
        
        if alignment_results:
            avg_distances = [r['avg_distance_to_surface'] for r in alignment_results]
            alignment_scores = [r['alignment_score'] for r in alignment_results]
            
            print(f"   平均表面距离: {np.mean(avg_distances):.2f}mm")
            print(f"   平均对齐分数: {np.mean(alignment_scores):.3f}")
            
            # 识别高质量样本
            high_quality_samples = [r for r in alignment_results if r['alignment_score'] > 0.8]
            print(f"   高质量样本: {len(high_quality_samples)} 个")
        
        self.quality_metrics['alignment_quality'] = {
            'analyzed_samples': len(alignment_results),
            'alignment_results': alignment_results,
            'high_quality_count': len(high_quality_samples) if 'high_quality_samples' in locals() else 0
        }
        
        return alignment_results
    
    def generate_quality_report(self):
        """生成质量报告"""
        
        print("📋 生成数据集质量报告...")
        
        report = {
            'dataset_path': self.data_dir,
            'analysis_timestamp': pd.Timestamp.now().isoformat(),
            'quality_metrics': self.quality_metrics,
            'recommendations': []
        }
        
        # 基于分析结果生成建议
        if 'annotation_consistency' in self.quality_metrics:
            ann_metrics = self.quality_metrics['annotation_consistency']
            if len(ann_metrics['outliers']) > 0:
                report['recommendations'].append({
                    'type': 'annotation_outliers',
                    'priority': 'high',
                    'description': f"发现 {len(ann_metrics['outliers'])} 个异常标注样本，建议重新检查",
                    'samples': ann_metrics['outliers'][:10]  # 前10个
                })
        
        if 'alignment_quality' in self.quality_metrics:
            align_metrics = self.quality_metrics['alignment_quality']
            if align_metrics['analyzed_samples'] > 0:
                alignment_results = align_metrics['alignment_results']
                poor_alignment = [r for r in alignment_results if r['alignment_score'] < 0.5]
                
                if poor_alignment:
                    report['recommendations'].append({
                        'type': 'poor_alignment',
                        'priority': 'high',
                        'description': f"发现 {len(poor_alignment)} 个对齐质量差的样本，建议重新处理",
                        'samples': [r['sample_id'] for r in poor_alignment[:10]]
                    })
        
        # 保存报告
        with open('dataset_quality_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ 质量报告已保存: dataset_quality_report.json")
        print(f"📊 发现 {len(report['recommendations'])} 个改进建议")
        
        return report

def main():
    """主函数 - 数据集质量分析"""
    
    print("🎯 数据集质量分析与改进")
    print("以数据集改进为主要目的，用模型验证改进效果")
    print("=" * 80)
    
    # 创建质量分析器
    analyzer = DatasetQualityAnalyzer()
    
    # 执行全面质量分析
    print("\n🔍 第一步: 分析标注一致性")
    annotation_stats = analyzer.analyze_annotation_consistency()
    
    print("\n🔍 第二步: 分析STL点云质量")
    stl_stats = analyzer.analyze_stl_point_cloud_quality()
    
    print("\n🔍 第三步: 分析关键点与表面对齐质量")
    alignment_results = analyzer.analyze_keypoint_surface_alignment()
    
    print("\n📋 第四步: 生成质量报告")
    quality_report = analyzer.generate_quality_report()
    
    print(f"\n🎉 数据集质量分析完成！")
    print(f"💡 关键发现:")
    
    if 'annotation_consistency' in analyzer.quality_metrics:
        ann_count = analyzer.quality_metrics['annotation_consistency']['valid_samples']
        outlier_count = len(analyzer.quality_metrics['annotation_consistency']['outliers'])
        print(f"   ✅ 有效标注样本: {ann_count}")
        if outlier_count > 0:
            print(f"   ⚠️ 异常标注样本: {outlier_count}")
    
    if 'alignment_quality' in analyzer.quality_metrics:
        align_count = analyzer.quality_metrics['alignment_quality']['analyzed_samples']
        high_quality_count = analyzer.quality_metrics['alignment_quality']['high_quality_count']
        print(f"   ✅ 可分析样本: {align_count}")
        print(f"   🎯 高质量样本: {high_quality_count}")
    
    print(f"\n🚀 下一步建议:")
    print(f"   1. 查看 dataset_quality_report.json 了解详细分析")
    print(f"   2. 根据建议修复数据质量问题")
    print(f"   3. 重新构建高质量数据集")
    print(f"   4. 用简单模型验证数据集改进效果")

if __name__ == "__main__":
    main()
