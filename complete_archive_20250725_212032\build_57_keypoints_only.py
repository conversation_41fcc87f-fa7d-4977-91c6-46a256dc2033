#!/usr/bin/env python3
"""
仅构建57关键点数据（不包含点云）
Build 57 keypoints data only (without point clouds)
"""

import numpy as np
import pandas as pd
import os
from pathlib import Path
import json

def load_57_keypoints_from_raw(sample_id, data_dir):
    """从原始CSV文件加载57个关键点"""
    
    annotations_dir = data_dir / "annotations"
    ann_file = annotations_dir / f"{sample_id}-Table-XYZ.CSV"
    
    if not ann_file.exists():
        return None, f"标注文件不存在"
    
    try:
        # 尝试不同编码
        for encoding in ['gbk', 'utf-8', 'latin1']:
            try:
                df = pd.read_csv(ann_file, encoding=encoding)
                break
            except:
                continue
        else:
            return None, "无法读取CSV文件"
        
        if len(df) < 57:
            return None, f"关键点数量不足: {len(df)} < 57"
        
        # 提取57个关键点坐标
        keypoints_57 = []
        labels = []
        
        for idx, row in df.iterrows():
            if idx >= 57:  # 只取前57个
                break
            x, y, z = row['X'], row['Y'], row['Z']
            label = row['label'] if 'label' in row else f"point_{idx}"
            
            keypoints_57.append([x, y, z])
            labels.append(label)
        
        return np.array(keypoints_57), labels, "成功"
        
    except Exception as e:
        return None, None, f"处理失败: {e}"

def extract_12_from_57(keypoints_57):
    """从57个关键点中提取12个核心关键点"""
    
    # 12点到57点的映射关系
    mapping_12_to_57 = {
        0: 0,   # F1-1 -> 原始索引0
        1: 1,   # F1-2 -> 原始索引1
        2: 2,   # F1-3 -> 原始索引2
        3: 12,  # F1-13 -> 原始索引12
        4: 19,  # F2-1 -> 原始索引19
        5: 20,  # F2-2 -> 原始索引20
        6: 21,  # F2-3 -> 原始索引21
        7: 31,  # F2-13 -> 原始索引31
        8: 38,  # F3-1 -> 原始索引38
        9: 52,  # F3-15 -> 原始索引52
        10: 50, # F3-13 -> 原始索引50
        11: 51, # F3-14 -> 原始索引51
    }
    
    keypoints_12 = np.zeros((12, 3))
    
    for i in range(12):
        original_idx = mapping_12_to_57[i]
        if original_idx < len(keypoints_57):
            keypoints_12[i] = keypoints_57[original_idx]
    
    return keypoints_12, mapping_12_to_57

def analyze_keypoint_structure(keypoints_57, labels):
    """分析关键点结构"""
    
    # 按区域分组
    regions = {'F1': [], 'F2': [], 'F3': []}
    
    for i, label in enumerate(labels):
        if 'F_1' in label or 'F1' in label:
            regions['F1'].append(i)
        elif 'F_2' in label or 'F2' in label:
            regions['F2'].append(i)
        elif 'F_3' in label or 'F3' in label:
            regions['F3'].append(i)
    
    analysis = {
        'total_points': len(keypoints_57),
        'regions': {}
    }
    
    for region_name, indices in regions.items():
        if indices:
            region_points = keypoints_57[indices]
            center = np.mean(region_points, axis=0)
            
            # 计算区域内点的分布
            distances_from_center = [np.linalg.norm(point - center) for point in region_points]
            
            analysis['regions'][region_name] = {
                'count': len(indices),
                'indices': indices,
                'center': center.tolist(),
                'avg_distance_from_center': np.mean(distances_from_center),
                'max_distance_from_center': np.max(distances_from_center)
            }
    
    return analysis

def build_57_keypoints_dataset():
    """构建57关键点数据集（仅关键点）"""
    
    print("🚀 构建57关键点数据集（仅关键点）...")
    print("=" * 60)
    
    data_dir = Path("/home/<USER>/pjc/GCN/data/Data")
    annotations_dir = data_dir / "annotations"
    
    print(f"📁 数据目录: {data_dir}")
    print(f"📁 标注目录: {annotations_dir}")
    
    if not annotations_dir.exists():
        print(f"❌ 标注目录不存在: {annotations_dir}")
        return None
    
    # 获取所有标注文件
    annotation_files = list(annotations_dir.glob("*-Table-XYZ.CSV"))
    print(f"📋 找到 {len(annotation_files)} 个标注文件")
    
    # 准备数据容器
    valid_samples = []
    keypoints_57_list = []
    keypoints_12_list = []
    sample_ids_list = []
    labels_list = []
    analysis_list = []
    processing_log = []
    
    # 处理所有样本
    print(f"🎯 处理所有 {len(annotation_files)} 个样本")
    
    for i, ann_file in enumerate(annotation_files):
        sample_id = ann_file.stem.split('-')[0]
        
        if i % 10 == 0:
            print(f"📋 处理进度: {i+1}/{len(annotation_files)} ({(i+1)/len(annotation_files)*100:.1f}%)")
        
        # 加载57个关键点
        result = load_57_keypoints_from_raw(sample_id, data_dir)
        
        if len(result) == 3:
            keypoints_57, labels, msg = result
        else:
            keypoints_57, msg = result[0], result[1]
            labels = None
        
        if keypoints_57 is None:
            processing_log.append((sample_id, "失败", msg))
            continue
        
        # 提取12个核心关键点
        keypoints_12, mapping = extract_12_from_57(keypoints_57)
        
        # 分析关键点结构
        if labels:
            analysis = analyze_keypoint_structure(keypoints_57, labels)
        else:
            analysis = {'total_points': len(keypoints_57)}
        
        # 验证数据质量
        if keypoints_57.shape[0] != 57:
            processing_log.append((sample_id, "失败", f"关键点数量错误: {keypoints_57.shape[0]}"))
            continue
        
        # 检查坐标是否合理
        coord_ranges = {
            'X': (keypoints_57[:, 0].min(), keypoints_57[:, 0].max()),
            'Y': (keypoints_57[:, 1].min(), keypoints_57[:, 1].max()),
            'Z': (keypoints_57[:, 2].min(), keypoints_57[:, 2].max())
        }
        
        # 简单的合理性检查
        reasonable = True
        for axis, (min_val, max_val) in coord_ranges.items():
            if abs(max_val - min_val) > 1000:  # 坐标范围过大
                reasonable = False
                break
        
        if not reasonable:
            processing_log.append((sample_id, "失败", "坐标范围异常"))
            continue
        
        # 添加到有效样本
        valid_samples.append(sample_id)
        keypoints_57_list.append(keypoints_57)
        keypoints_12_list.append(keypoints_12)
        sample_ids_list.append(sample_id)
        if labels:
            labels_list.append(labels)
        analysis_list.append(analysis)
        
        processing_log.append((sample_id, "成功", f"57点: {keypoints_57.shape}"))
    
    # 统计结果
    print(f"\n📊 处理统计:")
    print(f"   总处理数: {len(annotation_files)}")
    print(f"   成功数: {len(valid_samples)}")
    print(f"   失败数: {len(annotation_files) - len(valid_samples)}")
    print(f"   成功率: {len(valid_samples)/len(annotation_files)*100:.1f}%")
    
    if len(valid_samples) == 0:
        print("❌ 没有有效样本")
        return None
    
    # 转换为numpy数组
    keypoints_57_array = np.array(keypoints_57_list)
    keypoints_12_array = np.array(keypoints_12_list)
    sample_ids_array = np.array(sample_ids_list)
    
    print(f"\n📋 数据集信息:")
    print(f"   有效样本: {len(valid_samples)}")
    print(f"   57关键点: {keypoints_57_array.shape}")
    print(f"   12关键点: {keypoints_12_array.shape}")
    
    # 分析整体数据质量
    print(f"\n📏 整体数据质量分析:")
    
    # 坐标范围
    for axis, axis_name in enumerate(['X', 'Y', 'Z']):
        min_val = keypoints_57_array[:, :, axis].min()
        max_val = keypoints_57_array[:, :, axis].max()
        mean_val = keypoints_57_array[:, :, axis].mean()
        std_val = keypoints_57_array[:, :, axis].std()
        
        print(f"   {axis_name}轴: {min_val:.1f} ~ {max_val:.1f} (均值: {mean_val:.1f}, 标准差: {std_val:.1f})")
    
    # 区域分析
    if analysis_list:
        region_stats = {'F1': [], 'F2': [], 'F3': []}
        
        for analysis in analysis_list:
            if 'regions' in analysis:
                for region_name in region_stats.keys():
                    if region_name in analysis['regions']:
                        region_stats[region_name].append(analysis['regions'][region_name]['count'])
        
        print(f"\n🏥 解剖区域分析:")
        for region_name, counts in region_stats.items():
            if counts:
                print(f"   {region_name}区域: 平均{np.mean(counts):.1f}个点 (范围: {min(counts)}-{max(counts)})")
    
    # 保存数据集
    print(f"\n💾 保存数据集...")
    
    np.savez('keypoints_57_dataset.npz',
             keypoints_57=keypoints_57_array,
             keypoints_12=keypoints_12_array,
             sample_ids=sample_ids_array)
    
    print(f"✅ 关键点数据集已保存: keypoints_57_dataset.npz")
    
    # 保存详细信息
    dataset_info = {
        'processing_log': processing_log,
        'valid_samples': valid_samples,
        'failed_samples': [(sid, reason) for sid, status, reason in processing_log if status == "失败"],
        'mapping_12_to_57': {
            0: 0, 1: 1, 2: 2, 3: 12, 4: 19, 5: 20,
            6: 21, 7: 31, 8: 38, 9: 52, 10: 50, 11: 51
        },
        'dataset_stats': {
            'total_samples': len(valid_samples),
            'keypoints_57': 57,
            'keypoints_12': 12,
            'success_rate': len(valid_samples)/len(annotation_files)*100,
            'coordinate_ranges': {
                'X': (float(keypoints_57_array[:, :, 0].min()), float(keypoints_57_array[:, :, 0].max())),
                'Y': (float(keypoints_57_array[:, :, 1].min()), float(keypoints_57_array[:, :, 1].max())),
                'Z': (float(keypoints_57_array[:, :, 2].min()), float(keypoints_57_array[:, :, 2].max()))
            }
        },
        'analysis_summary': analysis_list[:5] if analysis_list else []  # 保存前5个样本的分析
    }
    
    with open('keypoints_57_dataset_info.json', 'w', encoding='utf-8') as f:
        json.dump(dataset_info, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 数据集信息已保存: keypoints_57_dataset_info.json")
    
    return keypoints_57_array, keypoints_12_array, sample_ids_array

def validate_keypoints_dataset():
    """验证关键点数据集"""
    
    print(f"\n🔍 验证关键点数据集...")
    print("=" * 50)
    
    try:
        data = np.load('keypoints_57_dataset.npz', allow_pickle=True)
        
        keypoints_57 = data['keypoints_57']
        keypoints_12 = data['keypoints_12']
        sample_ids = data['sample_ids']
        
        print(f"📊 数据集基本信息:")
        print(f"   样本数量: {len(sample_ids)}")
        print(f"   57关键点形状: {keypoints_57.shape}")
        print(f"   12关键点形状: {keypoints_12.shape}")
        
        # 验证12点提取的正确性
        print(f"\n✅ 12关键点提取验证:")
        
        mapping_12_to_57 = {0: 0, 3: 12, 4: 19, 8: 38}  # 检查几个关键映射
        
        all_correct = True
        for i in range(min(3, len(sample_ids))):  # 检查前3个样本
            sample_id = sample_ids[i]
            kp_57 = keypoints_57[i]
            kp_12 = keypoints_12[i]
            
            print(f"   样本 {sample_id}:")
            
            for idx_12, idx_57 in mapping_12_to_57.items():
                match = np.allclose(kp_12[idx_12], kp_57[idx_57], atol=1e-6)
                print(f"     12点[{idx_12}] vs 57点[{idx_57}]: {match}")
                if not match:
                    all_correct = False
        
        if all_correct:
            print(f"   ✅ 映射验证通过")
        else:
            print(f"   ⚠️ 部分映射验证失败")
        
        # 创建12→57扩展训练数据
        print(f"\n🔄 创建12→57扩展训练数据...")
        
        np.savez('expansion_training_keypoints.npz',
                 input_12=keypoints_12,
                 target_57=keypoints_57,
                 sample_ids=sample_ids)
        
        print(f"✅ 扩展训练数据已保存: expansion_training_keypoints.npz")
        
        # 分析扩展任务复杂度
        print(f"\n📈 扩展任务分析:")
        print(f"   维度扩展: {12*3} → {57*3} ({57*3/(12*3):.1f}倍)")
        print(f"   样本数量: {len(sample_ids)}")
        print(f"   任务类型: 回归任务 (连续坐标预测)")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据集验证失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🎯 构建57关键点数据集（仅关键点）")
    print("从原始CSV标注文件提取完整的57个关键点")
    print("=" * 80)
    
    # 步骤1: 构建关键点数据集
    result = build_57_keypoints_dataset()
    
    if result is not None:
        print(f"\n🎉 关键点数据集构建成功！")
        
        # 步骤2: 验证数据集
        if validate_keypoints_dataset():
            print(f"\n🎉 57关键点数据集完整构建完成！")
            print(f"📋 生成的文件:")
            print(f"   - keypoints_57_dataset.npz (57关键点数据集)")
            print(f"   - keypoints_57_dataset_info.json (详细信息)")
            print(f"   - expansion_training_keypoints.npz (12→57训练数据)")
            
            print(f"\n🚀 下一步:")
            print(f"   1. 实现12→57关键点扩展网络")
            print(f"   2. 训练扩展模型")
            print(f"   3. 结合现有点云数据进行端到端训练")
            print(f"   4. 评估57点vs12点的性能差异")
            
            print(f"\n💡 数据集特点:")
            print(f"   - 完整的57个解剖关键点")
            print(f"   - 基于医生专业标注")
            print(f"   - 包含F1/F2/F3三个解剖区域")
            print(f"   - 可用于12→57点扩展学习")
        else:
            print(f"❌ 数据集验证失败")
    else:
        print(f"❌ 数据集构建失败")

if __name__ == "__main__":
    main()
