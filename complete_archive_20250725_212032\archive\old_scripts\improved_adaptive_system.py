#!/usr/bin/env python3
"""
改进的自适应关键点检测系统
基于第一次测试结果的优化版本
"""

import numpy as np
import torch
from basic_19keypoints_system import BasicHeatmapPointNet19, extract_keypoints_from_heatmaps_19

class ImprovedAdaptiveDetector:
    """改进的自适应关键点检测器"""
    
    def __init__(self, model, device):
        self.model = model
        self.device = device
        
        # 关键点策略 (基于实际性能数据)
        self.keypoint_strategies = {
            # 几何策略 - 只对真正需要的点应用
            'geometric': [12],  # 只有F3-13 (Z最高点) 真正需要几何约束
            # 解剖策略 - 表现相对稳定的点
            'anatomical': [17],  # 只有F3-18 (尾骨尖) 需要解剖学精化
            # 相对策略 - 表现最差的点
            'relative': [3]  # 只有F3-4 (最差的点) 需要相对位置优化
        }
        
        # 保守的权重设置
        self.constraint_weights = {
            'geometric': 0.3,  # 降低几何约束权重
            'anatomical': 0.2,  # 降低解剖约束权重
            'relative': 0.25   # 降低相对约束权重
        }
        
        # 置信度阈值 - 只对低置信度预测应用约束
        self.confidence_threshold = 0.5
        
    def predict_adaptive(self, point_cloud):
        """改进的自适应预测"""
        
        # 1. 基础ML预测 (包含置信度)
        base_predictions, confidences = self._get_base_predictions_with_confidence(point_cloud)
        
        # 2. 选择性应用约束 (只对低置信度或已知问题点)
        refined_predictions = base_predictions.copy()
        
        # F3-13 (Z最高点) - 已知问题点，总是应用几何约束
        if 12 in self.keypoint_strategies['geometric']:
            refined_predictions[12] = self._apply_selective_geometric_constraint(
                base_predictions[12], point_cloud, 12, force=True)
        
        # F3-18 (尾骨尖) - 如果置信度低，应用解剖学约束
        if 17 in self.keypoint_strategies['anatomical']:
            if confidences[17] < self.confidence_threshold:
                refined_predictions[17] = self._apply_selective_anatomical_constraint(
                    base_predictions[17], point_cloud, 17)
        
        # F3-4 (问题点) - 如果置信度低，应用相对位置约束
        if 3 in self.keypoint_strategies['relative']:
            if confidences[3] < self.confidence_threshold:
                refined_predictions[3] = self._apply_selective_relative_constraint(
                    base_predictions, point_cloud, 3)
        
        return refined_predictions
    
    def _get_base_predictions_with_confidence(self, point_cloud):
        """获取基础预测和置信度"""
        
        # 采样点云
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            pc_sampled = point_cloud[indices]
        else:
            pc_sampled = point_cloud
        
        pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            pred_heatmaps = self.model(pc_tensor)
        
        pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze()
        pred_keypoints, confidences = extract_keypoints_from_heatmaps_19(pred_heatmaps_np, pc_sampled)
        
        return pred_keypoints, confidences
    
    def _apply_selective_geometric_constraint(self, prediction, point_cloud, kp_idx, force=False):
        """选择性应用几何约束"""
        
        if kp_idx == 12:  # F3-13 (Z最高点)
            return self._improved_z_max_constraint(prediction, point_cloud)
        
        return prediction
    
    def _improved_z_max_constraint(self, prediction, point_cloud):
        """改进的Z最高点约束"""
        
        # 1. 找到Z坐标前5%的点 (避免噪声)
        z_threshold = np.percentile(point_cloud[:, 2], 95)
        high_points = point_cloud[point_cloud[:, 2] >= z_threshold]
        
        if len(high_points) == 0:
            return prediction
        
        # 2. 在高点中找到最密集的区域
        best_point = None
        max_density = 0
        
        for point in high_points[::5]:  # 采样以提高效率
            distances = np.linalg.norm(high_points - point, axis=1)
            density = np.sum(distances <= 3.0)  # 3mm内的点数
            
            if density > max_density:
                max_density = density
                best_point = point
        
        if best_point is not None:
            # 3. 在最佳点周围做精细搜索
            distances = np.linalg.norm(high_points - best_point, axis=1)
            nearby_mask = distances <= 2.0
            nearby_points = high_points[nearby_mask]
            
            if len(nearby_points) > 0:
                # 使用Z坐标和密度双重加权
                z_coords = nearby_points[:, 2]
                z_weights = np.exp((z_coords - np.min(z_coords)) / 1.0)
                
                density_weights = []
                for point in nearby_points:
                    local_distances = np.linalg.norm(nearby_points - point, axis=1)
                    density = np.sum(local_distances <= 1.5)
                    density_weights.append(density)
                
                density_weights = np.array(density_weights)
                combined_weights = z_weights * density_weights
                combined_weights = combined_weights / np.sum(combined_weights)
                
                geometric_point = np.average(nearby_points, axis=0, weights=combined_weights)
                
                # 4. 保守的权重融合
                alpha = self.constraint_weights['geometric']
                return alpha * geometric_point + (1 - alpha) * prediction
        
        return prediction
    
    def _apply_selective_anatomical_constraint(self, prediction, point_cloud, kp_idx):
        """选择性应用解剖学约束"""
        
        if kp_idx == 17:  # F3-18 (尾骨尖)
            return self._improved_coccyx_constraint(prediction, point_cloud)
        
        return prediction
    
    def _improved_coccyx_constraint(self, prediction, point_cloud):
        """改进的尾骨约束"""
        
        # 1. 找到下半部分的点
        z_threshold = np.percentile(point_cloud[:, 2], 40)
        lower_region = point_cloud[point_cloud[:, 2] <= z_threshold]
        
        if len(lower_region) == 0:
            return prediction
        
        # 2. 在下半部分找到最突出的点 (最小凸包的顶点)
        tip_candidates = []
        
        for point in lower_region[::10]:
            # 计算点的"突出度" - 到其他点的最小距离
            distances = np.linalg.norm(lower_region - point, axis=1)
            distances = distances[distances > 0]  # 排除自己
            
            if len(distances) > 0:
                min_distance = np.min(distances)
                avg_distance = np.mean(distances[:10])  # 到最近10个点的平均距离
                prominence = min_distance * avg_distance  # 突出度指标
                tip_candidates.append((point, prominence))
        
        if tip_candidates:
            # 选择突出度最高的点
            best_tip = max(tip_candidates, key=lambda x: x[1])[0]
            
            # 保守融合
            alpha = self.constraint_weights['anatomical']
            return alpha * best_tip + (1 - alpha) * prediction
        
        return prediction
    
    def _apply_selective_relative_constraint(self, all_predictions, point_cloud, kp_idx):
        """选择性应用相对位置约束"""
        
        if kp_idx == 3:  # F3-4
            return self._improved_relative_constraint(all_predictions, point_cloud, kp_idx)
        
        return all_predictions[kp_idx]
    
    def _improved_relative_constraint(self, all_predictions, point_cloud, kp_idx):
        """改进的相对位置约束"""
        
        # F3-4应该在多个参考点的合理范围内
        reference_points = []
        
        # 使用多个稳定的参考点
        stable_indices = [0, 6, 7, 9]  # F3-1, F3-7, F3-8, F3-10
        
        for ref_idx in stable_indices:
            if ref_idx < len(all_predictions):
                reference_points.append(all_predictions[ref_idx])
        
        if len(reference_points) >= 2:
            # 计算参考点的重心
            ref_centroid = np.mean(reference_points, axis=0)
            
            # 基于重心和原预测的加权平均
            alpha = self.constraint_weights['relative']
            return alpha * ref_centroid + (1 - alpha) * all_predictions[kp_idx]
        
        return all_predictions[kp_idx]

def test_improved_system():
    """测试改进的系统"""
    
    print("🔧 测试改进的自适应关键点检测系统")
    print("=" * 60)
    
    # 加载数据和模型
    data = np.load('f3_19kp_preprocessed.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    sample_ids = data['sample_ids']
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 加载基础模型
    model = BasicHeatmapPointNet19(input_dim=3, num_keypoints=19).to(device)
    model.load_state_dict(torch.load('best_fixed_19kp_model.pth', map_location=device))
    model.eval()
    
    # 创建改进的自适应检测器
    improved_detector = ImprovedAdaptiveDetector(model, device)
    
    print(f"✅ 改进系统初始化完成")
    print(f"   目标关键点: F3-13 (几何), F3-18 (解剖), F3-4 (相对)")
    print(f"   约束权重: 几何{improved_detector.constraint_weights['geometric']}, "
          f"解剖{improved_detector.constraint_weights['anatomical']}, "
          f"相对{improved_detector.constraint_weights['relative']}")
    
    # 测试所有样本
    results_baseline = []
    results_improved = []
    
    for i in range(len(point_clouds)):
        sample_id = sample_ids[i]
        point_cloud = point_clouds[i]
        true_keypoints = keypoints[i]
        
        # 基础预测
        baseline_pred, confidences = improved_detector._get_base_predictions_with_confidence(point_cloud)
        baseline_errors = [np.linalg.norm(baseline_pred[j] - true_keypoints[j]) for j in range(19)]
        baseline_avg = np.mean(baseline_errors)
        
        # 改进的自适应预测
        improved_pred = improved_detector.predict_adaptive(point_cloud)
        improved_errors = [np.linalg.norm(improved_pred[j] - true_keypoints[j]) for j in range(19)]
        improved_avg = np.mean(improved_errors)
        
        improvement = baseline_avg - improved_avg
        
        # 重点关注目标关键点的改进
        f3_13_improvement = baseline_errors[12] - improved_errors[12]
        f3_18_improvement = baseline_errors[17] - improved_errors[17]
        f3_4_improvement = baseline_errors[3] - improved_errors[3]
        
        print(f"样本 {sample_id}: {baseline_avg:.2f}mm → {improved_avg:.2f}mm ({improvement:+.2f}mm)")
        print(f"   F3-13: {baseline_errors[12]:.2f}mm → {improved_errors[12]:.2f}mm ({f3_13_improvement:+.2f}mm)")
        print(f"   F3-18: {baseline_errors[17]:.2f}mm → {improved_errors[17]:.2f}mm ({f3_18_improvement:+.2f}mm)")
        print(f"   F3-4:  {baseline_errors[3]:.2f}mm → {improved_errors[3]:.2f}mm ({f3_4_improvement:+.2f}mm)")
        print(f"   置信度 - F3-13: {confidences[12]:.3f}, F3-18: {confidences[17]:.3f}, F3-4: {confidences[3]:.3f}")
        
        results_baseline.append({
            'sample_id': sample_id,
            'errors': baseline_errors,
            'avg_error': baseline_avg
        })
        
        results_improved.append({
            'sample_id': sample_id,
            'errors': improved_errors,
            'avg_error': improved_avg
        })
    
    # 总体统计
    overall_baseline = np.mean([r['avg_error'] for r in results_baseline])
    overall_improved = np.mean([r['avg_error'] for r in results_improved])
    overall_improvement = overall_baseline - overall_improved
    overall_improvement_pct = overall_improvement / overall_baseline * 100
    
    # 目标关键点统计
    f3_13_baseline = np.mean([r['errors'][12] for r in results_baseline])
    f3_13_improved = np.mean([r['errors'][12] for r in results_improved])
    f3_13_improvement = f3_13_baseline - f3_13_improved
    
    f3_18_baseline = np.mean([r['errors'][17] for r in results_baseline])
    f3_18_improved = np.mean([r['errors'][17] for r in results_improved])
    f3_18_improvement = f3_18_baseline - f3_18_improved
    
    f3_4_baseline = np.mean([r['errors'][3] for r in results_baseline])
    f3_4_improved = np.mean([r['errors'][3] for r in results_improved])
    f3_4_improvement = f3_4_baseline - f3_4_improved
    
    print(f"\n📊 总体结果:")
    print(f"   基础模型平均: {overall_baseline:.2f}mm")
    print(f"   改进系统平均: {overall_improved:.2f}mm")
    print(f"   总体改进: {overall_improvement:.2f}mm ({overall_improvement_pct:.1f}%)")
    
    print(f"\n🎯 目标关键点改进:")
    print(f"   F3-13 (Z最高点): {f3_13_baseline:.2f}mm → {f3_13_improved:.2f}mm ({f3_13_improvement:+.2f}mm)")
    print(f"   F3-18 (尾骨尖):  {f3_18_baseline:.2f}mm → {f3_18_improved:.2f}mm ({f3_18_improvement:+.2f}mm)")
    print(f"   F3-4 (问题点):   {f3_4_baseline:.2f}mm → {f3_4_improved:.2f}mm ({f3_4_improvement:+.2f}mm)")
    
    return results_baseline, results_improved

def main():
    """主函数"""
    print("🚀 改进的分层自适应关键点检测系统")
    print("基于第一次测试的优化版本")
    print("=" * 60)
    
    # 测试改进系统
    results_baseline, results_improved = test_improved_system()
    
    print(f"\n🎯 改进策略:")
    print("✅ 选择性应用: 只对真正需要的关键点应用约束")
    print("✅ 保守权重: 降低约束权重，避免破坏好的预测")
    print("✅ 置信度驱动: 只对低置信度预测应用约束")
    print("✅ 精细算法: 改进几何和解剖学约束算法")
    print("✅ 目标明确: 重点优化F3-13, F3-18, F3-4")
    
    print(f"\n💡 经验教训:")
    print("1. 不是所有关键点都需要约束")
    print("2. 权重设置需要非常保守")
    print("3. 置信度是应用约束的重要指标")
    print("4. 算法细节比策略分类更重要")

if __name__ == "__main__":
    main()
