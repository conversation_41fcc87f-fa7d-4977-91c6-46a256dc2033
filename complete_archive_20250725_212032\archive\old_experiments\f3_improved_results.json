{"model_type": "ImprovedSimpleNet", "best_error": "20.853754", "improvement_vs_baseline": -13.335619802060345, "total_epochs": 35, "history": [{"epoch": 1, "train_loss": 3.622357646624247, "val_loss": 3.8270151615142822, "val_error": "54.299553", "acc_5mm": 0.3289473684210526, "acc_10mm": 0.3289473684210526, "learning_rate": 0.001951080988037006}, {"epoch": 2, "train_loss": 2.574655761321386, "val_loss": 2.124847173690796, "val_error": "40.895477", "acc_5mm": 0.3289473684210526, "acc_10mm": 1.9736842105263157, "learning_rate": 0.0018091124858777598}, {"epoch": 3, "train_loss": 2.9777779082457223, "val_loss": 1.4888549248377483, "val_error": "32.898964", "acc_5mm": 1.9736842105263157, "acc_10mm": 5.921052631578947, "learning_rate": 0.0015879913596663266}, {"epoch": 4, "train_loss": 1.8108699321746826, "val_loss": 1.3109636704126995, "val_error": "30.37315", "acc_5mm": 0.3289473684210526, "acc_10mm": 4.934210526315789, "learning_rate": 0.0013093624858777598}, {"epoch": 5, "train_loss": 1.618966410557429, "val_loss": 1.2699522177378337, "val_error": "28.563232", "acc_5mm": 0.0, "acc_10mm": 1.3157894736842104, "learning_rate": 0.0010004999999999999}, {"epoch": 6, "train_loss": 1.8040426870187123, "val_loss": 1.403626561164856, "val_error": "31.805157", "acc_5mm": 0.3289473684210526, "acc_10mm": 3.289473684210526, "learning_rate": 0.0006916375141222402}, {"epoch": 7, "train_loss": 1.4036074827114742, "val_loss": 1.2002431948979695, "val_error": "27.212255", "acc_5mm": 0.6578947368421052, "acc_10mm": 4.934210526315789, "learning_rate": 0.0004130086403336732}, {"epoch": 8, "train_loss": 1.392882893482844, "val_loss": 1.1729541420936584, "val_error": "27.3031", "acc_5mm": 0.6578947368421052, "acc_10mm": 6.25, "learning_rate": 0.00019188751412224013}, {"epoch": 9, "train_loss": 1.377897024154663, "val_loss": 1.2181650002797444, "val_error": "27.219957", "acc_5mm": 0.6578947368421052, "acc_10mm": 5.263157894736842, "learning_rate": 4.991901196299404e-05}, {"epoch": 10, "train_loss": 1.1277365883191426, "val_loss": 1.1772824724515278, "val_error": "26.570328", "acc_5mm": 0.9868421052631579, "acc_10mm": 5.921052631578947, "learning_rate": 0.002}, {"epoch": 11, "train_loss": 1.3735981285572052, "val_loss": 1.3125056624412537, "val_error": "28.136122", "acc_5mm": 0.3289473684210526, "acc_10mm": 6.25, "learning_rate": 0.0019876944964248404}, {"epoch": 12, "train_loss": 1.216338872909546, "val_loss": 1.0975778301556904, "val_error": "24.892664", "acc_5mm": 0.6578947368421052, "acc_10mm": 6.578947368421052, "learning_rate": 0.001951080988037006}, {"epoch": 13, "train_loss": 1.5235606183608372, "val_loss": 1.9864336649576824, "val_error": "32.352077", "acc_5mm": 0.6578947368421052, "acc_10mm": 5.592105263157895, "learning_rate": 0.0018910610209262734}, {"epoch": 14, "train_loss": 1.2340959856907527, "val_loss": 1.0949199000994365, "val_error": "23.980585", "acc_5mm": 1.3157894736842104, "acc_10mm": 9.868421052631579, "learning_rate": 0.0018091124858777598}, {"epoch": 15, "train_loss": 1.1477576394875844, "val_loss": 1.2164666056632996, "val_error": "25.312395", "acc_5mm": 0.9868421052631579, "acc_10mm": 8.223684210526317, "learning_rate": 0.0017072532277959541}, {"epoch": 16, "train_loss": 0.9835080752770106, "val_loss": 1.309320072333018, "val_error": "25.991358", "acc_5mm": 0.9868421052631579, "acc_10mm": 8.881578947368421, "learning_rate": 0.0015879913596663266}, {"epoch": 17, "train_loss": 1.5102505286534627, "val_loss": 1.095974584420522, "val_error": "25.634827", "acc_5mm": 0.3289473684210526, "acc_10mm": 6.25, "learning_rate": 0.0014542635044896767}, {"epoch": 18, "train_loss": 1.1544525623321533, "val_loss": 1.0640065670013428, "val_error": "23.724127", "acc_5mm": 1.3157894736842104, "acc_10mm": 8.881578947368421, "learning_rate": 0.0013093624858777598}, {"epoch": 19, "train_loss": 1.159242570400238, "val_loss": 1.0567606886227925, "val_error": "24.453026", "acc_5mm": 0.9868421052631579, "acc_10mm": 7.236842105263158, "learning_rate": 0.0011568562478077107}, {"epoch": 20, "train_loss": 1.1592620958884556, "val_loss": 0.8488465150197347, "val_error": "20.853754", "acc_5mm": 0.9868421052631579, "acc_10mm": 9.868421052631579, "learning_rate": 0.0010004999999999999}, {"epoch": 21, "train_loss": 1.1561210056145985, "val_loss": 0.9551706910133362, "val_error": "23.479067", "acc_5mm": 0.6578947368421052, "acc_10mm": 8.552631578947368, "learning_rate": 0.0008441437521922895}, {"epoch": 22, "train_loss": 1.0489506771167119, "val_loss": 0.9560221632321676, "val_error": "23.845243", "acc_5mm": 0.0, "acc_10mm": 6.25, "learning_rate": 0.0006916375141222402}, {"epoch": 23, "train_loss": 1.4107154458761215, "val_loss": 1.0149224003156025, "val_error": "25.624132", "acc_5mm": 0.0, "acc_10mm": 6.578947368421052, "learning_rate": 0.000546736495510323}, {"epoch": 24, "train_loss": 1.0676744828621547, "val_loss": 0.9948998888333639, "val_error": "24.714378", "acc_5mm": 0.9868421052631579, "acc_10mm": 8.223684210526317, "learning_rate": 0.0004130086403336732}, {"epoch": 25, "train_loss": 0.9558320492506027, "val_loss": 0.9085220297177633, "val_error": "24.217941", "acc_5mm": 0.0, "acc_10mm": 5.592105263157895, "learning_rate": 0.0002937467722040458}, {"epoch": 26, "train_loss": 0.9164527530471483, "val_loss": 0.8955031832059225, "val_error": "23.705126", "acc_5mm": 0.0, "acc_10mm": 4.934210526315789, "learning_rate": 0.00019188751412224013}, {"epoch": 27, "train_loss": 1.125086098909378, "val_loss": 0.9547940691312155, "val_error": "24.835932", "acc_5mm": 0.6578947368421052, "acc_10mm": 8.223684210526317, "learning_rate": 0.00010993897907372639}, {"epoch": 28, "train_loss": 0.947666530807813, "val_loss": 0.9554237922032675, "val_error": "24.303577", "acc_5mm": 0.6578947368421052, "acc_10mm": 8.552631578947368, "learning_rate": 4.991901196299404e-05}, {"epoch": 29, "train_loss": 0.930856928229332, "val_loss": 0.9356326262156168, "val_error": "24.173687", "acc_5mm": 0.3289473684210526, "acc_10mm": 7.894736842105263, "learning_rate": 1.330550357515991e-05}, {"epoch": 30, "train_loss": 0.8288217733303705, "val_loss": 0.9169991811116537, "val_error": "23.786945", "acc_5mm": 0.6578947368421052, "acc_10mm": 7.894736842105263, "learning_rate": 0.002}, {"epoch": 31, "train_loss": 0.9972551514705023, "val_loss": 1.3155386845270793, "val_error": "27.665707", "acc_5mm": 0.6578947368421052, "acc_10mm": 5.263157894736842, "learning_rate": 0.0019969188750662615}, {"epoch": 32, "train_loss": 1.1867901881535847, "val_loss": 1.0735114018122356, "val_error": "23.240198", "acc_5mm": 1.644736842105263, "acc_10mm": 12.5, "learning_rate": 0.0019876944964248404}, {"epoch": 33, "train_loss": 1.0623049885034561, "val_loss": 1.0541613499323528, "val_error": "25.288555", "acc_5mm": 0.9868421052631579, "acc_10mm": 8.223684210526317, "learning_rate": 0.0019723837354374777}, {"epoch": 34, "train_loss": 1.0716296285390854, "val_loss": 1.0900444587071736, "val_error": "26.276724", "acc_5mm": 0.0, "acc_10mm": 6.578947368421052, "learning_rate": 0.001951080988037006}, {"epoch": 35, "train_loss": 1.5646723508834839, "val_loss": 1.029453416665395, "val_error": "24.588604", "acc_5mm": 0.6578947368421052, "acc_10mm": 6.578947368421052, "learning_rate": 0.0019239175927450308}], "training_strategies": ["Data normalization", "Improved architecture", "Adaptive loss function", "AdamW optimizer", "Cosine annealing scheduler", "Gradient clipping", "Conservative augmentation"]}