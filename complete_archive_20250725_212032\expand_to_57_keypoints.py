#!/usr/bin/env python3
"""
从12个关键点扩展到57个关键点的实现方案
Expansion from 12 keypoints to 57 keypoints implementation
"""

import numpy as np
import pandas as pd
import os
from pathlib import Path
import torch
import torch.nn as nn
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt

class KeypointExpansionStrategy:
    """关键点扩展策略"""
    
    def __init__(self):
        self.keypoint_mapping = self.create_keypoint_mapping()
        
    def create_keypoint_mapping(self):
        """创建12点到57点的映射关系"""
        
        # 当前12个关键点的定义 (基于我们的成功经验)
        current_12_points = {
            # F1区域 (左髂骨) - 4个关键点
            0: {"region": "F1", "name": "F1-1", "anatomy": "anterior_superior_iliac_spine"},
            1: {"region": "F1", "name": "F1-2", "anatomy": "posterior_superior_iliac_spine"}, 
            2: {"region": "F1", "name": "F1-3", "anatomy": "iliac_crest_anterior"},
            3: {"region": "F1", "name": "F1-4", "anatomy": "iliac_crest_posterior"},
            
            # F2区域 (右髂骨) - 4个关键点  
            4: {"region": "F2", "name": "F2-1", "anatomy": "anterior_superior_iliac_spine"},
            5: {"region": "F2", "name": "F2-2", "anatomy": "posterior_superior_iliac_spine"},
            6: {"region": "F2", "name": "F2-3", "anatomy": "iliac_crest_anterior"},
            7: {"region": "F2", "name": "F2-4", "anatomy": "iliac_crest_posterior"},
            
            # F3区域 (骶骨/尾骨) - 4个关键点
            8: {"region": "F3", "name": "F3-1", "anatomy": "sacral_promontory"},
            9: {"region": "F3", "name": "F3-2", "anatomy": "coccyx_tip"},
            10: {"region": "F3", "name": "F3-3", "anatomy": "sacral_lateral_left"},
            11: {"region": "F3", "name": "F3-4", "anatomy": "sacral_lateral_right"},
        }
        
        # 目标57个关键点的完整定义
        target_57_points = {}
        
        # F1区域: 19个关键点 (0-18)
        f1_points = [
            "髂前上棘", "髂前下棘", "髂嵴前端", "髂结节支", "髂骨翼端", "髂骨内侧", "髂窝", "髂骨结节支",
            "髂结", "髂骨结", "髂嵴", "髂嵴后端", "髂后上棘", "髂后下棘", "髂骨大切迹", "髂骨棘", "髂骨翼端",
            "坐骨棘", "坐骨结"
        ]
        
        for i, anatomy in enumerate(f1_points):
            target_57_points[i] = {
                "region": "F1", 
                "name": f"F1-{i+1}", 
                "anatomy": anatomy,
                "importance": "high" if i < 4 else "medium"
            }
        
        # F2区域: 19个关键点 (19-37)
        f2_points = [name.replace("左", "右") if "左" in name else name for name in f1_points]
        
        for i, anatomy in enumerate(f2_points):
            target_57_points[19+i] = {
                "region": "F2",
                "name": f"F2-{i+1}",
                "anatomy": anatomy,
                "importance": "high" if i < 4 else "medium"
            }
        
        # F3区域: 19个关键点 (38-56)
        f3_points = [
            "骶骨岬", "骶骨前缘", "骶骨孔", "骶骨间", "骶骨嵴", "骶骨比例位置", "骶骨体",
            "骶骨侧缘", "骶骨后缘", "骶骨下缘", "骶骨上缘", "骶骨中央", "骶骨左侧",
            "骶骨右侧", "尾骨尖", "尾骨体", "尾骨关节", "尾骨后缘", "尾骨前缘"
        ]
        
        for i, anatomy in enumerate(f3_points):
            target_57_points[38+i] = {
                "region": "F3",
                "name": f"F3-{i+1}",
                "anatomy": anatomy,
                "importance": "high" if i < 4 else "medium"
            }
        
        return {
            "current_12": current_12_points,
            "target_57": target_57_points,
            "expansion_strategy": self.create_expansion_strategy()
        }
    
    def create_expansion_strategy(self):
        """创建扩展策略"""
        
        return {
            "F1": {
                "core_points": [0, 1, 2, 3],  # 当前12点中的F1核心点
                "expansion_method": "anatomical_interpolation",
                "target_count": 19,
                "interpolation_weights": self.get_f1_interpolation_weights()
            },
            "F2": {
                "core_points": [4, 5, 6, 7],  # 当前12点中的F2核心点
                "expansion_method": "symmetry_mapping", 
                "target_count": 19,
                "symmetry_axis": "sagittal_plane"
            },
            "F3": {
                "core_points": [8, 9, 10, 11],  # 当前12点中的F3核心点
                "expansion_method": "anatomical_interpolation",
                "target_count": 19,
                "interpolation_weights": self.get_f3_interpolation_weights()
            }
        }
    
    def get_f1_interpolation_weights(self):
        """F1区域插值权重"""
        # 基于解剖学知识的插值权重
        weights = {}
        for i in range(19):
            if i < 4:
                # 直接使用核心点
                weights[i] = {"type": "direct", "source": i}
            else:
                # 插值生成
                weights[i] = {
                    "type": "interpolation",
                    "sources": [(i % 4, 0.6), ((i+1) % 4, 0.4)],
                    "offset": np.random.normal(0, 1, 3)  # 小的随机偏移
                }
        return weights
    
    def get_f3_interpolation_weights(self):
        """F3区域插值权重"""
        weights = {}
        for i in range(19):
            if i < 4:
                weights[i] = {"type": "direct", "source": i}
            else:
                # 基于骶骨解剖结构的插值
                weights[i] = {
                    "type": "anatomical_interpolation",
                    "sources": [(0, 0.3), (1, 0.3), (2, 0.2), (3, 0.2)],
                    "anatomical_constraint": "sacral_curvature"
                }
        return weights

class KeypointExpansionNetwork(nn.Module):
    """关键点扩展网络"""
    
    def __init__(self, input_dim=12*3, hidden_dim=512, output_dim=57*3):
        super().__init__()
        
        self.expansion_layers = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, hidden_dim*2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim*2, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        )
        
        # 解剖学约束层
        self.anatomical_constraints = AnatomicalConstraintLayer()
        
    def forward(self, keypoints_12):
        """前向传播"""
        # keypoints_12: [batch_size, 12, 3]
        batch_size = keypoints_12.size(0)
        
        # 展平输入
        x = keypoints_12.view(batch_size, -1)
        
        # 扩展到57个关键点
        expanded = self.expansion_layers(x)
        keypoints_57 = expanded.view(batch_size, 57, 3)
        
        # 应用解剖学约束
        constrained_keypoints = self.anatomical_constraints(keypoints_57)
        
        return constrained_keypoints

class AnatomicalConstraintLayer(nn.Module):
    """解剖学约束层"""
    
    def __init__(self):
        super().__init__()
        
    def forward(self, keypoints_57):
        """应用解剖学约束"""
        # keypoints_57: [batch_size, 57, 3]
        
        # F1-F2对称性约束
        keypoints_57 = self.apply_symmetry_constraint(keypoints_57)
        
        # 区域内距离约束
        keypoints_57 = self.apply_distance_constraints(keypoints_57)
        
        return keypoints_57
    
    def apply_symmetry_constraint(self, keypoints):
        """应用F1-F2对称性约束"""
        # 确保F1和F2区域的对称性
        f1_points = keypoints[:, 0:19, :]    # F1: 0-18
        f2_points = keypoints[:, 19:38, :]   # F2: 19-37
        f3_points = keypoints[:, 38:57, :]   # F3: 38-56
        
        # 计算对称轴
        f1_center = torch.mean(f1_points, dim=1, keepdim=True)
        f2_center = torch.mean(f2_points, dim=1, keepdim=True)
        symmetry_axis = (f1_center + f2_center) / 2
        
        # 应用对称性调整
        f1_adjusted = f1_points - (f1_center - symmetry_axis) * 0.1
        f2_adjusted = f2_points - (f2_center - symmetry_axis) * 0.1
        
        return torch.cat([f1_adjusted, f2_adjusted, f3_points], dim=1)
    
    def apply_distance_constraints(self, keypoints):
        """应用距离约束"""
        # 保持核心关键点之间的距离关系
        return keypoints

def load_original_57_keypoints():
    """加载原始57个关键点数据"""
    
    print("📊 加载原始57关键点数据...")
    
    data_dir = Path("data/Data")
    annotations_dir = data_dir / "annotations"
    stl_dir = data_dir / "stl_models"
    
    all_keypoints_57 = []
    all_point_clouds = []
    all_sample_ids = []
    
    # 遍历所有标注文件
    annotation_files = list(annotations_dir.glob("*-Table-XYZ.CSV"))
    
    for ann_file in annotation_files[:10]:  # 先处理10个样本测试
        sample_id = ann_file.stem.split('-')[0]
        
        try:
            # 读取标注文件
            df = pd.read_csv(ann_file, encoding='gbk')
            
            if len(df) < 57:
                print(f"⚠️ {sample_id}: 关键点数量不足 ({len(df)} < 57)")
                continue
            
            # 提取57个关键点坐标
            keypoints_57 = []
            for _, row in df.iterrows():
                x, y, z = row['X'], row['Y'], row['Z']
                keypoints_57.append([x, y, z])
            
            keypoints_57 = np.array(keypoints_57[:57])  # 确保只取前57个
            
            # 加载对应的点云数据 (合并F1, F2, F3)
            point_cloud = load_combined_point_cloud(stl_dir, sample_id)
            
            if point_cloud is not None:
                all_keypoints_57.append(keypoints_57)
                all_point_clouds.append(point_cloud)
                all_sample_ids.append(sample_id)
                
                print(f"✅ {sample_id}: 57个关键点, {len(point_cloud)}个点")
            
        except Exception as e:
            print(f"❌ {sample_id}: 处理失败 - {e}")
            continue
    
    if all_keypoints_57:
        keypoints_array = np.array(all_keypoints_57)
        point_clouds_array = np.array(all_point_clouds, dtype=object)
        sample_ids_array = np.array(all_sample_ids)
        
        print(f"✅ 成功加载 {len(all_keypoints_57)} 个57关键点样本")
        print(f"   关键点形状: {keypoints_array.shape}")
        
        # 保存57关键点数据集
        np.savez('full_57_keypoints_dataset.npz',
                 point_clouds=point_clouds_array,
                 keypoints=keypoints_array,
                 sample_ids=sample_ids_array)
        
        return point_clouds_array, keypoints_array, sample_ids_array
    
    return None, None, None

def load_combined_point_cloud(stl_dir, sample_id):
    """加载合并的点云数据"""
    
    try:
        import open3d as o3d
        
        combined_points = []
        
        # 加载F1, F2, F3三个STL文件
        for region in ['F_1', 'F_2', 'F_3']:
            stl_file = stl_dir / f"{sample_id}-{region}.stl"
            
            if stl_file.exists():
                mesh = o3d.io.read_triangle_mesh(str(stl_file))
                
                # 从mesh采样点云
                pcd = mesh.sample_points_uniformly(number_of_points=15000)
                points = np.asarray(pcd.points)
                
                combined_points.append(points)
        
        if combined_points:
            # 合并所有区域的点云
            all_points = np.vstack(combined_points)
            
            # 下采样到50000个点
            if len(all_points) > 50000:
                indices = np.random.choice(len(all_points), 50000, replace=False)
                all_points = all_points[indices]
            
            return all_points
        
    except Exception as e:
        print(f"⚠️ 点云加载失败: {e}")
        return None
    
    return None

def create_12_to_57_expansion_dataset():
    """创建12点到57点的扩展数据集"""
    
    print("🔄 创建12点到57点扩展数据集...")
    
    # 加载当前的12关键点数据
    male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz', allow_pickle=True)
    female_data = np.load('archive/old_experiments/f3_reduced_12kp_female.npz', allow_pickle=True)
    
    male_pc = male_data['point_clouds']
    male_kp_12 = male_data['keypoints']
    female_pc = female_data['point_clouds']
    female_kp_12 = female_data['keypoints']
    
    print(f"📊 当前数据: 男性{len(male_pc)}个, 女性{len(female_pc)}个")
    
    # 尝试加载原始57关键点数据作为目标
    original_pc, original_kp_57, original_ids = load_original_57_keypoints()
    
    if original_kp_57 is not None:
        print(f"✅ 原始57关键点数据: {len(original_kp_57)}个样本")
        
        # 创建训练数据对
        # 输入: 12个关键点, 输出: 57个关键点
        expansion_pairs = []
        
        # 从原始57关键点中提取对应的12个关键点作为输入
        for i, kp_57 in enumerate(original_kp_57):
            # 提取核心12个关键点 (基于我们的映射)
            kp_12_extracted = extract_12_from_57(kp_57)
            
            expansion_pairs.append({
                'input_12': kp_12_extracted,
                'target_57': kp_57,
                'point_cloud': original_pc[i],
                'sample_id': original_ids[i]
            })
        
        print(f"✅ 创建了 {len(expansion_pairs)} 个扩展训练对")
        
        # 保存扩展数据集
        save_expansion_dataset(expansion_pairs)
        
        return expansion_pairs
    
    else:
        print("⚠️ 无法加载原始57关键点数据，使用插值方法生成")
        return create_interpolated_57_dataset(male_pc, male_kp_12, female_pc, female_kp_12)

def extract_12_from_57(keypoints_57):
    """从57个关键点中提取对应的12个核心关键点"""
    
    # 基于解剖学对应关系的映射
    mapping_57_to_12 = {
        # F1区域: 选择最重要的4个点
        0: 0,   # F1-1 -> 核心点0
        1: 1,   # F1-2 -> 核心点1  
        2: 2,   # F1-3 -> 核心点2
        12: 3,  # F1-13 -> 核心点3
        
        # F2区域: 对称选择
        19: 4,  # F2-1 -> 核心点4
        20: 5,  # F2-2 -> 核心点5
        21: 6,  # F2-3 -> 核心点6
        31: 7,  # F2-13 -> 核心点7
        
        # F3区域: 选择关键的4个点
        38: 8,  # F3-1 -> 核心点8
        52: 9,  # F3-15 -> 核心点9
        50: 10, # F3-13 -> 核心点10
        51: 11, # F3-14 -> 核心点11
    }
    
    keypoints_12 = np.zeros((12, 3))
    
    for idx_57, idx_12 in mapping_57_to_12.items():
        if idx_57 < len(keypoints_57):
            keypoints_12[idx_12] = keypoints_57[idx_57]
    
    return keypoints_12

def create_interpolated_57_dataset(male_pc, male_kp_12, female_pc, female_kp_12):
    """使用插值方法创建57关键点数据集"""
    
    print("🔄 使用插值方法创建57关键点数据集...")
    
    expansion_strategy = KeypointExpansionStrategy()
    
    all_expansion_pairs = []
    
    # 处理男性数据
    for i, (pc, kp_12) in enumerate(zip(male_pc, male_kp_12)):
        kp_57 = expand_12_to_57_interpolation(kp_12, expansion_strategy)
        
        all_expansion_pairs.append({
            'input_12': kp_12,
            'target_57': kp_57,
            'point_cloud': pc,
            'sample_id': f'male_{i}',
            'gender': 'male'
        })
    
    # 处理女性数据
    for i, (pc, kp_12) in enumerate(zip(female_pc, female_kp_12)):
        kp_57 = expand_12_to_57_interpolation(kp_12, expansion_strategy)
        
        all_expansion_pairs.append({
            'input_12': kp_12,
            'target_57': kp_57,
            'point_cloud': pc,
            'sample_id': f'female_{i}',
            'gender': 'female'
        })
    
    print(f"✅ 创建了 {len(all_expansion_pairs)} 个插值扩展对")
    
    # 保存数据集
    save_expansion_dataset(all_expansion_pairs)
    
    return all_expansion_pairs

def expand_12_to_57_interpolation(keypoints_12, expansion_strategy):
    """使用插值方法将12个关键点扩展到57个"""
    
    keypoints_57 = np.zeros((57, 3))
    strategy = expansion_strategy.keypoint_mapping["expansion_strategy"]
    
    # F1区域扩展 (0-18)
    f1_core = keypoints_12[0:4]  # F1的4个核心点
    f1_weights = strategy["F1"]["interpolation_weights"]
    
    for i in range(19):
        if f1_weights[i]["type"] == "direct":
            keypoints_57[i] = f1_core[f1_weights[i]["source"]]
        else:
            # 插值生成
            point = np.zeros(3)
            for source_idx, weight in f1_weights[i]["sources"]:
                point += f1_core[source_idx] * weight
            
            # 添加小的解剖学合理偏移
            offset = np.random.normal(0, 2, 3)
            keypoints_57[i] = point + offset
    
    # F2区域扩展 (19-37) - 基于F1的对称性
    f2_core = keypoints_12[4:8]  # F2的4个核心点
    
    for i in range(19):
        if i < 4:
            keypoints_57[19+i] = f2_core[i]
        else:
            # 基于F1的对称映射
            f1_relative = keypoints_57[i] - np.mean(keypoints_57[0:4], axis=0)
            f2_center = np.mean(f2_core, axis=0)
            keypoints_57[19+i] = f2_center + f1_relative * np.array([-1, 1, 1])  # X轴对称
    
    # F3区域扩展 (38-56)
    f3_core = keypoints_12[8:12]  # F3的4个核心点
    
    for i in range(19):
        if i < 4:
            keypoints_57[38+i] = f3_core[i]
        else:
            # 基于骶骨解剖结构的插值
            weights = [0.3, 0.3, 0.2, 0.2]
            point = np.zeros(3)
            for j, weight in enumerate(weights):
                point += f3_core[j] * weight
            
            # 添加骶骨曲率相关的偏移
            anatomical_offset = generate_sacral_offset(i, f3_core)
            keypoints_57[38+i] = point + anatomical_offset
    
    return keypoints_57

def generate_sacral_offset(point_index, f3_core):
    """生成符合骶骨解剖结构的偏移"""
    
    # 基于骶骨的解剖曲率生成偏移
    sacral_center = np.mean(f3_core, axis=0)
    
    # 模拟骶骨的S形曲率
    t = point_index / 18.0  # 归一化位置
    
    # S形曲线偏移
    offset_magnitude = 3.0 * np.sin(t * np.pi)
    offset_direction = np.array([0, offset_magnitude, -offset_magnitude * 0.5])
    
    # 添加随机变化
    random_offset = np.random.normal(0, 1, 3)
    
    return offset_direction + random_offset

def save_expansion_dataset(expansion_pairs):
    """保存扩展数据集"""
    
    input_12_list = []
    target_57_list = []
    point_clouds_list = []
    sample_ids_list = []
    
    for pair in expansion_pairs:
        input_12_list.append(pair['input_12'])
        target_57_list.append(pair['target_57'])
        point_clouds_list.append(pair['point_cloud'])
        sample_ids_list.append(pair['sample_id'])
    
    # 保存为npz格式
    np.savez('keypoint_expansion_dataset.npz',
             input_12=np.array(input_12_list),
             target_57=np.array(target_57_list),
             point_clouds=np.array(point_clouds_list, dtype=object),
             sample_ids=np.array(sample_ids_list))
    
    print(f"💾 扩展数据集已保存: keypoint_expansion_dataset.npz")
    print(f"   输入12点形状: {np.array(input_12_list).shape}")
    print(f"   目标57点形状: {np.array(target_57_list).shape}")

def train_expansion_network():
    """训练扩展网络"""
    
    print("🚀 开始训练12点到57点扩展网络...")
    
    # 加载扩展数据集
    data = np.load('keypoint_expansion_dataset.npz', allow_pickle=True)
    input_12 = data['input_12']
    target_57 = data['target_57']
    
    print(f"📊 训练数据: {input_12.shape} -> {target_57.shape}")
    
    # 数据划分
    indices = np.arange(len(input_12))
    train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
    
    # 转换为PyTorch张量
    train_input = torch.FloatTensor(input_12[train_indices])
    train_target = torch.FloatTensor(target_57[train_indices])
    test_input = torch.FloatTensor(input_12[test_indices])
    test_target = torch.FloatTensor(target_57[test_indices])
    
    # 创建网络
    model = KeypointExpansionNetwork()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    criterion = nn.MSELoss()
    
    # 训练循环
    model.train()
    for epoch in range(100):
        optimizer.zero_grad()
        
        # 前向传播
        predicted_57 = model(train_input)
        loss = criterion(predicted_57, train_target)
        
        # 反向传播
        loss.backward()
        optimizer.step()
        
        if epoch % 10 == 0:
            # 测试性能
            model.eval()
            with torch.no_grad():
                test_pred = model(test_input)
                test_loss = criterion(test_pred, test_target)
                
                # 计算平均距离误差
                distances = torch.norm(test_pred - test_target, dim=2)
                mean_error = torch.mean(distances).item()
                
                print(f"Epoch {epoch}: Train Loss: {loss.item():.4f}, "
                      f"Test Loss: {test_loss.item():.4f}, "
                      f"Mean Error: {mean_error:.2f}mm")
            
            model.train()
    
    # 保存模型
    torch.save(model.state_dict(), 'keypoint_expansion_model.pth')
    print("💾 扩展模型已保存: keypoint_expansion_model.pth")
    
    return model

def main():
    """主函数"""
    
    print("🎯 12点到57点关键点扩展系统")
    print("基于解剖学约束的智能扩展策略")
    print("=" * 60)
    
    # 步骤1: 创建扩展数据集
    expansion_pairs = create_12_to_57_expansion_dataset()
    
    if expansion_pairs:
        print(f"✅ 扩展数据集创建成功: {len(expansion_pairs)}个样本")
        
        # 步骤2: 训练扩展网络
        model = train_expansion_network()
        
        # 步骤3: 测试扩展效果
        test_expansion_quality(model)
        
        print("\n🎉 12点到57点扩展系统构建完成！")
        print("📋 下一步建议:")
        print("   1. 使用扩展后的57点数据训练新模型")
        print("   2. 对比12点vs57点的性能差异")
        print("   3. 分析扩展策略的有效性")
        print("   4. 优化解剖学约束参数")
    
    else:
        print("❌ 扩展数据集创建失败")

def test_expansion_quality(model):
    """测试扩展质量"""
    
    print("\n🔍 测试扩展质量...")
    
    # 加载测试数据
    data = np.load('keypoint_expansion_dataset.npz', allow_pickle=True)
    input_12 = data['input_12']
    target_57 = data['target_57']
    
    # 随机选择几个样本测试
    test_indices = np.random.choice(len(input_12), 5, replace=False)
    
    model.eval()
    with torch.no_grad():
        for i, idx in enumerate(test_indices):
            input_kp = torch.FloatTensor(input_12[idx:idx+1])
            target_kp = target_57[idx]
            
            # 预测57个关键点
            predicted_kp = model(input_kp).numpy()[0]
            
            # 计算误差
            errors = np.linalg.norm(predicted_kp - target_kp, axis=1)
            mean_error = np.mean(errors)
            max_error = np.max(errors)
            
            print(f"   样本{i+1}: 平均误差 {mean_error:.2f}mm, 最大误差 {max_error:.2f}mm")
    
    print("✅ 扩展质量测试完成")

if __name__ == "__main__":
    main()
