#!/usr/bin/env python3
"""
渐进式关键点扩展实验
Progressive Keypoint Scaling Experiment
系统性地测试从少量到大量关键点的性能变化
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
import json
from pathlib import Path
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class ScalableUniversalModel(nn.Module):
    """可扩展的通用模型 - 支持不同数量的关键点"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super().__init__()
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 基础特征提取器 (固定架构)
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.<PERSON><PERSON><PERSON>(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.<PERSON>LU(),
            nn.Conv1d(128, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
        )
        
        # 自适应全局特征层
        self.global_adapter = nn.Sequential(
            nn.Conv1d(256, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.1),
        )
        
        # 相互辅助机制 (根据关键点数量自适应)
        self.mutual_assistance = nn.Sequential(
            nn.Linear(num_keypoints * 3, min(256, num_keypoints * 8)),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(min(256, num_keypoints * 8), min(128, num_keypoints * 4)),
            nn.ReLU(),
            nn.Linear(min(128, num_keypoints * 4), num_keypoints * 3)
        )
        
        # 关键点预测头 (根据关键点数量自适应)
        hidden_dim = min(512, max(256, num_keypoints * 16))
        self.keypoint_predictor = nn.Sequential(
            nn.Linear(512, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, num_keypoints * 3)
        )
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取
        features = self.feature_extractor(x)  # [B, 256, N]
        
        # 全局特征适配
        global_features = self.global_adapter(features)  # [B, 512, N]
        global_feat = torch.max(global_features, 2)[0]  # [B, 512]
        
        # 初始预测
        initial_kp = self.keypoint_predictor(global_feat)  # [B, num_keypoints*3]
        
        # 相互辅助
        assistance = self.mutual_assistance(initial_kp)  # [B, num_keypoints*3]
        
        # 最终预测 (残差连接)
        final_kp = initial_kp + 0.3 * assistance
        final_kp = final_kp.view(batch_size, self.num_keypoints, 3)
        
        return final_kp

class ProgressiveKeypointExperiment:
    """渐进式关键点扩展实验器"""
    
    def __init__(self, device='cuda:1'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        self.experiment_results = []
        
    def load_original_57_keypoint_data(self):
        """加载原始57关键点数据"""
        print("📥 加载原始57关键点数据")
        print("=" * 50)
        
        try:
            # 这里需要加载你的原始57关键点数据
            # 假设数据格式为 [N_samples, 57, 3]
            print("⚠️  需要提供原始57关键点数据路径")
            print("   当前使用模拟数据进行演示")
            
            # 模拟57关键点数据 (实际使用时替换为真实数据)
            np.random.seed(42)
            n_samples = 97  # 与12关键点数据集保持一致
            
            # 模拟点云数据
            point_clouds = []
            keypoints_57 = []
            
            for i in range(n_samples):
                # 模拟点云 (50000个点)
                pc = np.random.randn(50000, 3) * 50
                point_clouds.append(pc)
                
                # 模拟57个关键点
                kp = np.random.randn(57, 3) * 30
                keypoints_57.append(kp)
            
            point_clouds = np.array(point_clouds)
            keypoints_57 = np.array(keypoints_57)
            
            print(f"✅ 数据加载成功:")
            print(f"   样本数: {len(point_clouds)}")
            print(f"   关键点数: {keypoints_57.shape[1]}")
            print(f"   点云形状: {point_clouds.shape}")
            print(f"   关键点形状: {keypoints_57.shape}")
            
            return point_clouds, keypoints_57
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return None, None
    
    def create_keypoint_subsets(self, keypoints_57):
        """创建不同数量的关键点子集"""
        print("\n🔢 创建关键点子集")
        print("=" * 50)
        
        # 定义不同的关键点数量
        keypoint_counts = [6, 9, 12, 19, 28, 38, 47, 57]
        
        keypoint_subsets = {}
        
        for num_kp in keypoint_counts:
            # 均匀采样关键点
            indices = np.linspace(0, 56, num_kp, dtype=int)
            subset_kp = keypoints_57[:, indices, :]
            keypoint_subsets[num_kp] = subset_kp
            
            print(f"  {num_kp}关键点: 形状{subset_kp.shape}, 索引{indices[:5]}...")
        
        return keypoint_subsets, keypoint_counts
    
    def train_model_for_keypoints(self, point_clouds, keypoints, num_keypoints, stage_name):
        """为特定数量的关键点训练模型"""
        print(f"\n🎯 训练{num_keypoints}关键点模型 - {stage_name}")
        print("=" * 50)
        
        # 数据分割
        train_pc, test_pc, train_kp, test_kp = train_test_split(
            point_clouds, keypoints, test_size=0.2, random_state=42)
        
        print(f"📊 数据分割:")
        print(f"   训练: {len(train_pc)}样本")
        print(f"   测试: {len(test_pc)}样本")
        print(f"   关键点数: {num_keypoints}")
        
        # 创建模型
        model = ScalableUniversalModel(num_points=50000, num_keypoints=num_keypoints).to(self.device)
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        
        print(f"🏗️ {num_keypoints}关键点模型:")
        print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 转换为张量
        train_pc_tensor = torch.FloatTensor(train_pc).to(self.device)
        train_kp_tensor = torch.FloatTensor(train_kp).to(self.device)
        test_pc_tensor = torch.FloatTensor(test_pc).to(self.device)
        test_kp_tensor = torch.FloatTensor(test_kp).to(self.device)
        
        # 创建数据加载器
        batch_size = max(2, min(8, len(train_pc) // 4))
        train_dataset = TensorDataset(train_pc_tensor, train_kp_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        
        # 训练循环
        model.train()
        best_loss = float('inf')
        patience = 0
        max_epochs = min(100, max(50, num_keypoints * 2))  # 根据关键点数量调整训练轮数
        
        for epoch in range(max_epochs):
            epoch_loss = 0.0
            
            for batch_pc, batch_kp in train_loader:
                optimizer.zero_grad()
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                loss.backward()
                optimizer.step()
                epoch_loss += loss.item()
            
            avg_loss = epoch_loss / len(train_loader)
            scheduler.step(avg_loss)
            
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience = 0
                torch.save(model.state_dict(), f'best_model_{num_keypoints}kp.pth')
            else:
                patience += 1
                if patience >= 15:
                    print(f"早停于epoch {epoch+1}")
                    break
            
            if epoch % 20 == 0:
                print(f"Epoch {epoch+1}: Loss = {avg_loss:.6f}")
        
        # 加载最佳模型并测试
        model.load_state_dict(torch.load(f'best_model_{num_keypoints}kp.pth'))
        model.eval()
        
        with torch.no_grad():
            predicted = model(test_pc_tensor)
            test_errors = torch.norm(predicted - test_kp_tensor, dim=2)
            avg_error = torch.mean(test_errors).item()
            
            # 计算准确率
            sample_errors = torch.mean(test_errors, dim=1)
            errors_5mm = torch.sum(sample_errors <= 5.0).item()
            errors_10mm = torch.sum(sample_errors <= 10.0).item()
            errors_15mm = torch.sum(sample_errors <= 15.0).item()
            
            acc_5mm = (errors_5mm / len(test_pc)) * 100
            acc_10mm = (errors_10mm / len(test_pc)) * 100
            acc_15mm = (errors_15mm / len(test_pc)) * 100
        
        result = {
            'num_keypoints': num_keypoints,
            'stage': stage_name,
            'train_samples': len(train_pc),
            'test_samples': len(test_pc),
            'avg_error': avg_error,
            'accuracy_5mm': acc_5mm,
            'accuracy_10mm': acc_10mm,
            'accuracy_15mm': acc_15mm,
            'medical_grade': avg_error <= 10.0,
            'excellent_grade': avg_error <= 5.0,
            'parameters': sum(p.numel() for p in model.parameters()),
            'epochs_trained': epoch + 1
        }
        
        print(f"\n📊 {num_keypoints}关键点模型结果:")
        print(f"   平均误差: {result['avg_error']:.2f}mm")
        print(f"   5mm准确率: {result['accuracy_5mm']:.1f}%")
        print(f"   10mm准确率: {result['accuracy_10mm']:.1f}%")
        print(f"   15mm准确率: {result['accuracy_15mm']:.1f}%")
        print(f"   医疗级达标: {'✅' if result['medical_grade'] else '❌'}")
        print(f"   优秀级达标: {'✅' if result['excellent_grade'] else '❌'}")
        print(f"   参数数量: {result['parameters']:,}")
        
        self.experiment_results.append(result)
        return result
    
    def run_progressive_experiment(self):
        """运行渐进式关键点扩展实验"""
        print("🚀 开始渐进式关键点扩展实验")
        print("Progressive Keypoint Scaling Experiment")
        print("=" * 70)
        
        # 加载57关键点数据
        point_clouds, keypoints_57 = self.load_original_57_keypoint_data()
        if point_clouds is None:
            print("❌ 数据加载失败，退出")
            return
        
        # 创建关键点子集
        keypoint_subsets, keypoint_counts = self.create_keypoint_subsets(keypoints_57)
        
        # 逐步训练不同数量的关键点模型
        for num_kp in keypoint_counts:
            keypoints = keypoint_subsets[num_kp]
            stage_name = f"progressive_{num_kp}kp"
            
            try:
                self.train_model_for_keypoints(point_clouds, keypoints, num_kp, stage_name)
            except Exception as e:
                print(f"❌ {num_kp}关键点训练失败: {e}")
                continue
        
        # 保存结果
        self.save_progressive_results()
        
        # 创建分析报告
        self.create_scaling_analysis()
        
        return self.experiment_results
    
    def save_progressive_results(self):
        """保存渐进式实验结果"""
        results = {
            'experiment_type': 'progressive_keypoint_scaling',
            'description': '系统性测试从6到57关键点的性能变化',
            'keypoint_counts': [r['num_keypoints'] for r in self.experiment_results],
            'results': self.experiment_results,
            'summary': {
                'total_experiments': len(self.experiment_results),
                'keypoint_range': f"{min([r['num_keypoints'] for r in self.experiment_results])}-{max([r['num_keypoints'] for r in self.experiment_results])}",
                'best_performance': min([r['avg_error'] for r in self.experiment_results]),
                'worst_performance': max([r['avg_error'] for r in self.experiment_results])
            },
            'timestamp': '2025-07-25'
        }
        
        with open('progressive_keypoint_scaling_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 渐进式实验结果已保存到 progressive_keypoint_scaling_results.json")
    
    def create_scaling_analysis(self):
        """创建扩展性分析"""
        print("\n📈 创建扩展性分析")
        print("=" * 50)
        
        if not self.experiment_results:
            print("❌ 没有实验结果可分析")
            return
        
        # 提取数据
        keypoint_counts = [r['num_keypoints'] for r in self.experiment_results]
        avg_errors = [r['avg_error'] for r in self.experiment_results]
        acc_10mm = [r['accuracy_10mm'] for r in self.experiment_results]
        parameters = [r['parameters'] for r in self.experiment_results]
        
        # 创建可视化
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 子图1: 性能 vs 关键点数量
        ax1.plot(keypoint_counts, avg_errors, 'bo-', linewidth=2, markersize=8)
        ax1.axhline(y=10, color='orange', linestyle='--', label='Medical Grade (10mm)')
        ax1.axhline(y=5, color='red', linestyle='--', label='Excellent Grade (5mm)')
        ax1.set_xlabel('Number of Keypoints')
        ax1.set_ylabel('Average Error (mm)')
        ax1.set_title('Performance vs Number of Keypoints')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (x, y) in enumerate(zip(keypoint_counts, avg_errors)):
            ax1.annotate(f'{y:.1f}mm', (x, y), textcoords="offset points", xytext=(0,10), ha='center')
        
        # 子图2: 10mm准确率 vs 关键点数量
        ax2.plot(keypoint_counts, acc_10mm, 'go-', linewidth=2, markersize=8)
        ax2.axhline(y=90, color='orange', linestyle='--', label='90% Target')
        ax2.set_xlabel('Number of Keypoints')
        ax2.set_ylabel('10mm Accuracy (%)')
        ax2.set_title('Medical Grade Accuracy vs Number of Keypoints')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 子图3: 参数数量 vs 关键点数量
        ax3.plot(keypoint_counts, [p/1000 for p in parameters], 'ro-', linewidth=2, markersize=8)
        ax3.set_xlabel('Number of Keypoints')
        ax3.set_ylabel('Parameters (thousands)')
        ax3.set_title('Model Complexity vs Number of Keypoints')
        ax3.grid(True, alpha=0.3)
        
        # 子图4: 效率分析 (性能/参数)
        efficiency = [err * (param/1000) for err, param in zip(avg_errors, parameters)]
        ax4.plot(keypoint_counts, efficiency, 'mo-', linewidth=2, markersize=8)
        ax4.set_xlabel('Number of Keypoints')
        ax4.set_ylabel('Error × Parameters (mm·k)')
        ax4.set_title('Model Efficiency (Lower is Better)')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('progressive_keypoint_scaling_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✅ 扩展性分析图表已保存为 progressive_keypoint_scaling_analysis.png")
        
        # 创建文字分析
        analysis = {
            "扩展性趋势分析": {
                "性能变化": f"从{min(avg_errors):.2f}mm到{max(avg_errors):.2f}mm",
                "最佳关键点数": keypoint_counts[avg_errors.index(min(avg_errors))],
                "性能下降率": f"{(max(avg_errors) - min(avg_errors)) / min(avg_errors) * 100:.1f}%",
                "参数增长": f"从{min(parameters):,}到{max(parameters):,}"
            },
            
            "关键发现": [
                f"最佳性能在{keypoint_counts[avg_errors.index(min(avg_errors))]}关键点时达到",
                f"医疗级达标的关键点数量范围: {[kp for kp, err in zip(keypoint_counts, avg_errors) if err <= 10]}",
                f"参数效率最高的配置: {keypoint_counts[efficiency.index(min(efficiency))]}关键点",
                "关键点数量增加时性能呈现递减趋势" if avg_errors[-1] > avg_errors[0] else "性能随关键点数量增加而改善"
            ],
            
            "实用建议": [
                "选择平衡性能和复杂度的关键点数量",
                "考虑具体应用场景的精度要求",
                "评估计算资源限制",
                "验证关键点的解剖学意义"
            ]
        }
        
        print(f"\n📊 扩展性分析结果:")
        print(f"  最佳性能: {min(avg_errors):.2f}mm ({keypoint_counts[avg_errors.index(min(avg_errors))]}关键点)")
        print(f"  性能范围: {min(avg_errors):.2f}-{max(avg_errors):.2f}mm")
        print(f"  医疗级达标: {len([err for err in avg_errors if err <= 10])}/{len(avg_errors)}个配置")
        
        return analysis

def main():
    """主函数"""
    print("🎯 渐进式关键点扩展实验")
    print("Progressive Keypoint Scaling Experiment")
    print("=" * 60)
    
    # 创建实验器
    experimenter = ProgressiveKeypointExperiment()
    
    # 运行渐进式实验
    results = experimenter.run_progressive_experiment()
    
    if results:
        print(f"\n🎉 渐进式实验完成总结:")
        print(f"✅ 完成{len(results)}个不同关键点数量的实验")
        print(f"📊 关键点范围: {min([r['num_keypoints'] for r in results])}-{max([r['num_keypoints'] for r in results])}")
        print(f"🎯 最佳性能: {min([r['avg_error'] for r in results]):.2f}mm")
        print(f"📈 为读者提供了完整的扩展性分析资料")
    else:
        print("❌ 实验过程中出现问题")

if __name__ == "__main__":
    main()
