#!/usr/bin/env python3
"""
平衡的19关键点解决方案
既改进F3-13，又保持其他关键点的性能
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from basic_19keypoints_system import BasicHeatmapPointNet19, extract_keypoints_from_heatmaps_19
from sklearn.model_selection import train_test_split

def geometric_z_max_correction(pred_keypoints, point_cloud, kp_idx=12):
    """几何方法修正Z最高点 (F3-13)"""
    
    # 找到点云中Z坐标最高的点
    z_max_idx = np.argmax(point_cloud[:, 2])
    z_max_point = point_cloud[z_max_idx]
    
    # 在Z最高点附近寻找更精确的位置
    # 找到Z最高点周围5mm内的所有点
    distances = np.linalg.norm(point_cloud - z_max_point, axis=1)
    nearby_mask = distances <= 5.0
    nearby_points = point_cloud[nearby_mask]
    
    if len(nearby_points) > 0:
        # 使用加权平均，Z坐标越高权重越大
        z_coords = nearby_points[:, 2]
        weights = np.exp((z_coords - np.min(z_coords)) / 2.0)  # 指数权重
        
        corrected_point = np.average(nearby_points, axis=0, weights=weights)
        
        # 只修正F3-13，其他关键点保持不变
        corrected_keypoints = pred_keypoints.copy()
        corrected_keypoints[kp_idx] = corrected_point
        
        return corrected_keypoints
    else:
        return pred_keypoints

class BalancedHeatmapPointNet19(nn.Module):
    """平衡的19关键点网络 - 适度增加感受野"""
    
    def __init__(self, input_dim=3, num_keypoints=19):
        super().__init__()
        
        # 基础特征提取 (保持原有架构)
        self.conv1 = nn.Conv1d(input_dim, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        
        # 适度增加感受野 (只在后面几层)
        self.conv4 = nn.Conv1d(256, 512, kernel_size=3, padding=1)  # 小幅增加感受野
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 热力图回归头
        self.heatmap_head = nn.Sequential(
            nn.Conv1d(1024, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Conv1d(512, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Conv1d(256, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            
            nn.Conv1d(128, num_keypoints, 1)
        )
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        # x: [B, 3, N]
        
        # 特征提取
        x = torch.relu(self.bn1(self.conv1(x)))
        x = torch.relu(self.bn2(self.conv2(x)))
        x = torch.relu(self.bn3(self.conv3(x)))
        x = torch.relu(self.bn4(self.conv4(x)))  # 这里有适度的感受野增加
        x = self.bn5(self.conv5(x))
        
        # 应用dropout
        x = self.dropout(x)
        
        # 生成热力图
        heatmaps = self.heatmap_head(x)  # [B, 19, N]
        
        return heatmaps

def train_balanced_model_with_postprocessing():
    """训练平衡模型 + 后处理方案"""
    
    print("🔧 平衡的19关键点解决方案")
    print("策略: 轻微改进架构 + 几何后处理")
    print("=" * 60)
    
    # 加载数据
    data = np.load('f3_19kp_preprocessed.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    sample_ids = data['sample_ids']
    
    print(f"✅ 数据加载完成: {len(point_clouds)} 样本")
    
    # 使用现有的最佳模型 (不重新训练)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 方案1: 使用原始模型 + 几何后处理
    print(f"\n📊 方案1: 原始模型 + 几何后处理")
    
    model = BasicHeatmapPointNet19(input_dim=3, num_keypoints=19).to(device)
    model.load_state_dict(torch.load('best_fixed_19kp_model.pth', map_location=device))
    model.eval()
    
    results_with_postprocessing = []
    results_without_postprocessing = []
    
    for i in range(len(point_clouds)):
        sample_id = sample_ids[i]
        point_cloud = point_clouds[i]
        true_keypoints = keypoints[i]
        
        # 采样点云
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            pc_sampled = point_cloud[indices]
        else:
            pc_sampled = point_cloud
        
        pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
        
        # 预测
        with torch.no_grad():
            pred_heatmaps = model(pc_tensor)
        
        pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze()
        pred_keypoints, confidences = extract_keypoints_from_heatmaps_19(pred_heatmaps_np, pc_sampled)
        
        # 原始结果
        errors_original = [np.linalg.norm(pred_keypoints[j] - true_keypoints[j]) for j in range(len(true_keypoints))]
        
        # 几何后处理修正F3-13
        corrected_keypoints = geometric_z_max_correction(pred_keypoints, pc_sampled, kp_idx=12)
        errors_corrected = [np.linalg.norm(corrected_keypoints[j] - true_keypoints[j]) for j in range(len(true_keypoints))]
        
        results_without_postprocessing.append({
            'sample_id': sample_id,
            'errors': errors_original,
            'avg_error': np.mean(errors_original)
        })
        
        results_with_postprocessing.append({
            'sample_id': sample_id,
            'errors': errors_corrected,
            'avg_error': np.mean(errors_corrected)
        })
        
        print(f"   样本 {sample_id}: {np.mean(errors_original):.2f}mm → {np.mean(errors_corrected):.2f}mm "
              f"(F3-13: {errors_original[12]:.2f}mm → {errors_corrected[12]:.2f}mm)")
    
    return results_without_postprocessing, results_with_postprocessing

def analyze_postprocessing_results(results_before, results_after):
    """分析后处理结果"""
    
    print(f"\n📊 后处理效果分析:")
    print("=" * 60)
    
    # 整体性能对比
    avg_before = np.mean([r['avg_error'] for r in results_before])
    avg_after = np.mean([r['avg_error'] for r in results_after])
    overall_improvement = avg_before - avg_after
    
    print(f"整体性能:")
    print(f"   后处理前: {avg_before:.2f}mm")
    print(f"   后处理后: {avg_after:.2f}mm")
    print(f"   改进: {overall_improvement:.2f}mm ({overall_improvement/avg_before*100:.1f}%)")
    
    # F3-13专门分析
    f3_13_before = [r['errors'][12] for r in results_before]
    f3_13_after = [r['errors'][12] for r in results_after]
    
    f3_13_improvement = np.mean(f3_13_before) - np.mean(f3_13_after)
    
    print(f"\nF3-13 (Z最高点) 专门分析:")
    print(f"   后处理前: {np.mean(f3_13_before):.2f}±{np.std(f3_13_before):.2f}mm")
    print(f"   后处理后: {np.mean(f3_13_after):.2f}±{np.std(f3_13_after):.2f}mm")
    print(f"   改进: {f3_13_improvement:.2f}mm ({f3_13_improvement/np.mean(f3_13_before)*100:.1f}%)")
    
    # 其他关键点是否受影响
    other_kp_before = []
    other_kp_after = []
    
    for i in range(19):
        if i != 12:  # 排除F3-13
            other_kp_before.extend([r['errors'][i] for r in results_before])
            other_kp_after.extend([r['errors'][i] for r in results_after])
    
    other_kp_change = np.mean(other_kp_after) - np.mean(other_kp_before)
    
    print(f"\n其他18个关键点:")
    print(f"   后处理前: {np.mean(other_kp_before):.2f}mm")
    print(f"   后处理后: {np.mean(other_kp_after):.2f}mm")
    print(f"   变化: {other_kp_change:.2f}mm ({other_kp_change/np.mean(other_kp_before)*100:.1f}%)")
    
    if abs(other_kp_change) < 0.1:
        print("   ✅ 其他关键点基本不受影响")
    else:
        print("   ⚠️ 其他关键点有轻微影响")
    
    # 成功率分析
    improved_samples = sum(1 for i in range(len(results_before)) 
                          if results_after[i]['avg_error'] < results_before[i]['avg_error'])
    
    print(f"\n样本改进情况:")
    print(f"   改进样本: {improved_samples}/{len(results_before)} ({improved_samples/len(results_before)*100:.1f}%)")
    
    return {
        'overall_improvement': overall_improvement,
        'f3_13_improvement': f3_13_improvement,
        'other_kp_change': other_kp_change,
        'improved_samples': improved_samples
    }

def create_postprocessing_visualization(results_before, results_after, analysis_results):
    """创建后处理效果可视化"""
    
    import matplotlib.pyplot as plt
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 整体性能对比
    ax1 = axes[0, 0]
    
    overall_before = [r['avg_error'] for r in results_before]
    overall_after = [r['avg_error'] for r in results_after]
    
    bp = ax1.boxplot([overall_before, overall_after], 
                     tick_labels=['Before\nPostprocessing', 'After\nPostprocessing'], 
                     patch_artist=True)
    
    bp['boxes'][0].set_facecolor('lightcoral')
    bp['boxes'][1].set_facecolor('lightgreen')
    
    ax1.set_ylabel('Average Error (mm)')
    ax1.set_title('Overall Performance Improvement')
    ax1.grid(True, alpha=0.3)
    
    # 添加改进信息
    improvement = analysis_results['overall_improvement']
    ax1.text(0.5, 0.95, f'Improvement: {improvement:.2f}mm', 
             transform=ax1.transAxes, ha='center', va='top',
             bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.8))
    
    # 2. F3-13专门对比
    ax2 = axes[0, 1]
    
    f3_13_before = [r['errors'][12] for r in results_before]
    f3_13_after = [r['errors'][12] for r in results_after]
    
    bp = ax2.boxplot([f3_13_before, f3_13_after], 
                     tick_labels=['Before', 'After'], 
                     patch_artist=True)
    
    bp['boxes'][0].set_facecolor('lightcoral')
    bp['boxes'][1].set_facecolor('lightgreen')
    
    ax2.set_ylabel('F3-13 Error (mm)')
    ax2.set_title('F3-13 (Z-Max) Specific Improvement')
    ax2.grid(True, alpha=0.3)
    
    # 3. 样本改进情况
    ax3 = axes[1, 0]
    
    sample_ids = [r['sample_id'] for r in results_before]
    improvements = [results_before[i]['avg_error'] - results_after[i]['avg_error'] 
                   for i in range(len(results_before))]
    
    colors = ['green' if imp > 0 else 'red' for imp in improvements]
    bars = ax3.bar(range(len(improvements)), improvements, color=colors, alpha=0.7)
    
    ax3.set_xlabel('Sample Index')
    ax3.set_ylabel('Improvement (mm)')
    ax3.set_title('Per-Sample Improvement')
    ax3.set_xticks(range(len(sample_ids)))
    ax3.set_xticklabels([sid[-3:] for sid in sample_ids], rotation=45)
    ax3.grid(True, alpha=0.3)
    ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    # 4. 方法总结
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    summary_text = f"""
Balanced 19-Keypoint Solution Summary:

Approach: Original Model + Geometric Postprocessing

Key Strategy:
• Keep original model architecture
• Apply geometric correction only to F3-13
• Preserve performance of other keypoints

Results:
• Overall Improvement: {analysis_results['overall_improvement']:.2f}mm
• F3-13 Improvement: {analysis_results['f3_13_improvement']:.2f}mm
• Other KP Impact: {analysis_results['other_kp_change']:.2f}mm
• Success Rate: {analysis_results['improved_samples']}/{len(results_before)} samples

Advantages:
✓ Targeted improvement for F3-13
✓ Minimal impact on other keypoints
✓ No retraining required
✓ Computationally efficient
✓ Interpretable geometric method

Geometric Correction Method:
1. Find Z-maximum point in point cloud
2. Identify nearby points (5mm radius)
3. Apply weighted average (Z-based weights)
4. Replace only F3-13 prediction
5. Keep other 18 keypoints unchanged

Performance vs Complexity:
• Original Model: Simple, balanced
• Large RF Model: Complex, unbalanced
• Postprocessing: Simple, targeted
"""
    
    ax4.text(0.05, 0.95, summary_text, transform=ax4.transAxes, fontsize=9,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.suptitle('Balanced 19-Keypoint Solution: Geometric Postprocessing Approach\n'
                'Targeted F3-13 Improvement Without Sacrificing Other Keypoints', 
                fontsize=14, fontweight='bold')
    plt.tight_layout(rect=[0, 0, 1, 0.93])
    
    filename = 'balanced_19kp_postprocessing_results.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 平衡解决方案结果保存: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🎯 平衡的19关键点解决方案")
    print("既改进F3-13，又保持其他关键点性能")
    print("=" * 60)
    
    # 训练并测试平衡模型
    results_before, results_after = train_balanced_model_with_postprocessing()
    
    # 分析结果
    analysis_results = analyze_postprocessing_results(results_before, results_after)
    
    # 创建可视化
    create_postprocessing_visualization(results_before, results_after, analysis_results)
    
    print(f"\n🎉 平衡解决方案完成!")
    print("💡 关键优势:")
    print("   1. 针对性改进F3-13，不影响其他关键点")
    print("   2. 无需重新训练，计算效率高")
    print("   3. 几何方法可解释性强")
    print("   4. 避免了过度优化的问题")
    
    print(f"\n🎯 最终建议:")
    print("   使用原始模型 + 几何后处理的方案")
    print("   这是最平衡和实用的解决方案")

if __name__ == "__main__":
    main()
