#!/usr/bin/env python3
"""
高级小样本学习方案 - 97样本医疗关键点检测
Advanced Few-Shot Learning for 97 Medical Samples
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import random
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
import time
from torch.utils.data import DataLoader, Dataset

class SelfSupervisedPretrainer(nn.Module):
    """自监督预训练模块"""
    
    def __init__(self, input_dim=3, hidden_dim=256):
        super().__init__()
        
        # 编码器
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 128),
            nn.ReLU(),
            nn.Linear(128, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # 对比学习投影头
        self.projection_head = nn.Sequential(
            nn.Linear(hidden_dim, 128),
            nn.<PERSON>L<PERSON>(),
            nn.Linear(128, 64)
        )
        
        # 掩码重建头
        self.reconstruction_head = nn.Sequential(
            nn.Linear(hidden_dim, 128),
            nn.ReLU(),
            nn.Linear(128, input_dim)
        )
        
    def forward(self, x, task='contrastive'):
        """
        Args:
            x: (B, N, 3) 点云
            task: 'contrastive' 或 'reconstruction'
        """
        # 特征提取
        features = self.encoder(x)  # (B, N, hidden_dim)
        global_feature = torch.max(features, dim=1)[0]  # (B, hidden_dim)
        
        if task == 'contrastive':
            return self.projection_head(global_feature)
        elif task == 'reconstruction':
            return self.reconstruction_head(features)
        else:
            return global_feature

class MAMLKeypointDetector(nn.Module):
    """MAML元学习关键点检测器"""
    
    def __init__(self, pretrained_encoder=None, hidden_dim=256):
        super().__init__()
        
        if pretrained_encoder:
            self.encoder = pretrained_encoder.encoder
            # 冻结预训练编码器的部分层
            for param in self.encoder[:4].parameters():
                param.requires_grad = False
        else:
            self.encoder = nn.Sequential(
                nn.Linear(3, 64),
                nn.ReLU(),
                nn.Linear(64, 128),
                nn.ReLU(),
                nn.Linear(128, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, hidden_dim)
            )
        
        # 关键点回归头
        self.keypoint_head = nn.Sequential(
            nn.Linear(hidden_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, 19 * 3)
        )
        
    def forward(self, x):
        features = self.encoder(x)
        global_feature = torch.max(features, dim=1)[0]
        keypoints = self.keypoint_head(global_feature)
        return keypoints.view(-1, 19, 3)

class ContrastiveLearning:
    """对比学习训练"""
    
    def __init__(self, model, temperature=0.1):
        self.model = model
        self.temperature = temperature
        
    def create_augmented_pairs(self, point_cloud):
        """创建增强样本对"""
        # 原始样本
        original = point_cloud.clone()
        
        # 增强样本1: 旋转
        angle1 = np.random.uniform(-0.1, 0.1)
        aug1 = self.rotate_point_cloud(original, angle1)
        
        # 增强样本2: 噪声
        noise = torch.randn_like(original) * 0.01
        aug2 = original + noise
        
        return original, aug1, aug2
    
    def rotate_point_cloud(self, pc, angle):
        """旋转点云"""
        cos_a, sin_a = np.cos(angle), np.sin(angle)
        rotation_matrix = torch.tensor([
            [cos_a, -sin_a, 0],
            [sin_a, cos_a, 0],
            [0, 0, 1]
        ], dtype=pc.dtype, device=pc.device)
        
        return torch.matmul(pc, rotation_matrix.T)
    
    def contrastive_loss(self, z1, z2):
        """对比损失"""
        z1 = F.normalize(z1, dim=1)
        z2 = F.normalize(z2, dim=1)
        
        # 计算相似度矩阵
        similarity_matrix = torch.matmul(z1, z2.T) / self.temperature
        
        # 正样本在对角线上
        batch_size = z1.size(0)
        labels = torch.arange(batch_size).to(z1.device)
        
        loss = F.cross_entropy(similarity_matrix, labels)
        return loss

class AdvancedFewShotTrainer:
    """高级小样本学习训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def self_supervised_pretraining(self, dataset, epochs=50):
        """自监督预训练阶段"""
        print("🔧 开始自监督预训练...")
        
        # 创建预训练模型
        pretrain_model = SelfSupervisedPretrainer().to(self.device)
        optimizer = torch.optim.Adam(pretrain_model.parameters(), lr=0.001)
        contrastive_learner = ContrastiveLearning(pretrain_model)
        
        # 使用所有97个样本进行预训练
        all_point_clouds = dataset.point_clouds
        
        for epoch in range(epochs):
            total_loss = 0
            num_batches = 0
            
            # 随机采样批次
            for _ in range(20):  # 每个epoch 20个批次
                # 随机选择8个样本
                batch_indices = np.random.choice(len(all_point_clouds), 8, replace=False)
                batch_pcs = torch.FloatTensor(all_point_clouds[batch_indices]).to(self.device)
                
                optimizer.zero_grad()
                
                batch_loss = 0
                for pc in batch_pcs:
                    # 创建增强对
                    orig, aug1, aug2 = contrastive_learner.create_augmented_pairs(pc.unsqueeze(0))
                    
                    # 获取特征
                    z1 = pretrain_model(aug1, task='contrastive')
                    z2 = pretrain_model(aug2, task='contrastive')
                    
                    # 对比损失
                    loss = contrastive_learner.contrastive_loss(z1, z2)
                    batch_loss += loss
                
                batch_loss = batch_loss / len(batch_pcs)
                batch_loss.backward()
                optimizer.step()
                
                total_loss += batch_loss.item()
                num_batches += 1
            
            avg_loss = total_loss / num_batches
            if epoch % 10 == 0:
                print(f"预训练 Epoch {epoch}: Loss = {avg_loss:.4f}")
        
        print("✅ 自监督预训练完成")
        return pretrain_model
    
    def meta_learning_training(self, dataset, pretrained_model=None, episodes=1000):
        """元学习训练阶段"""
        print("🎯 开始元学习训练...")
        
        # 创建MAML模型
        maml_model = MAMLKeypointDetector(pretrained_model).to(self.device)
        meta_optimizer = torch.optim.Adam(maml_model.parameters(), lr=0.001)
        
        results = {}
        
        # 不同shot配置的实验
        for k_shot in [1, 3, 5, 10]:
            print(f"\n🔬 {k_shot}-shot 元学习实验")
            
            # 重置模型
            if pretrained_model:
                maml_model = MAMLKeypointDetector(pretrained_model).to(self.device)
            else:
                maml_model = MAMLKeypointDetector().to(self.device)
            meta_optimizer = torch.optim.Adam(maml_model.parameters(), lr=0.001)
            
            for episode in range(episodes):
                meta_optimizer.zero_grad()
                
                # 采样任务
                support_data, query_data = dataset.sample_episode('train', k_shot, 3)
                
                # 内循环更新
                adapted_model = self.inner_loop_update(maml_model, support_data, steps=5)
                
                # 外循环损失
                query_pcs, query_kps = query_data
                query_pcs = torch.FloatTensor(query_pcs).to(self.device)
                query_kps = torch.FloatTensor(query_kps).to(self.device)
                
                pred_kps = adapted_model(query_pcs)
                meta_loss = F.mse_loss(pred_kps, query_kps)
                
                meta_loss.backward()
                meta_optimizer.step()
                
                if episode % 200 == 0:
                    val_error = self.evaluate_meta_model(maml_model, dataset, k_shot)
                    print(f"Episode {episode}: Meta_Loss={meta_loss:.4f}, Val_Error={val_error:.2f}mm")
            
            # 最终测试
            test_error = self.evaluate_meta_model(maml_model, dataset, k_shot, split='test')
            results[k_shot] = test_error
            print(f"✅ {k_shot}-shot 最终误差: {test_error:.2f}mm")
        
        return results
    
    def inner_loop_update(self, model, support_data, steps=5, lr=0.01):
        """内循环快速适应"""
        support_pcs, support_kps = support_data
        support_pcs = torch.FloatTensor(support_pcs).to(self.device)
        support_kps = torch.FloatTensor(support_kps).to(self.device)

        # 简化版本：直接在支持集上微调
        adapted_model = type(model)().to(self.device)
        adapted_model.load_state_dict(model.state_dict())

        optimizer = torch.optim.SGD(adapted_model.parameters(), lr=lr)

        # 内循环更新
        for _ in range(steps):
            optimizer.zero_grad()
            pred_kps = adapted_model(support_pcs)
            loss = F.mse_loss(pred_kps, support_kps)
            loss.backward()
            optimizer.step()

        return adapted_model
    
    def evaluate_meta_model(self, model, dataset, k_shot, split='val', episodes=50):
        """评估元学习模型"""
        model.eval()
        total_error = 0
        
        with torch.no_grad():
            for _ in range(episodes):
                support_data, query_data = dataset.sample_episode(split, k_shot, 3)
                
                # 快速适应
                adapted_model = self.inner_loop_update(model, support_data, steps=5)
                
                # 查询集评估
                query_pcs, query_kps = query_data
                query_pcs = torch.FloatTensor(query_pcs).to(self.device)
                query_kps = torch.FloatTensor(query_kps).to(self.device)
                
                pred_kps = adapted_model(query_pcs)
                error = torch.mean(torch.norm(pred_kps - query_kps, dim=2))
                total_error += error.item()
        
        model.train()
        return total_error / episodes

def run_advanced_experiment():
    """运行高级小样本学习实验"""
    print("🚀 高级97样本小样本学习实验")
    print("=" * 60)
    
    # 导入数据集类
    from few_shot_97_samples_experiment import FewShotDataset
    
    # 1. 加载数据
    dataset = FewShotDataset()
    dataset.create_few_shot_splits()
    
    # 2. 创建训练器
    trainer = AdvancedFewShotTrainer()
    
    # 3. 自监督预训练
    pretrained_model = trainer.self_supervised_pretraining(dataset, epochs=30)
    
    # 4. 元学习训练
    results_with_pretrain = trainer.meta_learning_training(dataset, pretrained_model, episodes=500)
    
    # 5. 对比实验 (无预训练)
    print("\n🔄 对比实验 (无预训练)")
    results_without_pretrain = trainer.meta_learning_training(dataset, None, episodes=500)
    
    # 6. 结果对比
    print(f"\n📊 实验结果对比")
    print("=" * 60)
    print(f"{'Shot':<6} {'无预训练':<12} {'有预训练':<12} {'改进':<10}")
    print("-" * 50)
    
    for k_shot in [1, 3, 5, 10]:
        without = results_without_pretrain[k_shot]
        with_pretrain = results_with_pretrain[k_shot]
        improvement = (without - with_pretrain) / without * 100
        
        print(f"{k_shot:<6} {without:<12.2f} {with_pretrain:<12.2f} {improvement:<10.1f}%")
    
    return results_with_pretrain, results_without_pretrain

if __name__ == "__main__":
    results_with, results_without = run_advanced_experiment()
