#!/usr/bin/env python3
"""
Conservative Optimized Training for F3 Keypoint Detection

基于7.818mm成功基线，采用保守优化策略：
1. 保持成功的架构基础
2. 微调关键参数
3. 优化训练策略
4. 避免过度复杂化

分析：先进模型(9.220mm)比改进模型(7.818mm)差，说明过度复杂化导致过拟合
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import time
import json
import gc
import random

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class ConservativeF3Dataset(Dataset):
    """保守优化数据集 - 基于成功的数据增强策略"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points
        self.augment = augment
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
        
        print(f"   {split}: {len(self.sample_ids)} 样本")
    
    def __len__(self):
        return len(self.sample_ids)
    
    def conservative_augmentation(self, point_cloud, keypoints):
        """保守数据增强 - 基于成功经验，避免过度增强"""
        
        # 1. 适度旋转 (减少强度)
        if np.random.random() < 0.7:  # 降低概率
            angle = np.random.uniform(-0.08, 0.08)  # ±4.6度 (减少强度)
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
            point_cloud = point_cloud @ rotation.T
            keypoints = keypoints @ rotation.T
        
        # 2. 小幅平移
        if np.random.random() < 0.6:  # 降低概率
            translation = np.random.uniform(-0.4, 0.4, 3)  # ±0.4mm (减少强度)
            point_cloud += translation
            keypoints += translation
        
        # 3. 轻微缩放
        if np.random.random() < 0.5:  # 降低概率
            scale = np.random.uniform(0.99, 1.01, 3)  # ±1% (减少强度)
            point_cloud *= scale
            keypoints *= scale
        
        # 4. 轻微噪声
        if np.random.random() < 0.6:  # 降低概率
            noise_level = np.random.choice([0.02, 0.03, 0.04])  # 减少噪声强度
            noise = np.random.normal(0, noise_level, point_cloud.shape)
            point_cloud += noise
        
        return point_cloud, keypoints
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        # 保守数据增强
        if self.augment:
            point_cloud, keypoints = self.conservative_augmentation(point_cloud, keypoints)
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

class ConservativePointNet(nn.Module):
    """保守优化PointNet - 基于成功架构，微调改进"""
    
    def __init__(self, num_keypoints: int = 19):
        super(ConservativePointNet, self).__init__()
        
        # 保持成功的基础架构
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)  # 增加一层
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 简单残差连接 (只保留关键的)
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        # 全局特征处理 - 适度增深
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        # 批归一化和Dropout
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(0.3)  # 降低dropout
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # (batch, 3, points)
        
        # 点级特征提取 + 关键残差连接
        x1 = torch.relu(self.bn1(self.conv1(x)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        
        # 第一个残差连接
        x3_res = x3 + self.residual1(x1)
        
        x4 = torch.relu(self.bn4(self.conv4(x3_res)))
        
        # 第二个残差连接
        x4_res = x4 + self.residual2(x2)
        
        x5 = torch.relu(self.bn5(self.conv5(x4_res)))
        
        # 全局最大池化
        global_feat = torch.max(x5, 2)[0]  # (batch, 1024)
        
        # 全连接层
        x = torch.relu(self.bn_fc1(self.fc1(global_feat)))
        x = self.dropout(x)
        
        x = torch.relu(self.bn_fc2(self.fc2(x)))
        x = self.dropout(x)
        
        x = torch.relu(self.bn_fc3(self.fc3(x)))
        x = self.dropout(x)
        
        x = torch.relu(self.bn_fc4(self.fc4(x)))
        x = self.dropout(x)
        
        x = self.fc5(x)
        
        return x.view(batch_size, 19, 3)

class ImprovedLoss(nn.Module):
    """改进损失函数 - 保守优化"""
    
    def __init__(self, alpha=0.8, beta=0.2):
        super(ImprovedLoss, self).__init__()
        self.alpha = alpha  # MSE权重
        self.beta = beta    # Smooth L1权重
    
    def forward(self, pred, target):
        # MSE Loss
        mse_loss = F.mse_loss(pred, target)
        
        # Smooth L1 Loss (对异常值更鲁棒)
        smooth_l1_loss = F.smooth_l1_loss(pred, target)
        
        # 简单组合
        total_loss = self.alpha * mse_loss + self.beta * smooth_l1_loss
        
        return total_loss

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def train_conservative_model():
    """训练保守优化模型"""
    
    print("🚀 **保守优化训练 - F3关键点检测**")
    print("🎯 **策略: 基于7.818mm成功基线，保守微调优化**")
    print("📊 **分析: 先进模型(9.220mm)性能下降，避免过度复杂化**")
    print("=" * 80)
    
    set_seed(42)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 数据集
    dataset_path = "high_quality_f3_dataset.npz"
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    train_dataset = ConservativeF3Dataset(dataset_path, 'train', num_points=4096, 
                                        test_samples=test_samples, augment=True, seed=42)
    val_dataset = ConservativeF3Dataset(dataset_path, 'val', num_points=4096, 
                                      test_samples=test_samples, augment=False, seed=42)
    test_dataset = ConservativeF3Dataset(dataset_path, 'test', num_points=4096, 
                                       test_samples=test_samples, augment=False, seed=42)
    
    batch_size = 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    
    # 保守优化模型
    model = ConservativePointNet(num_keypoints=19).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 保守PointNet参数: {total_params:,}")
    
    # 改进损失函数和优化器
    criterion = ImprovedLoss(alpha=0.8, beta=0.2)
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
    
    # 保守的学习率调度
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer, max_lr=0.0016, epochs=100, steps_per_epoch=len(train_loader),
        pct_start=0.1, anneal_strategy='cos', div_factor=20, final_div_factor=100
    )
    
    # 训练配置
    num_epochs = 100  # 减少训练轮数，避免过拟合
    best_val_error = float('inf')
    patience = 20  # 减少耐心，早停
    patience_counter = 0
    history = []
    
    print(f"🎯 开始保守训练 (目标: 优于7.818mm基线)")
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 50)
        
        # 训练
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        for batch in train_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                scheduler.step()
                
                train_loss += loss.item()
                
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                        
            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue
        
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # 打印结果
        current_lr = optimizer.param_groups[0]['lr']
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"5mm={train_metrics['within_5mm_percent']:.1f}%, 7mm={train_metrics['within_7mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"5mm={val_metrics['within_5mm_percent']:.1f}%, 7mm={val_metrics['within_7mm_percent']:.1f}%")
        print(f"学习率: {current_lr:.2e}")
        
        # 保存历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'learning_rate': current_lr
        })
        
        # 检查改进
        current_error = val_metrics['mean_distance']
        if current_error < best_val_error:
            best_val_error = current_error
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics
            }, 'best_conservative_pointnet_f3.pth')
            
            print(f"🎉 新最佳! 验证误差: {best_val_error:.3f}mm")
            
            if best_val_error <= 5.0:
                print(f"🏆 **突破5mm目标!**")
            elif best_val_error <= 6.0:
                print(f"🎯 **接近目标!** 距离5mm很近")
            elif best_val_error < 7.818:
                print(f"✅ **优于基线!** 超越7.818mm")
        else:
            patience_counter += 1
            print(f"⏳ 无改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停")
            break
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    
    return model, test_loader, best_val_error, total_time, history

def evaluate_conservative_model(model, test_loader, device):
    """评估保守优化模型"""

    print(f"\n🧪 **保守模型测试集评估**")

    checkpoint = torch.load('best_conservative_pointnet_f3.pth')
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()

    test_metrics = {'mean_distance': 0, 'std_distance': 0, 'within_5mm_percent': 0,
                   'within_1mm_percent': 0, 'within_3mm_percent': 0, 'within_7mm_percent': 0}
    all_distances = []
    per_sample_results = []

    with torch.no_grad():
        for batch in test_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            sample_ids = batch['sample_id']

            try:
                pred_keypoints = model(point_cloud)

                for i in range(len(sample_ids)):
                    pred_single = pred_keypoints[i:i+1]
                    target_single = keypoints[i:i+1]

                    metrics = calculate_metrics(pred_single, target_single)

                    sample_result = {
                        'sample_id': sample_ids[i],
                        'mean_distance': metrics['mean_distance'],
                        'within_5mm': metrics['within_5mm_percent'] > 0,
                        'within_7mm': metrics['within_7mm_percent'] > 0
                    }
                    per_sample_results.append(sample_result)
                    all_distances.append(metrics['mean_distance'])

                batch_metrics = calculate_metrics(pred_keypoints, keypoints)
                for key in test_metrics:
                    if key in batch_metrics:
                        test_metrics[key] += batch_metrics[key]

            except RuntimeError as e:
                print(f"❌ 测试批次失败: {e}")
                continue

    for key in test_metrics:
        test_metrics[key] /= len(test_loader)

    all_distances = np.array(all_distances)

    print(f"📊 **保守PointNet测试结果**")
    print(f"   测试误差: {np.mean(all_distances):.3f}±{np.std(all_distances):.3f}mm")
    print(f"   中位数误差: {np.median(all_distances):.3f}mm")
    print(f"   最大误差: {np.max(all_distances):.3f}mm")
    print(f"   最小误差: {np.min(all_distances):.3f}mm")

    print(f"\n📈 **精度分布**")
    print(f"   ≤1mm: {test_metrics['within_1mm_percent']:.1f}%")
    print(f"   ≤3mm: {test_metrics['within_3mm_percent']:.1f}%")
    print(f"   ≤5mm: {test_metrics['within_5mm_percent']:.1f}%")
    print(f"   ≤7mm: {test_metrics['within_7mm_percent']:.1f}%")

    # 医疗级评估
    excellent_samples = np.sum(all_distances <= 1.0)
    good_samples = np.sum(all_distances <= 3.0)
    acceptable_samples = np.sum(all_distances <= 5.0)
    clinical_samples = np.sum(all_distances <= 7.0)

    print(f"\n🏥 **医疗级评估**")
    print(f"   优秀 (≤1mm): {excellent_samples}/{len(all_distances)} ({excellent_samples/len(all_distances)*100:.1f}%)")
    print(f"   良好 (≤3mm): {good_samples}/{len(all_distances)} ({good_samples/len(all_distances)*100:.1f}%)")
    print(f"   可接受 (≤5mm): {acceptable_samples}/{len(all_distances)} ({acceptable_samples/len(all_distances)*100:.1f}%)")
    print(f"   临床可用 (≤7mm): {clinical_samples}/{len(all_distances)} ({clinical_samples/len(all_distances)*100:.1f}%)")

    # 每个样本结果
    print(f"\n📋 **每个测试样本结果**")
    for result in per_sample_results:
        if result['mean_distance'] <= 5.0:
            status = "🏆"
        elif result['mean_distance'] <= 7.0:
            status = "✅"
        else:
            status = "❌"
        print(f"   {status} {result['sample_id']}: {result['mean_distance']:.3f}mm")

    return {
        'mean_distance': np.mean(all_distances),
        'std_distance': np.std(all_distances),
        'median_distance': np.median(all_distances),
        'max_distance': np.max(all_distances),
        'min_distance': np.min(all_distances),
        'within_1mm_percent': test_metrics['within_1mm_percent'],
        'within_3mm_percent': test_metrics['within_3mm_percent'],
        'within_5mm_percent': test_metrics['within_5mm_percent'],
        'within_7mm_percent': test_metrics['within_7mm_percent'],
        'per_sample_results': per_sample_results,
        'all_distances': all_distances.tolist()
    }

def main():
    """主函数"""

    try:
        # 训练保守优化模型
        model, test_loader, best_val_error, training_time, history = train_conservative_model()

        print(f"\n🎯 **保守训练完成!**")
        print(f"   最佳验证误差: {best_val_error:.3f}mm")
        print(f"   训练时间: {training_time/60:.1f}分钟")

        # 测试评估
        device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
        test_results = evaluate_conservative_model(model, test_loader, device)

        # 与之前结果对比
        baseline_error = 8.127  # 原始基线
        improved_error = 7.818  # 改进基线
        advanced_error = 9.220  # 先进模型(失败)
        current_error = test_results['mean_distance']

        print(f"\n📊 **性能对比**")
        print(f"   原始基线: {baseline_error:.3f}mm")
        print(f"   改进基线: {improved_error:.3f}mm")
        print(f"   先进模型: {advanced_error:.3f}mm (过拟合)")
        print(f"   保守模型: {current_error:.3f}mm")

        improvement_vs_baseline = (baseline_error - current_error) / baseline_error * 100
        improvement_vs_improved = (improved_error - current_error) / improved_error * 100
        improvement_vs_advanced = (advanced_error - current_error) / advanced_error * 100

        print(f"   📈 **vs原始基线**: {improvement_vs_baseline:.1f}%")
        print(f"   📈 **vs改进基线**: {improvement_vs_improved:.1f}%")
        print(f"   📈 **vs先进模型**: {improvement_vs_advanced:.1f}%")

        # 最终评估
        if current_error <= 5.0:
            print(f"\n🏆 **突破5mm目标!** 达到医疗级精度!")
        elif current_error <= 6.0:
            print(f"\n🎯 **非常接近!** 距离5mm目标很近")
        elif current_error <= 7.0:
            print(f"\n✅ **显著改进!** 达到临床可用精度")
        elif current_error < improved_error:
            print(f"\n✅ **持续改进!** 优于改进基线")
        elif current_error < advanced_error:
            print(f"\n✅ **避免过拟合!** 优于过度复杂模型")
        else:
            print(f"\n⚠️ **需要调整策略**")

        # 关键洞察
        print(f"\n💡 **关键洞察**")
        if current_error < advanced_error:
            print(f"   ✅ 保守策略成功：避免了过度复杂化导致的性能下降")
        if current_error < improved_error:
            print(f"   ✅ 微调优化有效：在成功基础上进一步提升")
        if test_results['within_7mm_percent'] > 50:
            print(f"   ✅ 临床可用性：超过50%样本达到7mm临床精度")

        # 保存结果
        results = {
            'model_name': 'Conservative_PointNet_F3',
            'training_completed': True,
            'best_validation_error_mm': float(best_val_error),
            'test_results': test_results,
            'training_time_minutes': float(training_time / 60),
            'total_epochs': len(history),
            'performance_comparison': {
                'original_baseline': baseline_error,
                'improved_baseline': improved_error,
                'advanced_model': advanced_error,
                'conservative_model': current_error,
                'improvement_vs_baseline_percent': float(improvement_vs_baseline),
                'improvement_vs_improved_percent': float(improvement_vs_improved),
                'improvement_vs_advanced_percent': float(improvement_vs_advanced)
            },
            'training_history': history
        }

        with open('conservative_pointnet_f3_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)

        print(f"\n💾 **结果已保存**: conservative_pointnet_f3_results.json")

        return results

    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    set_seed(42)

    print("🚀 **开始保守优化PointNet训练**")
    print("🔧 **策略**: 基于成功经验，保守微调，避免过度复杂化")
    print("🎯 **目标**: 在7.818mm基线上稳步提升，避免9.220mm的过拟合")

    results = main()
