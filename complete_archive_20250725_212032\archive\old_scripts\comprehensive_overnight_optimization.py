#!/usr/bin/env python3
"""
综合过夜优化策略
整合所有可能的优化方案，自动运行并记录结果
目标：在您睡觉期间持续优化，寻找突破5.857mm的方法
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import time
import json
import random
from sklearn.model_selection import KFold
import copy
import os
from datetime import datetime

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class AdvancedMedicalPointNet(nn.Module):
    """高级医疗PointNet - 集成多种优化技术"""
    
    def __init__(self, num_keypoints=12, statistical_baseline=None, config=None):
        super(AdvancedMedicalPointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        self.statistical_baseline = statistical_baseline
        self.config = config or {}
        
        # 可配置的架构参数
        dims = self.config.get('feature_dims', [32, 64, 128])
        activation = self.config.get('activation', 'relu')
        use_residual = self.config.get('use_residual', False)
        use_attention = self.config.get('use_attention', False)
        use_layernorm = self.config.get('use_layernorm', False)
        dropout_rate = self.config.get('dropout_rate', 0.3)
        
        # 特征提取层
        self.conv1 = nn.Conv1d(3, dims[0], 1)
        self.conv2 = nn.Conv1d(dims[0], dims[1], 1)
        self.conv3 = nn.Conv1d(dims[1], dims[2], 1)
        
        # 归一化层选择
        if use_layernorm:
            self.norm1 = nn.LayerNorm(dims[0])
            self.norm2 = nn.LayerNorm(dims[1])
            self.norm3 = nn.LayerNorm(dims[2])
            self.use_layernorm = True
        else:
            self.norm1 = nn.BatchNorm1d(dims[0])
            self.norm2 = nn.BatchNorm1d(dims[1])
            self.norm3 = nn.BatchNorm1d(dims[2])
            self.use_layernorm = False
        
        # 激活函数选择
        if activation == 'swish':
            self.activation = nn.SiLU()
        elif activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'mish':
            self.activation = nn.Mish()
        elif activation == 'leaky_relu':
            self.activation = nn.LeakyReLU(0.1)
        else:
            self.activation = nn.ReLU()
        
        # 残差连接
        if use_residual:
            self.residual1 = nn.Conv1d(dims[0], dims[1], 1)
            self.residual2 = nn.Conv1d(dims[0], dims[2], 1)
            self.res_weight1 = nn.Parameter(torch.tensor(0.1))
            self.res_weight2 = nn.Parameter(torch.tensor(0.05))
        self.use_residual = use_residual
        
        # 注意力机制
        if use_attention:
            self.attention_conv = nn.Conv1d(dims[2], dims[2]//4, 1)
            self.attention_score = nn.Conv1d(dims[2]//4, 1, 1)
        self.use_attention = use_attention
        
        # 预测头
        self.global_pool = nn.AdaptiveMaxPool1d(1)
        self.fc1 = nn.Linear(dims[2], dims[2]//2)
        self.fc2 = nn.Linear(dims[2]//2, dims[2]//4)
        self.fc3 = nn.Linear(dims[2]//4, num_keypoints * 3)
        
        self.dropout1 = nn.Dropout(dropout_rate)
        self.dropout2 = nn.Dropout(dropout_rate * 1.2)
        
        # 统计先验权重
        alpha_init = self.config.get('alpha_init', 0.55)
        self.alpha = nn.Parameter(torch.tensor(alpha_init))
        
        # 多重统计先验
        use_multi_prior = self.config.get('use_multi_prior', False)
        if use_multi_prior:
            self.beta = nn.Parameter(torch.tensor(0.1))  # 中位数权重
            self.gamma = nn.Parameter(torch.tensor(0.05))  # 分位数权重
        self.use_multi_prior = use_multi_prior
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"🔧 高级模型: {total_params:,}参数, 配置: {config}")
    
    def forward(self, x):
        batch_size = x.shape[0]
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 第一层
        x1 = self.conv1(x)
        if self.use_layernorm:
            x1 = x1.transpose(2, 1)
            x1 = self.norm1(x1)
            x1 = x1.transpose(2, 1)
        else:
            x1 = self.norm1(x1)
        x1 = self.activation(x1)
        
        # 第二层 + 可选残差
        x2 = self.conv2(x1)
        if self.use_layernorm:
            x2 = x2.transpose(2, 1)
            x2 = self.norm2(x2)
            x2 = x2.transpose(2, 1)
        else:
            x2 = self.norm2(x2)
        
        if self.use_residual:
            residual1 = self.residual1(x1)
            x2 = x2 + self.res_weight1 * residual1
        x2 = self.activation(x2)
        
        # 第三层 + 可选残差
        x3 = self.conv3(x2)
        if self.use_layernorm:
            x3 = x3.transpose(2, 1)
            x3 = self.norm3(x3)
            x3 = x3.transpose(2, 1)
        else:
            x3 = self.norm3(x3)
        
        if self.use_residual:
            residual2 = self.residual2(x1)
            x3 = x3 + self.res_weight2 * residual2
        x3 = self.activation(x3)
        
        # 可选注意力机制
        if self.use_attention:
            att_feat = self.activation(self.attention_conv(x3))
            att_scores = torch.softmax(self.attention_score(att_feat), dim=2)
            x3 = x3 * att_scores
        
        # 全局特征
        global_feat = self.global_pool(x3).squeeze(-1)
        
        # 预测头
        x = self.activation(self.fc1(global_feat))
        x = self.dropout1(x)
        x = self.activation(self.fc2(x))
        x = self.dropout2(x)
        delta = self.fc3(x)
        delta = delta.view(-1, self.num_keypoints, 3)
        
        # 多重统计先验集成
        if self.statistical_baseline is not None:
            baseline_mean = torch.tensor(self.statistical_baseline['mean'], 
                                       dtype=delta.dtype, device=delta.device)
            baseline_mean = baseline_mean.unsqueeze(0).expand(delta.shape[0], -1, -1)
            
            alpha = torch.sigmoid(self.alpha)
            output = alpha * baseline_mean + (1 - alpha) * (baseline_mean + delta)
            
            # 多重先验
            if self.use_multi_prior and 'median' in self.statistical_baseline:
                baseline_median = torch.tensor(self.statistical_baseline['median'], 
                                             dtype=delta.dtype, device=delta.device)
                baseline_median = baseline_median.unsqueeze(0).expand(delta.shape[0], -1, -1)
                
                beta = torch.sigmoid(self.beta)
                output = output + beta * (baseline_median - baseline_mean)
            
            return output
        
        return delta

class ComprehensiveOptimizer:
    """综合优化器 - 过夜自动优化"""
    
    def __init__(self, device='cuda:1'):
        self.device = device
        self.results = []
        self.best_performance = 5.857  # 当前最佳基线
        self.start_time = time.time()
        
        # 创建结果目录
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.result_dir = f"overnight_optimization_{self.timestamp}"
        os.makedirs(self.result_dir, exist_ok=True)
        
        print(f"🌙 综合过夜优化器启动")
        print(f"📁 结果保存目录: {self.result_dir}")
        print(f"🎯 目标: 突破{self.best_performance}mm")
    
    def calculate_enhanced_statistical_baseline(self, train_data):
        """计算增强的统计基线"""
        all_keypoints = []
        for sample in train_data:
            if isinstance(sample, dict):
                kp = sample['keypoints'].numpy()
            else:
                kp = sample[1]
            all_keypoints.append(kp)
        
        all_keypoints = np.array(all_keypoints)
        
        baseline = {
            'mean': np.mean(all_keypoints, axis=0),
            'median': np.median(all_keypoints, axis=0),
            'q25': np.percentile(all_keypoints, 25, axis=0),
            'q75': np.percentile(all_keypoints, 75, axis=0),
            'std': np.std(all_keypoints, axis=0)
        }
        
        return baseline
    
    def advanced_augment(self, point_cloud, keypoints, strategy='conservative'):
        """高级数据增强策略"""
        pc = point_cloud.copy()
        kp = keypoints.copy()
        
        if strategy == 'conservative':
            # 保守增强
            if np.random.random() < 0.6:
                angle = np.random.uniform(-0.035, 0.035)  # ±2度
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                pc = pc @ rotation.T
                kp = kp @ rotation.T
            
            if np.random.random() < 0.5:
                scale = np.random.uniform(0.99, 1.01)
                pc *= scale
                kp *= scale
                
        elif strategy == 'moderate':
            # 中等增强
            if np.random.random() < 0.7:
                angle = np.random.uniform(-0.052, 0.052)  # ±3度
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                pc = pc @ rotation.T
                kp = kp @ rotation.T
            
            if np.random.random() < 0.6:
                scale = np.random.uniform(0.98, 1.02)
                pc *= scale
                kp *= scale
                
        elif strategy == 'aggressive':
            # 激进增强
            if np.random.random() < 0.8:
                angle = np.random.uniform(-0.087, 0.087)  # ±5度
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                pc = pc @ rotation.T
                kp = kp @ rotation.T
            
            if np.random.random() < 0.7:
                scale = np.random.uniform(0.97, 1.03)
                pc *= scale
                kp *= scale
        
        # 通用增强
        if np.random.random() < 0.4:
            translation = np.random.uniform(-0.1, 0.1, 3)
            pc += translation
            kp += translation
        
        if np.random.random() < 0.3:
            noise_level = np.random.choice([0.003, 0.005, 0.008])
            noise = np.random.normal(0, noise_level, pc.shape)
            pc += noise
        
        return pc, kp
    
    def create_optimization_configs(self):
        """创建优化配置列表"""
        
        configs = []
        
        # 1. 基础配置变体
        base_configs = [
            # 原始最佳配置
            {
                'name': '原始最佳',
                'feature_dims': [32, 64, 128],
                'activation': 'relu',
                'alpha_init': 0.55,
                'dropout_rate': 0.3,
                'augment_strategy': 'conservative',
                'augment_factor': 2
            },
            
            # 不同特征维度
            {
                'name': '黄金比例维度',
                'feature_dims': [28, 56, 112],
                'activation': 'relu',
                'alpha_init': 0.55,
                'dropout_rate': 0.3,
                'augment_strategy': 'conservative',
                'augment_factor': 2
            },
            
            {
                'name': '紧凑维度',
                'feature_dims': [24, 48, 96],
                'activation': 'relu',
                'alpha_init': 0.55,
                'dropout_rate': 0.25,
                'augment_strategy': 'conservative',
                'augment_factor': 2
            },
            
            # 不同激活函数
            {
                'name': 'Swish激活',
                'feature_dims': [32, 64, 128],
                'activation': 'swish',
                'alpha_init': 0.55,
                'dropout_rate': 0.3,
                'augment_strategy': 'conservative',
                'augment_factor': 2
            },
            
            {
                'name': 'GELU激活',
                'feature_dims': [32, 64, 128],
                'activation': 'gelu',
                'alpha_init': 0.55,
                'dropout_rate': 0.3,
                'augment_strategy': 'conservative',
                'augment_factor': 2
            },
            
            # 不同α值
            {
                'name': 'α=0.52',
                'feature_dims': [32, 64, 128],
                'activation': 'relu',
                'alpha_init': 0.52,
                'dropout_rate': 0.3,
                'augment_strategy': 'conservative',
                'augment_factor': 2
            },
            
            {
                'name': 'α=0.58',
                'feature_dims': [32, 64, 128],
                'activation': 'relu',
                'alpha_init': 0.58,
                'dropout_rate': 0.3,
                'augment_strategy': 'conservative',
                'augment_factor': 2
            },
            
            {
                'name': 'α=0.62',
                'feature_dims': [32, 64, 128],
                'activation': 'relu',
                'alpha_init': 0.62,
                'dropout_rate': 0.3,
                'augment_strategy': 'conservative',
                'augment_factor': 2
            }
        ]
        
        configs.extend(base_configs)
        
        # 2. 高级技术配置
        advanced_configs = [
            # 残差连接
            {
                'name': '残差连接',
                'feature_dims': [32, 64, 128],
                'activation': 'relu',
                'alpha_init': 0.55,
                'dropout_rate': 0.3,
                'use_residual': True,
                'augment_strategy': 'conservative',
                'augment_factor': 2
            },
            
            # 注意力机制
            {
                'name': '注意力机制',
                'feature_dims': [32, 64, 128],
                'activation': 'relu',
                'alpha_init': 0.55,
                'dropout_rate': 0.3,
                'use_attention': True,
                'augment_strategy': 'conservative',
                'augment_factor': 2
            },
            
            # LayerNorm
            {
                'name': 'LayerNorm',
                'feature_dims': [32, 64, 128],
                'activation': 'relu',
                'alpha_init': 0.55,
                'dropout_rate': 0.3,
                'use_layernorm': True,
                'augment_strategy': 'conservative',
                'augment_factor': 2
            },
            
            # 多重统计先验
            {
                'name': '多重统计先验',
                'feature_dims': [32, 64, 128],
                'activation': 'relu',
                'alpha_init': 0.55,
                'dropout_rate': 0.3,
                'use_multi_prior': True,
                'augment_strategy': 'conservative',
                'augment_factor': 2
            },
            
            # 组合技术
            {
                'name': '残差+注意力',
                'feature_dims': [28, 56, 112],
                'activation': 'swish',
                'alpha_init': 0.58,
                'dropout_rate': 0.25,
                'use_residual': True,
                'use_attention': True,
                'augment_strategy': 'moderate',
                'augment_factor': 3
            },
            
            {
                'name': '全技术组合',
                'feature_dims': [24, 48, 96],
                'activation': 'gelu',
                'alpha_init': 0.52,
                'dropout_rate': 0.2,
                'use_residual': True,
                'use_attention': True,
                'use_layernorm': True,
                'use_multi_prior': True,
                'augment_strategy': 'moderate',
                'augment_factor': 4
            }
        ]
        
        configs.extend(advanced_configs)
        
        # 3. 数据增强策略变体
        augment_configs = [
            {
                'name': '中等增强',
                'feature_dims': [32, 64, 128],
                'activation': 'relu',
                'alpha_init': 0.55,
                'dropout_rate': 0.3,
                'augment_strategy': 'moderate',
                'augment_factor': 3
            },
            
            {
                'name': '激进增强',
                'feature_dims': [32, 64, 128],
                'activation': 'relu',
                'alpha_init': 0.55,
                'dropout_rate': 0.4,
                'augment_strategy': 'aggressive',
                'augment_factor': 4
            }
        ]
        
        configs.extend(augment_configs)
        
        return configs
    
    def test_single_config(self, config, train_samples):
        """测试单个配置"""
        
        print(f"\n🔍 测试配置: {config['name']}")
        
        try:
            # 3折快速验证
            kfold = KFold(n_splits=3, shuffle=True, random_state=42)
            fold_results = []
            
            for fold, (train_idx, val_idx) in enumerate(kfold.split(range(len(train_samples)))):
                
                # 分割数据
                fold_train = [train_samples[i] for i in train_idx]
                fold_val = [train_samples[i] for i in val_idx]
                
                # 计算统计基线
                statistical_baseline = self.calculate_enhanced_statistical_baseline(fold_train)
                
                # 数据增强
                augmented_train = []
                augment_strategy = config.get('augment_strategy', 'conservative')
                augment_factor = config.get('augment_factor', 2)
                
                for pc, kp, sid in fold_train:
                    augmented_train.append((pc, kp, sid))
                    for i in range(augment_factor):
                        aug_pc, aug_kp = self.advanced_augment(pc, kp, augment_strategy)
                        augmented_train.append((aug_pc, aug_kp, f"{sid}_aug_{i}"))
                
                # 创建模型
                model = AdvancedMedicalPointNet(
                    num_keypoints=12,
                    statistical_baseline=statistical_baseline,
                    config=config
                )
                model.to(self.device)
                
                # 训练
                fold_error = self.train_single_fold(model, augmented_train, fold_val, config)
                fold_results.append(fold_error)
            
            avg_error = np.mean(fold_results)
            std_error = np.std(fold_results)
            best_fold = min(fold_results)
            
            result = {
                'config': config,
                'avg_error': avg_error,
                'std_error': std_error,
                'best_fold': best_fold,
                'fold_results': fold_results,
                'timestamp': datetime.now().isoformat()
            }
            
            print(f"   结果: {avg_error:.3f}±{std_error:.3f}mm (最佳: {best_fold:.3f}mm)")
            
            # 检查是否有突破
            if best_fold < self.best_performance:
                improvement = (self.best_performance - best_fold) / self.best_performance * 100
                print(f"   🎉 新的突破! 提升{improvement:.1f}%")
                self.best_performance = best_fold
                
                # 保存最佳模型配置
                with open(f"{self.result_dir}/best_config.json", 'w') as f:
                    json.dump(result, f, indent=2)
            
            return result
            
        except Exception as e:
            print(f"   ❌ 配置失败: {e}")
            return {
                'config': config,
                'error': str(e),
                'avg_error': 999.0,
                'timestamp': datetime.now().isoformat()
            }
    
    def train_single_fold(self, model, train_data, val_data, config, epochs=30):
        """训练单折"""
        
        # 优化器配置
        lr = config.get('learning_rate', 0.001)
        weight_decay = config.get('weight_decay', 5e-4)
        
        optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
        scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=10)
        criterion = nn.MSELoss()
        
        best_val_error = float('inf')
        patience = 8
        patience_counter = 0
        
        for epoch in range(epochs):
            # 训练
            model.train()
            train_loss = 0.0
            
            batch_size = 4
            for i in range(0, len(train_data), batch_size):
                batch = train_data[i:i+batch_size]
                
                pc_list = []
                kp_list = []
                
                for pc, kp, _ in batch:
                    if len(pc) > 2048:
                        indices = np.random.choice(len(pc), 2048, replace=False)
                        pc = pc[indices]
                    
                    pc_list.append(torch.FloatTensor(pc))
                    kp_list.append(torch.FloatTensor(kp))
                
                pc_batch = torch.stack(pc_list).to(self.device)
                kp_batch = torch.stack(kp_list).to(self.device)
                
                optimizer.zero_grad()
                pred = model(pc_batch)
                loss = criterion(pred, kp_batch)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
                optimizer.step()
                
                train_loss += loss.item()
            
            # 验证
            model.eval()
            val_errors = []
            with torch.no_grad():
                for pc, kp, _ in val_data:
                    if len(pc) > 2048:
                        indices = np.random.choice(len(pc), 2048, replace=False)
                        pc = pc[indices]
                    
                    pc_tensor = torch.FloatTensor(pc).unsqueeze(0).to(self.device)
                    kp_tensor = torch.FloatTensor(kp).unsqueeze(0).to(self.device)
                    
                    pred = model(pc_tensor)
                    error = torch.norm(pred - kp_tensor, dim=2).mean().item()
                    val_errors.append(error)
            
            val_error = np.mean(val_errors)
            scheduler.step()
            
            if val_error < best_val_error:
                best_val_error = val_error
                patience_counter = 0
            else:
                patience_counter += 1
            
            if patience_counter >= patience:
                break
        
        return best_val_error
    
    def run_comprehensive_optimization(self):
        """运行综合优化"""
        
        print("\n🌙 **综合过夜优化开始**")
        print("🎯 **目标: 寻找突破5.857mm的方法**")
        print("⏰ **预计运行时间: 6-8小时**")
        print("=" * 80)
        
        # 加载数据
        data = np.load('f3_reduced_12kp_stable.npz', allow_pickle=True)
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        # 排除测试集
        test_samples = ['600114', '600115', '600116', '600117', '600118', 
                       '600119', '600120', '600121', '600122', '600123',
                       '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        train_samples = [(point_clouds[i], keypoints[i], sample_ids[i]) 
                        for i in range(len(sample_ids)) if train_val_mask[i]]
        
        print(f"📊 训练数据: {len(train_samples)}个样本")
        
        # 创建优化配置
        configs = self.create_optimization_configs()
        print(f"🔧 优化配置: {len(configs)}个")
        
        # 逐个测试配置
        for i, config in enumerate(configs):
            elapsed_time = time.time() - self.start_time
            print(f"\n进度: {i+1}/{len(configs)} (已运行{elapsed_time/3600:.1f}小时)")
            
            result = self.test_single_config(config, train_samples)
            self.results.append(result)
            
            # 定期保存结果
            if (i + 1) % 5 == 0:
                self.save_intermediate_results()
        
        # 最终结果分析
        self.analyze_final_results()
    
    def save_intermediate_results(self):
        """保存中间结果"""
        
        with open(f"{self.result_dir}/intermediate_results.json", 'w') as f:
            json.dump(self.results, f, indent=2)
        
        # 创建简要报告
        valid_results = [r for r in self.results if 'avg_error' in r and r['avg_error'] < 900]
        if valid_results:
            best_result = min(valid_results, key=lambda x: x['best_fold'])
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'total_configs_tested': len(self.results),
                'valid_results': len(valid_results),
                'current_best': {
                    'config_name': best_result['config']['name'],
                    'best_performance': best_result['best_fold'],
                    'avg_performance': best_result['avg_error']
                },
                'baseline': 5.857
            }
            
            with open(f"{self.result_dir}/progress_report.json", 'w') as f:
                json.dump(report, f, indent=2)
    
    def analyze_final_results(self):
        """分析最终结果"""
        
        print(f"\n📊 **最终结果分析**")
        print("=" * 60)
        
        # 过滤有效结果
        valid_results = [r for r in self.results if 'avg_error' in r and r['avg_error'] < 900]
        
        if not valid_results:
            print("❌ 没有有效结果")
            return
        
        # 按性能排序
        sorted_results = sorted(valid_results, key=lambda x: x['best_fold'])
        
        print(f"✅ 有效配置: {len(valid_results)}/{len(self.results)}")
        print(f"🏆 最佳性能: {sorted_results[0]['best_fold']:.3f}mm")
        print(f"📈 基线对比: {self.best_performance:.3f}mm vs 5.857mm")
        
        if self.best_performance < 5.857:
            improvement = (5.857 - self.best_performance) / 5.857 * 100
            print(f"🎉 成功突破! 提升{improvement:.1f}%")
        else:
            print(f"💡 未突破基线，但获得了宝贵经验")
        
        # 保存最终结果
        final_report = {
            'summary': {
                'total_runtime_hours': (time.time() - self.start_time) / 3600,
                'total_configs': len(self.results),
                'valid_configs': len(valid_results),
                'best_performance': self.best_performance,
                'baseline': 5.857,
                'breakthrough': self.best_performance < 5.857
            },
            'top_10_results': sorted_results[:10],
            'all_results': self.results
        }
        
        with open(f"{self.result_dir}/final_report.json", 'w') as f:
            json.dump(final_report, f, indent=2)
        
        # 打印前10名
        print(f"\n🏆 **前10名配置**:")
        for i, result in enumerate(sorted_results[:10]):
            config_name = result['config']['name']
            best_perf = result['best_fold']
            avg_perf = result['avg_error']
            print(f"   {i+1:2d}. {config_name:<20} {best_perf:.3f}mm (avg: {avg_perf:.3f}mm)")
        
        total_time = time.time() - self.start_time
        print(f"\n⏰ 总运行时间: {total_time/3600:.1f}小时")
        print(f"📁 结果保存在: {self.result_dir}/")

def main():
    """主函数 - 启动过夜优化"""
    
    print("🌙 **综合过夜优化策略**")
    print("😴 **您可以安心去睡觉，我会持续优化**")
    print("🎯 **目标: 寻找突破5.857mm的方法**")
    print("=" * 80)
    
    set_seed(42)
    
    # 创建优化器
    optimizer = ComprehensiveOptimizer(device='cuda:1')
    
    # 开始优化
    try:
        optimizer.run_comprehensive_optimization()
        print(f"\n🎉 **过夜优化完成!**")
        print(f"😊 **您醒来后可以查看结果**")
    except KeyboardInterrupt:
        print(f"\n⏹️ **优化被中断**")
        optimizer.save_intermediate_results()
        print(f"📁 **中间结果已保存**")
    except Exception as e:
        print(f"\n❌ **优化出错: {e}**")
        optimizer.save_intermediate_results()

if __name__ == "__main__":
    main()
