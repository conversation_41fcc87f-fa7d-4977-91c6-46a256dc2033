#!/usr/bin/env python3
"""
热图可视化工具
创建3D点云上的概率分布热图可视化
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns
from heatmap_keypoint_system import HeatmapPointNet, HeatmapKeypointDataset, extract_keypoints_from_heatmaps

def load_trained_model():
    """加载训练好的模型"""
    print("🔧 **加载训练好的热图模型**")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model.eval()
        print(f"   ✅ 模型加载成功")
        return model, device
    except Exception as e:
        print(f"   ❌ 模型加载失败: {e}")
        return None, device

def predict_heatmaps_for_sample(model, device, point_cloud, sample_id):
    """为单个样本预测热图"""
    
    # 准备数据
    if len(point_cloud) > 8192:
        indices = np.random.choice(len(point_cloud), 8192, replace=False)
        pc_sampled = point_cloud[indices]
    else:
        pc_sampled = point_cloud
    
    # 转换为tensor
    pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)  # [1, 3, N]
    
    # 预测
    with torch.no_grad():
        pred_heatmaps = model(pc_tensor)  # [1, 12, N]
    
    # 转换为numpy
    pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T  # [N, 12]
    
    return pc_sampled, pred_heatmaps_np

def create_heatmap_colormap():
    """创建热图颜色映射"""
    # 创建从蓝色到红色的颜色映射，类似热成像
    colors = ['#000080', '#0000FF', '#00FFFF', '#00FF00', '#FFFF00', '#FF8000', '#FF0000']
    n_bins = 256
    cmap = LinearSegmentedColormap.from_list('heatmap', colors, N=n_bins)
    return cmap

def visualize_single_keypoint_heatmap(point_cloud, heatmap, keypoint_idx, true_keypoint=None, 
                                    pred_keypoint=None, confidence=None, save_path=None):
    """可视化单个关键点的热图"""
    
    fig = plt.figure(figsize=(15, 5))
    
    # 创建热图颜色映射
    cmap = create_heatmap_colormap()
    
    # 3D散点图 - 原始视角
    ax1 = fig.add_subplot(131, projection='3d')
    scatter = ax1.scatter(point_cloud[:, 0], point_cloud[:, 1], point_cloud[:, 2], 
                         c=heatmap, cmap=cmap, s=1, alpha=0.8, vmin=0, vmax=1)
    
    # 添加真实关键点
    if true_keypoint is not None:
        ax1.scatter(true_keypoint[0], true_keypoint[1], true_keypoint[2], 
                   c='white', s=100, marker='*', edgecolor='black', linewidth=2, label='True')
    
    # 添加预测关键点
    if pred_keypoint is not None:
        ax1.scatter(pred_keypoint[0], pred_keypoint[1], pred_keypoint[2], 
                   c='yellow', s=80, marker='o', edgecolor='black', linewidth=2, label='Predicted')
    
    ax1.set_title(f'Keypoint {keypoint_idx} Heatmap - View 1')
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')
    if true_keypoint is not None or pred_keypoint is not None:
        ax1.legend()
    
    # 3D散点图 - 侧视角
    ax2 = fig.add_subplot(132, projection='3d')
    ax2.scatter(point_cloud[:, 0], point_cloud[:, 1], point_cloud[:, 2], 
               c=heatmap, cmap=cmap, s=1, alpha=0.8, vmin=0, vmax=1)
    
    if true_keypoint is not None:
        ax2.scatter(true_keypoint[0], true_keypoint[1], true_keypoint[2], 
                   c='white', s=100, marker='*', edgecolor='black', linewidth=2)
    
    if pred_keypoint is not None:
        ax2.scatter(pred_keypoint[0], pred_keypoint[1], pred_keypoint[2], 
                   c='yellow', s=80, marker='o', edgecolor='black', linewidth=2)
    
    ax2.view_init(elev=0, azim=90)  # 侧视角
    ax2.set_title(f'Keypoint {keypoint_idx} Heatmap - Side View')
    ax2.set_xlabel('X')
    ax2.set_ylabel('Y')
    ax2.set_zlabel('Z')
    
    # 热图分布直方图
    ax3 = fig.add_subplot(133)
    ax3.hist(heatmap, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax3.axvline(np.mean(heatmap), color='red', linestyle='--', label=f'Mean: {np.mean(heatmap):.3f}')
    ax3.axvline(np.max(heatmap), color='orange', linestyle='--', label=f'Max: {np.max(heatmap):.3f}')
    
    ax3.set_xlabel('Probability')
    ax3.set_ylabel('Frequency')
    ax3.set_title(f'Probability Distribution')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 添加统计信息
    stats_text = f'Statistics:\n'
    stats_text += f'Mean: {np.mean(heatmap):.4f}\n'
    stats_text += f'Std: {np.std(heatmap):.4f}\n'
    stats_text += f'Max: {np.max(heatmap):.4f}\n'
    stats_text += f'Min: {np.min(heatmap):.4f}\n'
    if confidence is not None:
        stats_text += f'Confidence: {confidence:.4f}'
    
    ax3.text(0.02, 0.98, stats_text, transform=ax3.transAxes, fontsize=10,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 添加颜色条
    plt.colorbar(scatter, ax=[ax1, ax2], shrink=0.8, label='Probability')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"   📊 关键点{keypoint_idx}热图已保存: {save_path}")
    
    plt.close()

def visualize_all_keypoints_overview(point_cloud, heatmaps, true_keypoints=None, 
                                   pred_keypoints=None, confidences=None, sample_id="Unknown"):
    """可视化所有关键点的概览"""
    
    fig = plt.figure(figsize=(20, 15))
    cmap = create_heatmap_colormap()
    
    # 创建4x3的子图布局
    for i in range(12):
        ax = fig.add_subplot(4, 3, i+1, projection='3d')
        
        # 绘制热图
        scatter = ax.scatter(point_cloud[:, 0], point_cloud[:, 1], point_cloud[:, 2], 
                           c=heatmaps[:, i], cmap=cmap, s=0.5, alpha=0.7, vmin=0, vmax=1)
        
        # 添加真实关键点
        if true_keypoints is not None:
            ax.scatter(true_keypoints[i, 0], true_keypoints[i, 1], true_keypoints[i, 2], 
                      c='white', s=50, marker='*', edgecolor='black', linewidth=1)
        
        # 添加预测关键点
        if pred_keypoints is not None:
            ax.scatter(pred_keypoints[i, 0], pred_keypoints[i, 1], pred_keypoints[i, 2], 
                      c='yellow', s=40, marker='o', edgecolor='black', linewidth=1)
        
        # 设置标题
        title = f'KP{i}'
        if confidences is not None:
            title += f' (Conf: {confidences[i]:.3f})'
        ax.set_title(title, fontsize=10)
        
        # 设置坐标轴
        ax.set_xlabel('X', fontsize=8)
        ax.set_ylabel('Y', fontsize=8)
        ax.set_zlabel('Z', fontsize=8)
        ax.tick_params(labelsize=6)
    
    plt.suptitle(f'All Keypoints Heatmap Overview - Sample {sample_id}', fontsize=16)
    plt.tight_layout()
    
    # 添加全局颜色条
    cbar_ax = fig.add_axes([0.92, 0.15, 0.02, 0.7])
    cbar = plt.colorbar(scatter, cax=cbar_ax)
    cbar.set_label('Probability', fontsize=12)
    
    save_path = f'heatmap_overview_{sample_id}.png'
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"   📊 所有关键点概览已保存: {save_path}")
    plt.close()

def analyze_uncertainty_patterns(heatmaps, confidences, sample_id):
    """分析不确定性模式"""
    
    print(f"\n🔍 **样本{sample_id}不确定性分析**:")
    
    # 计算统计信息
    mean_probs = np.mean(heatmaps, axis=0)
    std_probs = np.std(heatmaps, axis=0)
    max_probs = np.max(heatmaps, axis=0)
    
    print(f"   各关键点置信度分析:")
    for i in range(12):
        uncertainty = 1 - confidences[i]  # 不确定性 = 1 - 置信度
        print(f"     关键点{i}: 置信度={confidences[i]:.3f}, 不确定性={uncertainty:.3f}")
    
    # 找出最不确定的关键点
    uncertainties = 1 - confidences
    most_uncertain_idx = np.argmax(uncertainties)
    most_certain_idx = np.argmin(uncertainties)
    
    print(f"   最不确定关键点: {most_uncertain_idx} (不确定性: {uncertainties[most_uncertain_idx]:.3f})")
    print(f"   最确定关键点: {most_certain_idx} (不确定性: {uncertainties[most_certain_idx]:.3f})")
    
    return most_uncertain_idx, most_certain_idx

def create_uncertainty_comparison(point_cloud, heatmaps, true_keypoints, pred_keypoints, 
                                confidences, most_uncertain_idx, most_certain_idx, sample_id):
    """创建不确定性对比可视化"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12), subplot_kw={'projection': '3d'})
    cmap = create_heatmap_colormap()
    
    # 最不确定的关键点
    ax1 = axes[0, 0]
    scatter1 = ax1.scatter(point_cloud[:, 0], point_cloud[:, 1], point_cloud[:, 2], 
                          c=heatmaps[:, most_uncertain_idx], cmap=cmap, s=1, alpha=0.8, vmin=0, vmax=1)
    
    ax1.scatter(true_keypoints[most_uncertain_idx, 0], true_keypoints[most_uncertain_idx, 1], 
               true_keypoints[most_uncertain_idx, 2], c='white', s=100, marker='*', 
               edgecolor='black', linewidth=2, label='True')
    ax1.scatter(pred_keypoints[most_uncertain_idx, 0], pred_keypoints[most_uncertain_idx, 1], 
               pred_keypoints[most_uncertain_idx, 2], c='yellow', s=80, marker='o', 
               edgecolor='black', linewidth=2, label='Predicted')
    
    ax1.set_title(f'Most Uncertain: KP{most_uncertain_idx}\nConfidence: {confidences[most_uncertain_idx]:.3f}')
    ax1.legend()
    
    # 最确定的关键点
    ax2 = axes[0, 1]
    scatter2 = ax2.scatter(point_cloud[:, 0], point_cloud[:, 1], point_cloud[:, 2], 
                          c=heatmaps[:, most_certain_idx], cmap=cmap, s=1, alpha=0.8, vmin=0, vmax=1)
    
    ax2.scatter(true_keypoints[most_certain_idx, 0], true_keypoints[most_certain_idx, 1], 
               true_keypoints[most_certain_idx, 2], c='white', s=100, marker='*', 
               edgecolor='black', linewidth=2, label='True')
    ax2.scatter(pred_keypoints[most_certain_idx, 0], pred_keypoints[most_certain_idx, 1], 
               pred_keypoints[most_certain_idx, 2], c='yellow', s=80, marker='o', 
               edgecolor='black', linewidth=2, label='Predicted')
    
    ax2.set_title(f'Most Certain: KP{most_certain_idx}\nConfidence: {confidences[most_certain_idx]:.3f}')
    ax2.legend()
    
    # 置信度分布
    ax3 = axes[1, 0]
    ax3.remove()
    ax3 = fig.add_subplot(2, 2, 3)
    
    bars = ax3.bar(range(12), confidences, color='skyblue', alpha=0.7, edgecolor='black')
    bars[most_uncertain_idx].set_color('red')
    bars[most_certain_idx].set_color('green')
    
    ax3.set_xlabel('Keypoint Index')
    ax3.set_ylabel('Confidence')
    ax3.set_title('Confidence Distribution')
    ax3.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, v in enumerate(confidences):
        ax3.text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom', fontsize=8)
    
    # 不确定性统计
    ax4 = axes[1, 1]
    ax4.remove()
    ax4 = fig.add_subplot(2, 2, 4)
    
    uncertainties = 1 - confidences
    ax4.hist(uncertainties, bins=10, alpha=0.7, color='orange', edgecolor='black')
    ax4.axvline(np.mean(uncertainties), color='red', linestyle='--', 
               label=f'Mean: {np.mean(uncertainties):.3f}')
    ax4.axvline(np.median(uncertainties), color='blue', linestyle='--', 
               label=f'Median: {np.median(uncertainties):.3f}')
    
    ax4.set_xlabel('Uncertainty (1 - Confidence)')
    ax4.set_ylabel('Frequency')
    ax4.set_title('Uncertainty Distribution')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    save_path = f'uncertainty_analysis_{sample_id}.png'
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"   📊 不确定性分析已保存: {save_path}")
    plt.close()

def main():
    """主函数"""
    print("🎨 **热图可视化工具**")
    print("创建3D点云概率分布热图可视化")
    print("=" * 80)
    
    # 加载模型
    model, device = load_trained_model()
    if model is None:
        print("❌ 无法加载模型，请先运行训练脚本")
        return
    
    # 加载验证数据
    try:
        male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
        sample_ids = male_data['sample_ids']
        point_clouds = male_data['point_clouds']
        keypoints = male_data['keypoints']
        
        # 选择几个样本进行可视化
        demo_indices = [0, 5, 10]  # 选择3个样本
        
        for idx in demo_indices:
            sample_id = sample_ids[idx]
            point_cloud = point_clouds[idx]
            true_keypoints = keypoints[idx]
            
            print(f"\n🎯 **处理样本: {sample_id}**")
            
            # 预测热图
            pc_sampled, pred_heatmaps = predict_heatmaps_for_sample(
                model, device, point_cloud, sample_id
            )
            
            # 从热图提取关键点
            pred_keypoints, confidences = extract_keypoints_from_heatmaps(
                pred_heatmaps.T, pc_sampled
            )
            
            # 计算误差
            errors = []
            for i in range(12):
                error = np.linalg.norm(pred_keypoints[i] - true_keypoints[i])
                errors.append(error)
            
            avg_error = np.mean(errors)
            print(f"   平均预测误差: {avg_error:.2f}mm")
            
            # 创建可视化
            print(f"   创建可视化...")
            
            # 1. 所有关键点概览
            visualize_all_keypoints_overview(
                pc_sampled, pred_heatmaps, true_keypoints, pred_keypoints, 
                confidences, sample_id
            )
            
            # 2. 不确定性分析
            most_uncertain_idx, most_certain_idx = analyze_uncertainty_patterns(
                pred_heatmaps, confidences, sample_id
            )
            
            # 3. 不确定性对比
            create_uncertainty_comparison(
                pc_sampled, pred_heatmaps, true_keypoints, pred_keypoints, 
                confidences, most_uncertain_idx, most_certain_idx, sample_id
            )
            
            # 4. 单个关键点详细热图（最不确定的）
            visualize_single_keypoint_heatmap(
                pc_sampled, pred_heatmaps[:, most_uncertain_idx], most_uncertain_idx,
                true_keypoints[most_uncertain_idx], pred_keypoints[most_uncertain_idx],
                confidences[most_uncertain_idx], 
                f'detailed_heatmap_{sample_id}_kp{most_uncertain_idx}.png'
            )
        
        print(f"\n🎉 **可视化完成!**")
        print(f"✅ 生成了多种热图可视化")
        print(f"✅ 提供了不确定性量化分析")
        print(f"✅ 展示了概率分布特征")
        
    except Exception as e:
        print(f"❌ 可视化过程出错: {e}")

if __name__ == "__main__":
    main()
