#!/usr/bin/env python3
"""
使用PyTorch Lightning重构19关键点检测系统
提供更好的代码组织、训练监控和实验管理
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping, LearningRateMonitor
from pytorch_lightning.loggers import TensorBoardLogger, WandbLogger
import torchmetrics
from sklearn.model_selection import train_test_split

class LightningHeatmapPointNet(pl.LightningModule):
    """Lightning版本的19关键点检测模型"""
    
    def __init__(self, 
                 input_dim=3, 
                 num_keypoints=19,
                 learning_rate=0.001,
                 weight_decay=0.01,
                 scheduler_patience=10,
                 scheduler_factor=0.5):
        super().__init__()
        
        # 保存超参数
        self.save_hyperparameters()
        
        # 模型架构
        self.conv1 = nn.Conv1d(input_dim, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 热力图回归头
        self.heatmap_head = nn.Sequential(
            nn.Conv1d(1024, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Conv1d(512, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Conv1d(256, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            
            nn.Conv1d(128, num_keypoints, 1)
        )
        
        self.dropout = nn.Dropout(0.3)
        
        # 损失函数
        self.criterion = nn.MSELoss()
        
        # 评估指标
        self.train_mse = torchmetrics.MeanSquaredError()
        self.val_mse = torchmetrics.MeanSquaredError()
        self.test_mse = torchmetrics.MeanSquaredError()
        
        # 自定义指标 - 平均距离误差
        self.val_avg_distance_error = torchmetrics.MeanMetric()
        self.test_avg_distance_error = torchmetrics.MeanMetric()
        
    def forward(self, x):
        # x: [B, 3, N]
        x = torch.relu(self.bn1(self.conv1(x)))
        x = torch.relu(self.bn2(self.conv2(x)))
        x = torch.relu(self.bn3(self.conv3(x)))
        x = torch.relu(self.bn4(self.conv4(x)))
        x = self.bn5(self.conv5(x))
        
        x = self.dropout(x)
        heatmaps = self.heatmap_head(x)  # [B, 19, N]
        
        return heatmaps
    
    def training_step(self, batch, batch_idx):
        point_clouds = batch['point_cloud']  # [B, 3, N]
        heatmap_targets = batch['heatmaps']  # [B, 19, N]
        
        # 前向传播
        pred_heatmaps = self(point_clouds)
        
        # 计算损失
        loss = self.criterion(pred_heatmaps, heatmap_targets)
        
        # 记录指标
        self.train_mse(pred_heatmaps, heatmap_targets)
        
        # 日志记录
        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True)
        self.log('train_mse', self.train_mse, on_step=False, on_epoch=True)
        
        return loss
    
    def validation_step(self, batch, batch_idx):
        point_clouds = batch['point_cloud']
        heatmap_targets = batch['heatmaps']
        true_keypoints = batch['keypoints']  # [B, 19, 3]
        
        # 前向传播
        pred_heatmaps = self(point_clouds)
        
        # 计算损失
        loss = self.criterion(pred_heatmaps, heatmap_targets)
        
        # 记录MSE
        self.val_mse(pred_heatmaps, heatmap_targets)
        
        # 计算3D距离误差
        batch_size = point_clouds.shape[0]
        total_distance_error = 0
        
        for i in range(batch_size):
            pc = point_clouds[i].transpose(0, 1).cpu().numpy()  # [N, 3]
            pred_hm = pred_heatmaps[i].cpu().numpy()  # [19, N]
            true_kp = true_keypoints[i].cpu().numpy()  # [19, 3]
            
            # 从热力图提取关键点
            pred_kp = self.extract_keypoints_from_heatmaps(pred_hm, pc)
            
            # 计算距离误差
            distances = [np.linalg.norm(pred_kp[j] - true_kp[j]) for j in range(19)]
            avg_distance = np.mean(distances)
            total_distance_error += avg_distance
        
        avg_batch_distance_error = total_distance_error / batch_size
        self.val_avg_distance_error(avg_batch_distance_error)
        
        # 日志记录
        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
        self.log('val_mse', self.val_mse, on_step=False, on_epoch=True)
        self.log('val_avg_distance_error', self.val_avg_distance_error, on_step=False, on_epoch=True, prog_bar=True)
        
        return loss
    
    def test_step(self, batch, batch_idx):
        point_clouds = batch['point_cloud']
        heatmap_targets = batch['heatmaps']
        true_keypoints = batch['keypoints']
        
        # 前向传播
        pred_heatmaps = self(point_clouds)
        
        # 计算损失
        loss = self.criterion(pred_heatmaps, heatmap_targets)
        
        # 记录MSE
        self.test_mse(pred_heatmaps, heatmap_targets)
        
        # 计算3D距离误差
        batch_size = point_clouds.shape[0]
        total_distance_error = 0
        
        for i in range(batch_size):
            pc = point_clouds[i].transpose(0, 1).cpu().numpy()
            pred_hm = pred_heatmaps[i].cpu().numpy()
            true_kp = true_keypoints[i].cpu().numpy()
            
            pred_kp = self.extract_keypoints_from_heatmaps(pred_hm, pc)
            distances = [np.linalg.norm(pred_kp[j] - true_kp[j]) for j in range(19)]
            avg_distance = np.mean(distances)
            total_distance_error += avg_distance
        
        avg_batch_distance_error = total_distance_error / batch_size
        self.test_avg_distance_error(avg_batch_distance_error)
        
        # 日志记录
        self.log('test_loss', loss, on_step=False, on_epoch=True)
        self.log('test_mse', self.test_mse, on_step=False, on_epoch=True)
        self.log('test_avg_distance_error', self.test_avg_distance_error, on_step=False, on_epoch=True)
        
        return loss
    
    def configure_optimizers(self):
        optimizer = torch.optim.AdamW(
            self.parameters(), 
            lr=self.hparams.learning_rate,
            weight_decay=self.hparams.weight_decay
        )
        
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            patience=self.hparams.scheduler_patience,
            factor=self.hparams.scheduler_factor
        )
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'monitor': 'val_avg_distance_error',
                'interval': 'epoch',
                'frequency': 1
            }
        }
    
    def extract_keypoints_from_heatmaps(self, heatmaps, point_cloud):
        """从热力图中提取关键点位置"""
        num_keypoints = heatmaps.shape[0]
        keypoints = []
        
        for kp_idx in range(num_keypoints):
            heatmap = heatmaps[kp_idx]
            max_idx = np.argmax(heatmap)
            keypoint_3d = point_cloud[max_idx]
            keypoints.append(keypoint_3d)
        
        return np.array(keypoints)

class LightningF3Dataset(Dataset):
    """Lightning兼容的F3数据集"""
    
    def __init__(self, point_clouds, keypoints, sample_ids, num_points=8192, 
                 sigma=6.0, augment=False):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.sample_ids = sample_ids
        self.num_points = num_points
        self.sigma = sigma
        self.augment = augment
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        pc = self.point_clouds[idx].copy()
        kp = self.keypoints[idx].copy()
        sample_id = self.sample_ids[idx]
        
        # 数据增强
        if self.augment:
            pc, kp = self.apply_augmentation(pc, kp)
        
        # 采样点云
        if len(pc) > self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=False)
            pc = pc[indices]
        elif len(pc) < self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=True)
            pc = pc[indices]
        
        # 生成热力图目标
        heatmaps = self.create_heatmap_targets(kp, pc)
        
        return {
            'point_cloud': torch.FloatTensor(pc).transpose(0, 1),  # [3, N]
            'heatmaps': torch.FloatTensor(heatmaps),  # [19, N]
            'keypoints': torch.FloatTensor(kp),  # [19, 3]
            'sample_id': sample_id
        }
    
    def create_heatmap_targets(self, keypoints, point_cloud):
        """创建热力图目标"""
        num_points = len(point_cloud)
        num_keypoints = len(keypoints)
        heatmaps = np.zeros((num_keypoints, num_points))
        
        for kp_idx, keypoint in enumerate(keypoints):
            distances = np.linalg.norm(point_cloud - keypoint, axis=1)
            heatmaps[kp_idx] = np.exp(-distances**2 / (2 * self.sigma**2))
        
        return heatmaps
    
    def apply_augmentation(self, pc, kp):
        """数据增强"""
        # 小幅旋转
        if np.random.random() < 0.3:
            angle = np.random.uniform(-5, 5) * np.pi / 180
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            pc = pc @ rotation_matrix.T
            kp = kp @ rotation_matrix.T
        
        # 轻微缩放
        if np.random.random() < 0.2:
            scale = np.random.uniform(0.98, 1.02)
            pc = pc * scale
            kp = kp * scale
        
        # 小幅平移
        if np.random.random() < 0.2:
            translation = np.random.uniform(-1, 1, 3)
            pc = pc + translation
            kp = kp + translation
        
        return pc, kp

class LightningF3DataModule(pl.LightningDataModule):
    """Lightning数据模块"""
    
    def __init__(self, data_path='f3_19kp_preprocessed.npz', 
                 batch_size=4, num_workers=2, val_split=0.2, test_split=0.1):
        super().__init__()
        self.data_path = data_path
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.val_split = val_split
        self.test_split = test_split
    
    def setup(self, stage=None):
        # 加载数据
        data = np.load(self.data_path, allow_pickle=True)
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        sample_ids = data['sample_ids']
        
        # 数据分割
        train_val_indices, test_indices = train_test_split(
            range(len(point_clouds)), test_size=self.test_split, random_state=42
        )
        
        train_indices, val_indices = train_test_split(
            train_val_indices, test_size=self.val_split/(1-self.test_split), random_state=42
        )
        
        # 创建数据集
        self.train_dataset = LightningF3Dataset(
            point_clouds[train_indices], keypoints[train_indices], 
            sample_ids[train_indices], augment=True
        )
        
        self.val_dataset = LightningF3Dataset(
            point_clouds[val_indices], keypoints[val_indices], 
            sample_ids[val_indices], augment=False
        )
        
        self.test_dataset = LightningF3Dataset(
            point_clouds[test_indices], keypoints[test_indices], 
            sample_ids[test_indices], augment=False
        )
        
        print(f"数据分割完成:")
        print(f"  训练集: {len(self.train_dataset)} 样本")
        print(f"  验证集: {len(self.val_dataset)} 样本")
        print(f"  测试集: {len(self.test_dataset)} 样本")
    
    def train_dataloader(self):
        return DataLoader(
            self.train_dataset, 
            batch_size=self.batch_size, 
            shuffle=True, 
            num_workers=self.num_workers,
            persistent_workers=True if self.num_workers > 0 else False
        )
    
    def val_dataloader(self):
        return DataLoader(
            self.val_dataset, 
            batch_size=self.batch_size, 
            shuffle=False, 
            num_workers=self.num_workers,
            persistent_workers=True if self.num_workers > 0 else False
        )
    
    def test_dataloader(self):
        return DataLoader(
            self.test_dataset, 
            batch_size=self.batch_size, 
            shuffle=False, 
            num_workers=self.num_workers,
            persistent_workers=True if self.num_workers > 0 else False
        )

def train_lightning_model():
    """使用Lightning训练模型"""
    
    print("🚀 使用PyTorch Lightning训练19关键点检测模型")
    print("=" * 60)
    
    # 设置随机种子
    pl.seed_everything(42)
    
    # 创建数据模块
    data_module = LightningF3DataModule(
        data_path='f3_19kp_preprocessed.npz',
        batch_size=4,
        num_workers=2,
        val_split=0.2,
        test_split=0.1
    )
    
    # 创建模型
    model = LightningHeatmapPointNet(
        input_dim=3,
        num_keypoints=19,
        learning_rate=0.001,
        weight_decay=0.01,
        scheduler_patience=10,
        scheduler_factor=0.5
    )
    
    # 设置回调函数
    callbacks = [
        # 模型检查点
        ModelCheckpoint(
            monitor='val_avg_distance_error',
            mode='min',
            save_top_k=3,
            filename='best-{epoch:02d}-{val_avg_distance_error:.2f}',
            save_last=True
        ),
        
        # 早停
        EarlyStopping(
            monitor='val_avg_distance_error',
            mode='min',
            patience=20,
            verbose=True
        ),
        
        # 学习率监控
        LearningRateMonitor(logging_interval='epoch')
    ]
    
    # 设置日志记录器
    logger = TensorBoardLogger(
        save_dir='lightning_logs',
        name='f3_19keypoints',
        version=None
    )
    
    # 创建训练器
    trainer = pl.Trainer(
        max_epochs=100,
        accelerator='gpu' if torch.cuda.is_available() else 'cpu',
        devices=1,
        callbacks=callbacks,
        logger=logger,
        log_every_n_steps=10,
        val_check_interval=1.0,
        gradient_clip_val=1.0,
        deterministic=True
    )
    
    # 训练模型
    trainer.fit(model, data_module)
    
    # 测试模型
    trainer.test(model, data_module, ckpt_path='best')
    
    print(f"\n✅ 训练完成!")
    print(f"📊 TensorBoard日志: lightning_logs/f3_19keypoints")
    print(f"💾 最佳模型: {trainer.checkpoint_callback.best_model_path}")
    
    return trainer, model

def main():
    """主函数"""
    print("⚡ PyTorch Lightning 19关键点检测系统")
    print("=" * 60)
    
    # 训练模型
    trainer, model = train_lightning_model()
    
    print(f"\n🎯 Lightning的优势:")
    print("✅ 自动化训练循环")
    print("✅ 内置模型检查点")
    print("✅ 早停和学习率调度")
    print("✅ TensorBoard集成")
    print("✅ 多GPU支持")
    print("✅ 代码组织更清晰")
    print("✅ 实验管理更方便")
    
    print(f"\n💡 使用方法:")
    print("1. 运行训练: python lightning_keypoint_system.py")
    print("2. 查看日志: tensorboard --logdir lightning_logs")
    print("3. 加载模型: model = LightningHeatmapPointNet.load_from_checkpoint(path)")

if __name__ == "__main__":
    main()
