#!/usr/bin/env python3
"""
Create Training Dataset - Conservative Version

Create datasets with careful memory management to avoid segfaults.
"""

import numpy as np
import pandas as pd
from pathlib import Path
import h5py
import json
import gc
from sklearn.model_selection import train_test_split
from correct_data_loader import load_sample_data

def sample_point_cloud_safe(vertices, target_points=4096):
    """Safely sample point cloud with memory management"""
    try:
        if len(vertices) <= target_points:
            indices = np.random.choice(len(vertices), target_points, replace=True)
            result = vertices[indices].copy()
        else:
            indices = np.random.choice(len(vertices), target_points, replace=False)
            result = vertices[indices].copy()
        
        # Force cleanup
        del vertices, indices
        gc.collect()
        
        return result
    except Exception as e:
        print(f"      ❌ 点云采样失败: {e}")
        return None

def process_single_sample_f3(sample_id):
    """Process single F3 sample with conservative memory management"""
    
    try:
        print(f"   🔧 处理样本 {sample_id}")
        
        # Load sample data
        sample_data = load_sample_data(sample_id)
        
        if sample_data is None:
            return None
        
        # Check F3 availability
        if 'F3' not in sample_data['stl_data'] or len(sample_data['regions']['F3']['keypoints']) == 0:
            print(f"      ❌ F3数据不完整")
            return None
        
        # Get F3 data
        f3_vertices = sample_data['stl_data']['F3']
        f3_keypoints = sample_data['regions']['F3']['keypoints']
        
        # Sample point cloud
        sampled_pc = sample_point_cloud_safe(f3_vertices, target_points=4096)
        
        if sampled_pc is None:
            return None
        
        # Simple normalization (center only, keep scale)
        pc_center = np.mean(sampled_pc, axis=0)
        kp_center = np.mean(f3_keypoints, axis=0)
        
        # Use keypoint center for consistency
        normalized_pc = sampled_pc - kp_center
        normalized_kps = f3_keypoints - kp_center
        
        result = {
            'sample_id': sample_id,
            'point_cloud': normalized_pc.astype(np.float32),
            'keypoints': normalized_kps.astype(np.float32),
            'center': kp_center.astype(np.float32)
        }
        
        # Force cleanup
        del sample_data, f3_vertices, f3_keypoints, sampled_pc
        del pc_center, kp_center, normalized_pc, normalized_kps
        gc.collect()
        
        print(f"      ✅ 成功处理")
        return result
        
    except Exception as e:
        print(f"      ❌ 处理失败: {e}")
        return None

def create_f3_dataset_batch():
    """Create F3 dataset in small batches"""
    
    print("🏗️ **创建F3数据集 (保守版本)**")
    print("=" * 60)
    
    # Get available samples
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    annotations_dir = data_dir / "annotations"
    
    xyz_files = list(annotations_dir.glob("*-Table-XYZ.CSV"))
    excluded_samples = {'600025', '600026', '600027'}
    
    valid_sample_ids = []
    for csv_file in xyz_files:
        sample_id = csv_file.stem.split('-')[0]
        if sample_id not in excluded_samples:
            valid_sample_ids.append(sample_id)
    
    print(f"📂 找到 {len(valid_sample_ids)} 个有效样本")
    
    # Process in small batches
    batch_size = 5
    all_samples = []
    
    for i in range(0, len(valid_sample_ids), batch_size):
        batch_ids = valid_sample_ids[i:i+batch_size]
        print(f"\n📦 处理批次 {i//batch_size + 1}: {batch_ids}")
        
        batch_results = []
        for sample_id in batch_ids:
            result = process_single_sample_f3(sample_id)
            if result is not None:
                batch_results.append(result)
        
        all_samples.extend(batch_results)
        
        # Force cleanup after each batch
        del batch_results
        gc.collect()
        
        print(f"   📊 批次完成: {len(batch_results)} 个成功样本")
        print(f"   📈 累计样本: {len(all_samples)}")
    
    print(f"\n✅ 总共处理成功: {len(all_samples)} 个F3样本")
    
    if len(all_samples) < 10:
        print("❌ 样本数量不足，无法创建数据集")
        return None
    
    # Save dataset
    return save_f3_dataset_simple(all_samples)

def save_f3_dataset_simple(samples):
    """Save F3 dataset with simple format"""
    
    print(f"\n💾 **保存F3数据集**")
    
    # Split data
    train_samples, temp_samples = train_test_split(samples, test_size=0.3, random_state=42)
    val_samples, test_samples = train_test_split(temp_samples, test_size=0.5, random_state=42)
    
    print(f"   📊 数据分割:")
    print(f"      训练: {len(train_samples)} 样本")
    print(f"      验证: {len(val_samples)} 样本") 
    print(f"      测试: {len(test_samples)} 样本")
    
    # Save to HDF5
    output_path = "medical_f3_dataset_conservative.h5"
    
    try:
        with h5py.File(output_path, 'w') as f:
            # Save each split
            for split_name, split_samples in [
                ('train', train_samples),
                ('val', val_samples),
                ('test', test_samples)
            ]:
                if split_samples:
                    group = f.create_group(split_name)
                    
                    # Stack data
                    point_clouds = np.stack([s['point_cloud'] for s in split_samples])
                    keypoints = np.stack([s['keypoints'] for s in split_samples])
                    centers = np.stack([s['center'] for s in split_samples])
                    sample_ids = [s['sample_id'] for s in split_samples]
                    
                    # Save datasets
                    group.create_dataset('point_clouds', data=point_clouds, compression='gzip')
                    group.create_dataset('keypoints', data=keypoints, compression='gzip')
                    group.create_dataset('centers', data=centers, compression='gzip')
                    
                    # Save sample IDs
                    dt = h5py.string_dtype(encoding='utf-8')
                    group.create_dataset('sample_ids', data=sample_ids, dtype=dt)
                    
                    print(f"      {split_name}: 保存 {len(split_samples)} 样本")
            
            # Save metadata
            f.attrs['dataset_name'] = 'Medical_F3_Conservative'
            f.attrs['total_samples'] = len(samples)
            f.attrs['point_cloud_size'] = samples[0]['point_cloud'].shape[0]
            f.attrs['keypoint_count'] = samples[0]['keypoints'].shape[0]
            f.attrs['creation_date'] = str(pd.Timestamp.now())
        
        print(f"   ✅ 数据集已保存: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"   ❌ 保存失败: {e}")
        return None

def test_dataset_loading(dataset_path):
    """Test loading the created dataset"""
    
    print(f"\n🧪 **测试数据集加载: {dataset_path}**")
    
    try:
        with h5py.File(dataset_path, 'r') as f:
            print(f"   📊 数据集信息:")
            print(f"      名称: {f.attrs.get('dataset_name', 'Unknown')}")
            print(f"      总样本: {f.attrs.get('total_samples', 'Unknown')}")
            print(f"      点云大小: {f.attrs.get('point_cloud_size', 'Unknown')}")
            print(f"      关键点数: {f.attrs.get('keypoint_count', 'Unknown')}")
            
            for split in ['train', 'val', 'test']:
                if split in f:
                    group = f[split]
                    pc_shape = group['point_clouds'].shape
                    kp_shape = group['keypoints'].shape
                    print(f"      {split}: 点云{pc_shape}, 关键点{kp_shape}")
        
        print(f"   ✅ 数据集加载测试成功")
        return True
        
    except Exception as e:
        print(f"   ❌ 数据集加载测试失败: {e}")
        return False

if __name__ == "__main__":
    # Create F3 dataset
    dataset_path = create_f3_dataset_batch()
    
    if dataset_path:
        # Test loading
        test_dataset_loading(dataset_path)
        
        print(f"\n🎉 **F3数据集创建成功!**")
        print(f"📁 文件路径: {dataset_path}")
        print(f"🎯 用途: F3单部件关键点检测训练")
        print(f"💡 下一步: 使用此数据集训练基线模型验证性能")
    else:
        print(f"\n❌ **数据集创建失败**")
