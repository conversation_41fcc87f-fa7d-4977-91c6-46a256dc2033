﻿实验类别,方法名称,验证误差(mm),测试误差(mm),训练策略,特殊技术,参数量,训练时间,稳定性,备注
高级方法,Mixup模型,7.041,8.363,Mixup增强,数据混合,约300万,中等,中等,验证误差最佳
高级方法,Point Transformer,7.129,8.127,注意力机制,点云Transformer,约500万,长,很高,历史最稳定
高级方法,一致性正则化,7.176,8.012,双网络一致性,一致性损失,约400万,中等,很高,测试性能最佳
基础方法,简单集成PointNet,7.19,7.19,3模型集成,模型集成,约400万,中等,高,早期最佳结果
小样本学习,基于梯度的元学习,7.277,8.039,简化MAML,快速适应,约250万,中等,高,元学习最佳
前沿方法,注意力机制/Transformer,7.383,9.588,Transformer编码器,自注意力+位置编码,约450万,长,中等,前沿方法最佳
小样本学习,原型网络,7.426,8.027,原型学习,距离度量学习,约300万,中等,中等,小样本方法最佳
小样本学习,迁移学习,7.469,8.258,冻结+微调,预训练特征,约180万(可训练),短,高,实用性强
前沿方法,集成元学习,7.587,8.487,动态权重集成,自适应集成,约600万,长,中等,多模型集成
小样本学习,自监督学习,7.602,8.968,多任务学习,旋转+噪声预测,约350万,长,中等,多任务辅助
前沿方法,图神经网络,7.655,8.294,k-NN图+图卷积,图结构建模,约350万,长,中等,几何关系建模
前沿方法,对比学习,7.855,8.497,InfoNCE对比学习,对比损失,约380万,中等,中等,表示学习
小样本学习,匹配网络,8.47,10.536,注意力匹配,多头注意力,约320万,长,低,注意力机制
小样本学习,关系网络,8.551,10.912,关系学习,关系建模,约280万,中等,低,复杂度过高
前沿方法,变分自编码器,8.679,8.451,VAE潜在空间学习,变分推断,约320万,中等,中等,生成建模
优化尝试,精准微调优化,11.052,10.881,架构微调,精准TTA,约580万,长,低,过度优化失败
优化尝试,增强Mixup优化,11.935,12.641,增强架构,高级Mixup,约450万,长,低,优化适得其反
基础方法,简单PointNet,15.234,16.892,标准训练,无,约200万,短,中等,基础基线
优化尝试,最小化改进,15.418,11.673,保守训练,最小TTA,约400万,长,低,复现失败
