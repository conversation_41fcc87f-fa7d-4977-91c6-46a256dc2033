#!/usr/bin/env python3
"""
决定性测试 - 使用历史数据集
Decisive Test with Historical Dataset
使用历史f3_reduced_12kp_stable.npz数据集训练我们的模型
这将最终确定是数据集还是架构问题
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import json

class SimpleHistoricalPointNet(nn.Module):
    """简单的历史风格PointNet - 匹配历史架构"""
    
    def __init__(self, num_keypoints=12):
        super(SimpleHistoricalPointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 精确匹配历史架构
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 残差连接
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        # 全连接层 - 精确匹配历史
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)  # 12 * 3 = 36
        
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(0.3)
        
        # Xavier初始化 - 匹配历史
        self._initialize_weights()
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"🏗️ SimpleHistoricalPointNet: {total_params:,} 参数")
        print(f"   目标: 复现5.371mm性能")
        
    def _initialize_weights(self):
        """Xavier权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取 + 残差连接
        x1 = F.relu(self.bn1(self.conv1(x)))
        x2 = F.relu(self.bn2(self.conv2(x1)))
        x3 = F.relu(self.bn3(self.conv3(x2)))
        x3_res = x3 + self.residual1(x1)
        
        x4 = F.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = F.relu(self.bn5(self.conv5(x4_res)))
        
        # 全局最大池化
        global_feat = torch.max(x5, 2)[0]  # [B, 1024]
        
        # 回归头 - 精确匹配历史
        x = F.relu(self.bn_fc1(self.fc1(global_feat)))
        x = self.dropout(x)
        x = F.relu(self.bn_fc2(self.fc2(x)))
        x = self.dropout(x)
        x = F.relu(self.bn_fc3(self.fc3(x)))
        x = self.dropout(x)
        x = F.relu(self.bn_fc4(self.fc4(x)))
        x = self.dropout(x)
        
        keypoints = self.fc5(x)
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

class HistoricalDataset(Dataset):
    def __init__(self, point_clouds, keypoints):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def load_historical_dataset():
    """加载历史数据集"""
    
    print("📊 加载历史数据集...")
    
    historical_path = "archive/old_experiments/f3_reduced_12kp_stable.npz"
    
    try:
        data = np.load(historical_path, allow_pickle=True)
        
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        sample_ids = data['sample_ids']
        
        print(f"✅ 历史数据集加载成功:")
        print(f"   样本数: {len(sample_ids)}")
        print(f"   点云形状: {point_clouds.shape}")
        print(f"   关键点形状: {keypoints.shape}")
        print(f"   数据范围: [{np.min(keypoints):.2f}, {np.max(keypoints):.2f}]")
        print(f"   平均值: {np.mean(keypoints, axis=(0,1))}")
        print(f"   标准差: {np.std(keypoints, axis=(0,1))}")
        
        return point_clouds, keypoints, sample_ids
        
    except Exception as e:
        print(f"❌ 加载历史数据集失败: {e}")
        return None, None, None

def historical_normalization(point_clouds, keypoints):
    """历史风格的归一化"""
    
    print("🔧 执行历史风格归一化...")
    
    normalized_pc = []
    normalized_kp = []
    scalers = []
    
    for i in range(len(point_clouds)):
        pc = point_clouds[i].copy()
        kp = keypoints[i].copy()
        
        # 历史风格：基于关键点中心的归一化
        kp_center = np.mean(kp, axis=0)
        
        # 中心化
        pc_centered = pc - kp_center
        kp_centered = kp - kp_center
        
        # 基于关键点范围的缩放
        kp_scale = np.std(kp_centered)
        
        if kp_scale > 0:
            pc_normalized = pc_centered / kp_scale
            kp_normalized = kp_centered / kp_scale
        else:
            pc_normalized = pc_centered
            kp_normalized = kp_centered
        
        normalized_pc.append(pc_normalized)
        normalized_kp.append(kp_normalized)
        scalers.append({'center': kp_center, 'scale': kp_scale})
    
    return np.array(normalized_pc), np.array(normalized_kp), scalers

def train_on_historical_data(model, train_loader, val_loader, epochs=100, device='cuda'):
    """在历史数据上训练"""
    
    print(f"🚀 在历史数据集上训练模型...")
    print(f"   目标: 验证是否能达到5.371mm")
    
    model = model.to(device)
    
    # 历史风格的训练配置
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.8, patience=10, min_lr=1e-6
    )
    
    criterion = nn.MSELoss()
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 20
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            loss.backward()
            
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                val_loss += loss.item()
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        scheduler.step(val_loss)
        
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_historical_data_model.pth')
        else:
            patience_counter += 1
        
        if epoch % 10 == 0 or epoch < 5:
            current_lr = optimizer.param_groups[0]['lr']
            print(f"Epoch {epoch+1:3d}: "
                  f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
                  f"Train Error: {train_error:.4f}, Val Error: {val_error:.4f}, "
                  f"LR: {current_lr:.2e}")
        
        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    model.load_state_dict(torch.load('best_historical_data_model.pth'))
    return model

def test_on_historical_data(model, test_loader, scalers, test_indices, device='cuda'):
    """在历史数据上测试"""
    
    print("🔍 在历史数据集上测试...")
    
    model = model.to(device)
    model.eval()
    
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for batch_pc, batch_kp in test_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            predicted = model(batch_pc)
            
            for i in range(predicted.size(0)):
                all_predictions.append(predicted[i].cpu().numpy())
                all_targets.append(batch_kp[i].cpu().numpy())
    
    # 反归一化到真实空间
    real_errors = []
    
    for i, orig_idx in enumerate(test_indices):
        if i < len(all_predictions):
            pred_norm = all_predictions[i]
            target_norm = all_targets[i]
            
            scaler_info = scalers[orig_idx]
            center = scaler_info['center']
            scale = scaler_info['scale']
            
            # 反归一化
            pred_real = pred_norm * scale + center
            target_real = target_norm * scale + center
            
            # 计算真实空间误差
            distances = np.linalg.norm(pred_real - target_real, axis=1)
            real_errors.extend(distances)
    
    avg_error = np.mean(real_errors)
    
    # 计算准确率
    accuracy_5mm = np.mean(np.array(real_errors) < 5.0) * 100
    accuracy_7mm = np.mean(np.array(real_errors) < 7.0) * 100
    accuracy_10mm = np.mean(np.array(real_errors) < 10.0) * 100
    
    return avg_error, accuracy_5mm, accuracy_7mm, accuracy_10mm

def main():
    """主函数"""
    
    print("🎯 决定性测试 - 使用历史数据集")
    print("验证是数据集问题还是架构问题")
    print("=" * 80)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载历史数据集
    point_clouds, keypoints, sample_ids = load_historical_dataset()
    
    if point_clouds is None:
        print("❌ 无法加载历史数据集，测试终止")
        return
    
    # 历史风格归一化
    normalized_pc, normalized_kp, scalers = historical_normalization(point_clouds, keypoints)
    
    # 数据划分 - 使用相同的随机种子
    indices = np.arange(len(normalized_pc))
    train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
    train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
    
    # 创建数据集
    train_dataset = HistoricalDataset(normalized_pc[train_indices], normalized_kp[train_indices])
    val_dataset = HistoricalDataset(normalized_pc[val_indices], normalized_kp[val_indices])
    test_dataset = HistoricalDataset(normalized_pc[test_indices], normalized_kp[test_indices])
    
    # 数据加载器
    batch_size = 4  # 历史使用的小批次
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    
    print(f"📋 数据划分: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    
    # 创建模型
    model = SimpleHistoricalPointNet(num_keypoints=12)
    
    # 训练模型
    model = train_on_historical_data(model, train_loader, val_loader, epochs=80, device=device)
    
    # 测试模型
    avg_error, acc_5mm, acc_7mm, acc_10mm = test_on_historical_data(
        model, test_loader, scalers, test_indices, device=device
    )
    
    print(f"\n🎯 决定性测试结果:")
    print(f"   平均误差: {avg_error:.2f}mm")
    print(f"   <5mm准确率: {acc_5mm:.1f}%")
    print(f"   <7mm准确率: {acc_7mm:.1f}%")
    print(f"   <10mm准确率: {acc_10mm:.1f}%")
    
    print(f"\n📊 与历史目标对比:")
    print(f"   历史目标: 5.371mm (35%<5mm, 90%<7mm)")
    print(f"   我们结果: {avg_error:.2f}mm ({acc_5mm:.1f}%<5mm, {acc_7mm:.1f}%<7mm)")
    print(f"   误差差距: {avg_error - 5.371:.2f}mm")
    
    # 判断结果
    if avg_error < 7.0:
        print(f"\n🎉 成功！接近历史性能！")
        print(f"💡 证明问题主要在数据集，不是架构")
    elif avg_error < 10.0:
        print(f"\n✅ 显著改进！")
        print(f"💡 数据集是主要因素，架构可能有小影响")
    else:
        print(f"\n⚠️ 仍有差距")
        print(f"💡 可能架构和数据集都有影响")
    
    # 保存结果
    results = {
        'avg_error_mm': float(avg_error),
        'accuracy_5mm': float(acc_5mm),
        'accuracy_7mm': float(acc_7mm),
        'accuracy_10mm': float(acc_10mm),
        'historical_target': {
            'avg_error_mm': 5.371,
            'accuracy_5mm': 35.0,
            'accuracy_7mm': 90.0
        },
        'gap_to_target': float(avg_error - 5.371),
        'conclusion': 'dataset_vs_architecture_analysis',
        'dataset_used': 'f3_reduced_12kp_stable.npz',
        'model_architecture': 'SimpleHistoricalPointNet'
    }
    
    with open('decisive_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n🎯 结论:")
    if avg_error < 7.0:
        print(f"   ✅ 数据集是主要问题，架构基本正确")
    elif avg_error < 10.0:
        print(f"   ✅ 数据集是主要问题，架构有小影响")
    else:
        print(f"   ⚠️ 数据集和架构都可能有问题")
    
    print(f"\n💾 详细结果已保存: decisive_test_results.json")
    print(f"🎉 决定性测试完成！")

if __name__ == "__main__":
    main()
