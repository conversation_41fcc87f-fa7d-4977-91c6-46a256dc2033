#!/usr/bin/env python3
"""
优化集成双Softmax训练脚本
基于5.829mm成功配置的精细优化
目标: 突破5.5mm医疗级精度
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random
from optimize_ensemble_double_softmax import OptimizedBaselinePointNet, ReducedKeypointsF3Dataset

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

class ImprovedLoss(nn.Module):
    """改进损失函数"""
    
    def __init__(self, alpha=0.8, beta=0.2):
        super(ImprovedLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, pred, target):
        mse_loss = F.mse_loss(pred, target)
        smooth_l1_loss = F.smooth_l1_loss(pred, target)
        total_loss = self.alpha * mse_loss + self.beta * smooth_l1_loss
        return total_loss

def train_optimized_ensemble():
    """训练优化集成双Softmax模型"""
    
    print(f"🚀 **优化集成双Softmax训练**")
    print(f"🔬 **优化**: 5个模块 + 智能权重学习 + 精细参数调优")
    print(f"🎯 **基础**: 5.829mm集成双Softmax成功配置")
    print(f"📈 **目标**: 突破5.5mm医疗级精度")
    print(f"🏗️ **策略**: 保守优化，确保稳定改进")
    print("=" * 80)
    
    set_seed(42)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 数据集 (完全复用成功配置)
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    train_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'train', 
                                            num_points=4096, test_samples=test_samples, 
                                            augment=True, seed=42)
    val_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'val', 
                                          num_points=4096, test_samples=test_samples, 
                                          augment=False, seed=42)
    
    batch_size = 4  # 保持成功配置
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
    
    # 模型
    model = OptimizedBaselinePointNet(num_keypoints=12, dropout_rate=0.3, ensemble_type="advanced").to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 模型参数: {total_params:,}")
    
    # 损失函数 (保持成功配置)
    criterion = ImprovedLoss(alpha=0.8, beta=0.2)
    
    # 优化器 (保持成功配置)
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
    
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=12, min_lr=1e-6
    )
    
    num_epochs = 100  # 稍微减少轮数，避免过拟合
    best_val_error = float('inf')
    patience = 20
    patience_counter = 0
    history = []
    min_delta = 0.005
    
    print(f"🎯 训练配置: 优化集成双Softmax")
    print(f"   集成模块: 5个 (参数范围优化)")
    print(f"   智能权重: 可学习集成权重")
    print(f"   候选点数: 384 (增加精度)")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 40)
        
        # 训练
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        for batch in train_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                        
            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue
        
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # 验证 (使用优化的双Softmax精细化)
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)  # 推理时使用优化双Softmax
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # 学习率调度
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 打印结果
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"5mm={train_metrics['within_5mm_percent']:.1f}%, 7mm={train_metrics['within_7mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"5mm={val_metrics['within_5mm_percent']:.1f}%, 7mm={val_metrics['within_7mm_percent']:.1f}%")
        print(f"学习率: {current_lr:.2e}")
        
        # 打印集成权重 (如果是高级集成)
        if hasattr(model.double_softmax, 'ensemble_weights'):
            weights = F.softmax(model.double_softmax.ensemble_weights, dim=0)
            weights_str = ", ".join([f"{w:.3f}" for w in weights])
            print(f"集成权重: [{weights_str}]")
        
        # 保存历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'learning_rate': current_lr
        })
        
        # 检查改进
        current_error = val_metrics['mean_distance']
        improvement = best_val_error - current_error
        
        if improvement > min_delta:
            best_val_error = current_error
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics,
                'model_name': 'Optimized_Ensemble_Double_SoftMax',
                'config': 'advanced_ensemble_5_modules_smart_weights'
            }, f'best_optimized_ensemble_{best_val_error:.3f}mm.pth')
            
            print(f"🎉 新最佳! 验证误差: {best_val_error:.3f}mm (改进{improvement:.3f}mm)")
            
            if best_val_error <= 5.0:
                print(f"🏆 **突破5.0mm医疗级目标!**")
            elif best_val_error < 5.5:
                print(f"🎯 **突破5.5mm目标!** 达到医疗级精度")
            elif best_val_error < 5.829:
                print(f"✅ **超越基线!** 优化集成有效")
        else:
            patience_counter += 1
            print(f"⏳ 无显著改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停触发")
            break
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    
    # 保存结果
    results = {
        'method': 'Optimized Ensemble Double SoftMax',
        'baseline_error': 5.829,
        'best_val_error': float(best_val_error),
        'improvement': float((5.829 - best_val_error) / 5.829 * 100),
        'training_time_minutes': float(total_time / 60),
        'epochs_trained': len(history),
        'history': history,
        'optimized_config': {
            'num_ensembles': 5,
            'threshold_ratios': [0.08, 0.12, 0.16, 0.20, 0.24],
            'temperatures': [1.5, 1.8, 2.1, 2.4, 2.7],
            'weight_ratios': [0.65, 0.70, 0.75, 0.80, 0.85],
            'smart_ensemble_weights': True,
            'candidate_points': 384,
            'min_points_ratio': 0.1
        }
    }
    
    with open('optimized_ensemble_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 **优化集成双Softmax训练完成!**")
    print(f"📊 基线误差: 5.829mm")
    print(f"🎯 优化最佳: {best_val_error:.3f}mm")
    print(f"📈 改进幅度: {(5.829 - best_val_error) / 5.829 * 100:.1f}%")
    print(f"⏱️  训练时间: {total_time/60:.1f}分钟")
    
    if best_val_error < 5.0:
        print(f"🏆 **成功突破5.0mm医疗级目标!**")
    elif best_val_error < 5.5:
        print(f"🎯 **成功突破5.5mm目标!** 达到医疗级精度")
    elif best_val_error < 5.829:
        print(f"✅ **成功超越基线!** 优化集成有效")
    else:
        print(f"💡 **接近基线性能** 可能需要进一步调优")
    
    return best_val_error, results

if __name__ == "__main__":
    set_seed(42)
    best_error, results = train_optimized_ensemble()
