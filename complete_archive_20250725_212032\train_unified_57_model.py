#!/usr/bin/env python3
"""
训练统一骨盆57点模型
Train unified pelvis 57-point model
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import json
from tqdm import tqdm

class FixedMultiModalPointNet(nn.Module):
    """已验证的PointNet架构 - 适配57点"""
    
    def __init__(self, num_points=50000, num_keypoints=57):
        super(FixedMultiModalPointNet, self).__init__()
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 点云特征提取器
        self.point_conv1 = nn.Conv1d(3, 64, 1)
        self.point_conv2 = nn.Conv1d(64, 128, 1)
        self.point_conv3 = nn.Conv1d(128, 256, 1)
        self.point_conv4 = nn.Conv1d(256, 512, 1)
        self.point_conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化层
        self.point_bn1 = nn.BatchNorm1d(64)
        self.point_bn2 = nn.BatchNorm1d(128)
        self.point_bn3 = nn.BatchNorm1d(256)
        self.point_bn4 = nn.BatchNorm1d(512)
        self.point_bn5 = nn.BatchNorm1d(1024)
        
        # 全局特征提取
        self.global_conv1 = nn.Conv1d(1024, 512, 1)
        self.global_conv2 = nn.Conv1d(512, 256, 1)
        self.global_bn1 = nn.BatchNorm1d(512)
        self.global_bn2 = nn.BatchNorm1d(256)
        
        # 关键点预测头
        self.keypoint_fc1 = nn.Linear(256, 512)
        self.keypoint_fc2 = nn.Linear(512, 256)
        self.keypoint_fc3 = nn.Linear(256, num_keypoints * 3)
        
        # Dropout层
        self.dropout1 = nn.Dropout(0.3)
        self.dropout2 = nn.Dropout(0.4)
        
    def forward(self, point_cloud):
        """前向传播"""
        batch_size = point_cloud.size(0)
        
        # 点云特征提取
        x = point_cloud.transpose(2, 1)  # [B, 3, N]
        
        x = F.relu(self.point_bn1(self.point_conv1(x)))
        x = F.relu(self.point_bn2(self.point_conv2(x)))
        x = F.relu(self.point_bn3(self.point_conv3(x)))
        x = F.relu(self.point_bn4(self.point_conv4(x)))
        x = F.relu(self.point_bn5(self.point_conv5(x)))
        
        # 全局最大池化
        global_feature = torch.max(x, 2)[0]  # [B, 1024]
        
        # 全局特征处理
        x = global_feature.unsqueeze(2)  # [B, 1024, 1]
        x = F.relu(self.global_bn1(self.global_conv1(x)))
        x = F.relu(self.global_bn2(self.global_conv2(x)))
        x = x.squeeze(2)  # [B, 256]
        
        # 关键点预测
        x = F.relu(self.keypoint_fc1(x))
        x = self.dropout1(x)
        x = F.relu(self.keypoint_fc2(x))
        x = self.dropout2(x)
        keypoints = self.keypoint_fc3(x)
        
        # 重塑为关键点坐标
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

class Unified57Dataset(Dataset):
    """统一57点数据集"""
    
    def __init__(self, point_clouds, keypoints):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def load_unified_57_dataset(use_proper_alignment=True):
    """加载统一57点数据集"""

    if use_proper_alignment:
        dataset_file = 'properly_aligned_57_dataset.npz'
        print("📊 加载正确对齐的骨盆57点数据集...")
    else:
        dataset_file = 'unified_pelvis_57_dataset.npz'
        print("📊 加载简单平移的骨盆57点数据集...")

    try:
        data = np.load(dataset_file, allow_pickle=True)

        point_clouds = data['point_clouds']
        keypoints_57 = data['keypoints_57']
        keypoints_12 = data['keypoints_12']
        sample_ids = data['sample_ids']

        alignment_method = "刚体变换对齐" if use_proper_alignment else "简单平移对齐"
        print(f"✅ {alignment_method}数据加载成功:")
        print(f"   样本数: {len(sample_ids)}")
        print(f"   点云: {point_clouds.shape}")
        print(f"   57关键点: {keypoints_57.shape}")
        print(f"   12关键点: {keypoints_12.shape}")

        return point_clouds, keypoints_57, keypoints_12, sample_ids

    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None, None, None, None

def train_unified_57_model(model, train_loader, val_loader, epochs=50, device='cuda'):
    """训练统一57点模型"""
    
    print(f"🚀 开始训练统一57点模型...")
    print(f"   设备: {device}")
    print(f"   训练样本: {len(train_loader.dataset)}")
    print(f"   验证样本: {len(val_loader.dataset)}")
    print(f"   训练轮数: {epochs}")
    
    model = model.to(device)
    optimizer = optim.Adam(model.parameters(), lr=0.0002, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    criterion = nn.MSELoss()
    
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_error': [],
        'val_error': []
    }
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 20
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{epochs} [Train]')
        for batch_pc, batch_kp in train_pbar:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            
            # 计算平均距离误差
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
            
            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Error': f'{torch.mean(distances).item():.2f}mm'
            })
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{epochs} [Val]')
            for batch_pc, batch_kp in val_pbar:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                val_loss += loss.item()
                
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
                
                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Error': f'{torch.mean(distances).item():.2f}mm'
                })
        
        # 计算平均损失和误差
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_error'].append(train_error)
        history['val_error'].append(val_error)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_unified_57_model.pth')
        else:
            patience_counter += 1
        
        # 打印进度
        print(f"Epoch {epoch+1:3d}: "
              f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
              f"Train Error: {train_error:.2f}mm, Val Error: {val_error:.2f}mm")
        
        # 早停
        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    print(f"✅ 训练完成！最佳验证损失: {best_val_loss:.6f}")
    
    # 加载最佳模型
    model.load_state_dict(torch.load('best_unified_57_model.pth'))
    
    return history

def evaluate_unified_57_model(model, test_loader, device='cuda'):
    """评估统一57点模型"""
    
    print(f"\n🔍 评估统一57点模型性能...")
    
    model = model.to(device)
    model.eval()
    
    total_error = 0.0
    region_errors = {'F1': [], 'F2': [], 'F3': []}
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        test_pbar = tqdm(test_loader, desc='Testing')
        for batch_pc, batch_kp in test_pbar:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            predicted = model(batch_pc)
            
            # 计算整体误差
            distances = torch.norm(predicted - batch_kp, dim=2)
            total_error += torch.mean(distances).item()
            
            # 计算各区域误差
            for i in range(predicted.size(0)):
                pred = predicted[i].cpu().numpy()
                target = batch_kp[i].cpu().numpy()
                
                # F1区域 (0-18)
                f1_distances = np.linalg.norm(pred[0:19] - target[0:19], axis=1)
                region_errors['F1'].extend(f1_distances)
                
                # F2区域 (19-37)
                f2_distances = np.linalg.norm(pred[19:38] - target[19:38], axis=1)
                region_errors['F2'].extend(f2_distances)
                
                # F3区域 (38-56)
                f3_distances = np.linalg.norm(pred[38:57] - target[38:57], axis=1)
                region_errors['F3'].extend(f3_distances)
            
            all_predictions.append(predicted.cpu().numpy())
            all_targets.append(batch_kp.cpu().numpy())
            
            test_pbar.set_postfix({'Error': f'{torch.mean(distances).item():.2f}mm'})
    
    avg_error = total_error / len(test_loader)
    
    print(f"📊 统一57点模型评估结果:")
    print(f"   整体平均误差: {avg_error:.2f}mm")
    
    for region, errors in region_errors.items():
        if errors:
            mean_error = np.mean(errors)
            std_error = np.std(errors)
            max_error = np.max(errors)
            print(f"   {region}区域: {mean_error:.2f}±{std_error:.2f}mm (最大: {max_error:.2f}mm)")
    
    # 计算医疗级准确率
    all_errors = []
    for errors in region_errors.values():
        all_errors.extend(errors)
    
    if all_errors:
        accuracy_5mm = np.mean(np.array(all_errors) < 5.0) * 100
        accuracy_10mm = np.mean(np.array(all_errors) < 10.0) * 100
        
        print(f"   医疗级准确率:")
        print(f"     <5mm: {accuracy_5mm:.1f}%")
        print(f"     <10mm: {accuracy_10mm:.1f}%")
    
    return {
        'avg_error': avg_error,
        'region_errors': region_errors,
        'accuracy_5mm': accuracy_5mm if all_errors else 0,
        'accuracy_10mm': accuracy_10mm if all_errors else 0,
        'predictions': np.vstack(all_predictions),
        'targets': np.vstack(all_targets)
    }

def main():
    """主函数"""
    
    print("🎯 训练统一骨盆57点模型")
    print("基于F3中心的整体解剖结构")
    print("=" * 80)
    
    # 检查CUDA
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载统一数据
    point_clouds, keypoints_57, keypoints_12, sample_ids = load_unified_57_dataset()
    
    if point_clouds is None:
        print("❌ 数据加载失败，退出")
        return
    
    # 数据划分
    indices = np.arange(len(point_clouds))
    train_indices, test_indices = train_test_split(
        indices, test_size=0.2, random_state=42
    )
    train_indices, val_indices = train_test_split(
        train_indices, test_size=0.2, random_state=42
    )
    
    # 创建数据集
    train_dataset = Unified57Dataset(point_clouds[train_indices], keypoints_57[train_indices])
    val_dataset = Unified57Dataset(point_clouds[val_indices], keypoints_57[val_indices])
    test_dataset = Unified57Dataset(point_clouds[test_indices], keypoints_57[test_indices])
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False, drop_last=True)
    test_loader = DataLoader(test_dataset, batch_size=4, shuffle=False, drop_last=True)
    
    print(f"📋 数据划分:")
    print(f"   训练集: {len(train_dataset)} 样本")
    print(f"   验证集: {len(val_dataset)} 样本")
    print(f"   测试集: {len(test_dataset)} 样本")
    
    # 创建模型
    model = FixedMultiModalPointNet(num_points=50000, num_keypoints=57)
    print(f"🤖 模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练模型
    history = train_unified_57_model(model, train_loader, val_loader, epochs=40, device=device)
    
    # 评估模型
    results = evaluate_unified_57_model(model, test_loader, device=device)
    
    # 保存结果
    results_summary = {
        'avg_error': results['avg_error'],
        'accuracy_5mm': results['accuracy_5mm'],
        'accuracy_10mm': results['accuracy_10mm'],
        'region_errors': {k: float(np.mean(v)) for k, v in results['region_errors'].items()},
        'training_history': history,
        'dataset_info': {
            'total_samples': len(point_clouds),
            'train_samples': len(train_dataset),
            'val_samples': len(val_dataset),
            'test_samples': len(test_dataset),
            'data_source': 'unified_pelvis_f3_centered',
            'coordinate_system': 'f3_centered',
            'surface_alignment': '1.31mm_average'
        }
    }
    
    with open('unified_57_model_results.json', 'w') as f:
        json.dump(results_summary, f, indent=2)
    
    print(f"\n🎉 统一57点模型训练完成！")
    print(f"📋 生成的文件:")
    print(f"   - best_unified_57_model.pth (最佳模型)")
    print(f"   - unified_57_model_results.json (评估结果)")
    
    print(f"\n🎯 统一57点模型性能:")
    print(f"   平均误差: {results['avg_error']:.2f}mm")
    print(f"   医疗级准确率 (<5mm): {results['accuracy_5mm']:.1f}%")
    
    print(f"\n📊 性能对比:")
    print(f"   12点模型: ~5.64mm")
    print(f"   插值57点模型: 12.72mm")
    print(f"   错误57点模型: 46.16mm")
    print(f"   统一57点模型: {results['avg_error']:.2f}mm")
    
    if results['avg_error'] < 8.0:
        print(f"✅ 统一57点模型性能优秀！")
        print(f"💡 证明了用户洞察的正确性")
        print(f"🎯 F3中心对齐是关键因素")
    else:
        print(f"⚠️ 性能仍需优化，但应该比之前的方法好")

if __name__ == "__main__":
    main()
