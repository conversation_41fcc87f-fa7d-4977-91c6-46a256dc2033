#!/usr/bin/env python3
"""
保存最佳12关键点配置和成功经验
记录突破性结果：6.208mm验证误差
"""

import json
import shutil
import os
from datetime import datetime

def save_best_configuration():
    """保存最佳12关键点配置"""
    
    print("💾 **保存最佳12关键点配置**")
    print("=" * 60)
    
    # 最佳配置信息
    best_config = {
        "method_name": "12关键点稳定性选择策略",
        "breakthrough_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "performance": {
            "validation_error": 6.208,  # mm
            "improvement_vs_baseline": 27.4,  # %
            "improvement_vs_conservative": 18.6,  # %
            "training_time_minutes": 1.1,
            "epochs_to_convergence": 88,
            "precision_5mm": 30.0,  # %
            "precision_7mm": 80.0   # %
        },
        "dataset_config": {
            "original_keypoints": 19,
            "selected_keypoints": 12,
            "reduction_percentage": 37,
            "selected_indices": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17],
            "selection_strategy": "stability_based",
            "dataset_file": "f3_reduced_12kp_stable.npz"
        },
        "model_config": {
            "architecture": "AdaptivePointNet",
            "parameters": 1486692,
            "input_points": 4096,
            "output_keypoints": 12,
            "output_dimensions": 3
        },
        "training_config": {
            "optimizer": "AdamW",
            "learning_rate": 0.0008,
            "weight_decay": 1e-4,
            "batch_size": 4,
            "max_epochs": 150,
            "early_stopping_patience": 20,
            "min_improvement": 0.005,
            "lr_scheduler": "ReduceLROnPlateau",
            "lr_factor": 0.7,
            "lr_patience": 12,
            "loss_function": "ImprovedLoss(alpha=0.8, beta=0.2)",
            "data_augmentation": True,
            "device": "cuda:1"
        },
        "key_insights": [
            "减少关键点数量反而提升性能",
            "稳定性选择比随机选择更有效",
            "12个关键点是最优平衡点",
            "训练效率大幅提升",
            "避免了信息冗余和噪声干扰"
        ],
        "selected_keypoints_analysis": {
            "keypoint_2": {"stability": 0.847, "position": [43.60, -26.70, -18.24], "region": "右侧"},
            "keypoint_3": {"stability": 0.835, "position": [-43.32, -26.29, -19.32], "region": "左侧"},
            "keypoint_4": {"stability": 0.592, "position": [21.84, -5.00, -3.96], "region": "右中"},
            "keypoint_5": {"stability": 0.782, "position": [19.85, 17.45, 8.65], "region": "右上"},
            "keypoint_6": {"stability": 0.855, "position": [18.07, 31.24, 21.98], "region": "右上"},
            "keypoint_7": {"stability": 0.867, "position": [16.89, 38.01, 38.30], "region": "右顶"},
            "keypoint_8": {"stability": 0.597, "position": [-21.71, -5.18, -4.11], "region": "左中"},
            "keypoint_9": {"stability": 0.780, "position": [-20.01, 17.45, 8.32], "region": "左上"},
            "keypoint_10": {"stability": 0.863, "position": [-18.96, 31.02, 22.02], "region": "左上"},
            "keypoint_11": {"stability": 0.869, "position": [-18.19, 38.06, 38.12], "region": "左顶"},
            "keypoint_16": {"stability": 0.805, "position": [-20.70, -15.81, -40.37], "region": "左下"},
            "keypoint_17": {"stability": 0.809, "position": [21.54, -15.65, -40.84], "region": "右下"}
        },
        "comparison_with_other_methods": {
            "random_sampling_19kp": {"error": 8.543, "improvement": "+27.4%"},
            "weight_focused_19kp": {"error": 8.038, "improvement": "+22.8%"},
            "knn_focused_19kp": {"error": 8.467, "improvement": "+26.7%"},
            "optimized_knn_19kp": {"error": 7.474, "improvement": "+16.9%"},
            "conservative_baseline_19kp": {"error": 7.631, "improvement": "+18.6%"}
        }
    }
    
    # 保存配置文件
    with open('best_12kp_config.json', 'w', encoding='utf-8') as f:
        json.dump(best_config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 最佳配置已保存: best_12kp_config.json")
    
    # 备份最佳模型
    if os.path.exists('best_reduced_12kp_f3.pth'):
        backup_name = f"best_12kp_model_6.208mm_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pth"
        shutil.copy2('best_reduced_12kp_f3.pth', backup_name)
        print(f"✅ 最佳模型已备份: {backup_name}")
    
    # 备份数据集
    if os.path.exists('f3_reduced_12kp_stable.npz'):
        backup_dataset = f"f3_reduced_12kp_stable_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.npz"
        shutil.copy2('f3_reduced_12kp_stable.npz', backup_dataset)
        print(f"✅ 数据集已备份: {backup_dataset}")
    
    return best_config

def analyze_current_architecture():
    """分析当前架构的特点和改进空间"""
    
    print(f"\n🔍 **当前架构分析**")
    print("=" * 60)
    
    current_architecture = {
        "model_type": "AdaptivePointNet",
        "architecture_details": {
            "feature_extraction": {
                "conv_layers": [
                    "Conv1d(3→64) + BatchNorm + ReLU",
                    "Conv1d(64→128) + BatchNorm + ReLU", 
                    "Conv1d(128→256) + BatchNorm + ReLU",
                    "Conv1d(256→512) + BatchNorm + ReLU",
                    "Conv1d(512→1024) + BatchNorm + ReLU"
                ],
                "residual_connections": [
                    "Conv1d(64→256) 残差连接",
                    "Conv1d(128→512) 残差连接"
                ],
                "global_pooling": "Max Pooling (1024维全局特征)"
            },
            "regression_head": {
                "fc_layers": [
                    "Linear(1024→512) + BatchNorm + ReLU + Dropout(0.3)",
                    "Linear(512→256) + BatchNorm + ReLU + Dropout(0.3)",
                    "Linear(256→128) + BatchNorm + ReLU + Dropout(0.3)",
                    "Linear(128→64) + BatchNorm + ReLU + Dropout(0.3)",
                    "Linear(64→36)  # 12关键点 × 3维"
                ]
            }
        },
        "strengths": [
            "简单有效的基础架构",
            "残差连接帮助梯度传播",
            "BatchNorm提供训练稳定性",
            "Dropout防止过拟合",
            "Max Pooling提取全局特征"
        ],
        "limitations": [
            "单一全局特征，缺乏局部细节",
            "没有注意力机制",
            "没有多尺度特征融合",
            "回归头相对简单",
            "缺乏几何约束",
            "没有利用关键点间的空间关系"
        ],
        "improvement_opportunities": [
            "添加注意力机制",
            "多尺度特征提取",
            "图神经网络建模关键点关系",
            "改进损失函数",
            "几何约束和正则化",
            "特征金字塔网络",
            "自适应池化策略"
        ]
    }
    
    print(f"📊 **当前架构特点**:")
    print(f"   模型类型: {current_architecture['model_type']}")
    print(f"   参数量: 1,486,692")
    print(f"   输入: 4096个点 × 3维")
    print(f"   输出: 12个关键点 × 3维")
    
    print(f"\n✅ **架构优势**:")
    for strength in current_architecture['strengths']:
        print(f"   • {strength}")
    
    print(f"\n⚠️  **改进空间**:")
    for limitation in current_architecture['limitations']:
        print(f"   • {limitation}")
    
    print(f"\n🚀 **改进机会**:")
    for opportunity in current_architecture['improvement_opportunities']:
        print(f"   • {opportunity}")
    
    return current_architecture

def suggest_architecture_improvements():
    """建议具体的架构改进方案"""
    
    print(f"\n🚀 **架构改进建议**")
    print("=" * 60)
    
    improvements = [
        {
            "name": "注意力增强PointNet",
            "priority": "高",
            "description": "添加自注意力机制，让模型关注重要的点",
            "expected_improvement": "5-10%",
            "implementation_complexity": "中等",
            "key_features": [
                "Self-Attention层",
                "Channel Attention",
                "Spatial Attention",
                "多头注意力机制"
            ]
        },
        {
            "name": "多尺度特征金字塔",
            "priority": "高", 
            "description": "提取不同尺度的特征，融合局部和全局信息",
            "expected_improvement": "8-15%",
            "implementation_complexity": "中等",
            "key_features": [
                "Feature Pyramid Network",
                "多尺度卷积",
                "特征融合模块",
                "自适应池化"
            ]
        },
        {
            "name": "图神经网络关键点建模",
            "priority": "中",
            "description": "建模关键点间的空间关系和解剖约束",
            "expected_improvement": "10-20%",
            "implementation_complexity": "高",
            "key_features": [
                "Graph Convolutional Network",
                "关键点邻接矩阵",
                "空间关系建模",
                "解剖约束"
            ]
        },
        {
            "name": "改进损失函数",
            "priority": "高",
            "description": "设计更适合医疗关键点检测的损失函数",
            "expected_improvement": "3-8%",
            "implementation_complexity": "低",
            "key_features": [
                "Focal Loss处理难样本",
                "Wing Loss处理小误差",
                "几何一致性损失",
                "关键点间距离约束"
            ]
        },
        {
            "name": "数据增强优化",
            "priority": "中",
            "description": "设计更适合医疗数据的增强策略",
            "expected_improvement": "5-10%",
            "implementation_complexity": "低",
            "key_features": [
                "医疗数据特定增强",
                "解剖学约束增强",
                "自适应增强强度",
                "混合增强策略"
            ]
        }
    ]
    
    print(f"📋 **改进方案优先级排序**:")
    print()
    
    for i, improvement in enumerate(improvements, 1):
        print(f"🎯 **方案{i}: {improvement['name']}**")
        print(f"   优先级: {improvement['priority']}")
        print(f"   预期改进: {improvement['expected_improvement']}")
        print(f"   实现复杂度: {improvement['implementation_complexity']}")
        print(f"   描述: {improvement['description']}")
        print(f"   关键特性:")
        for feature in improvement['key_features']:
            print(f"     • {feature}")
        print()
    
    return improvements

def create_implementation_roadmap():
    """创建实现路线图"""
    
    print(f"🗺️  **实现路线图**")
    print("=" * 60)
    
    roadmap = {
        "phase_1": {
            "name": "快速改进阶段 (1-2天)",
            "goal": "在现有基础上快速提升",
            "target_improvement": "5-10%",
            "tasks": [
                "改进损失函数 (Wing Loss + Focal Loss)",
                "优化数据增强策略",
                "调整训练超参数",
                "添加简单的注意力机制"
            ]
        },
        "phase_2": {
            "name": "架构升级阶段 (3-5天)",
            "goal": "显著提升模型能力",
            "target_improvement": "10-20%",
            "tasks": [
                "实现多尺度特征金字塔",
                "添加完整的注意力机制",
                "改进回归头设计",
                "实现特征融合模块"
            ]
        },
        "phase_3": {
            "name": "高级优化阶段 (5-7天)",
            "goal": "突破医疗级精度",
            "target_improvement": "15-25%",
            "tasks": [
                "实现图神经网络建模",
                "添加几何约束",
                "实现自适应训练策略",
                "集成多模型ensemble"
            ]
        }
    }
    
    for phase_key, phase in roadmap.items():
        print(f"📅 **{phase['name']}**")
        print(f"   目标: {phase['goal']}")
        print(f"   预期改进: {phase['target_improvement']}")
        print(f"   任务清单:")
        for task in phase['tasks']:
            print(f"     ✓ {task}")
        print()
    
    print(f"🎯 **总体目标**: 从当前6.208mm提升到5mm以下医疗级精度")
    print(f"📈 **预期总改进**: 20-30%")
    print(f"⏱️  **预计时间**: 7-14天")
    
    return roadmap

def main():
    """主函数"""
    
    print("💾 **保存最佳12关键点配置并规划架构改进**")
    print("🎯 **当前成果**: 6.208mm验证误差 (新纪录)")
    print("🚀 **目标**: 基于成功配置继续改进，突破5mm医疗级精度")
    print("=" * 80)
    
    # 保存最佳配置
    best_config = save_best_configuration()
    
    # 分析当前架构
    current_arch = analyze_current_architecture()
    
    # 建议改进方案
    improvements = suggest_architecture_improvements()
    
    # 创建实现路线图
    roadmap = create_implementation_roadmap()
    
    # 保存完整分析
    complete_analysis = {
        "best_configuration": best_config,
        "current_architecture": current_arch,
        "improvement_suggestions": improvements,
        "implementation_roadmap": roadmap,
        "next_steps": [
            "立即开始Phase 1: 改进损失函数",
            "实现Wing Loss + Focal Loss组合",
            "优化数据增强策略",
            "添加简单注意力机制",
            "目标: 在2天内突破6mm"
        ]
    }
    
    with open('architecture_improvement_plan.json', 'w', encoding='utf-8') as f:
        json.dump(complete_analysis, f, indent=2, ensure_ascii=False)
    
    print(f"💾 **完整改进计划已保存**: architecture_improvement_plan.json")
    
    print(f"\n🎯 **立即行动建议**:")
    print(f"1. 🚀 **Phase 1优先**: 改进损失函数 (最快见效)")
    print(f"2. 🎯 **目标明确**: 2天内从6.208mm突破到6mm以下")
    print(f"3. 📈 **渐进改进**: 每个阶段验证效果再进入下一阶段")
    print(f"4. 💾 **保持备份**: 每次改进都保存成功配置")
    
    return complete_analysis

if __name__ == "__main__":
    results = main()
