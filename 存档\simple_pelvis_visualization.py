#!/usr/bin/env python3
"""
简化的骨盆预测可视化
使用对齐数据集，显示预测点和标注点的对比，用线连接显示距离
"""

import torch
import numpy as np
import h5py
from pathlib import Path
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

from train_improved_model import ImprovedMedicalPointNet

class SimplePelvisVisualizer:
    """简化骨盆可视化器"""
    
    def __init__(self, model_path="output/improved_model_training/best_improved_model.pth"):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model_path = Path(model_path)
        self.aligned_data_root = Path("MedicalAlignedDataset")
        self.output_dir = Path("output/simple_pelvis_visualization")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🦴 简化骨盆可视化器初始化")
        
        # 加载模型
        self.load_model()
        
        # 获取数据
        self.get_data()
        
        print(f"✅ 可视化器准备完成")
    
    def load_model(self):
        """加载改进的模型"""
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
            self.model = ImprovedMedicalPointNet(num_keypoints=57)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            print(f"✅ 改进模型加载成功")
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self.model = None
    
    def get_data(self):
        """获取数据"""
        aligned_data_dir = self.aligned_data_root / "aligned_data"
        if aligned_data_dir.exists():
            h5_files = list(aligned_data_dir.glob("*.h5"))
            self.patient_ids = [f.stem for f in h5_files]
            print(f"📊 找到 {len(self.patient_ids)} 个患者")
        else:
            print(f"❌ 对齐数据目录不存在")
            self.patient_ids = []
    
    def load_patient_data(self, patient_id):
        """加载患者数据"""
        aligned_file = self.aligned_data_root / "aligned_data" / f"{patient_id}.h5"
        if not aligned_file.exists():
            return None
        
        try:
            with h5py.File(aligned_file, 'r') as f:
                point_cloud = f['point_cloud'][:]
                keypoints = f['keypoints'][:]
            
            return {
                'point_cloud': point_cloud,
                'keypoints': keypoints,
                'patient_id': patient_id
            }
        except Exception as e:
            print(f"❌ 加载患者 {patient_id} 数据失败: {e}")
            return None
    
    def normalize_keypoints_to_pointcloud(self, keypoints, point_cloud):
        """将关键点归一化到点云坐标系"""
        pc_min, pc_max = point_cloud.min(axis=0), point_cloud.max(axis=0)
        pc_center = (pc_min + pc_max) / 2
        pc_range = pc_max - pc_min
        
        kp_min, kp_max = keypoints.min(axis=0), keypoints.max(axis=0)
        kp_center = (kp_min + kp_max) / 2
        kp_range = kp_max - kp_min
        
        # 智能缩放和平移
        scale_factors = []
        for i in range(3):
            if kp_range[i] > 0:
                target_range = pc_range[i] * 0.8
                scale_factor = target_range / kp_range[i]
            else:
                scale_factor = 1.0
            scale_factors.append(scale_factor)
        
        scale_factors = np.array(scale_factors)
        normalized_keypoints = (keypoints - kp_center) * scale_factors + pc_center
        
        return normalized_keypoints
    
    def predict_keypoints(self, point_cloud):
        """预测关键点"""
        if self.model is None:
            return None
            
        with torch.no_grad():
            point_cloud_tensor = torch.from_numpy(point_cloud.T).unsqueeze(0).to(self.device)
            pred_keypoints, _, _ = self.model(point_cloud_tensor)
            pred_keypoints = pred_keypoints.cpu().numpy()[0]
            return pred_keypoints
    
    def create_enhanced_visualization(self, patient_data):
        """创建增强的可视化"""
        patient_id = patient_data['patient_id']
        point_cloud = patient_data['point_cloud']
        original_keypoints = patient_data['keypoints']
        
        print(f"\n🦴 可视化患者: {patient_id}")
        
        # 归一化关键点
        normalized_gt = self.normalize_keypoints_to_pointcloud(original_keypoints, point_cloud)
        
        # 预测关键点
        pred_keypoints = self.predict_keypoints(point_cloud)
        if pred_keypoints is None:
            return None
        
        # 计算误差
        errors = np.linalg.norm(pred_keypoints - normalized_gt, axis=1)
        mean_error = np.mean(errors)
        max_error = np.max(errors)
        acc_5mm = np.mean(errors <= 5.0) * 100
        acc_3mm = np.mean(errors <= 3.0) * 100
        
        print(f"   📊 预测性能:")
        print(f"      平均误差: {mean_error:.2f}mm")
        print(f"      最大误差: {max_error:.2f}mm")
        print(f"      5mm准确率: {acc_5mm:.1f}%")
        print(f"      3mm准确率: {acc_3mm:.1f}%")
        
        # 创建可视化
        self.plot_enhanced_comparison(patient_id, point_cloud, normalized_gt, pred_keypoints, errors)
        
        return {
            'patient_id': patient_id,
            'mean_error': mean_error,
            'max_error': max_error,
            'acc_5mm': acc_5mm,
            'acc_3mm': acc_3mm
        }
    
    def plot_enhanced_comparison(self, patient_id, point_cloud, gt_keypoints, pred_keypoints, errors):
        """绘制增强的对比图"""
        
        fig = plt.figure(figsize=(20, 15))
        
        # 1. 主要3D视图 - 完整对比
        ax1 = fig.add_subplot(2, 3, 1, projection='3d')
        
        # 点云背景
        ax1.scatter(point_cloud[:, 0], point_cloud[:, 1], point_cloud[:, 2], 
                   c='lightgray', alpha=0.2, s=1, label='Point Cloud')
        
        # 真实关键点
        ax1.scatter(gt_keypoints[:, 0], gt_keypoints[:, 1], gt_keypoints[:, 2], 
                   c='green', s=50, alpha=0.8, marker='o', label='Ground Truth', edgecolors='darkgreen')
        
        # 预测关键点
        ax1.scatter(pred_keypoints[:, 0], pred_keypoints[:, 1], pred_keypoints[:, 2], 
                   c='red', s=40, alpha=0.8, marker='^', label='Prediction', edgecolors='darkred')
        
        # 连接线 - 根据误差着色
        for i in range(len(gt_keypoints)):
            color = 'red' if errors[i] > 5 else 'orange' if errors[i] > 3 else 'green'
            alpha = 0.8 if errors[i] > 3 else 0.4
            linewidth = 2 if errors[i] > 5 else 1
            
            ax1.plot([gt_keypoints[i, 0], pred_keypoints[i, 0]], 
                    [gt_keypoints[i, 1], pred_keypoints[i, 1]], 
                    [gt_keypoints[i, 2], pred_keypoints[i, 2]], 
                    color=color, alpha=alpha, linewidth=linewidth)
        
        ax1.set_title(f'{patient_id} - Complete Comparison\nMean Error: {np.mean(errors):.2f}mm')
        ax1.legend()
        ax1.set_xlabel('X (mm)')
        ax1.set_ylabel('Y (mm)')
        ax1.set_zlabel('Z (mm)')
        
        # 2. 误差热图视图
        ax2 = fig.add_subplot(2, 3, 2, projection='3d')
        
        # 点云背景（更透明）
        ax2.scatter(point_cloud[:, 0], point_cloud[:, 1], point_cloud[:, 2], 
                   c='lightgray', alpha=0.1, s=1)
        
        # 根据误差着色的预测点
        scatter = ax2.scatter(pred_keypoints[:, 0], pred_keypoints[:, 1], pred_keypoints[:, 2], 
                            c=errors, s=60, alpha=0.8, cmap='RdYlGn_r', 
                            vmin=0, vmax=max(5, np.max(errors)), edgecolors='black')
        
        # 只显示大误差的连接线
        large_error_mask = errors > 3.0
        for i in np.where(large_error_mask)[0]:
            ax2.plot([gt_keypoints[i, 0], pred_keypoints[i, 0]], 
                    [gt_keypoints[i, 1], pred_keypoints[i, 1]], 
                    [gt_keypoints[i, 2], pred_keypoints[i, 2]], 
                    'red', alpha=0.8, linewidth=2)
            
            # 标注大误差点
            ax2.text(pred_keypoints[i, 0], pred_keypoints[i, 1], pred_keypoints[i, 2], 
                    f'{i}\n{errors[i]:.1f}mm', fontsize=8, color='red')
        
        plt.colorbar(scatter, ax=ax2, shrink=0.5, aspect=20, label='Error (mm)')
        ax2.set_title(f'Error Heatmap (>3mm errors highlighted)')
        ax2.set_xlabel('X (mm)')
        ax2.set_ylabel('Y (mm)')
        ax2.set_zlabel('Z (mm)')
        
        # 3. XY平面投影 - 详细视图
        ax3 = fig.add_subplot(2, 3, 3)
        
        # 点云背景
        ax3.scatter(point_cloud[:, 0], point_cloud[:, 1], 
                   c='lightgray', alpha=0.2, s=1)
        
        # 关键点
        ax3.scatter(gt_keypoints[:, 0], gt_keypoints[:, 1], 
                   c='green', s=50, alpha=0.8, marker='o', label='GT', edgecolors='darkgreen')
        ax3.scatter(pred_keypoints[:, 0], pred_keypoints[:, 1], 
                   c='red', s=40, alpha=0.8, marker='^', label='Pred', edgecolors='darkred')
        
        # 连接线
        for i in range(len(gt_keypoints)):
            color = 'red' if errors[i] > 5 else 'orange' if errors[i] > 3 else 'green'
            alpha = 0.8 if errors[i] > 3 else 0.4
            linewidth = 2 if errors[i] > 5 else 1
            
            ax3.plot([gt_keypoints[i, 0], pred_keypoints[i, 0]], 
                    [gt_keypoints[i, 1], pred_keypoints[i, 1]], 
                    color=color, alpha=alpha, linewidth=linewidth)
        
        # 标注最大误差点
        max_error_idx = np.argmax(errors)
        ax3.annotate(f'Max Error\nPoint {max_error_idx}: {errors[max_error_idx]:.2f}mm', 
                    xy=(pred_keypoints[max_error_idx, 0], pred_keypoints[max_error_idx, 1]), 
                    xytext=(pred_keypoints[max_error_idx, 0] + 5, pred_keypoints[max_error_idx, 1] + 5),
                    arrowprops=dict(arrowstyle='->', color='red', lw=2),
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
        
        ax3.set_title('XY Plane - Detailed View')
        ax3.set_xlabel('X (mm)')
        ax3.set_ylabel('Y (mm)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.axis('equal')
        
        # 4. 误差分布条形图
        ax4 = fig.add_subplot(2, 3, 4)
        
        x = np.arange(len(errors))
        colors = ['darkred' if e > 5 else 'orange' if e > 3 else 'green' for e in errors]
        bars = ax4.bar(x, errors, color=colors, alpha=0.7, edgecolor='black', linewidth=0.5)
        
        # 标注前5个最大误差
        top5_indices = np.argsort(errors)[-5:]
        for idx in top5_indices:
            ax4.annotate(f'{idx}\n{errors[idx]:.1f}mm', 
                        xy=(idx, errors[idx]), 
                        xytext=(idx, errors[idx] + 0.5),
                        ha='center', fontsize=8,
                        arrowprops=dict(arrowstyle='->', color='red', lw=1))
        
        ax4.axhline(y=5.0, color='red', linestyle='--', alpha=0.7, label='5mm threshold')
        ax4.axhline(y=3.0, color='orange', linestyle='--', alpha=0.7, label='3mm threshold')
        ax4.axhline(y=np.mean(errors), color='blue', linestyle='-', alpha=0.7, 
                   label=f'Mean: {np.mean(errors):.2f}mm')
        
        ax4.set_xlabel('Keypoint Index')
        ax4.set_ylabel('Error (mm)')
        ax4.set_title('Per-Keypoint Error (Top 5 labeled)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        # 5. 误差分布直方图
        ax5 = fig.add_subplot(2, 3, 5)
        
        n, bins, patches = ax5.hist(errors, bins=20, alpha=0.7, color='skyblue', 
                                   edgecolor='black', density=True)
        
        # 根据误差范围着色
        for i, patch in enumerate(patches):
            bin_center = (bins[i] + bins[i+1]) / 2
            if bin_center > 5:
                patch.set_facecolor('red')
            elif bin_center > 3:
                patch.set_facecolor('orange')
            else:
                patch.set_facecolor('green')
        
        ax5.axvline(x=3.0, color='orange', linestyle='--', alpha=0.7, label='3mm')
        ax5.axvline(x=5.0, color='red', linestyle='--', alpha=0.7, label='5mm')
        ax5.axvline(x=np.mean(errors), color='blue', linestyle='-', alpha=0.7, 
                   label=f'Mean: {np.mean(errors):.2f}mm')
        
        ax5.set_xlabel('Error (mm)')
        ax5.set_ylabel('Density')
        ax5.set_title('Error Distribution')
        ax5.legend()
        ax5.grid(True, alpha=0.3)
        
        # 6. 详细统计信息
        ax6 = fig.add_subplot(2, 3, 6)
        ax6.axis('off')
        
        # 计算详细统计
        errors_under_1mm = np.sum(errors <= 1.0)
        errors_1_2mm = np.sum((errors > 1.0) & (errors <= 2.0))
        errors_2_3mm = np.sum((errors > 2.0) & (errors <= 3.0))
        errors_3_5mm = np.sum((errors > 3.0) & (errors <= 5.0))
        errors_over_5mm = np.sum(errors > 5.0)
        
        # 找出最大误差的关键点
        worst_points = np.argsort(errors)[-3:]
        
        stats_text = f"""Detailed Performance Analysis

Patient ID: {patient_id}

Error Statistics:
• Mean Error: {np.mean(errors):.2f} ± {np.std(errors):.2f}mm
• Median Error: {np.median(errors):.2f}mm
• Max Error: {np.max(errors):.2f}mm (Point {np.argmax(errors)})
• Min Error: {np.min(errors):.2f}mm (Point {np.argmin(errors)})

Error Distribution:
• Excellent (≤1mm): {errors_under_1mm}/57 ({errors_under_1mm/57*100:.1f}%)
• Very Good (1-2mm): {errors_1_2mm}/57 ({errors_1_2mm/57*100:.1f}%)
• Good (2-3mm): {errors_2_3mm}/57 ({errors_2_3mm/57*100:.1f}%)
• Acceptable (3-5mm): {errors_3_5mm}/57 ({errors_3_5mm/57*100:.1f}%)
• Poor (>5mm): {errors_over_5mm}/57 ({errors_over_5mm/57*100:.1f}%)

Accuracy Metrics:
• 1mm Accuracy: {np.mean(errors <= 1.0)*100:.1f}%
• 2mm Accuracy: {np.mean(errors <= 2.0)*100:.1f}%
• 3mm Accuracy: {np.mean(errors <= 3.0)*100:.1f}%
• 5mm Accuracy: {np.mean(errors <= 5.0)*100:.1f}%

Worst Performing Points:
• Point {worst_points[-1]}: {errors[worst_points[-1]]:.2f}mm
• Point {worst_points[-2]}: {errors[worst_points[-2]]:.2f}mm
• Point {worst_points[-3]}: {errors[worst_points[-3]]:.2f}mm

Data Info:
• Point Cloud Size: {len(point_cloud)} points
• Coordinate Range: 
  X: [{point_cloud[:, 0].min():.1f}, {point_cloud[:, 0].max():.1f}]mm
  Y: [{point_cloud[:, 1].min():.1f}, {point_cloud[:, 1].max():.1f}]mm
  Z: [{point_cloud[:, 2].min():.1f}, {point_cloud[:, 2].max():.1f}]mm
"""
        
        ax6.text(0.05, 0.95, stats_text, transform=ax6.transAxes, fontsize=9,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightcyan', alpha=0.8))
        
        plt.tight_layout()
        
        # 保存可视化
        plot_path = self.output_dir / f"enhanced_pelvis_{patient_id}.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"💾 增强骨盆可视化已保存: {plot_path}")
    
    def visualize_multiple_patients(self, max_patients=3):
        """可视化多个患者"""
        print(f"🦴 开始简化骨盆可视化...")
        
        if not self.patient_ids:
            print(f"❌ 没有可用的患者数据")
            return
        
        results = []
        
        for i, patient_id in enumerate(self.patient_ids[:max_patients]):
            try:
                patient_data = self.load_patient_data(patient_id)
                if patient_data is None:
                    continue
                
                result = self.create_enhanced_visualization(patient_data)
                if result:
                    results.append(result)
                    
            except Exception as e:
                print(f"❌ 处理患者 {patient_id} 时出错: {e}")
                continue
        
        # 总结结果
        if results:
            print(f"\n🎯 简化骨盆可视化总结:")
            mean_errors = [r['mean_error'] for r in results]
            acc_5mm = [r['acc_5mm'] for r in results]
            acc_3mm = [r['acc_3mm'] for r in results]
            
            print(f"   平均误差: {np.mean(mean_errors):.2f}±{np.std(mean_errors):.2f}mm")
            print(f"   5mm准确率: {np.mean(acc_5mm):.1f}±{np.std(acc_5mm):.1f}%")
            print(f"   3mm准确率: {np.mean(acc_3mm):.1f}±{np.std(acc_3mm):.1f}%")
        
        return results

def main():
    """主函数"""
    print("🦴 启动简化骨盆预测可视化...")
    
    visualizer = SimplePelvisVisualizer()
    visualizer.visualize_multiple_patients(max_patients=3)

if __name__ == "__main__":
    main()
