Engineering Applications of Artificial Intelligence 151 (2025) 110668








Research paper
Paying attention to the minute details: Supervised keypoint detection on dense, complex point clouds
<PERSON><PERSON><PERSON> a, <PERSON><PERSON><PERSON> a ,<PERSON>, <PERSON><PERSON> a, <PERSON><PERSON><PERSON> a , <PERSON><PERSON> b
a School of Computer Science and Engineering, Central South University, Changsha, 410083, Hunan, China
b The Third Xiangya Hospital, Central South University, Changsha, 410083, Hunan, China


A R T I C L E  I N F O	A B S T R A C T

 	 
 
Keywords:
Deep learning Point cloud
Supervised three-dimensional keypoint detection
Three-dimensional landmark detection Medical image process
 
Keypoints in three-dimensional (3D) objects are crucial for applications such as shape description, registration, and clinical diagnosis. However, current keypoint detection methods for 3D objects often depend on multiview two-dimensional (2D) images or unsupervised learning, largely due to the limited availability of annotated datasets. This limitation restricts their ability to detect anatomically significant keypoints directly on 3D models. To overcome this challenge, we propose a supervised keypoint detection framework designed for dense and complex 3D point clouds. Our framework combines a specialized segmentation network to identify potential keypoint regions, followed by precise keypoint localization using a PointNet encoder with residual modules and a novel ‘‘double SoftMax’’ mechanism. While keypoint detection accuracy benefits from restricting potential regions to smaller areas, this can leave some regions undetected in dense point clouds. To address this, we introduce a ‘‘Penalty Dice Loss’’ into the segmentation network, which effectively reduces undetected regions. Experiments on in-house skull and tibia datasets show mean radial errors of 1.43 mm and 1.54 mm, and success detection rates of 76.00% and 76.40%, respectively, within the clinically accepted 2 mm range. These results are competitive with state-of-the-art 2D cephalometric X-ray landmark detection methods and demonstrate the potential of our framework for clinical applications, such as preoperative planning, intraoperative navigation, and postoperative evaluation.
 

 

 
1.	Introduction

Keypoints are typically positioned at locations with prominent geo- metric features on three-dimensional (3D) objects, making them highly valuable in downstream tasks such as shape description and object registration. In medical applications, keypoint detection is critical for various clinical scenarios. For example, esthetic reference landmarks are commonly used in plastic surgery to establish standardized esthetic guidelines. One such landmark is the gonion, located at the intersection of the lower border of the mandible and the posterior border of the ramus. This keypoint is essential for facial contouring, and is used to determine the mandibular angle, which ideally ranges from 125◦ to 135◦ in adult males and is typically softer and more rounded in females. Another example is pedicle screw implantation, a procedure frequently employed in spinal fusion and stability reconstruction surgeries. This precise operation relies on accurate navigation and localization, par- ticularly when detecting keypoints on the vertebral pedicle to ensure screw placement while avoiding damage to critical structures such as nerves and blood vessels. Currently, most 3D keypoint detection
 
algorithms rely on multiview two-dimensional (2D) images, which introduce several challenges: increased manual annotation workload, difficulty in labeling overlapping positions in 3D space, and errors arising from inconsistencies in manual annotation across views. Direct keypoint detection on 3D models would significantly reduce these chal- lenges, cutting labor costs and improving efficiency. Most 3D detection methods use point clouds as the primary data format, which include 3D coordinates, normal vectors and color information. In medical applications, point cloud models are typically generated by extracting point data from mesh models reconstructed from CT scans. However, the irregular and sparse structure of point clouds represents challenges for processing with standard convolutional kernels. To address this, VoxelNet (Zhou and Tuzel, 2018) proposed partitioning 3D space into grids and applying 3D convolutional kernels to extract point features within each grid. However, 3D convolutional neural networks are computationally intensive and require significant memory. Dynamic graph convolutional neural network (Wang et al., 2019) constructs dynamic graph structures at each network layer to capture spatial
 

 
∗ Corresponding author.
E-mail addresses: <EMAIL> (Q. Chen), <EMAIL> (S. Liao), <EMAIL> (X. Kui), <EMAIL> (Z. Hu), <EMAIL> (J. Zhou).
https://doi.org/10.1016/j.engappai.2025.110668
Received 27 October 2024; Received in revised form 2 February 2025; Accepted 21 March 2025
Available online 3 April 2025
0952-1976/© 2025 Elsevier Ltd. All rights are reserved, including those for text and data mining, AI training, and similar technologies.
 

 
geometric information. Charles et al. introduced PointNet (Qi et al., 2017a) and PointNet++ (Qi et al., 2017b), which first select nodes using farthest point sampling (FPS), then aggregate point features extracted by multilayer perceptrons (MLPs) within each node’s neigh- borhood, and finally apply MaxPooling to select the feature vector that represents the region. Building on these architectures, relation-shape convolutional neural network (Liu et al., 2019) enhanced the model by learning the local implicit geometric representation of the point cloud using MLPs. PointTransformer (Zhao et al., 2021) and Point- Cloud Transformer (Guo et al., 2021) further improve performance by replacing traditional MLPs with self-attention mechanisms. Qian et al. (2022) advanced the model by incorporating residual connections and scaling the PointNet++ architecture. Park et al. (2023) simplified the transformer’s complexity by aggregating global information through self-positioning points generated in the input point cloud. A similar approach is used in SK-Net (Wu et al., 2020), which leverages keypoints to aggregate point cloud features.
While deep learning for point clouds has progressed rapidly, the scarcity of annotated data has led many current keypoint detection models to focus on unsupervised and self-supervised tasks. These de- tected keypoints are mainly used in downstream tasks such as feature description and registration. However, in clinical applications, there is often a need to detect specific keypoints with anatomical significance. Additionally, point cloud deep learning tasks such as shape recognition and part segmentation typically focus on datasets of numerous point clouds representing everyday objects, which are generally simpler in structure and lower in density. In contrast, medical point clouds are more complex, denser, and have fewer available samples. For example, a skull point cloud model reconstructed from CT data can contain hundreds of thousands of points. Even after downsampling to around 60,000 points while preserving the essential geometric structure, the model remains much larger and more intricate than those found in public datasets.
Our work proposes a ‘‘coarse-to-fine’’ two-stage supervised keypoint detection framework that directly identifies keypoints with specific semantic information in dense, complex point clouds. The framework is particularly focused on medical point clouds, where detecting anatom- ically significant keypoints is crucial. The main contributions of this work are as follows:
(1)	Novel neural network architecture for 3D keypoint de- tection: We introduce a neural network architecture designed for detecting keypoints with specific semantic meaning in dense, complex point cloud models. The method first identifies potential keypoint regions within the point clouds, then accurately locates the keypoints within these regions. This approach enables precise, one-to-one key- point detection directly on the point cloud, eliminating the need for additional 2D image annotations. Additionally, we demonstrate the ap- plication of this method for detecting anatomical significance keypoints in medical point cloud models.
(2)	Introduction of ‘‘Penalty Dice Loss’’: To address the challenge of missing keypoints due to undetected potential regions, we introduce a Penalty Dice Loss. This loss function specifically targets small yet crit- ical areas in dense point clouds, significantly improving the accuracy of keypoint detection.
(3)	Innovative keypoint localization mechanism: We combine a PointNet encoder with residual modules and a novel ‘‘double Soft- Max’’ (DS) weighting mechanism to compute weight matrices for the potential regions. These matrices are used to perform a weighted average of the coordinates of points within those regions, resulting in the final keypoint locations. This method substantially improves keypoint detection accuracy, producing results comparable to state- of-the-art 2D cephalometric X-ray landmark detection algorithms. The results from the skull and tibia datasets—both with complex topological information and distinct different geometric structures—demonstrate the broad applicability of our method to dense, complex point clouds and highlight its potential for practical clinical applications.
 
The remainder of this manuscript is organized as follows: Section 2 reviews existing methods for 3D keypoint detection and discusses their limitations. In Section 3, we present our proposed supervised deep learning framework, detailing its architecture and components. Sec- tion 4 provides experimental results and comparisons with existing approaches, followed by a discussion in Section 5, where we analyze the results and explore potential clinical applications. Finally, Sec- tion 6 concludes the manuscript by summarizing our contributions and suggesting directions for future research.

2.	Related work

2.1.	2D keypoint detection

Traditional keypoint detectors for 2D images typically identify edge and corner points based on the gradient of pixel intensity changes, as keypoints are often located in these areas (Harris et al., 1988; Lowe, 2004; Chenguang et al., 2009). Preprocessing techniques, such as image enhancement, are commonly applied to improve keypoint detection, particularly in data such as X-ray images (Nia and Shih, 2024).
In deep learning, neural network-based keypoint detectors for 2D images can be classified into two main types: (1) coordinate regression- based detectors (Sun et al., 2013; Zhang et al., 2014, 2016, 2017), which directly predict the keypoint coordinates from the input image, and (2) heatmap regression-based detectors (Kowalski et al., 2017; Wu et al., 2018; Zhu et al., 2021), which use both the image and heatmaps generated by the keypoints as inputs. The keypoint is then identified as the point with the highest value in the predicted heatmap.

2.2.	Keypoint detection on point clouds

Keypoint detection methods developed for 2D images can also be extended to point clouds. In point clouds, traditional keypoint detectors identify keypoints by calculating geometric features such as normal vectors and curvatures within a given region (Zhong, 2009; Knopp et al., 2010; Sipiran and Bustos, 2011). Many deep learning-based point cloud keypoint detectors use feature extraction mechanisms similar to those in PointNet. However, due to the scarcity of annotated keypoint data for point clouds, few studies have focused on supervised keypoint detection. Some methods attempt to detect keypoints using network architectures adapted from point cloud classification tasks (You et al., 2020; Cheng et al., 2021). These approaches have two main drawbacks:
(1) they can only classify whether a point is a keypoint, without assigning an independent semantic label to each keypoint, and (2) they fail to ensure that the number of predicted keypoints aligns with the ground truth. Other notable supervised methods include those by Yi et al. (2017), who used a spectral convolutional neural network to perform multiscale analysis on point clouds through coefficient shar- ing, generating 0–1 indicator vertex functions to determine keypoint locations. Sung et al. (2018) created a dictionary of semantic functions for each point category to capture the semantic structure between categories, which was then used to select keypoints.
Most keypoint detection models for point clouds focus on unsuper- vised or self-supervised tasks, selecting keypoints based on point cloud features without requiring manually annotated ground truth (Yew and Lee, 2018). These keypoints are primarily used for point cloud descrip- tion and registration. 3DSmoothNet (Gojcic et al., 2019) employs a fully convolutional Siamese network to obtain a rotation-invariant 3D feature descriptor for keypoint detection. Similarly, DH3D (Du et al., 2020) uses a Siamese network to simultaneously detect local features of the point cloud and train descriptors for these features, which are then used to identify keypoints. D3Feat (Bai et al., 2020) utilizes a 3D fully convolutional network to jointly learn local feature detectors and descriptors, predicting detection scores and feature descriptions for each point in the point cloud. USIP (Li and Lee, 2019) enhances
 

 

/ig. 1. Proposed framework for directly detecting keypoints with specific semantic labels on dense, complex point cloud models. (a) Cross-fusion of local features from different nodes across various network layers, which are then aggregated and concatenated into a global feature. (b) Concatenation and fusion of local features aggregated using the ball query method at different scales. (c) Keypoint detection within potential regions. T-Net is used for feature transformation, followed by dimensionality reduction via residual modules and weight matrix computation using the DS mechanism.

 
keypoint robustness across different rigid transformations by construct- ing a self-organizing map and incorporating a probabilistic Chamfer loss. UKPGAN (You et al., 2022) localizes important keypoints using a keypoint sparsity control and salient information distillation module based on a generative adversarial network. SNAKE (Zhong et al., 2022)
 
weighting the points inside them. In high-density point clouds, certain detailed information is often overlooked, but emphasizing these details can significantly improve keypoint detection accuracy.
The point cloud data, consisting of m samples, used as the training
set is denoted as {𝑃 𝐶𝑡}𝑚 , potential regions are denoted as {𝑅𝑖}𝑛  , the
 
𝑡=1
 
𝑛	𝑖=1
 
integrates surface reconstruction into 3D keypoint detection, maximiz- ing keypoint saliency. SC3K (Zohaib and Del Bue, 2023) computes semantically labeled keypoints by calculating the point cloud’s weight
 
center points of potential regions are denoted as {𝑠𝑝𝑖}𝑖=1, the weight matrix of points inside the potential region is denoted as 𝑊 𝑒𝑖𝑔ℎ𝑡𝑖, and
the final predicted keypoints are denoted as {𝑘𝑝𝑖}𝑛  , while the ground
 
truth is denoted as {𝑡𝑝 }𝑛
 
𝑖=1
 
matrix and uses multiple loss functions for self-supervised training to
ensure robustness.
Compared to the relatively idealized data objects targeted by most algorithms, real-world tasks often involve more complex data, requiring tailored and specialized solutions (Tourei et al., 2024a,b). This is particularly true in clinical medicine, where precision and stability are critical (Mehrnia et al., 2024). In such contexts, it is often necessary to design multistep frameworks to model specific task workflows (Tekin et al., 2024). Keypoint detection in clinical applications, typically a supervised task, involves identifying anatomically significant keypoints in complex and high-density medical point cloud models—an area that still requires more comprehensive research.

3.	Method

3.1.	Problem definition

Typical supervised keypoint detection on 3D objects often relies on multiview 2D keypoint detection for support. To minimize labeling effort and provide a more intuitive visualization of keypoint locations in 3D models, we aim to directly detect keypoints in dense, complex point clouds. We applied this method to detect anatomically significant keypoints in medical point clouds, supporting clinical tasks such as disease diagnosis and preoperative planning. Locating n keypoints with specific semantic information in dense point clouds presents challenges, as it can lead to mismatches between predicted and ground truth keypoints. A coarse-to-fine localization process helps mitigate this issue to some extent. To address this, we divide the task into two stages: (1)
 
 
𝑖 𝑖=1. The procedure is illustrated in Fig. 1.
3.2.	Keypoint-related potential region detection

In many unsupervised tasks, the selection of keypoint-related re- gions is based on the salient features of the point cloud (Zheng et al., 2019). However, in our task, the potential regions must directly corre- spond to the ground truth. Therefore, we define the potential regions by sampling k points from the keypoint’s neighborhood using K-nearest neighbors (KNN), denoted as {𝑝𝑖}𝑘 ∈ 𝑅. These potential regions function similarly to masks in segmentation tasks. Inspired by keypoint detection methods based on heatmap regression in 2D images, the de- tection model for potential regions can be trained using an architecture similar to point cloud part segmentation networks.

3.2.1.	Proposed network architecture
To more effectively analyze high-density point clouds, we developed a new point cloud part segmentation network, drawing inspiration from the architectures of HRNet (Sun et al., 2019) and Res2Net (Gao et al., 2019), as illustrated in Fig. 1(a) and (b). This network integrates features from point clouds at multiple scales, enabling the extraction of more comprehensive, multilevel point cloud features. The encoder selects nodes D from the point cloud using FPS, with features aggre- gated via the ball query strategy at varying scales {(𝑟𝑗 , 𝑛𝑗 ) 𝑗 ∈ 𝑁}. After BatchNormalization and Maxpooling, we obtain the local feature {ℎ𝑗 } from the aggregated features at different scales. These local features are then concatenated, fused through residual modules, and expressed as follows(j = 1, 2, 3):
⎪⎧ℎ2 = 𝛷(ℎ1 ⊕ ℎ2)
 

 	 
⎩	1	2	3
 

 
where 𝛷 represents a series of operations including MLPs, BatchNor- malization, and the ReLU activation function, while ⊕ denotes the concatenation operation.
Unlike PointNet++-like architectures (Qi et al., 2017b), our network cross-concatenates and fuses local features from different layers using residual connections, which can be expressed as 𝐻′ = 𝛷(𝐻1 ⊕⋯⊕𝐻𝑙 ⊕
⋯⊕𝐻𝑙+𝑎)+𝐻𝑙, 𝑎 ∈ 𝑁. Features from the upper layers are propagated to
the deeper layers, while features from the deeper layers are interpolated back to the upper layers, weighted by the Euclidean distances between nodes 𝐷𝑙 and their three nearest neighbors in 𝐷𝑙+1. The interpolation
can be expressed as follows:
⎧𝑊𝑙 = 𝑑𝑖𝑠𝑡(𝐷 ,𝐷  )2
 
Here, 𝑦𝑖 represents the ground truth label, while 𝑦̂𝑖 indicates the predicted probability of the point belonging to category i. The Penalty Dice Loss effectively prevents the occurrence of undetected regions while minimizing the value of k, thereby constricting the search space for subsequent keypoint detection. This leads to improved precision in the final keypoint localization.

3.3.	Keypoint detection

The detection of potential regions serves as a ‘‘coarse detection’’ step to prevent issues such as keypoint omission. After identifying these re- gions, we further determine the precise localization of keypoints within
 
𝑙
⎨⎪	= ∑3
 
𝑙+1
𝑊𝑙𝐻𝑖
 
(2)
 
them. This process involves two main strategies: (1) selecting the most suitable point from the potential region as the predicted keypoint and
 
Local features from all layers are aggregated and concatenated to form the global feature G, which can be expressed as follows:
{
 
 
to the points within this region. The first strategy can be unstable. When the k value is small, the features of points within the predicted potential
 
𝐻𝑙→𝐺 = 𝛷(𝑓𝑎𝑔𝑔(𝐻𝑙))
𝐺 = 𝛷(𝐻1→𝐺 ⊕ ⋯ ⊕ 𝐻𝐿→𝐺)
 
(3)
 
regions become very similar, making it difficult for the neural network to distinguish between them accurately. This similarity often leads to
 
where 𝑓𝑎𝑔𝑔 represents the ball query operation and L denotes the layers of the network.
The global feature is then propagated back to the upper layers through interpolation, similar to the process described in Eq. (2).

3.2.2. Penalty for undetected regions
Although detecting keypoint-related potential regions is similar to part segmentation, the accuracy is primarily determined by the selec- tion of k. Unlike point clouds of regular objects in public datasets, complex point clouds — such as medical point clouds — are signif- icantly denser and exhibit more intricate structures. If k falls below a certain threshold, undetected regions may arise on the point cloud. However, due to the irregularity and sparsity of point clouds, the larger k is, the greater the expected distances between points in regions and the ground truth. Our goal is to minimize the threshold of k as much as possible. Compared to the potential regions, the remaining point cloud, considered as the ‘‘background’’, is significantly larger. Using only the cross-entropy loss for optimization may not be sufficient, as high precision in predicting the ‘‘background’’ leads to lower loss values. In other words, the potential regions are not ‘‘prominent’’ enough to capture the network’s attention. To address this, we incorporated dice loss (Milletari et al., 2016) alongside the cross-entropy loss function. This combination encourages the network to focus on minute details within the point cloud, ensuring that all potential regions are detected while keeping k at a low value. The dice loss measures the similarity between two sets from a regional perspective. For keypoint 𝑡𝑝𝑖, the corresponding region of ground truth is expressed as 𝑅𝑡𝑎𝑟𝑔𝑒𝑡, while
 
highly random and inaccurate predictions. To address this, we adopted the second strategy, inspired by the work of Zohaib and Del Bue (2023). This strategy incorporates a PointNet encoder with residual modules to compute feature vectors for all points within the potential regions, from which we generate corresponding weight matrices. The final keypoint localization is determined by the weighted average of the points’ coor- dinates, as shown in Fig. 1(c). To enhance feature transformation, we introduced a T-Net and applied dimensionality reduction via residual modules. The final weight matrices are derived using our novel DS mechanism, which will be explained in detail later.
In typical deep learning tasks, the SoftMax function is used to compute weights based on feature values, such as the probabilities of belonging to different classes in classification problems. Similarly, in this task, the SoftMax function can be applied to calculate the probability of each point within the potential region being a keypoint. These probabilities are then used as weights to compute the weighted average of the coordinates, yielding the final predicted keypoint loca-
tion. However, this approach has limitations. When the center point 𝑠𝑝𝑖
of the predicted region 𝑅𝑝𝑟𝑒𝑑 is already close to the ground truth 𝑡𝑝𝑖, directly calculating the weight matrix via the neural network may lead to results that tend to converge toward the average, despite significant
variances in the distances between points within the region and the ground truth. Consequently, it becomes crucial to reduce the influence of points that are farther from the ground truth while increasing the weights of points closer to it. To address this issue, we introduce the DS mechanism. First, the SoftMax function is applied to calculate the
 
region predicted by network is expressed as
 
𝑖
𝑝𝑟𝑒𝑑
 
weight of each point. Then, a weight SoftMax step assigns zero weights
 
𝑅𝑖	. The dice coefficient between the ground truth and the predicted class i can be expressed as
follows:
 
to points whose calculated weight falls below a predefined threshold, as expressed in the following:
 

2 ∗ 𝑅𝑝𝑟𝑒𝑑 ∩ 𝑅𝑡𝑎𝑟𝑔𝑒𝑡 + 𝛿
 
𝑊 𝑆(𝜔 ) = { 	1	 |𝜔
 
𝐷𝑖𝑐𝑒(𝑖) =	𝑖	𝑖

 	 

 
(4)
 
𝑗	1 + exp (−1 ⋅ 𝑀(𝜔𝑗 − (1∕𝑚 − 𝜀))) | 𝑗
 
where 𝛿 = 1 × 10−6 is the smoothing term. The dice loss for all classes is then expressed as follows:
 
𝑖	| 𝑖
 
|  𝑗=1
 
where 𝑀 = 1 ∗ 102∗(𝛾+3), 𝜀 = 5 ∗ 10−1∗(𝛾+2), 𝛾 =
1 ∑
 
log10
 
𝑚⌉. The point’s
 
𝑖=1
 
{ 𝑝𝑟𝑒𝑑
 
}𝑘
 

𝑝𝑟𝑒𝑑
 

𝑡𝑎𝑟𝑔𝑒𝑡
 
( 𝑝𝑟𝑒𝑑
 
𝑡𝑎𝑟𝑔𝑒𝑡
 
𝑡𝑎𝑟𝑔𝑒𝑡
 
𝑖=1
 
within the region to determine the final predicted keypoint location.
 
𝑅𝑖	∪ 𝑅𝑖	) → 𝑅𝑖	. To achieve this, we further incorporated
(𝑅𝑝𝑟𝑒𝑑 ∪𝑅𝑡𝑎𝑟𝑔𝑒𝑡)
 

The process can be expressed as follows:
 
a penalty term 𝛼𝑖  =  (  𝑖	𝑖	, where 𝜎 is a constant with a
 
𝑖	𝑖
 
∑   𝜔𝑗 ⋅ 𝑊 𝑆(𝜔𝑗 ) 

   	 	 
 
𝑘	𝑘
𝐿 =	𝑦 log(𝑦̂ ) + 1 −	Dice(𝑖) + MAX exp(𝛼 )	(6)

 	 
 
For different potential regions, the number of selected points may
vary. While the error between the center points of the potential regions
   
 

 

/ig. 2. Visualization of the DS weighting mechanism. The first SoftMax converts feature vectors of points within the potential region into initial weights. The second SoftMax amplifies weight differences by applying a threshold, excluding low-weight points. The weights of the remaining points are recalculated, and their coordinates are aggregated using a weighted average to determine the final keypoint location.

 
optimized by our method show significant improvements in accu- racy. Fig. 2 illustrates the procedure of our DS weighting mechanism. This mechanism enhances keypoint detection precision within a region through two stages of refinement. First, the initial SoftMax assigns weights to all points in the region. However, due to the high density and similarity of points, the weights are often evenly distributed. To address this, the second stage-‘‘weight SoftMax’’-sharpens the focus by emphasizing points with higher weights and discarding those below a threshold. This two-step process ensures that only the most relevant points contribute to the final keypoint coordinates, improving both accuracy and robustness.

4.	Experiments and results

4.1.	Datasets and details

We validated our method using a dataset of 149 skull model sam- ples. The original data, provided in stereolithography format, had average dimensions of 231 × 171 × 150 mm. Keypoints were annotated according to standard cephalometric landmarks used in orthodontic X- ray measurements, as well as cosmetic reference landmarks from the Aesthetic Reference Guide for Plastic Surgery. A total of 14 representa- tive keypoints were selected, each assigned a unique semantic label, including: {‘‘left frontomalare orbitale’’: ‘‘1’’, ‘‘right frontomalare orbitale’’: ‘‘6’’, ‘‘left condylar process’’: ‘‘2’’, ‘‘right condylar pro-
cess’’: ‘‘7’’, ‘‘left sigmoid notch’’: ‘‘3’’, ‘‘right sigmoid notch’’: ‘‘8’’,
‘‘left gonion’’: ‘‘4’’, ‘‘right gonion’’: ‘‘9’’, ‘‘left inner edge of the
mandible’’: ‘‘5’’, ‘‘right inner edge of the mandible’’: ‘‘10’’, ‘‘nasion’’:
‘‘11’’, ‘‘anterior nasal spine’’: ‘‘12’’, ‘‘pogonion’’: ‘‘13’’, ‘‘gnathion’’:
 
plateau’’: ‘‘7’’, ‘‘center of the tibial tuberosity’’: ‘‘8’’} (Fig. 3(b)). The extracted point clouds contained approximately 2×104 to 1×105 sample points each, and were downsampled to 20,000 sample points.
Our experiments were conducted on an Ubuntu 22.04.4 LTS system with the following specifications: CPU: Intel® Xeon(R) Silver 4310 @
2.10 GHz × 12; GPU: Nvidia GeForce RTX 4090. The experimental setup employed the Adam optimizer with an initial learning rate of 0.001, and training was conducted for a total of 501 epochs.

4.2.	Evaluation metrics

Our task consists of two primary processes: detecting keypoint- related potential regions and identifying the keypoints within those regions. We evaluated the results of potential region detection by cal- culating the mean intersection over union (mIoU) between the ground truth and the predicted regions. Additionally, we analyzed the ratio of points within the predicted regions to those within the ground truth. This metric helps identify gaps in the predicted regions, which could lead to discrepancies in the number of predicted keypoints compared to the ground truth. We also computed the mean radial error (MRE) between the center points of the predicted regions and the ground truth keypoints on normalized point clouds. To assess keypoint detection accuracy within the potential regions, we measured the MRE (in mm) between the predicted keypoints and the ground truth on the original mesh models. Furthermore, we calculated the success detection rate (SDR), which is based on the MRE between both the region center points and the ground truth, as well as between the predicted key- points and the ground truth. The SDR represents the ratio of correctly predicted keypoints within the distance threshold 𝜃:
 {𝑘𝑝 𝑑𝑖𝑠𝑡(𝑘𝑝, 𝑡𝑝) ≤ 𝜃} 
 
‘‘14’’}, the remaining points were labeled as ‘‘background’’ (label ‘‘0’’). The locations of these keypoints on the skull are shown in Fig. 3(a). Point clouds were generated by extracting point coordinates and nor-
 
𝑆𝐷𝑅𝜃 = |
 
|  |{𝑘𝑝}|	|
 
(9)
 
mal vector information from the mesh models. The initial point clouds contained approximately 2 × 105 to 6 × 105 sample points each, which were downsampled to 60,000 sample points using FPS. To facilitate neural network training, the coordinates of the point clouds were nor- malized. We also validated our method on a dataset of 48 tibia model
samples, each with average dimensions of 352 × 78 × 61 mm. For the tibia dataset, we selected eight representative keypoints: {‘‘lateral endpoint’’: ‘‘1’’, ‘‘center of the lateral plateau’’: ‘‘2’’, ‘‘intercondylar ridge’’: ‘‘3’’, ‘‘medial endpoint’’: ‘‘4’’, ‘‘center of the medial plateau’’: ‘‘5’’, ‘‘anterior edge of the plateau’’: ‘‘6’’, ‘‘posterior edge of the
 
4.3.	Ablation study

4.3.1.	Region detection
To generate the ground truth for potential regions, we selected k points within each keypoint’s neighborhood and evaluated the impact of different k values (15, 20, 25, and 40). We also examined the effect of incorporating Penalty Dice Loss on potential region detection. Table 1 presents the mIoU between the predicted regions and the ground truth, as well as the MRE between the center points of the predicted regions and their corresponding ground truth keypoints for various k values.
 

 

/ig. 3. (a) Keypoints annotated on the skull: ⃝1 keypoint on left/right frontomalare orbitale, ⃝2 keypoint on left/right condylar process, ⃝3 keypoint on left/right sigmoid notch,
⃝4 keypoint on left/right gonion, ⃝5 keypoint on left/right inner edge of the mandible, ⃝6 keypoint on nasion, ⃝7 keypoint on anterior nasal spine, ⃝8 keypoint on pogonion, and
⃝9 keypoint on gnathion. (b) Keypoints annotated on the tibia: ⃝1 keypoint on lateral endpoint, ⃝2 keypoint on center of the lateral plateau, ⃝3 keypoint on intercondylar ridge,
⃝4 keypoint on medial endpoint, ⃝5 keypoint on center of the medial plateau, ⃝6 keypoint on anterior edge of the plateau, ⃝7 keypoint on posterior edge of the plateau, and ⃝8
keypoint on center of the tibial tuberosity.


/ig. 4. Sunburst chart displaying the ratio of point counts between predicted regions and ground truth regions. RatioA represents the ratio without the Penalty Dice Loss, while RatioB represents the ratio with the Penalty Dice Loss.

Table 1
Comparison of potential region detection across different k values using various methods, evaluated by mIoU, and comparison of the MRE between the center points of potential regions and the ground truth on normalized skull point clouds.
Model	𝑘 = 15	𝑘 = 20	𝑘 = 25	𝑘 = 40
PointNeXt (Qian et al., 2022)
36.82%	41.26%	43.69%	43.98%
PointVector (Deng et al., 2023)
34.42%	41.58%	42.89%	46.30%
SpoTr (Park et al., 2023)
43.01%	44.58%	46.13%	47.42%
Ours	52.46%	54.81%	56.35%	62.59%
Ours + Penalty Dice Loss	57.27%	58.79%	61.15%	66.60%
MRE	0.01450	0.01541	0.01647	0.01927
Note: The average distance from each point to its nearest neighbor is 0.01319 in the downsampled point clouds.

 
Additionally, we compared our approach with state-of-the-art models in point cloud part segmentation. While our task shares similarities with traditional point cloud part segmentation, the unique characteristics
 
of medical datasets create challenges for existing models, which are optimized for public datasets. In contrast, our model is specifically designed to handle the complexity and density of medical point clouds.
 

 

/ig. 5. MRE statistics between potential region center points and ground truth keypoints on normalized point clouds. (a) SDR curves, where the 𝑥-axis represents the Euclidean distance threshold and the 𝑦-axis represents the proportion of correct predictions within the threshold. (b) SDR stacked bar chart, illustrating the distribution of correct predictions across different k values within specific threshold intervals.


/ig. 6. (a) Box plots illustrating the MRE between predicted keypoints and the ground truth for each method. The 𝑦-axis represents the MRE (mm), and the 𝑥-axis denotes the methods employed: ⃝1 PointNet + Residual + DS, ⃝2 PointNet + Residual, ⃝3 PointNet++ + Residual + DS, ⃝4 PointNet++ + Residual, ⃝5 Center points of Region(k = 15). (b) Doughnut chart depicting the SDR across various distance threshold intervals for each method. The rings, from the innermost to the outermost, correspond to methods
⃝1 to ⃝5 as specified in (a).

 
By introducing a multiscale feature cross-fusion architecture and mod- ules to capture rich local features, our method outperforms existing approaches.
A major challenge in this task stems from the significant class imbalance between keypoint-related potential regions and background points. To address this, we integrated Penalty Dice Loss into the loss function, utilizing its normalization properties to emphasize the overlap between predicted and ground truth values for each semantic category. This approach shifts the network’s focus from ‘‘background points’’ to the keypoint-related potential regions, thereby improving detection accuracy. As shown in Fig. 4, decreasing the value of k leads to a lower ratio of predicted points to ground truth points within the regions, making it more challenging for the network to detect the corresponding regions. When k is reduced to 20 or below, blank regions begin to appear. Incorporating the Penalty Dice Loss helps mitigate this issue to some extent and, as shown in Table 1, significantly enhances the detection accuracy of keypoint-related potential regions. It is important to note that improving detection accuracy at lower k values goes beyond simply ensuring the robustness of the method. From Table 1 and Fig. 5, we observe that smaller k values result in lower MRE between the predicted region centers and the ground truth, or, equiv- alently, a smaller expected distance between the predicted points and the ground truth within the region. This also leads to a higher SDR under stricter threshold criteria. Therefore, our method ensures robust
 
detection of keypoint-related potential regions even at low k values, providing a solid foundation for subsequent keypoint detection within these regions.

4.3.2.	Keypoint detection
The number of points in the predicted potential regions cannot be guaranteed to remain consistent. To address this, we employ the following resampling strategies within each region: (1) Supplementing points: When the number of points is fewer than k, we add the region center point to supplement the points; (2) Selecting closest points: When the number of points exceeds k, we select the k points closest to the region center point.
The keypoint detector is then used to generate weight matrices for the points within each region. These matrices are utilized to com- pute the weighted average of the points’ coordinates, which serves as the final predicted keypoint. We evaluated several keypoint detection strategies, as shown in Table 2. Abbreviations were used for brevity in the presentation: frontomolare orbitale (left and right) is denoted as FO, condylar process (left and right) as CP, sigmoid notch (left and right) as SN, gonion (left and right) as Go, inner edge of the mandible (left and right) as IEM, nasion as Na, anterior nasal spine as ANS, pogonion as Po, and gnathion as Gn.
We first tested the accuracy of directly using the center points of the detected potential regions as the predicted keypoints. Next, we
 

Table 2
MRE (mm) between the predicted 14 keypoints and the ground truth in different regions on the original skull mesh models, evaluated using various strategies.
Part	Center points (𝑘 = 15)	PointNet++
+Residual	PointNet++
+Residual+DS	PointNet
+Residual	PointNet
+Residual+DS
			MRE ± SD (mm)		
FO	1.16 ± 0.43	1.15 ± 0.57	1.04 ± 0.54	1.09 ± 0.57	1.02 ± 0.59
CP	1.46 ± 0.69	1.23 ± 0.64	1.15 ± 0.61	1.01 ± 0.54	0.99 ± 0.52
SN	1.33 ± 0.55	1.25 ± 0.62	1.08 ± 0.57	1.24 ± 0.71	1.10 ± 0.61
Go	2.52 ± 1.49	2.58 ± 1.42	2.41 ± 1.38	2.46 ± 1.26	2.18 ± 1.25
IEM	1.97 ± 1.10	2.05 ± 0.99	1.73 ± 0.96	1.99 ± 0.99	1.72 ± 0.90
Na	1.50 ± 0.85	1.60 ± 0.89	1.43 ± 1.07	1.60 ± 0.83	1.46 ± 1.00
ANS	2.14 ± 0.60	1.94 ± 0.57	1.83 ± 0.38	0.95 ± 0.39	0.84 ± 0.32
Po	2.63 ± 1.48	2.61 ± 1.21	2.36 ± 1.32	2.55 ± 1.17	2.16 ± 1.15
Gn	2.13 ± 0.83	2.04 ± 0.77	1.80 ± 0.72	2.06 ± 0.77	1.61 ± 0.94
Mean	1.81 ± 1.08	1.77 ± 1.06	1.59 ± 1.03	1.62 ± 1.04	1.43 ± 0.97
Note: FO, CP, SN, Go, and IEM combine the statistics of keypoints on both the left and right sides. Values in bold indicate the best performance.

 
evaluated the impact of incorporating a keypoint detector, consisting of an encoder and a residual decoder, on detection accuracy. We conducted tests using both PointNet and PointNet++ as encoders for the potential regions. The results showed that PointNet outperformed PointNet++, with minimal improvement observed when using Point- Net++ over simply using the center points. We attribute PointNet’s superior performance primarily to the feature transformation process of the T-Net.
Notably, we found that detection accuracy could be further im- proved. In our experiments, we observed that when randomly selecting a subset of points within the region to compute the keypoint coordi- nates, the resulting weight matrix was nearly uniform, but the MRE sometimes showed a significant reduction. As a result, we decided to remove points that were farther from the ground truth based on the computed weight matrix and redistribute the weights among the remaining points. We introduced our DS weighting mechanism and evaluated its incorporation after the encoder–decoder architecture, leading to a significant improvement in keypoint detection accuracy, as shown in Table 2. We further analyzed the distribution of MRE for different approaches, as illustrated in Fig. 6(a). Additionally, we evaluated the SDRs of these approaches at different thresholds (𝜃), shown in Fig. 6(b). Our method achieved accuracies of 40.3%, 76%, 90.3%, and 98% at thresholds of 1 mm, 2 mm, 3 mm, and 4 mm, respectively. The MRE between the predicted keypoints and the ground truth on the normalized skull point clouds (each with 60,000 points) is only 0.01132, while the mean distance between points within these point clouds and their nearest neighbors is 0.01319. Currently, the clinical standard for keypoint detection on 2D images requires an error margin within 2 mm. Based on this standard, our method achieved an accuracy rate of 76% on 3D skull models, comparable to state-of-the- art cephalometric landmark detection methods applied to X-ray images. To provide a more intuitive demonstration of prediction accuracy, we remapped the detected keypoints from the normalized point cloud back to the original mesh model and visually compared them with the ground truth, as shown in Fig. 7. From Fig. 7 and Table 2, it is evident that the MRE of keypoints at the gonion, inner edge of the mandible, and gnathion is significantly larger compared to other key- points. The primary reasons for this include the less distinct features in these regions, the relatively ambiguous definitions of these keypoints, and interindividual anatomical variations. For instance, in sample V shown in Fig. 7, the gonion structure significantly differs from that of other cases, which tends to result in greater errors, even during manual annotation. Moreover, due to the limited size of our training dataset, the model’s stability can be more easily influenced by such atypical samples, leading to slightly higher, though still acceptable, errors and deviations in these regions. Despite these challenges, the predicted and ground truth keypoints in other regions align closely,
 
Table 3
MRE (mm) and SDR (%) of the predicted keypoints in different regions on the original tibia mesh models.
 
Part	MRE ± SD (mm)	SDR (%)

	1 mm	2 mm	3 mm	4 mm
LE	1.29 ± 0.74	46.7	80	95.6	100
CLP	1.14 ± 0.60	46.7	88.9	100	100
IR	1.67 ± 1.91	46.7	80	88.9	100
ME	1.41 ± 1.43	46.7	84.4	93.3	95.6
CMP	1.71 ± 1.42	35.6	71.1	86.7	95.6
AEP	2.36 ± 1.75	22.2	46.7	73.3	88.9
PEP	1.20 ± 0.77	44.4	86.7	97.8	97.8
CTT	1.54 ± 1.67	48.9	73.3	91.1	97.8
Mean	1.54 ± 1.42	42.2	76.4	90.8	96.1



fully demonstrating the feasibility and superior performance of our method.
To further validate the generalizability of our method, we also evaluated our method on the tibia dataset, which contains 48 samples. The tibia point clouds were downsampled to 20,000 points, and 𝑘 was set to 20. Given the limited sample size, we employed fivefold cross- validation for verification. The results, presented in Table 3, show the MRE (mm) and SDR (%) of the predicted keypoints for each part.
For brevity, abbreviations were used: lateral endpoint is denoted as LE, center of the lateral plateau as CLP, intercondylar ridge as IR, medial endpoint as ME, center of the medial plateau as CMP, anterior edge of the plateau as AEP, posterior edge of the plateau as PEP, and center of the tibial tuberosity as CTT. The final prediction results were remapped onto the original tibia mesh model. Fig. 8 visualizes the comparison between the predicted keypoints and the ground truth.

5.	Discussion

5.1.	Selection of k values for different point cloud models

In this study, we proposed a deep learning framework for keypoint detection that addresses the challenge of directly detecting keypoints with specific anatomical significance in dense, complex point clouds. Our framework follows a coarse-to-fine two-stage process: (1) detecting ‘‘potential regions’’ where keypoints may exist and (2) calculating a weight matrix for the points within these regions to precisely localize the keypoints by combining the weights with their coordinates. To generate the ground truth for the potential regions, we used the KNN algorithm. A key challenge in this process is determining an appropriate neighborhood size, k. An excessively small k value can result in blank predicted regions, missing potential keypoints. To address this, we introduced Penalty Dice Loss, which successfully limits k to a relatively
 

 

/ig. 7. Visualization of the predicted keypoints and ground truth on the original skull mesh models. The views, from top to bottom, are right lateral view, frontal view, and left lateral view.


/ig. 8. Visualization of the predicted keypoints and ground truth on the original tibia mesh model. The upper image shows the top view of the tibia, while the lower image presents the front view obtained by rotating the top view 90◦ around the 𝑥-axis.

 
small value. In our experiments, we set k to 15 for skull point clouds, each containing 60,000 points, and to 20 for tibia point clouds, each with 20,000 points per sample. The differences in k values between the skull and tibia models highlight the impact of dataset size, morphologi- cal variability, and feature prominence. Compared to the skull dataset,
 
the tibia dataset has fewer training samples and greater morphological diversity. Additionally, as shown in Fig. 8, many keypoints on tibias lack distinct geometric features, making them difficult to detect— even during manual annotation. In contrast, skull models exhibit more consistent geometric variations, enabling our framework to achieve
 


 


/ig. 9. Scatter plot of the MRE for detected keypoints across different parts of the skull and tibia models. The MRE between each predicted keypoint and its corresponding ground truth was calculated for all samples, with keypoints from different parts distinguished by color.

 
robust keypoint-related potential region detection, even with smaller
k values and denser point clouds.

5.2.	Factors leading to elevated MREs in keypoint detection

Elevated MREs in both the skull and tibia datasets are generally observed in regions where geometric features are less pronounced. As shown in Fig. 9 and Table 2, keypoints with higher MREs include the gonion (Go) and pogonion (Po) on the skull and the AEP on the tibia. These regions present distinct challenges:
(1)	Pogonion (Po): This region lacks clear distinguishing features, making it difficult for both manual annotation and automatic detection.
(2)	Gonion (Go): Although this region exhibits prominent fea- tures on the skull model, selecting a representative keypoint remains challenging. This difficulty is especially pronounced when multiple protrusions are present in some skull models, as shown in Fig. 7, increasing the likelihood of negatively impacting the training.
(3)	AEP: This region shows significant morphological variability across different tibia models, complicating the identification of key- point locations.
 
Additionally, compared to the skull model, the tibia model intro- duces an extra factor that affects training: the inclusion of both left and right tibias in the dataset. As shown in Fig. 9, a relatively large MRE is observed at the CTT location, which can be attributed to the morphological differences between the left and right tibias at this position. Nevertheless, the overall impact of this difference is relatively minor.

5.3.	Potential applications

Advanced, precise, and robust algorithms show significant potential for real-world applications. Our proposed method offers numerous practical and engineering applications. In clinical settings, 3D key- points provide surgeons with more intuitive and concise references compared to 2D keypoints, offering richer spatial information. This en- ables broader integration across multiple domains, such as preoperative planning, intraoperative navigation, and postoperative evaluation:
(1)	Preoperative planning: The detected 3D keypoints can help sur- geons mark critical anatomical regions on 3D models, simplifying the surgical planning process. Additionally, by ensuring precise localization
 

 
on 3D medical models, the method facilitates the design and accurate placement of implants.
(2)	Intraoperative navigation: Real-time detected keypoints can serve as reliable and accurate 3D anatomical references during surgery, offering essential guidance in confined anatomical spaces. Furthermore, the predicted keypoints can be integrated with surgical robotic arms to assist with path planning for robotic-assisted surgeries.
(3)	Postoperative evaluation: Leveraging keypoints as benchmarks establishes of a standardized framework for the automated comparison of preoperative and postoperative 3D models, enabling the quantitative evaluation of surgical outcomes.
It is important to note that, as a two-stage deep learning model, the inference process takes approximately 2 s. However, due to the time-intensive nature of data preprocessing (such as 3D model re- construction), real-time applicability for intraoperative navigation re- quires further consideration of factors such as scanning and imaging equipment and the hardware environment.
Beyond medical applications, our framework can be applied in various engineering and manufacturing sectors where precision is crit- ical. In high-precision manufacturing, the detected 3D keypoints with semantic labels can be used to accurately measure the dimensions and shapes of components. They can also assist automated systems in the precise identification and localization of parts, enhancing production efficiency and quality control in complex manufacturing environments. In summary, the proposed framework opens a new path in supervised keypoint detection in the 3D domain, with significant potential for real-world applications.

6.	Conclusion

In contrast to existing research, which primarily focuses on 2D keypoint detection, we proposed a pioneering supervised deep learning framework for detecting 3D keypoints with specific semantic informa- tion on dense point clouds. On our in-house skull and tibia datasets, our method achieved MREs of 1.43 mm and 1.54 mm, with SDRs of 76.00% and 76.40% within the clinically accepted 2 mm range, demonstrating performance comparable to state-of-the-art 2D detec- tion methods. Additionally, its successful application to both skull and tibia models each with distinct geometric structures and complex topologies—demonstrates the method’s broad applicability to a variety of 3D models. In clinical settings, our framework shows strong potential for use in a range of procedures, including preoperative planning, intraoperative navigation, and postoperative evaluation.
However, several limitations remain. Manual annotation in 3D space often results in larger absolute errors compared to 2D images, which can impact model accuracy and stability. Additionally, certain samples with highly complex 3D structures lead to greater detection errors for specific keypoints. To address these challenges, future work could incorporate 2D slices from CT or MRI scans during the second stage of keypoint detection, using 2D keypoints to refine 3D predictions within localized coordinate systems. Looking ahead, we plan to explore multimodal approaches to further enhance the accuracy and robustness of the model.

CRediT authorship contribution statement

Qiuyang Chen: Writing – review & editing, Writing – original draft, Visualization, Validation, Methodology, Formal analysis, Data curation, Conceptualization. Shenghui Liao: Writing – review & edit- ing, Supervision, Project administration, Investigation, Funding acquisi- tion, Conceptualization. Xiaoyan Kui: Supervision, Project administra- tion, Investigation, Funding acquisition. Ziyang Hu: Validation, Formal analysis, Data curation. Jianda Zhou: Supervision, Data curation.
 
Declaration of competing interest

The authors declare that they have no known competing financial interests or personal relationships that could have influenced the work reported in this paper.

Acknowledgments

This work is supported by grants from the National Natural Science Foundation of China (No. 62372475, Nos. U22A2034, 62177047). We also gratefully acknowledge the resources provided by the High Performance Computing Center of Central South University.

Data availability

The authors do not have permission to share data.


References

Bai, X., Luo, Z., Zhou, L., Fu, H., Quan, L., Tai, C.-L., 2020. D3Feat: Joint learning of dense detection and description of 3D local features. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. CVPR.
Cheng, S., Chen, X., He, X., Liu, Z., Bai, X., 2021. Pra-net: Point relation-aware network for 3D point cloud analysis. IEEE Trans. Image Process. 30, 4436–4448.
Chenguang, G., Xianglong, L., Linfeng, Z., Xiang, L., 2009. A fast and accurate corner detector based on harris algorithm. In: 2009 Third International Symposium on Intelligent Information Technology Application. Vol. 2, IEEE, pp. 49–52.
Deng, X., Zhang, W., Ding, Q., Zhang, X., 2023. Pointvector: a vector representation in point cloud analysis. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 9455–9465.
Du, J., Wang, R., Cremers, D., 2020. DH3D: Deep hierarchical 3D descriptors for robust large-scale 6dof relocalization. In: Vedaldi, A., Bischof, H., Brox, T., Frahm, J.-M. (Eds.), Computer Vision – ECCV 2020. Springer International Publishing, Cham,
pp. 744–762.
Gao, S.-H., Cheng, M.-M., Zhao, K., Zhang, X.-Y., Yang, M.-H., Torr, P., 2019. Res2net: A new multi-scale backbone architecture. IEEE Trans. Pattern Anal. Mach. Intell. 43 (2), 652–662.
Gojcic, Z., Zhou, C., Wegner, J.D., Wieser, A., 2019. The perfect match: 3D point cloud matching with smoothed densities. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 5545–5554.
Guo, M.-H., Cai, J.-X., Liu, Z.-N., Mu, T.-J., Martin, R.R., Hu, S.-M., 2021. Pct: Point
cloud transformer. Comput. Vis. Media 7, 187–199.
Harris, C., Stephens, M., et al., 1988. A combined corner and edge detector. In: Alvey Vision Conference. Vol. 15, Citeseer, pp. 10–5244.
Knopp, J., Prasad, M., Willems, G., Timofte, R., Van Gool, L., 2010. Hough trans- form and 3D SURF for robust three dimensional classification. In: Daniilidis, K., Maragos, P., Paragios, N. (Eds.), Computer Vision – ECCV 2010. Springer Berlin Heidelberg, Berlin, Heidelberg, pp. 589–602.
Kowalski, M., Naruniec, J., Trzcinski, T., 2017. Deep alignment network: A convo- lutional neural network for robust face alignment. In: Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition Workshops. pp. 88–97.
Li, J., Lee, G.H., 2019. USIP: Unsupervised stable interest point detection from 3D point clouds. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. ICCV.
Liu, Y., Fan, B., Xiang, S., Pan, C., 2019. Relation-shape convolutional neural network for point cloud analysis. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 8895–8904.
Lowe, D.G., 2004. Distinctive image features from scale-invariant keypoints. Int. J. Comput. Vis. 60, 91–110.
Mehrnia, M., Elbayumi, M., Elbaz, M.S.M., 2024. Assessing foundational medical ’segment anything’ (med-SAM1, med-SAM2) deep learning models for left atrial segmentation in 3D LGE mri. arXiv:2411.05963. URL https://arxiv.org/abs/2411. 05963.
Milletari, F., Navab, N., Ahmadi, S.-A., 2016. V-net: Fully convolutional neural networks for volumetric medical image segmentation. In: 2016 Fourth International Conference on 3D Vision. 3DV, Ieee, pp. 565–571.
Nia, S.N., Shih, F.Y., 2024. Medical X-Ray image enhancement using global contrast- limited adaptive histogram equalization. Int. J. Pattern Recognit. Artif. Intell. URL http://dx.doi.org/10.1142/S0218001424570106.
Park, J., Lee, S., Kim, S., Xiong, Y., Kim, H.J., 2023. Self-positioning point-based transformer for point cloud understanding. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 21814–21823.
Qi, C.R., Su, H., Mo, K., Guibas, L.J., 2017a. PointNet: Deep learning on point sets for 3D classification and segmentation. In: Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition. CVPR.
 

 
Qi, C.R., Yi, L., Su, H., Guibas, L.J., 2017b. Pointnet++: Deep hierarchical feature learning on point sets in a metric space. Adv. Neural Inf. Process. Syst. 30.
Qian, G., Li, Y., Peng, H., Mai, J., Hammoud, H., Elhoseiny, M., Ghanem, B., 2022. Pointnext: Revisiting pointnet++ with improved training and scaling strategies. Adv. Neural Inf. Process. Syst. 35, 23192–23204.
Sipiran, I., Bustos, B., 2011. Harris 3D: a robust extension of the harris operator for interest point detection on 3D meshes. Vis. Comput. 27, 963–976.
Sun, Y., Wang, X., Tang, X., 2013. Deep convolutional network cascade for facial point detection. In: Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition. pp. 3476–3483.
Sun, K., Xiao, B., Liu, D., Wang, J., 2019. Deep high-resolution representation learning for human pose estimation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 5693–5703.
Sung, M., Su, H., Yu, R., Guibas, L.J., 2018. Deep functional dictionaries: Learning consistent semantic structures on 3D models from functions. Adv. Neural Inf. Process. Syst. 31.
Tekin, A., Joghataee, M., Rovati, L., Truong, H., Castillo-Zambrano, C., Kushagra, K., Nikravangolsefid, N., Ozkan, M., Gupta, A., Herasevich, V., Domecq, J., O’Horo, J., Gajic, O., 2024. Development and validation of a preliminary multivariable diagnostic model for identifying unusual infections in hospitalized patients. Biomol. Biomed. 24 (5), 1387–1399. http://dx.doi.org/10.17305/bb.2024.10447.
Tourei, A., Ji, X., Rocha dos Santos, G., Czarny, R., Rybakov, S., Wang, Z., Hallissey, M., Martin, E.R., Xiao, M., Zhu, T., Nicolsky, D., Jensen, A., 2024a. Mapping permafrost variability and degradation using seismic surface waves, electrical resistivity, and temperature sensing: A case study in arctic alaska. J. Geophys. Res.: Earth Surf. 129 (3), e2023JF007352. http://dx.doi.org/10.1029/2023JF007352, URL https:// agupubs.onlinelibrary.wiley.com/doi/abs/10.1029/2023JF007352. e2023JF007352 2023JF007352.
Tourei, A., Martin, E.R., Ankamah, A.T., Hole, J.A., Chambers, D.J.A., 2024b. An autoencoder-based deep learning model for enhancing noise characterization and microseismic event detection in underground longwall coal mines using distributed acoustic sensing monitoring. In: 58th U.S. Rock Mechanics/Geomechanics Sympo- sium. In: U.S. Rock Mechanics/Geomechanics Symposium, D041S052R005. http:
//dx.doi.org/10.56952/ARMA-2024-0207.
Wang, Y., Sun, Y., Liu, Z., Sarma, S.E., Bronstein, M.M., Solomon, J.M., 2019. Dynamic graph CNN for learning on point clouds. ACM Trans. Graph. 38 (5), URL https:
//doi.org/10.1145/3326362.
Wu, W., Qian, C., Yang, S., Wang, Q., Cai, Y., Zhou, Q., 2018. Look at boundary: A boundary-aware face alignment algorithm. In: Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition. pp. 2129–2138.
Wu, W., Zhang, Y., Wang, D., Lei, Y., 2020. SK-Net: Deep learning on point cloud via end-to-end discovery of spatial keypoints. In: Proceedings of the AAAI Conference on Artificial Intelligence. Vol. 34, pp. 6422–6429.
 
Yew, Z.J., Lee, G.H., 2018. 3Dfeat-net: Weakly supervised local 3D features for point cloud registration. In: Proceedings of the European Conference on Computer Vision. ECCV, pp. 607–623.
Yi, L., Su, H., Guo, X., Guibas, L.J., 2017. SyncSpecCNN: Synchronized spectral CNN for 3D shape segmentation. In: Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition. CVPR.
You, Y., Liu, W., Ze, Y., Li, Y.-L., Wang, W., Lu, C., 2022. UKPGAN: A general
self-supervised keypoint detector. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. CVPR, pp. 17042–17051.
You, Y., Lou, Y., Li, C., Cheng, Z., Li, L., Ma, L., Lu, C., Wang, W., 2020. Keypointnet: A large-scale 3D keypoint dataset aggregated from numerous human annotations. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 13647–13656.
Zhang, Z., Luo, P., Loy, C.C., Tang, X., 2014. Facial landmark detection by deep multi- task learning. In: Computer Vision–ECCV 2014: 13th European Conference, Zurich, Switzerland, September 6-12, 2014, Proceedings, Part VI 13. Springer, pp. 94–108.
Zhang, K., Zhang, Z., Li, Z., Qiao, Y., 2016. Joint face detection and alignment using multitask cascaded convolutional networks. IEEE Signal Process. Lett. 23 (10), 1499–1503. http://dx.doi.org/10.1109/LSP.2016.2603342.
Zhang, K., Zhang, Z., Wang, H., Li, Z., Qiao, Y., Liu, W., 2017. Detecting faces using inside cascaded contextual cnn. In: Proceedings of the IEEE International Conference on Computer Vision. pp. 3171–3179.
Zhao, H., Jiang, L., Jia, J., Torr, P.H., Koltun, V., 2021. Point transformer. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 16259–16268.
Zheng, T., Chen, C., Yuan, J., Li, B., Ren, K., 2019. PointCloud saliency maps. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. ICCV.
Zhong, Y., 2009. Intrinsic shape signatures: A shape descriptor for 3D object recogni- tion. In: 2009 IEEE 12th International Conference on Computer Vision Workshops, ICCV Workshops. pp. 689–696. http://dx.doi.org/10.1109/ICCVW.2009.5457637.
Zhong, C., You, P., Chen, X., Zhao, H., Sun, F., Zhou, G., Mu, X., Gan, C., Huang, W., 2022. Snake: Shape-aware neural 3D keypoint field. Adv. Neural Inf. Process. Syst. 35, 7052–7064.
Zhou, Y., Tuzel, O., 2018. VoxelNet: End-to-end learning for point cloud based 3D object detection. In: Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition. CVPR.
Zhu, H., Yao, Q., Xiao, L., Zhou, S.K., 2021. You only learn once: Universal anatom- ical landmark detection. In: Medical Image Computing and Computer Assisted Intervention–MICCAI 2021: 24th International Conference, Strasbourg, France, September 27–October 1, 2021, Proceedings, Part V 24. Springer, pp. 85–95.
Zohaib, M., Del Bue, A., 2023. Sc3k: Self-supervised and coherent 3D keypoints estimation from rotated, noisy, and decimated point cloud data. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 22509–22519.
