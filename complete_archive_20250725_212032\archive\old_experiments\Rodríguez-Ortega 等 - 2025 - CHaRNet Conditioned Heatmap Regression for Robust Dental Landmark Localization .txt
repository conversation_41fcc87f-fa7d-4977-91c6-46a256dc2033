 
CHARNET: CONDITIONED HEATMAP REGRESSION FOR ROBUST DENTAL LANDMARK LOCALIZATION



 
<PERSON>-Ortega
Nemotec Madrid, Spain
<EMAIL>

Dept. of Computer Science and Artificial Intelligence University of Granada
Granada, Spain
<EMAIL>
 
<PERSON>-<PERSON>
Nemotec Madrid, Spain
<EMAIL>
 

Siham Tabik
Dept. of Computer Science and Artificial Intelligence, University of Granada
Granada, Spain
<EMAIL>



ABSTRACT
Identifying anatomical landmarks in 3D dental models is vital for orthodontic treatment, yet manual placement is complex and time-consuming. Although some machine learning approaches have been proposed for automatic tooth landmark detection in 3D Intraoral Scans (IOS), none provide a fully end-to-end solution that bypasses teeth segmentation, limiting practical applicability.
We introduce CHaRNet (Conditioned Heatmap Regression Network), the first fully end-to-end deep learning framework for tooth landmark detection in 3D IOS. Unlike traditional two-stage workflows that segment teeth before detecting landmarks, CHaRNet directly operates on the input point cloud, thus reducing complexity and computational overhead. Our method integrates four modules: (1) a point cloud encoder, (2) a point cloud decoder with a heatmap regression head, (3) a teeth presence classification head, and (4) the novel Conditioned Heatmap Regression (CHaR) module. By leveraging teeth presence classification, the CHaR module dynamically adapts to missing teeth and enhances detection accuracy in complex dental models.
We evaluate CHaRNet using five point cloud learning algorithms on a clinical dataset of 1,214 annotated 3D models. Both the dataset and code will be publicly released1 to address the lack of open datasets in orthodontics and inspire further research. CHaRNet achieves a Mean Euclidean Distance Error (MEDE) of 0.51 mm on typical dental models and 1.28 mm across all dentition types, with corresponding Mean Success Rates (MSR) of 87.06% and 82.40%, respectively. Notably, it exhibits robust performance on irregular geometries, including models with missing teeth. This end-to-end approach streamlines orthodontic workflows, enhances 3D IOS analysis precision, and supports efficient computer-assisted treatment planning.

Keywords Intraoral Scan · Neural Networks · Deep Learning · Landmark Detection · Orthodontic Treatment Planning

1Resources will be made public upon acceptance
 
1	Introduction
Automatic 3D anatomical keypoints or landmarks detection plays a crucial role in computer-aided orthodontics, allowing tasks such as orthodontic planning, prosthetic design, and the diagnosis of dental anomalies [1]. Intraoral scans (IOS) are commonly utilized to capture accurate digital surface models of dentition. These scanners represent the 3D surface of teeth, typically as point clouds or meshes. These 3D dental models are highly valuable for simulating procedures such as tooth extraction, movement, deletion, and rearrangement, allowing dentists to predict treatment outcomes more effectively. As a result, digital teeth models have the potential to streamline dentists’ workflows and reduce the time spent on labor-intensive tasks.
Existing approaches to perform 3D dental tasks such as teeth segmentation and landmark detection often rely on point cloud-based learning methods [2, 3, 4, 5, 6]. Although effective for general point cloud problems, these methods struggle in dental applications, where incomplete geometries and subtle variations in anatomy, such as missing teeth or the presence of third molars, lack specialized handling [7, 3, 8, 9]. Furthermore, the absolute lack of publicly available dental datasets impedes progress by preventing fair comparisons between methods and limiting accessibility to this field to researchers outside the specialized domains [8, 9].
In this work, we address these challenges through three key contributions:
1.	We introduce CHaRNet, an end-to-end deep learning (DL) model that directly detects landmarks on the IOS point cloud without relying on previous teeth segmentation, drastically reducing the latency of the overall computation.
2.	We propose the Conditioned Heatmap Regression (CHaR) module, a novel enhancement to point cloud learning architectures, which dynamically adapts to missing teeth.
3.	We construct and make publicly available a dataset of 1, 214 annotated 3D digital teeth models, which includes diverse complexities, detailed reference landmarks and a taxonomy of dentition types to evaluate models across varying levels of complexity.
Our results highlight the effectiveness of the proposed CHaR module, demonstrating significant performance improve- ments across multiple point cloud learning models. Notably, the CHaR-augmented PointMLP, referred to as CHaRNet, achieves a Mean Euclidean Distance Error (MEDE) of 0.51 mm on typical dental models, defined as those with a complete set of teeth excluding third molars, and 1.28 mm across all dentition types. It achieves a Mean Success Rate (MSR) of 87.06% on typical dental models and 82.40% across all dentition types. These findings underscore the robustness of CHaRNet, particularly in challenging cases involving several missing teeth.
The remainder of this paper is organized as follows: Related works are presented in Section 2. Section 3 describes the dataset construction process, including the proposed taxonomy and data preprocessing steps. Section 4 formally introduces the problem and the proposed method, including the CHaR module and its integration into point cloud learning architectures. Section 5 presents a thorough evaluation of the proposed approach across different point cloud architectures. Section 6 discusses the broader implications, limitations, and potential directions for future work. Finally, Section 7 summarizes the key contributions and results of this work.

2	Related works
2.1	Point cloud learning
Traditionally, three main stream methods have been used for 3D point cloud processing: projection-based, voxel-based and point cloud-based. Projection-based methods try to firstly project the original point cloud to a simpler 2D domain to later apply common 2D operations for feature extraction [10, 11, 12, 13]. While simple and effective, the projection operations over point sets inherently collapse potentially useful geometric information. Voxel-based methods transform the irregular nature of point clouds to regular 3D voxels (the equivalent of pixels in 3D) then apply 3D convolution operations [14, 15]. Given the sparse nature of 3D data, directly applying 3D convolutions over the voxel input results in a highly inefficient computation. To take advantage of this sparsity, sparse convolutions [16, 17, 18] greatly reduce computation and memory requirements by evaluating the convolution operation only in occupied voxels. Finally, point cloud-based methods directly process point clouds as unordered point sets. PointNet [19] pioneered this approach by directly processing point sets using shared multilayer perceptrons (MLP). PointNet++ [20] improves its predecessor by hierarchically applying set abstraction operations. PointNeXt [21] further improves PointNet++ by using modern training strategies such as data augmentation, new optimization techniques, and increased model size. The authors of PointMLP [22] hypothesize that local geometrical information may not be the key to point cloud learning and introduce a simple and pure residual MLP network that performs competitively with more complicated methods.
 
DGCNN [23] and similar graph-based approaches [24, 25, 26] perform typical graph operations like message passing over the point cloud previously connected as some form of graph. Given the success of the self-attention mechanism, PointTransformer [27] designed self-attention layers for point cloud processing and use them to perform tasks such as scene semantic segmentation or object classification in 3D scenes. Since then several studies have kept using transformers [28, 29, 30, 31, 32].
Recently, some DL methods operate directly on the 3D point cloud representation of dental models. For example, [33] proposed an end-to-end DL approach based on PointCNN [34] to segment individual teeth and gingiva from point cloud representation of IOS. [35] made use of FeaStNet [36], a graph convolutional neural network, to also perform teeth segmentation on 3D dental models. The authors in [37] introduced Mask-MCNet to perform instance segmentation of point cloud data from IOS. Mask-MCNet localizes each tooth by predicting its 3D bounding box and simultaneously segments all the points inside each box. TSegNet [38] was proposed as an end-to-end learning-based method for robust tooth segmentation on 3D point cloud dental models. It introduced a distance-aware tooth centroid voting scheme and a confidence-aware cascade segmentation module to handle challenging cases like missing, crowded, or misaligned teeth. DBGANet [39] was proposed as a Dual-Branch Geometric Attention Network for 3D tooth segmentation, utilizing centroid-guided separable attention and Gaussian neighbor attention to capture global geometric structure and refine tooth-gingiva boundaries. DentalMAE [40] extended the self-supervised MeshMAE [41] framework, demonstrating improved generalization and transfer learning for teeth segmentation on 3D intra-oral scans, even with limited training data.

2.2	Landmark detection
Landmark detection is a fundamental task in both computer vision and medical imaging, playing a critical role in tasks such as diagnosis, treatment planning, and surgical simulation. Most classical methods [42, 43, 44, 45] have historically made use of local geometric information to detect landmarks that are highly associated with sharp features. Although these methods are effective within their well-defined domains, they are limited in scope and are unable to identify landmarks outside their domain. With the rise of DL, two approaches dominate the data-driven landmark localization problem. The most intuitive approximation is the regression-based approach, which directly regresses coordinates from the input (images, volumes, point clouds, etc.) [46, 47, 48, 49]. However, this direct mapping is extremely challenging given the unbounded nature of the coordinates [50]. Instead, heatmap-based detection, introduced in [51], has become a popular and preferred approach for landmark detection due to its ability to encode location information as probabilities over the inputs. This approach simplifies the task by reducing the space of possibilities to the number of elements in the input space (e.g., pixels, voxels, vertices) [52, 53, 54].
In recent years, landmark localization task has become popular in medical image analysis [55, 56, 57, 58]. However, only a handful of works have been proposed for landmark detection in 3D dental models. [1] proposed a set of analytical approaches to identify dental-specific features (e.g., cusps, marginal ridges, and grooves) on digital dental meshes. At individual tooth level, [3] designed a novel neural network architecture for the joint tasks of predicting landmarks and axes. [59] presented a method for landmark detection in 3D dental meshes that leverages a multi-view approach, transforming the task into the 2D domain. In this way, the network detects landmarks through heatmap regression across multiple viewpoints. ALR [60] first determines the orientation of the scan, then uses local maxima along the vertical axis as an initial approximation for landmarks. This is followed by analyzing surface gradient and curvature information to identify the shape and boundaries of each tooth. c-SCN [7] was proposed as an end-to-end method for tooth segmentation and landmark localization but only on teeth crowns. TS-MDL [8] proposed a two-stage framework to subsequently perform teeth segmentation and landmark detection on IOS. By harnessing the segmentation outputs, authors used a variant of PointNet to detect landmarks for each individual tooth. While effective, this approach comes at a high computational cost derived from separating the process into two stages. Besides, the used dataset includes mainly regular dental models without considering complex dental models with missing teeth and one or two third molars. ALIIOS algorithm [9] combines image processing, segmentation, and DL to identify dental landmarks on IOS by synthesizing 3D patches with the output of a 2D U-Net. However, most of these methods rely on a previous segmentation of teeth, which adds considerable latency to the overall system.

3	Data construction
Existing datasets for landmark detection in 3D intraoral scans are very small, private and do not include frequent anomalies of the dentition. A comparison between these datasets is provided in Table 1. In this work, we construct a comprehensive dataset of 1,214 digital 3D dental models derived from post-processed raw scans. Each model is annotated with detailed reference landmarks and categorized into a taxonomy of dentition types to facilitate the evaluation of automatic 3D landmark detection systems under varying levels of difficulty. Our dataset does not only
 
Table 1: Comparison between datasets for landmark detection in 3D dental models.
Work (year)	Missing teeth	Third molars	Dental arch	Public	# of 3D dental models
[7] (2020)
Not mentioned	Not mentioned	Not mentioned	No	100
[9] (2022)
Yes	Yes	Upper and lower	No	405
[8] (2022)
Yes	No	Upper	No	136
[59] (2022)
Yes	Yes	Upper and lower	No	337
Ours (2025)	Yes	Yes	Upper and lower	Yes	1, 214


(a) Occlusal view.	(b) Lateral view.







(c) Facial view.	(d) Lingual view.
Figure 1: Four views of a complete dentition (16 teeth) with its corresponding 80 landmarks (5 per tooth).


provide a robust foundation for training and evaluating point cloud learning methods but also addresses a significant gap in the orthodontic field, where publicly available annotated datasets are scarce. In this regard, we make our dataset publicly available so that the community can explore new research ideas on it. This is also an attempt to encourage the orthodontic community to publish their datasets and hence allow for a fair comparison between different AI methods, attracting more practitioners to the field of computer-assisted orthodontics, and encouraging the development of new domain-specific technologies.
To enable accurate and consistent annotations, each dentition in our dataset contains five landmarks per tooth, making up to 80 landmarks in total (depending on the number of teeth present in each dentition). These landmarks represent key points for subsequent tasks, such as treatment planning, and are illustrated in Figure 1 and detailed in Table 2. Specifically, for each tooth, the five landmarks identify important anatomical structures that help guide orthodontic measurements and interventions. This meticulous labeling ensures that every model in the dataset captures essential dental characteristics, ranging from normal anatomical variations to more complex anomalies, thereby broadening the scope and applicability of the dataset for diverse research and clinical needs.
 
Table 2: Description of dental landmarks.
Landmark	Abbreviation	Description
Mesial Point	MP	Located on the mesial surface of the tooth, facing towards the midline of the dental arch, indicating the most anterior point.
Distal Point	DP	Located on the distal surface of the tooth, facing away from the midline, indicating the most posterior point.
Cusp Point	CP	The tip of a cusp, typically found on the chewing surface of teeth, mark- ing the highest point.
Facial Gingival Point	FGP	Located on the facial (or buccal) surface near the gingival margin on the cheek or lip side, indicating the gum line position.
Lingual Gingival Point	LGP	Located on the lingual (or palatal) surface near the gingival margin on the side facing the tongue, indicating the gum line position.

Table 3: Alphanumeric dentition layout.
Upper Right	Upper Left
UR8	UR7	UR6	UR5	UR4	UR3	UR2	UR1	LR1	LR2	LR3	LR4	LR5	LR6	LR7	LR8
LR8	LR7	LR6	LR5	LR4	LR3	LR2	LR1	LL1	LL2	LL3	LL4	LL5	LL6	LL7	LL8
Lower Right	Lower Left

3.1	Dentition layout and taxonomy
In this paper, we adopt the alphanumeric notation (see Table 3) to indicate each tooth. Each tooth is identified by a combination of letters and numbers indicating its position and type. For instance, the upper right first molar is denoted as UR6, while the lower left central incisor is denoted as LL1. This standardized notation facilitates clear communication and precise reference to specific teeth throughout the paper.
The 1, 214 3D models are divided into different types of dentition to further analyze the performance of the model at different levels of difficulty, something we believe is of utmost importance and missing from previous work. We introduced a taxonomy in which each dentition can be categorized following a two-digit nomenclature that indicates two features. The first digit can be considered a binary variable that indicates if the dentition contains any of the third molars, that is, UR8 and UL8 for the upper dentition and LR8 and LL8 for the lower dentition. The second digit indicates the number of missing teeth (not counting UR8, UL8, LR8 or LL8) in the dentition. Table 4 explains the dentition taxonomy and shows the count in the data set by dentition type. Dental models are centered on the origin and oriented parallel to the z axis. An example of each type of dentition is shown in Figure 2.

3.2	Teeth distribution
An important aspect of our dataset is the real-world distribution of teeth across all dental models, which reflects the natural variability observed in clinical practice. Specifically, as shown in Figure 3, our dataset includes an imbalanced distribution of teeth: less common teeth, such as the third molars, are underrepresented, while more common teeth, such as the first incisors, are almost always present. This imbalance introduces additional challenges for learning methods, as correctly classifying underrepresented teeth becomes more difficult compared to those that appear more frequently.

3.3	Data preprocessing
In order to feed the data to the point cloud learning methods, our 3D dental models, composed of 67, 000 points and 130, 000 faces on average, are downsampled to 10, 000 vertices, as it allows enough resolution for the point cloud models to learn the desired mapping while reducing memory and computation costs. Some original and downsampled point clouds are shown in Figure 4.
Many dental models in the dataset have missing teeth, and since we use a heatmap regression approach for landmark detection, it is necessary to account for landmarks associated with these missing teeth. Allocating these landmarks to any point within the original downsampled point cloud would be illogical, as the corresponding teeth are absent. To address this issue, we introduce an additional point located outside the downsampled point cloud, called the "null point" (see Figure 5). This point serves as a placeholder for the landmarks of missing teeth, ensuring that the model can handle incomplete dental models effectively. The location of the null point is determined in a data-dependent manner and is
 
 
Figure 2: Examples of each dentition type based on our proposed taxonomy. The rows represent the presence or absence of third molars: the first row (0) indicates no third molars are present, while the second row (1) indicates the presence of at least one third molar. The columns correspond to the number of missing teeth in the dentition.


Table 4: Dentition taxonomy and dataset count.
Dent type	Description	# of 3D dental models
00	No third molar, no missing teeth	668
01	No third molar, one missing tooth	85
02	No third molar, two missing teeth	106
03	No third molar, three missing teeth	14
04	No third molar, four or more missing teeth	10
10	One or two third molars, no missing teeth	211
11	One or two third molars, one missing tooth	44
12	One or two third molars, two missing teeth	59
13	One or two third molars, three missing teeth	9
14	One or two third molars, four or more missing teeth	8


computed as follows: Let mb represent the largest extent of the bounding box enclosing the point cloud across the three axes, and let c denote the centroid of the point cloud. The null point n is then placed at n = c + mb · (0, 1, 0), ensuring it is positioned outside the point cloud along the positive y-axis. By adding this point, each point cloud will contain 10001 vertices in total.

3.4	Ground-truth generation
For our model to be able to detect landmarks, we define a Gaussian distance field for each landmark over the point cloud, where higher values are associated with closeness to the corresponding landmark. Namely, for each landmark lk and for each point in the input point cloud xi, we compute its heatmap value with respect to landmark k as:

 
hki = exp
 
d(xi, lk)2
—	2σ2
 
(1)
 
 

Figure 3: Count of each tooth in the dataset. The green dashed line represents the total number of dental models, i.e., the maximum number of possible appearances of each tooth.



Figure 4: Original (top row) and downsampled (bottom row) point clouds.




where d is the Euclidean distance function, and σ is a hyperparameter representing the standard deviation of the Gaussian distance field, which we set to 2mm.
Furthermore, to enable the classification of tooth presence or absence in CHaR versions, we define a binary label yt for each tooth t, where yt = 1 indicates the presence of tooth t and yt = 0 indicates its absence. These binary labels are generated based on the dental anatomy provided for each input sample.
 
 	 

(a) Occlusal view.	(b) Lateral view.





(c) Facial view.	(d) Lingual view.
Figure 5: 4 views of an input sample: A point cloud with 10000 points randomly subsampled from a 3D dental model (blue) plus the added "null" point (red).

4	Methodology
In this section, we first formally define our problem in Section 4.1. Then, our proposed framework is introduced in Section 4.2. Finally, our implementation details are described in Section 4.3.

4.1	Problem statement
Given an input point cloud P = {xi ∈ R3 | i = 1, ..., N } (where N = 10001, including the "null" point), our task is to predict the positions L = {lk ∈ R3 | k = 1, ..., 80} of 80 dental landmarks. Our method employs a heatmap-based approach to encode the likelihood of each point xi being a particular landmark. For each landmark k, the heatmap Hˆk = {hˆki | i = 1, ..., N } represents these likelihoods. Then, the predicted landmark positions are defined as:

Lˆ = {ˆlk = argmax(Hˆk ) | k = 1, ..., 80}.	(2)
xi∈P

To train the model, we employ two loss functions: one for the landmark localization task and another for the pres- ence/absence classification task.

Heatmap regression loss
For the heatmap regression task, we minimize the mean squared error (MSE) loss between the ground truth heatmaps Hk = {hki | i = 1, ..., N } and the predicted heatmaps Hˆk . Specifically, for each landmark k, the MSE loss is computed as:
 
 
Figure 6: The pipeline of our proposed method: Given an input point cloud of a 3D dental model, (1) the point cloud encoder extracts the internal features, then these features are fed into the next two stages simultaneously. (2) The initial landmark regression stage and (3) the presence classification of teeth are performed in two separate branches. Finally,
(4) the conditioned heatmap regression module makes use of the presence classification to adjust the initial landmark regression.



 

LMSE =
 
1
K
k=1
 
N

N
i=1
 
2
hki − hˆki
 

,	(3)
 
where K = 80 is the total number of landmarks, and N = 10001 is the number of points in the point cloud. This loss encourages the model to produce heatmaps that accurately represent the spatial distribution of each landmark, ensuring the predicted landmarks Lˆ derived from the maxima of the heatmaps closely match the ground truth landmarks L.
Teeth presence classification loss
For the presence/absence classification task, we define a binary label yt ∈ {0, 1} for each tooth t, where yt = 1 indicates the presence of the tooth and yt = 0 indicates its absence. The model predicts probabilities yˆt ∈ [0, 1] for each tooth t, and we optimize these predictions using the binary cross-entropy (BCE) loss:

LBCE = −  1 Σ [y log(yˆ ) + (1 − y ) log(1 − yˆ )] ,	(4)
where T is the total number of teeth under consideration. This loss ensures that the model accurately predicts the presence or absence of teeth.

Combined loss function
To jointly train the model for landmark localization and tooth presence/absence classification, we define a combined loss function with separate weights for each task:

L = λregLMSE + λclsLBCE,	(5)

where λreg and λcls are hyperparameters that control the relative importance of the localization task (LMSE) and the classification task (LBCE), respectively.
By minimizing this combined loss, the model learns to simultaneously predict accurate landmark positions and classify the presence or absence of teeth, ensuring robust performance across both tasks.

4.2	Network
In this paper, we propose CHaRNet (Conditioned Heatmap Regression Network), an end-to-end DL-based landmark detection method for 3D dental models, which does not rely on previous segmentation of teeth. It is able to handle missing teeth and fasten inference, making it suitable for clinical applications. Our proposed method is shown in Figure 6, where (1) the point cloud encoder extracts the internal representation, (2) the point cloud decoder plus the heatmap regression head performs the initial heatmap-based regression of landmarks, (3) the classification head predicts the
 
 


Figure 7: Overview of the Conditioned Heatmap Regression (CHaR) module. Initial heatmaps are adjusted by weighting the original point cloud likelihoods with the predicted tooth presence probabilities pt and the null point likelihoods with the absence probabilities (1 − pt).

presence or abscense of every teeth, and (4) our novel conditioned heatmap regression module adjusts every heatmap’s scores based on the presence classification of its associated teeth.

Encoder
The point cloud encoder is in charge of extracting a reduced internal representation of the whole input point cloud. Point cloud learning methods usually perform this dimensionality reduction and feature selection hierarchically through set abstraction operations [?, ?]. This learned high-level features will subsequently be fed to the decoder plus heatmap regression head, and the teeth presence classification.

Decoder + heatmap regression
The decoder reverses the encoding process by progressively interpolating the abstracted features back to the original number of points in the input point cloud. This step ensures that the high-level features extracted by the encoder are effectively propagated back to the spatial resolution of the input. Subsequently, the heatmap regressor computes the initial likelihood that each point in the point cloud is associated with a specific landmark.
This is achieved by generating a set of heatmaps, where each heatmap corresponds to a landmark and encodes the likelihood distribution over all points in the point cloud. Formally, for each landmark k we obtain the initial heatmap
Hˆ′ = {hˆ′  | i = 1, ..., N }, which will be used to compute the final heatmap Hˆk as explained below.
k	ki
 
Teeth presence classification
The classification head of our network employs a multi-layer sequential architecture for robust feature extraction and class prediction. It takes the high-level features extracted by the encoder as input and processes them through three fully connected layers, each interleaved with batch normalization, ReLU activation, and dropout for regularization. The first two layers project the input into a 256-dimensional feature space, stabilizing training with batch normalization and introducing non-linearity through ReLU activations. Dropout is applied after each activation to mitigate overfitting. The final layer maps the features into an output space of 16 classes, corresponding to the number of teeth in a dental arch, producing the desired logits. A sigmoid function is then applied to these logits, generating a probability vector p = [p1, p2, ..., p16], where each pi represents the likelihood of the presence of a corresponding tooth. These probabilities are subsequently used to condition the final heatmap regression.

Conditioned Heatmap Regression (CHaR)
In general, when a tooth is missing, the vanilla landmark regression network is not explicitly designed to map all landmarks associated with that tooth to the null point. To address this issue, the conditioned heatmap regression (CHaR) module adjusts the initial landmark regression to take into account the predicted presence probability of each tooth. For clarity, each landmark k is denoted using a tuple of two digits (t, g), where the first digit t = 1, 2, ..., 16 indicates the tooth associated with the landmark, and the second digit g = 1, 2, ..., 5 indicates the specific landmark within the group of 5 landmarks associated with tooth t. For example, landmark 16 (the 1st landmark of the 4th tooth) is denoted as (4, 1) in this notation.
The intuition behind the CHaR module is to condition the likelihood values of each landmark (t, g) on the predicted presence probability pt of the associated tooth. Specifically, for each landmark (t, g), the likelihood of every point in the original point cloud (excluding the null point) is weighted by pt, while the null point is weighted by the absence probability (1 − pt). Formally, assuming that the null point is the last point in the input point cloud, the final heatmap values for landmark (t, g) are computed as:

 
Hˆt,g = {h′
 
· pt | i = 1, ..., N − 1} ∪ {h′
 
· (1 − pt)}	(6)
 
In this formulation, the weights pt and (1 − pt) act as a switch: when pt is high (tooth t is present), the likelihoods of points in the original point cloud dominate, encouraging the landmarks associated with tooth t to remain in the point cloud. In contrast, when pt is low (tooth t is absent), the probability of a null point dominates, shifting the associated landmarks to the null point. Figure 7 provides a schematic representation of our CHaR module. For clarity, we denote
the set of likelihood values for the original points (excluding the null point) as h(t,g) = {h′	| i = 1, ..., N − 1},
and the likelihood value for the null point as n(t,g) = h′	.
This conditioning ensures that the network dynamically adjusts the landmark predictions based on the presence of the tooth, improving the robustness in scenarios where some teeth are missing.

4.3	Implementation details Dataset splitting
We follow the typical three-way holdout setup for model comparison and performance estimation [61], that is, the 1,214
dental models are divided into train (70%), validation (15%) and test (15%) sets. The training set is used to adjust the weights of the neural network, the validation set is used to adjust the network and training hyper-parameters, and the test set is used to estimate model performance on previously unseen data. Given that the 1,214 dental models contain maxilar and mandibular samples from 923 patients, we split the patients into three subsets and subsequently assign their corresponding dental models to avoid any correlation between them.

Training details
All base models and CHaR versions in the results section were trained by minimizing the MSE loss (Formula 3) and the combined loss (Formula 5), respectively, using the Adam optimizer [62] for a total of 100 epochs using a batch size of
16. The initial learning rate is set to 0.005 and is progressively reduced using the cosine learning rate decay scheduler. To prevent overfitting, weight decay was set to 0.003, serving as a regularization term. Additionally, dropout rates were tuned between 0.3 and 0.5 across different model configurations to enhance generalization. Pytorch [63] was used as the DL framework to conduct all experiments. With respect to hyper-parameters controlling the relative importance
 
in the final loss value in the CHaR versions, λreg and λcls are set to 0.001 and 1 respectively, given the natural bigger quantity of the landmark localization loss.

5	Experiments
In this section, we validate the effectiveness of our CHaR module for the challenging task of 3D landmark detection in dentition models, particularly in cases with missing teeth. We demonstrate that integrating the CHaR module into various point cloud learning models significantly improves localization performance. To ensure robustness, we evaluated across five point cloud processing architectures spanning different families, comparing baseline models with their CHaR-augmented versions.
A description of the used evaluation metrics is introduced in Section 5.1, followed by a detailed presentation of the results in Section 5.2.

5.1	Evaluation metrics
In order to evaluate the performance of the tooth landmark detection and tooth classification in 3D dental models, we have used the mean Euclidean distance error (MEDE) and the mean success ratio (MSR) for the landmark detection task, and F1 score to also evaluate the classification performance.
Let N be the number of landmarks in a dental model. Then, for each landmark i ∈ [1, N ], we can define the Euclidean distance Di as:

 
 
3
Di =
j=1
 
(yij − yˆij)2	(7)
 

where yi and yˆi are the actual and predicted landmarks, respectively. Subsequently, we define the MEDE as follows.

ΣN  Di
 


Finally, the MSR is expressed as
 
MEDE =   i=1	
N
 
(8)
 

 

MSR =
 
1 Σ
 

δi × 100%,	δi =
 
 1  if Di ≤ r,
 

(9)
 
N
i=1
 
0  if Di > r
 
where r is the radius in mm of the sphere that delimits the space for a predicted landmark to be considered a success. In our case, we define r = 1mm.
Finally, since our CHaR module makes use of the teeth classification to condition the final landmark location, we also considered the F1 score to evaluate the performance in this regard:

 
F 1t
 
	2 ∗ TPt	
=
2 ∗ TPt + FPt + FNt
 
(10)
 
where TPt, FPt, FNt are true positives (TP), false positives (FP), and false negatives (FN) for each tooth t. To compute the overall or macro-averaged, we take the mean across all teeth:

 
F 1	=  1 Σ F 1
 

(11)
 


where T = 16 is the total number of teeth.
 
macro
 
T	t
t=1
 
Table 5: Comparison of each neural network base model with its corresponding Conditioned Heatmap Regression (CHaR) version in terms of Mean Euclidean Distance Error (MEDE) expressed in millimeters (mm). The results are expressed across each dentition type and aggregated using the macro and micro average.
	Models
	PointNet++	DGCNN	PointTransformer	PointNeXt	PointMLP
Dent type	Base	CHaR	Base	CHaR	Base	CHaR	Base	CHaR	Base	CHaR
00	0.82	0.86	1.26	0.74	0.77	0.59	0.81	0.70	0.65	0.51
01	3.37	2.20	3.66	2.66	2.66	1.86	2.64	2.11	2.29	1.74
02	4.52	3.25	5.38	3.33	5.54	2.04	3.74	3.04	4.32	3.09
03	7.83	5.68	7.81	4.47	4.57	6.90	3.24	2.98	6.01	6.73
04	4.26	1.93	9.32	5.15	6.87	2.79	4.25	5.71	4.39	3.76
10	1.09	1.04	1.10	0.87	0.99	0.82	0.89	0.84	0.88	0.62
11	4.43	4.37	5.00	3.34	3.29	2.49	4.24	3.04	2.59	1.75
12	6.21	4.56	7.96	5.95	6.45	6.83	6.00	4.69	4.95	4.89
13	7.65	7.60	8.85	8.97	10.55	2.66	2.92	4.46	1.51	4.83
14	17.13	21.54	17.64	16.39	8.57	12.44	15.53	14.47	15.37	11.54
macro-average
micro-average	5.73
1.99	5.31
1.71	6.80
2.46	5.19
1.67	5.03
1.90	3.94
1.40	4.42
1.72	4.20
1.46	4.30
1.57	3.94
1.28


5.2	Results
The quantitative 3D landmark detection results for the five point cloud base models, namely PointNet++, DGCNN, PointTransformer, PointNeXt, and PointMLP with and without CHaR, are compared in Table 5 and Table 6, using MEDE and MSR as evaluation metrics, respectively. Three key observations can be made. First, the CHaR-augmented versions consistently outperform their base counterparts, demonstrating the effectiveness of the proposed method. Second, the models perform well on typical dental models, whereas cases involving missing teeth pose a significantly more challenging task for automatic 3D landmark detection. This underscores the importance of evaluating landmark detection systems in scenarios that involve missing teeth. Finally, the CHaR-augmented version of PointMLP (CHaRNet) emerges as the best-performing method in all comparisons.
In terms of MEDE, CHaRNet achieves a remarkable 0.51 mm in typical dental models, 1.28 mm in all dentition models, and 3.94 mm when averaged across all dentition types. For MSR, it achieves an impressive 87.06% in typical dental models, 82.40% in all dentition models, and 68.95% when averaged across all dentition types.
Furthermore, qualitative results are shown in Figure 8 between the base and CHaR models, and the ground truth for different dental models. The red and green squares show wrong and correct landmark localization, respectively. This comparison clearly reveals superior performance from the CHaR models in complex dental models with missing teeth compared to their base model counterparts.

6	Discussion

The results demonstrate the effectiveness of the proposed CHaR module in improving 3D landmark detection for point cloud-based models, particularly in challenging scenarios involving incomplete dental models. Across multiple architectures, CHaR-augmented models consistently outperform their baseline counterparts on both MEDE and MSR, underscoring the impact of enhancing the encoding of local geometric information. This improvement is especially evident in cases with missing teeth, where baseline models often fail to capture the subtle cues necessary for accurate landmark location.
When comparing performance across different dentition types, all models perform better on typical dental models than on incomplete ones. For instance, CHarNet achieves a MEDE of 0.51mm and an MSR of 87.06% on typical models, highlighting its ability to handle simpler cases with precision. However, macroaveraged results that include more complex scenarios exhibit a higher error (3.94mm MEDE) and lower success ratio (68.95% MSR), suggesting that the absence of certain teeth disrupts key structural patterns used for landmark detection. Although the CHaR module significantly narrows this performance gap, there remains a clear need for methods better equipped to handle incomplete geometries.
 
Table 6: Comparison of each neural network base model with its corresponding Conditioned Heatmap Regression (CHaR) version in terms of Mean Success Rate (MSR) expresed in % of landmarks located withing a sphere of 1mm radius. The results are expressed across each dentition type and aggregated using the macro and micro average.
	Models
	PointNet++	DGCNN	PointTransformer	PointNeXt	PointMLP
Dent type	Base	CHaR	Base	CHaR	Base	CHaR	Base	CHaR	Base	CHaR
00	68.58	68.16	75.32	77.46	77.63	83.38	70.12	73.70	78.35	87.06
01	52.71	54.90	62.92	66.04	70.83	76.67	60.52	63.44	70.00	79.48
02	54.33	57.08	59.00	66.00	60.50	76.42	59.00	65.75	63.33	78.42
03	33.75	36.88	43.75	52.50	64.38	65.63	54.38	55.63	56.88	53.75
04	37.50	48.75	27.50	36.25	36.25	60.00	41.25	31.25	47.50	48.75
10	60.00	60.83	72.03	72.20	69.76	76.42	62.84	66.72	70.57	80.47
11	39.79	44.38	50.63	56.04	58.33	66.46	45.63	53.33	61.88	73.33
12	46.75	50.88	52.38	60.38	56.25	64.88	47.25	56.38	64.13	73.25
13	35.00	38.75	37.50	50.00	20.00	65.00	50.00	48.75	63.75	66.25
14	18.75	13.75	17,50	26.25	50.00	40.00	16.25	23.75	41.25	48.75
macro-average
micro-average	44.72
61.46	47.33
62.03	50.35
69.31	56.31
72.12	56.39
71.52	67.48
78.71	50.72
64.29	53.87
68.48	61.76
72.97	68.95
82.40


Figure 8: Qualitative comparison between base models (top row), CHaR-augmented models (middle row) and ground truth (bottom row). Each column represents one of the point cloud learning models. The red and green squares represents wrong and correct landmark localizations, respectively.


Among the point cloud learning models evaluated, PointMLP demonstrates the most pronounced gains with the addition of CHaR module, indicating that PointMLP’s architecture may be particularly compatible with CHaR’s approach to capturing and processing local features. Other architectures such as PointNet++ and PointNeXt also benefit from the inclusion of the CHaR module, but do not reach the same level of improvement. This disparity may be attributed to differences in how each model balances local and global feature extraction.
Qualitative analyses reinforce these quantitative findings. Visual inspections, as shown in Figure 8, reveal that while baseline models frequently misplace landmarks in regions with missing teeth, CHaR-augmented models are more robust, even when direct geometric cues are limited. The inferred landmark placements in these complex regions highlight the CHaR module’s capability to reason about plausible geometries in the face of missing data.
 
Despite these promising results, certain limitations suggest directions for future work. Although the CHaR module significantly enhances performance in incomplete cases, current MEDE and MSR values leave room for improvement. Approaches that combine the CHaR module with additional processing steps or other network modules could be explored to address more severe or irregular forms of dental model incompleteness. Furthermore, because the CHaR module appears to rely on specific architectural features of PointMLP, efforts to generalize it to a broader range of network designs could expand its utility. Finally, while the focus here is on landmark detection accuracy, real-time processing and scalability to larger point clouds remain open questions that merit further investigation.

7	Conclusion
This work introduces the CHaRNet and the CHaR module, a novel approach to enhance 3D landmark detection in point cloud-based models, particularly for dental applications. The results demonstrate that the CHaR module significantly improves performance across multiple architectures, as evidenced by consistent reductions in MEDE and increases in MSR for both typical dental models and cases involving missing teeth. By effectively grouping landmarks associated with each tooth, the CHaR module enables the model to adapt dynamically based on the tooth’s presence or absence, assigning landmarks to a null point if the tooth is missing or accurately localizing them within the dentition if the tooth is present. This capability addresses a key challenge in dental landmark detection, where structural irregularities and missing teeth often hinder model performance.
The superior performance of the CHaR-augmented version in every model, particularly in PointMLP, highlights its suitability for incorporating the proposed module, achieving state-of-the-art results with a MEDE of 0.51 mm in typical dental models and a robust performance with 1.28 mm across all dentition types. These findings validate the efficacy of the CHaR module in improving the accuracy and robustness of landmark detection, particularly in challenging scenarios.
To contribute to the advancement of research in this domain, we are making the dataset and the code used in this study publicly available. This effort aims to address the lack of "open spirit" that is prevalent in fields such as orthodontics, in contrast to the artificial intelligence community, where openness and collaboration are common. We hope that this initiative will encourage others in the orthodontic community to share their datasets, allowing fair comparisons between AI methods, attracting more practitioners to the field of computer-assisted orthodontics, and fostering the development of new domain-specific technologies.
Future work will explore ways to further enhance the performance of the module in complex cases, generalize its application to a wider range of architectures, and evaluate its scalability and efficiency in larger datasets or real-time applications. By addressing these challenges, this research aims to contribute to the development of more effective and versatile 3D landmark detection systems.

References
[1]		Yokesh Kumar, Ravi Janardan, and Brent Larson. Automatic feature identification in dental meshes. Computer- Aided Design and Applications, 9(6):747–769, 2012.
[2]	Sukun Tian, Ning Dai, Bei Zhang, Fulai Yuan, Qing Yu, and Xiaosheng Cheng. Automatic classification and segmentation of teeth on 3d dental model using hierarchical deep learning networks. Ieee Access, 7:84817–84828, 2019.
[3]	Guangshun Wei, Zhiming Cui, Jie Zhu, Lei Yang, Yuanfeng Zhou, Pradeep Singh, Min Gu, and Wenping Wang. Dense representative tooth landmark/axis detection network on 3d model. Computer Aided Geometric Design, 94:102077, 2022.
[4]		Joon Im, Ju-Yeong Kim, Hyung-Seog Yu, Kee-Joon Lee, Sung-Hwan Choi, Ji-Hoi Kim, Hee-Kap Ahn, and Jung-Yul Cha. Accuracy and efficiency of automatic tooth segmentation in digital dental models using deep learning. Scientific reports, 12(1):9429, 2022.
[5]	Chunfeng Lian, Li Wang, Tai-Hsien Wu, Fan Wang, Pew-Thian Yap, Ching-Chang Ko, and Dinggang Shen. Deep multi-scale mesh feature learning for automated labeling of raw dental surfaces from 3d intraoral scanners. IEEE transactions on medical imaging, 39(7):2440–2450, 2020.
[6]		Chunfeng Lian, Li Wang, Tai-Hsien Wu, Mingxia Liu, Francisca Durán, Ching-Chang Ko, and Dinggang Shen. Meshsnet: Deep multi-scale mesh feature learning for end-to-end tooth labeling on 3d dental surfaces. In Medical Image Computing and Computer Assisted Intervention–MICCAI 2019: 22nd International Conference, Shenzhen, China, October 13–17, 2019, Proceedings, Part VI 22, pages 837–845. Springer, 2019.
 
[7]		Diya Sun, Yuru Pei, Peixin Li, Guangying Song, Yuke Guo, Hongbin Zha, and Tianmin Xu. Automatic tooth segmentation and dense correspondence of 3d dental model. In Medical Image Computing and Computer Assisted Intervention–MICCAI 2020: 23rd International Conference, Lima, Peru, October 4–8, 2020, Proceedings, Part IV 23, pages 703–712. Springer, 2020.
[8]	Tai-Hsien Wu, Chunfeng Lian, Sanghee Lee, Matthew Pastewait, Christian Piers, Jie Liu, Fan Wang, Li Wang, Chiung-Ying Chiu, Wenchi Wang, et al. Two-stage mesh deep learning for automated tooth segmentation and landmark localization on 3d intraoral scans. IEEE transactions on medical imaging, 41(11):3158–3166, 2022.
[9]		Baptiste Baquero, Maxime Gillot, Lucia Cevidanes, Najla Al Turkestani, Marcela Gurgel, Mathieu Leclercq, Jonas Bianchi, Marilia Yatabe, Antonio Ruellas, Camila Massaro, et al. Automatic landmark identification on intraoralscans. In Workshop on Clinical Image-Based Procedures, pages 32–42. Springer, 2022.
[10]	Xiaozhi Chen, Huimin Ma, Ji Wan, Bo Li, and Tian Xia. Multi-view 3d object detection network for autonomous driving. In Proceedings of the IEEE conference on Computer Vision and Pattern Recognition, pages 1907–1915, 2017.
[11]	Asako Kanezaki, Yasuyuki Matsushita, and Yoshifumi Nishida. Rotationnet: Joint object categorization and pose estimation using multiviews from unsupervised viewpoints. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 5010–5019, 2018.
[12]	Alex H Lang, Sourabh Vora, Holger Caesar, Lubing Zhou, Jiong Yang, and Oscar Beijbom. Pointpillars: Fast encoders for object detection from point clouds. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pages 12697–12705, 2019.
[13]	Hang Su, Subhransu Maji, Evangelos Kalogerakis, and Erik Learned-Miller. Multi-view convolutional neural networks for 3d shape recognition. In Proceedings of the IEEE international conference on computer vision, pages 945–953, 2015.
[14]	Daniel Maturana and Sebastian Scherer. Voxnet: A 3d convolutional neural network for real-time object recognition. In 2015 IEEE/RSJ international conference on intelligent robots and systems (IROS), pages 922–928. IEEE, 2015.
[15]	Shuran Song, Fisher Yu, Andy Zeng, Angel X Chang, Manolis Savva, and Thomas Funkhouser. Semantic scene completion from a single depth image. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 1746–1754, 2017.
[16]	Christopher Choy, JunYoung Gwak, and Silvio Savarese. 4d spatio-temporal convnets: Minkowski convolutional neural networks. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pages 3075–3084, 2019.
[17]	Benjamin Graham, Martin Engelcke, and Laurens Van Der Maaten. 3d semantic segmentation with submanifold sparse convolutional networks. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 9224–9232, 2018.
[18]	Peng-Shuai Wang, Yang Liu, Yu-Xiao Guo, Chun-Yu Sun, and Xin Tong. O-cnn: Octree-based convolutional neural networks for 3d shape analysis. ACM Transactions On Graphics (TOG), 36(4):1–11, 2017.
[19]	Charles R Qi, Hao Su, Kaichun Mo, and Leonidas J Guibas. Pointnet: Deep learning on point sets for 3d classification and segmentation. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 652–660, 2017.
[20]	Charles Ruizhongtai Qi, Li Yi, Hao Su, and Leonidas J Guibas. Pointnet++: Deep hierarchical feature learning on point sets in a metric space. Advances in neural information processing systems, 30, 2017.
[21]	Guocheng Qian, Yuchen Li, Houwen Peng, Jinjie Mai, Hasan Hammoud, Mohamed Elhoseiny, and Bernard Ghanem. Pointnext: Revisiting pointnet++ with improved training and scaling strategies. Advances in neural information processing systems, 35:23192–23204, 2022.
[22]	Xu Ma, Can Qin, Haoxuan You, Haoxi Ran, and Yun Fu. Rethinking network design and local geometry in point cloud: A simple residual mlp framework. arXiv preprint arXiv:2202.07123, 2022.
[23]	Yue Wang, Yongbin Sun, Ziwei Liu, Sanjay E Sarma, Michael M Bronstein, and Justin M Solomon. Dynamic graph cnn for learning on point clouds. ACM Transactions on Graphics (tog), 38(5):1–12, 2019.
[24]	Martin Simonovsky and Nikos Komodakis. Dynamic edge-conditioned filters in convolutional neural networks on graphs. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 3693–3702, 2017.
[25]	Hengshuang Zhao, Li Jiang, Chi-Wing Fu, and Jiaya Jia. Pointweb: Enhancing local neighborhood features for point cloud processing. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pages 5565–5573, 2019.
 
[26]	Guohao Li, Matthias Muller, Ali Thabet, and Bernard Ghanem. Deepgcns: Can gcns go as deep as cnns? In
Proceedings of the IEEE/CVF international conference on computer vision, pages 9267–9276, 2019.
[27]	Hengshuang Zhao, Li Jiang, Jiaya Jia, Philip HS Torr, and Vladlen Koltun. Point transformer. In Proceedings of the IEEE/CVF international conference on computer vision, pages 16259–16268, 2021.
[28]	Meng-Hao Guo, Jun-Xiong Cai, Zheng-Ning Liu, Tai-Jiang Mu, Ralph R Martin, and Shi-Min Hu. Pct: Point cloud transformer. Computational Visual Media, 7:187–199, 2021.
[29]	Yu-Qi Yang, Yu-Xiao Guo, Jian-Yu Xiong, Yang Liu, Hao Pan, Peng-Shuai Wang, Xin Tong, and Baining Guo. Swin3d: A pretrained transformer backbone for 3d indoor scene understanding. arXiv preprint arXiv:2304.06906, 2023.
[30]	Damien Robert, Hugo Raguet, and Loic Landrieu. Efficient 3d semantic segmentation with superpoint transformer. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 17195–17204, 2023.
[31]	Xiaoyang Wu, Yixing Lao, Li Jiang, Xihui Liu, and Hengshuang Zhao. Point transformer v2: Grouped vector attention and partition-based pooling. Advances in Neural Information Processing Systems, 35:33330–33342, 2022.
[32]	Xiaoyang Wu, Li Jiang, Peng-Shuai Wang, Zhijian Liu, Xihui Liu, Yu Qiao, Wanli Ouyang, Tong He, and Hengshuang Zhao. Point transformer v3: Simpler faster stronger. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 4840–4851, 2024.
[33]	Farhad Ghazvinian Zanjani, David Anssari Moin, Bas Verheij, Frank Claessen, Teo Cherici, Tao Tan, et al. Deep learning approach to semantic segmentation in 3d point cloud intra-oral scans of teeth. In International Conference on Medical Imaging with Deep Learning, pages 557–571. PMLR, 2019.
[34]		Yangyan Li, Rui Bu, Mingchao Sun, Wei Wu, Xinhan Di, and Baoquan Chen. Pointcnn: Convolution on x-transformed points. Advances in neural information processing systems, 31, 2018.
[35]	Diya Sun, Yuru Pei, Guangying Song, Yuke Guo, Gengyu Ma, Tianmin Xu, and Hongbin Zha. Tooth segmentation and labeling from digital dental casts. In 2020 IEEE 17th International Symposium on Biomedical Imaging (ISBI), pages 669–673. IEEE, 2020.
[36]	Nitika Verma, Edmond Boyer, and Jakob Verbeek. Feastnet: Feature-steered graph convolutions for 3d shape analysis. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 2598–2606, 2018.
[37]	Farhad Ghazvinian Zanjani, David Anssari Moin, Frank Claessen, Teo Cherici, Sarah Parinussa, Arash Pourtahe- rian, Svitlana Zinger, and Peter HN de With. Mask-mcnet: Instance segmentation in 3d point cloud of intra-oral scans. In Medical Image Computing and Computer Assisted Intervention–MICCAI 2019: 22nd International Conference, Shenzhen, China, October 13–17, 2019, Proceedings, Part V 22, pages 128–136. Springer, 2019.
[38]		Zhiming Cui, Changjian Li, Nenglun Chen, Guodong Wei, Runnan Chen, Yuanfeng Zhou, Dinggang Shen, and Wenping Wang. Tsegnet: An efficient and accurate tooth segmentation network on 3d dental model. Medical Image Analysis, 69:101949, 2021.
[39]		Zhijie Lin, Zhaoshui He, Xu Wang, Bing Zhang, Chang Liu, Wenqing Su, Ji Tan, and Shengli Xie. Dbganet: dual-branch geometric attention network for accurate 3d tooth segmentation. IEEE Transactions on Circuits and Systems for Video Technology, 2023.
[40]	Amani Almalki and Longin Jan Latecki. Self-supervised learning with masked autoencoders for teeth segmentation from intra-oral 3d scans. In Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision, pages 7820–7830, 2024.
[41]	Yaqian Liang, Shanshan Zhao, Baosheng Yu, Jing Zhang, and Fazhi He. Meshmae: Masked autoencoders for 3d mesh data analysis. In European Conference on Computer Vision, pages 37–54. Springer, 2022.
[42]		Chris Maes, Thomas Fabry, Johannes Keustermans, Dirk Smeets, Paul Suetens, and Dirk Vandermeulen. Feature detection on 3d face surfaces for pose normalisation and recognition. In 2010 Fourth IEEE International Conference on Biometrics: Theory, Applications and Systems (BTAS), pages 1–6. IEEE, 2010.
[43]		Ivan Sipiran and Benjamin Bustos. A robust 3d interest points detector based on harris operator. In 3DOR@ Eurographics, pages 7–14, 2010.
[44]	Afzal Godil and Asim Imdad Wagan. Salient local 3d features for 3d shape retrieval. In Three-Dimensional Imaging, Interaction, and Measurement, volume 7864, pages 275–282. SPIE, 2011.
[45]		JinJiang Li and Hui Fan. Curvature-direction measures for 3d feature detection. Science China Information Sciences, 56:1–9, 2013.
 
[46]	Alexander Toshev and Christian Szegedy. Deeppose: Human pose estimation via deep neural networks. In
Proceedings of the IEEE conference on computer vision and pattern recognition, pages 1653–1660, 2014.
[47]		Joao Carreira, Pulkit Agrawal, Katerina Fragkiadaki, and Jitendra Malik. Human pose estimation with iterative error feedback. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 4733–4742, 2016.
[48]	Yufei Xu, Jing Zhang, Qiming Zhang, and Dacheng Tao. Vitpose: Simple vision transformer baselines for human pose estimation. Advances in Neural Information Processing Systems, 35:38571–38584, 2022.
[49]	Dongkai Wang and Shiliang Zhang. Spatial-aware regression for keypoint localization. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 624–633, 2024.
[50]	Tomas Pfister, James Charles, and Andrew Zisserman. Flowing convnets for human pose estimation in videos. In
Proceedings of the IEEE international conference on computer vision, pages 1913–1921, 2015.
[51]	Jonathan J Tompson, Arjun Jain, Yann LeCun, and Christoph Bregler. Joint training of a convolutional network and a graphical model for human pose estimation. Advances in neural information processing systems, 27, 2014.
[52]	Zhenyu Shu, Shiqing Xin, Xin Xu, Ligang Liu, and Ladislav Kavan. Detecting 3d points of interest using multiple features and stacked auto-encoder. IEEE transactions on visualization and computer graphics, 25(8):2583–2596, 2018.
[53]	Ke Sun, Bin Xiao, Dong Liu, and Jingdong Wang. Deep high-resolution representation learning for human pose estimation. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pages 5693–5703, 2019.
[54]	Sen Yang, Zhibin Quan, Mu Nie, and Wankou Yang. Transpose: Keypoint localization via transformer. In
Proceedings of the IEEE/CVF international conference on computer vision, pages 11802–11812, 2021.
[55]	Jun Zhang, Yue Gao, Yaozong Gao, Brent C Munsell, and Dinggang Shen. Detecting anatomical landmarks for fast alzheimer’s disease diagnosis. IEEE transactions on medical imaging, 35(12):2524–2533, 2016.
[56]	Chunfeng Lian, Mingxia Liu, Jun Zhang, and Dinggang Shen. Hierarchical fully convolutional network for joint atrophy localization and alzheimer’s disease diagnosis using structural mri. IEEE transactions on pattern analysis and machine intelligence, 42(4):880–893, 2018.
[57]	Jun Zhang, Mingxia Liu, Li Wang, Si Chen, Peng Yuan, Jianfu Li, Steve Guo-Fang Shen, Zhen Tang, Ken- Chung Chen, James J Xia, et al. Context-guided fully convolutional networks for joint craniomaxillofacial bone segmentation and landmark digitization. Medical image analysis, 60:101621, 2020.
[58]	Haifan Gong, Luoyao Kang, Yitao Wang, Xiang Wan, and Haofeng Li. nnmamba: 3d biomedical image segmentation, classification and landmark detection with state space model. arXiv preprint arXiv:2402.03526, 2024.
[59]	Tibor Kubík and Michal Španeˇl. Robust teeth detection in 3d dental scans by automated multi-view landmarking. In Proceedings of the 15th International Joint Conference on Biomedical Engineering Systems and Technologies (BIOSTEC 2022) - BIOIMAGING, pages 24–34. INSTICC, SciTePress, 2022.
[60]	Brénainn Woodsend, Eirini Koufoudaki, Ping Lin, Grant McIntyre, Ahmed El-Angbawi, Azad Aziz, William Shaw, Gunvor Semb, Gowri Vijay Reesu, and Peter A Mossey. Development of intra-oral automated landmark recognition (alr) for dental and occlusal outcome measurements. European Journal of Orthodontics, 44(1):43–50, 2022.
[61]	Sebastian Raschka. Model evaluation, model selection, and algorithm selection in machine learning. (arXiv:1811.12808), November 2020. arXiv:1811.12808.
[62]	Diederik P Kingma and Jimmy Ba. Adam: A method for stochastic optimization. arXiv preprint arXiv:1412.6980, 2014.
[63]	Adam Paszke, Sam Gross, Francisco Massa, Adam Lerer, James Bradbury, Gregory Chanan, Trevor Killeen, Zeming Lin, Natalia Gimelshein, Luca Antiga, et al. Pytorch: An imperative style, high-performance deep learning library. Advances in neural information processing systems, 32, 2019.
