{"method": "Optimized Ensemble Double SoftMax", "baseline_error": 5.829, "best_val_error": 6.238987445831299, "improvement": -7.033581160255603, "training_time_minutes": 1.807599373658498, "epochs_trained": 66, "history": [{"epoch": 1, "train_loss": 524.1348948759191, "val_loss": 539.5299560546875, "train_metrics": {"mean_distance": 42.14350262810202, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 42.7540283203125, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 2, "train_loss": 511.67556583180146, "val_loss": 533.58525390625, "train_metrics": {"mean_distance": 41.60354030833525, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 42.51166305541992, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 3, "train_loss": 498.85936063878677, "val_loss": 519.8838500976562, "train_metrics": {"mean_distance": 41.02578668033375, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 41.92726821899414, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 4, "train_loss": 486.0845839556526, "val_loss": 513.98134765625, "train_metrics": {"mean_distance": 40.45453800874598, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 41.66308746337891, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 5, "train_loss": 472.97755342371323, "val_loss": 500.3541320800781, "train_metrics": {"mean_distance": 39.874909569235406, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 41.07843170166016, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 6, "train_loss": 462.3802544088925, "val_loss": 492.1196044921875, "train_metrics": {"mean_distance": 39.38414405373966, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 40.716416931152345, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 7, "train_loss": 446.77543550379136, "val_loss": 479.3106750488281, "train_metrics": {"mean_distance": 38.67050372852999, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 40.1157958984375, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 8, "train_loss": 432.2412432502298, "val_loss": 476.23009033203124, "train_metrics": {"mean_distance": 37.97273815379423, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 39.98334274291992, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 9, "train_loss": 421.0855551326976, "val_loss": 445.9494384765625, "train_metrics": {"mean_distance": 37.44726809333353, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 38.62834091186524, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 10, "train_loss": 411.47865564682905, "val_loss": 429.928173828125, "train_metrics": {"mean_distance": 36.95945178761202, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 37.83613052368164, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 11, "train_loss": 396.48676614200366, "val_loss": 418.9768371582031, "train_metrics": {"mean_distance": 36.23119668399586, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 37.34438705444336, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 12, "train_loss": 385.0210607192096, "val_loss": 401.12465209960936, "train_metrics": {"mean_distance": 35.68193951775046, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 36.48071823120117, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 13, "train_loss": 369.40808823529414, "val_loss": 399.45244140625, "train_metrics": {"mean_distance": 34.869545207304114, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 36.35368194580078, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 14, "train_loss": 353.85891544117646, "val_loss": 382.2228637695313, "train_metrics": {"mean_distance": 34.053115732529584, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 35.4637939453125, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 15, "train_loss": 338.95911362591914, "val_loss": 369.589013671875, "train_metrics": {"mean_distance": 33.28444570653579, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 34.83999099731445, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 16, "train_loss": 316.70477833467373, "val_loss": 325.47723388671875, "train_metrics": {"mean_distance": 32.081851173849664, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 32.52495346069336, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 17, "train_loss": 303.5359425264246, "val_loss": 301.7214416503906, "train_metrics": {"mean_distance": 31.27862750782686, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 31.138185119628908, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 18, "train_loss": 289.02767316032856, "val_loss": 269.1898590087891, "train_metrics": {"mean_distance": 30.43973496380974, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 29.33326873779297, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 19, "train_loss": 266.6154426125919, "val_loss": 268.40648498535154, "train_metrics": {"mean_distance": 29.137625301585476, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 29.273274993896486, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 20, "train_loss": 249.16972530589385, "val_loss": 241.0755645751953, "train_metrics": {"mean_distance": 28.06793414845186, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 27.547569274902344, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 21, "train_loss": 227.98978648466223, "val_loss": 236.6686981201172, "train_metrics": {"mean_distance": 26.725723939783432, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 27.264189529418946, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 22, "train_loss": 203.88025710161995, "val_loss": 231.53806762695314, "train_metrics": {"mean_distance": 25.085255342371322, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 26.91231346130371, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 23, "train_loss": 186.47307990579043, "val_loss": 202.41283264160157, "train_metrics": {"mean_distance": 23.84740425558651, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 24.978347396850587, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 24, "train_loss": 163.57363218419692, "val_loss": 181.7457305908203, "train_metrics": {"mean_distance": 22.226423263549805, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 23.443767547607422, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 25, "train_loss": 150.29808986888213, "val_loss": 194.10128784179688, "train_metrics": {"mean_distance": 21.15600529839011, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 24.333753204345705, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 26, "train_loss": 130.992857540355, "val_loss": 142.25943298339843, "train_metrics": {"mean_distance": 19.633500491871555, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 20.429967880249023, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 27, "train_loss": 111.05102987850414, "val_loss": 125.29288024902344, "train_metrics": {"mean_distance": 17.789916038513184, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 19.026313018798827, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 28, "train_loss": 103.49416328878964, "val_loss": 118.1140625, "train_metrics": {"mean_distance": 17.155125113094556, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 18.29317283630371, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 29, "train_loss": 93.48594328936409, "val_loss": 84.91654968261719, "train_metrics": {"mean_distance": 16.131024024065802, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 15.248556900024415, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 30, "train_loss": 76.10489273071289, "val_loss": 72.46846008300781, "train_metrics": {"mean_distance": 14.645717676948099, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 13.992365837097168, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 31, "train_loss": 66.21558492323932, "val_loss": 81.63413543701172, "train_metrics": {"mean_distance": 13.641371895285214, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 15.024639320373534, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 32, "train_loss": 62.2367255266975, "val_loss": 83.14112930297851, "train_metrics": {"mean_distance": 13.19054126739502, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 15.194701385498046, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 33, "train_loss": 51.37713062061983, "val_loss": 50.19853515625, "train_metrics": {"mean_distance": 11.960283840403838, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "val_metrics": {"mean_distance": 11.673478507995606, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 34, "train_loss": 48.46058699663948, "val_loss": 41.378818130493165, "train_metrics": {"mean_distance": 11.561972618103027, "within_5mm_percent": 0.0, "within_7mm_percent": 5.882352941176471}, "val_metrics": {"mean_distance": 10.531049919128417, "within_5mm_percent": 0.0, "within_7mm_percent": 5.0}, "learning_rate": 0.0008}, {"epoch": 35, "train_loss": 41.73345969705021, "val_loss": 38.958822631835936, "train_metrics": {"mean_distance": 10.746817420510684, "within_5mm_percent": 0.0, "within_7mm_percent": 8.823529411764707}, "val_metrics": {"mean_distance": 10.200338172912598, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 36, "train_loss": 40.09032002617331, "val_loss": 33.276292419433595, "train_metrics": {"mean_distance": 10.574750731973086, "within_5mm_percent": 0.0, "within_7mm_percent": 14.705882352941176}, "val_metrics": {"mean_distance": 9.602263450622559, "within_5mm_percent": 0.0, "within_7mm_percent": 0.0}, "learning_rate": 0.0008}, {"epoch": 37, "train_loss": 36.738851210650274, "val_loss": 36.63206748962402, "train_metrics": {"mean_distance": 10.30820094837862, "within_5mm_percent": 0.0, "within_7mm_percent": 10.294117647058824}, "val_metrics": {"mean_distance": 10.036696434020996, "within_5mm_percent": 0.0, "within_7mm_percent": 10.0}, "learning_rate": 0.0008}, {"epoch": 38, "train_loss": 32.73843013539034, "val_loss": 31.531091690063477, "train_metrics": {"mean_distance": 9.618866135092343, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 19.11764705882353}, "val_metrics": {"mean_distance": 9.263306045532227, "within_5mm_percent": 0.0, "within_7mm_percent": 20.0}, "learning_rate": 0.0008}, {"epoch": 39, "train_loss": 29.969632429235123, "val_loss": 26.086434173583985, "train_metrics": {"mean_distance": 9.270635885350844, "within_5mm_percent": 1.4705882352941178, "within_7mm_percent": 19.11764705882353}, "val_metrics": {"mean_distance": 8.275154876708985, "within_5mm_percent": 10.0, "within_7mm_percent": 50.0}, "learning_rate": 0.0008}, {"epoch": 40, "train_loss": 31.30322821000043, "val_loss": 19.337069511413574, "train_metrics": {"mean_distance": 9.439519321217256, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 20.58823529411765}, "val_metrics": {"mean_distance": 7.267940330505371, "within_5mm_percent": 10.0, "within_7mm_percent": 45.0}, "learning_rate": 0.0008}, {"epoch": 41, "train_loss": 27.003472889170926, "val_loss": 18.992493629455566, "train_metrics": {"mean_distance": 8.682351364808923, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 32.35294117647059}, "val_metrics": {"mean_distance": 6.991641807556152, "within_5mm_percent": 10.0, "within_7mm_percent": 70.0}, "learning_rate": 0.0008}, {"epoch": 42, "train_loss": 28.486838677350214, "val_loss": 19.097790145874022, "train_metrics": {"mean_distance": 8.903628068811754, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 26.470588235294116}, "val_metrics": {"mean_distance": 7.334204387664795, "within_5mm_percent": 10.0, "within_7mm_percent": 55.0}, "learning_rate": 0.0008}, {"epoch": 43, "train_loss": 29.696769153370578, "val_loss": 16.60788059234619, "train_metrics": {"mean_distance": 9.175673905540915, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 27.941176470588236}, "val_metrics": {"mean_distance": 6.681417083740234, "within_5mm_percent": 20.0, "within_7mm_percent": 70.0}, "learning_rate": 0.0008}, {"epoch": 44, "train_loss": 24.86657159468707, "val_loss": 14.800180053710937, "train_metrics": {"mean_distance": 8.455284791834215, "within_5mm_percent": 8.823529411764707, "within_7mm_percent": 32.35294117647059}, "val_metrics": {"mean_distance": 6.42352294921875, "within_5mm_percent": 30.0, "within_7mm_percent": 60.0}, "learning_rate": 0.0008}, {"epoch": 45, "train_loss": 23.452234885271857, "val_loss": 19.737284469604493, "train_metrics": {"mean_distance": 8.11992858437931, "within_5mm_percent": 8.823529411764707, "within_7mm_percent": 39.705882352941174}, "val_metrics": {"mean_distance": 7.37031192779541, "within_5mm_percent": 30.0, "within_7mm_percent": 50.0}, "learning_rate": 0.0008}, {"epoch": 46, "train_loss": 22.763115602381088, "val_loss": 14.00006160736084, "train_metrics": {"mean_distance": 8.049155487733728, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 38.23529411764706}, "val_metrics": {"mean_distance": 6.238987445831299, "within_5mm_percent": 25.0, "within_7mm_percent": 55.0}, "learning_rate": 0.0008}, {"epoch": 47, "train_loss": 27.068825385149786, "val_loss": 15.261796855926514, "train_metrics": {"mean_distance": 8.729645869311165, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 26.470588235294116}, "val_metrics": {"mean_distance": 6.413395500183105, "within_5mm_percent": 40.0, "within_7mm_percent": 65.0}, "learning_rate": 0.0008}, {"epoch": 48, "train_loss": 21.880517847397748, "val_loss": 17.64548263549805, "train_metrics": {"mean_distance": 7.885690661037669, "within_5mm_percent": 11.764705882352942, "within_7mm_percent": 39.705882352941174}, "val_metrics": {"mean_distance": 6.6571910858154295, "within_5mm_percent": 30.0, "within_7mm_percent": 75.0}, "learning_rate": 0.0008}, {"epoch": 49, "train_loss": 26.043380176319797, "val_loss": 20.744469261169435, "train_metrics": {"mean_distance": 8.566096894881305, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 32.35294117647059}, "val_metrics": {"mean_distance": 7.233195495605469, "within_5mm_percent": 15.0, "within_7mm_percent": 50.0}, "learning_rate": 0.0008}, {"epoch": 50, "train_loss": 26.436362434835996, "val_loss": 17.673993492126463, "train_metrics": {"mean_distance": 8.589901839985567, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 30.88235294117647}, "val_metrics": {"mean_distance": 6.981948947906494, "within_5mm_percent": 15.0, "within_7mm_percent": 70.0}, "learning_rate": 0.0008}, {"epoch": 51, "train_loss": 26.240634581621954, "val_loss": 23.277797317504884, "train_metrics": {"mean_distance": 8.455611144795137, "within_5mm_percent": 8.823529411764707, "within_7mm_percent": 39.705882352941174}, "val_metrics": {"mean_distance": 8.03705005645752, "within_5mm_percent": 5.0, "within_7mm_percent": 45.0}, "learning_rate": 0.0008}, {"epoch": 52, "train_loss": 25.404136713813333, "val_loss": 22.500250816345215, "train_metrics": {"mean_distance": 8.42112678640029, "within_5mm_percent": 2.9411764705882355, "within_7mm_percent": 38.23529411764706}, "val_metrics": {"mean_distance": 7.741897201538086, "within_5mm_percent": 25.0, "within_7mm_percent": 55.0}, "learning_rate": 0.0008}, {"epoch": 53, "train_loss": 27.818846309886258, "val_loss": 34.823041915893555, "train_metrics": {"mean_distance": 8.767807034885182, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 33.8235294117647}, "val_metrics": {"mean_distance": 7.915764427185058, "within_5mm_percent": 20.0, "within_7mm_percent": 75.0}, "learning_rate": 0.0008}, {"epoch": 54, "train_loss": 25.67847953123205, "val_loss": 27.654509353637696, "train_metrics": {"mean_distance": 8.488698538611917, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 38.23529411764706}, "val_metrics": {"mean_distance": 7.620539665222168, "within_5mm_percent": 15.0, "within_7mm_percent": 45.0}, "learning_rate": 0.0008}, {"epoch": 55, "train_loss": 24.248448484084186, "val_loss": 26.90369281768799, "train_metrics": {"mean_distance": 8.347327372607063, "within_5mm_percent": 13.235294117647058, "within_7mm_percent": 32.35294117647059}, "val_metrics": {"mean_distance": 7.802299976348877, "within_5mm_percent": 25.0, "within_7mm_percent": 50.0}, "learning_rate": 0.0008}, {"epoch": 56, "train_loss": 22.75670842563405, "val_loss": 23.704751396179198, "train_metrics": {"mean_distance": 7.981509012334487, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 44.11764705882353}, "val_metrics": {"mean_distance": 7.000947284698486, "within_5mm_percent": 30.0, "within_7mm_percent": 75.0}, "learning_rate": 0.0008}, {"epoch": 57, "train_loss": 23.568681212032544, "val_loss": 28.55290412902832, "train_metrics": {"mean_distance": 8.230736620285931, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 29.41176470588235}, "val_metrics": {"mean_distance": 7.842797374725341, "within_5mm_percent": 25.0, "within_7mm_percent": 50.0}, "learning_rate": 0.0008}, {"epoch": 58, "train_loss": 24.29802687027875, "val_loss": 18.127600288391115, "train_metrics": {"mean_distance": 8.15615286546595, "within_5mm_percent": 10.294117647058824, "within_7mm_percent": 41.1764705882353}, "val_metrics": {"mean_distance": 7.055970096588135, "within_5mm_percent": 25.0, "within_7mm_percent": 60.0}, "learning_rate": 0.0008}, {"epoch": 59, "train_loss": 26.14493117612951, "val_loss": 19.84944133758545, "train_metrics": {"mean_distance": 8.542945076437558, "within_5mm_percent": 11.764705882352942, "within_7mm_percent": 36.76470588235294}, "val_metrics": {"mean_distance": 7.055477237701416, "within_5mm_percent": 25.0, "within_7mm_percent": 60.0}, "learning_rate": 0.00056}, {"epoch": 60, "train_loss": 22.69734629462747, "val_loss": 20.24913568496704, "train_metrics": {"mean_distance": 8.110101559582878, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 30.88235294117647}, "val_metrics": {"mean_distance": 6.343776512145996, "within_5mm_percent": 40.0, "within_7mm_percent": 85.0}, "learning_rate": 0.00056}, {"epoch": 61, "train_loss": 27.195166195140164, "val_loss": 20.212699794769286, "train_metrics": {"mean_distance": 8.722148951362161, "within_5mm_percent": 4.411764705882353, "within_7mm_percent": 27.941176470588236}, "val_metrics": {"mean_distance": 7.065553760528564, "within_5mm_percent": 40.0, "within_7mm_percent": 65.0}, "learning_rate": 0.00056}, {"epoch": 62, "train_loss": 23.6926809759701, "val_loss": 22.554570579528807, "train_metrics": {"mean_distance": 8.152473225313074, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 41.1764705882353}, "val_metrics": {"mean_distance": 7.064060020446777, "within_5mm_percent": 25.0, "within_7mm_percent": 60.0}, "learning_rate": 0.00056}, {"epoch": 63, "train_loss": 22.880775956546557, "val_loss": 20.39756622314453, "train_metrics": {"mean_distance": 8.081973272211412, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 39.705882352941174}, "val_metrics": {"mean_distance": 6.82313642501831, "within_5mm_percent": 20.0, "within_7mm_percent": 80.0}, "learning_rate": 0.00056}, {"epoch": 64, "train_loss": 24.73429999631994, "val_loss": 27.904450035095216, "train_metrics": {"mean_distance": 8.383997580584358, "within_5mm_percent": 5.882352941176471, "within_7mm_percent": 36.76470588235294}, "val_metrics": {"mean_distance": 7.740721225738525, "within_5mm_percent": 15.0, "within_7mm_percent": 70.0}, "learning_rate": 0.00056}, {"epoch": 65, "train_loss": 28.788357622483197, "val_loss": 17.46981611251831, "train_metrics": {"mean_distance": 8.862181411069983, "within_5mm_percent": 10.294117647058824, "within_7mm_percent": 39.705882352941174}, "val_metrics": {"mean_distance": 6.604787826538086, "within_5mm_percent": 25.0, "within_7mm_percent": 65.0}, "learning_rate": 0.00056}, {"epoch": 66, "train_loss": 24.977777424980612, "val_loss": 19.573202323913574, "train_metrics": {"mean_distance": 8.376581444459802, "within_5mm_percent": 7.352941176470588, "within_7mm_percent": 42.64705882352941}, "val_metrics": {"mean_distance": 6.940215396881103, "within_5mm_percent": 30.0, "within_7mm_percent": 60.0}, "learning_rate": 0.00056}], "optimized_config": {"num_ensembles": 5, "threshold_ratios": [0.08, 0.12, 0.16, 0.2, 0.24], "temperatures": [1.5, 1.8, 2.1, 2.4, 2.7], "weight_ratios": [0.65, 0.7, 0.75, 0.8, 0.85], "smart_ensemble_weights": true, "candidate_points": 384, "min_points_ratio": 0.1}}