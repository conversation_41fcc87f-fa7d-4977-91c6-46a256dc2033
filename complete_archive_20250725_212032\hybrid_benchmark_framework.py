#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合基准测试框架 - 结合已有模型和新训练的主流模型
Hybrid Benchmark Framework - Combining Existing Models and Newly Trained Mainstream Models
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import matplotlib.pyplot as plt
import pandas as pd
import json
import os
from sklearn.model_selection import train_test_split
from torch.utils.data import Dataset, DataLoader
import time

# 设置样式
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

class SimpleDataset(Dataset):
    """简单数据集"""
    
    def __init__(self, point_clouds, keypoints, num_points=50000):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.num_points = num_points
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        pc = self.point_clouds[idx].copy()
        kp = self.keypoints[idx].copy()
        
        # 重采样点云
        if len(pc) != self.num_points:
            if len(pc) > self.num_points:
                indices = np.random.choice(len(pc), self.num_points, replace=False)
                pc = pc[indices]
            else:
                indices = np.random.choice(len(pc), self.num_points, replace=True)
                pc = pc[indices]
        
        return torch.FloatTensor(pc), torch.FloatTensor(kp)

# 主流模型实现
class SimplePointNet(nn.Module):
    """简化的PointNet"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super().__init__()
        self.num_keypoints = num_keypoints
        
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(1024)
        
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        
        x = torch.max(x, 2)[0]
        
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

class AdaptiveKeypointModel(nn.Module):
    """您的自适应模型"""
    
    def __init__(self, num_points=50000, num_keypoints=12, architecture_type='balanced'):
        super().__init__()
        self.num_keypoints = num_keypoints
        self.architecture_type = architecture_type
        
        if architecture_type == 'lightweight':
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(256, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, 128), nn.ReLU(), nn.Dropout(0.1),
                nn.Linear(128, self.num_keypoints * 3)
            )
            
        elif architecture_type == 'balanced':
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(512, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, self.num_keypoints * 3)
            )
            
        elif architecture_type == 'enhanced':
            self.feature_extractor = nn.Sequential(
                nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(),
                nn.Conv1d(64, 128, 1), nn.BatchNorm1d(128), nn.ReLU(),
                nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(),
                nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(),
                nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU(),
            )
            self.global_conv = nn.Conv1d(1024, 512, 1)
            self.predictor = nn.Sequential(
                nn.Linear(512, 1024), nn.ReLU(), nn.Dropout(0.4),
                nn.Linear(1024, 512), nn.ReLU(), nn.Dropout(0.3),
                nn.Linear(512, 256), nn.ReLU(), nn.Dropout(0.2),
                nn.Linear(256, self.num_keypoints * 3)
            )
        
        # 相互辅助机制
        mutual_dim = min(256, max(64, self.num_keypoints * 8))
        self.mutual_assistance = nn.Sequential(
            nn.Linear(self.num_keypoints * 3, mutual_dim),
            nn.ReLU(), nn.Dropout(0.2),
            nn.Linear(mutual_dim, mutual_dim // 2),
            nn.ReLU(),
            nn.Linear(mutual_dim // 2, self.num_keypoints * 3)
        )
    
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        features = self.feature_extractor(x)
        global_features = self.global_conv(features)
        global_feat = torch.max(global_features, 2)[0]
        
        initial_kp = self.predictor(global_feat)
        assistance = self.mutual_assistance(initial_kp)
        final_kp = initial_kp + 0.3 * assistance
        final_kp = final_kp.view(batch_size, self.num_keypoints, 3)
        
        return final_kp

class HybridBenchmarkFramework:
    """混合基准测试框架"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️ 使用设备: {self.device}")
        
        # 加载数据集
        self.load_dataset()
        
        # 扫描已有模型
        self.scan_existing_models()
        
        # 主流模型配置
        self.mainstream_models = {
            'PointNet_Baseline': SimplePointNet,
        }
        
        self.results = {}
    
    def load_dataset(self):
        """加载数据集"""
        print("📥 加载数据集...")
        
        data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
        self.point_clouds = data['point_clouds']
        self.keypoints_57 = data['keypoints_57']
        
        # 创建数据划分
        indices = np.arange(len(self.point_clouds))
        _, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
        
        self.test_pc = self.point_clouds[test_indices]
        self.test_kp_57 = self.keypoints_57[test_indices]
        
        print(f"✅ 数据加载完成: 测试集 {len(self.test_pc)} 样本")
    
    def scan_existing_models(self):
        """扫描已有的模型文件"""
        print("🔍 扫描已有的模型文件...")
        
        self.existing_models = []
        
        for filename in os.listdir('.'):
            if filename.startswith('best_') and filename.endswith('.pth'):
                parts = filename.replace('best_', '').replace('.pth', '').split('_')
                if len(parts) == 2:
                    kp_str, arch = parts
                    if kp_str.endswith('kp'):
                        kp_count = int(kp_str[:-2])
                        self.existing_models.append({
                            'filename': filename,
                            'keypoints': kp_count,
                            'architecture': arch,
                            'path': filename,
                            'type': 'existing'
                        })
        
        print(f"✅ 找到 {len(self.existing_models)} 个已有模型")
    
    def evaluate_existing_model(self, model_info):
        """评估已有模型"""
        
        kp_count = model_info['keypoints']
        architecture = model_info['architecture']
        model_path = model_info['path']
        
        print(f"🔄 评估已有模型: {kp_count}点 {architecture}架构...")
        
        try:
            # 创建模型
            model = AdaptiveKeypointModel(
                num_points=50000,
                num_keypoints=kp_count,
                architecture_type=architecture
            )
            
            # 加载权重
            checkpoint = torch.load(model_path, map_location=self.device)
            model.load_state_dict(checkpoint)
            model.to(self.device)
            model.eval()
            
            # 准备测试数据
            if kp_count == 57:
                test_kp = self.test_kp_57
            else:
                indices = np.linspace(0, 56, kp_count, dtype=int)
                test_kp = self.test_kp_57[:, indices, :]
            
            # 评估
            all_errors = []
            
            with torch.no_grad():
                for i in range(len(self.test_pc)):
                    pc_tensor = torch.FloatTensor(self.test_pc[i]).unsqueeze(0).to(self.device)
                    pred_kp = model(pc_tensor).cpu().numpy()[0]
                    true_kp = test_kp[i]
                    
                    # 计算误差
                    errors = np.linalg.norm(true_kp - pred_kp, axis=1)
                    all_errors.extend(errors)
            
            all_errors = np.array(all_errors)
            
            result = {
                'model_type': 'existing',
                'keypoints': kp_count,
                'architecture': f'Our_{architecture}',
                'avg_error': np.mean(all_errors),
                'std_error': np.std(all_errors),
                'medical_rate': np.sum(all_errors <= 10) / len(all_errors) * 100,
                'excellent_rate': np.sum(all_errors <= 5) / len(all_errors) * 100,
                'num_params': sum(p.numel() for p in model.parameters()),
                'model_file': model_path
            }
            
            print(f"  ✅ 平均误差: {result['avg_error']:.2f}mm")
            
            return result
            
        except Exception as e:
            print(f"  ❌ 评估失败: {e}")
            return None
    
    def train_and_evaluate_mainstream_model(self, model_name, model_class, num_keypoints):
        """训练和评估主流模型"""
        
        print(f"🔄 训练主流模型: {model_name}, {num_keypoints}关键点...")
        
        try:
            # 准备数据
            if num_keypoints == 57:
                keypoints = self.keypoints_57
            else:
                indices = np.linspace(0, 56, num_keypoints, dtype=int)
                keypoints = self.keypoints_57[:, indices, :]
            
            # 创建数据划分
            indices = np.arange(len(self.point_clouds))
            train_indices, test_val_indices = train_test_split(indices, test_size=0.4, random_state=42)
            val_indices, test_indices = train_test_split(test_val_indices, test_size=0.5, random_state=42)
            
            # 创建数据集
            train_dataset = SimpleDataset(
                self.point_clouds[train_indices],
                keypoints[train_indices],
                num_points=50000
            )
            
            val_dataset = SimpleDataset(
                self.point_clouds[val_indices],
                keypoints[val_indices],
                num_points=50000
            )
            
            test_dataset = SimpleDataset(
                self.point_clouds[test_indices],
                keypoints[test_indices],
                num_points=50000
            )
            
            # 创建数据加载器
            train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False)
            test_loader = DataLoader(test_dataset, batch_size=4, shuffle=False)
            
            # 创建模型
            model = model_class(num_points=50000, num_keypoints=num_keypoints)
            model = model.to(self.device)
            
            # 训练模型
            optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
            criterion = nn.MSELoss()
            
            best_val_loss = float('inf')
            patience = 10
            patience_counter = 0
            
            for epoch in range(50):
                # 训练
                model.train()
                train_loss = 0.0
                
                for batch_pc, batch_kp in train_loader:
                    batch_pc = batch_pc.to(self.device)
                    batch_kp = batch_kp.to(self.device)
                    
                    optimizer.zero_grad()
                    pred_kp = model(batch_pc)
                    loss = criterion(pred_kp, batch_kp)
                    loss.backward()
                    optimizer.step()
                    
                    train_loss += loss.item()
                
                # 验证
                model.eval()
                val_loss = 0.0
                
                with torch.no_grad():
                    for batch_pc, batch_kp in val_loader:
                        batch_pc = batch_pc.to(self.device)
                        batch_kp = batch_kp.to(self.device)
                        
                        pred_kp = model(batch_pc)
                        loss = criterion(pred_kp, batch_kp)
                        val_loss += loss.item()
                
                train_loss /= len(train_loader)
                val_loss /= len(val_loader)
                
                # 早停
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    best_model_state = model.state_dict().copy()
                else:
                    patience_counter += 1
                    if patience_counter >= patience:
                        break
                
                if (epoch + 1) % 10 == 0:
                    print(f"    Epoch {epoch+1}: Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")
            
            # 加载最佳模型
            model.load_state_dict(best_model_state)
            
            # 评估
            model.eval()
            all_errors = []
            
            with torch.no_grad():
                for batch_pc, batch_kp in test_loader:
                    batch_pc = batch_pc.to(self.device)
                    batch_kp = batch_kp.to(self.device)
                    
                    pred_kp = model(batch_pc)
                    
                    errors = torch.norm(pred_kp - batch_kp, dim=2)
                    all_errors.extend(errors.cpu().numpy().flatten())
            
            all_errors = np.array(all_errors)
            
            result = {
                'model_type': 'mainstream',
                'keypoints': num_keypoints,
                'architecture': model_name,
                'avg_error': np.mean(all_errors),
                'std_error': np.std(all_errors),
                'medical_rate': np.sum(all_errors <= 10) / len(all_errors) * 100,
                'excellent_rate': np.sum(all_errors <= 5) / len(all_errors) * 100,
                'num_params': sum(p.numel() for p in model.parameters())
            }
            
            print(f"  ✅ 平均误差: {result['avg_error']:.2f}mm")
            
            return result
            
        except Exception as e:
            print(f"  ❌ 训练失败: {e}")
            return None
    
    def run_hybrid_benchmark(self):
        """运行混合基准测试"""
        
        print("\n🚀 开始混合基准测试...")
        print("=" * 80)
        
        all_results = []
        
        # 评估已有模型（选择代表性的）
        representative_existing = [m for m in self.existing_models if m['keypoints'] in [12, 28, 57]][:10]
        
        for model_info in representative_existing:
            result = self.evaluate_existing_model(model_info)
            if result:
                all_results.append(result)
        
        # 训练和评估主流模型
        for model_name, model_class in self.mainstream_models.items():
            for num_keypoints in [12, 28, 57]:
                result = self.train_and_evaluate_mainstream_model(model_name, model_class, num_keypoints)
                if result:
                    all_results.append(result)
        
        # 保存结果
        self.save_hybrid_results(all_results)
        
        print(f"\n✅ 混合基准测试完成！评估了 {len(all_results)} 个模型")
        
        return all_results
    
    def save_hybrid_results(self, results):
        """保存混合结果"""
        
        # 保存为CSV
        df = pd.DataFrame(results)
        df.to_csv('hybrid_benchmark_results.csv', index=False)
        
        # 保存为JSON
        with open('hybrid_benchmark_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print("💾 混合基准测试结果已保存:")
        print("   📊 hybrid_benchmark_results.csv")
        print("   📄 hybrid_benchmark_results.json")

if __name__ == "__main__":
    print("🧪 混合基准测试框架")
    print("结合已有模型和新训练的主流模型")
    print("=" * 80)
    
    # 创建基准测试框架
    benchmark = HybridBenchmarkFramework()
    
    # 运行混合基准测试
    results = benchmark.run_hybrid_benchmark()
    
    print(f"\n📋 混合基准测试总结:")
    print(f"   🔬 已有模型: {len(benchmark.existing_models)} 个")
    print(f"   🔬 主流模型: {len(benchmark.mainstream_models)} 个")
    print(f"   📊 总评估数: {len(results)} 个")
    
    if results:
        # 显示最佳结果
        best_existing = min([r for r in results if r['model_type'] == 'existing'], 
                           key=lambda x: x['avg_error'], default=None)
        best_mainstream = min([r for r in results if r['model_type'] == 'mainstream'], 
                             key=lambda x: x['avg_error'], default=None)
        
        if best_existing:
            print(f"\n🏆 最佳已有模型:")
            print(f"   📊 {best_existing['keypoints']}点 {best_existing['architecture']}: {best_existing['avg_error']:.2f}mm")
        
        if best_mainstream:
            print(f"\n🏆 最佳主流模型:")
            print(f"   📊 {best_mainstream['keypoints']}点 {best_mainstream['architecture']}: {best_mainstream['avg_error']:.2f}mm")
    
    print(f"\n💡 这个混合基准测试提供了:")
    print(f"   • 您已有模型的真实性能")
    print(f"   • 主流模型的对比基准")
    print(f"   • 统一的评估协议")
    print(f"   • 可信的性能对比")
