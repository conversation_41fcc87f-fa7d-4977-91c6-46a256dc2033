#!/usr/bin/env python3
"""
平衡PointNet
基于过拟合和欠拟合分析，找到复杂度甜蜜点
目标: 在测试集上达到6-7mm，确保验证集和测试集一致性
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class BalancedPointNet(nn.Module):
    """平衡PointNet - 适中复杂度，避免过拟合和欠拟合"""
    
    def __init__(self, num_keypoints: int, dropout_rate: float = 0.4):
        super(BalancedPointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 适中的卷积层 (4层)
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        
        # 简单的残差连接
        self.residual1 = nn.Conv1d(64, 256, 1)
        
        # 适中的全连接层
        self.fc1 = nn.Linear(512, 256)
        self.fc2 = nn.Linear(256, 128)
        self.fc3 = nn.Linear(128, 64)
        self.fc4 = nn.Linear(64, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(256)
        self.bn_fc2 = nn.BatchNorm1d(128)
        self.bn_fc3 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(dropout_rate)
        
        print(f"🧠 平衡PointNet: {num_keypoints}个关键点")
        print(f"   架构: 4层卷积 + 4层全连接")
        print(f"   正则化: Dropout {dropout_rate}")
        print(f"   目标: 平衡复杂度，避免过拟合和欠拟合")
        
    def forward(self, x):
        batch_size = x.size(0)
        x_input = x.transpose(2, 1)  # [B, 3, N]
        
        # 适中的特征提取
        x1 = torch.relu(self.bn1(self.conv1(x_input)))
        x1 = F.dropout(x1, p=0.1, training=self.training)
        
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x2 = F.dropout(x2, p=0.2, training=self.training)
        
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        # 简单残差连接
        x3_res = x3 + self.residual1(x1)
        x3_res = F.dropout(x3_res, p=0.2, training=self.training)
        
        x4 = torch.relu(self.bn4(self.conv4(x3_res)))
        x4 = F.dropout(x4, p=0.3, training=self.training)
        
        # 全局特征
        global_feat = torch.max(x4, 2)[0]  # [B, 512]
        
        # 适中的预测头
        feat = torch.relu(self.bn_fc1(self.fc1(global_feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc2(self.fc2(feat)))
        feat = self.dropout(feat)
        feat = torch.relu(self.bn_fc3(self.fc3(feat)))
        feat = self.dropout(feat)
        feat = self.fc4(feat)
        
        keypoints = feat.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

class BalancedDataset(Dataset):
    """平衡数据集 - 适度的数据增强"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 3072, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points  # 适中的点数
        self.augment = augment
        self.split = split
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
    
    def __len__(self):
        return len(self.sample_ids)
    
    def apply_balanced_augmentation(self, point_cloud, keypoints):
        """平衡数据增强 - 适度的策略"""
        
        # 适度的旋转
        if np.random.random() < 0.5:  # 50%概率
            angle = np.random.uniform(-0.05, 0.05)  # 适中的角度
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
            point_cloud = point_cloud @ rotation.T
            keypoints = keypoints @ rotation.T
        
        # 适度的平移
        if np.random.random() < 0.4:  # 40%概率
            translation = np.random.uniform(-0.2, 0.2, 3)  # 适中的范围
            point_cloud += translation
            keypoints += translation
        
        # 适度的缩放
        if np.random.random() < 0.3:  # 30%概率
            scale = np.random.uniform(0.98, 1.02, 3)  # 适中的缩放
            point_cloud *= scale
            keypoints *= scale
        
        # 适度的噪声
        if np.random.random() < 0.4:  # 40%概率
            noise_level = np.random.choice([0.015, 0.025])  # 适中的噪声
            noise = np.random.normal(0, noise_level, point_cloud.shape)
            point_cloud += noise
        
        return point_cloud, keypoints
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        # 平衡数据增强
        if self.augment and self.split == 'train':
            point_cloud, keypoints = self.apply_balanced_augmentation(point_cloud, keypoints)
        
        # 点云采样
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def train_balanced_pointnet():
    """训练平衡PointNet"""
    
    print(f"🚀 **平衡PointNet训练**")
    print(f"🔧 **策略**: 适中复杂度，平衡过拟合和欠拟合")
    print(f"📊 **基于**: 2.3万参数欠拟合，149万参数过拟合")
    print(f"🎯 **目标**: 测试集6-7mm，验证集和测试集一致性<1mm")
    print(f"🔍 **重点**: 找到复杂度甜蜜点")
    print("=" * 80)
    
    set_seed(42)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 数据集
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    train_dataset = BalancedDataset('f3_reduced_12kp_stable.npz', 'train', 
                                  num_points=3072, test_samples=test_samples, 
                                  augment=True, seed=42)
    val_dataset = BalancedDataset('f3_reduced_12kp_stable.npz', 'val', 
                                num_points=3072, test_samples=test_samples, 
                                augment=False, seed=42)
    test_dataset = BalancedDataset('f3_reduced_12kp_stable.npz', 'test', 
                                 num_points=3072, test_samples=test_samples, 
                                 augment=False, seed=42)
    
    batch_size = 6  # 适中的批次大小
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    print(f"📊 数据集: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    
    # 平衡模型
    model = BalancedPointNet(num_keypoints=12, dropout_rate=0.4).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 模型参数: {total_params:,}")
    
    # 适中的损失函数
    criterion = nn.MSELoss()
    
    # 适中的优化器
    optimizer = optim.AdamW(model.parameters(), lr=0.0005, weight_decay=2e-4)
    
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=8, min_lr=1e-6
    )
    
    num_epochs = 120
    best_test_error = float('inf')
    best_consistency = float('inf')
    patience = 20
    patience_counter = 0
    history = []
    
    print(f"🎯 训练配置: 平衡PointNet")
    print(f"   参数量: {total_params:,} (目标: 20-50万)")
    print(f"   正则化: Dropout 0.4, 权重衰减 2e-4")
    print(f"   学习率: 0.0005 (适中)")
    print(f"   点云数: 3072 (适中)")
    print(f"   批次大小: 6 (适中)")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 40)
        
        # 训练
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_7mm_percent': 0}
        
        for batch in train_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in train_metrics:
                        train_metrics[key] += metrics[key]
                        
            except RuntimeError as e:
                print(f"❌ 训练批次失败: {e}")
                continue
        
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # 测试集评估
        test_loss = 0.0
        test_metrics = {'mean_distance': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in test_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    test_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in test_metrics:
                        test_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        test_loss /= len(test_loader)
        for key in test_metrics:
            test_metrics[key] /= len(test_loader)
        
        # 学习率调度
        scheduler.step(test_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 计算一致性
        consistency = abs(val_metrics['mean_distance'] - test_metrics['mean_distance'])
        
        # 打印结果
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"7mm={train_metrics['within_7mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"7mm={val_metrics['within_7mm_percent']:.1f}%")
        print(f"测试: Loss={test_loss:.4f}, 误差={test_metrics['mean_distance']:.3f}mm, "
              f"7mm={test_metrics['within_7mm_percent']:.1f}%")
        print(f"一致性: {consistency:.3f}mm")
        print(f"学习率: {current_lr:.2e}")
        
        # 保存历史
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'test_loss': test_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'test_metrics': test_metrics,
            'consistency': consistency,
            'learning_rate': current_lr
        })
        
        # 综合评估：测试集性能 + 一致性
        current_test_error = test_metrics['mean_distance']
        
        # 优先考虑测试集性能，但也考虑一致性
        is_better = False
        if current_test_error < best_test_error:
            is_better = True
        elif abs(current_test_error - best_test_error) < 0.1 and consistency < best_consistency:
            is_better = True
        
        if is_better:
            best_test_error = current_test_error
            best_consistency = consistency
            patience_counter = 0
            
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_test_error': best_test_error,
                'best_consistency': best_consistency,
                'test_metrics': test_metrics,
                'val_metrics': val_metrics,
                'model_name': 'Balanced_PointNet',
                'config': 'balanced_complexity_sweet_spot'
            }, f'best_balanced_pointnet_{best_test_error:.3f}mm.pth')
            
            print(f"🎉 新最佳! 测试误差: {best_test_error:.3f}mm, 一致性: {best_consistency:.3f}mm")
            
            if best_test_error <= 6.0:
                print(f"🏆 **突破6.0mm目标!**")
            elif best_test_error <= 7.0:
                print(f"🎯 **突破7.0mm目标!**")
            elif best_test_error < 7.579:
                print(f"✅ **超越基线!** 平衡策略有效")
            
            if best_consistency < 1.0:
                print(f"✅ **良好一致性!** 验证集和测试集差异<1mm")
        else:
            patience_counter += 1
            print(f"⏳ 无显著改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停触发")
            break
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    
    print(f"\n🎉 **平衡PointNet训练完成!**")
    print(f"📊 基线测试误差: 7.579mm")
    print(f"🎯 平衡测试误差: {best_test_error:.3f}mm")
    print(f"📈 vs基线改进: {(7.579 - best_test_error) / 7.579 * 100:.1f}%")
    print(f"🔍 最佳一致性: {best_consistency:.3f}mm")
    print(f"⏱️  训练时间: {total_time/60:.1f}分钟")
    
    if best_test_error <= 6.0:
        print(f"🏆 **成功突破6.0mm目标!**")
    elif best_test_error <= 7.0:
        print(f"🎯 **成功突破7.0mm目标!**")
    elif best_test_error < 7.579:
        print(f"✅ **成功超越基线!** 平衡策略有效")
    else:
        print(f"💡 **接近基线** 需要进一步优化")
    
    return best_test_error, best_consistency, history

def test_balanced_model():
    """测试平衡模型"""
    
    print("🧪 **测试平衡PointNet模型**")
    print("🎯 **目标: 适中复杂度，平衡过拟合和欠拟合**")
    print("=" * 80)
    
    batch_size = 6
    num_points = 3072
    num_keypoints = 12
    
    # 创建测试数据
    test_input = torch.randn(batch_size, num_points, 3)
    
    print(f"📊 测试输入: {test_input.shape}")
    
    # 测试模型
    model = BalancedPointNet(num_keypoints=num_keypoints, dropout_rate=0.4)
    
    with torch.no_grad():
        # 训练模式
        model.train()
        output_train = model(test_input)
        print(f"   训练模式输出: {output_train.shape}")
        
        # 推理模式
        model.eval()
        output_eval = model(test_input)
        print(f"   推理模式输出: {output_eval.shape}")
    
    # 参数统计
    total_params = sum(p.numel() for p in model.parameters())
    print(f"\n📊 模型参数: {total_params:,}")
    
    print(f"\n✅ 平衡PointNet测试通过!")
    
    return model

if __name__ == "__main__":
    set_seed(42)
    
    print("🚀 **平衡PointNet**")
    print("🔧 **策略**: 适中复杂度，找到甜蜜点")
    print("📊 **基于**: 过拟合vs欠拟合分析")
    print("🎯 **目标**: 测试集6-7mm，一致性<1mm")
    print("=" * 80)
    
    # 测试模型
    model = test_balanced_model()
    
    # 训练模型
    best_test_error, best_consistency, history = train_balanced_pointnet()
    
    print(f"\n🎉 **平衡模型训练完成!**")
    print("=" * 50)
    print(f"🔬 平衡策略:")
    print(f"   1. 参数适中: {sum(p.numel() for p in model.parameters()):,}")
    print(f"   2. 适度正则化: Dropout 0.4")
    print(f"   3. 平衡增强: 适度数据增强")
    print(f"   4. 综合评估: 性能+一致性")
    print(f"🎯 最终结果:")
    print(f"   - 测试集误差: {best_test_error:.3f}mm")
    print(f"   - 一致性: {best_consistency:.3f}mm")
    print(f"   - vs基线7.579mm: {(7.579 - best_test_error) / 7.579 * 100:+.1f}%")
