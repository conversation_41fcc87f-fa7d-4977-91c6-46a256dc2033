{"method": "Hyperparameter Tuning for 12 Keypoints", "baseline_error": 6.208, "results": [{"config": {"name": "基线配置", "lr": 0.0008, "weight_decay": 0.0001, "batch_size": 4, "dropout": 0.3, "loss_alpha": 0.8, "loss_beta": 0.2, "lr_factor": 0.7, "lr_patience": 12}, "best_val_error": 6.987755203247071, "training_time_minutes": 0.9141322096188863, "improvement_vs_baseline": -12.560489743026265}, {"config": {"name": "更高学习率", "lr": 0.001, "weight_decay": 0.0001, "batch_size": 4, "dropout": 0.3, "loss_alpha": 0.8, "loss_beta": 0.2, "lr_factor": 0.7, "lr_patience": 12}, "best_val_error": 7.951669216156006, "training_time_minutes": 0.5978868842124939, "improvement_vs_baseline": -28.087455157152153}, {"config": {"name": "更低学习率", "lr": 0.0005, "weight_decay": 0.0001, "batch_size": 4, "dropout": 0.3, "loss_alpha": 0.8, "loss_beta": 0.2, "lr_factor": 0.7, "lr_patience": 12}, "best_val_error": 6.9643481254577635, "training_time_minutes": 0.9103781660397847, "improvement_vs_baseline": -12.183442742554176}, {"config": {"name": "更强正则化", "lr": 0.0008, "weight_decay": 0.0005, "batch_size": 4, "dropout": 0.4, "loss_alpha": 0.8, "loss_beta": 0.2, "lr_factor": 0.7, "lr_patience": 12}, "best_val_error": 6.669025230407715, "training_time_minutes": 0.9616733074188233, "improvement_vs_baseline": -7.4263084795057095}, {"config": {"name": "更弱正则化", "lr": 0.0008, "weight_decay": 5e-05, "batch_size": 4, "dropout": 0.2, "loss_alpha": 0.8, "loss_beta": 0.2, "lr_factor": 0.7, "lr_patience": 12}, "best_val_error": 6.716434288024902, "training_time_minutes": 0.6742321769396464, "improvement_vs_baseline": -8.189985309679477}, {"config": {"name": "更大批次", "lr": 0.001, "weight_decay": 0.0001, "batch_size": 8, "dropout": 0.3, "loss_alpha": 0.8, "loss_beta": 0.2, "lr_factor": 0.7, "lr_patience": 12}, "best_val_error": 6.60653829574585, "training_time_minutes": 0.8607475956281027, "improvement_vs_baseline": -6.419753475287522}, {"config": {"name": "优化损失权重", "lr": 0.0008, "weight_decay": 0.0001, "batch_size": 4, "dropout": 0.3, "loss_alpha": 0.9, "loss_beta": 0.1, "lr_factor": 0.7, "lr_patience": 12}, "best_val_error": 8.763689041137695, "training_time_minutes": 0.5988748470942179, "improvement_vs_baseline": -41.167671410078846}, {"config": {"name": "更激进学习率调度", "lr": 0.0008, "weight_decay": 0.0001, "batch_size": 4, "dropout": 0.3, "loss_alpha": 0.8, "loss_beta": 0.2, "lr_factor": 0.5, "lr_patience": 8}, "best_val_error": 6.458150482177734, "training_time_minutes": 0.9283666849136353, "improvement_vs_baseline": -4.029485859821744}]}