#!/usr/bin/env python3
"""
训练真实F3 19关键点系统
基于医生真实标注的19个F3关键点进行训练
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import pandas as pd
from pathlib import Path
from sklearn.model_selection import train_test_split
import struct

# F3区域19个关键点的解剖信息
F3_KEYPOINT_INFO = {
    0: {"name": "F3-1", "anatomy": "sacral_region", "importance": "high"},
    1: {"name": "F3-2", "anatomy": "anterior_boundary", "importance": "high"},  # 前方边界
    2: {"name": "F3-3", "anatomy": "sacral_region", "importance": "medium"},
    3: {"name": "F3-4", "anatomy": "sacral_region", "importance": "medium"},
    4: {"name": "F3-5", "anatomy": "sacral_region", "importance": "medium"},
    5: {"name": "F3-6", "anatomy": "sacral_region", "importance": "medium"},
    6: {"name": "F3-7", "anatomy": "sacral_region", "importance": "medium"},
    7: {"name": "F3-8", "anatomy": "sacral_region", "importance": "medium"},
    8: {"name": "F3-9", "anatomy": "sacral_region", "importance": "medium"},
    9: {"name": "F3-10", "anatomy": "sacral_region", "importance": "medium"},
    10: {"name": "F3-11", "anatomy": "sacral_region", "importance": "medium"},
    11: {"name": "F3-12", "anatomy": "posterior_boundary", "importance": "high"},  # 后方边界
    12: {"name": "F3-13", "anatomy": "z_maximum", "importance": "critical"},      # Z轴最高点
    13: {"name": "F3-14", "anatomy": "left_boundary", "importance": "high"},      # 左侧边界
    14: {"name": "F3-15", "anatomy": "sacral_region", "importance": "medium"},
    15: {"name": "F3-16", "anatomy": "sacral_region", "importance": "medium"},
    16: {"name": "F3-17", "anatomy": "sacral_region", "importance": "medium"},
    17: {"name": "F3-18", "anatomy": "z_minimum", "importance": "critical"},      # Z轴最低点
    18: {"name": "F3-19", "anatomy": "right_boundary", "importance": "high"}      # 右侧边界
}

class RealF3HeatmapPointNet(nn.Module):
    """真实F3 19关键点热力图网络"""
    
    def __init__(self, input_dim=3, num_keypoints=19):
        super().__init__()
        
        # 点云特征提取 - 针对F3区域优化
        self.conv1 = nn.Conv1d(input_dim, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 热力图回归头 - 专门为19个F3关键点设计
        self.heatmap_head = nn.Sequential(
            nn.Conv1d(1024, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Conv1d(512, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Conv1d(256, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            
            nn.Conv1d(128, num_keypoints, 1)  # 输出19个F3关键点的热力图
        )
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        # x: [B, 3, N]
        batch_size = x.size(0)
        
        # 特征提取
        x = torch.relu(self.bn1(self.conv1(x)))
        x = torch.relu(self.bn2(self.conv2(x)))
        x = torch.relu(self.bn3(self.conv3(x)))
        x = torch.relu(self.bn4(self.conv4(x)))
        x = self.bn5(self.conv5(x))
        
        # 应用dropout
        x = self.dropout(x)
        
        # 生成热力图
        heatmaps = self.heatmap_head(x)  # [B, 19, N]
        
        return heatmaps

def load_real_f3_data():
    """加载真实的F3 19关键点数据"""
    
    print("📊 加载真实F3 19关键点数据...")
    
    # 检查是否有现成的F3数据
    f3_files = [
        'f3_reduced_12kp_male.npz',  # 当前的12关键点数据
        'f3_19kp_real.npz'           # 可能的19关键点数据
    ]
    
    # 先尝试加载现有数据
    if Path('f3_reduced_12kp_male.npz').exists():
        print("⚠️ 发现12关键点数据，需要重新加载完整的19关键点数据")
        
        # 从原始标注文件重新加载19关键点数据
        return load_f3_from_annotations()
    else:
        print("❌ 未找到F3数据文件")
        return None, None, None

def load_f3_from_annotations():
    """从原始标注文件加载F3 19关键点数据"""
    
    print("🔄 从原始标注文件重新加载F3 19关键点数据...")
    
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    annotations_dir = data_dir / "annotations"
    stl_dir = data_dir / "stl_models"
    
    all_point_clouds = []
    all_keypoints = []
    all_sample_ids = []
    
    # 遍历所有标注文件
    annotation_files = list(annotations_dir.glob("*-Table-XYZ.CSV"))
    
    for csv_file in annotation_files[:20]:  # 先处理前20个样本
        sample_id = csv_file.stem.split('-')[0]
        
        print(f"   处理样本: {sample_id}")
        
        try:
            # 加载标注
            df = pd.read_csv(csv_file, encoding='gbk')
            
            # 提取F3关键点
            f3_mask = df['label'].str.startswith('F_3')
            f3_data = df[f3_mask]
            
            if len(f3_data) != 19:
                print(f"     ⚠️ F3关键点数量不正确: {len(f3_data)}, 跳过")
                continue
            
            keypoints = f3_data[['X', 'Y', 'Z']].values
            
            # 加载对应的F3点云
            f3_stl_file = stl_dir / f"{sample_id}-F_3.stl"
            
            if not f3_stl_file.exists():
                print(f"     ❌ F3 STL文件不存在: {f3_stl_file}")
                continue
            
            # 加载STL点云
            point_cloud = load_stl_file(str(f3_stl_file))
            
            if point_cloud is None or len(point_cloud) < 1000:
                print(f"     ❌ 点云加载失败或点数过少")
                continue
            
            all_point_clouds.append(point_cloud)
            all_keypoints.append(keypoints)
            all_sample_ids.append(sample_id)
            
            print(f"     ✅ 成功加载: {len(point_cloud)}个点, 19个关键点")
            
        except Exception as e:
            print(f"     ❌ 处理失败: {e}")
            continue
    
    if not all_point_clouds:
        print("❌ 没有成功加载任何F3数据")
        return None, None, None
    
    # 转换为numpy数组
    point_clouds = np.array(all_point_clouds, dtype=object)
    keypoints = np.array(all_keypoints)
    sample_ids = np.array(all_sample_ids)
    
    print(f"✅ 成功加载F3数据:")
    print(f"   样本数: {len(point_clouds)}")
    print(f"   关键点形状: {keypoints.shape}")
    
    # 保存处理后的数据
    np.savez('f3_19kp_real.npz', 
             point_clouds=point_clouds,
             keypoints=keypoints,
             sample_ids=sample_ids)
    
    print("💾 数据已保存为: f3_19kp_real.npz")
    
    return point_clouds, keypoints, sample_ids

def load_stl_file(stl_path):
    """加载STL文件为点云"""
    try:
        with open(stl_path, 'rb') as f:
            # 跳过STL头部
            f.read(80)
            triangle_count = struct.unpack('<I', f.read(4))[0]
            
            vertices = []
            for _ in range(triangle_count):
                # 跳过法向量
                f.read(12)
                # 读取三个顶点
                for _ in range(3):
                    vertex = struct.unpack('<fff', f.read(12))
                    vertices.append(vertex)
                # 跳过属性字节
                f.read(2)
            
            vertices = np.array(vertices)
            
            # 去重
            unique_vertices = np.unique(vertices, axis=0)
            
            return unique_vertices
            
    except Exception as e:
        print(f"STL加载失败: {e}")
        return None

class RealF3Dataset(Dataset):
    """真实F3 19关键点数据集"""
    
    def __init__(self, point_clouds, keypoints, sample_ids, num_points=8192, 
                 sigma=6.0, augment=False):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.sample_ids = sample_ids
        self.num_points = num_points
        self.sigma = sigma
        self.augment = augment
        
        print(f"📊 真实F3数据集初始化:")
        print(f"   样本数: {len(self.point_clouds)}")
        print(f"   关键点数: {len(self.keypoints[0]) if len(self.keypoints) > 0 else 0}")
        print(f"   数据增强: {self.augment}")
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        pc = self.point_clouds[idx].copy()
        kp = self.keypoints[idx].copy()
        sample_id = self.sample_ids[idx]
        
        # 数据增强
        if self.augment:
            pc, kp = self.apply_medical_augmentation(pc, kp)
        
        # 采样点云
        if len(pc) > self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=False)
            pc = pc[indices]
        elif len(pc) < self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=True)
            pc = pc[indices]
        
        # 生成热力图目标
        heatmaps = self.create_heatmap_targets(kp, pc)
        
        return {
            'point_cloud': torch.FloatTensor(pc).transpose(0, 1),  # [3, N]
            'heatmaps': torch.FloatTensor(heatmaps),  # [19, N]
            'keypoints': torch.FloatTensor(kp),  # [19, 3]
            'sample_id': sample_id
        }
    
    def create_heatmap_targets(self, keypoints, point_cloud):
        """创建热力图目标 - 针对F3关键点优化"""
        num_points = len(point_cloud)
        num_keypoints = len(keypoints)
        heatmaps = np.zeros((num_keypoints, num_points))
        
        for kp_idx, keypoint in enumerate(keypoints):
            # 根据关键点重要性调整sigma
            importance = F3_KEYPOINT_INFO[kp_idx]['importance']
            if importance == 'critical':
                sigma = self.sigma * 0.8  # 更尖锐的峰值
            elif importance == 'high':
                sigma = self.sigma
            else:
                sigma = self.sigma * 1.2  # 更宽的分布
            
            # 计算每个点到关键点的距离
            distances = np.linalg.norm(point_cloud - keypoint, axis=1)
            # 生成高斯分布
            heatmaps[kp_idx] = np.exp(-distances**2 / (2 * sigma**2))
        
        return heatmaps
    
    def apply_medical_augmentation(self, pc, kp):
        """医疗级数据增强"""
        # 保守的旋转 - 医疗数据不能过度变形
        if np.random.random() < 0.3:
            angle = np.random.uniform(-5, 5) * np.pi / 180  # 只有5度
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            pc = pc @ rotation_matrix.T
            kp = kp @ rotation_matrix.T
        
        # 轻微缩放
        if np.random.random() < 0.2:
            scale = np.random.uniform(0.98, 1.02)  # 只有2%的缩放
            pc = pc * scale
            kp = kp * scale
        
        # 小幅平移
        if np.random.random() < 0.2:
            translation = np.random.uniform(-1, 1, 3)  # 只有1mm的平移
            pc = pc + translation
            kp = kp + translation
        
        # 轻微噪声
        if np.random.random() < 0.1:
            noise = np.random.normal(0, 0.1, pc.shape)  # 很小的噪声
            pc = pc + noise
        
        return pc, kp

def train_real_f3_model(train_loader, val_loader, device, num_epochs=50):
    """训练真实F3 19关键点模型"""

    # 创建模型
    model = RealF3HeatmapPointNet(input_dim=3, num_keypoints=19).to(device)

    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=0.0005, weight_decay=0.01)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=10, factor=0.5)
    criterion = nn.MSELoss()

    # 训练历史
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')

    print(f"🚀 开始训练真实F3 19关键点模型 ({num_epochs} epochs)")
    print(f"📊 模型参数: {sum(p.numel() for p in model.parameters())}")

    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0

        for batch_idx, batch in enumerate(train_loader):
            point_clouds = batch['point_cloud'].to(device)  # [B, 3, N]
            heatmap_targets = batch['heatmaps'].to(device)  # [B, 19, N]

            optimizer.zero_grad()

            # 前向传播
            pred_heatmaps = model(point_clouds)

            # 计算损失
            loss = criterion(pred_heatmaps, heatmap_targets)

            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()

            train_loss += loss.item()

        # 验证阶段
        model.eval()
        val_loss = 0

        with torch.no_grad():
            for batch in val_loader:
                point_clouds = batch['point_cloud'].to(device)
                heatmap_targets = batch['heatmaps'].to(device)

                pred_heatmaps = model(point_clouds)
                loss = criterion(pred_heatmaps, heatmap_targets)
                val_loss += loss.item()

        # 计算平均损失
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)

        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)

        # 学习率调度
        scheduler.step(avg_val_loss)

        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            torch.save(model.state_dict(), 'best_real_f3_19kp_model.pth')
            print(f"✅ 保存最佳模型 (epoch {epoch+1})")

        # 打印进度
        if (epoch + 1) % 5 == 0:
            print(f"Epoch {epoch+1}/{num_epochs}:")
            print(f"  Train Loss: {avg_train_loss:.4f}")
            print(f"  Val Loss: {avg_val_loss:.4f}")
            print(f"  LR: {optimizer.param_groups[0]['lr']:.6f}")

    return model, train_losses, val_losses

def main():
    """主函数"""
    print("🏥 真实F3 19关键点训练系统")
    print("基于医生真实标注的19个F3关键点")
    print("=" * 60)

    # 加载真实F3数据
    point_clouds, keypoints, sample_ids = load_real_f3_data()

    if point_clouds is None:
        print("❌ 数据加载失败，退出")
        return

    print(f"📊 数据概览:")
    print(f"   样本数: {len(point_clouds)}")
    print(f"   关键点形状: {keypoints.shape}")
    print(f"   平均点云大小: {np.mean([len(pc) for pc in point_clouds]):.0f}")

    # 分析关键点分布
    print(f"\n🔍 关键点分析:")
    for i in range(19):
        kp_coords = keypoints[:, i, :]
        mean_pos = np.mean(kp_coords, axis=0)
        std_pos = np.std(kp_coords, axis=0)
        info = F3_KEYPOINT_INFO[i]

        print(f"   {info['name']} ({info['anatomy']}): "
              f"({mean_pos[0]:.1f}±{std_pos[0]:.1f}, "
              f"{mean_pos[1]:.1f}±{std_pos[1]:.1f}, "
              f"{mean_pos[2]:.1f}±{std_pos[2]:.1f})")

    # 数据分割
    train_indices, val_indices = train_test_split(
        range(len(point_clouds)), test_size=0.2, random_state=42
    )

    # 创建数据集
    train_dataset = RealF3Dataset(
        point_clouds[train_indices], keypoints[train_indices],
        sample_ids[train_indices], augment=True
    )

    val_dataset = RealF3Dataset(
        point_clouds[val_indices], keypoints[val_indices],
        sample_ids[val_indices], augment=False
    )

    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False, num_workers=2)

    print(f"\n📊 数据分割:")
    print(f"   训练集: {len(train_dataset)} 样本")
    print(f"   验证集: {len(val_dataset)} 样本")

    # 训练模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")

    model, train_losses, val_losses = train_real_f3_model(
        train_loader, val_loader, device, num_epochs=40
    )

    # 绘制训练曲线
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='Train Loss', alpha=0.8)
    plt.plot(val_losses, label='Validation Loss', alpha=0.8)
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Real F3 19-Keypoint Training Progress')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('real_f3_19kp_training_progress.png', dpi=300, bbox_inches='tight')
    print("📊 训练进度保存: real_f3_19kp_training_progress.png")
    plt.close()

    print(f"\n🎉 真实F3 19关键点训练完成!")
    print("✅ 最佳模型保存为: best_real_f3_19kp_model.pth")
    print("💡 关键优势:")
    print("   1. 基于医生真实标注的19个关键点")
    print("   2. 包含Z轴极值点等明显特征")
    print("   3. 高密度点云数据 (平均11万点)")
    print("   4. 医疗级数据增强策略")
    print("   5. 针对F3骶骨区域优化的架构")

if __name__ == "__main__":
    main()
