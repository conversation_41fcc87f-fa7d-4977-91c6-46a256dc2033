#!/usr/bin/env python3
"""
简单的对齐校正测试
"""

import torch
import numpy as np
import h5py
from pathlib import Path
import matplotlib.pyplot as plt

from train_improved_model import ImprovedMedicalPointNet

class SimpleAlignmentTester:
    """简单对齐测试器"""
    
    def __init__(self, model_path="output/improved_model_training/best_improved_model.pth"):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model_path = Path(model_path)
        self.aligned_data_root = Path("MedicalAlignedDataset")
        self.output_dir = Path("output/simple_alignment_test")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🔧 简单对齐测试器初始化")
        self.load_model()
        self.get_data()
        print(f"✅ 测试器准备完成")
    
    def load_model(self):
        """加载模型"""
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
            self.model = ImprovedMedicalPointNet(num_keypoints=57)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            print(f"✅ 模型加载成功")
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self.model = None
    
    def get_data(self):
        """获取数据"""
        aligned_data_dir = self.aligned_data_root / "aligned_data"
        if aligned_data_dir.exists():
            h5_files = list(aligned_data_dir.glob("*.h5"))
            self.patient_ids = [f.stem for f in h5_files][:10]  # 只取前10个
            print(f"📊 找到 {len(self.patient_ids)} 个患者")
        else:
            self.patient_ids = []
    
    def load_patient_data(self, patient_id):
        """加载患者数据"""
        aligned_file = self.aligned_data_root / "aligned_data" / f"{patient_id}.h5"
        try:
            with h5py.File(aligned_file, 'r') as f:
                point_cloud = f['point_cloud'][:]
                keypoints = f['keypoints'][:]
            return {'point_cloud': point_cloud, 'keypoints': keypoints, 'patient_id': patient_id}
        except:
            return None
    
    def normalize_keypoints_to_pointcloud(self, keypoints, point_cloud):
        """归一化关键点"""
        pc_min, pc_max = point_cloud.min(axis=0), point_cloud.max(axis=0)
        pc_center = (pc_min + pc_max) / 2
        pc_range = pc_max - pc_min
        
        kp_min, kp_max = keypoints.min(axis=0), keypoints.max(axis=0)
        kp_center = (kp_min + kp_max) / 2
        kp_range = kp_max - kp_min
        
        scale_factors = pc_range * 0.8 / (kp_range + 1e-6)
        normalized_keypoints = (keypoints - kp_center) * scale_factors + pc_center
        return normalized_keypoints
    
    def predict_keypoints(self, point_cloud):
        """预测关键点"""
        if self.model is None:
            return None
        
        with torch.no_grad():
            point_cloud_tensor = torch.from_numpy(point_cloud.T).unsqueeze(0).to(self.device)
            pred_keypoints, _, _ = self.model(point_cloud_tensor)
            pred_keypoints = pred_keypoints.cpu().numpy()[0]
            return pred_keypoints
    
    def centroid_alignment(self, pred_keypoints, gt_keypoints):
        """质心对齐"""
        pred_centroid = np.mean(pred_keypoints, axis=0)
        gt_centroid = np.mean(gt_keypoints, axis=0)
        global_offset = gt_centroid - pred_centroid
        corrected_pred = pred_keypoints + global_offset
        return corrected_pred, global_offset
    
    def test_one_patient(self, patient_id):
        """测试一个患者"""
        print(f"\n🔧 测试患者: {patient_id}")
        
        patient_data = self.load_patient_data(patient_id)
        if patient_data is None:
            return None
        
        point_cloud = patient_data['point_cloud']
        original_keypoints = patient_data['keypoints']
        
        # 归一化关键点
        normalized_gt = self.normalize_keypoints_to_pointcloud(original_keypoints, point_cloud)
        
        # 预测关键点
        pred_keypoints = self.predict_keypoints(point_cloud)
        if pred_keypoints is None:
            return None
        
        # 计算原始误差
        original_errors = np.linalg.norm(pred_keypoints - normalized_gt, axis=1)
        original_mean_error = np.mean(original_errors)
        
        # 质心对齐校正
        corrected_pred, global_offset = self.centroid_alignment(pred_keypoints, normalized_gt)
        corrected_errors = np.linalg.norm(corrected_pred - normalized_gt, axis=1)
        corrected_mean_error = np.mean(corrected_errors)
        
        improvement = original_mean_error - corrected_mean_error
        
        print(f"   原始误差: {original_mean_error:.2f}mm")
        print(f"   校正后误差: {corrected_mean_error:.2f}mm")
        print(f"   改善: {improvement:.2f}mm ({improvement/original_mean_error*100:.1f}%)")
        print(f"   全局偏移: ({global_offset[0]:.2f}, {global_offset[1]:.2f}, {global_offset[2]:.2f})mm")
        
        # 简单可视化
        self.simple_visualize(patient_id, point_cloud, normalized_gt, pred_keypoints, 
                            corrected_pred, original_errors, corrected_errors)
        
        return {
            'patient_id': patient_id,
            'original_error': original_mean_error,
            'corrected_error': corrected_mean_error,
            'improvement': improvement,
            'improvement_percent': improvement/original_mean_error*100,
            'global_offset': global_offset
        }
    
    def simple_visualize(self, patient_id, point_cloud, gt_keypoints, original_pred, 
                        corrected_pred, original_errors, corrected_errors):
        """简单可视化"""
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        
        # 1. 原始预测
        axes[0].scatter(point_cloud[:, 0], point_cloud[:, 1], c='lightgray', alpha=0.2, s=1)
        axes[0].scatter(gt_keypoints[:, 0], gt_keypoints[:, 1], c='green', s=30, marker='o', label='GT')
        axes[0].scatter(original_pred[:, 0], original_pred[:, 1], c='red', s=25, marker='^', label='Pred')
        
        # 连接线
        for i in range(len(gt_keypoints)):
            color = 'red' if original_errors[i] > 3 else 'orange' if original_errors[i] > 2 else 'green'
            axes[0].plot([gt_keypoints[i, 0], original_pred[i, 0]], 
                        [gt_keypoints[i, 1], original_pred[i, 1]], 
                        color=color, alpha=0.5, linewidth=1)
        
        axes[0].set_title(f'Original\nError: {np.mean(original_errors):.2f}mm')
        axes[0].legend()
        axes[0].axis('equal')
        axes[0].grid(True, alpha=0.3)
        
        # 2. 校正后预测
        axes[1].scatter(point_cloud[:, 0], point_cloud[:, 1], c='lightgray', alpha=0.2, s=1)
        axes[1].scatter(gt_keypoints[:, 0], gt_keypoints[:, 1], c='green', s=30, marker='o', label='GT')
        axes[1].scatter(corrected_pred[:, 0], corrected_pred[:, 1], c='blue', s=25, marker='^', label='Corrected')
        
        # 连接线
        for i in range(len(gt_keypoints)):
            color = 'red' if corrected_errors[i] > 3 else 'orange' if corrected_errors[i] > 2 else 'green'
            axes[1].plot([gt_keypoints[i, 0], corrected_pred[i, 0]], 
                        [gt_keypoints[i, 1], corrected_pred[i, 1]], 
                        color=color, alpha=0.5, linewidth=1)
        
        axes[1].set_title(f'Corrected\nError: {np.mean(corrected_errors):.2f}mm')
        axes[1].legend()
        axes[1].axis('equal')
        axes[1].grid(True, alpha=0.3)
        
        # 3. 误差对比
        x = np.arange(len(original_errors))
        width = 0.35
        
        axes[2].bar(x - width/2, original_errors, width, label='Original', alpha=0.7, color='red')
        axes[2].bar(x + width/2, corrected_errors, width, label='Corrected', alpha=0.7, color='blue')
        
        axes[2].axhline(y=3.0, color='orange', linestyle='--', alpha=0.7, label='3mm')
        axes[2].axhline(y=5.0, color='red', linestyle='--', alpha=0.7, label='5mm')
        
        axes[2].set_xlabel('Keypoint Index')
        axes[2].set_ylabel('Error (mm)')
        axes[2].set_title('Error Comparison')
        axes[2].legend()
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存
        plot_path = self.output_dir / f"alignment_test_{patient_id}.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"💾 对齐测试可视化已保存: {plot_path}")
    
    def test_multiple_patients(self, max_patients=5):
        """测试多个患者"""
        print(f"🔧 开始简单对齐测试...")
        
        results = []
        
        for patient_id in self.patient_ids[:max_patients]:
            result = self.test_one_patient(patient_id)
            if result:
                results.append(result)
        
        # 总结
        if results:
            print(f"\n🎯 对齐校正总结:")
            original_errors = [r['original_error'] for r in results]
            corrected_errors = [r['corrected_error'] for r in results]
            improvements = [r['improvement'] for r in results]
            improvement_percents = [r['improvement_percent'] for r in results]
            
            print(f"   原始平均误差: {np.mean(original_errors):.2f}±{np.std(original_errors):.2f}mm")
            print(f"   校正后平均误差: {np.mean(corrected_errors):.2f}±{np.std(corrected_errors):.2f}mm")
            print(f"   平均改善: {np.mean(improvements):.2f}±{np.std(improvements):.2f}mm")
            print(f"   平均改善率: {np.mean(improvement_percents):.1f}±{np.std(improvement_percents):.1f}%")
            
            # 分析全局偏移模式
            offsets = np.array([r['global_offset'] for r in results])
            mean_offset = np.mean(offsets, axis=0)
            print(f"   平均全局偏移: ({mean_offset[0]:.2f}, {mean_offset[1]:.2f}, {mean_offset[2]:.2f})mm")
            
            if np.linalg.norm(mean_offset) > 1.0:
                print(f"   ⚠️ 检测到系统性偏移，建议在训练中加入对齐损失")
            else:
                print(f"   ✅ 系统性偏移较小，当前校正方法有效")
        
        return results

def main():
    """主函数"""
    print("🔧 启动简单对齐校正测试...")
    
    tester = SimpleAlignmentTester()
    tester.test_multiple_patients(max_patients=5)

if __name__ == "__main__":
    main()
