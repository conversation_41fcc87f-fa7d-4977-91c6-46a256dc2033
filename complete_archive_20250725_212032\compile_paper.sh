#!/bin/bash

# LaTeX compilation script for Nature Scientific Data paper
# Usage: ./compile_paper.sh

echo "🔧 Compiling 3D Pelvic Keypoint Dataset Paper..."

# Check if pdflatex is available
if ! command -v pdflatex &> /dev/null; then
    echo "❌ Error: pdflatex not found. Please install LaTeX distribution."
    exit 1
fi

# Check if bibtex is available
if ! command -v bibtex &> /dev/null; then
    echo "❌ Error: bibtex not found. Please install LaTeX distribution."
    exit 1
fi

# Main LaTeX file
MAIN_FILE="pelvic_dataset_paper"

echo "📝 First LaTeX compilation..."
pdflatex -interaction=nonstopmode ${MAIN_FILE}.tex

if [ $? -ne 0 ]; then
    echo "❌ Error in first LaTeX compilation"
    exit 1
fi

echo "📚 Processing bibliography..."
bibtex ${MAIN_FILE}

if [ $? -ne 0 ]; then
    echo "⚠️  Warning: Bibliography processing had issues"
fi

echo "📝 Second LaTeX compilation..."
pdflatex -interaction=nonstopmode ${MAIN_FILE}.tex

if [ $? -ne 0 ]; then
    echo "❌ Error in second LaTeX compilation"
    exit 1
fi

echo "📝 Final LaTeX compilation..."
pdflatex -interaction=nonstopmode ${MAIN_FILE}.tex

if [ $? -ne 0 ]; then
    echo "❌ Error in final LaTeX compilation"
    exit 1
fi

# Clean up auxiliary files
echo "🧹 Cleaning up auxiliary files..."
rm -f ${MAIN_FILE}.aux ${MAIN_FILE}.bbl ${MAIN_FILE}.blg ${MAIN_FILE}.log ${MAIN_FILE}.out ${MAIN_FILE}.toc ${MAIN_FILE}.lof ${MAIN_FILE}.lot

echo "✅ Compilation successful! PDF generated: ${MAIN_FILE}.pdf"

# Check if PDF was created
if [ -f "${MAIN_FILE}.pdf" ]; then
    echo "📄 PDF file size: $(du -h ${MAIN_FILE}.pdf | cut -f1)"
    echo "🎉 Paper ready for submission!"
else
    echo "❌ Error: PDF file not generated"
    exit 1
fi
