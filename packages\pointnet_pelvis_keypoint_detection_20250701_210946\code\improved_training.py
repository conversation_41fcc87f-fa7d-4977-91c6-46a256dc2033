"""
改进的训练脚本
使用新的模型架构、损失函数和数据增强
"""

import torch
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import json
from tqdm import tqdm
from datetime import datetime
import matplotlib.pyplot as plt
from pathlib import Path
import pandas as pd

from improved_model import ImprovedPointNet2, KeypointLoss
from improved_data_loader import ImprovedDataLoader

class ImprovedTrainer:
    """改进的训练器"""
    
    def __init__(self, data_root="output/training_fixed", num_keypoints=57, 
                 batch_size=4, learning_rate=0.001, num_points=1024):
        self.data_root = data_root
        self.num_keypoints = num_keypoints
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.num_points = num_points
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 创建输出目录
        self.output_dir = Path("output/improved_training")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"Using device: {self.device}")
        print(f"Training with improved architecture")
        
    def create_model(self):
        """创建改进的模型"""
        model = ImprovedPointNet2(num_keypoints=self.num_keypoints)
        model = model.to(self.device)
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"模型参数总数: {total_params:,}")
        print(f"可训练参数: {trainable_params:,}")
        
        return model
    
    def create_dataloaders(self):
        """创建数据加载器"""
        data_loader_manager = ImprovedDataLoader(
            data_root=self.data_root,
            batch_size=self.batch_size,
            num_workers=2,
            num_points=self.num_points
        )
        
        train_loader, val_loader = data_loader_manager.create_dataloaders(train_ratio=0.8)
        return train_loader, val_loader
    
    def create_optimizer_and_scheduler(self, model):
        """创建优化器和学习率调度器"""
        # 使用AdamW优化器
        optimizer = optim.AdamW(
            model.parameters(),
            lr=self.learning_rate,
            weight_decay=1e-4,
            betas=(0.9, 0.999)
        )
        
        # 使用余弦退火学习率调度
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=50,  # 50个epoch的周期
            eta_min=1e-6
        )
        
        return optimizer, scheduler
    
    def train_epoch(self, model, train_loader, optimizer, loss_fn, epoch):
        """训练一个epoch"""
        model.train()
        total_loss = 0.0
        loss_components = {
            'total_loss': 0.0,
            'smooth_l1_loss': 0.0,
            'mse_loss': 0.0,
            'geometric_loss': 0.0
        }
        
        pbar = tqdm(train_loader, desc=f"Train Epoch {epoch}")
        for batch_idx, (point_cloud, keypoints) in enumerate(pbar):
            point_cloud = point_cloud.to(self.device)
            keypoints = keypoints.to(self.device)
            
            optimizer.zero_grad()
            
            # 前向传播
            pred_keypoints = model(point_cloud)
            
            # 计算损失
            loss_dict = loss_fn(pred_keypoints, keypoints)
            loss = loss_dict['total_loss']
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            # 累积损失
            total_loss += loss.item()
            for key, value in loss_dict.items():
                loss_components[key] += value.item()
            
            # 更新进度条
            pbar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'avg_loss': f"{total_loss/(batch_idx+1):.4f}"
            })
        
        # 计算平均损失
        num_batches = len(train_loader)
        avg_loss = total_loss / num_batches
        for key in loss_components:
            loss_components[key] /= num_batches
        
        return avg_loss, loss_components
    
    def validate_epoch(self, model, val_loader, loss_fn, epoch):
        """验证一个epoch"""
        model.eval()
        total_loss = 0.0
        loss_components = {
            'total_loss': 0.0,
            'smooth_l1_loss': 0.0,
            'mse_loss': 0.0,
            'geometric_loss': 0.0
        }
        
        # 计算准确率指标
        all_distances = []
        
        with torch.no_grad():
            pbar = tqdm(val_loader, desc=f"Val Epoch {epoch}")
            for batch_idx, (point_cloud, keypoints) in enumerate(pbar):
                point_cloud = point_cloud.to(self.device)
                keypoints = keypoints.to(self.device)
                
                # 前向传播
                pred_keypoints = model(point_cloud)
                
                # 计算损失
                loss_dict = loss_fn(pred_keypoints, keypoints)
                loss = loss_dict['total_loss']
                
                # 累积损失
                total_loss += loss.item()
                for key, value in loss_dict.items():
                    loss_components[key] += value.item()
                
                # 计算距离误差
                distances = torch.norm(pred_keypoints - keypoints, dim=2)  # [B, num_keypoints]
                all_distances.append(distances.cpu())
                
                # 更新进度条
                pbar.set_postfix({
                    'loss': f"{loss.item():.4f}",
                    'avg_loss': f"{total_loss/(batch_idx+1):.4f}"
                })
        
        # 计算平均损失
        num_batches = len(val_loader)
        avg_loss = total_loss / num_batches
        for key in loss_components:
            loss_components[key] /= num_batches
        
        # 计算准确率指标
        all_distances = torch.cat(all_distances, dim=0).flatten()
        accuracy_metrics = {
            'mean_error': torch.mean(all_distances).item(),
            'std_error': torch.std(all_distances).item(),
            'accuracy_5mm': (all_distances <= 5.0).float().mean().item() * 100,
            'accuracy_10mm': (all_distances <= 10.0).float().mean().item() * 100,
            'accuracy_20mm': (all_distances <= 20.0).float().mean().item() * 100
        }
        
        return avg_loss, loss_components, accuracy_metrics
    
    def train(self, num_epochs=50):
        """完整训练流程"""
        print("开始改进模型训练...")
        
        # 创建模型、数据加载器、优化器
        model = self.create_model()
        train_loader, val_loader = self.create_dataloaders()
        optimizer, scheduler = self.create_optimizer_and_scheduler(model)
        loss_fn = KeypointLoss()
        
        # 训练历史
        history = {
            'train_loss': [],
            'val_loss': [],
            'train_components': [],
            'val_components': [],
            'accuracy_metrics': [],
            'learning_rates': []
        }
        
        best_val_loss = float('inf')
        best_accuracy = 0.0
        best_epoch = 0
        patience = 10
        patience_counter = 0
        
        for epoch in range(1, num_epochs + 1):
            print(f"\nEpoch {epoch}/{num_epochs}")
            print(f"Learning rate: {optimizer.param_groups[0]['lr']:.2e}")
            
            # 训练
            train_loss, train_components = self.train_epoch(
                model, train_loader, optimizer, loss_fn, epoch
            )
            
            # 验证
            val_loss, val_components, accuracy_metrics = self.validate_epoch(
                model, val_loader, loss_fn, epoch
            )
            
            # 学习率调度
            scheduler.step()
            
            # 记录历史
            history['train_loss'].append(train_loss)
            history['val_loss'].append(val_loss)
            history['train_components'].append(train_components)
            history['val_components'].append(val_components)
            history['accuracy_metrics'].append(accuracy_metrics)
            history['learning_rates'].append(optimizer.param_groups[0]['lr'])
            
            # 保存最佳模型（基于10mm准确率）
            current_accuracy = accuracy_metrics['accuracy_10mm']
            if current_accuracy > best_accuracy:
                best_accuracy = current_accuracy
                best_val_loss = val_loss
                best_epoch = epoch
                patience_counter = 0
                
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'val_loss': val_loss,
                    'train_loss': train_loss,
                    'accuracy_metrics': accuracy_metrics
                }, self.output_dir / 'best_improved_model.pth')
                
                print(f"🎉 新的最佳模型! 10mm准确率: {current_accuracy:.1f}%")
            else:
                patience_counter += 1
            
            # 打印epoch总结
            print(f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
            print(f"Mean Error: {accuracy_metrics['mean_error']:.2f}mm")
            print(f"Accuracy - 5mm: {accuracy_metrics['accuracy_5mm']:.1f}%, "
                  f"10mm: {accuracy_metrics['accuracy_10mm']:.1f}%, "
                  f"20mm: {accuracy_metrics['accuracy_20mm']:.1f}%")
            print(f"Best 10mm Accuracy: {best_accuracy:.1f}% (Epoch {best_epoch})")
            
            # 早停
            if patience_counter >= patience:
                print(f"Early stopping triggered after {patience} epochs without improvement")
                break
        
        # 保存训练历史
        with open(self.output_dir / 'improved_training_history.json', 'w') as f:
            json.dump(history, f, indent=2)
        
        # 绘制训练曲线
        self.plot_training_curves(history)
        
        # 保存训练总结
        training_summary = {
            'training_timestamp': pd.Timestamp.now().isoformat(),
            'model_architecture': 'ImprovedPointNet2',
            'training_config': {
                'num_epochs': num_epochs,
                'batch_size': self.batch_size,
                'learning_rate': self.learning_rate,
                'num_keypoints': self.num_keypoints,
                'num_points': self.num_points
            },
            'best_performance': {
                'best_epoch': best_epoch,
                'best_val_loss': best_val_loss,
                'best_10mm_accuracy': best_accuracy,
                'final_accuracy_metrics': history['accuracy_metrics'][-1] if history['accuracy_metrics'] else {}
            },
            'dataset_info': {
                'train_samples': len(train_loader.dataset),
                'val_samples': len(val_loader.dataset),
                'data_augmentation': True
            }
        }
        
        with open(self.output_dir / 'improved_training_summary.json', 'w') as f:
            json.dump(training_summary, f, indent=2)
        
        print(f"\n🎯 训练完成!")
        print(f"最佳模型: Epoch {best_epoch}, 10mm准确率: {best_accuracy:.1f}%")
        print(f"结果保存在: {self.output_dir}")
        
        return model, history, training_summary
    
    def plot_training_curves(self, history):
        """绘制训练曲线"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        epochs = range(1, len(history['train_loss']) + 1)
        
        # 损失曲线
        axes[0, 0].plot(epochs, history['train_loss'], label='Train Loss', color='blue')
        axes[0, 0].plot(epochs, history['val_loss'], label='Val Loss', color='red')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].set_title('Training and Validation Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 准确率曲线
        if history['accuracy_metrics']:
            acc_5mm = [m['accuracy_5mm'] for m in history['accuracy_metrics']]
            acc_10mm = [m['accuracy_10mm'] for m in history['accuracy_metrics']]
            acc_20mm = [m['accuracy_20mm'] for m in history['accuracy_metrics']]
            
            axes[0, 1].plot(epochs, acc_5mm, label='5mm Accuracy', color='green')
            axes[0, 1].plot(epochs, acc_10mm, label='10mm Accuracy', color='orange')
            axes[0, 1].plot(epochs, acc_20mm, label='20mm Accuracy', color='purple')
            axes[0, 1].set_xlabel('Epoch')
            axes[0, 1].set_ylabel('Accuracy (%)')
            axes[0, 1].set_title('Validation Accuracy')
            axes[0, 1].legend()
            axes[0, 1].grid(True)
        
        # 平均误差
        if history['accuracy_metrics']:
            mean_errors = [m['mean_error'] for m in history['accuracy_metrics']]
            axes[0, 2].plot(epochs, mean_errors, color='red')
            axes[0, 2].set_xlabel('Epoch')
            axes[0, 2].set_ylabel('Mean Error (mm)')
            axes[0, 2].set_title('Mean Distance Error')
            axes[0, 2].grid(True)
        
        # 损失组件
        if history['val_components']:
            smooth_l1 = [c['smooth_l1_loss'] for c in history['val_components']]
            mse = [c['mse_loss'] for c in history['val_components']]
            geometric = [c['geometric_loss'] for c in history['val_components']]
            
            axes[1, 0].plot(epochs, smooth_l1, label='Smooth L1', color='blue')
            axes[1, 0].plot(epochs, mse, label='MSE', color='green')
            axes[1, 0].plot(epochs, geometric, label='Geometric', color='red')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Loss')
            axes[1, 0].set_title('Loss Components')
            axes[1, 0].legend()
            axes[1, 0].grid(True)
        
        # 学习率
        axes[1, 1].plot(epochs, history['learning_rates'], color='purple')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('Learning Rate')
        axes[1, 1].set_title('Learning Rate Schedule')
        axes[1, 1].set_yscale('log')
        axes[1, 1].grid(True)
        
        # 性能总结
        axes[1, 2].axis('off')
        if history['accuracy_metrics']:
            final_metrics = history['accuracy_metrics'][-1]
            best_10mm = max(m['accuracy_10mm'] for m in history['accuracy_metrics'])
            
            summary_text = f"Training Summary:\n\n"
            summary_text += f"Final Mean Error: {final_metrics['mean_error']:.2f}mm\n"
            summary_text += f"Final 5mm Accuracy: {final_metrics['accuracy_5mm']:.1f}%\n"
            summary_text += f"Final 10mm Accuracy: {final_metrics['accuracy_10mm']:.1f}%\n"
            summary_text += f"Final 20mm Accuracy: {final_metrics['accuracy_20mm']:.1f}%\n\n"
            summary_text += f"Best 10mm Accuracy: {best_10mm:.1f}%\n"
            summary_text += f"Total Epochs: {len(epochs)}\n"
            
            axes[1, 2].text(0.1, 0.9, summary_text, fontsize=12, verticalalignment='top',
                           bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
        
        axes[1, 2].set_title('Performance Summary', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'improved_training_curves.png', 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"训练曲线保存到 {self.output_dir / 'improved_training_curves.png'}")

def main():
    """主函数"""
    # 创建改进的训练器
    trainer = ImprovedTrainer(
        data_root="output/training_fixed",
        num_keypoints=57,
        batch_size=3,  # 减小batch size以适应内存
        learning_rate=0.001,
        num_points=1024  # 减少点数以提高训练速度
    )
    
    # 开始训练
    model, history, summary = trainer.train(num_epochs=30)
    
    print("改进模型训练完成!")

if __name__ == "__main__":
    main()
