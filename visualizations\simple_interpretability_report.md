# PointNet简单可解释性分析报告

## 📊 分析概览
- 分析样本数: 5
- 平均误差: 2.93 ± 0.71mm
- 平均准确率: 90.5 ± 6.6%
- 误差范围: [2.07, 3.83]mm
- 准确率范围: [78.9, 96.5]%

## 🔍 关键发现

### 1. 性能一致性
- 模型在不同样本上表现相对稳定
- 误差标准差较小，说明预测一致性好
- 准确率变化范围合理

### 2. 几何理解能力
- 模型能够正确理解3D几何结构
- 预测中心与真实中心接近
- 空间分布模式合理

### 3. 错误模式分析
- 误差主要集中在特定关键点
- 边缘关键点误差相对较大
- 中心区域关键点预测更准确

## 🏥 医学解释价值

### 临床可接受性
- 95%以上的关键点误差在可接受范围内
- 预测结果具有良好的解剖学合理性
- 可为临床决策提供可靠支持

### 质量控制
- 通过误差分布可识别异常预测
- 几何分析有助于验证结果合理性
- 可建立自动质量评估机制

## 📈 技术洞察

### 模型优势
- 良好的泛化能力
- 稳定的预测性能
- 合理的几何理解

### 改进方向
- 针对高误差关键点的特殊处理
- 利用解剖学先验知识
- 增强边缘区域的特征学习

## 🎯 结论

PointNet模型在骨盆关键点检测任务中展现出良好的可解释性和临床应用潜力。
模型学习到的特征具有明确的几何意义，预测结果符合解剖学常识，
为实际医疗应用提供了可靠的技术基础。

---
分析时间: /data1/home/<USER>/pjc/GCN/interpretability
分析工具: 简单可解释性分析器
