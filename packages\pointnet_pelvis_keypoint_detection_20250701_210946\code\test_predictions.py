"""
测试预测结果
加载最佳模型并可视化预测效果
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from pathlib import Path
import json

from save_best_model import BestSimplePointNet
from improved_data_loader import ImprovedDataLoader

class PredictionTester:
    """预测测试器"""
    
    def __init__(self, data_root="output/training_fixed"):
        self.data_root = data_root
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 创建输出目录
        self.output_dir = Path("output/prediction_test")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🔍 预测测试器初始化")
        print(f"Device: {self.device}")
        
    def load_best_model(self):
        """加载最佳模型"""
        print("加载最佳模型...")
        
        # 尝试加载不同的最佳模型
        model_paths = [
            "output/scale_corrected_training/best_baseline_model.pth",
            "debug_model_epoch_7.pth",
            "output/improved_training/best_model.pth"
        ]
        
        model = BestSimplePointNet(num_keypoints=57)
        
        for model_path in model_paths:
            if Path(model_path).exists():
                print(f"加载模型: {model_path}")
                checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
                model.load_state_dict(checkpoint['model_state_dict'])
                
                # 打印模型信息
                if 'accuracy_5mm' in checkpoint:
                    print(f"📊 模型性能: 5mm准确率 {checkpoint['accuracy_5mm']:.1f}%")
                if 'mean_error' in checkpoint:
                    print(f"📊 平均误差: {checkpoint['mean_error']:.2f}mm")
                
                break
        else:
            print("⚠️ 未找到训练好的模型，使用随机权重")
        
        model = model.to(self.device)
        model.eval()
        return model
    
    def load_test_data(self, num_samples=8):
        """加载测试数据"""
        print(f"加载 {num_samples} 个测试样本...")
        
        data_loader_manager = ImprovedDataLoader(
            data_root=self.data_root,
            batch_size=1,
            num_workers=0,
            num_points=512
        )
        
        _, val_loader = data_loader_manager.create_dataloaders(train_ratio=0.8)
        
        samples = []
        for i, (point_cloud, keypoints) in enumerate(val_loader):
            if i >= num_samples:
                break
            
            samples.append({
                'point_cloud': point_cloud,
                'keypoints': keypoints,
                'sample_id': f"sample_{i+1}"
            })
        
        print(f"✅ 加载了 {len(samples)} 个样本")
        return samples
    
    def predict_samples(self, model, samples):
        """对样本进行预测"""
        print("进行预测...")
        
        predictions = []
        ground_truths = []
        errors = []
        
        with torch.no_grad():
            for sample in samples:
                point_cloud = sample['point_cloud'].to(self.device)
                keypoints = sample['keypoints'].to(self.device)
                
                # 预测
                pred_keypoints = model(point_cloud)
                
                # 计算误差
                error = torch.norm(pred_keypoints - keypoints, dim=2).cpu().numpy()
                
                predictions.append(pred_keypoints.cpu().numpy())
                ground_truths.append(keypoints.cpu().numpy())
                errors.append(error)
                
                # 打印每个样本的结果
                sample_error = np.mean(error)
                sample_5mm_acc = (error <= 5.0).mean() * 100
                print(f"{sample['sample_id']}: 平均误差 {sample_error:.2f}mm, 5mm准确率 {sample_5mm_acc:.1f}%")
        
        return predictions, ground_truths, errors
    
    def visualize_predictions_2d(self, samples, predictions, ground_truths, errors):
        """2D可视化预测结果"""
        print("生成2D预测可视化...")
        
        num_samples = len(samples)
        fig, axes = plt.subplots(2, 4, figsize=(20, 10))
        axes = axes.flatten()
        
        for i in range(min(num_samples, 8)):
            ax = axes[i]
            
            # 获取数据
            point_cloud = samples[i]['point_cloud'][0].numpy()
            gt = ground_truths[i][0]  # [57, 3]
            pred = predictions[i][0]  # [57, 3]
            error = errors[i][0]      # [57]
            
            # 绘制点云 (XY平面投影)
            ax.scatter(point_cloud[:, 0], point_cloud[:, 1], 
                      c='lightblue', alpha=0.3, s=1, label='Point Cloud')
            
            # 绘制真实关键点
            ax.scatter(gt[:, 0], gt[:, 1], 
                      c='red', s=40, alpha=0.8, label='Ground Truth', marker='o')
            
            # 绘制预测关键点
            ax.scatter(pred[:, 0], pred[:, 1], 
                      c='blue', s=40, alpha=0.8, label='Prediction', marker='x')
            
            # 连接对应的真实点和预测点
            for j in range(len(gt)):
                ax.plot([gt[j, 0], pred[j, 0]], [gt[j, 1], pred[j, 1]], 
                       'gray', alpha=0.5, linewidth=0.5)
            
            # 设置标题和标签
            mean_error = np.mean(error)
            accuracy_5mm = (error <= 5.0).mean() * 100
            ax.set_title(f'{samples[i]["sample_id"]}\n误差: {mean_error:.2f}mm, 5mm准确率: {accuracy_5mm:.1f}%', 
                        fontsize=10, fontweight='bold')
            ax.set_xlabel('X (mm)')
            ax.set_ylabel('Y (mm)')
            ax.grid(True, alpha=0.3)
            ax.set_aspect('equal')
            
            if i == 0:
                ax.legend(fontsize=8)
        
        # 隐藏多余的子图
        for i in range(num_samples, 8):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'predictions_2d.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 2D预测可视化完成")
    
    def visualize_predictions_3d(self, samples, predictions, ground_truths, errors):
        """3D可视化预测结果"""
        print("生成3D预测可视化...")
        
        num_samples = min(len(samples), 4)  # 只显示前4个样本
        fig = plt.figure(figsize=(20, 15))
        
        for i in range(num_samples):
            ax = fig.add_subplot(2, 2, i+1, projection='3d')
            
            # 获取数据
            point_cloud = samples[i]['point_cloud'][0].numpy()
            gt = ground_truths[i][0]  # [57, 3]
            pred = predictions[i][0]  # [57, 3]
            error = errors[i][0]      # [57]
            
            # 绘制点云
            ax.scatter(point_cloud[:, 0], point_cloud[:, 1], point_cloud[:, 2], 
                      c='lightblue', alpha=0.2, s=1, label='Point Cloud')
            
            # 绘制真实关键点
            ax.scatter(gt[:, 0], gt[:, 1], gt[:, 2], 
                      c='red', s=60, alpha=0.9, label='Ground Truth', 
                      edgecolors='darkred', linewidth=1)
            
            # 绘制预测关键点
            ax.scatter(pred[:, 0], pred[:, 1], pred[:, 2], 
                      c='blue', s=60, alpha=0.9, label='Prediction', 
                      marker='^', edgecolors='darkblue', linewidth=1)
            
            # 连接对应点
            for j in range(len(gt)):
                ax.plot([gt[j, 0], pred[j, 0]], 
                       [gt[j, 1], pred[j, 1]], 
                       [gt[j, 2], pred[j, 2]], 
                       'gray', alpha=0.6, linewidth=1)
            
            # 设置标题和标签
            mean_error = np.mean(error)
            accuracy_5mm = (error <= 5.0).mean() * 100
            ax.set_title(f'{samples[i]["sample_id"]}\n误差: {mean_error:.2f}mm, 准确率: {accuracy_5mm:.1f}%', 
                        fontsize=12, fontweight='bold')
            ax.set_xlabel('X (mm)')
            ax.set_ylabel('Y (mm)')
            ax.set_zlabel('Z (mm)')
            
            # 设置相同的坐标范围
            ax.set_xlim([-40, 40])
            ax.set_ylim([-40, 40])
            ax.set_zlim([-40, 40])
            
            # 设置视角
            ax.view_init(elev=20, azim=45)
            
            if i == 0:
                ax.legend()
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'predictions_3d.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 3D预测可视化完成")
    
    def analyze_prediction_quality(self, predictions, ground_truths, errors):
        """分析预测质量"""
        print("分析预测质量...")
        
        # 转换为numpy数组
        all_predictions = np.concatenate(predictions, axis=0)  # [N, 57, 3]
        all_ground_truths = np.concatenate(ground_truths, axis=0)  # [N, 57, 3]
        all_errors = np.concatenate(errors, axis=0)  # [N, 57]
        
        # 计算整体统计
        flat_errors = all_errors.flatten()
        
        stats = {
            'mean_error': float(np.mean(flat_errors)),
            'std_error': float(np.std(flat_errors)),
            'median_error': float(np.median(flat_errors)),
            'min_error': float(np.min(flat_errors)),
            'max_error': float(np.max(flat_errors)),
            'accuracy_1mm': float((flat_errors <= 1.0).mean() * 100),
            'accuracy_2mm': float((flat_errors <= 2.0).mean() * 100),
            'accuracy_5mm': float((flat_errors <= 5.0).mean() * 100),
            'accuracy_10mm': float((flat_errors <= 10.0).mean() * 100)
        }
        
        # 分析每个关键点的性能
        keypoint_errors = np.mean(all_errors, axis=0)  # [57]
        best_keypoints = np.argsort(keypoint_errors)[:5]
        worst_keypoints = np.argsort(keypoint_errors)[-5:]
        
        # 可视化分析结果
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 误差分布
        axes[0, 0].hist(flat_errors, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('Error Distribution', fontweight='bold')
        axes[0, 0].set_xlabel('Error (mm)')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].axvline(stats['mean_error'], color='red', linestyle='--', 
                          label=f'Mean: {stats["mean_error"]:.2f}mm')
        axes[0, 0].axvline(stats['median_error'], color='green', linestyle='--', 
                          label=f'Median: {stats["median_error"]:.2f}mm')
        axes[0, 0].legend()
        
        # 2. 准确率条形图
        thresholds = ['1mm', '2mm', '5mm', '10mm']
        accuracies = [stats['accuracy_1mm'], stats['accuracy_2mm'], 
                     stats['accuracy_5mm'], stats['accuracy_10mm']]
        colors = ['red', 'orange', 'green', 'blue']
        
        bars = axes[0, 1].bar(thresholds, accuracies, color=colors, alpha=0.7)
        axes[0, 1].set_title('Accuracy at Different Thresholds', fontweight='bold')
        axes[0, 1].set_ylabel('Accuracy (%)')
        axes[0, 1].set_ylim(0, 105)
        
        for bar, acc in zip(bars, accuracies):
            axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                           f'{acc:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # 3. 每个关键点的误差
        axes[1, 0].bar(range(len(keypoint_errors)), keypoint_errors, alpha=0.7, color='lightcoral')
        axes[1, 0].set_title('Error by Keypoint', fontweight='bold')
        axes[1, 0].set_xlabel('Keypoint Index')
        axes[1, 0].set_ylabel('Mean Error (mm)')
        axes[1, 0].axhline(stats['mean_error'], color='red', linestyle='--', 
                          label=f'Overall Mean: {stats["mean_error"]:.2f}mm')
        axes[1, 0].legend()
        
        # 4. 最佳和最差关键点
        x_pos = np.arange(5)
        width = 0.35
        
        axes[1, 1].bar(x_pos - width/2, keypoint_errors[best_keypoints], width, 
                      label='Best 5', color='green', alpha=0.7)
        axes[1, 1].bar(x_pos + width/2, keypoint_errors[worst_keypoints], width, 
                      label='Worst 5', color='red', alpha=0.7)
        
        axes[1, 1].set_title('Best vs Worst Keypoints', fontweight='bold')
        axes[1, 1].set_xlabel('Rank')
        axes[1, 1].set_ylabel('Mean Error (mm)')
        axes[1, 1].set_xticks(x_pos)
        axes[1, 1].set_xticklabels([f'{i+1}' for i in range(5)])
        axes[1, 1].legend()
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'prediction_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 保存统计结果
        detailed_stats = {
            'overall_statistics': stats,
            'best_keypoints': {
                'indices': best_keypoints.tolist(),
                'errors': keypoint_errors[best_keypoints].tolist()
            },
            'worst_keypoints': {
                'indices': worst_keypoints.tolist(),
                'errors': keypoint_errors[worst_keypoints].tolist()
            },
            'keypoint_errors': keypoint_errors.tolist()
        }
        
        with open(self.output_dir / 'prediction_statistics.json', 'w') as f:
            json.dump(detailed_stats, f, indent=2)
        
        print("✅ 预测质量分析完成")
        return stats, detailed_stats
    
    def generate_prediction_report(self, stats, detailed_stats):
        """生成预测报告"""
        print("生成预测报告...")
        
        with open(self.output_dir / 'prediction_report.txt', 'w', encoding='utf-8') as f:
            f.write("预测结果分析报告\n")
            f.write("=" * 30 + "\n\n")
            
            f.write("整体性能统计:\n")
            f.write("-" * 15 + "\n")
            f.write(f"平均误差: {stats['mean_error']:.2f} ± {stats['std_error']:.2f}mm\n")
            f.write(f"中位数误差: {stats['median_error']:.2f}mm\n")
            f.write(f"误差范围: [{stats['min_error']:.2f}, {stats['max_error']:.2f}]mm\n\n")
            
            f.write("准确率统计:\n")
            f.write("-" * 15 + "\n")
            f.write(f"1mm准确率: {stats['accuracy_1mm']:.1f}%\n")
            f.write(f"2mm准确率: {stats['accuracy_2mm']:.1f}%\n")
            f.write(f"5mm准确率: {stats['accuracy_5mm']:.1f}%\n")
            f.write(f"10mm准确率: {stats['accuracy_10mm']:.1f}%\n\n")
            
            f.write("关键点分析:\n")
            f.write("-" * 15 + "\n")
            f.write("表现最佳的5个关键点:\n")
            for i, (idx, error) in enumerate(zip(detailed_stats['best_keypoints']['indices'], 
                                               detailed_stats['best_keypoints']['errors'])):
                f.write(f"  {i+1}. 关键点 #{idx}: {error:.2f}mm\n")
            
            f.write("\n表现最差的5个关键点:\n")
            for i, (idx, error) in enumerate(zip(detailed_stats['worst_keypoints']['indices'], 
                                               detailed_stats['worst_keypoints']['errors'])):
                f.write(f"  {i+1}. 关键点 #{idx}: {error:.2f}mm\n")
            
            f.write(f"\n模型评价:\n")
            f.write("-" * 15 + "\n")
            if stats['accuracy_5mm'] >= 90:
                f.write("🎉 优秀! 5mm准确率超过90%，达到临床应用标准\n")
            elif stats['accuracy_5mm'] >= 80:
                f.write("✅ 良好! 5mm准确率超过80%，性能可接受\n")
            else:
                f.write("⚠️ 需要改进! 5mm准确率低于80%\n")
            
            if stats['mean_error'] <= 3.0:
                f.write("🎯 精确! 平均误差小于3mm，定位精度很高\n")
            elif stats['mean_error'] <= 5.0:
                f.write("👍 合格! 平均误差在可接受范围内\n")
            else:
                f.write("📈 有改进空间! 平均误差较大\n")
        
        print("✅ 预测报告生成完成")
    
    def run_prediction_test(self):
        """运行完整的预测测试"""
        print("🚀 开始预测测试...")
        
        # 1. 加载模型和数据
        model = self.load_best_model()
        samples = self.load_test_data(num_samples=8)
        
        # 2. 进行预测
        predictions, ground_truths, errors = self.predict_samples(model, samples)
        
        # 3. 可视化结果
        self.visualize_predictions_2d(samples, predictions, ground_truths, errors)
        self.visualize_predictions_3d(samples, predictions, ground_truths, errors)
        
        # 4. 分析预测质量
        stats, detailed_stats = self.analyze_prediction_quality(predictions, ground_truths, errors)
        
        # 5. 生成报告
        self.generate_prediction_report(stats, detailed_stats)
        
        print(f"\n🎯 预测测试完成!")
        print(f"📁 结果保存在: {self.output_dir}")
        print(f"📊 整体性能: 平均误差 {stats['mean_error']:.2f}mm, 5mm准确率 {stats['accuracy_5mm']:.1f}%")
        
        return stats, detailed_stats

def main():
    """主函数"""
    tester = PredictionTester(data_root="output/training_fixed")
    
    stats, detailed_stats = tester.run_prediction_test()
    
    print("🎉 预测测试完成!")
    print(f"模型表现: {stats['accuracy_5mm']:.1f}% 5mm准确率，{stats['mean_error']:.2f}mm 平均误差")

if __name__ == "__main__":
    main()
