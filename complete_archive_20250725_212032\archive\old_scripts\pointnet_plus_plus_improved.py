#!/usr/bin/env python3
"""
PointNet++ 改进方案
基于层次化特征提取和注意力机制
专门针对骶骨等解剖结构的局部特征提取
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

def square_distance(src, dst):
    """计算点之间的平方距离"""
    B, N, _ = src.shape
    _, M, _ = dst.shape
    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))
    dist += torch.sum(src ** 2, -1).view(B, N, 1)
    dist += torch.sum(dst ** 2, -1).view(B, 1, M)
    return dist

def index_points(points, idx):
    """根据索引提取点"""
    device = points.device
    B = points.shape[0]
    view_shape = list(idx.shape)
    view_shape[1:] = [1] * (len(view_shape) - 1)
    repeat_shape = list(idx.shape)
    repeat_shape[0] = 1
    batch_indices = torch.arange(B, dtype=torch.long).to(device).view(view_shape).repeat(repeat_shape)
    new_points = points[batch_indices, idx, :]
    return new_points

def farthest_point_sample(xyz, npoint):
    """最远点采样"""
    device = xyz.device
    B, N, C = xyz.shape
    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)
    distance = torch.ones(B, N).to(device) * 1e10
    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)
    batch_indices = torch.arange(B, dtype=torch.long).to(device)
    
    for i in range(npoint):
        centroids[:, i] = farthest
        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)
        dist = torch.sum((xyz - centroid) ** 2, -1)
        mask = dist < distance
        distance[mask] = dist[mask]
        farthest = torch.max(distance, -1)[1]
    
    return centroids

def query_ball_point(radius, nsample, xyz, new_xyz):
    """球查询"""
    device = xyz.device
    B, N, C = xyz.shape
    _, S, _ = new_xyz.shape
    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])
    sqrdists = square_distance(new_xyz, xyz)
    group_idx[sqrdists > radius ** 2] = N
    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]
    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])
    mask = group_idx == N
    group_idx[mask] = group_first[mask]
    return group_idx

class AnatomicalAttention(nn.Module):
    """解剖结构注意力机制"""
    
    def __init__(self, in_channel, out_channel):
        super(AnatomicalAttention, self).__init__()
        
        self.in_channel = in_channel
        self.out_channel = out_channel
        
        # 空间注意力
        self.spatial_attention = nn.Sequential(
            nn.Conv1d(in_channel, in_channel // 4, 1),
            nn.BatchNorm1d(in_channel // 4),
            nn.ReLU(),
            nn.Conv1d(in_channel // 4, 1, 1),
            nn.Sigmoid()
        )
        
        # 通道注意力
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool1d(1),
            nn.Conv1d(in_channel, in_channel // 4, 1),
            nn.ReLU(),
            nn.Conv1d(in_channel // 4, in_channel, 1),
            nn.Sigmoid()
        )
        
        # 特征融合
        self.feature_fusion = nn.Sequential(
            nn.Conv1d(in_channel, out_channel, 1),
            nn.BatchNorm1d(out_channel),
            nn.ReLU()
        )
        
    def forward(self, x):
        # x: [B, C, N]
        
        # 空间注意力
        spatial_att = self.spatial_attention(x)  # [B, 1, N]
        
        # 通道注意力
        channel_att = self.channel_attention(x)  # [B, C, 1]
        
        # 应用注意力
        attended_features = x * spatial_att * channel_att
        
        # 特征融合
        output = self.feature_fusion(attended_features)
        
        return output

class LocalGeometryExtractor(nn.Module):
    """局部几何特征提取器"""
    
    def __init__(self, radius, nsample, in_channel, mlp):
        super(LocalGeometryExtractor, self).__init__()
        
        self.radius = radius
        self.nsample = nsample
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        
        last_channel = in_channel + 3  # 坐标 + 特征
        
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm2d(out_channel))
            last_channel = out_channel
            
        # 几何特征计算
        self.geometry_mlp = nn.Sequential(
            nn.Conv2d(3, 64, 1),  # 处理相对坐标
            nn.BatchNorm2d(64),
            nn.ReLU(),
            nn.Conv2d(64, 128, 1),
            nn.BatchNorm2d(128),
            nn.ReLU()
        )
        
    def forward(self, xyz, points, new_xyz):
        """
        Args:
            xyz: [B, N, 3] 原始点坐标
            points: [B, C, N] 点特征
            new_xyz: [B, S, 3] 采样点坐标
        """
        B, N, C = xyz.shape
        _, S, _ = new_xyz.shape
        
        # 球查询
        idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)  # [B, S, nsample]
        grouped_xyz = index_points(xyz, idx)  # [B, S, nsample, 3]
        
        # 计算相对坐标
        grouped_xyz_norm = grouped_xyz - new_xyz.view(B, S, 1, 3)  # [B, S, nsample, 3]
        
        # 提取几何特征
        geometry_features = self.geometry_mlp(grouped_xyz_norm.permute(0, 3, 1, 2))  # [B, 128, S, nsample]
        
        if points is not None:
            grouped_points = index_points(points.permute(0, 2, 1), idx)  # [B, S, nsample, C]
            grouped_points = grouped_points.permute(0, 3, 1, 2)  # [B, C, S, nsample]
            
            # 拼接坐标和特征
            new_points = torch.cat([grouped_xyz_norm.permute(0, 3, 1, 2), grouped_points], dim=1)
        else:
            new_points = grouped_xyz_norm.permute(0, 3, 1, 2)
        
        # 添加几何特征
        new_points = torch.cat([new_points, geometry_features], dim=1)
        
        # MLP处理
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            new_points = F.relu(bn(conv(new_points)))
        
        # 最大池化
        new_points = torch.max(new_points, -1)[0]  # [B, mlp[-1], S]
        
        return new_points

class HierarchicalPointNet(nn.Module):
    """层次化PointNet++"""
    
    def __init__(self, num_keypoints=12):
        super(HierarchicalPointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 第一层：细粒度特征 (1024 -> 512)
        self.sa1 = LocalGeometryExtractor(
            radius=2.0, nsample=32, in_channel=0, mlp=[64, 64, 128]
        )
        self.att1 = AnatomicalAttention(128, 128)
        
        # 第二层：中等粒度特征 (512 -> 128)
        self.sa2 = LocalGeometryExtractor(
            radius=4.0, nsample=64, in_channel=128, mlp=[128, 128, 256]
        )
        self.att2 = AnatomicalAttention(256, 256)
        
        # 第三层：粗粒度特征 (128 -> 32)
        self.sa3 = LocalGeometryExtractor(
            radius=8.0, nsample=128, in_channel=256, mlp=[256, 512, 1024]
        )
        self.att3 = AnatomicalAttention(1024, 1024)
        
        # 全局特征处理
        self.global_mlp = nn.Sequential(
            nn.Conv1d(1024, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Conv1d(512, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 关键点预测头
        self.keypoint_head = nn.Sequential(
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(64, num_keypoints * 3)
        )
        
        print(f"🧠 层次化PointNet++: {num_keypoints}个关键点")
        print(f"   - 多尺度特征提取: 2.0mm -> 4.0mm -> 8.0mm")
        print(f"   - 解剖结构注意力机制")
        print(f"   - 局部几何特征增强")
        
    def forward(self, xyz):
        """
        Args:
            xyz: [B, N, 3] 输入点云
        """
        B, N, _ = xyz.shape
        
        # 第一层采样和特征提取
        l1_xyz = xyz
        l1_points = None
        
        # 采样点
        l2_xyz_idx = farthest_point_sample(l1_xyz, 512)
        l2_xyz = index_points(l1_xyz, l2_xyz_idx)
        l2_points = self.sa1(l1_xyz, l1_points, l2_xyz)  # [B, 128, 512]
        l2_points = self.att1(l2_points)
        
        # 第二层
        l3_xyz_idx = farthest_point_sample(l2_xyz, 128)
        l3_xyz = index_points(l2_xyz, l3_xyz_idx)
        l3_points = self.sa2(l2_xyz, l2_points, l3_xyz)  # [B, 256, 128]
        l3_points = self.att2(l3_points)
        
        # 第三层
        l4_xyz_idx = farthest_point_sample(l3_xyz, 32)
        l4_xyz = index_points(l3_xyz, l4_xyz_idx)
        l4_points = self.sa3(l3_xyz, l3_points, l4_xyz)  # [B, 1024, 32]
        l4_points = self.att3(l4_points)
        
        # 全局特征
        global_features = self.global_mlp(l4_points)  # [B, 256, 32]
        global_features = torch.max(global_features, -1)[0]  # [B, 256]
        
        # 关键点预测
        keypoints = self.keypoint_head(global_features)  # [B, num_keypoints * 3]
        keypoints = keypoints.view(B, self.num_keypoints, 3)
        
        return keypoints

def test_hierarchical_pointnet():
    """测试层次化PointNet++"""
    
    print("🧪 **测试层次化PointNet++**")
    print("🎯 **多尺度特征提取 + 解剖结构注意力**")
    print("=" * 80)
    
    batch_size = 2
    num_points = 4096
    num_keypoints = 12
    
    # 创建测试数据
    test_input = torch.randn(batch_size, num_points, 3)
    
    print(f"📊 测试输入: {test_input.shape}")
    
    # 测试模型
    model = HierarchicalPointNet(num_keypoints=num_keypoints)
    
    with torch.no_grad():
        output = model(test_input)
        print(f"📊 输出形状: {output.shape}")
    
    # 参数统计
    total_params = sum(p.numel() for p in model.parameters())
    print(f"\n📊 模型参数: {total_params:,}")
    
    print(f"\n✅ 层次化PointNet++测试通过!")
    
    return model

if __name__ == "__main__":
    model = test_hierarchical_pointnet()
    
    print(f"\n🎉 **层次化PointNet++准备完成!**")
    print("=" * 50)
    print(f"🔬 核心创新:")
    print(f"   1. 多尺度局部特征提取 (2mm->4mm->8mm)")
    print(f"   2. 解剖结构注意力机制")
    print(f"   3. 几何特征增强")
    print(f"   4. 层次化特征融合")
    print(f"🎯 预期改进:")
    print(f"   - 更好的局部特征提取 (骶骨孔洞等)")
    print(f"   - 多尺度空间理解")
    print(f"   - 注意力聚焦重要结构")
