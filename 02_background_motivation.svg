<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="1280" height="720" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="url(#headerGrad)"/>
  <text x="640" y="50" text-anchor="middle" fill="white" 
        font-family="Arial, sans-serif" font-size="36" font-weight="bold">
    Research Background & Motivation
  </text>
  
  <!-- Left side: 3D Keypoint Importance -->
  <rect x="50" y="120" width="580" height="280" rx="15" fill="white" stroke="#cbd5e1" stroke-width="2"/>
  <text x="340" y="155" text-anchor="middle" fill="#1e40af" 
        font-family="Arial, sans-serif" font-size="24" font-weight="bold">
    3D Keypoint Detection Importance
  </text>
  
  <!-- Medical applications icons -->
  <circle cx="150" cy="220" r="30" fill="#10b981" opacity="0.2"/>
  <text x="150" y="230" text-anchor="middle" fill="#10b981" 
        font-family="Arial, sans-serif" font-size="24" font-weight="bold">🏥</text>
  <text x="150" y="270" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="14" font-weight="600">
    Clinical Diagnosis
  </text>
  
  <circle cx="340" cy="220" r="30" fill="#f59e0b" opacity="0.2"/>
  <text x="340" y="230" text-anchor="middle" fill="#f59e0b" 
        font-family="Arial, sans-serif" font-size="24" font-weight="bold">⚕️</text>
  <text x="340" y="270" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="14" font-weight="600">
    Surgical Planning
  </text>
  
  <circle cx="530" cy="220" r="30" fill="#8b5cf6" opacity="0.2"/>
  <text x="530" y="230" text-anchor="middle" fill="#8b5cf6" 
        font-family="Arial, sans-serif" font-size="24" font-weight="bold">🔧</text>
  <text x="530" y="270" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="14" font-weight="600">
    Navigation
  </text>
  
  <!-- Key applications -->
  <text x="70" y="320" fill="#374151" font-family="Arial, sans-serif" font-size="16">
    • Gonion detection for facial contouring (125°-135° angle)
  </text>
  <text x="70" y="345" fill="#374151" font-family="Arial, sans-serif" font-size="16">
    • Pedicle screw implantation guidance
  </text>
  <text x="70" y="370" fill="#374151" font-family="Arial, sans-serif" font-size="16">
    • Anatomically significant landmark identification
  </text>
  
  <!-- Right side: Current Limitations -->
  <rect x="650" y="120" width="580" height="280" rx="15" fill="white" stroke="#cbd5e1" stroke-width="2"/>
  <text x="940" y="155" text-anchor="middle" fill="#dc2626" 
        font-family="Arial, sans-serif" font-size="24" font-weight="bold">
    Current Method Limitations
  </text>
  
  <!-- 2D vs 3D comparison -->
  <rect x="680" y="180" width="240" height="100" rx="8" fill="#fef2f2" stroke="#fca5a5" stroke-width="1"/>
  <text x="800" y="205" text-anchor="middle" fill="#dc2626" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    2D Multi-view Methods
  </text>
  <text x="690" y="230" fill="#7f1d1d" font-family="Arial, sans-serif" font-size="12">
    ❌ High annotation workload
  </text>
  <text x="690" y="250" fill="#7f1d1d" font-family="Arial, sans-serif" font-size="12">
    ❌ 3D overlapping issues
  </text>
  <text x="690" y="270" fill="#7f1d1d" font-family="Arial, sans-serif" font-size="12">
    ❌ Manual inconsistencies
  </text>
  
  <rect x="940" y="180" width="240" height="100" rx="8" fill="#fef3c7" stroke="#fcd34d" stroke-width="1"/>
  <text x="1060" y="205" text-anchor="middle" fill="#d97706" 
        font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    Unsupervised 3D Methods
  </text>
  <text x="950" y="230" fill="#92400e" font-family="Arial, sans-serif" font-size="12">
    ❌ No semantic meaning
  </text>
  <text x="950" y="250" fill="#92400e" font-family="Arial, sans-serif" font-size="12">
    ❌ Limited clinical relevance
  </text>
  <text x="950" y="270" fill="#92400e" font-family="Arial, sans-serif" font-size="12">
    ❌ Feature-based only
  </text>
  
  <!-- Data complexity -->
  <text x="670" y="320" fill="#374151" font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    Medical Point Cloud Challenges:
  </text>
  <text x="670" y="345" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    • Dense structure (60K-600K points)
  </text>
  <text x="670" y="365" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    • Complex topology
  </text>
  <text x="670" y="385" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    • Limited annotated datasets
  </text>
  
  <!-- Bottom: Our Solution Preview -->
  <rect x="50" y="430" width="1180" height="240" rx="15" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="2"/>
  <text x="640" y="465" text-anchor="middle" fill="#0c4a6e" 
        font-family="Arial, sans-serif" font-size="28" font-weight="bold">
    Our Solution: Direct 3D Supervised Keypoint Detection
  </text>
  
  <!-- Solution features -->
  <rect x="100" y="490" width="300" height="150" rx="10" fill="white" stroke="#0ea5e9" stroke-width="1"/>
  <text x="250" y="520" text-anchor="middle" fill="#0c4a6e" 
        font-family="Arial, sans-serif" font-size="18" font-weight="bold">
    Coarse-to-Fine Framework
  </text>
  <text x="120" y="550" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    ✓ Two-stage detection
  </text>
  <text x="120" y="570" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    ✓ Potential region identification
  </text>
  <text x="120" y="590" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    ✓ Precise localization
  </text>
  <text x="120" y="610" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    ✓ Semantic labels
  </text>
  
  <rect x="440" y="490" width="300" height="150" rx="10" fill="white" stroke="#10b981" stroke-width="1"/>
  <text x="590" y="520" text-anchor="middle" fill="#065f46" 
        font-family="Arial, sans-serif" font-size="18" font-weight="bold">
    Novel Components
  </text>
  <text x="460" y="550" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    ✓ Penalty Dice Loss
  </text>
  <text x="460" y="570" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    ✓ Double SoftMax mechanism
  </text>
  <text x="460" y="590" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    ✓ Residual modules
  </text>
  <text x="460" y="610" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    ✓ Multi-scale features
  </text>
  
  <rect x="780" y="490" width="300" height="150" rx="10" fill="white" stroke="#f59e0b" stroke-width="1"/>
  <text x="930" y="520" text-anchor="middle" fill="#92400e" 
        font-family="Arial, sans-serif" font-size="18" font-weight="bold">
    Clinical Performance
  </text>
  <text x="800" y="550" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    ✓ 1.43mm MRE (skull)
  </text>
  <text x="800" y="570" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    ✓ 76% success rate (2mm)
  </text>
  <text x="800" y="590" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    ✓ Comparable to 2D methods
  </text>
  <text x="800" y="610" fill="#374151" font-family="Arial, sans-serif" font-size="14">
    ✓ Direct 3D detection
  </text>
</svg>
