#!/usr/bin/env python3
"""
Train PointNet++ on High Quality 50K Point F3 Dataset

Train PointNet++ model on our newly created high-quality 50K point F3 dataset
for medical-grade keypoint detection.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import h5py
from pathlib import Path
import time
import json
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split

class HighQualityF3Dataset(Dataset):
    """Dataset loader for high quality 50K point F3 dataset"""
    
    def __init__(self, data_path: str, split: str = 'train', test_size: float = 0.2, 
                 val_size: float = 0.1, random_state: int = 42, augment: bool = False):
        
        self.augment = augment
        
        # Load the high quality dataset
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']  # (N, 50000, 3)
        keypoints = data['keypoints']        # (N, 19, 3)
        centers = data['centers']            # (N, 3)
        
        print(f"📦 加载数据集: {len(sample_ids)} 样本")
        print(f"   点云形状: {point_clouds.shape}")
        print(f"   关键点形状: {keypoints.shape}")
        
        # Split data
        indices = np.arange(len(sample_ids))
        train_indices, temp_indices = train_test_split(
            indices, test_size=test_size + val_size, random_state=random_state
        )
        val_indices, test_indices = train_test_split(
            temp_indices, test_size=test_size/(test_size + val_size), random_state=random_state
        )
        
        if split == 'train':
            self.indices = train_indices
        elif split == 'val':
            self.indices = val_indices
        elif split == 'test':
            self.indices = test_indices
        else:
            raise ValueError(f"Unknown split: {split}")
        
        self.sample_ids = sample_ids[self.indices]
        self.point_clouds = point_clouds[self.indices]
        self.keypoints = keypoints[self.indices]
        self.centers = centers[self.indices]
        
        print(f"   {split} 分割: {len(self.indices)} 样本")
    
    def __len__(self):
        return len(self.indices)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()  # (50000, 3)
        keypoints = self.keypoints[idx].copy()       # (19, 3)
        
        # Data augmentation for training
        if self.augment:
            # Random rotation around Z-axis (medical data should preserve anatomical orientation)
            angle = np.random.uniform(-np.pi/12, np.pi/12)  # ±15 degrees
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            
            point_cloud = point_cloud @ rotation_matrix.T
            keypoints = keypoints @ rotation_matrix.T
            
            # Small random translation
            translation = np.random.uniform(-2.0, 2.0, 3)  # ±2mm
            point_cloud += translation
            keypoints += translation
            
            # Add small amount of noise
            noise = np.random.normal(0, 0.1, point_cloud.shape)  # 0.1mm std
            point_cloud += noise
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),  # (50000, 3)
            'keypoints': torch.FloatTensor(keypoints),      # (19, 3)
            'sample_id': self.sample_ids[idx]
        }

class PointNetPlusPlus(nn.Module):
    """PointNet++ for medical keypoint detection"""
    
    def __init__(self, num_keypoints: int = 19, num_points: int = 50000):
        super(PointNetPlusPlus, self).__init__()
        
        self.num_keypoints = num_keypoints
        self.num_points = num_points
        
        # Set Abstraction layers
        self.sa1 = SetAbstractionLayer(num_points//4, 0.2, 32, 3, [64, 64, 128])
        self.sa2 = SetAbstractionLayer(num_points//16, 0.4, 32, 128, [128, 128, 256])
        self.sa3 = SetAbstractionLayer(num_points//64, 0.8, 32, 256, [256, 512, 1024])
        
        # Feature Propagation layers
        self.fp3 = FeaturePropagationLayer(1024, [256, 256])
        self.fp2 = FeaturePropagationLayer(256, [256, 128])
        self.fp1 = FeaturePropagationLayer(128, [128, 128, 128])
        
        # Final prediction layers
        self.conv1 = nn.Conv1d(128, 128, 1)
        self.bn1 = nn.BatchNorm1d(128)
        self.drop1 = nn.Dropout(0.5)
        self.conv2 = nn.Conv1d(128, num_keypoints * 3, 1)
        
    def forward(self, xyz):
        # xyz: (batch_size, num_points, 3)
        batch_size = xyz.size(0)
        
        # Set Abstraction
        l1_xyz, l1_points = self.sa1(xyz, None)
        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)
        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)
        
        # Feature Propagation
        l2_points = self.fp3(l2_xyz, l3_xyz, l2_points, l3_points)
        l1_points = self.fp2(l1_xyz, l2_xyz, l1_points, l2_points)
        l0_points = self.fp1(xyz, l1_xyz, None, l1_points)
        
        # Final prediction
        feat = torch.relu(self.bn1(self.conv1(l0_points)))
        feat = self.drop1(feat)
        keypoints = self.conv2(feat)  # (batch_size, num_keypoints*3, num_points)
        
        # Global average pooling to get final keypoints
        keypoints = torch.mean(keypoints, dim=2)  # (batch_size, num_keypoints*3)
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

class SetAbstractionLayer(nn.Module):
    """Set Abstraction Layer for PointNet++"""
    
    def __init__(self, npoint, radius, nsample, in_channel, mlp):
        super(SetAbstractionLayer, self).__init__()
        self.npoint = npoint
        self.radius = radius
        self.nsample = nsample
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        
        last_channel = in_channel
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm2d(out_channel))
            last_channel = out_channel
    
    def forward(self, xyz, points):
        # Simplified implementation - use farthest point sampling and ball query
        batch_size, num_points, _ = xyz.size()
        
        # Farthest point sampling
        if self.npoint < num_points:
            # Simple random sampling for now (can be improved with FPS)
            indices = torch.randperm(num_points)[:self.npoint]
            new_xyz = xyz[:, indices, :]
        else:
            new_xyz = xyz
        
        # For simplicity, use all points as neighbors (can be improved with ball query)
        if points is None:
            points = xyz.transpose(1, 2).contiguous()  # (batch_size, 3, num_points)
        
        # Apply MLPs
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            points = torch.relu(bn(conv(points.unsqueeze(-1)))).squeeze(-1)
        
        # Max pooling
        new_points = torch.max(points, dim=2)[0]  # (batch_size, channel)
        new_points = new_points.unsqueeze(2).repeat(1, 1, new_xyz.size(1))
        
        return new_xyz, new_points

class FeaturePropagationLayer(nn.Module):
    """Feature Propagation Layer for PointNet++"""
    
    def __init__(self, in_channel, mlp):
        super(FeaturePropagationLayer, self).__init__()
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        
        last_channel = in_channel
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv1d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm1d(out_channel))
            last_channel = out_channel
    
    def forward(self, xyz1, xyz2, points1, points2):
        # Simplified interpolation
        if points1 is not None:
            points = points1
        else:
            points = points2
        
        # Apply MLPs
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            points = torch.relu(bn(conv(points)))
        
        return points

def calculate_metrics(pred_keypoints, target_keypoints):
    """Calculate evaluation metrics"""
    # pred_keypoints, target_keypoints: (batch_size, num_keypoints, 3)
    
    distances = torch.norm(pred_keypoints - target_keypoints, dim=2)  # (batch_size, num_keypoints)
    
    # Average distance per sample
    avg_distances = torch.mean(distances, dim=1)  # (batch_size,)
    
    # Overall metrics
    mean_distance = torch.mean(avg_distances).item()
    std_distance = torch.std(avg_distances).item()
    max_distance = torch.max(avg_distances).item()
    
    # Accuracy metrics
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_10mm = (avg_distances <= 10.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_distance,
        'std_distance': std_distance,
        'max_distance': max_distance,
        'within_1mm_percent': within_1mm,
        'within_5mm_percent': within_5mm,
        'within_10mm_percent': within_10mm
    }

def train_pointnetpp_f3():
    """Train PointNet++ on high quality F3 dataset"""

    print("🚀 **训练PointNet++模型 - 高质量50K点F3数据集**")
    print("🎯 **目标: 医疗级关键点检测精度 (<1mm)**")
    print("=" * 80)

    # Device setup
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  使用设备: {device}")

    # Dataset setup
    dataset_path = "high_quality_f3_dataset.npz"

    if not Path(dataset_path).exists():
        print(f"❌ 数据集文件不存在: {dataset_path}")
        return None

    # Create datasets
    train_dataset = HighQualityF3Dataset(dataset_path, split='train', augment=True)
    val_dataset = HighQualityF3Dataset(dataset_path, split='val', augment=False)
    test_dataset = HighQualityF3Dataset(dataset_path, split='test', augment=False)

    # Data loaders
    batch_size = 2  # Small batch size for 50K points
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=4)

    print(f"📊 数据集统计:")
    print(f"   训练样本: {len(train_dataset)}")
    print(f"   验证样本: {len(val_dataset)}")
    print(f"   测试样本: {len(test_dataset)}")
    print(f"   批量大小: {batch_size}")
    print(f"   点云大小: 50,000 点")
    print(f"   关键点数: 19 个F3关键点")

    # Model setup
    model = PointNetPlusPlus(num_keypoints=19, num_points=50000).to(device)

    total_params = sum(p.numel() for p in model.parameters())
    print(f"\n🧠 PointNet++模型:")
    print(f"   总参数: {total_params:,}")
    print(f"   模型大小: {total_params * 4 / (1024 * 1024):.1f}MB")

    # Training setup
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=10, min_lr=1e-6, verbose=True
    )

    # Training configuration
    num_epochs = 200
    best_val_error = float('inf')
    patience_counter = 0
    patience = 20

    # Training history
    training_history = []

    print(f"\n🎯 开始训练 (目标: <1mm医疗级精度)")
    print(f"📈 训练轮数: {num_epochs}, 早停耐心: {patience}")

    start_time = time.time()

    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 60)

        # Training phase
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_1mm_percent': 0, 'within_5mm_percent': 0}

        for batch_idx, batch in enumerate(train_loader):
            point_cloud = batch['point_cloud'].to(device)  # (batch_size, 50000, 3)
            keypoints = batch['keypoints'].to(device)      # (batch_size, 19, 3)

            optimizer.zero_grad()

            # Forward pass
            pred_keypoints = model(point_cloud)  # (batch_size, 19, 3)

            # Calculate loss
            loss = criterion(pred_keypoints, keypoints)

            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()

            train_loss += loss.item()

            # Calculate metrics
            with torch.no_grad():
                metrics = calculate_metrics(pred_keypoints, keypoints)
                for key in train_metrics:
                    train_metrics[key] += metrics[key]

            if (batch_idx + 1) % 10 == 0:
                print(f"   Batch {batch_idx+1}/{len(train_loader)}: Loss={loss.item():.4f}")

        # Average training metrics
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)

        # Validation phase
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_1mm_percent': 0, 'within_5mm_percent': 0}

        with torch.no_grad():
            for batch in val_loader:
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)

                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)

                val_loss += loss.item()

                metrics = calculate_metrics(pred_keypoints, keypoints)
                for key in val_metrics:
                    val_metrics[key] += metrics[key]

        # Average validation metrics
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)

        # Learning rate scheduling
        scheduler.step(val_metrics['mean_distance'])

        # Print epoch results
        print(f"📊 训练结果:")
        print(f"   训练损失: {train_loss:.4f}")
        print(f"   训练误差: {train_metrics['mean_distance']:.3f}mm")
        print(f"   训练1mm精度: {train_metrics['within_1mm_percent']:.1f}%")
        print(f"   训练5mm精度: {train_metrics['within_5mm_percent']:.1f}%")

        print(f"📊 验证结果:")
        print(f"   验证损失: {val_loss:.4f}")
        print(f"   验证误差: {val_metrics['mean_distance']:.3f}mm")
        print(f"   验证1mm精度: {val_metrics['within_1mm_percent']:.1f}%")
        print(f"   验证5mm精度: {val_metrics['within_5mm_percent']:.1f}%")

        # Save training history
        epoch_data = {
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'learning_rate': optimizer.param_groups[0]['lr']
        }
        training_history.append(epoch_data)

        # Check for improvement
        current_val_error = val_metrics['mean_distance']
        if current_val_error < best_val_error:
            best_val_error = current_val_error
            patience_counter = 0

            # Save best model
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics
            }, 'best_pointnetpp_f3_model.pth')

            print(f"🎉 新的最佳模型! 验证误差: {best_val_error:.3f}mm")
        else:
            patience_counter += 1
            print(f"⏳ 无改善 ({patience_counter}/{patience})")

        # Early stopping
        if patience_counter >= patience:
            print(f"🛑 早停触发! 最佳验证误差: {best_val_error:.3f}mm")
            break

    total_time = time.time() - start_time

    print(f"\n🎯 PointNet++ F3模型训练完成!")
    print(f"   训练时间: {total_time/60:.1f} 分钟")
    print(f"   最佳验证误差: {best_val_error:.3f}mm")
    print(f"   医疗级目标: {'✅ 达成' if best_val_error <= 1.0 else '❌ 未达成'}")

    # Test evaluation
    print(f"\n🧪 **最终测试评估**")
    model.load_state_dict(torch.load('best_pointnetpp_f3_model.pth')['model_state_dict'])
    model.eval()

    test_metrics = {'mean_distance': 0, 'std_distance': 0, 'within_1mm_percent': 0, 'within_5mm_percent': 0}

    with torch.no_grad():
        for batch in test_loader:
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)

            pred_keypoints = model(point_cloud)
            metrics = calculate_metrics(pred_keypoints, keypoints)

            for key in test_metrics:
                test_metrics[key] += metrics[key]

    for key in test_metrics:
        test_metrics[key] /= len(test_loader)

    print(f"📊 测试结果:")
    print(f"   测试误差: {test_metrics['mean_distance']:.3f}±{test_metrics['std_distance']:.3f}mm")
    print(f"   测试1mm精度: {test_metrics['within_1mm_percent']:.1f}%")
    print(f"   测试5mm精度: {test_metrics['within_5mm_percent']:.1f}%")

    # Save results
    results = {
        'training_completed': True,
        'best_validation_error_mm': best_val_error,
        'test_metrics': test_metrics,
        'training_time_minutes': total_time / 60,
        'total_epochs': len(training_history),
        'dataset_samples': len(train_dataset) + len(val_dataset) + len(test_dataset),
        'model_parameters': total_params,
        'training_history': training_history
    }

    with open('pointnetpp_f3_50k_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)

    return best_val_error, test_metrics, total_time

if __name__ == "__main__":
    try:
        best_error, test_metrics, training_time = train_pointnetpp_f3()

        print(f"\n🎉 **PointNet++ F3训练成功完成!**")
        print(f"🎯 最终结果:")
        print(f"   验证误差: {best_error:.3f}mm")
        print(f"   测试误差: {test_metrics['mean_distance']:.3f}mm")
        print(f"   测试1mm精度: {test_metrics['within_1mm_percent']:.1f}%")
        print(f"   测试5mm精度: {test_metrics['within_5mm_percent']:.1f}%")
        print(f"⏱️ 训练时间: {training_time/60:.1f} 分钟")

        # Medical grade assessment
        if test_metrics['mean_distance'] <= 1.0:
            print(f"🏆 **医疗级精度达成!** (<1mm)")
        elif test_metrics['mean_distance'] <= 5.0:
            print(f"✅ **医疗可用精度达成!** (<5mm)")
        else:
            print(f"❌ **未达到医疗级精度要求**")

    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        import traceback
        traceback.print_exc()
