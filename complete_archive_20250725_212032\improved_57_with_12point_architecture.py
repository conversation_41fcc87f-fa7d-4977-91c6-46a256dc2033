#!/usr/bin/env python3
"""
基于12点模型成功架构的改进57点模型
Improved 57-point model based on successful 12-point architecture
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import json
from tqdm import tqdm

class AdaptivePointNet57(nn.Module):
    """基于成功12点架构的57点模型 - 增强稳定性版本"""

    def __init__(self, num_keypoints=57, dropout_rate=0.3):  # 降低dropout
        super(AdaptivePointNet57, self).__init__()

        self.num_keypoints = num_keypoints
        
        # 🎯 核心特征提取 - 与12点模型完全相同
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # 🛡️ 批归一化 - 与12点模型相同
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 🔗 残差连接 - 12点模型的关键特性
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        # 📉 渐进式回归头 - 适配57点但保持12点的设计理念
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        # 🛡️ 批归一化 - 全连接层
        self.fc_bn1 = nn.BatchNorm1d(512)
        self.fc_bn2 = nn.BatchNorm1d(256)
        self.fc_bn3 = nn.BatchNorm1d(128)
        self.fc_bn4 = nn.BatchNorm1d(64)
        
        # 🎲 Dropout - 与12点模型相同的策略
        self.dropout = nn.Dropout(dropout_rate)
        
        # 🎯 权重初始化 - 提高训练稳定性
        self._initialize_weights()

        print(f"🏗️ AdaptivePointNet57 构建完成:")
        total_params = sum(p.numel() for p in self.parameters())
        print(f"   总参数: {total_params:,}")
        print(f"   输出关键点: {num_keypoints}")
        print(f"   Dropout率: {dropout_rate}")
        print(f"   权重初始化: Xavier + 零偏置")

    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
        
    def forward(self, x):
        """前向传播 - 复制12点模型的成功模式"""
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 🎯 特征提取 + 残差连接
        x1 = F.relu(self.bn1(self.conv1(x)))           # [B, 64, N]
        x2 = F.relu(self.bn2(self.conv2(x1)))          # [B, 128, N]
        x3 = F.relu(self.bn3(self.conv3(x2)))          # [B, 256, N]
        
        # 第一个残差连接
        x3_res = x3 + self.residual1(x1)               # [B, 256, N]
        
        x4 = F.relu(self.bn4(self.conv4(x3_res)))      # [B, 512, N]
        
        # 第二个残差连接
        x4_res = x4 + self.residual2(x2)               # [B, 512, N]
        
        x5 = F.relu(self.bn5(self.conv5(x4_res)))      # [B, 1024, N]
        
        # 🌐 全局最大池化
        global_feat = torch.max(x5, 2)[0]              # [B, 1024]
        
        # 📉 渐进式回归
        x = F.relu(self.fc_bn1(self.fc1(global_feat)))
        x = self.dropout(x)
        
        x = F.relu(self.fc_bn2(self.fc2(x)))
        x = self.dropout(x)
        
        x = F.relu(self.fc_bn3(self.fc3(x)))
        x = self.dropout(x)
        
        x = F.relu(self.fc_bn4(self.fc4(x)))
        x = self.dropout(x)
        
        # 最终输出
        keypoints = self.fc5(x)                         # [B, 57*3]
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

class ImprovedLoss(nn.Module):
    """改进的损失函数 - 增强版本"""

    def __init__(self, alpha=0.7, beta=0.2, gamma=0.1):
        super(ImprovedLoss, self).__init__()
        self.alpha = alpha  # MSE权重
        self.beta = beta    # 平滑L1权重
        self.gamma = gamma  # Huber权重

    def forward(self, pred, target):
        mse_loss = F.mse_loss(pred, target)
        smooth_l1_loss = F.smooth_l1_loss(pred, target)
        huber_loss = F.huber_loss(pred, target, delta=1.0)

        return self.alpha * mse_loss + self.beta * smooth_l1_loss + self.gamma * huber_loss

class Dataset57(Dataset):
    """57点数据集"""
    
    def __init__(self, point_clouds, keypoints):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def train_improved_57_model(model, train_loader, val_loader, epochs=200, device='cuda'):
    """训练改进的57点模型 - 使用12点模型的成功配置"""
    
    print(f"🚀 训练改进的57点模型...")
    print(f"   基于12点模型的成功架构和训练策略")
    
    model = model.to(device)
    
    # 🎯 优化器配置 - 更稳定的学习率
    optimizer = optim.AdamW(model.parameters(), lr=0.0005, weight_decay=1e-4)  # 降低学习率和权重衰减

    # 📈 学习率调度 - 更保守的调度
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.8, patience=15, min_lr=5e-7
    )
    
    # 🎯 损失函数 - 与12点模型相同
    criterion = ImprovedLoss(alpha=0.8, beta=0.2)
    
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_error': [],
        'val_error': []
    }
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 30  # 更长的耐心，允许更充分的训练
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{epochs} [Train]')
        for batch_pc, batch_kp in train_pbar:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)

            loss.backward()

            # 🎯 梯度裁剪 - 提高训练稳定性
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

            optimizer.step()
            
            train_loss += loss.item()
            
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
            
            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Error': f'{torch.mean(distances).item():.2f}mm'
            })
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{epochs} [Val]')
            for batch_pc, batch_kp in val_pbar:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                val_loss += loss.item()
                
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
                
                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Error': f'{torch.mean(distances).item():.2f}mm'
                })
        
        # 计算平均值
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_error'].append(train_error)
        history['val_error'].append(val_error)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_improved_57_model.pth')
        else:
            patience_counter += 1
        
        # 打印进度
        current_lr = optimizer.param_groups[0]['lr']
        print(f"Epoch {epoch+1:3d}: "
              f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
              f"Train Error: {train_error:.2f}mm, Val Error: {val_error:.2f}mm, "
              f"LR: {current_lr:.2e}")

        # 每10个epoch详细报告
        if (epoch + 1) % 10 == 0:
            print(f"   📊 第{epoch+1}轮详细报告:")
            print(f"      最佳验证误差: {best_val_loss:.6f}")
            print(f"      耐心计数: {patience_counter}/{patience}")
            print(f"      当前学习率: {current_lr:.2e}")
        
        # 早停
        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    print(f"✅ 训练完成！最佳验证损失: {best_val_loss:.6f}")
    
    # 加载最佳模型
    model.load_state_dict(torch.load('best_improved_57_model.pth'))
    
    return history

def evaluate_improved_57_model(model, test_loader, device='cuda'):
    """评估改进的57点模型"""
    
    print(f"\n🔍 评估改进的57点模型性能...")
    
    model = model.to(device)
    model.eval()
    
    total_error = 0.0
    region_errors = {'F1': [], 'F2': [], 'F3': []}
    
    with torch.no_grad():
        test_pbar = tqdm(test_loader, desc='Testing')
        for batch_pc, batch_kp in test_pbar:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            predicted = model(batch_pc)
            
            # 计算整体误差
            distances = torch.norm(predicted - batch_kp, dim=2)
            total_error += torch.mean(distances).item()
            
            # 计算各区域误差
            for i in range(predicted.size(0)):
                pred = predicted[i].cpu().numpy()
                target = batch_kp[i].cpu().numpy()
                
                # F1区域 (0-18)
                f1_distances = np.linalg.norm(pred[0:19] - target[0:19], axis=1)
                region_errors['F1'].extend(f1_distances)
                
                # F2区域 (19-37)
                f2_distances = np.linalg.norm(pred[19:38] - target[19:38], axis=1)
                region_errors['F2'].extend(f2_distances)
                
                # F3区域 (38-56)
                f3_distances = np.linalg.norm(pred[38:57] - target[38:57], axis=1)
                region_errors['F3'].extend(f3_distances)
            
            test_pbar.set_postfix({'Error': f'{torch.mean(distances).item():.2f}mm'})
    
    avg_error = total_error / len(test_loader)
    
    print(f"📊 改进57点模型评估结果:")
    print(f"   整体平均误差: {avg_error:.2f}mm")
    
    for region, errors in region_errors.items():
        if errors:
            mean_error = np.mean(errors)
            std_error = np.std(errors)
            max_error = np.max(errors)
            print(f"   {region}区域: {mean_error:.2f}±{std_error:.2f}mm (最大: {max_error:.2f}mm)")
    
    # 计算医疗级准确率
    all_errors = []
    for errors in region_errors.values():
        all_errors.extend(errors)
    
    if all_errors:
        accuracy_5mm = np.mean(np.array(all_errors) < 5.0) * 100
        accuracy_10mm = np.mean(np.array(all_errors) < 10.0) * 100
        
        print(f"   医疗级准确率:")
        print(f"     <5mm: {accuracy_5mm:.1f}%")
        print(f"     <10mm: {accuracy_10mm:.1f}%")
    
    return {
        'avg_error': avg_error,
        'region_errors': region_errors,
        'accuracy_5mm': accuracy_5mm if all_errors else 0,
        'accuracy_10mm': accuracy_10mm if all_errors else 0
    }

def main():
    """主函数"""
    
    print("🎯 基于12点模型成功架构的57点模型")
    print("复制12点模型的所有成功特性")
    print("=" * 80)
    
    # 检查CUDA
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载简单平移对齐的数据（已证明更好）
    print("📊 加载简单平移对齐数据集...")
    data = np.load('unified_pelvis_57_dataset.npz', allow_pickle=True)
    
    point_clouds = data['point_clouds']
    keypoints_57 = data['keypoints_57']
    sample_ids = data['sample_ids']
    
    print(f"✅ 数据加载成功: {len(sample_ids)} 个样本")
    
    # 数据划分 - 与12点模型相同的策略
    indices = np.arange(len(point_clouds))
    train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
    train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
    
    # 创建数据集
    train_dataset = Dataset57(point_clouds[train_indices], keypoints_57[train_indices])
    val_dataset = Dataset57(point_clouds[val_indices], keypoints_57[val_indices])
    test_dataset = Dataset57(point_clouds[test_indices], keypoints_57[test_indices])
    
    # 创建数据加载器 - 使用12点模型的最佳批次大小
    batch_size = 8  # 12点模型发现的最佳配置
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    
    print(f"📋 数据划分:")
    print(f"   训练集: {len(train_dataset)} 样本")
    print(f"   验证集: {len(val_dataset)} 样本")
    print(f"   测试集: {len(test_dataset)} 样本")
    print(f"   批次大小: {batch_size} (12点模型最佳配置)")
    
    # 创建模型 - 使用优化的配置
    model = AdaptivePointNet57(num_keypoints=57, dropout_rate=0.3)  # 降低dropout提高稳定性

    # 训练模型 - 更长的训练时间
    print(f"\n🚀 开始训练，预计时间较长...")
    print(f"   关键改进: 降低学习率、梯度裁剪、更好初始化、更长训练")
    history = train_improved_57_model(model, train_loader, val_loader, epochs=200, device=device)
    
    # 评估模型
    results = evaluate_improved_57_model(model, test_loader, device=device)
    
    # 保存结果
    results_summary = {
        'avg_error': results['avg_error'],
        'accuracy_5mm': results['accuracy_5mm'],
        'accuracy_10mm': results['accuracy_10mm'],
        'region_errors': {k: float(np.mean(v)) for k, v in results['region_errors'].items()},
        'training_history': history,
        'architecture_info': {
            'based_on': '12point_AdaptivePointNet',
            'key_features': [
                'residual_connections',
                'batch_normalization',
                'progressive_regression_head',
                'improved_loss_function',
                'optimal_hyperparameters'
            ],
            'total_params': sum(p.numel() for p in model.parameters()),
            'dropout_rate': 0.4,
            'batch_size': batch_size
        }
    }
    
    with open('improved_57_model_results.json', 'w') as f:
        json.dump(results_summary, f, indent=2, default=str)
    
    print(f"\n🎉 改进57点模型训练完成！")
    print(f"📋 生成的文件:")
    print(f"   - best_improved_57_model.pth (最佳模型)")
    print(f"   - improved_57_model_results.json (评估结果)")
    
    print(f"\n🎯 改进57点模型性能:")
    print(f"   平均误差: {results['avg_error']:.2f}mm")
    print(f"   医疗级准确率 (<5mm): {results['accuracy_5mm']:.1f}%")
    
    print(f"\n📊 性能对比:")
    print(f"   12点模型: 6.208mm")
    print(f"   简单57点模型: 15.50mm")
    print(f"   改进57点模型: {results['avg_error']:.2f}mm")
    
    if results['avg_error'] < 12.0:
        print(f"✅ 改进57点模型性能优秀！")
        print(f"💡 成功应用了12点模型的架构优势")
    elif results['avg_error'] < 15.0:
        print(f"✅ 改进57点模型有显著提升！")
        print(f"💡 12点模型架构确实有效")
    else:
        print(f"⚠️ 仍需进一步优化")

if __name__ == "__main__":
    main()
