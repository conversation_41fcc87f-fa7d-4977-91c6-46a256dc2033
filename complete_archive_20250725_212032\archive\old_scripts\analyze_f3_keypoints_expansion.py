#!/usr/bin/env python3
"""
分析F3区域关键点扩展
基于现有的12个F3关键点，分析如何合理扩展到19个关键点
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import pandas as pd

def load_f3_sample_annotation(sample_id="600008"):
    """加载F3样本的真实标注"""
    
    csv_path = f"Data/annotations/{sample_id}-Table-XYZ.CSV"
    
    try:
        df = pd.read_csv(csv_path, encoding='gbk')
    except:
        try:
            df = pd.read_csv(csv_path, encoding='utf-8')
        except:
            df = pd.read_csv(csv_path, encoding='latin-1')
    
    # 提取F3区域的关键点
    f3_mask = df['label'].str.startswith('F_3')
    f3_data = df[f3_mask]
    
    keypoints = f3_data[['X', 'Y', 'Z']].values
    labels = f3_data['label'].values.tolist()
    
    return keypoints, labels

def analyze_f3_anatomy(keypoints, labels):
    """分析F3区域的解剖结构"""
    
    print(f"🔍 F3区域解剖分析")
    print(f"当前关键点数量: {len(keypoints)}")
    print("=" * 50)
    
    # 计算几何特征
    centroid = np.mean(keypoints, axis=0)
    
    # Z轴分析
    z_coords = keypoints[:, 2]
    z_min_idx = np.argmin(z_coords)
    z_max_idx = np.argmax(z_coords)
    
    print(f"📊 几何特征:")
    print(f"   重心: ({centroid[0]:.1f}, {centroid[1]:.1f}, {centroid[2]:.1f})")
    print(f"   Z轴范围: {np.min(z_coords):.1f} - {np.max(z_coords):.1f}mm")
    print(f"   最高点: {labels[z_max_idx]} at Z={z_coords[z_max_idx]:.1f}")
    print(f"   最低点: {labels[z_min_idx]} at Z={z_coords[z_min_idx]:.1f}")
    
    # X轴分析（左右）
    x_coords = keypoints[:, 0]
    x_min_idx = np.argmin(x_coords)
    x_max_idx = np.argmax(x_coords)
    
    print(f"   X轴范围: {np.min(x_coords):.1f} - {np.max(x_coords):.1f}mm")
    print(f"   最左点: {labels[x_min_idx]} at X={x_coords[x_min_idx]:.1f}")
    print(f"   最右点: {labels[x_max_idx]} at X={x_coords[x_max_idx]:.1f}")
    
    # Y轴分析（前后）
    y_coords = keypoints[:, 1]
    y_min_idx = np.argmin(y_coords)
    y_max_idx = np.argmax(y_coords)
    
    print(f"   Y轴范围: {np.min(y_coords):.1f} - {np.max(y_coords):.1f}mm")
    print(f"   最前点: {labels[y_min_idx]} at Y={y_coords[y_min_idx]:.1f}")
    print(f"   最后点: {labels[y_max_idx]} at Y={y_coords[y_max_idx]:.1f}")
    
    return {
        'centroid': centroid,
        'z_extremes': (z_min_idx, z_max_idx),
        'x_extremes': (x_min_idx, x_max_idx),
        'y_extremes': (y_min_idx, y_max_idx),
        'ranges': {
            'x': (np.min(x_coords), np.max(x_coords)),
            'y': (np.min(y_coords), np.max(y_coords)),
            'z': (np.min(z_coords), np.max(z_coords))
        }
    }

def propose_additional_keypoints(keypoints, labels, analysis):
    """提出额外的7个关键点"""
    
    print(f"\n💡 提议的7个额外关键点:")
    print("=" * 50)
    
    additional_keypoints = []
    additional_labels = []
    
    # 1. 几何中心点
    centroid = analysis['centroid']
    additional_keypoints.append(centroid)
    additional_labels.append("F3-Centroid")
    print(f"1. F3-Centroid: 骶骨几何中心 ({centroid[0]:.1f}, {centroid[1]:.1f}, {centroid[2]:.1f})")
    
    # 2. Z轴最高点（如果不在现有点中）
    z_max_point = keypoints[analysis['z_extremes'][1]]
    additional_keypoints.append(z_max_point + [0, 0, 2])  # 稍微偏移避免重复
    additional_labels.append("F3-ZMax")
    print(f"2. F3-ZMax: Z轴最高区域 ({z_max_point[0]:.1f}, {z_max_point[1]:.1f}, {z_max_point[2]+2:.1f})")
    
    # 3. Z轴最低点（如果不在现有点中）
    z_min_point = keypoints[analysis['z_extremes'][0]]
    additional_keypoints.append(z_min_point + [0, 0, -2])  # 稍微偏移
    additional_labels.append("F3-ZMin")
    print(f"3. F3-ZMin: Z轴最低区域 ({z_min_point[0]:.1f}, {z_min_point[1]:.1f}, {z_min_point[2]-2:.1f})")
    
    # 4. 左侧边界点
    x_min_point = keypoints[analysis['x_extremes'][0]]
    additional_keypoints.append(x_min_point + [-2, 0, 0])
    additional_labels.append("F3-LeftBound")
    print(f"4. F3-LeftBound: 左侧边界 ({x_min_point[0]-2:.1f}, {x_min_point[1]:.1f}, {x_min_point[2]:.1f})")
    
    # 5. 右侧边界点
    x_max_point = keypoints[analysis['x_extremes'][1]]
    additional_keypoints.append(x_max_point + [2, 0, 0])
    additional_labels.append("F3-RightBound")
    print(f"5. F3-RightBound: 右侧边界 ({x_max_point[0]+2:.1f}, {x_max_point[1]:.1f}, {x_max_point[2]:.1f})")
    
    # 6. 前方边界点
    y_min_point = keypoints[analysis['y_extremes'][0]]
    additional_keypoints.append(y_min_point + [0, -2, 0])
    additional_labels.append("F3-FrontBound")
    print(f"6. F3-FrontBound: 前方边界 ({y_min_point[0]:.1f}, {y_min_point[1]-2:.1f}, {y_min_point[2]:.1f})")
    
    # 7. 后方边界点
    y_max_point = keypoints[analysis['y_extremes'][1]]
    additional_keypoints.append(y_max_point + [0, 2, 0])
    additional_labels.append("F3-BackBound")
    print(f"7. F3-BackBound: 后方边界 ({y_max_point[0]:.1f}, {y_max_point[1]+2:.1f}, {y_max_point[2]:.1f})")
    
    return np.array(additional_keypoints), additional_labels

def create_f3_19kp_visualization(original_kp, original_labels, additional_kp, additional_labels):
    """创建F3 19关键点可视化"""
    
    fig = plt.figure(figsize=(20, 12))
    
    # 1. 3D总览
    ax1 = fig.add_subplot(2, 3, 1, projection='3d')
    
    # 原始12个关键点
    ax1.scatter(original_kp[:, 0], original_kp[:, 1], original_kp[:, 2],
               c='blue', s=100, marker='o', label='Original 12', alpha=0.8)
    
    # 新增7个关键点
    ax1.scatter(additional_kp[:, 0], additional_kp[:, 1], additional_kp[:, 2],
               c='red', s=100, marker='^', label='Additional 7', alpha=0.8)
    
    # 添加标签
    for i, label in enumerate(original_labels):
        ax1.text(original_kp[i, 0], original_kp[i, 1], original_kp[i, 2] + 1,
                label.split('-')[-1], fontsize=6, ha='center')
    
    for i, label in enumerate(additional_labels):
        ax1.text(additional_kp[i, 0], additional_kp[i, 1], additional_kp[i, 2] + 1,
                label.split('-')[-1], fontsize=6, ha='center', color='red')
    
    ax1.set_title('F3 Region: 12 + 7 = 19 Keypoints')
    ax1.set_xlabel('X (mm)')
    ax1.set_ylabel('Y (mm)')
    ax1.set_zlabel('Z (mm)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. XY投影
    ax2 = fig.add_subplot(2, 3, 2)
    ax2.scatter(original_kp[:, 0], original_kp[:, 1], c='blue', s=80, marker='o', alpha=0.8)
    ax2.scatter(additional_kp[:, 0], additional_kp[:, 1], c='red', s=80, marker='^', alpha=0.8)
    ax2.set_title('XY Projection (Top View)')
    ax2.set_xlabel('X (mm)')
    ax2.set_ylabel('Y (mm)')
    ax2.grid(True, alpha=0.3)
    ax2.axis('equal')
    
    # 3. XZ投影
    ax3 = fig.add_subplot(2, 3, 3)
    ax3.scatter(original_kp[:, 0], original_kp[:, 2], c='blue', s=80, marker='o', alpha=0.8)
    ax3.scatter(additional_kp[:, 0], additional_kp[:, 2], c='red', s=80, marker='^', alpha=0.8)
    ax3.set_title('XZ Projection (Front View)')
    ax3.set_xlabel('X (mm)')
    ax3.set_ylabel('Z (mm)')
    ax3.grid(True, alpha=0.3)
    ax3.axis('equal')
    
    # 4. YZ投影
    ax4 = fig.add_subplot(2, 3, 4)
    ax4.scatter(original_kp[:, 1], original_kp[:, 2], c='blue', s=80, marker='o', alpha=0.8)
    ax4.scatter(additional_kp[:, 1], additional_kp[:, 2], c='red', s=80, marker='^', alpha=0.8)
    ax4.set_title('YZ Projection (Side View)')
    ax4.set_xlabel('Y (mm)')
    ax4.set_ylabel('Z (mm)')
    ax4.grid(True, alpha=0.3)
    ax4.axis('equal')
    
    # 5. 关键点列表
    ax5 = fig.add_subplot(2, 3, 5)
    ax5.axis('off')
    
    keypoint_text = "F3 Region Keypoints:\n\n"
    keypoint_text += "Original 12 Points:\n"
    for i, label in enumerate(original_labels):
        keypoint_text += f"{i+1:2d}. {label}\n"
    
    keypoint_text += "\nAdditional 7 Points:\n"
    for i, label in enumerate(additional_labels):
        keypoint_text += f"{i+13:2d}. {label}\n"
    
    ax5.text(0.05, 0.95, keypoint_text, transform=ax5.transAxes, fontsize=9,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 6. 几何特征分析
    ax6 = fig.add_subplot(2, 3, 6)
    ax6.axis('off')
    
    all_kp = np.vstack([original_kp, additional_kp])
    
    stats_text = f"""
F3 Region Statistics:

Geometric Properties:
• Total Points: {len(all_kp)}
• X Range: {np.min(all_kp[:, 0]):.1f} - {np.max(all_kp[:, 0]):.1f} mm
• Y Range: {np.min(all_kp[:, 1]):.1f} - {np.max(all_kp[:, 1]):.1f} mm  
• Z Range: {np.min(all_kp[:, 2]):.1f} - {np.max(all_kp[:, 2]):.1f} mm

Centroid: ({np.mean(all_kp[:, 0]):.1f}, {np.mean(all_kp[:, 1]):.1f}, {np.mean(all_kp[:, 2]):.1f})

Coverage:
• Width (X): {np.max(all_kp[:, 0]) - np.min(all_kp[:, 0]):.1f} mm
• Depth (Y): {np.max(all_kp[:, 1]) - np.min(all_kp[:, 1]):.1f} mm
• Height (Z): {np.max(all_kp[:, 2]) - np.min(all_kp[:, 2]):.1f} mm

Additional Points Strategy:
✓ Geometric extremes (Z-max, Z-min)
✓ Boundary points (L/R, F/B)
✓ Anatomical centroid
✓ Enhanced spatial coverage
"""
    
    ax6.text(0.05, 0.95, stats_text, transform=ax6.transAxes, fontsize=9,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    plt.suptitle('F3 Region: Expansion from 12 to 19 Keypoints\n'
                'Enhanced Anatomical Coverage for Sacral Region', 
                fontsize=16, fontweight='bold')
    
    plt.tight_layout(rect=[0, 0, 1, 0.93])
    
    filename = 'f3_19keypoints_expansion_plan.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 F3 expansion plan saved: {filename}")
    plt.close()

def create_f3_19kp_dataset_plan():
    """创建F3 19关键点数据集计划"""
    
    print(f"\n📋 F3 19关键点数据集创建计划")
    print("=" * 60)
    
    plan = {
        "dataset_name": "F3_19KeyPoints_Medical",
        "base_dataset": "F3_12KeyPoints_50K (current)",
        "expansion_strategy": "Geometric + Anatomical",
        "additional_points": 7,
        "total_points": 19,
        "point_cloud_density": "50K points",
        "expected_benefits": [
            "更详细的骶骨解剖覆盖",
            "几何极值点增强边界检测",
            "中心点提供整体参考",
            "边界点改善空间定位",
            "保持医疗级精度要求"
        ],
        "implementation_steps": [
            "1. 基于现有12点数据生成7个几何特征点",
            "2. 验证新增点的解剖学合理性",
            "3. 更新数据加载和预处理流程",
            "4. 调整模型架构支持19个输出",
            "5. 重新训练并评估性能",
            "6. 对比12点vs19点的性能差异"
        ],
        "quality_targets": {
            "average_error": "< 6mm (vs current ~5mm)",
            "accuracy_5mm": "> 40%",
            "medical_grade": "Suitable for clinical reference",
            "stability": "Consistent across samples"
        }
    }
    
    for key, value in plan.items():
        print(f"\n{key.replace('_', ' ').title()}:")
        if isinstance(value, list):
            for item in value:
                print(f"   • {item}")
        elif isinstance(value, dict):
            for subkey, subvalue in value.items():
                print(f"   {subkey}: {subvalue}")
        else:
            print(f"   {value}")
    
    return plan

def main():
    """主函数"""
    print("🔍 F3区域关键点扩展分析")
    print("从12个关键点扩展到19个关键点的策略")
    print("=" * 60)
    
    # 加载F3样本数据
    try:
        keypoints, labels = load_f3_sample_annotation("600008")
        print(f"✅ 成功加载样本600008的F3标注")
        print(f"   关键点数量: {len(keypoints)}")
        print(f"   标签: {labels}")
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return
    
    # 分析F3解剖结构
    analysis = analyze_f3_anatomy(keypoints, labels)
    
    # 提出额外关键点
    additional_kp, additional_labels = propose_additional_keypoints(keypoints, labels, analysis)
    
    # 创建可视化
    create_f3_19kp_visualization(keypoints, labels, additional_kp, additional_labels)
    
    # 创建数据集计划
    plan = create_f3_19kp_dataset_plan()
    
    print(f"\n🎯 总结:")
    print("✅ F3区域适合扩展到19个关键点")
    print("✅ 新增7个点具有明确的几何和解剖意义")
    print("✅ 保持了与现有12点的兼容性")
    print("✅ 增强了骶骨区域的空间覆盖")
    print("\n💡 建议:")
    print("1. 先在小规模数据上验证19点模型")
    print("2. 对比12点vs19点的性能差异")
    print("3. 确保新增点在所有样本中都有意义")
    print("4. 考虑医生对新增点的临床价值评估")

if __name__ == "__main__":
    main()
