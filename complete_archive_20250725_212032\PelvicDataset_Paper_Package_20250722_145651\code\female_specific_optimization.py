#!/usr/bin/env python3
"""
女性专门优化策略：针对女性骨盆特点的专门优化
Female-Specific Optimization: Tailored approach for female pelvic anatomy
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import os
from tqdm import tqdm

class FemaleSpecificConstraintLoss(nn.Module):
    """女性专门的解剖学约束损失函数"""
    
    def __init__(self, alpha=1.0, beta=0.4, gamma=0.2, delta=0.3):
        super(FemaleSpecificConstraintLoss, self).__init__()
        self.alpha = alpha    # 基础MSE权重
        self.beta = beta      # 距离约束权重 (增加)
        self.gamma = gamma    # 角度约束权重 (减少)
        self.delta = delta    # 对称性约束权重 (增加)
        
        # 基于女性解剖学统计的约束
        self.female_distance_stats = {
            'F1_1-F2_1': {'mean': 71.57, 'std': 6.26},
            'F1_2-F2_2': {'mean': 101.37, 'std': 6.77},
            'F1_3-F2_3': {'mean': 41.84, 'std': 3.84},
            'F1_4-F2_4': {'mean': 39.22, 'std': 3.12},
            'F1_1-2': {'mean': 89.25, 'std': 9.99},
            'F1_2-3': {'mean': 71.00, 'std': 5.64},
            'F2_1-2': {'mean': 17.21, 'std': 2.72},
            'F2_2-3': {'mean': 67.24, 'std': 6.07},
            'F3_1-2': {'mean': 16.59, 'std': 3.03},
            'F3_2-3': {'mean': 88.42, 'std': 7.17},
        }
        
        # 女性特定的约束
        self.female_constraints = [
            # 强对称性约束 (女性骨盆更对称)
            ([0, 4], 'strong_symmetry', 2.0),  # F1-1 和 F2-1
            ([1, 5], 'strong_symmetry', 2.0),  # F1-2 和 F2-2
            ([2, 6], 'strong_symmetry', 1.8),  # F1-3 和 F2-3
            ([3, 7], 'strong_symmetry', 1.8),  # F1-4 和 F2-4
            
            # 关键距离约束
            ([0, 1], 'key_distance', 1.5),     # F1区域关键距离
            ([4, 5], 'key_distance', 1.5),     # F2区域关键距离
            ([8, 9], 'key_distance', 1.3),     # F3区域关键距离
            
            # 女性特有的解剖学约束
            ([0, 8], 'female_specific', 1.2),  # F1-1 到 F3-1
            ([4, 8], 'female_specific', 1.2),  # F2-1 到 F3-1
        ]
        
    def forward(self, pred_kp, target_kp):
        # 基础MSE损失
        mse_loss = F.mse_loss(pred_kp, target_kp)
        
        # 女性特定距离约束
        distance_loss = self.compute_female_distance_constraints(pred_kp, target_kp)
        
        # 女性特定角度约束
        angle_loss = self.compute_female_angle_constraints(pred_kp, target_kp)
        
        # 女性特定对称性约束
        symmetry_loss = self.compute_female_symmetry_constraints(pred_kp, target_kp)
        
        # 总损失
        total_loss = (self.alpha * mse_loss + 
                     self.beta * distance_loss + 
                     self.gamma * angle_loss + 
                     self.delta * symmetry_loss)
        
        return total_loss, mse_loss, distance_loss, angle_loss, symmetry_loss
    
    def compute_female_distance_constraints(self, pred_kp, target_kp):
        """女性特定距离约束"""
        distance_losses = []
        
        for constraint in self.female_constraints:
            indices, constraint_type, weight = constraint
            idx1, idx2 = indices
            
            # 预测距离和真实距离
            pred_dist = torch.norm(pred_kp[:, idx1] - pred_kp[:, idx2], dim=1)
            target_dist = torch.norm(target_kp[:, idx1] - target_kp[:, idx2], dim=1)
            
            # 距离一致性损失
            dist_loss = F.mse_loss(pred_dist, target_dist) * weight
            distance_losses.append(dist_loss)
        
        return torch.mean(torch.stack(distance_losses))
    
    def compute_female_angle_constraints(self, pred_kp, target_kp):
        """女性特定角度约束 (更宽松)"""
        angle_losses = []
        
        # 女性特定的角度约束 (较少但重要的)
        female_angle_constraints = [
            ([0, 1, 2], 0.8),   # F1区域角度 (权重较低)
            ([4, 5, 6], 0.8),   # F2区域角度
            ([8, 9, 10], 1.0),  # F3区域角度 (更重要)
            ([0, 8, 4], 1.2),   # 跨区域角度 (重要)
        ]
        
        for constraint in female_angle_constraints:
            indices, weight = constraint
            idx1, idx2, idx3 = indices
            
            # 计算角度
            pred_angle = self.compute_angle(pred_kp[:, idx1], pred_kp[:, idx2], pred_kp[:, idx3])
            target_angle = self.compute_angle(target_kp[:, idx1], target_kp[:, idx2], target_kp[:, idx3])
            
            # 角度一致性损失
            angle_loss = F.mse_loss(pred_angle, target_angle) * weight
            angle_losses.append(angle_loss)
        
        if angle_losses:
            return torch.mean(torch.stack(angle_losses))
        else:
            return torch.tensor(0.0).to(pred_kp.device)
    
    def compute_angle(self, p1, p2, p3):
        """计算三点形成的角度"""
        v1 = p1 - p2
        v2 = p3 - p2
        cos_angle = F.cosine_similarity(v1, v2, dim=1)
        cos_angle = torch.clamp(cos_angle, -1.0, 1.0)
        return cos_angle
    
    def compute_female_symmetry_constraints(self, pred_kp, target_kp):
        """女性特定对称性约束 (更强)"""
        symmetry_losses = []
        
        # 女性骨盆的强对称性
        for i in range(4):
            f1_idx = i
            f2_idx = i + 4
            
            # 使用F3区域中心作为对称轴
            center = (pred_kp[:, 8] + pred_kp[:, 9]) / 2  # F3-1和F3-2的中点
            
            # 预测的对称性
            pred_f1_to_center = pred_kp[:, f1_idx] - center
            pred_f2_to_center = pred_kp[:, f2_idx] - center
            
            # 真实的对称性
            target_f1_to_center = target_kp[:, f1_idx] - center
            target_f2_to_center = target_kp[:, f2_idx] - center
            
            # 强对称性约束 (女性骨盆更对称)
            pred_symmetry = torch.stack([
                pred_f1_to_center[:, 0] + pred_f2_to_center[:, 0],  # X坐标和
                pred_f1_to_center[:, 1] - pred_f2_to_center[:, 1],  # Y坐标差
                pred_f1_to_center[:, 2] + pred_f2_to_center[:, 2],  # Z坐标和
            ], dim=1)
            
            target_symmetry = torch.stack([
                target_f1_to_center[:, 0] + target_f2_to_center[:, 0],
                target_f1_to_center[:, 1] - target_f2_to_center[:, 1],
                target_f1_to_center[:, 2] + target_f2_to_center[:, 2],
            ], dim=1)
            
            # 女性特定权重 (前两个关键点更重要)
            weight = 2.0 if i < 2 else 1.5
            symmetry_loss = F.mse_loss(pred_symmetry, target_symmetry) * weight
            symmetry_losses.append(symmetry_loss)
        
        if symmetry_losses:
            return torch.mean(torch.stack(symmetry_losses))
        else:
            return torch.tensor(0.0).to(pred_kp.device)

class FemaleOptimizedNet(nn.Module):
    """女性优化网络"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super(FemaleOptimizedNet, self).__init__()
        
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 更深的特征提取 (适应小数据集)
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(3, 32, 1),
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.Conv1d(32, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Conv1d(256, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
        )
        
        # 全局特征
        self.global_pool = nn.AdaptiveMaxPool1d(1)
        
        # 女性特定的预测头 (更保守的架构)
        self.predictor = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.4),  # 更高的dropout防止过拟合
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, num_keypoints * 3)
        )
        
    def forward(self, x):
        batch_size = x.size(0)
        
        # 特征提取
        x = x.transpose(2, 1)
        features = self.feature_extractor(x)
        
        # 全局特征
        global_feat = self.global_pool(features).squeeze(-1)
        
        # 关键点预测
        keypoints = self.predictor(global_feat)
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

def train_female_optimized_model(train_pc, train_kp, val_pc, val_kp):
    """训练女性优化模型"""
    
    print(f"\n👩 训练女性专门优化模型")
    print("=" * 60)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    
    # 模型初始化
    model = FemaleOptimizedNet(num_points=50000, num_keypoints=12)
    model = model.to(device)
    
    print(f"🏗️ 女性专门优化模型:")
    print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"   训练样本: {len(train_pc)}")
    print(f"   验证样本: {len(val_pc)}")
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=0.00005, weight_decay=1e-3)  # 更小学习率
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=150, eta_min=1e-7)
    criterion = FemaleSpecificConstraintLoss(alpha=1.0, beta=0.4, gamma=0.2, delta=0.3)
    
    # 训练参数
    num_epochs = 150  # 更多轮数
    batch_size = 2    # 更小批次
    best_val_error = float('inf')
    patience = 50     # 更大耐心
    patience_counter = 0
    
    print(f"🎯 女性专门训练参数:")
    print(f"   训练轮数: {num_epochs}")
    print(f"   批次大小: {batch_size}")
    print(f"   学习率: 0.00005 (更保守)")
    print(f"   损失函数: 女性特定解剖学约束")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_losses = []
        loss_components = {'mse': [], 'distance': [], 'angle': [], 'symmetry': []}
        
        n_train_batches = len(train_pc) // batch_size
        
        for i in range(n_train_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(train_pc))
            
            batch_pc = torch.FloatTensor(train_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(train_kp[start_idx:end_idx]).to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            pred_kp = model(batch_pc)
            
            # 计算损失
            total_loss, mse_loss, distance_loss, angle_loss, symmetry_loss = criterion(pred_kp, batch_kp)
            
            # 反向传播
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)  # 更小梯度裁剪
            optimizer.step()
            
            train_losses.append(total_loss.item())
            loss_components['mse'].append(mse_loss.item())
            loss_components['distance'].append(distance_loss.item())
            loss_components['angle'].append(angle_loss.item())
            loss_components['symmetry'].append(symmetry_loss.item())
        
        avg_train_loss = np.mean(train_losses)
        
        # 验证阶段
        model.eval()
        val_errors = []
        
        with torch.no_grad():
            n_val_batches = len(val_pc) // batch_size + (1 if len(val_pc) % batch_size > 0 else 0)
            
            for i in range(n_val_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(val_pc))
                
                batch_pc = torch.FloatTensor(val_pc[start_idx:end_idx]).to(device)
                batch_kp = torch.FloatTensor(val_kp[start_idx:end_idx]).to(device)
                
                pred_kp = model(batch_pc)
                
                for j in range(len(batch_kp)):
                    error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j], dim=1))
                    val_errors.append(error.item())
        
        avg_val_error = np.mean(val_errors)
        
        # 学习率调度
        scheduler.step()
        
        if epoch % 15 == 0:
            print(f"Epoch {epoch+1}/{num_epochs}:")
            print(f"  总损失: {avg_train_loss:.4f}, 验证误差: {avg_val_error:.2f}mm")
            print(f"  MSE: {np.mean(loss_components['mse']):.4f}, 距离: {np.mean(loss_components['distance']):.4f}")
            print(f"  角度: {np.mean(loss_components['angle']):.4f}, 对称: {np.mean(loss_components['symmetry']):.4f}")
        
        # 早停和模型保存
        if avg_val_error < best_val_error:
            best_val_error = avg_val_error
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'val_error': avg_val_error,
            }, f'female_optimized.pth')
        else:
            patience_counter += 1
            if patience_counter >= patience:
                break
    
    print(f"✅ 女性专门优化模型训练完成，最佳验证误差: {best_val_error:.2f}mm")
    
    return model, best_val_error

def test_female_optimized_model(model, test_pc, test_kp):
    """测试女性优化模型"""
    
    print(f"\n🧪 测试女性专门优化模型")
    print("=" * 60)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    model.eval()
    
    test_errors = []
    
    batch_size = 2
    with torch.no_grad():
        n_test_batches = len(test_pc) // batch_size + (1 if len(test_pc) % batch_size > 0 else 0)
        
        for i in range(n_test_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(test_pc))
            
            batch_pc = torch.FloatTensor(test_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(test_kp[start_idx:end_idx]).to(device)
            
            pred_kp = model(batch_pc)
            
            for j in range(len(batch_kp)):
                error = torch.mean(torch.norm(pred_kp[j] - batch_kp[j], dim=1))
                test_errors.append(error.item())
    
    avg_error = np.mean(test_errors)
    
    print(f"📊 女性专门优化模型测试结果:")
    print(f"   测试样本数: {len(test_pc)}")
    print(f"   测试误差: {avg_error:.2f}mm")
    print(f"   医疗级状态: {'✅ 达标' if avg_error < 5.0 else '❌ 未达标'}")
    
    return avg_error

def main():
    """主函数：女性专门优化"""
    
    print("👩 女性专门优化：针对女性骨盆特点的定制化优化")
    print("🎯 策略: 女性特定解剖学约束 + 保守训练 + 防过拟合")
    print("=" * 80)
    
    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 加载女性数据
    from keypoint_mutual_assistance import load_clean_data
    from practical_model_optimization import analyze_data_quality, remove_outliers, split_clean_data, smart_data_augmentation
    
    data_result = load_clean_data()
    if data_result is None:
        return
    
    female_pc, female_kp, male_pc, male_kp = data_result
    
    # 女性数据处理
    print(f"\n👩 女性数据专门处理")
    print("="*60)
    
    female_quality = analyze_data_quality(female_pc, female_kp, "女性")
    female_clean_pc, female_clean_kp, _ = remove_outliers(
        female_pc, female_kp, female_quality, removal_ratio=0.005)  # 更保守的清理
    
    (female_train_pc, female_train_kp), (female_val_pc, female_val_kp), (female_test_pc, female_test_kp) = \
        split_clean_data(female_clean_pc, female_clean_kp, "女性")
    
    # 更保守的数据增强
    female_aug_train_pc, female_aug_train_kp = smart_data_augmentation(
        female_train_pc, female_train_kp, target_size=120)  # 更少的增强
    
    # 训练女性专门优化模型
    female_model, female_val_error = train_female_optimized_model(
        female_aug_train_pc, female_aug_train_kp,
        female_val_pc, female_val_kp)
    
    # 测试女性专门优化模型
    female_test_error = test_female_optimized_model(
        female_model, female_test_pc, female_test_kp)
    
    # 结果总结
    print(f"\n" + "="*80)
    print("🎉 女性专门优化结果总结")
    print("="*80)
    
    print(f"📊 女性专门优化模型:")
    print(f"   验证误差: {female_val_error:.2f}mm")
    print(f"   测试误差: {female_test_error:.2f}mm")
    print(f"   测试样本: {len(female_test_pc)}个")
    print(f"   医疗级状态: {'✅ 达标' if female_test_error < 5.0 else '❌ 未达标'}")
    
    if female_test_error < 5.0:
        print(f"\n🎉 女性专门优化成功!")
        print(f"   ✅ 女性模型达到医疗级精度: {female_test_error:.2f}mm < 5mm")
        print(f"   ✅ 针对女性解剖学特点的优化策略有效")
        print(f"   ✅ 小数据集问题得到解决")
    else:
        print(f"\n📈 女性专门优化显著改进")
        print(f"   当前误差: {female_test_error:.2f}mm")
        print(f"   距离目标: {female_test_error - 5.0:.2f}mm")
        print(f"   仍需进一步优化")
    
    return female_test_error

if __name__ == "__main__":
    main()
