#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的性能汇总图表 - 只显示误差对比
Simplified Performance Summary - Error Comparison Only
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd

# 设置专业样式
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.style.use('default')

def load_all_model_results():
    """加载所有模型的结果"""
    results = {
        3: {'arch': 'enhanced', 'error': 8.09, 'std': 1.99, 'medical': 78.3, 'excellent': 8.3, 'params': 2.41},
        6: {'arch': 'enhanced', 'error': 7.31, 'std': 2.01, 'medical': 93.3, 'excellent': 12.5, 'params': 2.42},
        9: {'arch': 'enhanced', 'error': 5.18, 'std': 1.32, 'medical': 100.0, 'excellent': 46.7, 'params': 2.42},
        12: {'arch': 'enhanced', 'error': 5.27, 'std': 1.29, 'medical': 100.0, 'excellent': 44.2, 'params': 2.43},
        15: {'arch': 'balanced', 'error': 5.25, 'std': 1.58, 'medical': 99.7, 'excellent': 44.0, 'params': 0.86},
        19: {'arch': 'balanced', 'error': 6.18, 'std': 1.94, 'medical': 97.1, 'excellent': 27.6, 'params': 0.87},
        24: {'arch': 'balanced', 'error': 6.75, 'std': 2.00, 'medical': 95.8, 'excellent': 17.1, 'params': 0.89},
        28: {'arch': 'auto', 'error': 7.15, 'std': 2.35, 'medical': 88.8, 'excellent': 17.3, 'params': 2.48},
        33: {'arch': 'lightweight', 'error': 7.82, 'std': 2.96, 'medical': 76.2, 'excellent': 17.3, 'params': 0.42},
        38: {'arch': 'balanced', 'error': 6.89, 'std': 2.07, 'medical': 94.2, 'excellent': 15.8, 'params': 0.94},
        43: {'arch': 'balanced', 'error': 6.95, 'std': 2.09, 'medical': 93.8, 'excellent': 15.5, 'params': 0.95},
        47: {'arch': 'enhanced', 'error': 6.30, 'std': 1.58, 'medical': 98.9, 'excellent': 25.5, 'params': 2.53},
        52: {'arch': 'balanced', 'error': 6.61, 'std': 1.98, 'medical': 96.2, 'excellent': 19.2, 'params': 0.97},
        57: {'arch': 'balanced', 'error': 6.83, 'std': 2.05, 'medical': 94.7, 'excellent': 16.7, 'params': 0.97}
    }
    return results

def create_simplified_performance_summary():
    """创建简化的性能汇总图表 - 只显示误差对比"""
    print("📊 创建简化的性能汇总图表...")
    
    results = load_all_model_results()
    configs = sorted(results.keys())
    
    # 提取数据
    errors = [results[k]['error'] for k in configs]
    stds = [results[k]['std'] for k in configs]
    architectures = [results[k]['arch'] for k in configs]
    
    # 创建单个图表
    fig, ax = plt.subplots(1, 1, figsize=(16, 8))
    
    # 配色方案
    colors = {'lightweight': '#E74C3C', 'balanced': '#3498DB', 'enhanced': '#2ECC71', 'auto': '#F39C12'}
    bar_colors = [colors[arch] for arch in architectures]
    
    # 创建误差条形图
    bars = ax.bar(range(len(configs)), errors, yerr=stds, capsize=5, 
                  color=bar_colors, alpha=0.8, edgecolor='black', linewidth=1.2)
    
    # 添加优秀级标准线（5mm）
    ax.axhline(y=5, color='#27AE60', linestyle='--', linewidth=2.5, alpha=0.8, 
               label='Excellent Grade (≤5mm)', zorder=10)
    
    # 在条形图上添加数值标签
    for i, (bar, error, std) in enumerate(zip(bars, errors, stds)):
        ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.15, 
                f'{error:.2f}', ha='center', va='bottom', fontweight='bold', fontsize=11)
    
    # 设置坐标轴
    ax.set_xlabel('Model Configuration', fontsize=14, fontweight='bold')
    ax.set_ylabel('Average Error (mm)', fontsize=14, fontweight='bold')
    ax.set_title('Model Performance Comparison Across All Keypoint Configurations', 
                 fontsize=16, fontweight='bold', pad=20)
    
    # 设置x轴标签
    ax.set_xticks(range(len(configs)))
    ax.set_xticklabels([f'{k} kp\n{results[k]["arch"].capitalize()}' for k in configs], 
                       rotation=45, ha='right', fontsize=10)
    
    # 设置y轴范围
    max_error = max([e + s for e, s in zip(errors, stds)])
    ax.set_ylim(0, max_error + 0.8)
    
    # 添加图例
    ax.legend(fontsize=12, loc='upper right')
    
    # 添加网格
    ax.grid(True, alpha=0.3, axis='y', linestyle='-', linewidth=0.5)
    
    # 设置刻度
    ax.tick_params(axis='y', labelsize=11)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    filename = 'dataset_paper_simplified_performance_summary.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white', 
                edgecolor='none', pad_inches=0.2)
    plt.show()
    
    print(f"✅ 简化性能汇总图表已保存: {filename}")
    
    return filename

def create_clean_error_comparison():
    """创建更清洁的误差对比图"""
    print("\n📊 创建清洁的误差对比图...")
    
    results = load_all_model_results()
    configs = sorted(results.keys())
    
    # 提取数据
    errors = [results[k]['error'] for k in configs]
    stds = [results[k]['std'] for k in configs]
    architectures = [results[k]['arch'] for k in configs]
    
    # 创建图表
    fig, ax = plt.subplots(1, 1, figsize=(18, 6))
    
    # 配色方案
    colors = {'lightweight': '#E74C3C', 'balanced': '#3498DB', 'enhanced': '#2ECC71', 'auto': '#F39C12'}
    bar_colors = [colors[arch] for arch in architectures]
    
    # 创建条形图
    bars = ax.bar(range(len(configs)), errors, yerr=stds, capsize=4, 
                  color=bar_colors, alpha=0.85, edgecolor='white', linewidth=1.5)
    
    # 添加5mm基准线
    ax.axhline(y=5, color='#2C3E50', linestyle='--', linewidth=2, alpha=0.7, 
               label='5mm Threshold')
    
    # 在条形图上添加数值标签
    for i, (bar, error) in enumerate(zip(bars, errors)):
        ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.2, 
                f'{error:.1f}', ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    # 设置标题和标签
    ax.set_xlabel('Number of Keypoints', fontsize=13, fontweight='bold')
    ax.set_ylabel('Average Error (mm)', fontsize=13, fontweight='bold')
    ax.set_title('Medical Pelvis Keypoint Detection: Performance Across All Model Configurations', 
                 fontsize=15, fontweight='bold', pad=15)
    
    # 设置x轴标签 - 更简洁
    ax.set_xticks(range(len(configs)))
    ax.set_xticklabels([str(k) for k in configs], fontsize=10)
    
    # 添加架构类型的颜色说明
    arch_legend = []
    for arch, color in colors.items():
        arch_legend.append(plt.Rectangle((0,0),1,1, facecolor=color, alpha=0.85, 
                                        label=arch.capitalize()))
    
    # 创建两个图例
    legend1 = ax.legend(handles=arch_legend, title='Architecture', 
                       loc='upper left', fontsize=10, title_fontsize=11)
    ax.add_artist(legend1)
    
    threshold_legend = ax.legend([plt.Line2D([0], [0], color='#2C3E50', linestyle='--', linewidth=2)], 
                                ['5mm Threshold'], loc='upper right', fontsize=10)
    
    # 设置y轴范围
    ax.set_ylim(0, max(errors) + 1)
    
    # 添加轻微的网格
    ax.grid(True, alpha=0.2, axis='y', linestyle='-', linewidth=0.5)
    
    # 设置背景色
    ax.set_facecolor('#FAFAFA')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    clean_filename = 'dataset_paper_clean_error_comparison.png'
    plt.savefig(clean_filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print(f"✅ 清洁误差对比图已保存: {clean_filename}")
    
    return clean_filename

def create_horizontal_error_chart():
    """创建横向误差图表"""
    print("\n📊 创建横向误差图表...")
    
    results = load_all_model_results()
    configs = sorted(results.keys())
    
    # 提取数据
    errors = [results[k]['error'] for k in configs]
    architectures = [results[k]['arch'] for k in configs]
    
    # 创建横向图表
    fig, ax = plt.subplots(1, 1, figsize=(12, 10))
    
    # 配色方案
    colors = {'lightweight': '#E74C3C', 'balanced': '#3498DB', 'enhanced': '#2ECC71', 'auto': '#F39C12'}
    bar_colors = [colors[arch] for arch in architectures]
    
    # 创建横向条形图
    y_pos = np.arange(len(configs))
    bars = ax.barh(y_pos, errors, color=bar_colors, alpha=0.8, 
                   edgecolor='white', linewidth=1.5, height=0.7)
    
    # 添加5mm基准线
    ax.axvline(x=5, color='#2C3E50', linestyle='--', linewidth=2, alpha=0.7, 
               label='5mm Threshold')
    
    # 在条形图上添加数值标签
    for i, (bar, error) in enumerate(zip(bars, errors)):
        ax.text(bar.get_width() + 0.1, bar.get_y() + bar.get_height()/2, 
                f'{error:.1f}mm', ha='left', va='center', fontweight='bold', fontsize=10)
    
    # 设置标题和标签
    ax.set_ylabel('Model Configuration', fontsize=13, fontweight='bold')
    ax.set_xlabel('Average Error (mm)', fontsize=13, fontweight='bold')
    ax.set_title('Medical Pelvis Keypoint Detection: Error Comparison', 
                 fontsize=15, fontweight='bold', pad=15)
    
    # 设置y轴标签
    ax.set_yticks(y_pos)
    ax.set_yticklabels([f'{k} kp ({results[k]["arch"].capitalize()})' for k in configs], 
                       fontsize=10)
    
    # 反转y轴（最好的在上面）
    ax.invert_yaxis()
    
    # 设置x轴范围
    ax.set_xlim(0, max(errors) + 1)
    
    # 添加网格
    ax.grid(True, alpha=0.3, axis='x', linestyle='-', linewidth=0.5)
    
    # 添加图例
    ax.legend(fontsize=11, loc='lower right')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    horizontal_filename = 'dataset_paper_horizontal_error_chart.png'
    plt.savefig(horizontal_filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print(f"✅ 横向误差图表已保存: {horizontal_filename}")
    
    return horizontal_filename

if __name__ == "__main__":
    print("📊 创建简化的性能汇总图表")
    print("去掉医疗级达标率，只保留误差对比")
    print("=" * 60)
    
    # 创建简化的性能汇总
    simplified_file = create_simplified_performance_summary()
    
    # 创建清洁的误差对比
    clean_file = create_clean_error_comparison()
    
    # 创建横向误差图表
    horizontal_file = create_horizontal_error_chart()
    
    print(f"\n✅ 完成！生成的简化图表文件:")
    print(f"   📊 简化性能汇总: {simplified_file}")
    print(f"   📊 清洁误差对比: {clean_file}")
    print(f"   📊 横向误差图表: {horizontal_file}")
    print(f"\n💡 特点:")
    print(f"   • 去掉了医疗级达标率图表")
    print(f"   • 只保留核心的误差对比")
    print(f"   • 提供多种布局选择")
    print(f"   • 清晰的架构颜色区分")
