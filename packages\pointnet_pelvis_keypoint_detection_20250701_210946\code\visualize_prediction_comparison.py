"""
可视化预测与真实关键点的对比
创建清晰直观的对比图表
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.patches as patches
from pathlib import Path
import seaborn as sns

from save_best_model import BestSimplePointNet
from improved_data_loader import ImprovedDataLoader

class PredictionComparisonVisualizer:
    """预测对比可视化器"""
    
    def __init__(self, data_root="output/training_fixed"):
        self.data_root = data_root
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 创建输出目录
        self.output_dir = Path("output/prediction_comparison")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🎨 预测对比可视化器初始化")
        print(f"Device: {self.device}")
        
        # 设置matplotlib中文字体
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial Unicode MS', 'SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        
    def load_model_and_data(self):
        """加载模型和数据"""
        print("加载模型和数据...")
        
        # 加载模型
        model = BestSimplePointNet(num_keypoints=57)
        model_path = "output/scale_corrected_training/best_baseline_model.pth"
        
        if Path(model_path).exists():
            checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"✅ 模型加载成功，5mm准确率: {checkpoint.get('accuracy_5mm', 'N/A')}%")
        
        model = model.to(self.device)
        model.eval()
        
        # 加载数据
        data_loader_manager = ImprovedDataLoader(
            data_root=self.data_root,
            batch_size=1,
            num_workers=0,
            num_points=512
        )
        
        _, val_loader = data_loader_manager.create_dataloaders(train_ratio=0.8)
        
        # 获取几个样本
        samples = []
        for i, (point_cloud, keypoints) in enumerate(val_loader):
            if i >= 6:  # 取6个样本
                break
            samples.append((point_cloud, keypoints))
        
        print(f"✅ 加载了 {len(samples)} 个样本")
        return model, samples
    
    def predict_samples(self, model, samples):
        """预测样本"""
        predictions = []
        ground_truths = []
        point_clouds = []
        errors = []
        
        with torch.no_grad():
            for point_cloud, keypoints in samples:
                point_cloud = point_cloud.to(self.device)
                keypoints = keypoints.to(self.device)
                
                pred_keypoints = model(point_cloud)
                error = torch.norm(pred_keypoints - keypoints, dim=2).cpu().numpy()
                
                predictions.append(pred_keypoints.cpu().numpy())
                ground_truths.append(keypoints.cpu().numpy())
                point_clouds.append(point_cloud.cpu().numpy())
                errors.append(error)
        
        return predictions, ground_truths, point_clouds, errors
    
    def create_detailed_comparison_plot(self, predictions, ground_truths, point_clouds, errors):
        """创建详细的对比图"""
        print("生成详细对比图...")
        
        # 选择最好和最差的样本进行对比
        sample_errors = [np.mean(error) for error in errors]
        best_idx = np.argmin(sample_errors)
        worst_idx = np.argmax(sample_errors)
        
        fig = plt.figure(figsize=(24, 16))
        
        # 样本信息
        samples_info = [
            (best_idx, f"Best Sample (Error: {sample_errors[best_idx]:.2f}mm)", 'green'),
            (worst_idx, f"Worst Sample (Error: {sample_errors[worst_idx]:.2f}mm)", 'red')
        ]
        
        for row, (idx, title, color) in enumerate(samples_info):
            pc = point_clouds[idx][0]  # [N, 3]
            gt = ground_truths[idx][0]  # [57, 3]
            pred = predictions[idx][0]  # [57, 3]
            error = errors[idx][0]  # [57]
            
            # 1. 3D整体视图
            ax1 = fig.add_subplot(2, 4, row*4 + 1, projection='3d')
            
            # 绘制点云
            ax1.scatter(pc[:, 0], pc[:, 1], pc[:, 2], 
                       c='lightgray', alpha=0.1, s=0.5, label='Point Cloud')
            
            # 绘制真实关键点
            ax1.scatter(gt[:, 0], gt[:, 1], gt[:, 2], 
                       c='red', s=50, alpha=0.8, label='Ground Truth', marker='o')
            
            # 绘制预测关键点
            ax1.scatter(pred[:, 0], pred[:, 1], pred[:, 2], 
                       c='blue', s=50, alpha=0.8, label='Prediction', marker='^')
            
            # 连接对应点
            for i in range(len(gt)):
                ax1.plot([gt[i, 0], pred[i, 0]], 
                        [gt[i, 1], pred[i, 1]], 
                        [gt[i, 2], pred[i, 2]], 
                        'gray', alpha=0.3, linewidth=0.5)
            
            ax1.set_title(f'{title}\n3D Overview', fontsize=12, fontweight='bold', color=color)
            ax1.set_xlabel('X (mm)')
            ax1.set_ylabel('Y (mm)')
            ax1.set_zlabel('Z (mm)')
            ax1.legend()
            
            # 2. XY平面投影
            ax2 = fig.add_subplot(2, 4, row*4 + 2)
            
            # 绘制点云投影
            ax2.scatter(pc[:, 0], pc[:, 1], c='lightgray', alpha=0.2, s=1)
            
            # 绘制关键点投影
            scatter_gt = ax2.scatter(gt[:, 0], gt[:, 1], c='red', s=40, alpha=0.8, 
                                   label='Ground Truth', marker='o', edgecolors='darkred')
            scatter_pred = ax2.scatter(pred[:, 0], pred[:, 1], c='blue', s=40, alpha=0.8, 
                                     label='Prediction', marker='^', edgecolors='darkblue')
            
            # 连接对应点
            for i in range(len(gt)):
                ax2.plot([gt[i, 0], pred[i, 0]], [gt[i, 1], pred[i, 1]], 
                        'gray', alpha=0.4, linewidth=0.5)
            
            ax2.set_title('XY Plane Projection', fontsize=12, fontweight='bold')
            ax2.set_xlabel('X (mm)')
            ax2.set_ylabel('Y (mm)')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            ax2.set_aspect('equal')
            
            # 3. 误差热图
            ax3 = fig.add_subplot(2, 4, row*4 + 3)
            
            # 创建误差颜色映射
            colors = plt.cm.RdYlGn_r(error / 10.0)  # 假设最大误差10mm
            
            scatter = ax3.scatter(gt[:, 0], gt[:, 1], c=error, s=60, 
                                cmap='RdYlGn_r', vmin=0, vmax=8, alpha=0.8)
            
            # 添加颜色条
            cbar = plt.colorbar(scatter, ax=ax3)
            cbar.set_label('Error (mm)', rotation=270, labelpad=15)
            
            ax3.set_title('Error Heatmap (XY)', fontsize=12, fontweight='bold')
            ax3.set_xlabel('X (mm)')
            ax3.set_ylabel('Y (mm)')
            ax3.grid(True, alpha=0.3)
            ax3.set_aspect('equal')
            
            # 4. 误差分布
            ax4 = fig.add_subplot(2, 4, row*4 + 4)
            
            # 误差直方图
            ax4.hist(error, bins=20, alpha=0.7, color=color, edgecolor='black')
            ax4.axvline(np.mean(error), color='red', linestyle='--', 
                       label=f'Mean: {np.mean(error):.2f}mm')
            ax4.axvline(5.0, color='orange', linestyle='--', 
                       label='5mm Threshold')
            
            ax4.set_title('Error Distribution', fontsize=12, fontweight='bold')
            ax4.set_xlabel('Error (mm)')
            ax4.set_ylabel('Frequency')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'detailed_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 详细对比图生成完成")
    
    def create_error_analysis_plot(self, predictions, ground_truths, errors):
        """创建误差分析图"""
        print("生成误差分析图...")
        
        # 计算所有样本的统计信息
        all_errors = np.concatenate(errors, axis=0).flatten()
        all_predictions = np.concatenate(predictions, axis=0)
        all_ground_truths = np.concatenate(ground_truths, axis=0)
        
        # 计算每个关键点的平均误差
        keypoint_errors = []
        for i in range(57):
            kp_errors = [error[i] for error in errors]
            keypoint_errors.append(np.mean(kp_errors))
        keypoint_errors = np.array(keypoint_errors)
        
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        
        # 1. 整体误差分布
        axes[0, 0].hist(all_errors, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].axvline(np.mean(all_errors), color='red', linestyle='--', 
                          label=f'Mean: {np.mean(all_errors):.2f}mm')
        axes[0, 0].axvline(np.median(all_errors), color='green', linestyle='--', 
                          label=f'Median: {np.median(all_errors):.2f}mm')
        axes[0, 0].axvline(5.0, color='orange', linestyle='--', alpha=0.8, 
                          label='5mm Threshold')
        axes[0, 0].set_title('Overall Error Distribution', fontsize=14, fontweight='bold')
        axes[0, 0].set_xlabel('Error (mm)')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 每个关键点的误差
        colors = ['red' if e > 3.0 else 'orange' if e > 2.0 else 'green' for e in keypoint_errors]
        bars = axes[0, 1].bar(range(57), keypoint_errors, color=colors, alpha=0.7)
        axes[0, 1].axhline(np.mean(keypoint_errors), color='red', linestyle='--', 
                          label=f'Mean: {np.mean(keypoint_errors):.2f}mm')
        axes[0, 1].set_title('Error by Keypoint', fontsize=14, fontweight='bold')
        axes[0, 1].set_xlabel('Keypoint Index')
        axes[0, 1].set_ylabel('Mean Error (mm)')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 预测vs真实散点图 (X坐标)
        pred_x = all_predictions[:, :, 0].flatten()
        gt_x = all_ground_truths[:, :, 0].flatten()
        axes[0, 2].scatter(gt_x, pred_x, alpha=0.5, s=10, c='blue')
        
        # 添加完美预测线
        min_val, max_val = min(gt_x.min(), pred_x.min()), max(gt_x.max(), pred_x.max())
        axes[0, 2].plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, linewidth=2)
        
        axes[0, 2].set_title('Predicted vs Ground Truth (X)', fontsize=14, fontweight='bold')
        axes[0, 2].set_xlabel('Ground Truth X (mm)')
        axes[0, 2].set_ylabel('Predicted X (mm)')
        axes[0, 2].grid(True, alpha=0.3)
        
        # 计算相关系数
        corr_x = np.corrcoef(gt_x, pred_x)[0, 1]
        axes[0, 2].text(0.05, 0.95, f'Correlation: {corr_x:.3f}', 
                       transform=axes[0, 2].transAxes, fontsize=12, 
                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # 4. 准确率统计
        thresholds = [1, 2, 3, 5, 10]
        accuracies = [(all_errors <= t).mean() * 100 for t in thresholds]
        colors_acc = ['red', 'orange', 'yellow', 'lightgreen', 'green']
        
        bars = axes[1, 0].bar([f'{t}mm' for t in thresholds], accuracies, 
                             color=colors_acc, alpha=0.7, edgecolor='black')
        axes[1, 0].set_title('Accuracy at Different Thresholds', fontsize=14, fontweight='bold')
        axes[1, 0].set_ylabel('Accuracy (%)')
        axes[1, 0].set_ylim(0, 105)
        
        # 添加数值标签
        for bar, acc in zip(bars, accuracies):
            axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                           f'{acc:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # 5. 最佳和最差关键点对比
        best_kps = np.argsort(keypoint_errors)[:10]
        worst_kps = np.argsort(keypoint_errors)[-10:]
        
        x_pos = np.arange(10)
        width = 0.35
        
        axes[1, 1].bar(x_pos - width/2, keypoint_errors[best_kps], width, 
                      label='Best 10', color='green', alpha=0.7)
        axes[1, 1].bar(x_pos + width/2, keypoint_errors[worst_kps], width, 
                      label='Worst 10', color='red', alpha=0.7)
        
        axes[1, 1].set_title('Best vs Worst Keypoints', fontsize=14, fontweight='bold')
        axes[1, 1].set_xlabel('Rank')
        axes[1, 1].set_ylabel('Mean Error (mm)')
        axes[1, 1].set_xticks(x_pos)
        axes[1, 1].set_xticklabels([f'{i+1}' for i in range(10)])
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        # 6. 误差vs距离关系
        # 计算每个关键点到中心的距离
        center = np.mean(all_ground_truths, axis=1, keepdims=True)
        distances = np.linalg.norm(all_ground_truths - center, axis=2).flatten()
        
        axes[1, 2].scatter(distances, all_errors, alpha=0.5, s=10, c='purple')
        
        # 添加趋势线
        z = np.polyfit(distances, all_errors, 1)
        p = np.poly1d(z)
        x_trend = np.linspace(distances.min(), distances.max(), 100)
        axes[1, 2].plot(x_trend, p(x_trend), "r--", alpha=0.8, linewidth=2)
        
        axes[1, 2].set_title('Error vs Distance from Center', fontsize=14, fontweight='bold')
        axes[1, 2].set_xlabel('Distance from Center (mm)')
        axes[1, 2].set_ylabel('Error (mm)')
        axes[1, 2].grid(True, alpha=0.3)
        
        # 计算相关系数
        corr_dist = np.corrcoef(distances, all_errors)[0, 1]
        axes[1, 2].text(0.05, 0.95, f'Correlation: {corr_dist:.3f}', 
                       transform=axes[1, 2].transAxes, fontsize=12, 
                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'error_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 误差分析图生成完成")
    
    def create_interactive_comparison(self, predictions, ground_truths, point_clouds, errors):
        """创建交互式对比图"""
        print("生成交互式对比图...")
        
        # 选择一个中等性能的样本进行详细展示
        sample_errors = [np.mean(error) for error in errors]
        median_idx = np.argsort(sample_errors)[len(sample_errors)//2]
        
        pc = point_clouds[median_idx][0]
        gt = ground_truths[median_idx][0]
        pred = predictions[median_idx][0]
        error = errors[median_idx][0]
        
        fig = plt.figure(figsize=(20, 15))
        
        # 1. 主要3D视图
        ax1 = fig.add_subplot(2, 3, (1, 4), projection='3d')
        
        # 绘制点云
        ax1.scatter(pc[:, 0], pc[:, 1], pc[:, 2], 
                   c='lightgray', alpha=0.1, s=1, label='Point Cloud')
        
        # 绘制关键点，根据误差着色
        scatter_gt = ax1.scatter(gt[:, 0], gt[:, 1], gt[:, 2], 
                               c='red', s=80, alpha=0.8, label='Ground Truth', 
                               marker='o', edgecolors='darkred', linewidth=2)
        
        scatter_pred = ax1.scatter(pred[:, 0], pred[:, 1], pred[:, 2], 
                                 c=error, s=80, alpha=0.8, cmap='RdYlGn_r', 
                                 vmin=0, vmax=6, marker='^', 
                                 edgecolors='black', linewidth=1, label='Prediction')
        
        # 连接对应点，线条粗细表示误差大小
        for i in range(len(gt)):
            linewidth = max(0.5, min(3.0, error[i]))
            alpha = min(1.0, error[i] / 3.0)
            ax1.plot([gt[i, 0], pred[i, 0]], 
                    [gt[i, 1], pred[i, 1]], 
                    [gt[i, 2], pred[i, 2]], 
                    color='red' if error[i] > 3 else 'orange' if error[i] > 2 else 'green',
                    alpha=alpha, linewidth=linewidth)
        
        # 添加颜色条
        cbar = plt.colorbar(scatter_pred, ax=ax1, shrink=0.8)
        cbar.set_label('Prediction Error (mm)', rotation=270, labelpad=20)
        
        ax1.set_title(f'3D Keypoint Comparison\nSample Error: {np.mean(error):.2f}mm', 
                     fontsize=16, fontweight='bold')
        ax1.set_xlabel('X (mm)')
        ax1.set_ylabel('Y (mm)')
        ax1.set_zlabel('Z (mm)')
        ax1.legend()
        
        # 2. XY投影 + 误差向量
        ax2 = fig.add_subplot(2, 3, 2)
        
        # 绘制误差向量
        for i in range(len(gt)):
            color = 'red' if error[i] > 3 else 'orange' if error[i] > 2 else 'green'
            ax2.arrow(gt[i, 0], gt[i, 1], 
                     pred[i, 0] - gt[i, 0], pred[i, 1] - gt[i, 1],
                     head_width=1, head_length=1, fc=color, ec=color, alpha=0.7)
        
        ax2.scatter(gt[:, 0], gt[:, 1], c='red', s=60, alpha=0.8, 
                   label='Ground Truth', marker='o', edgecolors='darkred')
        ax2.scatter(pred[:, 0], pred[:, 1], c='blue', s=60, alpha=0.8, 
                   label='Prediction', marker='^', edgecolors='darkblue')
        
        ax2.set_title('XY Projection with Error Vectors', fontsize=14, fontweight='bold')
        ax2.set_xlabel('X (mm)')
        ax2.set_ylabel('Y (mm)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_aspect('equal')
        
        # 3. XZ投影
        ax3 = fig.add_subplot(2, 3, 3)
        
        ax3.scatter(gt[:, 0], gt[:, 2], c='red', s=60, alpha=0.8, 
                   label='Ground Truth', marker='o', edgecolors='darkred')
        ax3.scatter(pred[:, 0], pred[:, 2], c='blue', s=60, alpha=0.8, 
                   label='Prediction', marker='^', edgecolors='darkblue')
        
        for i in range(len(gt)):
            ax3.plot([gt[i, 0], pred[i, 0]], [gt[i, 2], pred[i, 2]], 
                    'gray', alpha=0.5, linewidth=0.5)
        
        ax3.set_title('XZ Projection', fontsize=14, fontweight='bold')
        ax3.set_xlabel('X (mm)')
        ax3.set_ylabel('Z (mm)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.set_aspect('equal')
        
        # 4. YZ投影
        ax4 = fig.add_subplot(2, 3, 5)
        
        ax4.scatter(gt[:, 1], gt[:, 2], c='red', s=60, alpha=0.8, 
                   label='Ground Truth', marker='o', edgecolors='darkred')
        ax4.scatter(pred[:, 1], pred[:, 2], c='blue', s=60, alpha=0.8, 
                   label='Prediction', marker='^', edgecolors='darkblue')
        
        for i in range(len(gt)):
            ax4.plot([gt[i, 1], pred[i, 1]], [gt[i, 2], pred[i, 2]], 
                    'gray', alpha=0.5, linewidth=0.5)
        
        ax4.set_title('YZ Projection', fontsize=14, fontweight='bold')
        ax4.set_xlabel('Y (mm)')
        ax4.set_ylabel('Z (mm)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        ax4.set_aspect('equal')
        
        # 5. 误差统计
        ax5 = fig.add_subplot(2, 3, 6)
        
        # 创建误差统计表
        stats_text = f"""
Sample Statistics:
Mean Error: {np.mean(error):.2f} mm
Std Error: {np.std(error):.2f} mm
Min Error: {np.min(error):.2f} mm
Max Error: {np.max(error):.2f} mm

Accuracy:
< 1mm: {(error < 1).sum()}/57 ({(error < 1).mean()*100:.1f}%)
< 2mm: {(error < 2).sum()}/57 ({(error < 2).mean()*100:.1f}%)
< 3mm: {(error < 3).sum()}/57 ({(error < 3).mean()*100:.1f}%)
< 5mm: {(error < 5).sum()}/57 ({(error < 5).mean()*100:.1f}%)

Worst 3 Keypoints:
#{np.argsort(error)[-1]}: {error[np.argsort(error)[-1]]:.2f}mm
#{np.argsort(error)[-2]}: {error[np.argsort(error)[-2]]:.2f}mm
#{np.argsort(error)[-3]}: {error[np.argsort(error)[-3]]:.2f}mm

Best 3 Keypoints:
#{np.argsort(error)[0]}: {error[np.argsort(error)[0]]:.2f}mm
#{np.argsort(error)[1]}: {error[np.argsort(error)[1]]:.2f}mm
#{np.argsort(error)[2]}: {error[np.argsort(error)[2]]:.2f}mm
        """
        
        ax5.text(0.05, 0.95, stats_text, transform=ax5.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        ax5.set_xlim(0, 1)
        ax5.set_ylim(0, 1)
        ax5.axis('off')
        ax5.set_title('Detailed Statistics', fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'interactive_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 交互式对比图生成完成")
    
    def run_visualization(self):
        """运行完整的可视化"""
        print("🚀 开始预测对比可视化...")
        
        # 1. 加载模型和数据
        model, samples = self.load_model_and_data()
        
        # 2. 进行预测
        predictions, ground_truths, point_clouds, errors = self.predict_samples(model, samples)
        
        # 3. 生成各种可视化
        self.create_detailed_comparison_plot(predictions, ground_truths, point_clouds, errors)
        self.create_error_analysis_plot(predictions, ground_truths, errors)
        self.create_interactive_comparison(predictions, ground_truths, point_clouds, errors)
        
        # 4. 打印总结
        all_errors = np.concatenate(errors, axis=0).flatten()
        print(f"\n🎯 可视化完成!")
        print(f"📁 结果保存在: {self.output_dir}")
        print(f"📊 整体性能: 平均误差 {np.mean(all_errors):.2f}mm, 5mm准确率 {(all_errors <= 5.0).mean()*100:.1f}%")
        
        return predictions, ground_truths, errors

def main():
    """主函数"""
    visualizer = PredictionComparisonVisualizer(data_root="output/training_fixed")
    
    predictions, ground_truths, errors = visualizer.run_visualization()
    
    print("🎉 预测对比可视化完成!")
    print("生成的图表包括:")
    print("  - detailed_comparison.png: 最佳/最差样本详细对比")
    print("  - error_analysis.png: 全面的误差分析")
    print("  - interactive_comparison.png: 交互式详细对比")

if __name__ == "__main__":
    main()
