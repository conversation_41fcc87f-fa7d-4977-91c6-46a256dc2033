#!/usr/bin/env python3
"""
Analyze Component Coordinates

Check which component (F1/F2/F3) has annotations starting from origin,
and analyze the coordinate distribution of each component.
"""

import numpy as np
import pandas as pd
from pathlib import Path
import matplotlib.pyplot as plt

def load_annotation_file(csv_path: str):
    """Load annotation CSV file with proper encoding"""
    try:
        df = pd.read_csv(csv_path, encoding='gbk')
    except:
        try:
            df = pd.read_csv(csv_path, encoding='utf-8')
        except:
            df = pd.read_csv(csv_path, encoding='latin-1')
    
    keypoints = df[['X', 'Y', 'Z']].values
    labels = df['label'].values.tolist()
    
    return keypoints, labels

def analyze_component_coordinates(sample_id: str, coord_system: str = 'XYZ'):
    """Analyze coordinate distribution for each component"""
    
    print(f"\n🔍 **分析样本 {sample_id} ({coord_system}) 的部件坐标**")
    
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    annotations_dir = data_dir / "annotations"
    
    # Load annotation file
    csv_file = annotations_dir / f"{sample_id}-Table-{coord_system}.CSV"
    
    if not csv_file.exists():
        print(f"   ❌ 标注文件不存在")
        return None
    
    try:
        keypoints, labels = load_annotation_file(str(csv_file))
    except Exception as e:
        print(f"   ❌ 标注加载失败: {e}")
        return None
    
    # Separate by components
    f1_indices = [i for i, label in enumerate(labels) if label.startswith('F_1')]
    f2_indices = [i for i, label in enumerate(labels) if label.startswith('F_2')]
    f3_indices = [i for i, label in enumerate(labels) if label.startswith('F_3')]
    
    components = {
        'F1': {'indices': f1_indices, 'keypoints': keypoints[f1_indices] if f1_indices else np.array([])},
        'F2': {'indices': f2_indices, 'keypoints': keypoints[f2_indices] if f2_indices else np.array([])},
        'F3': {'indices': f3_indices, 'keypoints': keypoints[f3_indices] if f3_indices else np.array([])}
    }
    
    print(f"   📊 部件分布: F1={len(f1_indices)}, F2={len(f2_indices)}, F3={len(f3_indices)}")
    
    results = {}
    
    for comp_name, comp_data in components.items():
        if len(comp_data['keypoints']) == 0:
            continue
            
        kps = comp_data['keypoints']
        
        # Calculate statistics
        center = np.mean(kps, axis=0)
        min_coords = np.min(kps, axis=0)
        max_coords = np.max(kps, axis=0)
        range_coords = np.ptp(kps, axis=0)
        std_coords = np.std(kps, axis=0)
        
        # Distance from origin
        distances_from_origin = np.linalg.norm(kps, axis=1)
        min_dist_origin = np.min(distances_from_origin)
        mean_dist_origin = np.mean(distances_from_origin)
        
        # Check if close to origin
        close_to_origin = min_dist_origin < 10  # Within 10mm of origin
        
        print(f"\n   📋 **{comp_name} 部件分析**:")
        print(f"      关键点数量: {len(kps)}")
        print(f"      中心坐标: [{center[0]:.1f}, {center[1]:.1f}, {center[2]:.1f}]")
        print(f"      最小坐标: [{min_coords[0]:.1f}, {min_coords[1]:.1f}, {min_coords[2]:.1f}]")
        print(f"      最大坐标: [{max_coords[0]:.1f}, {max_coords[1]:.1f}, {max_coords[2]:.1f}]")
        print(f"      坐标范围: [{range_coords[0]:.1f}, {range_coords[1]:.1f}, {range_coords[2]:.1f}] mm")
        print(f"      标准差: [{std_coords[0]:.1f}, {std_coords[1]:.1f}, {std_coords[2]:.1f}]")
        print(f"      距原点最近: {min_dist_origin:.1f}mm")
        print(f"      距原点平均: {mean_dist_origin:.1f}mm")
        print(f"      接近原点: {'✅ 是' if close_to_origin else '❌ 否'}")
        
        results[comp_name] = {
            'keypoints_count': len(kps),
            'center': center.tolist(),
            'min_coords': min_coords.tolist(),
            'max_coords': max_coords.tolist(),
            'range': range_coords.tolist(),
            'std': std_coords.tolist(),
            'min_dist_origin': min_dist_origin,
            'mean_dist_origin': mean_dist_origin,
            'close_to_origin': close_to_origin,
            'keypoints': kps.tolist()
        }
    
    return results

def compare_multiple_samples():
    """Compare coordinate patterns across multiple samples"""
    
    print("🔍 **多样本部件坐标分析**")
    print("🎯 **目标: 找出接近原点的部件**")
    print("=" * 80)
    
    # Analyze several XYZ samples
    xyz_samples = ['600001', '600076', '600114', '600050', '600100']
    
    all_results = {}
    origin_components = []
    
    for sample_id in xyz_samples:
        result = analyze_component_coordinates(sample_id, 'XYZ')
        if result:
            all_results[sample_id] = result
            
            # Check which components are close to origin
            for comp_name, comp_data in result.items():
                if comp_data['close_to_origin']:
                    origin_components.append((sample_id, comp_name, comp_data['min_dist_origin']))
    
    # Summary analysis
    print(f"\n📋 **总结分析**")
    print("=" * 60)
    
    if origin_components:
        print(f"\n🎯 **接近原点的部件**:")
        for sample_id, comp_name, min_dist in origin_components:
            print(f"   样本 {sample_id} - {comp_name}: 距原点 {min_dist:.1f}mm")
        
        # Find the most common component near origin
        comp_counts = {}
        for _, comp_name, _ in origin_components:
            comp_counts[comp_name] = comp_counts.get(comp_name, 0) + 1
        
        if comp_counts:
            most_common = max(comp_counts.items(), key=lambda x: x[1])
            print(f"\n   🏆 最常接近原点的部件: {most_common[0]} ({most_common[1]}/{len(xyz_samples)} 样本)")
    else:
        print(f"\n⚠️ 没有发现接近原点的部件")
    
    # Analyze coordinate ranges for each component
    print(f"\n📊 **各部件坐标范围统计**:")
    
    for comp_name in ['F1', 'F2', 'F3']:
        comp_centers = []
        comp_ranges = []
        comp_min_dists = []
        
        for sample_id, sample_data in all_results.items():
            if comp_name in sample_data:
                comp_centers.append(sample_data[comp_name]['center'])
                comp_ranges.append(sample_data[comp_name]['range'])
                comp_min_dists.append(sample_data[comp_name]['min_dist_origin'])
        
        if comp_centers:
            avg_center = np.mean(comp_centers, axis=0)
            avg_range = np.mean(comp_ranges, axis=0)
            avg_min_dist = np.mean(comp_min_dists)
            
            print(f"\n   {comp_name} 部件 ({len(comp_centers)} 样本):")
            print(f"      平均中心: [{avg_center[0]:.1f}, {avg_center[1]:.1f}, {avg_center[2]:.1f}]")
            print(f"      平均范围: [{avg_range[0]:.1f}, {avg_range[1]:.1f}, {avg_range[2]:.1f}] mm")
            print(f"      平均距原点: {avg_min_dist:.1f}mm")
            
            # Determine if this component is typically near origin
            near_origin_count = sum(1 for d in comp_min_dists if d < 20)
            near_origin_percent = near_origin_count / len(comp_min_dists) * 100
            
            print(f"      接近原点频率: {near_origin_count}/{len(comp_min_dists)} ({near_origin_percent:.1f}%)")
            
            if near_origin_percent > 50:
                print(f"      🎯 {comp_name} 经常接近原点，可能是局部坐标系")
    
    return all_results, origin_components

def recommend_single_component_test(all_results, origin_components):
    """Recommend which component to test first"""
    
    print(f"\n💡 **单部件测试建议**")
    print("=" * 60)
    
    if not origin_components:
        print(f"   没有发现明显接近原点的部件")
        print(f"   建议: 选择坐标范围最小的部件进行测试")
        
        # Find component with smallest coordinate range
        comp_ranges = {}
        for sample_id, sample_data in all_results.items():
            for comp_name, comp_data in sample_data.items():
                if comp_name not in comp_ranges:
                    comp_ranges[comp_name] = []
                comp_ranges[comp_name].append(np.sum(comp_data['range']))
        
        if comp_ranges:
            avg_ranges = {comp: np.mean(ranges) for comp, ranges in comp_ranges.items()}
            smallest_comp = min(avg_ranges.items(), key=lambda x: x[1])
            print(f"   推荐测试: {smallest_comp[0]} (平均范围: {smallest_comp[1]:.1f}mm)")
        
        return None
    
    # Find the best component for testing
    comp_stats = {}
    for sample_id, comp_name, min_dist in origin_components:
        if comp_name not in comp_stats:
            comp_stats[comp_name] = []
        comp_stats[comp_name].append(min_dist)
    
    # Choose component that is most consistently near origin
    best_comp = None
    best_score = float('inf')
    
    for comp_name, distances in comp_stats.items():
        avg_dist = np.mean(distances)
        consistency = len(distances)  # How many samples
        score = avg_dist / consistency  # Lower is better
        
        print(f"   {comp_name}: 平均距原点 {avg_dist:.1f}mm, 出现在 {consistency} 个样本中")
        
        if score < best_score:
            best_score = score
            best_comp = comp_name
    
    if best_comp:
        print(f"\n🎯 **推荐测试部件: {best_comp}**")
        print(f"   理由: 最经常接近原点，可能使用局部坐标系")
        print(f"   建议: 先用 {best_comp} 部件验证STL-CSV对齐质量")
        
        # Find a good sample for testing
        best_sample = None
        best_dist = float('inf')
        
        for sample_id, comp_name, min_dist in origin_components:
            if comp_name == best_comp and min_dist < best_dist:
                best_dist = min_dist
                best_sample = sample_id
        
        if best_sample:
            print(f"   推荐测试样本: {best_sample} (距原点 {best_dist:.1f}mm)")
    
    return best_comp, best_sample if 'best_sample' in locals() else None

def main():
    """Main analysis function"""
    
    # Analyze multiple samples
    all_results, origin_components = compare_multiple_samples()
    
    # Get recommendation
    recommendation = recommend_single_component_test(all_results, origin_components)
    
    print(f"\n" + "="*80)
    print(f"📋 **分析结论**")
    print(f"=" * 80)
    
    if recommendation and recommendation[0]:
        comp_name, sample_id = recommendation
        print(f"\n🎯 **立即行动建议**:")
        print(f"   1. 专注测试 {comp_name} 部件")
        print(f"   2. 使用样本 {sample_id} 作为测试案例")
        print(f"   3. 验证 {comp_name} 的STL文件和CSV标注的对齐质量")
        print(f"   4. 如果对齐良好，可以先用单部件训练模型")
        print(f"   5. 验证单部件的标注质量和模型性能")
    else:
        print(f"\n⚠️ **需要进一步调查**:")
        print(f"   没有发现明显的局部坐标系部件")
        print(f"   建议手动检查几个样本的STL和CSV文件")
    
    # Save results for further analysis
    import json
    with open('component_coordinate_analysis.json', 'w') as f:
        json.dump({
            'all_results': all_results,
            'origin_components': origin_components,
            'recommendation': recommendation
        }, f, indent=2, default=str)
    
    print(f"\n📁 分析结果已保存: component_coordinate_analysis.json")

if __name__ == "__main__":
    main()
