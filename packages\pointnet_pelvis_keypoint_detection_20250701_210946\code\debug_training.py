"""
调试训练脚本
回到最基础的方法，专注解决根本问题
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from tqdm import tqdm
from pathlib import Path
import matplotlib.pyplot as plt

from simple_improved_model import SimpleImprovedPointNet
from improved_data_loader import ImprovedDataLoader

class DebugTrainer:
    """调试训练器 - 专注解决基础问题"""
    
    def __init__(self, data_root="output/training_fixed", batch_size=2):
        self.data_root = data_root
        self.batch_size = batch_size
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        print(f"调试训练器 - 专注解决基础问题")
        print(f"Device: {self.device}, Batch size: {batch_size}")
        
    def create_simple_model(self):
        """创建最简单的有效模型"""
        class VerySimplePointNet(nn.Module):
            def __init__(self, num_keypoints=57):
                super().__init__()
                self.num_keypoints = num_keypoints
                
                # 极简特征提取
                self.conv1 = nn.Conv1d(3, 64, 1)
                self.conv2 = nn.Conv1d(64, 128, 1)
                self.conv3 = nn.Conv1d(128, 256, 1)
                
                self.bn1 = nn.BatchNorm1d(64)
                self.bn2 = nn.BatchNorm1d(128)
                self.bn3 = nn.BatchNorm1d(256)
                
                # 简单回归头
                self.fc1 = nn.Linear(256, 128)
                self.fc2 = nn.Linear(128, num_keypoints * 3)
                
                self.dropout = nn.Dropout(0.2)
                
            def forward(self, x):
                # x: [B, N, 3] -> [B, 3, N]
                x = x.transpose(1, 2)
                
                # 特征提取
                x = torch.relu(self.bn1(self.conv1(x)))
                x = torch.relu(self.bn2(self.conv2(x)))
                x = torch.relu(self.bn3(self.conv3(x)))
                
                # 全局最大池化
                x = torch.max(x, dim=2)[0]  # [B, 256]
                
                # 回归
                x = torch.relu(self.fc1(x))
                x = self.dropout(x)
                x = self.fc2(x)  # [B, num_keypoints * 3]
                
                # 重塑
                x = x.view(-1, self.num_keypoints, 3)
                
                return x
        
        model = VerySimplePointNet().to(self.device)
        total_params = sum(p.numel() for p in model.parameters())
        print(f"极简模型参数: {total_params:,}")
        return model
    
    def debug_data(self):
        """调试数据加载"""
        print("调试数据加载...")
        
        data_loader_manager = ImprovedDataLoader(
            data_root=self.data_root,
            batch_size=self.batch_size,
            num_workers=0,  # 避免多进程问题
            num_points=512  # 减少点数
        )
        
        train_loader, val_loader = data_loader_manager.create_dataloaders(train_ratio=0.8)
        
        # 检查数据
        for i, (point_cloud, keypoints) in enumerate(train_loader):
            print(f"Batch {i+1}:")
            print(f"  点云形状: {point_cloud.shape}")
            print(f"  关键点形状: {keypoints.shape}")
            print(f"  点云范围: [{point_cloud.min():.2f}, {point_cloud.max():.2f}]")
            print(f"  关键点范围: [{keypoints.min():.2f}, {keypoints.max():.2f}]")
            
            # 检查是否有异常值
            if torch.isnan(point_cloud).any() or torch.isnan(keypoints).any():
                print("  ⚠️ 发现NaN值!")
            
            if torch.isinf(point_cloud).any() or torch.isinf(keypoints).any():
                print("  ⚠️ 发现无穷值!")
            
            # 检查关键点分布
            distances = torch.norm(keypoints[0], dim=1)
            print(f"  关键点到原点距离: 平均{distances.mean():.2f}, 最大{distances.max():.2f}")
            
            if i >= 2:  # 只检查前3个batch
                break
        
        return train_loader, val_loader
    
    def simple_train(self, num_epochs=10):
        """极简训练流程"""
        print("开始极简训练...")
        
        # 创建模型和数据
        model = self.create_simple_model()
        train_loader, val_loader = self.debug_data()
        
        # 最简单的优化器和损失
        optimizer = optim.Adam(model.parameters(), lr=0.001)
        criterion = nn.MSELoss()
        
        train_losses = []
        val_losses = []
        val_errors = []
        
        for epoch in range(1, num_epochs + 1):
            print(f"\nEpoch {epoch}/{num_epochs}")
            
            # 训练
            model.train()
            train_loss = 0.0
            for batch_idx, (point_cloud, keypoints) in enumerate(train_loader):
                point_cloud = point_cloud.to(self.device)
                keypoints = keypoints.to(self.device)
                
                optimizer.zero_grad()
                
                # 前向传播
                pred_keypoints = model(point_cloud)
                
                # 计算损失
                loss = criterion(pred_keypoints, keypoints)
                
                # 反向传播
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                
                if batch_idx % 10 == 0:
                    print(f"  Batch {batch_idx}: loss={loss.item():.3f}")
            
            avg_train_loss = train_loss / len(train_loader)
            train_losses.append(avg_train_loss)
            
            # 验证
            model.eval()
            val_loss = 0.0
            all_distances = []
            
            with torch.no_grad():
                for point_cloud, keypoints in val_loader:
                    point_cloud = point_cloud.to(self.device)
                    keypoints = keypoints.to(self.device)
                    
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    # 计算距离误差
                    distances = torch.norm(pred_keypoints - keypoints, dim=2)
                    all_distances.append(distances.cpu())
            
            avg_val_loss = val_loss / len(val_loader)
            val_losses.append(avg_val_loss)
            
            # 计算准确率
            all_distances = torch.cat(all_distances, dim=0).flatten()
            mean_error = torch.mean(all_distances).item()
            accuracy_5mm = (all_distances <= 5.0).float().mean().item() * 100
            accuracy_10mm = (all_distances <= 10.0).float().mean().item() * 100
            
            val_errors.append(mean_error)
            
            print(f"Train Loss: {avg_train_loss:.3f}, Val Loss: {avg_val_loss:.3f}")
            print(f"Mean Error: {mean_error:.2f}mm")
            print(f"5mm Accuracy: {accuracy_5mm:.1f}%, 10mm Accuracy: {accuracy_10mm:.1f}%")
            
            # 检查是否有改进
            if accuracy_5mm > 0:
                print(f"🎉 首次获得5mm准确率: {accuracy_5mm:.1f}%!")
                
                # 保存模型
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'train_loss': avg_train_loss,
                    'val_loss': avg_val_loss,
                    'mean_error': mean_error,
                    'accuracy_5mm': accuracy_5mm
                }, f"debug_model_epoch_{epoch}.pth")
        
        # 绘制训练曲线
        self.plot_debug_results(train_losses, val_losses, val_errors)
        
        return model, train_losses, val_losses, val_errors
    
    def plot_debug_results(self, train_losses, val_losses, val_errors):
        """绘制调试结果"""
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        epochs = range(1, len(train_losses) + 1)
        
        # 损失曲线
        axes[0].plot(epochs, train_losses, label='Train Loss', color='blue')
        axes[0].plot(epochs, val_losses, label='Val Loss', color='red')
        axes[0].set_xlabel('Epoch')
        axes[0].set_ylabel('Loss')
        axes[0].set_title('Training Loss')
        axes[0].legend()
        axes[0].grid(True)
        
        # 误差曲线
        axes[1].plot(epochs, val_errors, color='green')
        axes[1].set_xlabel('Epoch')
        axes[1].set_ylabel('Mean Error (mm)')
        axes[1].set_title('Validation Error')
        axes[1].grid(True)
        
        # 损失对比
        axes[2].plot(epochs, train_losses, label='Train', color='blue')
        axes[2].plot(epochs, val_losses, label='Val', color='red')
        axes[2].set_xlabel('Epoch')
        axes[2].set_ylabel('Loss')
        axes[2].set_title('Loss Comparison')
        axes[2].legend()
        axes[2].grid(True)
        axes[2].set_yscale('log')  # 对数尺度
        
        plt.tight_layout()
        plt.savefig('debug_training_results.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("调试结果图表保存为 debug_training_results.png")
    
    def analyze_predictions(self, model, val_loader):
        """分析预测结果"""
        print("分析预测结果...")
        
        model.eval()
        with torch.no_grad():
            for i, (point_cloud, keypoints) in enumerate(val_loader):
                if i >= 1:  # 只分析一个batch
                    break
                
                point_cloud = point_cloud.to(self.device)
                keypoints = keypoints.to(self.device)
                
                pred_keypoints = model(point_cloud)
                
                # 分析第一个样本
                gt = keypoints[0].cpu().numpy()  # [57, 3]
                pred = pred_keypoints[0].cpu().numpy()  # [57, 3]
                
                print(f"样本分析:")
                print(f"  GT关键点范围: [{gt.min():.2f}, {gt.max():.2f}]")
                print(f"  预测关键点范围: [{pred.min():.2f}, {pred.max():.2f}]")
                
                # 计算每个关键点的误差
                distances = np.linalg.norm(pred - gt, axis=1)
                print(f"  距离误差统计:")
                print(f"    平均: {distances.mean():.2f}mm")
                print(f"    中位数: {np.median(distances):.2f}mm")
                print(f"    最小: {distances.min():.2f}mm")
                print(f"    最大: {distances.max():.2f}mm")
                print(f"    标准差: {distances.std():.2f}mm")
                
                # 找出最好和最差的关键点
                best_idx = np.argmin(distances)
                worst_idx = np.argmax(distances)
                
                print(f"  最佳关键点 #{best_idx}: 误差 {distances[best_idx]:.2f}mm")
                print(f"    GT: [{gt[best_idx, 0]:.2f}, {gt[best_idx, 1]:.2f}, {gt[best_idx, 2]:.2f}]")
                print(f"    Pred: [{pred[best_idx, 0]:.2f}, {pred[best_idx, 1]:.2f}, {pred[best_idx, 2]:.2f}]")
                
                print(f"  最差关键点 #{worst_idx}: 误差 {distances[worst_idx]:.2f}mm")
                print(f"    GT: [{gt[worst_idx, 0]:.2f}, {gt[worst_idx, 1]:.2f}, {gt[worst_idx, 2]:.2f}]")
                print(f"    Pred: [{pred[worst_idx, 0]:.2f}, {pred[worst_idx, 1]:.2f}, {pred[worst_idx, 2]:.2f}]")

def main():
    """主函数"""
    trainer = DebugTrainer(
        data_root="output/training_fixed",
        batch_size=2
    )
    
    # 开始调试训练
    model, train_losses, val_losses, val_errors = trainer.simple_train(num_epochs=15)
    
    # 分析结果
    _, val_loader = trainer.debug_data()
    trainer.analyze_predictions(model, val_loader)
    
    print("调试训练完成!")

if __name__ == "__main__":
    main()
