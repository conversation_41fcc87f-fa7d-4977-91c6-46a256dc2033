#!/usr/bin/env python3
"""
分析19关键点性能下降的具体原因
找出哪些关键点导致了性能下降
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps
from basic_19keypoints_system import BasicHeatmapPointNet19, extract_keypoints_from_heatmaps_19

# F3关键点的解剖信息和重要性
F3_KEYPOINT_ANALYSIS = {
    0: {"name": "F3-1", "anatomy": "sacral_body", "difficulty": "medium", "visibility": "good"},
    1: {"name": "F3-2", "anatomy": "anterior_edge", "difficulty": "medium", "visibility": "good"},
    2: {"name": "F3-3", "anatomy": "sacral_body", "difficulty": "medium", "visibility": "good"},
    3: {"name": "F3-4", "anatomy": "sacral_body", "difficulty": "medium", "visibility": "good"},
    4: {"name": "F3-5", "anatomy": "sacral_body", "difficulty": "medium", "visibility": "good"},
    5: {"name": "F3-6", "anatomy": "sacral_body", "difficulty": "medium", "visibility": "good"},
    6: {"name": "F3-7", "anatomy": "sacral_body", "difficulty": "medium", "visibility": "good"},
    7: {"name": "F3-8", "anatomy": "sacral_center", "difficulty": "easy", "visibility": "excellent"},
    8: {"name": "F3-9", "anatomy": "sacral_body", "difficulty": "medium", "visibility": "good"},
    9: {"name": "F3-10", "anatomy": "sacral_body", "difficulty": "medium", "visibility": "good"},
    10: {"name": "F3-11", "anatomy": "sacral_body", "difficulty": "medium", "visibility": "good"},
    11: {"name": "F3-12", "anatomy": "posterior_edge", "difficulty": "medium", "visibility": "good"},
    12: {"name": "F3-13", "anatomy": "superior_peak", "difficulty": "easy", "visibility": "excellent"},  # Z最高点
    13: {"name": "F3-14", "anatomy": "left_edge", "difficulty": "hard", "visibility": "poor"},
    14: {"name": "F3-15", "anatomy": "sacral_body", "difficulty": "medium", "visibility": "good"},
    15: {"name": "F3-16", "anatomy": "sacral_body", "difficulty": "medium", "visibility": "good"},
    16: {"name": "F3-17", "anatomy": "sacral_body", "difficulty": "hard", "visibility": "poor"},
    17: {"name": "F3-18", "anatomy": "inferior_tip", "difficulty": "easy", "visibility": "excellent"},   # Z最低点
    18: {"name": "F3-19", "anatomy": "right_edge", "difficulty": "hard", "visibility": "poor"}
}

def load_models_and_data():
    """加载模型和数据"""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 加载12点模型
    model_12kp = HeatmapPointNet().to(device)
    try:
        model_12kp.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model_12kp.eval()
        print("✅ 12点模型加载成功")
    except Exception as e:
        print(f"❌ 12点模型加载失败: {e}")
        model_12kp = None
    
    # 加载19点模型
    model_19kp = BasicHeatmapPointNet19(input_dim=3, num_keypoints=19).to(device)
    try:
        model_19kp.load_state_dict(torch.load('best_fixed_19kp_model.pth', map_location=device))
        model_19kp.eval()
        print("✅ 19点模型加载成功")
    except Exception as e:
        print(f"❌ 19点模型加载失败: {e}")
        model_19kp = None
    
    # 加载数据
    data_12kp = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    data_19kp = np.load('f3_19kp_preprocessed.npz', allow_pickle=True)
    
    return model_12kp, model_19kp, data_12kp, data_19kp, device

def find_common_samples(data_12kp, data_19kp):
    """找到共同样本"""
    
    ids_12kp = data_12kp['sample_ids']
    ids_19kp = data_19kp['sample_ids']
    
    common_ids = []
    indices_12kp = []
    indices_19kp = []
    
    for i, id_19 in enumerate(ids_19kp):
        if id_19 in ids_12kp:
            j = list(ids_12kp).index(id_19)
            common_ids.append(id_19)
            indices_12kp.append(j)
            indices_19kp.append(i)
    
    print(f"找到 {len(common_ids)} 个共同样本")
    return common_ids, indices_12kp, indices_19kp

def test_models_on_common_samples(model_12kp, model_19kp, data_12kp, data_19kp, 
                                 common_ids, indices_12kp, indices_19kp, device):
    """在共同样本上测试两个模型"""
    
    results_12kp = []
    results_19kp = []
    
    print("🔍 在共同样本上测试模型性能...")
    
    for i, (sample_id, idx_12, idx_19) in enumerate(zip(common_ids, indices_12kp, indices_19kp)):
        print(f"   测试样本 {i+1}/{len(common_ids)}: {sample_id}")
        
        # 12点模型测试
        if model_12kp is not None:
            pc_12 = data_12kp['point_clouds'][idx_12]
            kp_12 = data_12kp['keypoints'][idx_12]
            
            # 采样点云
            if len(pc_12) > 8192:
                indices = np.random.choice(len(pc_12), 8192, replace=False)
                pc_sampled = pc_12[indices]
            else:
                pc_sampled = pc_12
            
            pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
            
            with torch.no_grad():
                pred_heatmaps = model_12kp(pc_tensor)
            
            pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze()
            pred_kp_12, conf_12 = extract_keypoints_from_heatmaps(pred_heatmaps_np, pc_sampled)
            
            errors_12 = [np.linalg.norm(pred_kp_12[j] - kp_12[j]) for j in range(len(kp_12))]
            
            results_12kp.append({
                'sample_id': sample_id,
                'errors': errors_12,
                'avg_error': np.mean(errors_12),
                'confidences': conf_12,
                'pred_keypoints': pred_kp_12,
                'true_keypoints': kp_12
            })
        
        # 19点模型测试
        if model_19kp is not None:
            pc_19 = data_19kp['point_clouds'][idx_19]
            kp_19 = data_19kp['keypoints'][idx_19]
            kp_12_from_19 = data_19kp['keypoints_12'][idx_19]  # 对应的12个关键点
            
            # 采样点云
            if len(pc_19) > 8192:
                indices = np.random.choice(len(pc_19), 8192, replace=False)
                pc_sampled = pc_19[indices]
            else:
                pc_sampled = pc_19
            
            pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
            
            with torch.no_grad():
                pred_heatmaps = model_19kp(pc_tensor)
            
            pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze()
            pred_kp_19, conf_19 = extract_keypoints_from_heatmaps_19(pred_heatmaps_np, pc_sampled)
            
            errors_19 = [np.linalg.norm(pred_kp_19[j] - kp_19[j]) for j in range(len(kp_19))]
            
            # 提取对应12个关键点的误差
            selected_indices = [0, 1, 2, 3, 6, 7, 11, 12, 13, 17, 18, 9]  # 与预处理时相同
            errors_12_from_19 = [errors_19[idx] for idx in selected_indices]
            
            results_19kp.append({
                'sample_id': sample_id,
                'errors_all_19': errors_19,
                'errors_selected_12': errors_12_from_19,
                'avg_error_19': np.mean(errors_19),
                'avg_error_12_from_19': np.mean(errors_12_from_19),
                'confidences': conf_19,
                'pred_keypoints': pred_kp_19,
                'true_keypoints': kp_19
            })
    
    return results_12kp, results_19kp

def analyze_performance_degradation(results_12kp, results_19kp):
    """分析性能下降的具体原因"""
    
    print("\n🔍 性能下降分析:")
    print("=" * 60)
    
    # 计算平均性能
    avg_12kp = np.mean([r['avg_error'] for r in results_12kp])
    avg_19kp_all = np.mean([r['avg_error_19'] for r in results_19kp])
    avg_19kp_selected = np.mean([r['avg_error_12_from_19'] for r in results_19kp])
    
    print(f"平均误差对比:")
    print(f"   12点模型 (12个关键点): {avg_12kp:.2f}mm")
    print(f"   19点模型 (对应12个关键点): {avg_19kp_selected:.2f}mm")
    print(f"   19点模型 (全部19个关键点): {avg_19kp_all:.2f}mm")
    
    degradation_selected = avg_19kp_selected - avg_12kp
    degradation_all = avg_19kp_all - avg_12kp
    
    print(f"\n性能下降:")
    print(f"   对应12点的下降: {degradation_selected:.2f}mm ({degradation_selected/avg_12kp*100:.1f}%)")
    print(f"   全部19点的下降: {degradation_all:.2f}mm ({degradation_all/avg_12kp*100:.1f}%)")
    
    # 分析每个关键点的性能
    print(f"\n📊 19个关键点的个体性能分析:")
    print("-" * 80)
    print(f"{'Index':<5} {'Name':<8} {'Anatomy':<15} {'Difficulty':<10} {'Avg Error':<10} {'Status'}")
    print("-" * 80)
    
    all_errors_19 = np.array([r['errors_all_19'] for r in results_19kp])  # [samples, 19]
    kp_avg_errors = np.mean(all_errors_19, axis=0)  # [19]
    
    problematic_keypoints = []
    excellent_keypoints = []
    
    for kp_idx in range(19):
        info = F3_KEYPOINT_ANALYSIS[kp_idx]
        avg_error = kp_avg_errors[kp_idx]
        
        # 判断性能等级
        if avg_error < 5:
            status = "🏆 优秀"
            excellent_keypoints.append(kp_idx)
        elif avg_error < 8:
            status = "✅ 良好"
        elif avg_error < 12:
            status = "⚠️ 中等"
        else:
            status = "🚨 问题"
            problematic_keypoints.append(kp_idx)
        
        print(f"{kp_idx:<5} {info['name']:<8} {info['anatomy']:<15} {info['difficulty']:<10} "
              f"{avg_error:<10.2f} {status}")
    
    # 分析问题关键点
    if problematic_keypoints:
        print(f"\n🚨 问题关键点详细分析:")
        for kp_idx in problematic_keypoints:
            info = F3_KEYPOINT_ANALYSIS[kp_idx]
            avg_error = kp_avg_errors[kp_idx]
            print(f"   {info['name']} ({info['anatomy']}):")
            print(f"     平均误差: {avg_error:.2f}mm")
            print(f"     难度等级: {info['difficulty']}")
            print(f"     可见性: {info['visibility']}")
            
            # 分析可能原因
            if info['difficulty'] == 'hard':
                print(f"     可能原因: 解剖结构复杂，难以精确定位")
            if info['visibility'] == 'poor':
                print(f"     可能原因: 在点云中特征不明显")
            if 'edge' in info['anatomy']:
                print(f"     可能原因: 边缘点容易受采样影响")
    
    # 分析优秀关键点
    if excellent_keypoints:
        print(f"\n🏆 优秀关键点分析:")
        for kp_idx in excellent_keypoints:
            info = F3_KEYPOINT_ANALYSIS[kp_idx]
            avg_error = kp_avg_errors[kp_idx]
            print(f"   {info['name']} ({info['anatomy']}): {avg_error:.2f}mm")
    
    return kp_avg_errors, problematic_keypoints, excellent_keypoints

def create_degradation_analysis_visualization(results_12kp, results_19kp, kp_avg_errors, 
                                            problematic_keypoints, excellent_keypoints):
    """创建性能下降分析可视化"""
    
    fig, axes = plt.subplots(3, 2, figsize=(16, 18))
    
    # 1. 整体性能对比
    ax1 = axes[0, 0]
    
    errors_12kp = [r['avg_error'] for r in results_12kp]
    errors_19kp_selected = [r['avg_error_12_from_19'] for r in results_19kp]
    errors_19kp_all = [r['avg_error_19'] for r in results_19kp]
    
    bp = ax1.boxplot([errors_12kp, errors_19kp_selected, errors_19kp_all], 
                     labels=['12-Point\nModel', '19-Point Model\n(Selected 12)', '19-Point Model\n(All 19)'],
                     patch_artist=True)
    
    colors = ['lightblue', 'lightgreen', 'lightcoral']
    for patch, color in zip(bp['boxes'], colors):
        patch.set_facecolor(color)
    
    ax1.set_ylabel('Average Error (mm)')
    ax1.set_title('Performance Comparison')
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    means = [np.mean(errors_12kp), np.mean(errors_19kp_selected), np.mean(errors_19kp_all)]
    for i, mean in enumerate(means):
        ax1.text(i+1, mean + 0.5, f'{mean:.2f}mm', ha='center', fontweight='bold')
    
    # 2. 19个关键点误差分布
    ax2 = axes[0, 1]
    
    # 按性能给关键点着色
    colors = []
    for kp_idx in range(19):
        if kp_idx in excellent_keypoints:
            colors.append('green')
        elif kp_idx in problematic_keypoints:
            colors.append('red')
        else:
            colors.append('orange')
    
    bars = ax2.bar(range(19), kp_avg_errors, color=colors, alpha=0.7)
    ax2.set_xlabel('Keypoint Index')
    ax2.set_ylabel('Average Error (mm)')
    ax2.set_title('Individual Keypoint Performance')
    ax2.grid(True, alpha=0.3)
    
    # 添加阈值线
    ax2.axhline(y=5, color='green', linestyle='--', alpha=0.7, label='Excellent (5mm)')
    ax2.axhline(y=8, color='orange', linestyle='--', alpha=0.7, label='Good (8mm)')
    ax2.axhline(y=12, color='red', linestyle='--', alpha=0.7, label='Problem (12mm)')
    ax2.legend()
    
    # 3. 解剖类型性能分析
    ax3 = axes[1, 0]
    
    anatomy_groups = {}
    for kp_idx in range(19):
        anatomy = F3_KEYPOINT_ANALYSIS[kp_idx]['anatomy']
        if anatomy not in anatomy_groups:
            anatomy_groups[anatomy] = []
        anatomy_groups[anatomy].append(kp_avg_errors[kp_idx])
    
    anatomy_names = list(anatomy_groups.keys())
    anatomy_errors = [np.mean(errors) for errors in anatomy_groups.values()]
    
    bars = ax3.bar(range(len(anatomy_names)), anatomy_errors, alpha=0.7)
    ax3.set_xticks(range(len(anatomy_names)))
    ax3.set_xticklabels(anatomy_names, rotation=45, ha='right')
    ax3.set_ylabel('Average Error (mm)')
    ax3.set_title('Performance by Anatomy Type')
    ax3.grid(True, alpha=0.3)
    
    # 4. 难度等级性能分析
    ax4 = axes[1, 1]
    
    difficulty_groups = {'easy': [], 'medium': [], 'hard': []}
    for kp_idx in range(19):
        difficulty = F3_KEYPOINT_ANALYSIS[kp_idx]['difficulty']
        difficulty_groups[difficulty].append(kp_avg_errors[kp_idx])
    
    difficulty_data = [difficulty_groups['easy'], difficulty_groups['medium'], difficulty_groups['hard']]
    difficulty_labels = ['Easy', 'Medium', 'Hard']
    
    bp = ax4.boxplot(difficulty_data, labels=difficulty_labels, patch_artist=True)
    colors = ['lightgreen', 'lightyellow', 'lightcoral']
    for patch, color in zip(bp['boxes'], colors):
        patch.set_facecolor(color)
    
    ax4.set_ylabel('Average Error (mm)')
    ax4.set_title('Performance by Difficulty Level')
    ax4.grid(True, alpha=0.3)
    
    # 5. 样本间变异性
    ax5 = axes[2, 0]
    
    sample_ids = [r['sample_id'] for r in results_12kp]
    x = np.arange(len(sample_ids))
    width = 0.25
    
    ax5.bar(x - width, errors_12kp, width, label='12-Point', alpha=0.7)
    ax5.bar(x, errors_19kp_selected, width, label='19-Point (Selected)', alpha=0.7)
    ax5.bar(x + width, errors_19kp_all, width, label='19-Point (All)', alpha=0.7)
    
    ax5.set_xlabel('Sample Index')
    ax5.set_ylabel('Average Error (mm)')
    ax5.set_title('Error Across Samples')
    ax5.set_xticks(x)
    ax5.set_xticklabels([sid[-3:] for sid in sample_ids], rotation=45)
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    # 6. 性能下降总结
    ax6 = axes[2, 1]
    ax6.axis('off')
    
    # 计算统计信息
    avg_12 = np.mean(errors_12kp)
    avg_19_sel = np.mean(errors_19kp_selected)
    avg_19_all = np.mean(errors_19kp_all)
    
    degradation_sel = avg_19_sel - avg_12
    degradation_all = avg_19_all - avg_12
    
    summary_text = f"""
Performance Degradation Analysis:

Overall Performance:
• 12-Point Model: {avg_12:.2f}mm
• 19-Point (Selected 12): {avg_19_sel:.2f}mm
• 19-Point (All 19): {avg_19_all:.2f}mm

Performance Drop:
• Selected 12 points: +{degradation_sel:.2f}mm ({degradation_sel/avg_12*100:.1f}%)
• All 19 points: +{degradation_all:.2f}mm ({degradation_all/avg_12*100:.1f}%)

Keypoint Analysis:
• Excellent (≤5mm): {len(excellent_keypoints)} keypoints
• Problematic (≥12mm): {len(problematic_keypoints)} keypoints

Main Causes:
1. Task Complexity: 58% more keypoints
2. Training Data: 72% fewer samples (72→20)
3. Difficult Keypoints: Edge/boundary points
4. Model Capacity: Same architecture for harder task

Problematic Keypoints:
"""
    
    for kp_idx in problematic_keypoints:
        info = F3_KEYPOINT_ANALYSIS[kp_idx]
        summary_text += f"• {info['name']} ({info['anatomy']}): {kp_avg_errors[kp_idx]:.1f}mm\n"
    
    ax6.text(0.05, 0.95, summary_text, transform=ax6.transAxes, fontsize=9,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    
    plt.suptitle('19-Point Performance Degradation Analysis\nIdentifying Problematic Keypoints', 
                fontsize=16, fontweight='bold')
    plt.tight_layout(rect=[0, 0, 1, 0.93])
    
    filename = '19kp_degradation_analysis.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 性能下降分析保存: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🔍 19关键点性能下降分析")
    print("找出具体哪些关键点导致了性能下降")
    print("=" * 60)
    
    # 加载模型和数据
    model_12kp, model_19kp, data_12kp, data_19kp, device = load_models_and_data()
    
    if model_12kp is None or model_19kp is None:
        print("❌ 模型加载失败，无法进行对比")
        return
    
    # 找到共同样本
    common_ids, indices_12kp, indices_19kp = find_common_samples(data_12kp, data_19kp)
    
    if len(common_ids) == 0:
        print("❌ 没有找到共同样本")
        return
    
    # 在共同样本上测试
    results_12kp, results_19kp = test_models_on_common_samples(
        model_12kp, model_19kp, data_12kp, data_19kp, 
        common_ids, indices_12kp, indices_19kp, device
    )
    
    # 分析性能下降
    kp_avg_errors, problematic_keypoints, excellent_keypoints = analyze_performance_degradation(
        results_12kp, results_19kp
    )
    
    # 创建可视化
    create_degradation_analysis_visualization(
        results_12kp, results_19kp, kp_avg_errors, 
        problematic_keypoints, excellent_keypoints
    )
    
    print(f"\n🎯 关键发现:")
    print("=" * 50)
    print(f"主要性能下降原因:")
    print("1. 任务复杂度增加 - 关键点数量增加58% (12→19)")
    print("2. 训练数据减少 - 样本数量减少72% (72→20)")
    print("3. 困难关键点 - 边缘和边界点难以精确定位")
    print("4. 模型容量限制 - 相同架构处理更复杂任务")
    
    if problematic_keypoints:
        print(f"\n🚨 需要重点优化的关键点:")
        for kp_idx in problematic_keypoints:
            info = F3_KEYPOINT_ANALYSIS[kp_idx]
            print(f"   {info['name']} ({info['anatomy']}): {kp_avg_errors[kp_idx]:.2f}mm")
    
    print(f"\n💡 改进建议:")
    print("1. 增加训练数据，特别是困难关键点的样本")
    print("2. 针对边缘点设计特殊的检测策略")
    print("3. 使用更大的模型容量处理复杂任务")
    print("4. 考虑分层训练：先训练容易的点，再训练困难的点")

if __name__ == "__main__":
    main()
