#!/usr/bin/env python3
"""
使用修复后的19关键点数据训练模型
应用正确的预处理后重新训练
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from basic_19keypoints_system import BasicHeatmapPointNet19, extract_keypoints_from_heatmaps_19

class FixedF3Dataset(Dataset):
    """修复后的F3 19关键点数据集"""
    
    def __init__(self, point_clouds, keypoints, sample_ids, num_points=8192, 
                 sigma=6.0, augment=False):
        self.point_clouds = point_clouds
        self.keypoints = keypoints
        self.sample_ids = sample_ids
        self.num_points = num_points
        self.sigma = sigma
        self.augment = augment
        
        print(f"📊 修复后F3数据集:")
        print(f"   样本数: {len(self.point_clouds)}")
        print(f"   关键点数: {len(self.keypoints[0]) if len(self.keypoints) > 0 else 0}")
        print(f"   数据增强: {self.augment}")
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        pc = self.point_clouds[idx].copy()
        kp = self.keypoints[idx].copy()
        sample_id = self.sample_ids[idx]
        
        # 数据增强
        if self.augment:
            pc, kp = self.apply_augmentation(pc, kp)
        
        # 采样点云
        if len(pc) > self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=False)
            pc = pc[indices]
        elif len(pc) < self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=True)
            pc = pc[indices]
        
        # 生成热力图目标
        heatmaps = self.create_heatmap_targets(kp, pc)
        
        return {
            'point_cloud': torch.FloatTensor(pc).transpose(0, 1),  # [3, N]
            'heatmaps': torch.FloatTensor(heatmaps),  # [19, N]
            'keypoints': torch.FloatTensor(kp),  # [19, 3]
            'sample_id': sample_id
        }
    
    def create_heatmap_targets(self, keypoints, point_cloud):
        """创建热力图目标"""
        num_points = len(point_cloud)
        num_keypoints = len(keypoints)
        heatmaps = np.zeros((num_keypoints, num_points))
        
        for kp_idx, keypoint in enumerate(keypoints):
            # 计算每个点到关键点的距离
            distances = np.linalg.norm(point_cloud - keypoint, axis=1)
            # 生成高斯分布
            heatmaps[kp_idx] = np.exp(-distances**2 / (2 * self.sigma**2))
        
        return heatmaps
    
    def apply_augmentation(self, pc, kp):
        """保守的数据增强"""
        # 小幅旋转
        if np.random.random() < 0.3:
            angle = np.random.uniform(-5, 5) * np.pi / 180
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            pc = pc @ rotation_matrix.T
            kp = kp @ rotation_matrix.T
        
        # 轻微缩放
        if np.random.random() < 0.2:
            scale = np.random.uniform(0.98, 1.02)
            pc = pc * scale
            kp = kp * scale
        
        # 小幅平移
        if np.random.random() < 0.2:
            translation = np.random.uniform(-1, 1, 3)
            pc = pc + translation
            kp = kp + translation
        
        # 轻微噪声
        if np.random.random() < 0.1:
            noise = np.random.normal(0, 0.1, pc.shape)
            pc = pc + noise
        
        return pc, kp

def train_fixed_19kp_model(train_loader, val_loader, device, num_epochs=50):
    """训练修复后的19关键点模型"""
    
    # 创建模型 - 使用与12点相同的架构
    model = BasicHeatmapPointNet19(input_dim=3, num_keypoints=19).to(device)
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=0.0005, weight_decay=0.01)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=10, factor=0.5)
    criterion = nn.MSELoss()
    
    # 训练历史
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    
    print(f"🚀 开始训练修复后的19关键点模型 ({num_epochs} epochs)")
    print(f"📊 模型参数: {sum(p.numel() for p in model.parameters())}")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0
        
        for batch_idx, batch in enumerate(train_loader):
            point_clouds = batch['point_cloud'].to(device)  # [B, 3, N]
            heatmap_targets = batch['heatmaps'].to(device)  # [B, 19, N]
            
            optimizer.zero_grad()
            
            # 前向传播
            pred_heatmaps = model(point_clouds)
            
            # 计算损失
            loss = criterion(pred_heatmaps, heatmap_targets)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
        
        # 验证阶段
        model.eval()
        val_loss = 0
        
        with torch.no_grad():
            for batch in val_loader:
                point_clouds = batch['point_cloud'].to(device)
                heatmap_targets = batch['heatmaps'].to(device)
                
                pred_heatmaps = model(point_clouds)
                loss = criterion(pred_heatmaps, heatmap_targets)
                val_loss += loss.item()
        
        # 计算平均损失
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            torch.save(model.state_dict(), 'best_fixed_19kp_model.pth')
            print(f"✅ 保存最佳模型 (epoch {epoch+1})")
        
        # 打印进度
        if (epoch + 1) % 5 == 0:
            print(f"Epoch {epoch+1}/{num_epochs}:")
            print(f"  Train Loss: {avg_train_loss:.4f}")
            print(f"  Val Loss: {avg_val_loss:.4f}")
            print(f"  LR: {optimizer.param_groups[0]['lr']:.6f}")
    
    return model, train_losses, val_losses

def test_fixed_model_performance(model, point_clouds, keypoints, sample_ids, device):
    """测试修复后模型的性能"""
    
    print(f"🔍 测试修复后模型性能...")
    
    results = []
    
    for i in range(len(point_clouds)):
        sample_id = sample_ids[i]
        point_cloud = point_clouds[i]
        true_keypoints = keypoints[i]
        
        # 采样点云
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            pc_sampled = point_cloud[indices]
        else:
            pc_sampled = point_cloud
        
        pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
        
        # 预测
        with torch.no_grad():
            pred_heatmaps = model(pc_tensor)
        
        pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze()
        pred_keypoints, confidences = extract_keypoints_from_heatmaps_19(pred_heatmaps_np, pc_sampled)
        
        # 计算误差
        errors = [np.linalg.norm(pred_keypoints[j] - true_keypoints[j]) for j in range(len(true_keypoints))]
        avg_error = np.mean(errors)
        
        results.append({
            'sample_id': sample_id,
            'avg_error': avg_error,
            'errors': errors,
            'confidences': confidences
        })
        
        print(f"   样本 {sample_id}: {avg_error:.2f}mm")
    
    return results

def create_performance_comparison_chart(results_fixed, results_original=None):
    """创建性能对比图表"""
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 误差分布
    ax1 = axes[0, 0]
    
    errors_fixed = [r['avg_error'] for r in results_fixed]
    
    ax1.hist(errors_fixed, bins=10, alpha=0.7, color='blue', label='Fixed 19-Point')
    ax1.set_xlabel('Average Error (mm)')
    ax1.set_ylabel('Number of Samples')
    ax1.set_title('Error Distribution')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 添加统计信息
    mean_error = np.mean(errors_fixed)
    std_error = np.std(errors_fixed)
    ax1.axvline(mean_error, color='red', linestyle='--', 
               label=f'Mean: {mean_error:.2f}mm')
    ax1.legend()
    
    # 2. 样本误差
    ax2 = axes[0, 1]
    
    sample_indices = range(len(results_fixed))
    ax2.bar(sample_indices, errors_fixed, alpha=0.7, color='blue')
    ax2.set_xlabel('Sample Index')
    ax2.set_ylabel('Average Error (mm)')
    ax2.set_title('Error by Sample')
    ax2.grid(True, alpha=0.3)
    
    # 添加平均线
    ax2.axhline(mean_error, color='red', linestyle='--', alpha=0.7,
               label=f'Average: {mean_error:.2f}mm')
    ax2.legend()
    
    # 3. 关键点误差分析
    ax3 = axes[1, 0]
    
    # 计算每个关键点的平均误差
    all_kp_errors = np.array([r['errors'] for r in results_fixed])  # [samples, 19]
    kp_mean_errors = np.mean(all_kp_errors, axis=0)  # [19]
    
    ax3.bar(range(19), kp_mean_errors, alpha=0.7, color='green')
    ax3.set_xlabel('Keypoint Index')
    ax3.set_ylabel('Mean Error (mm)')
    ax3.set_title('Error by Keypoint')
    ax3.grid(True, alpha=0.3)
    
    # 标记最好和最差的关键点
    best_kp = np.argmin(kp_mean_errors)
    worst_kp = np.argmax(kp_mean_errors)
    ax3.bar(best_kp, kp_mean_errors[best_kp], color='blue', alpha=0.8, 
           label=f'Best: KP{best_kp+1} ({kp_mean_errors[best_kp]:.1f}mm)')
    ax3.bar(worst_kp, kp_mean_errors[worst_kp], color='red', alpha=0.8,
           label=f'Worst: KP{worst_kp+1} ({kp_mean_errors[worst_kp]:.1f}mm)')
    ax3.legend()
    
    # 4. 性能统计
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    # 计算准确率
    accuracy_5mm = np.sum(np.array(errors_fixed) <= 5) / len(errors_fixed) * 100
    accuracy_10mm = np.sum(np.array(errors_fixed) <= 10) / len(errors_fixed) * 100
    
    stats_text = f"""
Fixed 19-Point Model Performance:

Overall Statistics:
• Total Samples: {len(results_fixed)}
• Mean Error: {mean_error:.2f} ± {std_error:.2f} mm
• Median Error: {np.median(errors_fixed):.2f} mm
• Min Error: {np.min(errors_fixed):.2f} mm
• Max Error: {np.max(errors_fixed):.2f} mm

Accuracy Rates:
• ≤ 5mm: {accuracy_5mm:.1f}% ({np.sum(np.array(errors_fixed) <= 5)}/{len(errors_fixed)})
• ≤ 10mm: {accuracy_10mm:.1f}% ({np.sum(np.array(errors_fixed) <= 10)}/{len(errors_fixed)})

Best Keypoints:
• KP{best_kp+1}: {kp_mean_errors[best_kp]:.2f}mm
• KP{np.argsort(kp_mean_errors)[1]+1}: {kp_mean_errors[np.argsort(kp_mean_errors)[1]]:.2f}mm
• KP{np.argsort(kp_mean_errors)[2]+1}: {kp_mean_errors[np.argsort(kp_mean_errors)[2]]:.2f}mm

Worst Keypoints:
• KP{worst_kp+1}: {kp_mean_errors[worst_kp]:.2f}mm
• KP{np.argsort(kp_mean_errors)[-2]+1}: {kp_mean_errors[np.argsort(kp_mean_errors)[-2]]:.2f}mm
• KP{np.argsort(kp_mean_errors)[-3]+1}: {kp_mean_errors[np.argsort(kp_mean_errors)[-3]]:.2f}mm

Data Processing:
✓ Applied same preprocessing as 12-point data
✓ Coordinate system alignment
✓ Proper scaling and centering
✓ Consistent point cloud sampling
"""
    
    ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, fontsize=10,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    plt.suptitle('Fixed 19-Point Model Performance Analysis\nAfter Proper Data Preprocessing', 
                fontsize=16, fontweight='bold')
    plt.tight_layout(rect=[0, 0, 1, 0.93])
    
    filename = 'fixed_19kp_performance.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 修复后性能分析保存: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🔧 训练修复后的19关键点模型")
    print("使用正确预处理的数据")
    print("=" * 60)
    
    # 加载修复后的数据
    data = np.load('f3_19kp_preprocessed.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    sample_ids = data['sample_ids']
    
    print(f"✅ 修复后数据加载完成:")
    print(f"   样本数: {len(point_clouds)}")
    print(f"   关键点形状: {keypoints.shape}")
    print(f"   平均点云大小: {np.mean([len(pc) for pc in point_clouds]):.0f}")
    
    # 数据分割
    train_indices, val_indices = train_test_split(
        range(len(point_clouds)), test_size=0.2, random_state=42
    )
    
    # 创建数据集
    train_dataset = FixedF3Dataset(
        point_clouds[train_indices], keypoints[train_indices], 
        sample_ids[train_indices], augment=True
    )
    
    val_dataset = FixedF3Dataset(
        point_clouds[val_indices], keypoints[val_indices], 
        sample_ids[val_indices], augment=False
    )
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False, num_workers=2)
    
    print(f"\n📊 数据分割:")
    print(f"   训练集: {len(train_dataset)} 样本")
    print(f"   验证集: {len(val_dataset)} 样本")
    
    # 训练模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    model, train_losses, val_losses = train_fixed_19kp_model(
        train_loader, val_loader, device, num_epochs=40
    )
    
    # 绘制训练曲线
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='Train Loss', alpha=0.8)
    plt.plot(val_losses, label='Validation Loss', alpha=0.8)
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Fixed 19-Point Training Progress')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('fixed_19kp_training_progress.png', dpi=300, bbox_inches='tight')
    print("📊 训练进度保存: fixed_19kp_training_progress.png")
    plt.close()
    
    # 测试模型性能
    results = test_fixed_model_performance(model, point_clouds, keypoints, sample_ids, device)
    
    # 创建性能分析
    create_performance_comparison_chart(results)
    
    # 总结
    avg_error = np.mean([r['avg_error'] for r in results])
    print(f"\n🎉 修复后19关键点模型训练完成!")
    print(f"✅ 最佳模型保存为: best_fixed_19kp_model.pth")
    print(f"📊 平均误差: {avg_error:.2f}mm")
    print(f"💡 关键改进:")
    print("   1. 应用了与12点相同的预处理流程")
    print("   2. 坐标系对齐和数据归一化")
    print("   3. 一致的点云采样策略")
    print("   4. 修复了数据不匹配问题")

if __name__ == "__main__":
    main()
