#!/usr/bin/env python3
"""
简化的57关键点数据分析
Simple 57 keypoints data analysis
"""

import numpy as np
import pandas as pd
import os
from pathlib import Path

def analyze_original_57_data():
    """分析原始57关键点数据"""
    
    print("📊 分析原始57关键点数据...")
    print("=" * 50)
    
    data_dir = Path("data/Data/annotations")
    
    if not data_dir.exists():
        print(f"❌ 数据目录不存在: {data_dir}")
        return
    
    # 获取所有标注文件
    annotation_files = list(data_dir.glob("*-Table-XYZ.CSV"))
    print(f"📁 找到 {len(annotation_files)} 个标注文件")
    
    # 分析前几个文件
    sample_count = 0
    valid_samples = []
    
    for ann_file in annotation_files[:10]:  # 只分析前10个
        sample_id = ann_file.stem.split('-')[0]
        
        try:
            # 尝试不同编码
            for encoding in ['gbk', 'utf-8', 'latin1']:
                try:
                    df = pd.read_csv(ann_file, encoding=encoding)
                    break
                except:
                    continue
            else:
                print(f"⚠️ {sample_id}: 无法读取文件")
                continue
            
            print(f"\n📋 样本 {sample_id}:")
            print(f"   关键点数量: {len(df)}")
            print(f"   列名: {list(df.columns)}")
            
            if len(df) >= 57:
                # 分析关键点分布
                analyze_keypoint_distribution(df, sample_id)
                valid_samples.append(sample_id)
                sample_count += 1
            else:
                print(f"   ⚠️ 关键点数量不足: {len(df)} < 57")
                
        except Exception as e:
            print(f"❌ {sample_id}: 处理失败 - {e}")
            continue
    
    print(f"\n✅ 有效样本数: {sample_count}")
    print(f"📋 有效样本ID: {valid_samples}")
    
    return valid_samples

def analyze_keypoint_distribution(df, sample_id):
    """分析关键点分布"""
    
    # 统计各区域的关键点
    f1_count = len([idx for idx, row in df.iterrows() if row['label'].startswith('F_1')])
    f2_count = len([idx for idx, row in df.iterrows() if row['label'].startswith('F_2')])
    f3_count = len([idx for idx, row in df.iterrows() if row['label'].startswith('F_3')])
    
    print(f"   F1区域: {f1_count}个点")
    print(f"   F2区域: {f2_count}个点") 
    print(f"   F3区域: {f3_count}个点")
    print(f"   总计: {f1_count + f2_count + f3_count}个点")
    
    # 检查坐标范围
    if 'X' in df.columns and 'Y' in df.columns and 'Z' in df.columns:
        x_range = (df['X'].min(), df['X'].max())
        y_range = (df['Y'].min(), df['Y'].max())
        z_range = (df['Z'].min(), df['Z'].max())
        
        print(f"   X范围: {x_range[0]:.1f} ~ {x_range[1]:.1f}")
        print(f"   Y范围: {y_range[0]:.1f} ~ {y_range[1]:.1f}")
        print(f"   Z范围: {z_range[0]:.1f} ~ {z_range[1]:.1f}")

def create_12_to_57_mapping():
    """创建12点到57点的映射关系"""
    
    print("\n🔗 创建12点到57点映射关系...")
    print("=" * 50)
    
    # 当前12个关键点的定义
    current_12_mapping = {
        # F1区域 (左髂骨) - 4个关键点
        0: {"region": "F1", "name": "F1-1", "anatomy": "髂前上棘", "original_idx": 0},
        1: {"region": "F1", "name": "F1-2", "anatomy": "髂前下棘", "original_idx": 1},
        2: {"region": "F1", "name": "F1-3", "anatomy": "髂嵴前端", "original_idx": 2},
        3: {"region": "F1", "name": "F1-13", "anatomy": "髂后上棘", "original_idx": 12},
        
        # F2区域 (右髂骨) - 4个关键点
        4: {"region": "F2", "name": "F2-1", "anatomy": "髂前上棘", "original_idx": 19},
        5: {"region": "F2", "name": "F2-2", "anatomy": "髂前下棘", "original_idx": 20},
        6: {"region": "F2", "name": "F2-3", "anatomy": "髂嵴前端", "original_idx": 21},
        7: {"region": "F2", "name": "F2-13", "anatomy": "髂后上棘", "original_idx": 31},
        
        # F3区域 (骶骨/尾骨) - 4个关键点
        8: {"region": "F3", "name": "F3-1", "anatomy": "骶骨岬", "original_idx": 38},
        9: {"region": "F3", "name": "F3-15", "anatomy": "尾骨尖", "original_idx": 52},
        10: {"region": "F3", "name": "F3-13", "anatomy": "骶骨左侧", "original_idx": 50},
        11: {"region": "F3", "name": "F3-14", "anatomy": "骶骨右侧", "original_idx": 51},
    }
    
    print("📋 当前12个关键点映射:")
    for idx, info in current_12_mapping.items():
        print(f"   {idx:2d}: {info['name']} ({info['anatomy']}) -> 原始索引 {info['original_idx']}")
    
    return current_12_mapping

def extract_12_from_57_sample():
    """从57点样本中提取12个核心点"""
    
    print("\n🔄 从57点样本提取12个核心点...")
    print("=" * 50)
    
    data_dir = Path("data/Data/annotations")
    annotation_files = list(data_dir.glob("*-Table-XYZ.CSV"))
    
    if not annotation_files:
        print("❌ 没有找到标注文件")
        return
    
    # 使用第一个有效文件作为示例
    for ann_file in annotation_files[:5]:
        sample_id = ann_file.stem.split('-')[0]
        
        try:
            # 读取文件
            for encoding in ['gbk', 'utf-8', 'latin1']:
                try:
                    df = pd.read_csv(ann_file, encoding=encoding)
                    break
                except:
                    continue
            
            if len(df) < 57:
                continue
            
            print(f"\n📋 处理样本: {sample_id}")
            
            # 提取57个关键点坐标
            keypoints_57 = []
            labels = []
            
            for idx, row in df.iterrows():
                if idx >= 57:  # 只取前57个
                    break
                    
                x, y, z = row['X'], row['Y'], row['Z']
                label = row['label']
                
                keypoints_57.append([x, y, z])
                labels.append(label)
            
            keypoints_57 = np.array(keypoints_57)
            
            print(f"   57个关键点形状: {keypoints_57.shape}")
            
            # 根据映射提取12个核心点
            mapping = create_12_to_57_mapping()
            keypoints_12 = np.zeros((12, 3))
            
            for i in range(12):
                original_idx = mapping[i]['original_idx']
                if original_idx < len(keypoints_57):
                    keypoints_12[i] = keypoints_57[original_idx]
                    print(f"   提取 {mapping[i]['name']}: {keypoints_57[original_idx]}")
            
            print(f"   提取的12个关键点形状: {keypoints_12.shape}")
            
            # 保存示例数据
            np.save(f'sample_{sample_id}_57points.npy', keypoints_57)
            np.save(f'sample_{sample_id}_12points.npy', keypoints_12)
            
            print(f"   ✅ 已保存: sample_{sample_id}_57points.npy, sample_{sample_id}_12points.npy")
            
            return keypoints_57, keypoints_12, labels
            
        except Exception as e:
            print(f"❌ {sample_id}: 处理失败 - {e}")
            continue
    
    return None, None, None

def analyze_expansion_feasibility():
    """分析扩展可行性"""
    
    print("\n🎯 分析12→57扩展可行性...")
    print("=" * 50)
    
    # 检查数据可用性
    data_dir = Path("data/Data")
    annotations_dir = data_dir / "annotations"
    stl_dir = data_dir / "stl_models"
    
    print(f"📁 数据目录检查:")
    print(f"   标注目录: {annotations_dir.exists()} ({annotations_dir})")
    print(f"   STL目录: {stl_dir.exists()} ({stl_dir})")
    
    if annotations_dir.exists():
        ann_files = list(annotations_dir.glob("*.CSV"))
        print(f"   标注文件数: {len(ann_files)}")
    
    if stl_dir.exists():
        stl_files = list(stl_dir.glob("*.stl"))
        print(f"   STL文件数: {len(stl_files)}")
    
    # 检查当前12点数据
    current_data_files = [
        "archive/old_experiments/f3_reduced_12kp_female.npz",
        "archive/old_experiments/f3_reduced_12kp_male.npz"
    ]
    
    print(f"\n📊 当前12点数据检查:")
    total_12_samples = 0
    
    for file_path in current_data_files:
        if os.path.exists(file_path):
            data = np.load(file_path, allow_pickle=True)
            pc = data['point_clouds']
            kp = data['keypoints']
            gender = "女性" if "female" in file_path else "男性"
            
            print(f"   {gender}: {len(pc)}个样本, 关键点形状: {kp.shape}")
            total_12_samples += len(pc)
        else:
            print(f"   ❌ 文件不存在: {file_path}")
    
    print(f"   总计12点样本: {total_12_samples}")
    
    # 可行性评估
    print(f"\n✅ 扩展可行性评估:")
    
    feasibility_score = 0
    
    if annotations_dir.exists() and len(list(annotations_dir.glob("*.CSV"))) > 50:
        print(f"   ✅ 原始57点数据充足 (+30分)")
        feasibility_score += 30
    else:
        print(f"   ⚠️ 原始57点数据不足 (+10分)")
        feasibility_score += 10
    
    if total_12_samples >= 90:
        print(f"   ✅ 12点训练数据充足 (+25分)")
        feasibility_score += 25
    else:
        print(f"   ⚠️ 12点训练数据有限 (+15分)")
        feasibility_score += 15
    
    if stl_dir.exists() and len(list(stl_dir.glob("*.stl"))) > 200:
        print(f"   ✅ STL模型数据充足 (+20分)")
        feasibility_score += 20
    else:
        print(f"   ⚠️ STL模型数据有限 (+10分)")
        feasibility_score += 10
    
    # 技术基础评估
    print(f"   ✅ 关键点相互辅助策略成熟 (+15分)")
    feasibility_score += 15
    
    print(f"   ✅ 解剖学约束经验丰富 (+10分)")
    feasibility_score += 10
    
    print(f"\n🎯 总体可行性评分: {feasibility_score}/100")
    
    if feasibility_score >= 80:
        recommendation = "强烈推荐"
        color = "🟢"
    elif feasibility_score >= 60:
        recommendation = "推荐"
        color = "🟡"
    else:
        recommendation = "需要谨慎评估"
        color = "🔴"
    
    print(f"{color} 扩展建议: {recommendation}")
    
    return feasibility_score

def generate_expansion_roadmap():
    """生成扩展路线图"""
    
    print("\n🗺️ 生成扩展实施路线图...")
    print("=" * 50)
    
    roadmap = {
        "阶段1: 数据准备 (1-2周)": [
            "✅ 分析原始57点数据结构",
            "🔄 建立12→57映射关系",
            "📊 创建扩展训练数据集",
            "🔍 验证数据质量"
        ],
        
        "阶段2: 渐进扩展 (2-3周)": [
            "🎯 实现12→19点扩展 (每区域6-7点)",
            "🧪 验证19点性能",
            "🔄 实现19→38点扩展",
            "📈 性能对比分析"
        ],
        
        "阶段3: 完整扩展 (2-3周)": [
            "🚀 实现38→57点完整扩展",
            "🤖 训练端到端57点模型",
            "📊 全面性能评估",
            "🏥 医疗应用验证"
        ],
        
        "阶段4: 优化完善 (1-2周)": [
            "⚡ 模型优化和压缩",
            "📝 撰写技术文档",
            "🎉 集成到论文中",
            "🚀 准备开源发布"
        ]
    }
    
    for stage, tasks in roadmap.items():
        print(f"\n📋 {stage}")
        for task in tasks:
            print(f"   {task}")
    
    print(f"\n⏱️ 预计总时间: 6-10周")
    print(f"🎯 关键里程碑:")
    print(f"   - 2周后: 19点扩展完成")
    print(f"   - 5周后: 57点系统完成")
    print(f"   - 8周后: 论文集成完成")

def main():
    """主函数"""
    
    print("🎯 12点到57点关键点扩展可行性分析")
    print("基于当前优秀的12点成果，评估扩展到完整57点系统的可行性")
    print("=" * 80)
    
    # 1. 分析原始57点数据
    valid_samples = analyze_original_57_data()
    
    # 2. 创建映射关系
    mapping = create_12_to_57_mapping()
    
    # 3. 提取示例数据
    kp_57, kp_12, labels = extract_12_from_57_sample()
    
    # 4. 可行性评估
    feasibility_score = analyze_expansion_feasibility()
    
    # 5. 生成路线图
    generate_expansion_roadmap()
    
    # 6. 总结建议
    print(f"\n" + "=" * 80)
    print("🎉 分析总结")
    print("=" * 80)
    
    if feasibility_score >= 70:
        print(f"✅ 扩展到57点是可行的！")
        print(f"💡 建议:")
        print(f"   1. 立即开始数据准备工作")
        print(f"   2. 采用渐进式扩展策略")
        print(f"   3. 保持当前12点的优秀性能作为基础")
        print(f"   4. 重点关注解剖学约束的扩展")
        
        print(f"\n🎯 预期收益:")
        print(f"   - 更完整的数据集 (57 vs 12点)")
        print(f"   - 更高的学术价值")
        print(f"   - 更广的临床应用")
        print(f"   - 更强的技术创新")
        
    else:
        print(f"⚠️ 扩展到57点需要谨慎评估")
        print(f"💡 建议:")
        print(f"   1. 先完善当前12点系统")
        print(f"   2. 收集更多原始数据")
        print(f"   3. 进行小规模扩展试验")
        print(f"   4. 评估风险和收益")
    
    print(f"\n🚀 下一步行动:")
    print(f"   1. 运行数据提取脚本")
    print(f"   2. 建立完整的映射关系")
    print(f"   3. 实现渐进式扩展原型")
    print(f"   4. 评估初步扩展效果")

if __name__ == "__main__":
    main()
