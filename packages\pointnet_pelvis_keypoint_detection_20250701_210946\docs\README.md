# PointNet 骨盆关键点检测项目

## 项目概述
基于PointNet的骨盆关键点检测系统，实现了95.2%的5mm准确率和2.39mm的平均误差。

## 性能指标
- **5mm准确率**: 95.2%
- **平均误差**: 2.39mm
- **关键点数量**: 57个
- **训练数据**: 100个样本

## 目录结构
```
pointnet_pelvis_keypoint_detection_20250701_210946/
├── code/                   # 核心代码
│   ├── save_best_model.py     # 最佳模型定义
│   ├── improved_data_loader.py # 数据加载器
│   ├── improved_training.py    # 训练脚本
│   └── web_3d_viewer.py       # 3D可视化
├── models/                 # 训练好的模型
│   ├── best_baseline_model.pth
│   └── debug_model_epoch_7.pth
├── results/                # 训练结果和可视化
│   ├── output/
│   └── visualization/
├── data_samples/           # 数据样本
├── docs/                   # 文档
├── configs/                # 配置文件
└── scripts/                # 运行脚本
```

## 快速开始

### 1. 环境配置
```bash
conda create -n pointnet python=3.8
conda activate pointnet
pip install torch torchvision matplotlib plotly tqdm
```

### 2. 运行预测
```bash
python code/test_predictions.py
```

### 3. 查看3D可视化
```bash
python code/web_3d_viewer.py
python code/start_web_server.py
```

### 4. 重新训练
```bash
python code/improved_training.py
```

## 主要文件说明

### 核心模型
- `save_best_model.py`: 最佳性能的PointNet模型
- `scale_corrected_model.py`: 尺度校正版本模型

### 数据处理
- `improved_data_loader.py`: 优化的数据加载器
- `fix_data_loader.py`: 数据修复工具

### 训练脚本
- `improved_training.py`: 主要训练脚本
- `debug_training.py`: 调试训练脚本

### 可视化工具
- `web_3d_viewer.py`: Web版3D可视化
- `direct_3d_viewer.py`: 直接3D查看器
- `start_web_server.py`: Web服务器启动器

### 测试验证
- `test_predictions.py`: 预测测试
- `validate_scale_hypothesis.py`: 尺度假设验证

## 技术特点
1. **高精度**: 95.2%的5mm准确率
2. **鲁棒性**: 稳定的训练过程
3. **可视化**: 完整的3D交互式可视化
4. **易用性**: 简洁的代码结构

## 改进历程
1. 数据质量修复 → 90%准确率
2. 模型架构优化 → 94%准确率  
3. 训练策略改进 → 95.2%准确率

## 联系信息
项目打包时间: 2025-07-01 21:09:49
