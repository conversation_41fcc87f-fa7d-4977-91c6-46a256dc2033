#!/usr/bin/env python3
"""
Train Conservative PointNet on F3 Dataset

Start with a simpler PointNet model and smaller point clouds to avoid memory issues.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import h5py
from pathlib import Path
import time
import json
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split

class ConservativeF3Dataset(Dataset):
    """Conservative dataset loader with smaller point clouds"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 8192,
                 test_size: float = 0.2, val_size: float = 0.1, random_state: int = 42, 
                 augment: bool = False):
        
        self.num_points = num_points
        self.augment = augment
        
        # Load the high quality dataset
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']  # (N, 50000, 3)
        keypoints = data['keypoints']        # (N, 19, 3)
        centers = data['centers']            # (N, 3)
        
        print(f"📦 加载数据集: {len(sample_ids)} 样本")
        print(f"   原始点云形状: {point_clouds.shape}")
        print(f"   目标点云大小: {num_points}")
        
        # Split data
        indices = np.arange(len(sample_ids))
        train_indices, temp_indices = train_test_split(
            indices, test_size=test_size + val_size, random_state=random_state
        )
        val_indices, test_indices = train_test_split(
            temp_indices, test_size=test_size/(test_size + val_size), random_state=random_state
        )
        
        if split == 'train':
            self.indices = train_indices
        elif split == 'val':
            self.indices = val_indices
        elif split == 'test':
            self.indices = test_indices
        else:
            raise ValueError(f"Unknown split: {split}")
        
        self.sample_ids = sample_ids[self.indices]
        self.point_clouds = point_clouds[self.indices]
        self.keypoints = keypoints[self.indices]
        self.centers = centers[self.indices]
        
        print(f"   {split} 分割: {len(self.indices)} 样本")
    
    def __len__(self):
        return len(self.indices)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()  # (50000, 3)
        keypoints = self.keypoints[idx].copy()       # (19, 3)
        
        # Downsample point cloud
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        elif len(point_cloud) < self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=True)
            point_cloud = point_cloud[indices]
        
        # Data augmentation for training
        if self.augment:
            # Random rotation around Z-axis
            angle = np.random.uniform(-np.pi/12, np.pi/12)  # ±15 degrees
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            
            point_cloud = point_cloud @ rotation_matrix.T
            keypoints = keypoints @ rotation_matrix.T
            
            # Small random translation
            translation = np.random.uniform(-1.0, 1.0, 3)  # ±1mm
            point_cloud += translation
            keypoints += translation
            
            # Add small amount of noise
            noise = np.random.normal(0, 0.05, point_cloud.shape)  # 0.05mm std
            point_cloud += noise
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),  # (num_points, 3)
            'keypoints': torch.FloatTensor(keypoints),      # (19, 3)
            'sample_id': self.sample_ids[idx]
        }

class SimplePointNet(nn.Module):
    """Simple PointNet for medical keypoint detection"""
    
    def __init__(self, num_keypoints: int = 19, num_points: int = 8192):
        super(SimplePointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        self.num_points = num_points
        
        # Point-wise MLPs
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(1024)
        
        # Global feature MLPs
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, num_keypoints * 3)
        
        self.dropout = nn.Dropout(p=0.3)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(256)
        
    def forward(self, x):
        # x: (batch_size, num_points, 3)
        batch_size = x.size(0)
        
        # Transpose for conv1d: (batch_size, 3, num_points)
        x = x.transpose(2, 1)
        
        # Point-wise feature extraction
        x = torch.relu(self.bn1(self.conv1(x)))
        x = torch.relu(self.bn2(self.conv2(x)))
        x = torch.relu(self.bn3(self.conv3(x)))
        
        # Global max pooling
        x = torch.max(x, 2, keepdim=True)[0]  # (batch_size, 1024, 1)
        x = x.view(batch_size, -1)  # (batch_size, 1024)
        
        # Global feature MLPs
        x = torch.relu(self.bn4(self.fc1(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn5(self.fc2(x)))
        x = self.dropout(x)
        x = self.fc3(x)  # (batch_size, num_keypoints * 3)
        
        # Reshape to keypoints
        x = x.view(batch_size, self.num_keypoints, 3)
        
        return x

def calculate_metrics(pred_keypoints, target_keypoints):
    """Calculate evaluation metrics"""
    # pred_keypoints, target_keypoints: (batch_size, num_keypoints, 3)
    
    distances = torch.norm(pred_keypoints - target_keypoints, dim=2)  # (batch_size, num_keypoints)
    
    # Average distance per sample
    avg_distances = torch.mean(distances, dim=1)  # (batch_size,)
    
    # Overall metrics
    mean_distance = torch.mean(avg_distances).item()
    std_distance = torch.std(avg_distances).item()
    max_distance = torch.max(avg_distances).item()
    
    # Accuracy metrics
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_10mm = (avg_distances <= 10.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_distance,
        'std_distance': std_distance,
        'max_distance': max_distance,
        'within_1mm_percent': within_1mm,
        'within_5mm_percent': within_5mm,
        'within_10mm_percent': within_10mm
    }

def train_conservative_pointnet():
    """Train conservative PointNet on F3 dataset"""
    
    print("🚀 **训练保守PointNet模型 - F3数据集**")
    print("🎯 **目标: 验证数据集质量和基线性能**")
    print("=" * 70)
    
    # Device setup
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  使用设备: {device}")
    
    # Dataset setup
    dataset_path = "high_quality_f3_dataset.npz"
    
    if not Path(dataset_path).exists():
        print(f"❌ 数据集文件不存在: {dataset_path}")
        return None
    
    # Create datasets with conservative point count
    num_points = 8192  # Conservative point count
    train_dataset = ConservativeF3Dataset(dataset_path, split='train', 
                                        num_points=num_points, augment=True)
    val_dataset = ConservativeF3Dataset(dataset_path, split='val', 
                                      num_points=num_points, augment=False)
    test_dataset = ConservativeF3Dataset(dataset_path, split='test', 
                                       num_points=num_points, augment=False)
    
    # Data loaders
    batch_size = 4  # Conservative batch size
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=2)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=2)
    
    print(f"📊 数据集统计:")
    print(f"   训练样本: {len(train_dataset)}")
    print(f"   验证样本: {len(val_dataset)}")
    print(f"   测试样本: {len(test_dataset)}")
    print(f"   批量大小: {batch_size}")
    print(f"   点云大小: {num_points:,} 点")
    print(f"   关键点数: 19 个F3关键点")
    
    # Model setup
    model = SimplePointNet(num_keypoints=19, num_points=num_points).to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    print(f"\n🧠 保守PointNet模型:")
    print(f"   总参数: {total_params:,}")
    print(f"   模型大小: {total_params * 4 / (1024 * 1024):.1f}MB")
    
    # Training setup
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.8, patience=8, min_lr=1e-6, verbose=True
    )
    
    # Training configuration
    num_epochs = 100
    best_val_error = float('inf')
    patience_counter = 0
    patience = 15
    
    # Training history
    training_history = []
    
    print(f"\n🎯 开始训练 (目标: 验证数据集质量)")
    print(f"📈 训练轮数: {num_epochs}, 早停耐心: {patience}")
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        print("-" * 50)
        
        # Training phase
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_1mm_percent': 0, 'within_5mm_percent': 0}
        
        for batch_idx, batch in enumerate(train_loader):
            point_cloud = batch['point_cloud'].to(device)  # (batch_size, num_points, 3)
            keypoints = batch['keypoints'].to(device)      # (batch_size, 19, 3)
            
            optimizer.zero_grad()
            
            # Forward pass
            pred_keypoints = model(point_cloud)  # (batch_size, 19, 3)
            
            # Calculate loss
            loss = criterion(pred_keypoints, keypoints)
            
            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            
            # Calculate metrics
            with torch.no_grad():
                metrics = calculate_metrics(pred_keypoints, keypoints)
                for key in train_metrics:
                    train_metrics[key] += metrics[key]
        
        # Average training metrics
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_1mm_percent': 0, 'within_5mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                val_loss += loss.item()
                
                metrics = calculate_metrics(pred_keypoints, keypoints)
                for key in val_metrics:
                    val_metrics[key] += metrics[key]
        
        # Average validation metrics
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # Learning rate scheduling
        scheduler.step(val_metrics['mean_distance'])
        
        # Print epoch results
        print(f"📊 训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"1mm精度={train_metrics['within_1mm_percent']:.1f}%")
        print(f"📊 验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"1mm精度={val_metrics['within_1mm_percent']:.1f}%")
        
        # Save training history
        epoch_data = {
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'learning_rate': optimizer.param_groups[0]['lr']
        }
        training_history.append(epoch_data)
        
        # Check for improvement
        current_val_error = val_metrics['mean_distance']
        if current_val_error < best_val_error:
            best_val_error = current_val_error
            patience_counter = 0
            
            # Save best model
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_error': best_val_error,
                'val_metrics': val_metrics
            }, 'best_conservative_pointnet_f3.pth')
            
            print(f"🎉 新的最佳模型! 验证误差: {best_val_error:.3f}mm")
        else:
            patience_counter += 1
            print(f"⏳ 无改善 ({patience_counter}/{patience})")
        
        # Early stopping
        if patience_counter >= patience:
            print(f"🛑 早停触发! 最佳验证误差: {best_val_error:.3f}mm")
            break
    
    total_time = time.time() - start_time
    
    print(f"\n🎯 保守PointNet训练完成!")
    print(f"   训练时间: {total_time/60:.1f} 分钟")
    print(f"   最佳验证误差: {best_val_error:.3f}mm")
    
    return best_val_error, total_time

if __name__ == "__main__":
    try:
        best_error, training_time = train_conservative_pointnet()
        
        print(f"\n🎉 **保守PointNet训练成功完成!**")
        print(f"🎯 最终结果: {best_error:.3f}mm 误差")
        print(f"⏱️ 训练时间: {training_time/60:.1f} 分钟")
        
        # Assessment
        if best_error <= 1.0:
            print(f"🏆 **优秀结果!** 达到医疗级精度 (<1mm)")
        elif best_error <= 5.0:
            print(f"✅ **良好结果!** 达到医疗可用精度 (<5mm)")
        else:
            print(f"⚠️ **基线结果** 需要进一步优化")
        
        print(f"\n💡 **数据集质量验证**: 模型能够训练并收敛，证明数据集质量良好")
        
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        import traceback
        traceback.print_exc()
