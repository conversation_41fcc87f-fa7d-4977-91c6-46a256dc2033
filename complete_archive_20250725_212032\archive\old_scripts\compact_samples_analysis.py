#!/usr/bin/env python3
"""
紧凑型样本分析
检查是否还有其他类似600051的"紧凑型"样本
"""

import numpy as np
import matplotlib.pyplot as plt

def find_compact_samples():
    """寻找紧凑型样本"""
    
    print("🔍 **寻找紧凑型样本**")
    print("检查是否还有其他类似600051的样本")
    print("=" * 80)
    
    # 加载数据
    data = np.load('f3_reduced_12kp_stable.npz', allow_pickle=True)
    sample_ids = data['sample_ids']
    keypoints = data['keypoints']
    
    # 计算所有样本的紧凑性指标
    compact_metrics = []
    
    for i, (sid, kp) in enumerate(zip(sample_ids, keypoints)):
        # 计算关键点间平均距离
        distances = []
        for j in range(12):
            for k in range(j+1, 12):
                dist = np.linalg.norm(kp[j] - kp[k])
                distances.append(dist)
        
        avg_distance = np.mean(distances)
        
        # 计算体积估计
        kp_center = np.mean(kp, axis=0)
        kp_centered = kp - kp_center
        ranges = np.max(kp_centered, axis=0) - np.min(kp_centered, axis=0)
        volume_estimate = np.prod(ranges)
        
        # 计算长宽比
        from sklearn.decomposition import PCA
        pca = PCA()
        pca.fit(kp_centered)
        explained_variance = pca.explained_variance_
        aspect_ratio = explained_variance[0] / explained_variance[1] if explained_variance[1] > 0 else 0
        
        compact_metrics.append({
            'sample_id': sid,
            'index': i,
            'avg_distance': avg_distance,
            'volume_estimate': volume_estimate,
            'aspect_ratio': aspect_ratio,
            'ranges': ranges
        })
    
    # 按平均距离排序（从小到大）
    compact_metrics.sort(key=lambda x: x['avg_distance'])
    
    print(f"📊 **最紧凑的10个样本** (按平均关键点距离排序):")
    print(f"{'排名':<4} {'样本ID':<8} {'平均距离':<10} {'体积估计':<12} {'长宽比':<8}")
    print("-" * 50)
    
    for i, metric in enumerate(compact_metrics[:10]):
        print(f"{i+1:<4} {metric['sample_id']:<8} {metric['avg_distance']:<10.2f} "
              f"{metric['volume_estimate']:<12.0f} {metric['aspect_ratio']:<8.2f}")
    
    # 检查600051的排名
    target_rank = None
    for i, metric in enumerate(compact_metrics):
        if metric['sample_id'] == '600051':
            target_rank = i + 1
            break
    
    print(f"\n🎯 **600051样本排名**: 第{target_rank}位 (共{len(compact_metrics)}个样本)")
    
    # 分析紧凑型样本的特征
    print(f"\n📈 **紧凑型样本特征分析**:")
    
    # 前10%最紧凑的样本
    top_10_percent = int(len(compact_metrics) * 0.1)
    compact_samples = compact_metrics[:top_10_percent]
    
    compact_ids = [s['sample_id'] for s in compact_samples]
    compact_distances = [s['avg_distance'] for s in compact_samples]
    compact_volumes = [s['volume_estimate'] for s in compact_samples]
    compact_ratios = [s['aspect_ratio'] for s in compact_samples]
    
    print(f"   前10%紧凑样本 ({len(compact_samples)}个): {compact_ids}")
    print(f"   平均距离范围: {min(compact_distances):.2f} - {max(compact_distances):.2f}mm")
    print(f"   体积范围: {min(compact_volumes):.0f} - {max(compact_volumes):.0f}")
    print(f"   长宽比范围: {min(compact_ratios):.2f} - {max(compact_ratios):.2f}")
    
    # 检查600051是否在紧凑型样本中
    is_600051_compact = '600051' in compact_ids
    print(f"   600051是否属于紧凑型: {'是' if is_600051_compact else '否'}")
    
    return compact_metrics, compact_samples

def analyze_compact_vs_normal():
    """分析紧凑型vs正常样本"""
    
    compact_metrics, compact_samples = find_compact_samples()
    
    print(f"\n📊 **紧凑型 vs 正常样本对比**:")
    
    # 分离紧凑型和正常样本
    compact_ids = [s['sample_id'] for s in compact_samples]
    
    compact_group = []
    normal_group = []
    
    for metric in compact_metrics:
        if metric['sample_id'] in compact_ids:
            compact_group.append(metric)
        else:
            normal_group.append(metric)
    
    # 统计对比
    compact_avg_dist = np.mean([s['avg_distance'] for s in compact_group])
    normal_avg_dist = np.mean([s['avg_distance'] for s in normal_group])
    
    compact_avg_vol = np.mean([s['volume_estimate'] for s in compact_group])
    normal_avg_vol = np.mean([s['volume_estimate'] for s in normal_group])
    
    compact_avg_ratio = np.mean([s['aspect_ratio'] for s in compact_group])
    normal_avg_ratio = np.mean([s['aspect_ratio'] for s in normal_group])
    
    print(f"   紧凑型样本 ({len(compact_group)}个):")
    print(f"     平均距离: {compact_avg_dist:.2f}mm")
    print(f"     平均体积: {compact_avg_vol:.0f}")
    print(f"     平均长宽比: {compact_avg_ratio:.2f}")
    
    print(f"   正常样本 ({len(normal_group)}个):")
    print(f"     平均距离: {normal_avg_dist:.2f}mm")
    print(f"     平均体积: {normal_avg_vol:.0f}")
    print(f"     平均长宽比: {normal_avg_ratio:.2f}")
    
    print(f"   差异:")
    print(f"     距离差异: {(normal_avg_dist - compact_avg_dist)/normal_avg_dist*100:.1f}%")
    print(f"     体积差异: {(normal_avg_vol - compact_avg_vol)/normal_avg_vol*100:.1f}%")
    print(f"     长宽比差异: {(normal_avg_ratio - compact_avg_ratio)/normal_avg_ratio*100:.1f}%")

def check_medical_validity():
    """检查医学合理性"""
    
    print(f"\n🏥 **医学合理性检查**:")
    
    compact_metrics, compact_samples = find_compact_samples()
    
    # 检查紧凑型样本是否可能是正常变异
    print(f"   💡 **可能的解释**:")
    
    explanations = [
        "个体差异: 不同患者的骨盆大小和形状确实存在差异",
        "年龄因素: 年轻患者可能骨盆相对更紧凑",
        "性别差异: 男性和女性骨盆形状存在显著差异",
        "病理状态: 某些疾病可能导致骨盆形状改变",
        "扫描角度: 不同的扫描体位可能影响测量结果",
        "标注一致性: 标注者对关键点的理解可能存在差异"
    ]
    
    for i, explanation in enumerate(explanations, 1):
        print(f"     {i}. {explanation}")
    
    print(f"\n   🎯 **建议验证方法**:")
    
    verification_methods = [
        "回溯原始DICOM/STL文件，检查数据处理流程",
        "请医学专家审核紧凑型样本的解剖合理性",
        "检查这些样本的患者信息（年龄、性别、诊断）",
        "对比不同标注者对同一样本的标注结果",
        "使用不同的关键点检测算法进行交叉验证"
    ]
    
    for i, method in enumerate(verification_methods, 1):
        print(f"     {i}. {method}")

def create_decision_framework():
    """创建决策框架"""
    
    print(f"\n🎯 **600051样本处理决策框架**:")
    
    decision_tree = {
        "情况1: 医学专家确认正常": {
            "行动": "保留样本，标记为'紧凑型正常变异'",
            "理由": "专业医学判断优于统计异常检测",
            "风险": "低 - 增加数据多样性"
        },
        
        "情况2: 发现数据处理问题": {
            "行动": "修复数据处理流程，重新生成样本",
            "理由": "消除技术性错误",
            "风险": "无 - 提升数据质量"
        },
        
        "情况3: 确认为异常样本": {
            "行动": "移除样本",
            "理由": "避免误导模型训练",
            "风险": "中 - 可能丢失有价值信息"
        },
        
        "情况4: 无法确定": {
            "行动": "A/B测试 - 分别训练包含和不包含该样本的模型",
            "理由": "通过实验验证影响",
            "风险": "低 - 基于实证决策"
        }
    }
    
    for situation, details in decision_tree.items():
        print(f"\n   {situation}:")
        print(f"     行动: {details['行动']}")
        print(f"     理由: {details['理由']}")
        print(f"     风险: {details['风险']}")
    
    print(f"\n💡 **当前建议**: 采用情况4的A/B测试方法")
    print(f"   1. 训练两个模型: 包含600051 vs 不包含600051")
    print(f"   2. 对比性能差异")
    print(f"   3. 基于实际效果做决定")

def main():
    """主函数"""
    
    print("🔍 **紧凑型样本分析**")
    print("🎯 **目标: 理解600051样本在整体数据中的位置**")
    print("=" * 80)
    
    # 寻找紧凑型样本
    compact_metrics, compact_samples = find_compact_samples()
    
    # 对比分析
    analyze_compact_vs_normal()
    
    # 医学合理性检查
    check_medical_validity()
    
    # 决策框架
    create_decision_framework()
    
    print(f"\n🎉 **分析总结**:")
    print(f"✅ 600051确实是统计上的异常样本")
    print(f"✅ 但可能属于正常的生物学变异")
    print(f"✅ 建议通过A/B测试验证实际影响")
    print(f"✅ 您的专业判断仍然很重要!")
    
    print(f"\n🚀 **下一步行动**:")
    print(f"   1. 可以先保留600051，继续数据扩展工作")
    print(f"   2. 在有更多数据后，重新评估异常样本")
    print(f"   3. 通过实际训练效果验证处理策略")

if __name__ == "__main__":
    main()
