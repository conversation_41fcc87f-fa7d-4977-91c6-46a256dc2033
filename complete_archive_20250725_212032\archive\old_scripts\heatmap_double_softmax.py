#!/usr/bin/env python3
"""
热图回归 + 双Softmax组合方案
结合热图回归的稳定性和双Softmax的精细化能力
基于5.829mm成功配置进行改进
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import time
import json
import gc
import random
from torch.utils.data import Dataset, DataLoader
import torch.optim as optim

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class HeatmapGenerator(nn.Module):
    """
    热图生成器
    为每个关键点生成3D热图
    """
    
    def __init__(self, feature_dim=1024, num_keypoints=12, sigma=2.0):
        super(HeatmapGenerator, self).__init__()
        
        self.num_keypoints = num_keypoints
        self.sigma = sigma
        
        # 为每个关键点生成热图的网络
        self.heatmap_heads = nn.ModuleList([
            nn.Sequential(
                nn.Linear(feature_dim, 256),
                nn.BatchNorm1d(256),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(256, 128),
                nn.BatchNorm1d(128),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(128, 1)  # 每个点的热图值
            ) for _ in range(num_keypoints)
        ])
        
        print(f"🔥 热图生成器: {num_keypoints}个关键点")
        print(f"   高斯标准差: {sigma}mm")
    
    def generate_target_heatmap(self, points, keypoint_pos, sigma):
        """
        生成目标高斯热图
        
        Args:
            points: [num_points, 3] 点云坐标
            keypoint_pos: [3] 关键点位置
            sigma: 高斯标准差
            
        Returns:
            heatmap: [num_points] 高斯热图值
        """
        distances = torch.norm(points - keypoint_pos.unsqueeze(0), dim=1)
        heatmap = torch.exp(-distances**2 / (2 * sigma**2))
        return heatmap
    
    def forward(self, global_features, points, target_keypoints=None):
        """
        生成热图
        
        Args:
            global_features: [batch_size, feature_dim] 全局特征
            points: [batch_size, num_points, 3] 点云坐标
            target_keypoints: [batch_size, num_keypoints, 3] 目标关键点 (训练时)
            
        Returns:
            predicted_heatmaps: [batch_size, num_keypoints, num_points] 预测热图
            target_heatmaps: [batch_size, num_keypoints, num_points] 目标热图 (训练时)
        """
        batch_size, num_points, _ = points.shape
        
        # 为每个关键点生成预测热图
        predicted_heatmaps = []
        target_heatmaps = []
        
        for k in range(self.num_keypoints):
            # 预测热图
            heatmap_logits = self.heatmap_heads[k](global_features)  # [batch_size, 1]
            
            # 扩展到所有点
            batch_heatmaps = []
            batch_targets = []
            
            for b in range(batch_size):
                # 简化的热图预测：基于全局特征为每个点分配权重
                # 这里可以改进为更复杂的点-特征交互
                point_features = points[b]  # [num_points, 3]
                
                # 使用点的坐标特征生成热图
                # 简化版：基于点到原点的距离
                distances_to_origin = torch.norm(point_features, dim=1)  # [num_points]
                base_heatmap = torch.exp(-distances_to_origin / 10.0)  # 简单的距离衰减
                
                # 用网络预测的权重调制
                weight = torch.sigmoid(heatmap_logits[b, 0])
                predicted_heatmap = base_heatmap * weight
                predicted_heatmap = F.softmax(predicted_heatmap, dim=0)  # 归一化为概率分布
                
                batch_heatmaps.append(predicted_heatmap)
                
                # 生成目标热图 (训练时)
                if target_keypoints is not None:
                    target_heatmap = self.generate_target_heatmap(
                        point_features, target_keypoints[b, k], self.sigma
                    )
                    target_heatmap = F.softmax(target_heatmap, dim=0)  # 归一化
                    batch_targets.append(target_heatmap)
            
            predicted_heatmaps.append(torch.stack(batch_heatmaps))
            if target_keypoints is not None:
                target_heatmaps.append(torch.stack(batch_targets))
        
        predicted_heatmaps = torch.stack(predicted_heatmaps, dim=1)  # [batch_size, num_keypoints, num_points]
        
        if target_keypoints is not None:
            target_heatmaps = torch.stack(target_heatmaps, dim=1)  # [batch_size, num_keypoints, num_points]
            return predicted_heatmaps, target_heatmaps
        else:
            return predicted_heatmaps, None

class EnhancedDoubleSoftMax(nn.Module):
    """
    增强版双Softmax机制
    基于5.829mm成功配置的改进版本
    """
    
    def __init__(self, threshold_ratio=0.15, temperature=2.0, weight_ratio=0.8):
        super(EnhancedDoubleSoftMax, self).__init__()
        
        self.threshold_ratio = threshold_ratio
        self.temperature = temperature
        self.weight_ratio = weight_ratio
        
        # 改进的权重计算网络
        self.weight_net = nn.Sequential(
            nn.Linear(3, 64),
            nn.ReLU(),
            nn.BatchNorm1d(64),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.BatchNorm1d(32),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 1)
        )
        
        print(f"🎯 增强双Softmax机制:")
        print(f"   阈值比例: {threshold_ratio}")
        print(f"   温度参数: {temperature}")
        print(f"   权重比例: {weight_ratio}:{1-weight_ratio}")
    
    def forward(self, points, predicted_keypoint, heatmap_weights=None):
        """
        增强的双Softmax权重计算
        
        Args:
            points: [num_points, 3] 候选点
            predicted_keypoint: [3] 预测的关键点
            heatmap_weights: [num_points] 热图权重 (可选)
            
        Returns:
            refined_keypoint: [3] 精细化后的关键点
        """
        # 计算相对位置
        relative_pos = points - predicted_keypoint.unsqueeze(0)
        
        # 第一个Softmax - 基于距离的权重
        distances = torch.norm(relative_pos, dim=1)
        distance_weights = F.softmax(-distances**2 / (2 * self.temperature**2), dim=0)
        
        # 第二个Softmax - 基于神经网络的权重
        if len(relative_pos) > 1:
            nn_weights = self.weight_net(relative_pos).squeeze(-1)
            nn_weights = F.softmax(nn_weights / self.temperature, dim=0)
        else:
            nn_weights = torch.ones_like(distance_weights)
        
        # 第三个权重 - 热图权重 (如果提供)
        if heatmap_weights is not None:
            heatmap_weights = F.softmax(heatmap_weights, dim=0)
            # 三重权重组合
            combined_weights = (self.weight_ratio * distance_weights + 
                              (1 - self.weight_ratio) * 0.5 * nn_weights +
                              (1 - self.weight_ratio) * 0.5 * heatmap_weights)
        else:
            # 双重权重组合
            combined_weights = self.weight_ratio * distance_weights + (1 - self.weight_ratio) * nn_weights
        
        # 改进的阈值过滤
        threshold = torch.quantile(combined_weights, 1 - self.threshold_ratio)
        filter_mask = combined_weights >= threshold
        
        # 确保至少保留一些点
        if filter_mask.sum() < 3:
            _, top_indices = torch.topk(combined_weights, min(5, len(combined_weights)))
            filter_mask = torch.zeros_like(combined_weights, dtype=torch.bool)
            filter_mask[top_indices] = True
        
        # 重新归一化
        filtered_weights = combined_weights * filter_mask.float()
        sum_weights = torch.sum(filtered_weights)
        
        if sum_weights > 1e-8:
            final_weights = filtered_weights / sum_weights
        else:
            final_weights = combined_weights / combined_weights.sum()
        
        # 加权平均得到精细化关键点
        refined_keypoint = torch.sum(final_weights.unsqueeze(-1) * points, dim=0)
        
        return refined_keypoint

class HeatmapDoubleSoftMaxPointNet(nn.Module):
    """
    热图回归 + 双Softmax PointNet
    结合热图回归的稳定性和双Softmax的精细化能力
    """
    
    def __init__(self, num_keypoints=12, dropout_rate=0.3):
        super(HeatmapDoubleSoftMaxPointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 基线架构 (保持成功配置)
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        # 热图生成器
        self.heatmap_generator = HeatmapGenerator(
            feature_dim=1024,
            num_keypoints=num_keypoints,
            sigma=2.0
        )
        
        # 双Softmax精细化
        self.double_softmax = EnhancedDoubleSoftMax(
            threshold_ratio=0.15,
            temperature=2.0,
            weight_ratio=0.8
        )
        
        self.dropout = nn.Dropout(dropout_rate)
        
        print(f"🧠 热图+双Softmax PointNet: {num_keypoints}个关键点")
        print(f"   - 基线架构 + 热图回归 + 双Softmax精细化")
        print(f"   - 多阶段优化: 热图→坐标→精细化")
        
    def forward(self, x, target_keypoints=None):
        batch_size = x.size(0)
        points_original = x.clone()  # 保存原始点云坐标
        x = x.transpose(2, 1)  # [batch, 3, num_points]
        
        # 基线特征提取
        x1 = torch.relu(self.bn1(self.conv1(x)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        
        x3_res = x3 + self.residual1(x1)
        
        x4 = torch.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = torch.relu(self.bn5(self.conv5(x4_res)))
        
        # 全局特征
        global_feat = torch.max(x5, 2)[0]  # [batch, 1024]
        
        # 1. 热图回归阶段
        predicted_heatmaps, target_heatmaps = self.heatmap_generator(
            global_feat, points_original, target_keypoints
        )
        
        # 2. 从热图预测初始关键点坐标
        initial_keypoints = []
        
        for b in range(batch_size):
            batch_keypoints = []
            for k in range(self.num_keypoints):
                heatmap = predicted_heatmaps[b, k]  # [num_points]
                # 加权平均得到初始关键点
                initial_kp = torch.sum(heatmap.unsqueeze(-1) * points_original[b], dim=0)
                batch_keypoints.append(initial_kp)
            initial_keypoints.append(torch.stack(batch_keypoints))
        
        initial_keypoints = torch.stack(initial_keypoints)
        
        # 3. 双Softmax精细化阶段 (仅在推理时)
        if not self.training:
            refined_keypoints = []
            
            for b in range(batch_size):
                batch_refined = []
                for k in range(self.num_keypoints):
                    initial_kp = initial_keypoints[b, k]
                    heatmap_weights = predicted_heatmaps[b, k]
                    
                    # 选择候选点
                    distances = torch.norm(points_original[b] - initial_kp.unsqueeze(0), dim=1)
                    K = min(256, points_original.shape[1])
                    _, nearest_indices = torch.topk(distances, K, largest=False)
                    
                    candidate_points = points_original[b, nearest_indices]
                    candidate_heatmap_weights = heatmap_weights[nearest_indices]
                    
                    # 双Softmax精细化
                    refined_kp = self.double_softmax(
                        candidate_points, initial_kp, candidate_heatmap_weights
                    )
                    batch_refined.append(refined_kp)
                
                refined_keypoints.append(torch.stack(batch_refined))
            
            refined_keypoints = torch.stack(refined_keypoints)
            return refined_keypoints, predicted_heatmaps, target_heatmaps
        else:
            return initial_keypoints, predicted_heatmaps, target_heatmaps

class HeatmapDoubleSoftMaxLoss(nn.Module):
    """
    热图+双Softmax组合损失函数
    """
    
    def __init__(self, lambda_coord=1.0, lambda_heatmap=0.5):
        super(HeatmapDoubleSoftMaxLoss, self).__init__()
        
        self.lambda_coord = lambda_coord
        self.lambda_heatmap = lambda_heatmap
        
        self.mse_loss = nn.MSELoss()
        self.kl_loss = nn.KLDivLoss(reduction='batchmean')
        
    def forward(self, pred_keypoints, target_keypoints, pred_heatmaps, target_heatmaps):
        """
        计算组合损失
        
        Args:
            pred_keypoints: [batch_size, num_keypoints, 3] 预测关键点
            target_keypoints: [batch_size, num_keypoints, 3] 目标关键点
            pred_heatmaps: [batch_size, num_keypoints, num_points] 预测热图
            target_heatmaps: [batch_size, num_keypoints, num_points] 目标热图
        """
        
        # 1. 关键点坐标损失
        coord_loss = self.mse_loss(pred_keypoints, target_keypoints)
        
        # 2. 热图损失
        heatmap_loss = 0.0
        if target_heatmaps is not None:
            # 使用KL散度损失，因为热图是概率分布
            batch_size, num_keypoints, num_points = pred_heatmaps.shape
            
            for b in range(batch_size):
                for k in range(num_keypoints):
                    pred_log_prob = torch.log(pred_heatmaps[b, k] + 1e-8)
                    target_prob = target_heatmaps[b, k]
                    heatmap_loss += self.kl_loss(pred_log_prob.unsqueeze(0), target_prob.unsqueeze(0))
            
            heatmap_loss /= (batch_size * num_keypoints)
        
        # 总损失
        total_loss = self.lambda_coord * coord_loss + self.lambda_heatmap * heatmap_loss
        
        return {
            'total_loss': total_loss,
            'coord_loss': coord_loss,
            'heatmap_loss': heatmap_loss
        }

def test_heatmap_double_softmax():
    """测试热图+双Softmax模型"""
    
    print("🧪 **测试热图+双Softmax模型**")
    print("🎯 **结合热图回归稳定性和双Softmax精细化能力**")
    print("=" * 80)
    
    batch_size = 4
    num_points = 4096
    num_keypoints = 12
    
    # 创建测试数据
    test_input = torch.randn(batch_size, num_points, 3)
    target_keypoints = torch.randn(batch_size, num_keypoints, 3)
    
    print(f"📊 测试输入: {test_input.shape}")
    print(f"📊 目标关键点: {target_keypoints.shape}")
    
    # 测试模型
    print(f"\n🔍 测试热图+双Softmax PointNet:")
    model = HeatmapDoubleSoftMaxPointNet(num_keypoints=num_keypoints)
    
    with torch.no_grad():
        # 训练模式
        model.train()
        pred_kp_train, pred_heatmaps, target_heatmaps = model(test_input, target_keypoints)
        print(f"   训练模式 - 预测关键点: {pred_kp_train.shape}")
        print(f"   训练模式 - 预测热图: {pred_heatmaps.shape}")
        print(f"   训练模式 - 目标热图: {target_heatmaps.shape}")
        
        # 推理模式
        model.eval()
        pred_kp_eval, pred_heatmaps_eval, _ = model(test_input)
        print(f"   推理模式 - 精细化关键点: {pred_kp_eval.shape}")
        print(f"   推理模式 - 预测热图: {pred_heatmaps_eval.shape}")
    
    # 测试损失函数
    print(f"\n🔍 测试组合损失函数:")
    criterion = HeatmapDoubleSoftMaxLoss(lambda_coord=1.0, lambda_heatmap=0.5)
    
    loss_dict = criterion(
        pred_kp_train, target_keypoints,
        pred_heatmaps, target_heatmaps
    )
    
    print(f"   总损失: {loss_dict['total_loss']:.4f}")
    print(f"   坐标损失: {loss_dict['coord_loss']:.4f}")
    print(f"   热图损失: {loss_dict['heatmap_loss']:.4f}")
    
    # 参数统计
    total_params = sum(p.numel() for p in model.parameters())
    print(f"\n📊 模型参数: {total_params:,}")
    
    print(f"\n✅ 热图+双Softmax模型测试通过!")
    
    return model, criterion

if __name__ == "__main__":
    set_seed(42)
    
    print("🚀 **热图回归 + 双Softmax组合方案**")
    print("📚 **结合热图回归稳定性和双Softmax精细化能力**")
    print("🎯 **基于5.829mm成功配置进行改进**")
    print("=" * 80)
    
    # 测试模型
    model, criterion = test_heatmap_double_softmax()
    
    print(f"\n🎉 **热图+双Softmax方案准备完成!**")
    print("=" * 50)
    print(f"🔬 核心创新: 热图回归 + 双Softmax精细化")
    print(f"🎯 当前基线: 5.829mm (集成双Softmax)")
    print(f"🏆 目标精度: 5.0mm")
    print(f"📈 预期改进: 多阶段优化带来更高精度")
    
    print(f"\n💡 **方法优势**:")
    print(f"   1. 热图回归: 更稳定的训练过程")
    print(f"   2. 双Softmax: 精细化定位能力")
    print(f"   3. 多阶段: 粗到细的优化策略")
    print(f"   4. 组合损失: 同时优化热图和坐标")
