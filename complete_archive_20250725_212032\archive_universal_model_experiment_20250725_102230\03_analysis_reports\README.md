# Medical Point Cloud Keypoint Detection

## 项目概述
医疗点云关键点检测项目，目标是实现5mm医疗级精度的关键点定位。

## 目录结构
```
├── experiments/                 # 实验相关文件
│   ├── baseline_models/        # 基线模型
│   ├── phase1_improvements/    # Phase 1改进模型
│   ├── phase2_architectures/   # Phase 2架构改进
│   ├── results/               # 实验结果JSON文件
│   ├── configs/               # 配置文件
│   └── logs/                  # 训练日志
├── models/                     # 模型文件
│   ├── best_models/           # 最佳模型
│   └── checkpoints/           # 检查点
├── data/                      # 数据文件
│   ├── processed/             # 处理后的数据
│   └── raw/                   # 原始数据
└── *.py                       # 训练和工具脚本
```

## 实验进展

### ✅ 基线模型 (当前最佳)
- **模型**: 12关键点 + 稳定性选择
- **性能**: 6.208mm验证误差
- **改进**: 相比19关键点提升27.4%
- **文件**: `experiments/baseline_models/best_reduced_12kp_f3.pth`

### ❌ Phase 1: 损失函数优化
- **尝试**: Wing Loss, Focal Loss, 超参数调优
- **最佳**: 6.458mm (超参数调优)
- **结论**: 复杂损失函数在小数据集上效果有限

### ✅ Phase 2: 架构改进
- **最佳**: Feature Pyramid PointNet (6.384mm)
- **发现**: 多层特征融合有效，注意力机制过拟合

## 下一步计划
1. 在基线模型上应用双Softmax机制
2. 优化Feature Pyramid PointNet
3. 数据质量改进
4. 目标：突破6mm，达到5.5mm医疗级精度

## 使用方法
```bash
# 训练基线模型
python train_reduced_keypoints_f3.py

# Phase 2架构对比
python train_phase2_attention.py

# 整理文件结构
python organize_models.py
```
