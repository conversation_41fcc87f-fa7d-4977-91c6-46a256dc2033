#!/usr/bin/env python3
"""
模型失败原因实验验证
通过对比实验验证为什么复杂模型在这个数据集上失败
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import time
import json
from torch.utils.data import Dataset, DataLoader

def set_seed(seed=42):
    torch.manual_seed(seed)
    np.random.seed(seed)

class SimpleDataset(Dataset):
    """简化数据集"""
    
    def __init__(self, data_path, split='train', num_points=1024):
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        test_samples = ['600114', '600115', '600116', '600117', '600118', 
                       '600119', '600120', '600121', '600122', '600123',
                       '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        
        if split == 'test':
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
        else:
            train_val_data = point_clouds[~test_mask]
            train_val_kps = keypoints[~test_mask]
            
            val_size = int(0.2 * len(train_val_data))
            if split == 'train':
                self.point_clouds = train_val_data[val_size:]
                self.keypoints = train_val_kps[val_size:]
            else:  # val
                self.point_clouds = train_val_data[:val_size]
                self.keypoints = train_val_kps[:val_size]
        
        self.num_points = num_points
        print(f"{split}集: {len(self.point_clouds)}个样本")
    
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        pc = self.point_clouds[idx]
        kp = self.keypoints[idx]
        
        # 随机采样点
        if len(pc) > self.num_points:
            indices = np.random.choice(len(pc), self.num_points, replace=False)
            pc = pc[indices]
        
        return torch.FloatTensor(pc), torch.FloatTensor(kp)

class VerySimpleModel(nn.Module):
    """极简模型 - 参数量匹配数据集规模"""
    
    def __init__(self, num_keypoints=12):
        super(VerySimpleModel, self).__init__()
        
        # 极简特征提取
        self.conv1 = nn.Conv1d(3, 32, 1)
        self.conv2 = nn.Conv1d(32, 64, 1)
        
        # 简单预测头
        self.fc = nn.Linear(64, num_keypoints * 3)
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"极简模型参数量: {total_params:,}")
    
    def forward(self, x):
        x = x.transpose(2, 1)  # [B, 3, N]
        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))
        x = torch.max(x, 2)[0]  # 全局最大池化
        x = self.fc(x)
        return x.view(-1, 12, 3)

class ModerateModel(nn.Module):
    """适中模型 - 平衡复杂度"""
    
    def __init__(self, num_keypoints=12):
        super(ModerateModel, self).__init__()
        
        # 适中特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        
        # 适中预测头
        self.fc1 = nn.Linear(256, 128)
        self.fc2 = nn.Linear(128, num_keypoints * 3)
        self.dropout = nn.Dropout(0.3)
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"适中模型参数量: {total_params:,}")
    
    def forward(self, x):
        x = x.transpose(2, 1)  # [B, 3, N]
        x = torch.relu(self.bn1(self.conv1(x)))
        x = torch.relu(self.bn2(self.conv2(x)))
        x = torch.relu(self.bn3(self.conv3(x)))
        x = torch.max(x, 2)[0]  # 全局最大池化
        x = torch.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.fc2(x)
        return x.view(-1, 12, 3)

class ComplexModel(nn.Module):
    """复杂模型 - 模拟过参数化"""
    
    def __init__(self, num_keypoints=12):
        super(ComplexModel, self).__init__()
        
        # 复杂特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 复杂预测头
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, num_keypoints * 3)
        self.dropout = nn.Dropout(0.3)
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"复杂模型参数量: {total_params:,}")
    
    def forward(self, x):
        x = x.transpose(2, 1)  # [B, 3, N]
        x = torch.relu(self.bn1(self.conv1(x)))
        x = torch.relu(self.bn2(self.conv2(x)))
        x = torch.relu(self.bn3(self.conv3(x)))
        x = torch.relu(self.bn4(self.conv4(x)))
        x = torch.relu(self.bn5(self.conv5(x)))
        x = torch.max(x, 2)[0]  # 全局最大池化
        x = torch.relu(self.fc1(x))
        x = self.dropout(x)
        x = torch.relu(self.fc2(x))
        x = self.dropout(x)
        x = torch.relu(self.fc3(x))
        x = self.dropout(x)
        x = self.fc4(x)
        return x.view(-1, 12, 3)

def calculate_baseline_performance(test_loader):
    """计算统计基线性能"""
    print("\n📊 计算统计基线性能...")
    
    all_keypoints = []
    for pc, kp in test_loader:
        all_keypoints.append(kp.numpy())
    
    all_keypoints = np.concatenate(all_keypoints, axis=0)
    mean_keypoints = np.mean(all_keypoints, axis=0)
    
    # 计算误差
    errors = []
    for kp in all_keypoints:
        diff = np.linalg.norm(kp - mean_keypoints, axis=1)
        errors.append(np.mean(diff))
    
    baseline_error = np.mean(errors)
    print(f"统计基线误差: {baseline_error:.3f}mm")
    
    return baseline_error, mean_keypoints

def train_model(model, train_loader, val_loader, device, epochs=50, lr=0.001):
    """训练模型"""
    model.to(device)
    optimizer = optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
    criterion = nn.MSELoss()
    
    best_val_loss = float('inf')
    train_losses = []
    val_losses = []
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0.0
        for pc, kp in train_loader:
            pc, kp = pc.to(device), kp.to(device)
            
            optimizer.zero_grad()
            pred = model(pc)
            loss = criterion(pred, kp)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # 验证
        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            for pc, kp in val_loader:
                pc, kp = pc.to(device), kp.to(device)
                pred = model(pc)
                loss = criterion(pred, kp)
                val_loss += loss.item()
        
        val_loss /= len(val_loader)
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        
        if val_loss < best_val_loss:
            best_val_loss = val_loss
        
        if epoch % 10 == 0:
            print(f"Epoch {epoch}: Train={train_loss:.4f}, Val={val_loss:.4f}")
    
    return best_val_loss, train_losses, val_losses

def test_model(model, test_loader, device):
    """测试模型"""
    model.eval()
    errors = []
    
    with torch.no_grad():
        for pc, kp in test_loader:
            pc, kp = pc.to(device), kp.to(device)
            pred = model(pc)
            
            # 计算误差
            diff = torch.norm(pred - kp, dim=2)
            avg_error = torch.mean(diff, dim=1)
            errors.extend(avg_error.cpu().numpy())
    
    return np.mean(errors)

def overfitting_analysis(train_losses, val_losses):
    """过拟合分析"""
    min_val_idx = np.argmin(val_losses)
    min_val_loss = val_losses[min_val_idx]
    final_train_loss = train_losses[-1]
    final_val_loss = val_losses[-1]
    
    overfitting_score = (final_val_loss - min_val_loss) / min_val_loss
    generalization_gap = final_val_loss - final_train_loss
    
    return {
        'min_val_loss': min_val_loss,
        'final_train_loss': final_train_loss,
        'final_val_loss': final_val_loss,
        'overfitting_score': overfitting_score,
        'generalization_gap': generalization_gap
    }

def main():
    """主实验"""
    print("🔬 **模型失败原因实验验证**")
    print("🎯 **对比不同复杂度模型的表现**")
    print("=" * 60)
    
    set_seed(42)
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    
    # 数据加载
    train_dataset = SimpleDataset('f3_reduced_12kp_stable.npz', 'train', num_points=1024)
    val_dataset = SimpleDataset('f3_reduced_12kp_stable.npz', 'val', num_points=1024)
    test_dataset = SimpleDataset('f3_reduced_12kp_stable.npz', 'test', num_points=1024)
    
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=4, shuffle=False)
    
    # 计算统计基线
    baseline_error, _ = calculate_baseline_performance(test_loader)
    
    # 测试不同复杂度的模型
    models = {
        'VerySimple': VerySimpleModel(),
        'Moderate': ModerateModel(),
        'Complex': ComplexModel()
    }
    
    results = {'baseline_error': baseline_error}
    
    for name, model in models.items():
        print(f"\n🧠 训练{name}模型...")
        
        start_time = time.time()
        best_val_loss, train_losses, val_losses = train_model(
            model, train_loader, val_loader, device, epochs=50
        )
        training_time = time.time() - start_time
        
        # 测试性能
        test_error = test_model(model, test_loader, device)
        
        # 过拟合分析
        overfitting_stats = overfitting_analysis(train_losses, val_losses)
        
        # 参数量
        param_count = sum(p.numel() for p in model.parameters())
        
        results[name] = {
            'param_count': param_count,
            'test_error': test_error,
            'best_val_loss': best_val_loss,
            'training_time': training_time,
            'overfitting_stats': overfitting_stats,
            'vs_baseline': (test_error - baseline_error) / baseline_error * 100
        }
        
        print(f"   参数量: {param_count:,}")
        print(f"   测试误差: {test_error:.3f}mm")
        print(f"   vs基线: {results[name]['vs_baseline']:+.1f}%")
        print(f"   过拟合分数: {overfitting_stats['overfitting_score']:.3f}")
        print(f"   泛化差距: {overfitting_stats['generalization_gap']:.4f}")
    
    # 结果总结
    print(f"\n📊 **实验结果总结**")
    print("=" * 60)
    print(f"统计基线: {baseline_error:.3f}mm")
    
    for name in models.keys():
        result = results[name]
        print(f"{name:12}: {result['test_error']:.3f}mm ({result['vs_baseline']:+.1f}%) "
              f"参数:{result['param_count']:,}")
    
    # 保存结果
    with open('model_failure_experiment_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💡 **关键发现**:")
    
    # 分析参数量与性能的关系
    param_counts = [results[name]['param_count'] for name in models.keys()]
    test_errors = [results[name]['test_error'] for name in models.keys()]
    
    best_model = min(models.keys(), key=lambda x: results[x]['test_error'])
    worst_model = max(models.keys(), key=lambda x: results[x]['test_error'])
    
    print(f"   最佳模型: {best_model} ({results[best_model]['test_error']:.3f}mm)")
    print(f"   最差模型: {worst_model} ({results[worst_model]['test_error']:.3f}mm)")
    
    # 检查是否有模型超越基线
    better_than_baseline = [name for name in models.keys() 
                           if results[name]['test_error'] < baseline_error]
    
    if better_than_baseline:
        print(f"   超越基线的模型: {better_than_baseline}")
    else:
        print(f"   ❌ 没有模型超越统计基线!")
    
    # 过拟合分析
    most_overfitted = max(models.keys(), 
                         key=lambda x: results[x]['overfitting_stats']['overfitting_score'])
    print(f"   最过拟合模型: {most_overfitted}")
    
    print(f"\n🎯 **结论**: 在小数据集上，模型复杂度与性能不成正比，"
          f"统计基线往往是最难超越的!")

if __name__ == "__main__":
    main()
