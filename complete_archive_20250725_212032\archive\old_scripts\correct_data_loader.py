#!/usr/bin/env python3
"""
Correct Data Loader for Medical Keypoint Detection

Properly load F1/F2/F3 STL files with corresponding keypoints from single CSV file.
Fix the fundamental data loading issue.
"""

import numpy as np
import pandas as pd
from pathlib import Path
import struct
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def load_annotation_file(csv_path: str):
    """Load annotation CSV file with proper encoding"""
    try:
        df = pd.read_csv(csv_path, encoding='gbk')
    except:
        try:
            df = pd.read_csv(csv_path, encoding='utf-8')
        except:
            df = pd.read_csv(csv_path, encoding='latin-1')
    
    keypoints = df[['X', 'Y', 'Z']].values
    labels = df['label'].values.tolist()
    
    return keypoints, labels

def read_stl_binary_complete(stl_path: str):
    """Read STL file completely"""
    try:
        with open(stl_path, 'rb') as f:
            f.read(80)  # Skip header
            num_triangles = struct.unpack('<I', f.read(4))[0]
            
            vertices = []
            for i in range(num_triangles):
                f.read(12)  # Skip normal
                for j in range(3):
                    x, y, z = struct.unpack('<fff', f.read(12))
                    vertices.append([x, y, z])
                f.read(2)  # Skip attribute
            
            return np.array(vertices)
    except Exception as e:
        print(f"STL读取失败: {e}")
        return None

def separate_keypoints_by_region(keypoints, labels):
    """Separate keypoints by F1/F2/F3 regions based on labels"""
    
    f1_keypoints = []
    f2_keypoints = []
    f3_keypoints = []
    
    f1_labels = []
    f2_labels = []
    f3_labels = []
    
    for i, label in enumerate(labels):
        if isinstance(label, str):
            if label.startswith('F_1') or label.startswith('F1'):
                f1_keypoints.append(keypoints[i])
                f1_labels.append(label)
            elif label.startswith('F_2') or label.startswith('F2'):
                f2_keypoints.append(keypoints[i])
                f2_labels.append(label)
            elif label.startswith('F_3') or label.startswith('F3'):
                f3_keypoints.append(keypoints[i])
                f3_labels.append(label)
    
    return {
        'F1': {'keypoints': np.array(f1_keypoints), 'labels': f1_labels},
        'F2': {'keypoints': np.array(f2_keypoints), 'labels': f2_labels},
        'F3': {'keypoints': np.array(f3_keypoints), 'labels': f3_labels}
    }

def load_sample_data(sample_id: str, data_dir: str = "/home/<USER>/pjc/GCN/Data"):
    """Load complete sample data with correct F1/F2/F3 separation"""
    
    print(f"\n🔍 **加载样本 {sample_id}**")
    
    data_path = Path(data_dir)
    annotations_dir = data_path / "annotations"
    stl_dir = data_path / "stl_models"
    
    # Load annotation file
    csv_file = annotations_dir / f"{sample_id}-Table-XYZ.CSV"
    
    if not csv_file.exists():
        print(f"   ❌ 标注文件不存在: {csv_file}")
        return None
    
    try:
        keypoints, labels = load_annotation_file(str(csv_file))
        print(f"   ✅ 加载了 {len(keypoints)} 个关键点")
    except Exception as e:
        print(f"   ❌ 标注加载失败: {e}")
        return None
    
    # Separate keypoints by region
    regions = separate_keypoints_by_region(keypoints, labels)
    
    print(f"   📊 关键点分布:")
    for region_name, region_data in regions.items():
        print(f"      {region_name}: {len(region_data['keypoints'])} 个关键点")
    
    # Load STL files
    stl_data = {}
    for region_name in ['F1', 'F2', 'F3']:
        stl_file = stl_dir / f"{sample_id}-F_{region_name[-1]}.stl"
        
        if stl_file.exists():
            vertices = read_stl_binary_complete(str(stl_file))
            if vertices is not None:
                # Remove duplicate vertices
                unique_vertices = np.unique(vertices, axis=0)
                stl_data[region_name] = unique_vertices
                print(f"   ✅ {region_name} STL: {len(unique_vertices)} 个顶点")
            else:
                print(f"   ❌ {region_name} STL读取失败")
        else:
            print(f"   ❌ {region_name} STL文件不存在: {stl_file}")
    
    return {
        'sample_id': sample_id,
        'regions': regions,
        'stl_data': stl_data,
        'all_keypoints': keypoints,
        'all_labels': labels
    }

def validate_alignment(sample_data):
    """Validate STL-keypoint alignment for each region"""
    
    print(f"\n🎯 **验证对齐质量**")
    
    alignment_results = {}
    
    for region_name in ['F1', 'F2', 'F3']:
        if region_name in sample_data['stl_data'] and len(sample_data['regions'][region_name]['keypoints']) > 0:
            
            stl_vertices = sample_data['stl_data'][region_name]
            region_keypoints = sample_data['regions'][region_name]['keypoints']
            
            # Calculate distances from keypoints to STL surface
            distances = []
            for kp in region_keypoints:
                dists = np.linalg.norm(stl_vertices - kp, axis=1)
                min_dist = np.min(dists)
                distances.append(min_dist)
            
            distances = np.array(distances)
            
            # Calculate metrics
            mean_dist = np.mean(distances)
            max_dist = np.max(distances)
            within_1mm = np.sum(distances <= 1.0) / len(distances) * 100
            within_5mm = np.sum(distances <= 5.0) / len(distances) * 100
            within_10mm = np.sum(distances <= 10.0) / len(distances) * 100
            
            alignment_results[region_name] = {
                'mean_distance': mean_dist,
                'max_distance': max_dist,
                'within_1mm_percent': within_1mm,
                'within_5mm_percent': within_5mm,
                'within_10mm_percent': within_10mm,
                'distances': distances
            }
            
            print(f"   {region_name} 对齐质量:")
            print(f"      平均距离: {mean_dist:.2f}mm")
            print(f"      最大距离: {max_dist:.2f}mm")
            print(f"      ≤1mm: {within_1mm:.1f}%")
            print(f"      ≤5mm: {within_5mm:.1f}%")
            print(f"      ≤10mm: {within_10mm:.1f}%")
    
    return alignment_results

def visualize_sample_data(sample_data, alignment_results=None):
    """Visualize sample data with STL and keypoints"""

    print(f"\n📊 **可视化样本 {sample_data['sample_id']}**")

    fig = plt.figure(figsize=(15, 5))

    for i, region_name in enumerate(['F1', 'F2', 'F3']):
        ax = fig.add_subplot(1, 3, i+1, projection='3d')

        if region_name in sample_data['stl_data'] and len(sample_data['regions'][region_name]['keypoints']) > 0:

            stl_vertices = sample_data['stl_data'][region_name]
            region_keypoints = sample_data['regions'][region_name]['keypoints']

            # Sample STL vertices for visualization
            if len(stl_vertices) > 5000:
                indices = np.random.choice(len(stl_vertices), 5000, replace=False)
                sampled_stl = stl_vertices[indices]
            else:
                sampled_stl = stl_vertices

            # Plot STL point cloud
            ax.scatter(sampled_stl[:, 0], sampled_stl[:, 1], sampled_stl[:, 2],
                      c='lightblue', alpha=0.3, s=1, label='STL Surface')

            # Plot keypoints
            ax.scatter(region_keypoints[:, 0], region_keypoints[:, 1], region_keypoints[:, 2],
                      c='red', s=50, label='Keypoints')

            # Add alignment info if available
            if alignment_results and region_name in alignment_results:
                mean_dist = alignment_results[region_name]['mean_distance']
                within_5mm = alignment_results[region_name]['within_5mm_percent']
                ax.set_title(f'{region_name}\nAvg: {mean_dist:.2f}mm, ≤5mm: {within_5mm:.1f}%')
            else:
                ax.set_title(f'{region_name}\n{len(region_keypoints)} keypoints')

            ax.set_xlabel('X (mm)')
            ax.set_ylabel('Y (mm)')
            ax.set_zlabel('Z (mm)')
            ax.legend()
        else:
            ax.set_title(f'{region_name}\nNo data')

    plt.tight_layout()
    plt.savefig(f'sample_{sample_data["sample_id"]}_visualization.png', dpi=150, bbox_inches='tight')
    plt.show()

def test_data_loader():
    """Test the data loader with a few samples"""

    print("🧪 **测试数据加载器**")
    print("=" * 60)

    # Get available samples
    data_dir = Path("/home/<USER>/pjc/GCN/Data")
    annotations_dir = data_dir / "annotations"

    xyz_files = list(annotations_dir.glob("*-Table-XYZ.CSV"))

    if not xyz_files:
        print("❌ 没有找到XYZ标注文件")
        return

    print(f"📂 找到 {len(xyz_files)} 个标注文件")

    # Test with first few samples
    test_samples = []
    for csv_file in xyz_files[:3]:  # Test first 3 samples
        sample_id = csv_file.stem.split('-')[0]
        test_samples.append(sample_id)

    print(f"🎯 测试样本: {test_samples}")

    all_results = []

    for sample_id in test_samples:
        print(f"\n{'='*60}")

        # Load sample data
        sample_data = load_sample_data(sample_id)

        if sample_data is None:
            continue

        # Validate alignment
        alignment_results = validate_alignment(sample_data)

        # Visualize
        visualize_sample_data(sample_data, alignment_results)

        # Store results
        result = {
            'sample_id': sample_id,
            'keypoint_counts': {
                region: len(data['keypoints'])
                for region, data in sample_data['regions'].items()
            },
            'stl_available': list(sample_data['stl_data'].keys()),
            'alignment_results': alignment_results
        }

        all_results.append(result)

    # Summary
    print(f"\n📋 **测试总结**")
    print(f"   成功加载: {len(all_results)} 个样本")

    for result in all_results:
        print(f"\n   样本 {result['sample_id']}:")
        print(f"      关键点: {result['keypoint_counts']}")
        print(f"      STL文件: {result['stl_available']}")

        if result['alignment_results']:
            for region, metrics in result['alignment_results'].items():
                print(f"      {region} 对齐: {metrics['mean_distance']:.2f}mm平均, "
                      f"{metrics['within_5mm_percent']:.1f}% ≤5mm")

    return all_results

if __name__ == "__main__":
    # Run test
    results = test_data_loader()
