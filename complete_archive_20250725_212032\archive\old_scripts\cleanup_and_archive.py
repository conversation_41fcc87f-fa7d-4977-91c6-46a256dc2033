#!/usr/bin/env python3
"""
Cleanup and Archive Workspace

Organize the workspace and create archives for the F3 model experiments
before focusing on dataset quality issues.
"""

import shutil
from pathlib import Path
import json
import time

def create_archive_structure():
    """Create organized archive structure"""
    
    print("📁 **工作区整理与存档**")
    print("🎯 **目标: 整理实验文件，专注数据集质量问题**")
    print("=" * 80)
    
    # Create archive directories
    archive_base = Path("Archive_F3_Experiments")
    archive_base.mkdir(exist_ok=True)
    
    # Create subdirectories
    subdirs = [
        "01_Dataset_Creation",
        "02_Model_Training", 
        "03_Optimization_Attempts",
        "04_Analysis_Results",
        "05_Key_Findings"
    ]
    
    for subdir in subdirs:
        (archive_base / subdir).mkdir(exist_ok=True)
    
    print(f"✅ 创建存档目录结构: {archive_base}")
    
    return archive_base

def organize_files(archive_base):
    """Organize files into appropriate directories"""
    
    print(f"\n📋 **文件分类整理**")
    
    # File organization mapping
    file_mapping = {
        "01_Dataset_Creation": [
            "create_f3_single_component_dataset.py",
            "create_f3_dataset_simple.py", 
            "create_f3_dataset_robust.py",
            "create_practical_medical_dataset.py",
            "create_aligned_medical_dataset.py",
            "F3SimpleDataset/",
            "f3_batch_*.json",
            "f3_dataset_complete.json"
        ],
        "02_Model_Training": [
            "train_f3_single_component.py",
            "train_f3_only.py",
            "train_f3_direct.py",
            "simple_f3_train.py",
            "best_f3_simple.pth",
            "best_f3_model.pth"
        ],
        "03_Optimization_Attempts": [
            "optimize_f3_models.py",
            "improve_f3_training.py", 
            "optimize_f3_conservative.py",
            "best_f3_improved.pth",
            "best_enhanced.pth",
            "best_deeper.pth",
            "best_original.pth"
        ],
        "04_Analysis_Results": [
            "analyze_component_coordinates.py",
            "test_f3_component_alignment.py",
            "analyze_f3_dataset_results.py",
            "analyze_dataset_fundamental_issues.py",
            "component_coordinate_analysis.json",
            "f3_alignment_test_results.json",
            "f3_training_results.json",
            "f3_optimization_results.json",
            "f3_improved_results.json",
            "f3_conservative_results.json",
            "dataset_fundamental_analysis.json"
        ],
        "05_Key_Findings": [
            "debug_segfault.py",
            "final_recommendation_report.py"
        ]
    }
    
    # Move files
    for category, files in file_mapping.items():
        category_dir = archive_base / category
        print(f"\n📂 整理 {category}:")
        
        for file_pattern in files:
            if file_pattern.endswith('/'):
                # Directory
                source_dir = Path(file_pattern.rstrip('/'))
                if source_dir.exists():
                    target_dir = category_dir / source_dir.name
                    if target_dir.exists():
                        shutil.rmtree(target_dir)
                    shutil.copytree(source_dir, target_dir)
                    print(f"   📁 {source_dir} → {target_dir}")
            elif '*' in file_pattern:
                # Wildcard pattern
                for file_path in Path('.').glob(file_pattern):
                    if file_path.is_file():
                        target_path = category_dir / file_path.name
                        shutil.copy2(file_path, target_path)
                        print(f"   📄 {file_path} → {target_path}")
            else:
                # Single file
                source_file = Path(file_pattern)
                if source_file.exists():
                    target_file = category_dir / source_file.name
                    shutil.copy2(source_file, target_file)
                    print(f"   📄 {source_file} → {target_file}")

def create_summary_report(archive_base):
    """Create comprehensive summary report"""
    
    print(f"\n📊 **创建总结报告**")
    
    summary_report = {
        "archive_info": {
            "creation_date": time.strftime("%Y-%m-%d %H:%M:%S"),
            "purpose": "Archive F3 model experiments before focusing on dataset quality",
            "archive_location": str(archive_base)
        },
        "experiment_timeline": {
            "phase_1": {
                "name": "数据集创建",
                "description": "从原始STL-CSV数据创建F3单部件数据集",
                "key_achievement": "成功创建96个F3样本的数据集",
                "main_challenge": "解决段错误和内存管理问题"
            },
            "phase_2": {
                "name": "模型训练",
                "description": "训练F3关键点检测模型",
                "key_achievement": "达到18.40mm基线性能",
                "main_challenge": "性能远低于医疗级要求"
            },
            "phase_3": {
                "name": "模型优化",
                "description": "尝试多种先进架构和训练策略",
                "key_achievement": "最佳性能17.04mm (轻微改进)",
                "main_challenge": "复杂模型反而性能更差"
            },
            "phase_4": {
                "name": "根因分析",
                "description": "深度分析数据集质量问题",
                "key_achievement": "发现STL-CSV严重对齐问题",
                "main_challenge": "确认数据集存在根本性质量问题"
            }
        },
        "key_findings": {
            "dataset_quality_issues": {
                "f1_f2_alignment": "110-126mm平均偏移，完全不可用",
                "f3_alignment": "35-45mm平均偏移，相对较好但仍有问题",
                "root_cause": "STL文件与CSV标注存在系统性坐标系偏移"
            },
            "model_performance": {
                "best_f3_error": "17.04mm",
                "medical_target": "<10mm",
                "conclusion": "性能受限于数据质量而非模型架构"
            },
            "technical_insights": {
                "simple_models_better": "简单模型在低质量数据上表现更好",
                "data_normalization_harmful": "数据归一化破坏了有用的空间信息",
                "segfault_solution": "批处理和内存管理解决了段错误问题"
            }
        },
        "dataset_paper_value": {
            "original_goal": "创建高质量医疗关键点数据集",
            "actual_contribution": "识别和分析医疗数据集质量挑战",
            "academic_value": [
                "系统性数据质量评估方法",
                "数据质量对模型性能的决定性影响",
                "医疗AI数据集建设的经验教训",
                "STL-CSV对齐问题的深度分析"
            ]
        },
        "next_steps": {
            "immediate": "专注于数据集质量问题的深度分析",
            "research_direction": "医疗数据集质量评估与挑战分析",
            "paper_focus": "数据质量优先的重要性和方法论贡献"
        },
        "files_archived": {
            "dataset_creation": "F3数据集创建相关脚本和数据",
            "model_training": "各种模型训练脚本和结果",
            "optimization": "模型优化尝试和改进策略",
            "analysis": "数据质量分析和诊断脚本",
            "findings": "关键发现和总结报告"
        }
    }
    
    # Save summary report
    with open(archive_base / "EXPERIMENT_SUMMARY.json", 'w', encoding='utf-8') as f:
        json.dump(summary_report, f, indent=2, ensure_ascii=False)
    
    # Create README
    readme_content = f"""# F3模型实验存档

## 📋 实验总结

本存档包含了F3单部件关键点检测模型的完整实验过程，从数据集创建到根因分析。

## 🎯 关键发现

**数据集存在根本性质量问题:**
- F1/F2: 110-126mm STL-CSV对齐偏移 (完全不可用)
- F3: 35-45mm STL-CSV对齐偏移 (相对较好但仍有问题)
- 根因: STL文件与CSV标注的系统性坐标系偏移

**模型性能受限于数据质量:**
- 最佳F3性能: 17.04mm
- 医疗级目标: <10mm
- 结论: 问题在数据质量，不在模型架构

## 📁 目录结构

- `01_Dataset_Creation/`: F3数据集创建脚本和数据
- `02_Model_Training/`: 模型训练脚本和结果
- `03_Optimization_Attempts/`: 模型优化尝试
- `04_Analysis_Results/`: 数据质量分析结果
- `05_Key_Findings/`: 关键发现和总结

## 🎓 学术价值

这个实验虽然没有达到预期的模型性能，但发现了医疗数据集的重要质量问题，
为医疗AI数据集建设提供了宝贵经验和方法论贡献。

## 📝 论文方向

从"高质量医疗关键点数据集"转向"医疗数据集质量评估与挑战分析"，
重点展示数据质量对模型性能的决定性影响。

---
存档时间: {time.strftime("%Y-%m-%d %H:%M:%S")}
"""
    
    with open(archive_base / "README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"   📄 创建总结报告: {archive_base}/EXPERIMENT_SUMMARY.json")
    print(f"   📄 创建说明文档: {archive_base}/README.md")

def cleanup_workspace():
    """Clean up the main workspace"""
    
    print(f"\n🧹 **清理主工作区**")
    
    # Files to keep in main workspace
    keep_files = [
        "train_wang2022_12point.py",  # User's original file
        "analyze_dataset_fundamental_issues.py",  # Current focus
        "dataset_fundamental_analysis.json",  # Key analysis results
        "Data/",  # Original data directory
        "Archive_F3_Experiments/"  # Our archive
    ]
    
    # List files that could be cleaned up
    cleanup_candidates = []
    for file_path in Path('.').iterdir():
        if file_path.name not in keep_files and not file_path.name.startswith('.'):
            if file_path.is_file() and (
                file_path.name.startswith('f3_') or
                file_path.name.startswith('train_f3') or
                file_path.name.startswith('create_f3') or
                file_path.name.startswith('optimize_f3') or
                file_path.name.startswith('improve_f3') or
                file_path.name.endswith('.pth') or
                file_path.name.startswith('best_')
            ):
                cleanup_candidates.append(file_path)
    
    print(f"   保留核心文件: {len(keep_files)} 个")
    print(f"   可清理文件: {len(cleanup_candidates)} 个")
    
    # Note: We won't actually delete files, just report what could be cleaned
    if cleanup_candidates:
        print(f"   建议清理的文件已存档，可以安全删除:")
        for file_path in cleanup_candidates[:5]:  # Show first 5
            print(f"      - {file_path.name}")
        if len(cleanup_candidates) > 5:
            print(f"      - ... 还有 {len(cleanup_candidates) - 5} 个文件")

def main():
    """Main cleanup and archive function"""
    
    try:
        # Create archive structure
        archive_base = create_archive_structure()
        
        # Organize files
        organize_files(archive_base)
        
        # Create summary report
        create_summary_report(archive_base)
        
        # Cleanup workspace
        cleanup_workspace()
        
        print(f"\n🎉 **工作区整理完成!**")
        print(f"📁 存档位置: {archive_base}")
        print(f"📊 实验总结: {archive_base}/EXPERIMENT_SUMMARY.json")
        print(f"📖 说明文档: {archive_base}/README.md")
        
        print(f"\n🎯 **下一步专注方向:**")
        print(f"   1. 深度分析数据集质量问题")
        print(f"   2. 研究STL-CSV对齐解决方案")
        print(f"   3. 撰写数据集质量评估论文")
        print(f"   4. 为医疗AI数据集建设提供指导")
        
        print(f"\n💡 **核心发现:**")
        print(f"   - F1/F2存在110+mm系统性偏移")
        print(f"   - F3相对较好但仍有35+mm偏移")
        print(f"   - 数据质量是性能瓶颈，不是模型架构")
        print(f"   - 这是重要的学术发现，值得发表!")
        
        return True
        
    except Exception as e:
        print(f"❌ 整理过程出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ 工作区整理成功，可以专注数据集问题了!")
    else:
        print(f"\n❌ 工作区整理失败")
