#!/usr/bin/env python3
"""
测试改进的医疗关键点检测模型
"""

import torch
import numpy as np
import h5py
from pathlib import Path
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

from train_improved_model import ImprovedMedicalPointNet

class ImprovedModelTester:
    """改进模型测试器"""
    
    def __init__(self, model_path="output/improved_model_training/best_improved_model.pth"):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model_path = Path(model_path)
        self.aligned_data_root = Path("MedicalAlignedDataset")
        self.output_dir = Path("output/improved_model_test")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🧪 改进模型测试器初始化")
        
        # 加载模型
        self.load_model()
        
        # 获取可用的患者ID
        self.get_available_patients()
        
        print(f"✅ 测试器准备完成")
    
    def load_model(self):
        """加载改进的模型"""
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
            self.model = ImprovedMedicalPointNet(num_keypoints=57)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            print(f"✅ 改进模型加载成功")
            
            # 打印模型信息
            if 'epoch' in checkpoint:
                print(f"   训练轮数: {checkpoint['epoch']}")
            if 'best_loss' in checkpoint:
                print(f"   最佳损失: {checkpoint['best_loss']:.4f}")
                
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self.model = None
    
    def get_available_patients(self):
        """获取可用的患者ID"""
        aligned_data_dir = self.aligned_data_root / "aligned_data"
        if aligned_data_dir.exists():
            h5_files = list(aligned_data_dir.glob("*.h5"))
            self.patient_ids = [f.stem for f in h5_files]
            print(f"📊 找到 {len(self.patient_ids)} 个患者")
        else:
            print(f"❌ 对齐数据目录不存在: {aligned_data_dir}")
            self.patient_ids = []
    
    def load_patient_data(self, patient_id):
        """加载患者数据"""
        aligned_file = self.aligned_data_root / "aligned_data" / f"{patient_id}.h5"
        if not aligned_file.exists():
            return None
        
        try:
            with h5py.File(aligned_file, 'r') as f:
                point_cloud = f['point_cloud'][:]
                keypoints = f['keypoints'][:]
            
            return {
                'point_cloud': point_cloud,
                'keypoints': keypoints,
                'patient_id': patient_id
            }
        except Exception as e:
            print(f"❌ 加载患者 {patient_id} 数据失败: {e}")
            return None
    
    def normalize_keypoints_to_pointcloud(self, keypoints, point_cloud):
        """将关键点归一化到点云坐标系（与训练时一致）"""
        pc_min, pc_max = point_cloud.min(axis=0), point_cloud.max(axis=0)
        pc_center = (pc_min + pc_max) / 2
        pc_range = pc_max - pc_min
        
        kp_min, kp_max = keypoints.min(axis=0), keypoints.max(axis=0)
        kp_center = (kp_min + kp_max) / 2
        kp_range = kp_max - kp_min
        
        # 智能缩放和平移
        scale_factors = []
        for i in range(3):
            if kp_range[i] > 0:
                # 目标是让关键点范围占点云范围的80%
                target_range = pc_range[i] * 0.8
                scale_factor = target_range / kp_range[i]
            else:
                scale_factor = 1.0
            scale_factors.append(scale_factor)
        
        scale_factors = np.array(scale_factors)
        
        # 应用变换
        normalized_keypoints = (keypoints - kp_center) * scale_factors + pc_center
        
        return normalized_keypoints
    
    def predict_keypoints(self, point_cloud):
        """预测关键点"""
        if self.model is None:
            return None, None
            
        with torch.no_grad():
            point_cloud_tensor = torch.from_numpy(point_cloud.T).unsqueeze(0).to(self.device)  # [1, 3, N]
            pred_keypoints, pred_range, features = self.model(point_cloud_tensor)
            pred_keypoints = pred_keypoints.cpu().numpy()[0]  # [57, 3]
            pred_range = pred_range.cpu().numpy()[0]  # [6]
            return pred_keypoints, pred_range
    
    def visualize_improved_prediction(self, patient_data, pred_keypoints, pred_range):
        """可视化改进模型的预测结果"""
        patient_id = patient_data['patient_id']
        point_cloud = patient_data['point_cloud']
        gt_keypoints = patient_data['keypoints']
        
        # 归一化真实关键点（用于公平比较）
        gt_keypoints_normalized = self.normalize_keypoints_to_pointcloud(gt_keypoints, point_cloud)
        
        # 计算误差
        errors = np.linalg.norm(pred_keypoints - gt_keypoints_normalized, axis=1)
        mean_error = np.mean(errors)
        acc_5mm = np.mean(errors <= 5.0) * 100
        acc_3mm = np.mean(errors <= 3.0) * 100
        
        print(f"   📊 改进模型预测性能:")
        print(f"      平均误差: {mean_error:.2f}mm")
        print(f"      5mm准确率: {acc_5mm:.1f}%")
        print(f"      3mm准确率: {acc_3mm:.1f}%")
        print(f"      最大误差: {np.max(errors):.2f}mm")
        print(f"      最小误差: {np.min(errors):.2f}mm")
        
        # 分析坐标范围
        pc_min, pc_max = point_cloud.min(axis=0), point_cloud.max(axis=0)
        gt_min, gt_max = gt_keypoints_normalized.min(axis=0), gt_keypoints_normalized.max(axis=0)
        pred_min, pred_max = pred_keypoints.min(axis=0), pred_keypoints.max(axis=0)
        
        print(f"   📏 坐标范围分析:")
        print(f"      点云范围: X[{pc_min[0]:.1f}, {pc_max[0]:.1f}], Y[{pc_min[1]:.1f}, {pc_max[1]:.1f}], Z[{pc_min[2]:.1f}, {pc_max[2]:.1f}]")
        print(f"      真实范围: X[{gt_min[0]:.1f}, {gt_max[0]:.1f}], Y[{gt_min[1]:.1f}, {gt_max[1]:.1f}], Z[{gt_min[2]:.1f}, {gt_max[2]:.1f}]")
        print(f"      预测范围: X[{pred_min[0]:.1f}, {pred_max[0]:.1f}], Y[{pred_min[1]:.1f}, {pred_max[1]:.1f}], Z[{pred_min[2]:.1f}, {pred_max[2]:.1f}]")
        print(f"      模型预测范围: X[{pred_range[0]:.1f}, {pred_range[1]:.1f}], Y[{pred_range[2]:.1f}, {pred_range[3]:.1f}], Z[{pred_range[4]:.1f}, {pred_range[5]:.1f}]")
        
        # 创建可视化
        fig = plt.figure(figsize=(20, 12))
        
        # 1. 3D整体视图
        ax1 = fig.add_subplot(2, 4, 1, projection='3d')
        ax1.scatter(point_cloud[:, 0], point_cloud[:, 1], point_cloud[:, 2], 
                   c='lightgray', alpha=0.3, s=1, label='Point Cloud')
        ax1.scatter(gt_keypoints_normalized[:, 0], gt_keypoints_normalized[:, 1], gt_keypoints_normalized[:, 2], 
                   c='green', s=30, alpha=0.8, marker='o', label='Ground Truth')
        ax1.scatter(pred_keypoints[:, 0], pred_keypoints[:, 1], pred_keypoints[:, 2], 
                   c='blue', s=20, alpha=0.8, marker='^', label='Improved Prediction')
        
        ax1.set_title(f'Improved Model - {patient_id}\nError: {mean_error:.2f}mm')
        ax1.legend()
        ax1.set_xlabel('X (mm)')
        ax1.set_ylabel('Y (mm)')
        ax1.set_zlabel('Z (mm)')
        
        # 2. XY平面投影
        ax2 = fig.add_subplot(2, 4, 2)
        ax2.scatter(point_cloud[:, 0], point_cloud[:, 1], 
                   c='lightgray', alpha=0.3, s=1, label='Point Cloud')
        ax2.scatter(gt_keypoints_normalized[:, 0], gt_keypoints_normalized[:, 1], 
                   c='green', s=30, alpha=0.8, marker='o', label='Ground Truth')
        ax2.scatter(pred_keypoints[:, 0], pred_keypoints[:, 1], 
                   c='blue', s=20, alpha=0.8, marker='^', label='Improved Prediction')
        ax2.set_title('XY Plane Projection')
        ax2.set_xlabel('X (mm)')
        ax2.set_ylabel('Y (mm)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.axis('equal')
        
        # 3. XZ平面投影
        ax3 = fig.add_subplot(2, 4, 3)
        ax3.scatter(point_cloud[:, 0], point_cloud[:, 2], 
                   c='lightgray', alpha=0.3, s=1, label='Point Cloud')
        ax3.scatter(gt_keypoints_normalized[:, 0], gt_keypoints_normalized[:, 2], 
                   c='green', s=30, alpha=0.8, marker='o', label='Ground Truth')
        ax3.scatter(pred_keypoints[:, 0], pred_keypoints[:, 2], 
                   c='blue', s=20, alpha=0.8, marker='^', label='Improved Prediction')
        ax3.set_title('XZ Plane Projection')
        ax3.set_xlabel('X (mm)')
        ax3.set_ylabel('Z (mm)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.axis('equal')
        
        # 4. YZ平面投影
        ax4 = fig.add_subplot(2, 4, 4)
        ax4.scatter(point_cloud[:, 1], point_cloud[:, 2], 
                   c='lightgray', alpha=0.3, s=1, label='Point Cloud')
        ax4.scatter(gt_keypoints_normalized[:, 1], gt_keypoints_normalized[:, 2], 
                   c='green', s=30, alpha=0.8, marker='o', label='Ground Truth')
        ax4.scatter(pred_keypoints[:, 1], pred_keypoints[:, 2], 
                   c='blue', s=20, alpha=0.8, marker='^', label='Improved Prediction')
        ax4.set_title('YZ Plane Projection')
        ax4.set_xlabel('Y (mm)')
        ax4.set_ylabel('Z (mm)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        ax4.axis('equal')
        
        # 5. 误差分布
        ax5 = fig.add_subplot(2, 4, 5)
        x = np.arange(len(errors))
        colors = ['red' if e > 5 else 'orange' if e > 3 else 'green' for e in errors]
        ax5.bar(x, errors, color=colors, alpha=0.7)
        ax5.axhline(y=5.0, color='red', linestyle='--', alpha=0.7, label='5mm threshold')
        ax5.axhline(y=3.0, color='orange', linestyle='--', alpha=0.7, label='3mm threshold')
        ax5.set_xlabel('Keypoint Index')
        ax5.set_ylabel('Error (mm)')
        ax5.set_title('Per-Keypoint Error')
        ax5.legend()
        ax5.grid(True, alpha=0.3)
        
        # 6. 误差直方图
        ax6 = fig.add_subplot(2, 4, 6)
        ax6.hist(errors, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax6.axvline(x=3.0, color='orange', linestyle='--', alpha=0.7, label='3mm threshold')
        ax6.axvline(x=5.0, color='red', linestyle='--', alpha=0.7, label='5mm threshold')
        ax6.axvline(x=mean_error, color='blue', linestyle='-', alpha=0.7, label=f'Mean: {mean_error:.2f}mm')
        ax6.set_xlabel('Error (mm)')
        ax6.set_ylabel('Frequency')
        ax6.set_title('Error Distribution')
        ax6.legend()
        ax6.grid(True, alpha=0.3)
        
        # 7. 坐标范围对比
        ax7 = fig.add_subplot(2, 4, 7)
        categories = ['X_min', 'X_max', 'Y_min', 'Y_max', 'Z_min', 'Z_max']
        pc_values = [pc_min[0], pc_max[0], pc_min[1], pc_max[1], pc_min[2], pc_max[2]]
        gt_values = [gt_min[0], gt_max[0], gt_min[1], gt_max[1], gt_min[2], gt_max[2]]
        pred_values = [pred_min[0], pred_max[0], pred_min[1], pred_max[1], pred_min[2], pred_max[2]]
        model_range_values = [pred_range[0], pred_range[1], pred_range[2], pred_range[3], pred_range[4], pred_range[5]]
        
        x = np.arange(len(categories))
        width = 0.2
        
        ax7.bar(x - 1.5*width, pc_values, width, label='Point Cloud', alpha=0.7)
        ax7.bar(x - 0.5*width, gt_values, width, label='Ground Truth', alpha=0.7)
        ax7.bar(x + 0.5*width, pred_values, width, label='Prediction', alpha=0.7)
        ax7.bar(x + 1.5*width, model_range_values, width, label='Model Range', alpha=0.7)
        
        ax7.set_xlabel('Coordinate Axis')
        ax7.set_ylabel('Coordinate Value (mm)')
        ax7.set_title('Coordinate Range Comparison')
        ax7.set_xticks(x)
        ax7.set_xticklabels(categories)
        ax7.legend()
        ax7.grid(True, alpha=0.3)
        
        # 8. 性能统计
        ax8 = fig.add_subplot(2, 4, 8)
        ax8.axis('off')
        
        # 计算更多统计信息
        errors_under_3mm = np.sum(errors <= 3.0)
        errors_3_5mm = np.sum((errors > 3.0) & (errors <= 5.0))
        errors_over_5mm = np.sum(errors > 5.0)
        
        stats_text = f"""Improved Model Performance

Patient ID: {patient_id}

Error Statistics:
• Mean Error: {mean_error:.2f}mm
• Std Dev: {np.std(errors):.2f}mm
• Max Error: {np.max(errors):.2f}mm
• Min Error: {np.min(errors):.2f}mm

Accuracy:
• 3mm Accuracy: {acc_3mm:.1f}% ({errors_under_3mm}/57)
• 5mm Accuracy: {acc_5mm:.1f}% ({errors_under_3mm + errors_3_5mm}/57)
• >5mm Errors: {errors_over_5mm}/57

Coordinate Matching:
• PC Center: ({np.mean(point_cloud, axis=0)[0]:.1f}, {np.mean(point_cloud, axis=0)[1]:.1f}, {np.mean(point_cloud, axis=0)[2]:.1f})
• GT Center: ({np.mean(gt_keypoints_normalized, axis=0)[0]:.1f}, {np.mean(gt_keypoints_normalized, axis=0)[1]:.1f}, {np.mean(gt_keypoints_normalized, axis=0)[2]:.1f})
• Pred Center: ({np.mean(pred_keypoints, axis=0)[0]:.1f}, {np.mean(pred_keypoints, axis=0)[1]:.1f}, {np.mean(pred_keypoints, axis=0)[2]:.1f})

Range Prediction:
• Model Range Accuracy: {np.mean(np.abs(pred_range - np.concatenate([pc_min, pc_max]))):.2f}mm
"""
        
        ax8.text(0.05, 0.95, stats_text, transform=ax8.transAxes, fontsize=9,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
        
        plt.tight_layout()
        
        # 保存可视化
        plot_path = self.output_dir / f"improved_model_{patient_id}.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"💾 改进模型可视化已保存: {plot_path}")
        
        return {
            'patient_id': patient_id,
            'mean_error': mean_error,
            'acc_5mm': acc_5mm,
            'acc_3mm': acc_3mm,
            'max_error': np.max(errors),
            'min_error': np.min(errors),
            'std_error': np.std(errors),
            'range_accuracy': np.mean(np.abs(pred_range - np.concatenate([pc_min, pc_max])))
        }
    
    def test_improved_model(self, max_patients=3):
        """测试改进的模型"""
        print(f"🧪 开始测试改进的模型...")
        
        if not self.patient_ids:
            print(f"❌ 没有可用的患者数据")
            return
        
        all_results = []
        
        for i, patient_id in enumerate(self.patient_ids[:max_patients]):
            print(f"\n{'='*60}")
            print(f"患者 {i+1}: {patient_id}")
            print(f"{'='*60}")
            
            try:
                # 加载数据
                patient_data = self.load_patient_data(patient_id)
                if patient_data is None:
                    continue
                
                print(f"   点云形状: {patient_data['point_cloud'].shape}")
                print(f"   关键点形状: {patient_data['keypoints'].shape}")
                
                # 预测关键点
                pred_keypoints, pred_range = self.predict_keypoints(patient_data['point_cloud'])
                if pred_keypoints is None:
                    continue
                
                print(f"   预测关键点形状: {pred_keypoints.shape}")
                print(f"   预测范围形状: {pred_range.shape}")
                
                # 可视化预测结果
                result = self.visualize_improved_prediction(patient_data, pred_keypoints, pred_range)
                all_results.append(result)
                
            except Exception as e:
                print(f"❌ 处理患者 {patient_id} 时出错: {e}")
                continue
        
        # 总结结果
        if all_results:
            self.summarize_improved_results(all_results)
        
        return all_results
    
    def summarize_improved_results(self, results):
        """总结改进模型的结果"""
        print(f"\n🎯 改进模型性能总结:")
        
        mean_errors = [r['mean_error'] for r in results]
        acc_5mm = [r['acc_5mm'] for r in results]
        acc_3mm = [r['acc_3mm'] for r in results]
        range_accuracies = [r['range_accuracy'] for r in results]
        
        print(f"   平均误差: {np.mean(mean_errors):.2f}±{np.std(mean_errors):.2f}mm")
        print(f"   5mm准确率: {np.mean(acc_5mm):.1f}±{np.std(acc_5mm):.1f}%")
        print(f"   3mm准确率: {np.mean(acc_3mm):.1f}±{np.std(acc_3mm):.1f}%")
        print(f"   范围预测准确性: {np.mean(range_accuracies):.2f}±{np.std(range_accuracies):.2f}mm")
        print(f"   最佳患者误差: {np.min(mean_errors):.2f}mm")
        print(f"   最差患者误差: {np.max(mean_errors):.2f}mm")

def main():
    """主函数"""
    print("🧪 启动改进模型测试...")
    
    # 创建测试器
    tester = ImprovedModelTester()
    
    # 测试改进模型
    tester.test_improved_model(max_patients=3)

if __name__ == "__main__":
    main()
