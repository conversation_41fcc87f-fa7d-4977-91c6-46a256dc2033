#!/usr/bin/env python3
"""
分层自适应关键点检测系统
针对不同标注策略使用不同的方法
"""

import numpy as np
import torch
import torch.nn as nn
from basic_19keypoints_system import BasicHeatmapPointNet19, extract_keypoints_from_heatmaps_19

class AdaptiveKeypointDetector:
    """自适应关键点检测器"""
    
    def __init__(self, model, device):
        self.model = model
        self.device = device
        
        # 定义关键点策略
        self.keypoint_strategies = {
            # 几何策略 - 基于数学特征
            'geometric': [1, 7, 11, 12, 13, 18],  # F3-2, F3-8, F3-12, F3-13, F3-14, F3-19
            # 解剖策略 - 基于解剖学特征  
            'anatomical': [0, 2, 4, 6, 9, 15, 17],  # F3-1, F3-3, F3-5, F3-7, F3-10, F3-16, F3-18
            # 相对策略 - 基于相对位置
            'relative': [3, 5, 8, 10, 14, 16]  # F3-4, F3-6, F3-9, F3-11, F3-15, F3-17
        }
        
        # 策略优先级 (稳定性排序)
        self.strategy_priority = ['anatomical', 'geometric', 'relative']
        
    def predict_adaptive(self, point_cloud):
        """自适应预测"""
        
        # 1. 基础ML预测
        base_predictions = self._get_base_predictions(point_cloud)
        
        # 2. 分层处理
        refined_predictions = base_predictions.copy()
        
        # 按优先级处理
        for strategy in self.strategy_priority:
            kp_indices = self.keypoint_strategies[strategy]
            
            if strategy == 'anatomical':
                refined_predictions = self._apply_anatomical_refinement(
                    refined_predictions, point_cloud, kp_indices)
            elif strategy == 'geometric':
                refined_predictions = self._apply_geometric_constraints(
                    refined_predictions, point_cloud, kp_indices)
            elif strategy == 'relative':
                refined_predictions = self._apply_relative_optimization(
                    refined_predictions, point_cloud, kp_indices)
        
        return refined_predictions
    
    def _get_base_predictions(self, point_cloud):
        """获取基础ML预测"""
        
        # 采样点云
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            pc_sampled = point_cloud[indices]
        else:
            pc_sampled = point_cloud
        
        pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            pred_heatmaps = self.model(pc_tensor)
        
        pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze()
        pred_keypoints, confidences = extract_keypoints_from_heatmaps_19(pred_heatmaps_np, pc_sampled)
        
        return pred_keypoints
    
    def _apply_anatomical_refinement(self, predictions, point_cloud, kp_indices):
        """解剖学精化 - 基于解剖学先验知识"""
        
        refined = predictions.copy()
        
        for kp_idx in kp_indices:
            if kp_idx == 17:  # F3-18 (尾骨尖)
                refined[kp_idx] = self._refine_coccyx_tip(predictions[kp_idx], point_cloud)
            elif kp_idx == 0:  # F3-1 (骶骨体中心)
                refined[kp_idx] = self._refine_sacral_center(predictions[kp_idx], point_cloud)
            elif kp_idx in [2, 4, 6, 9, 15]:  # 其他解剖特征点
                refined[kp_idx] = self._refine_anatomical_feature(predictions[kp_idx], point_cloud, kp_idx)
        
        return refined
    
    def _apply_geometric_constraints(self, predictions, point_cloud, kp_indices):
        """几何约束 - 基于数学特征"""
        
        refined = predictions.copy()
        
        for kp_idx in kp_indices:
            if kp_idx == 12:  # F3-13 (Z最高点)
                refined[kp_idx] = self._geometric_z_max(predictions[kp_idx], point_cloud)
            elif kp_idx == 1:  # F3-2 (前边界)
                refined[kp_idx] = self._geometric_anterior_boundary(predictions[kp_idx], point_cloud)
            elif kp_idx == 11:  # F3-12 (后边界)
                refined[kp_idx] = self._geometric_posterior_boundary(predictions[kp_idx], point_cloud)
            elif kp_idx == 13:  # F3-14 (左边界)
                refined[kp_idx] = self._geometric_left_boundary(predictions[kp_idx], point_cloud)
            elif kp_idx == 18:  # F3-19 (右边界)
                refined[kp_idx] = self._geometric_right_boundary(predictions[kp_idx], point_cloud)
            elif kp_idx == 7:  # F3-8 (中心质量)
                refined[kp_idx] = self._geometric_centroid(predictions[kp_idx], point_cloud)
        
        return refined
    
    def _apply_relative_optimization(self, predictions, point_cloud, kp_indices):
        """相对位置优化 - 基于空间关系"""
        
        refined = predictions.copy()
        
        # 联合优化相对位置点
        for kp_idx in kp_indices:
            if kp_idx == 3:  # F3-4 (在标志之间)
                refined[kp_idx] = self._optimize_between_landmarks(predictions, point_cloud, kp_idx)
            elif kp_idx in [5, 14, 16]:  # 比例位置点
                refined[kp_idx] = self._optimize_proportional_position(predictions, point_cloud, kp_idx)
            elif kp_idx == 8:  # F3-9 (对称位置)
                refined[kp_idx] = self._optimize_symmetric_position(predictions, point_cloud, kp_idx)
            elif kp_idx == 10:  # F3-11 (比例间距)
                refined[kp_idx] = self._optimize_proportional_spacing(predictions, point_cloud, kp_idx)
        
        return refined
    
    # ==================== 解剖学精化方法 ====================
    
    def _refine_coccyx_tip(self, prediction, point_cloud):
        """精化尾骨尖 - 基于尾骨解剖学特征"""
        
        # 找到Z坐标较低的区域 (下半部分)
        z_threshold = np.percentile(point_cloud[:, 2], 30)
        lower_region = point_cloud[point_cloud[:, 2] <= z_threshold]
        
        if len(lower_region) == 0:
            return prediction
        
        # 在下半部分找到最尖锐的点 (局部密度最小)
        tip_candidates = []
        for point in lower_region[::10]:  # 采样以提高效率
            distances = np.linalg.norm(lower_region - point, axis=1)
            local_density = np.sum(distances <= 3.0)  # 3mm内的点数
            tip_candidates.append((point, local_density))
        
        # 选择密度最小的点作为尖端
        if tip_candidates:
            tip_point = min(tip_candidates, key=lambda x: x[1])[0]
            
            # 与原预测加权平均
            alpha = 0.3  # 解剖学约束权重
            return alpha * tip_point + (1 - alpha) * prediction
        
        return prediction
    
    def _refine_sacral_center(self, prediction, point_cloud):
        """精化骶骨体中心 - 基于质量中心"""
        
        # 计算点云的质量中心
        centroid = np.mean(point_cloud, axis=0)
        
        # 与原预测加权平均
        alpha = 0.2
        return alpha * centroid + (1 - alpha) * prediction
    
    def _refine_anatomical_feature(self, prediction, point_cloud, kp_idx):
        """精化其他解剖特征点"""
        
        # 在预测点周围寻找最佳位置
        search_radius = 5.0
        distances = np.linalg.norm(point_cloud - prediction, axis=1)
        nearby_mask = distances <= search_radius
        nearby_points = point_cloud[nearby_mask]
        
        if len(nearby_points) > 0:
            # 使用局部密度加权
            weights = []
            for point in nearby_points:
                local_distances = np.linalg.norm(nearby_points - point, axis=1)
                local_density = np.sum(local_distances <= 2.0)
                weights.append(local_density)
            
            weights = np.array(weights)
            if np.sum(weights) > 0:
                weights = weights / np.sum(weights)
                refined_point = np.average(nearby_points, axis=0, weights=weights)
                
                alpha = 0.3
                return alpha * refined_point + (1 - alpha) * prediction
        
        return prediction
    
    # ==================== 几何约束方法 ====================
    
    def _geometric_z_max(self, prediction, point_cloud):
        """几何Z最高点约束"""
        
        # 找到点云中Z坐标最高的点
        z_max_idx = np.argmax(point_cloud[:, 2])
        z_max_point = point_cloud[z_max_idx]
        
        # 在最高点周围寻找更精确的位置
        distances = np.linalg.norm(point_cloud - z_max_point, axis=1)
        nearby_mask = distances <= 5.0
        nearby_points = point_cloud[nearby_mask]
        
        if len(nearby_points) > 0:
            # 使用Z坐标加权平均
            z_coords = nearby_points[:, 2]
            weights = np.exp((z_coords - np.min(z_coords)) / 2.0)
            weights = weights / np.sum(weights)
            
            geometric_point = np.average(nearby_points, axis=0, weights=weights)
            
            # 强几何约束
            alpha = 0.7
            return alpha * geometric_point + (1 - alpha) * prediction
        
        return z_max_point
    
    def _geometric_anterior_boundary(self, prediction, point_cloud):
        """几何前边界约束"""
        
        # 找到Y坐标最小的点 (前方)
        y_min_idx = np.argmin(point_cloud[:, 1])
        boundary_point = point_cloud[y_min_idx]
        
        alpha = 0.4
        return alpha * boundary_point + (1 - alpha) * prediction
    
    def _geometric_posterior_boundary(self, prediction, point_cloud):
        """几何后边界约束"""
        
        # 找到Y坐标最大的点 (后方)
        y_max_idx = np.argmax(point_cloud[:, 1])
        boundary_point = point_cloud[y_max_idx]
        
        alpha = 0.4
        return alpha * boundary_point + (1 - alpha) * prediction
    
    def _geometric_left_boundary(self, prediction, point_cloud):
        """几何左边界约束"""
        
        # 找到X坐标最小的点 (左侧)
        x_min_idx = np.argmin(point_cloud[:, 0])
        boundary_point = point_cloud[x_min_idx]
        
        alpha = 0.4
        return alpha * boundary_point + (1 - alpha) * prediction
    
    def _geometric_right_boundary(self, prediction, point_cloud):
        """几何右边界约束"""
        
        # 找到X坐标最大的点 (右侧)
        x_max_idx = np.argmax(point_cloud[:, 0])
        boundary_point = point_cloud[x_max_idx]
        
        alpha = 0.4
        return alpha * boundary_point + (1 - alpha) * prediction
    
    def _geometric_centroid(self, prediction, point_cloud):
        """几何中心约束"""
        
        centroid = np.mean(point_cloud, axis=0)
        
        alpha = 0.3
        return alpha * centroid + (1 - alpha) * prediction
    
    # ==================== 相对位置优化方法 ====================
    
    def _optimize_between_landmarks(self, all_predictions, point_cloud, kp_idx):
        """优化标志间位置 - F3-4"""
        
        # F3-4应该在F3-1和F3-7之间
        f3_1 = all_predictions[0]  # F3-1
        f3_7 = all_predictions[6]  # F3-7
        
        # 计算中点
        midpoint = (f3_1 + f3_7) / 2
        
        alpha = 0.5
        return alpha * midpoint + (1 - alpha) * all_predictions[kp_idx]
    
    def _optimize_proportional_position(self, all_predictions, point_cloud, kp_idx):
        """优化比例位置"""
        
        # 基于相邻关键点的比例关系
        if kp_idx == 5:  # F3-6
            # 在F3-5和F3-7之间的1/3位置
            f3_5 = all_predictions[4]
            f3_7 = all_predictions[6]
            proportional_point = f3_5 + 0.33 * (f3_7 - f3_5)
        elif kp_idx == 14:  # F3-15
            # 在边界之间的中心位置
            f3_14 = all_predictions[13]  # 左边界
            f3_19 = all_predictions[18]  # 右边界
            proportional_point = (f3_14 + f3_19) / 2
        else:
            return all_predictions[kp_idx]
        
        alpha = 0.4
        return alpha * proportional_point + (1 - alpha) * all_predictions[kp_idx]
    
    def _optimize_symmetric_position(self, all_predictions, point_cloud, kp_idx):
        """优化对称位置 - F3-9"""
        
        # F3-9应该与某个点对称
        centroid = np.mean(point_cloud, axis=0)
        
        # 基于中心对称
        alpha = 0.3
        return alpha * centroid + (1 - alpha) * all_predictions[kp_idx]
    
    def _optimize_proportional_spacing(self, all_predictions, point_cloud, kp_idx):
        """优化比例间距 - F3-11"""
        
        # 基于相邻点的等间距原理
        f3_10 = all_predictions[9]
        f3_12 = all_predictions[11]
        
        # 等间距位置
        spacing_point = (f3_10 + f3_12) / 2
        
        alpha = 0.4
        return alpha * spacing_point + (1 - alpha) * all_predictions[kp_idx]

def test_adaptive_system():
    """测试自适应系统"""
    
    print("🔧 测试分层自适应关键点检测系统")
    print("=" * 60)
    
    # 加载数据和模型
    data = np.load('f3_19kp_preprocessed.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints = data['keypoints']
    sample_ids = data['sample_ids']
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 加载基础模型
    model = BasicHeatmapPointNet19(input_dim=3, num_keypoints=19).to(device)
    model.load_state_dict(torch.load('best_fixed_19kp_model.pth', map_location=device))
    model.eval()
    
    # 创建自适应检测器
    adaptive_detector = AdaptiveKeypointDetector(model, device)
    
    print(f"✅ 自适应系统初始化完成")
    print(f"   几何策略关键点: {len(adaptive_detector.keypoint_strategies['geometric'])} 个")
    print(f"   解剖策略关键点: {len(adaptive_detector.keypoint_strategies['anatomical'])} 个")
    print(f"   相对策略关键点: {len(adaptive_detector.keypoint_strategies['relative'])} 个")
    
    # 测试几个样本
    results_baseline = []
    results_adaptive = []
    
    for i in range(min(5, len(point_clouds))):  # 测试前5个样本
        sample_id = sample_ids[i]
        point_cloud = point_clouds[i]
        true_keypoints = keypoints[i]
        
        print(f"\n🔍 测试样本 {sample_id}:")
        
        # 基础预测
        baseline_pred = adaptive_detector._get_base_predictions(point_cloud)
        baseline_errors = [np.linalg.norm(baseline_pred[j] - true_keypoints[j]) for j in range(19)]
        baseline_avg = np.mean(baseline_errors)
        
        # 自适应预测
        adaptive_pred = adaptive_detector.predict_adaptive(point_cloud)
        adaptive_errors = [np.linalg.norm(adaptive_pred[j] - true_keypoints[j]) for j in range(19)]
        adaptive_avg = np.mean(adaptive_errors)
        
        improvement = baseline_avg - adaptive_avg
        improvement_pct = improvement / baseline_avg * 100 if baseline_avg > 0 else 0
        
        print(f"   基础模型: {baseline_avg:.2f}mm")
        print(f"   自适应系统: {adaptive_avg:.2f}mm")
        print(f"   改进: {improvement:.2f}mm ({improvement_pct:.1f}%)")
        
        # 分析各策略的改进
        for strategy, kp_indices in adaptive_detector.keypoint_strategies.items():
            baseline_strategy_errors = [baseline_errors[idx] for idx in kp_indices]
            adaptive_strategy_errors = [adaptive_errors[idx] for idx in kp_indices]
            
            baseline_strategy_avg = np.mean(baseline_strategy_errors)
            adaptive_strategy_avg = np.mean(adaptive_strategy_errors)
            strategy_improvement = baseline_strategy_avg - adaptive_strategy_avg
            
            print(f"     {strategy}: {baseline_strategy_avg:.2f}mm → {adaptive_strategy_avg:.2f}mm "
                  f"({strategy_improvement:+.2f}mm)")
        
        results_baseline.append({
            'sample_id': sample_id,
            'errors': baseline_errors,
            'avg_error': baseline_avg
        })
        
        results_adaptive.append({
            'sample_id': sample_id,
            'errors': adaptive_errors,
            'avg_error': adaptive_avg
        })
    
    # 总体统计
    overall_baseline = np.mean([r['avg_error'] for r in results_baseline])
    overall_adaptive = np.mean([r['avg_error'] for r in results_adaptive])
    overall_improvement = overall_baseline - overall_adaptive
    overall_improvement_pct = overall_improvement / overall_baseline * 100
    
    print(f"\n📊 总体结果:")
    print(f"   基础模型平均: {overall_baseline:.2f}mm")
    print(f"   自适应系统平均: {overall_adaptive:.2f}mm")
    print(f"   总体改进: {overall_improvement:.2f}mm ({overall_improvement_pct:.1f}%)")
    
    return results_baseline, results_adaptive

def main():
    """主函数"""
    print("🚀 分层自适应关键点检测系统")
    print("针对不同标注策略使用不同方法")
    print("=" * 60)
    
    # 测试系统
    results_baseline, results_adaptive = test_adaptive_system()
    
    print(f"\n🎯 系统特点:")
    print("✅ 几何策略: 使用数学约束 (Z最高点、边界等)")
    print("✅ 解剖策略: 使用解剖学先验 (尾骨尖、骶骨中心等)")
    print("✅ 相对策略: 使用空间关系优化 (比例位置、对称等)")
    print("✅ 分层处理: 按稳定性优先级处理")
    print("✅ 自适应权重: 不同策略使用不同约束强度")
    
    print(f"\n💡 下一步改进:")
    print("1. 调优各策略的权重参数")
    print("2. 增加更多解剖学约束")
    print("3. 实现策略间的相互验证")
    print("4. 添加置信度评估机制")

if __name__ == "__main__":
    main()
