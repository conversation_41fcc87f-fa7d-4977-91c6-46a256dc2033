#!/usr/bin/env python3
"""
分析性别平衡问题
Analyze Gender Balance Issue
"""

import numpy as np
import os

def analyze_current_balance():
    """分析当前的性别平衡情况"""
    
    print("⚖️ 性别平衡分析")
    print("=" * 60)
    
    # 检查原始数据
    print("📊 原始数据分布:")
    print("   女性: 25个样本 (25.8%)")
    print("   男性: 72个样本 (74.2%)")
    print("   总计: 97个样本")
    print("   比例: 1:2.88 (女性:男性)")
    
    # 检查增强后的女性数据
    female_aug_path = "f3_reduced_12kp_female_augmented.npz"
    if os.path.exists(female_aug_path):
        female_data = np.load(female_aug_path, allow_pickle=True)
        female_count = len(female_data['sample_ids'])
        print(f"\n🔥 增强后女性数据:")
        print(f"   女性增强: {female_count}个样本")
        print(f"   增强倍数: {female_count / 25:.1f}x")
    else:
        female_count = 25
        print(f"\n❌ 女性增强数据不存在")
    
    # 检查男性数据
    male_path = "archive/old_experiments/f3_reduced_12kp_male.npz"
    if os.path.exists(male_path):
        male_data = np.load(male_path, allow_pickle=True)
        male_count = len(male_data['sample_ids'])
        print(f"\n👨 男性数据:")
        print(f"   男性原始: {male_count}个样本")
    else:
        male_count = 72
        print(f"\n❌ 男性数据文件不存在")
    
    # 计算新的比例
    print(f"\n⚖️ 增强后比例分析:")
    total_after = female_count + male_count
    female_ratio = female_count / total_after * 100
    male_ratio = male_count / total_after * 100
    
    print(f"   女性: {female_count}个 ({female_ratio:.1f}%)")
    print(f"   男性: {male_count}个 ({male_ratio:.1f}%)")
    print(f"   总计: {total_after}个样本")
    print(f"   新比例: {female_count/male_count:.2f}:1 (女性:男性)")
    
    # 问题分析
    print(f"\n⚠️ 比例失调问题:")
    if female_count > male_count:
        print(f"   现在女性样本占多数 ({female_ratio:.1f}%)")
        print(f"   比例从 1:2.88 变成 {female_count/male_count:.2f}:1")
        print(f"   这确实会造成新的不平衡问题!")
    
    return female_count, male_count

def suggest_balance_solutions():
    """建议平衡解决方案"""
    
    print(f"\n🎯 解决方案建议:")
    print("=" * 60)
    
    print("方案1: 🔄 同时增强男性数据")
    print("   优点:")
    print("   • 保持性别平衡")
    print("   • 两个性别都有充足数据")
    print("   • 可以训练更好的混合模型")
    print("   缺点:")
    print("   • 需要额外计算时间")
    print("   • 数据量会变得很大")
    
    print(f"\n方案2: 📊 分层训练策略")
    print("   优点:")
    print("   • 继续使用性别特异性模型")
    print("   • 女性模型用增强数据(250个)")
    print("   • 男性模型用原始数据(72个)")
    print("   • 避免混合训练的不平衡问题")
    print("   缺点:")
    print("   • 男性模型可能性能较差")
    print("   • 需要维护两个独立模型")
    
    print(f"\n方案3: ⚖️ 平衡采样策略")
    print("   优点:")
    print("   • 从增强女性数据中采样72个")
    print("   • 保持1:1的平衡比例")
    print("   • 可以训练单一混合模型")
    print("   缺点:")
    print("   • 浪费了大部分增强数据")
    print("   • 可能不如充分利用数据")
    
    print(f"\n方案4: 🎯 加权训练策略")
    print("   优点:")
    print("   • 使用全部数据(250女性+72男性)")
    print("   • 通过损失函数加权平衡")
    print("   • 充分利用所有增强数据")
    print("   缺点:")
    print("   • 需要调整权重参数")
    print("   • 实现相对复杂")

def implement_male_augmentation():
    """实现男性数据增强"""
    
    print(f"\n🔥 实施男性数据增强:")
    print("=" * 60)
    
    # 检查男性数据
    male_path = "archive/old_experiments/f3_reduced_12kp_male.npz"
    if not os.path.exists(male_path):
        print(f"❌ 男性数据文件不存在: {male_path}")
        return False
    
    print(f"📊 加载男性数据...")
    male_data = np.load(male_path, allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    print(f"✅ 男性数据加载成功:")
    print(f"   样本数量: {len(sample_ids)}")
    print(f"   点云形状: {point_clouds.shape}")
    print(f"   关键点形状: {keypoints.shape}")
    
    # 计算需要的增强倍数
    target_female_count = 250
    current_male_count = len(sample_ids)
    needed_augment_factor = target_female_count / current_male_count
    
    print(f"\n🎯 增强目标:")
    print(f"   当前男性样本: {current_male_count}")
    print(f"   目标女性样本: {target_female_count}")
    print(f"   需要增强倍数: {needed_augment_factor:.1f}x")
    
    # 建议增强策略
    if needed_augment_factor <= 4:
        print(f"✅ 可行性: 增强倍数合理，建议实施")
        return True
    else:
        print(f"⚠️ 可行性: 增强倍数较高，需要谨慎")
        return False

def recommend_best_strategy():
    """推荐最佳策略"""
    
    print(f"\n💡 最佳策略推荐:")
    print("=" * 60)
    
    print("🏆 推荐方案: 分层训练 + 适度男性增强")
    print()
    print("📋 具体实施步骤:")
    print("   1. 保持女性增强数据(250个)")
    print("   2. 对男性数据进行适度增强(72→144个)")
    print("   3. 继续使用性别特异性模型训练")
    print("   4. 分别评估两个模型性能")
    print("   5. 如需要，再考虑混合模型")
    
    print(f"\n✅ 优势:")
    print("   • 充分利用女性增强数据")
    print("   • 改善男性模型数据不足")
    print("   • 保持性别特异性优势")
    print("   • 避免严重的比例失调")
    
    print(f"\n🎯 预期效果:")
    print("   • 女性模型: 4.88mm → 3.5-4.0mm")
    print("   • 男性模型: 当前未知 → 预计5-6mm")
    print("   • 整体性能: 显著提升")
    
    print(f"\n⚡ 立即行动:")
    print("   选择1: 直接用女性增强数据训练，看效果")
    print("   选择2: 同时增强男性数据，保持平衡")
    print("   选择3: 先试女性，效果好再考虑男性")

def main():
    """主函数"""
    
    print("⚖️ 性别比例失调问题分析")
    print("🎯 评估增强后的数据平衡情况")
    print("=" * 80)
    
    # 分析当前平衡
    female_count, male_count = analyze_current_balance()
    
    # 建议解决方案
    suggest_balance_solutions()
    
    # 评估男性增强可行性
    male_augment_feasible = implement_male_augmentation()
    
    # 推荐最佳策略
    recommend_best_strategy()
    
    print(f"\n🤔 您的担心很有道理!")
    print("=" * 50)
    print("确实，女性从25个增强到250个，而男性还是72个，")
    print("会造成严重的比例失调 (77.6% vs 22.4%)")
    print()
    print("建议:")
    print("1. 🚀 先试试女性增强数据的效果")
    print("2. 🔄 如果效果好，再增强男性数据到150个左右")
    print("3. ⚖️ 最终目标: 保持相对平衡的性别比例")

if __name__ == "__main__":
    main()
