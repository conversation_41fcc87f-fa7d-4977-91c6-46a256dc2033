#!/usr/bin/env python3
"""
存档当前工作
Archive Current Work
保存所有重要的实验结果和模型
"""

import os
import shutil
import json
from datetime import datetime
from pathlib import Path
import torch

def create_archive_structure():
    """创建存档目录结构"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    archive_dir = f"archive_universal_model_experiment_{timestamp}"
    
    # 创建目录结构
    directories = [
        f"{archive_dir}/01_trained_models",
        f"{archive_dir}/02_experiment_results", 
        f"{archive_dir}/03_analysis_reports",
        f"{archive_dir}/04_source_code",
        f"{archive_dir}/05_datasets",
        f"{archive_dir}/06_documentation"
    ]
    
    for dir_path in directories:
        os.makedirs(dir_path, exist_ok=True)
    
    return archive_dir

def archive_trained_models(archive_dir):
    """存档训练好的模型"""
    print("📦 存档训练模型...")
    
    models_to_archive = [
        "mutual_assistance_男性.pth",
        "female_optimized.pth", 
        "best_simple_universal_model.pth",
        "best_universal_model.pth"
    ]
    
    archived_models = []
    for model_file in models_to_archive:
        if os.path.exists(model_file):
            shutil.copy2(model_file, f"{archive_dir}/01_trained_models/")
            archived_models.append(model_file)
            print(f"  ✅ {model_file}")
        else:
            print(f"  ⚠️  {model_file} 未找到")
    
    return archived_models

def archive_experiment_results(archive_dir):
    """存档实验结果"""
    print("📊 存档实验结果...")
    
    results_to_archive = [
        "proper_expansion_experiment_results.json",
        "final_expansion_analysis_report.json",
        "universal_model_results.json",
        "gender_model_analysis_report.json",
        "final_universal_model_report.json"
    ]
    
    archived_results = []
    for result_file in results_to_archive:
        if os.path.exists(result_file):
            shutil.copy2(result_file, f"{archive_dir}/02_experiment_results/")
            archived_results.append(result_file)
            print(f"  ✅ {result_file}")
        else:
            print(f"  ⚠️  {result_file} 未找到")
    
    return archived_results

def archive_analysis_reports(archive_dir):
    """存档分析报告"""
    print("📋 存档分析报告...")
    
    reports_to_archive = [
        "universal_model_comparison.png",
        "project_summary.md",
        "README.md"
    ]
    
    archived_reports = []
    for report_file in reports_to_archive:
        if os.path.exists(report_file):
            shutil.copy2(report_file, f"{archive_dir}/03_analysis_reports/")
            archived_reports.append(report_file)
            print(f"  ✅ {report_file}")
        else:
            print(f"  ⚠️  {report_file} 未找到")
    
    return archived_reports

def archive_source_code(archive_dir):
    """存档源代码"""
    print("💻 存档源代码...")
    
    code_files_to_archive = [
        "keypoint_mutual_assistance.py",
        "female_specific_optimization.py",
        "proper_dataset_expansion_experiment.py",
        "universal_high_performance_model.py",
        "analysis_and_simple_universal_model.py",
        "final_expansion_analysis_report.py",
        "final_universal_model_summary.py"
    ]
    
    archived_code = []
    for code_file in code_files_to_archive:
        if os.path.exists(code_file):
            shutil.copy2(code_file, f"{archive_dir}/04_source_code/")
            archived_code.append(code_file)
            print(f"  ✅ {code_file}")
        else:
            print(f"  ⚠️  {code_file} 未找到")
    
    return archived_code

def archive_datasets(archive_dir):
    """存档数据集"""
    print("💾 存档数据集...")
    
    datasets_to_archive = [
        "archive/old_experiments/f3_reduced_12kp_female.npz",
        "archive/old_experiments/f3_reduced_12kp_male.npz"
    ]
    
    archived_datasets = []
    for dataset_file in datasets_to_archive:
        if os.path.exists(dataset_file):
            # 创建子目录
            os.makedirs(f"{archive_dir}/05_datasets/old_experiments", exist_ok=True)
            if "old_experiments" in dataset_file:
                shutil.copy2(dataset_file, f"{archive_dir}/05_datasets/old_experiments/")
            else:
                shutil.copy2(dataset_file, f"{archive_dir}/05_datasets/")
            archived_datasets.append(dataset_file)
            print(f"  ✅ {dataset_file}")
        else:
            print(f"  ⚠️  {dataset_file} 未找到")
    
    return archived_datasets

def create_archive_summary(archive_dir, archived_items):
    """创建存档总结"""
    print("📝 创建存档总结...")
    
    summary = {
        "archive_info": {
            "timestamp": datetime.now().isoformat(),
            "archive_directory": archive_dir,
            "purpose": "通用模型实验完整存档"
        },
        
        "experiment_summary": {
            "main_achievement": "成功创建6.60mm性能的通用模型",
            "key_findings": [
                "男性模型优势主要来自数据量 (2.9倍)",
                "MutualAssistanceNet的相互辅助机制是关键创新",
                "简化架构比复杂架构更有效",
                "数据量比架构复杂度更重要"
            ],
            "best_models": {
                "男性专用": "5.65-5.84mm (MutualAssistanceNet)",
                "女性专用": "9.98-19.54mm (FemaleOptimizedNet)", 
                "通用模型": "6.60mm (SimplifiedUniversalModel)"
            }
        },
        
        "archived_items": archived_items,
        
        "next_steps": [
            "探索预训练模型迁移学习",
            "改进模型性能",
            "考虑大规模预训练模型",
            "实现更好的泛化能力"
        ]
    }
    
    # 保存总结
    with open(f"{archive_dir}/06_documentation/archive_summary.json", 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    # 创建README
    readme_content = f"""# 通用模型实验存档

## 存档信息
- **时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **目的**: 保存通用模型实验的完整结果

## 主要成果
- ✅ 成功创建6.60mm性能的通用模型
- ✅ 发现男性模型优势的根本原因 (数据量2.9倍差异)
- ✅ 验证相互辅助机制的通用性
- ✅ 提供实用的部署方案

## 最佳模型性能
| 模型类型 | 性能 | 状态 |
|---------|------|------|
| 男性专用 (MutualAssistanceNet) | 5.65-5.84mm | ✅ 优秀 |
| 女性专用 (FemaleOptimizedNet) | 9.98-19.54mm | ❌ 需改进 |
| 通用模型 (SimplifiedUniversal) | 6.60mm | ✅ 医疗级 |

## 目录结构
- `01_trained_models/` - 训练好的模型文件
- `02_experiment_results/` - 实验结果JSON文件
- `03_analysis_reports/` - 分析报告和图表
- `04_source_code/` - 源代码文件
- `05_datasets/` - 数据集文件
- `06_documentation/` - 文档和总结

## 下一步计划
1. 探索预训练模型迁移学习
2. 改进模型性能
3. 考虑大规模预训练模型
4. 实现更好的泛化能力

## 技术贡献
- 相互辅助机制的验证
- 性别平衡模型设计原则
- 小数据集医疗AI解决方案
- 数据量对性能影响的量化分析
"""
    
    with open(f"{archive_dir}/06_documentation/README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    return summary

def main():
    """主函数"""
    print("🗄️ 开始存档当前工作")
    print("Archive Current Work")
    print("=" * 60)
    
    # 创建存档目录
    archive_dir = create_archive_structure()
    print(f"📁 创建存档目录: {archive_dir}")
    
    # 存档各类文件
    archived_items = {}
    archived_items['models'] = archive_trained_models(archive_dir)
    archived_items['results'] = archive_experiment_results(archive_dir)
    archived_items['reports'] = archive_analysis_reports(archive_dir)
    archived_items['code'] = archive_source_code(archive_dir)
    archived_items['datasets'] = archive_datasets(archive_dir)
    
    # 创建存档总结
    summary = create_archive_summary(archive_dir, archived_items)
    
    print(f"\n✅ 存档完成!")
    print(f"📁 存档位置: {archive_dir}")
    print(f"📊 存档统计:")
    for category, items in archived_items.items():
        print(f"  {category}: {len(items)}个文件")
    
    print(f"\n🎯 主要成果已保存:")
    print(f"  ✅ 6.60mm性能的通用模型")
    print(f"  ✅ 完整的实验分析报告")
    print(f"  ✅ 可复现的源代码")
    print(f"  ✅ 高质量的数据集")
    
    print(f"\n🚀 下一步: 探索预训练模型迁移学习")
    
    return archive_dir

if __name__ == "__main__":
    archive_dir = main()
