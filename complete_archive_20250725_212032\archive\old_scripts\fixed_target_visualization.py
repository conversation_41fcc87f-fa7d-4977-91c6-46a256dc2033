#!/usr/bin/env python3
"""
修复的靶子风格可视化
解决坐标轴范围、标注大小、点云密度等问题
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps

# 关键点名称
KEYPOINT_NAMES = {
    0: "L-ASIS", 1: "R-ASIS", 2: "L-PSIS", 3: "R-PSIS",
    4: "L-IC", 5: "R-IC", 6: "SP", 7: "L-SIJ", 8: "R-SIJ",
    9: "L-IS", 10: "R-IS", 11: "CT"
}

def create_medical_colormap():
    """创建医学风格的颜色映射"""
    colors = [
        '#F0F8FF',  # 浅蓝白色 (最低置信度)
        '#E6F3FF',  # 很浅蓝色
        '#CCE7FF',  # 浅蓝色
        '#99D6FF',  # 中浅蓝色
        '#66C2FF',  # 中蓝色
        '#33AAFF',  # 深蓝色
        '#0088FF',  # 更深蓝色
        '#FF6600',  # 橙色 (高置信度)
        '#FF3300',  # 红色
        '#FF0000'   # 深红色 (最高置信度)
    ]
    return LinearSegmentedColormap.from_list('medical_heatmap', colors, N=256)

def enhance_heatmap_properly(heatmap, enhancement_factor=2.0):
    """正确增强热图，避免过度处理"""
    
    # 1. 基础增强 - 使用平方根增强对比度
    enhanced = np.sqrt(heatmap)
    
    # 2. 温和的非线性增强
    enhanced = np.power(enhanced, 1.0/enhancement_factor)
    
    # 3. 确保归一化
    if np.max(enhanced) > 0:
        enhanced = enhanced / np.max(enhanced)
    
    # 4. 设置最小阈值，避免噪声
    enhanced = np.where(enhanced > 0.01, enhanced, 0)
    
    return enhanced

def create_fixed_target_visualization(point_cloud, heatmap, true_keypoint, 
                                    pred_keypoint, confidence, kp_idx, sample_id):
    """创建修复的靶子可视化"""
    
    print(f"🔧 Creating fixed target visualization for {KEYPOINT_NAMES[kp_idx]}")
    
    medical_cmap = create_medical_colormap()
    
    fig = plt.figure(figsize=(20, 6))
    
    # 三种增强级别
    enhancement_levels = [1.0, 2.0, 3.0]
    titles = ["Mild Enhancement", "Moderate Enhancement", "Strong Enhancement"]
    
    for i, (enhancement, title) in enumerate(zip(enhancement_levels, titles)):
        ax = fig.add_subplot(1, 3, i+1, projection='3d')
        
        # 增强热图
        enhanced_heatmap = enhance_heatmap_properly(heatmap, enhancement)
        
        # 保持完整点云显示，但让热力图更聚焦
        center = pred_keypoint
        radius = 50  # 恢复完整显示范围
        
        # 筛选显示区域内的点
        distances_to_center = np.linalg.norm(point_cloud - center, axis=1)
        display_mask = distances_to_center < radius
        
        display_pc = point_cloud[display_mask]
        display_heatmap = enhanced_heatmap[display_mask]
        
        # 保持合理的点云密度
        if len(display_pc) > 3000:  # 恢复更多点数
            sample_indices = np.random.choice(len(display_pc), 3000, replace=False)
            display_pc = display_pc[sample_indices]
            display_heatmap = display_heatmap[sample_indices]
        
        # 分层显示
        # 1. 背景点云 - 低置信度，更小的点
        background_mask = display_heatmap < 0.1
        if np.any(background_mask):
            ax.scatter(display_pc[background_mask, 0],
                      display_pc[background_mask, 1],
                      display_pc[background_mask, 2],
                      c='lightgray', s=0.5, alpha=0.2, rasterized=True)
        
        # 2. 中等置信度区域，更小的点
        medium_mask = (display_heatmap >= 0.1) & (display_heatmap < 0.5)
        if np.any(medium_mask):
            ax.scatter(display_pc[medium_mask, 0],
                      display_pc[medium_mask, 1],
                      display_pc[medium_mask, 2],
                      c=display_heatmap[medium_mask],
                      cmap=medical_cmap, s=2, alpha=0.8, vmin=0, vmax=1)
        
        # 3. 高置信度区域 - 靶子中心，更突出
        high_mask = display_heatmap >= 0.5
        if np.any(high_mask):
            scatter = ax.scatter(display_pc[high_mask, 0],
                               display_pc[high_mask, 1],
                               display_pc[high_mask, 2],
                               c=display_heatmap[high_mask],
                               cmap=medical_cmap, s=6, alpha=0.9, vmin=0, vmax=1)
        
        # 4. 峰值点 - 靶心
        peak_mask = display_heatmap >= 0.8
        if np.any(peak_mask):
            ax.scatter(display_pc[peak_mask, 0],
                      display_pc[peak_mask, 1],
                      display_pc[peak_mask, 2],
                      c='red', s=20, marker='o', 
                      edgecolor='white', linewidth=1, alpha=1.0)
        
        # 5. 关键点标记 - 显著增大
        # 真实关键点 - 大黑星
        ax.scatter(true_keypoint[0], true_keypoint[1], true_keypoint[2],
                  c='black', s=300, marker='*', edgecolor='yellow', 
                  linewidth=3, alpha=1.0, label='Ground Truth', zorder=10)
        
        # 预测关键点 - 大黄圆
        ax.scatter(pred_keypoint[0], pred_keypoint[1], pred_keypoint[2],
                  c='yellow', s=200, marker='o', edgecolor='black', 
                  linewidth=3, alpha=1.0, label='Predicted', zorder=10)
        
        # 误差线 - 加粗
        ax.plot([pred_keypoint[0], true_keypoint[0]],
               [pred_keypoint[1], true_keypoint[1]],
               [pred_keypoint[2], true_keypoint[2]],
               color='red', linestyle='-', linewidth=4, alpha=0.8, zorder=5)
        
        # 计算误差
        error = np.linalg.norm(pred_keypoint - true_keypoint)
        
        # 设置坐标轴范围 - 确保所有内容都在框内
        margin = 5  # 增加边距
        ax.set_xlim([center[0] - radius - margin, center[0] + radius + margin])
        ax.set_ylim([center[1] - radius - margin, center[1] + radius + margin])
        ax.set_zlim([center[2] - radius - margin, center[2] + radius + margin])
        
        # 设置标题 - 增大字体
        ax.set_title(f'{title}\n{KEYPOINT_NAMES[kp_idx]}\n'
                    f'Confidence: {confidence:.3f}\nError: {error:.1f}mm',
                    fontsize=14, fontweight='bold', pad=20)
        
        # 设置坐标轴标签 - 增大字体
        ax.set_xlabel('X (mm)', fontsize=12, labelpad=10)
        ax.set_ylabel('Y (mm)', fontsize=12, labelpad=10)
        ax.set_zlabel('Z (mm)', fontsize=12, labelpad=10)
        ax.tick_params(labelsize=10)
        
        # 设置视角
        if kp_idx in [0, 1]:  # ASIS
            ax.view_init(elev=30, azim=45)
        elif kp_idx == 6:     # SP
            ax.view_init(elev=30, azim=225)
        else:
            ax.view_init(elev=45, azim=0)
        
        # 添加颜色条
        if i == 2 and (np.any(medium_mask) or np.any(high_mask)):
            cbar = plt.colorbar(scatter, ax=ax, shrink=0.8, pad=0.1)
            cbar.set_label('Confidence Level', fontsize=12)
            cbar.ax.tick_params(labelsize=10)
        
        # 图例 - 只在第一个子图显示
        if i == 0:
            ax.legend(fontsize=12, loc='upper right')
        
        # 添加网格
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息
        max_conf = np.max(display_heatmap)
        high_conf_points = np.sum(display_heatmap > 0.5)
        total_points = len(display_heatmap)
        
        stats_text = f'Max Conf: {max_conf:.3f}\nHigh Conf Points: {high_conf_points}\nTotal Points: {total_points}'
        ax.text2D(0.02, 0.98, stats_text, transform=ax.transAxes, 
                 fontsize=11, verticalalignment='top',
                 bbox=dict(boxstyle='round', facecolor='white', alpha=0.9))
    
    plt.tight_layout()
    
    save_path = f'fixed_target_{sample_id}_kp{kp_idx}_{KEYPOINT_NAMES[kp_idx]}.png'
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"   📊 Fixed target visualization saved: {save_path}")
    plt.close()

def create_clean_overview(point_cloud, pred_heatmaps, true_keypoints, 
                         pred_keypoints, confidences, sample_id):
    """创建清洁的概览图"""
    
    print(f"🧹 Creating clean overview for sample {sample_id}")
    
    medical_cmap = create_medical_colormap()
    
    # 只显示6个最重要的关键点
    important_keypoints = [0, 1, 2, 3, 6, 11]  # L-ASIS, R-ASIS, L-PSIS, R-PSIS, SP, CT
    
    fig = plt.figure(figsize=(18, 12))
    
    for idx, kp_idx in enumerate(important_keypoints):
        ax = fig.add_subplot(2, 3, idx+1, projection='3d')
        
        heatmap = pred_heatmaps[:, kp_idx]
        enhanced_heatmap = enhance_heatmap_properly(heatmap, 2.5)
        
        # 计算局部显示区域
        center = pred_keypoints[kp_idx]
        radius = 20
        
        distances = np.linalg.norm(point_cloud - center, axis=1)
        local_mask = distances < radius
        
        local_pc = point_cloud[local_mask]
        local_heatmap = enhanced_heatmap[local_mask]
        
        # 减少点密度
        if len(local_pc) > 1500:
            sample_indices = np.random.choice(len(local_pc), 1500, replace=False)
            local_pc = local_pc[sample_indices]
            local_heatmap = local_heatmap[sample_indices]
        
        # 背景点云
        background_mask = local_heatmap < 0.2
        if np.any(background_mask):
            ax.scatter(local_pc[background_mask, 0],
                      local_pc[background_mask, 1],
                      local_pc[background_mask, 2],
                      c='lightgray', s=0.5, alpha=0.4)
        
        # 热图区域
        heatmap_mask = local_heatmap >= 0.2
        if np.any(heatmap_mask):
            scatter = ax.scatter(local_pc[heatmap_mask, 0],
                               local_pc[heatmap_mask, 1],
                               local_pc[heatmap_mask, 2],
                               c=local_heatmap[heatmap_mask],
                               cmap=medical_cmap, s=4, alpha=0.8, vmin=0, vmax=1)
        
        # 峰值点
        peak_mask = local_heatmap >= 0.7
        if np.any(peak_mask):
            ax.scatter(local_pc[peak_mask, 0],
                      local_pc[peak_mask, 1],
                      local_pc[peak_mask, 2],
                      c='red', s=15, marker='o', 
                      edgecolor='white', linewidth=1)
        
        # 关键点标记
        ax.scatter(true_keypoints[kp_idx, 0], true_keypoints[kp_idx, 1], 
                  true_keypoints[kp_idx, 2], c='black', s=150, marker='*', 
                  edgecolor='yellow', linewidth=2, alpha=1.0)
        
        ax.scatter(pred_keypoints[kp_idx, 0], pred_keypoints[kp_idx, 1], 
                  pred_keypoints[kp_idx, 2], c='yellow', s=100, marker='o', 
                  edgecolor='black', linewidth=2, alpha=1.0)
        
        # 误差线
        ax.plot([pred_keypoints[kp_idx, 0], true_keypoints[kp_idx, 0]],
               [pred_keypoints[kp_idx, 1], true_keypoints[kp_idx, 1]],
               [pred_keypoints[kp_idx, 2], true_keypoints[kp_idx, 2]],
               color='red', linestyle='-', linewidth=3, alpha=0.8)
        
        # 计算误差
        error = np.linalg.norm(pred_keypoints[kp_idx] - true_keypoints[kp_idx])
        
        # 设置范围
        margin = 3
        ax.set_xlim([center[0] - radius - margin, center[0] + radius + margin])
        ax.set_ylim([center[1] - radius - margin, center[1] + radius + margin])
        ax.set_zlim([center[2] - radius - margin, center[2] + radius + margin])
        
        # 标题
        ax.set_title(f'{KEYPOINT_NAMES[kp_idx]}\nConf: {confidences[kp_idx]:.3f}\nError: {error:.1f}mm',
                    fontsize=12, fontweight='bold')
        
        # 坐标轴
        ax.set_xlabel('X', fontsize=10)
        ax.set_ylabel('Y', fontsize=10)
        ax.set_zlabel('Z', fontsize=10)
        ax.tick_params(labelsize=8)
        
        # 视角
        ax.view_init(elev=30, azim=45)
        ax.grid(True, alpha=0.3)
    
    # 添加全局颜色条
    if 'scatter' in locals():
        cbar_ax = fig.add_axes([0.92, 0.15, 0.02, 0.7])
        cbar = plt.colorbar(scatter, cax=cbar_ax)
        cbar.set_label('Confidence Level', fontsize=12)
    
    plt.suptitle(f'Clean Target-Style Heatmap Overview - Sample {sample_id}\n'
                f'Medical-Grade Visualization with Proper Scaling', 
                fontsize=16, fontweight='bold')
    
    plt.tight_layout(rect=[0, 0, 0.9, 0.94])
    
    save_path = f'clean_target_overview_{sample_id}.png'
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"   📊 Clean overview saved: {save_path}")
    plt.close()

def main():
    """主函数"""
    print("🔧 Fixed Target-Style Heatmap Visualization")
    print("Solving coordinate range, annotation size, and point density issues")
    print("=" * 80)
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model.eval()
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # 选择一个样本进行详细展示
    sample_idx = 0
    sample_id = sample_ids[sample_idx]
    point_cloud = point_clouds[sample_idx]
    true_keypoints = keypoints[sample_idx]
    
    print(f"\n🎯 Processing sample: {sample_id}")
    
    # 采样点云
    if len(point_cloud) > 8192:
        indices = np.random.choice(len(point_cloud), 8192, replace=False)
        pc_sampled = point_cloud[indices]
    else:
        pc_sampled = point_cloud
    
    # 预测热图
    pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
    
    with torch.no_grad():
        pred_heatmaps = model(pc_tensor)
    
    pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T  # [N, 12]
    pred_keypoints, confidences = extract_keypoints_from_heatmaps(
        pred_heatmaps_np.T, pc_sampled
    )
    
    # 计算误差
    errors = []
    for i in range(12):
        error = np.linalg.norm(pred_keypoints[i] - true_keypoints[i])
        errors.append(error)
    
    # 创建修复的可视化
    # 选择几个有代表性的关键点
    demo_keypoints = [0, 6, 11]  # L-ASIS, SP, CT
    
    for kp_idx in demo_keypoints:
        create_fixed_target_visualization(
            pc_sampled, pred_heatmaps_np[:, kp_idx], 
            true_keypoints[kp_idx], pred_keypoints[kp_idx],
            confidences[kp_idx], kp_idx, sample_id
        )
    
    # 创建清洁的概览图
    create_clean_overview(
        pc_sampled, pred_heatmaps_np, true_keypoints,
        pred_keypoints, confidences, sample_id
    )
    
    print(f"\n🎉 Fixed Target Visualization Complete!")
    print(f"✅ Proper coordinate ranges")
    print(f"✅ Large, visible annotations")
    print(f"✅ Reduced point cloud density")
    print(f"✅ Medical-grade color scheme")
    print(f"✅ Clear visual hierarchy")
    
    print(f"\n📊 Performance Summary:")
    print(f"   Average error: {np.mean(errors):.2f}mm")
    print(f"   Average confidence: {np.mean(confidences):.3f}")

if __name__ == "__main__":
    main()
