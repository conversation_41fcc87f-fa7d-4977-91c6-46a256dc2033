#!/usr/bin/env python3
"""
从原始数据构建57关键点数据集
Build 57 keypoints dataset from raw data in /home/<USER>/pjc/GCN/data/Data
"""

import numpy as np
import pandas as pd
import os
from pathlib import Path
import json

def load_57_keypoints_from_raw(sample_id, data_dir):
    """从原始CSV文件加载57个关键点"""
    
    annotations_dir = data_dir / "annotations"
    ann_file = annotations_dir / f"{sample_id}-Table-XYZ.CSV"
    
    if not ann_file.exists():
        return None, f"标注文件不存在: {ann_file}"
    
    try:
        # 尝试不同编码
        for encoding in ['gbk', 'utf-8', 'latin1']:
            try:
                df = pd.read_csv(ann_file, encoding=encoding)
                break
            except:
                continue
        else:
            return None, "无法读取CSV文件"
        
        if len(df) < 57:
            return None, f"关键点数量不足: {len(df)} < 57"
        
        # 提取57个关键点坐标
        keypoints_57 = []
        for idx, row in df.iterrows():
            if idx >= 57:  # 只取前57个
                break
            x, y, z = row['X'], row['Y'], row['Z']
            keypoints_57.append([x, y, z])
        
        return np.array(keypoints_57), "成功"
        
    except Exception as e:
        return None, f"处理失败: {e}"

def load_point_cloud_from_stl(sample_id, data_dir):
    """从STL文件生成点云"""
    
    stl_dir = data_dir / "stl_models"
    
    try:
        import open3d as o3d
        
        combined_points = []
        regions_loaded = []
        
        # 加载F1, F2, F3三个STL文件
        for region in ['F_1', 'F_2', 'F_3']:
            stl_file = stl_dir / f"{sample_id}-{region}.stl"
            
            if stl_file.exists():
                try:
                    mesh = o3d.io.read_triangle_mesh(str(stl_file))
                    
                    if len(mesh.vertices) > 0:
                        # 从mesh采样点云
                        pcd = mesh.sample_points_uniformly(number_of_points=15000)
                        points = np.asarray(pcd.points)
                        combined_points.append(points)
                        regions_loaded.append(region)
                except Exception as e:
                    print(f"⚠️ {sample_id}-{region}: STL加载失败 - {e}")
                    continue
        
        if combined_points:
            # 合并所有区域的点云
            all_points = np.vstack(combined_points)
            
            # 调整到50000个点
            if len(all_points) > 50000:
                indices = np.random.choice(len(all_points), 50000, replace=False)
                all_points = all_points[indices]
            elif len(all_points) < 30000:
                # 如果点太少，进行上采样
                indices = np.random.choice(len(all_points), 50000, replace=True)
                all_points = all_points[indices]
            
            return all_points, f"成功加载{len(regions_loaded)}个区域: {regions_loaded}"
        else:
            return None, "没有成功加载任何STL文件"
            
    except ImportError:
        return None, "Open3D未安装"
    except Exception as e:
        return None, f"点云生成失败: {e}"

def extract_12_from_57(keypoints_57):
    """从57个关键点中提取12个核心关键点"""
    
    # 12点到57点的映射关系
    mapping_12_to_57 = {
        0: 0,   # F1-1 -> 原始索引0
        1: 1,   # F1-2 -> 原始索引1
        2: 2,   # F1-3 -> 原始索引2
        3: 12,  # F1-13 -> 原始索引12
        4: 19,  # F2-1 -> 原始索引19
        5: 20,  # F2-2 -> 原始索引20
        6: 21,  # F2-3 -> 原始索引21
        7: 31,  # F2-13 -> 原始索引31
        8: 38,  # F3-1 -> 原始索引38
        9: 52,  # F3-15 -> 原始索引52
        10: 50, # F3-13 -> 原始索引50
        11: 51, # F3-14 -> 原始索引51
    }
    
    keypoints_12 = np.zeros((12, 3))
    
    for i in range(12):
        original_idx = mapping_12_to_57[i]
        if original_idx < len(keypoints_57):
            keypoints_12[i] = keypoints_57[original_idx]
    
    return keypoints_12

def build_57_dataset_from_raw():
    """从原始数据构建57关键点数据集"""
    
    print("🚀 从原始数据构建57关键点数据集...")
    print("=" * 60)
    
    data_dir = Path("/home/<USER>/pjc/GCN/data/Data")
    annotations_dir = data_dir / "annotations"
    
    print(f"📁 数据目录: {data_dir}")
    print(f"📁 标注目录: {annotations_dir}")
    
    if not data_dir.exists():
        print(f"❌ 数据目录不存在: {data_dir}")
        return None
    
    if not annotations_dir.exists():
        print(f"❌ 标注目录不存在: {annotations_dir}")
        return None
    
    # 获取所有标注文件
    annotation_files = list(annotations_dir.glob("*-Table-XYZ.CSV"))
    print(f"📋 找到 {len(annotation_files)} 个标注文件")
    
    if len(annotation_files) == 0:
        print("❌ 没有找到标注文件")
        return None
    
    # 准备数据容器
    valid_samples = []
    point_clouds_list = []
    keypoints_57_list = []
    keypoints_12_list = []
    sample_ids_list = []
    processing_log = []
    
    # 限制处理数量进行测试
    max_samples = 20
    print(f"🎯 测试模式: 处理前 {max_samples} 个样本")
    
    # 处理每个样本
    for i, ann_file in enumerate(annotation_files[:max_samples]):
        sample_id = ann_file.stem.split('-')[0]
        
        print(f"\n📋 处理样本 {i+1}/{max_samples}: {sample_id}")
        
        # 加载57个关键点
        keypoints_57, kp_msg = load_57_keypoints_from_raw(sample_id, data_dir)
        
        if keypoints_57 is None:
            print(f"❌ 关键点加载失败: {kp_msg}")
            processing_log.append((sample_id, "失败", f"关键点: {kp_msg}"))
            continue
        
        print(f"✅ 关键点加载成功: {keypoints_57.shape}")
        
        # 加载点云
        point_cloud, pc_msg = load_point_cloud_from_stl(sample_id, data_dir)
        
        if point_cloud is None:
            print(f"❌ 点云加载失败: {pc_msg}")
            processing_log.append((sample_id, "失败", f"点云: {pc_msg}"))
            continue
        
        print(f"✅ 点云加载成功: {point_cloud.shape}")
        
        # 提取12个核心关键点
        keypoints_12 = extract_12_from_57(keypoints_57)
        print(f"✅ 12关键点提取成功: {keypoints_12.shape}")
        
        # 验证数据质量
        if point_cloud.shape[0] < 10000:
            print(f"⚠️ 点云太小: {point_cloud.shape[0]} < 10000")
            processing_log.append((sample_id, "失败", "点云太小"))
            continue
        
        if keypoints_57.shape[0] != 57:
            print(f"⚠️ 关键点数量错误: {keypoints_57.shape[0]} != 57")
            processing_log.append((sample_id, "失败", "关键点数量错误"))
            continue
        
        # 添加到有效样本
        valid_samples.append(sample_id)
        point_clouds_list.append(point_cloud)
        keypoints_57_list.append(keypoints_57)
        keypoints_12_list.append(keypoints_12)
        sample_ids_list.append(sample_id)
        
        processing_log.append((sample_id, "成功", f"点云{point_cloud.shape}, 57点{keypoints_57.shape}"))
        print(f"✅ {sample_id}: 处理完成")
    
    # 统计结果
    print(f"\n📊 处理统计:")
    print(f"   总处理数: {len(annotation_files[:max_samples])}")
    print(f"   成功数: {len(valid_samples)}")
    print(f"   失败数: {len(annotation_files[:max_samples]) - len(valid_samples)}")
    print(f"   成功率: {len(valid_samples)/len(annotation_files[:max_samples])*100:.1f}%")
    
    if len(valid_samples) == 0:
        print("❌ 没有有效样本")
        return None
    
    # 转换为numpy数组
    point_clouds_array = np.array(point_clouds_list, dtype=object)
    keypoints_57_array = np.array(keypoints_57_list)
    keypoints_12_array = np.array(keypoints_12_list)
    sample_ids_array = np.array(sample_ids_list)
    
    print(f"\n📋 数据集信息:")
    print(f"   有效样本: {len(valid_samples)}")
    print(f"   点云数组: {len(point_clouds_array)} 个样本")
    print(f"   57关键点: {keypoints_57_array.shape}")
    print(f"   12关键点: {keypoints_12_array.shape}")
    
    # 保存数据集
    print(f"\n💾 保存数据集...")
    
    np.savez('raw_57_dataset.npz',
             point_clouds=point_clouds_array,
             keypoints_57=keypoints_57_array,
             keypoints_12=keypoints_12_array,
             sample_ids=sample_ids_array)
    
    print(f"✅ 数据集已保存: raw_57_dataset.npz")
    
    # 保存处理日志
    log_data = {
        'processing_log': processing_log,
        'valid_samples': valid_samples,
        'mapping_12_to_57': {
            0: 0, 1: 1, 2: 2, 3: 12, 4: 19, 5: 20,
            6: 21, 7: 31, 8: 38, 9: 52, 10: 50, 11: 51
        },
        'dataset_info': {
            'total_samples': len(valid_samples),
            'keypoints_57': 57,
            'keypoints_12': 12,
            'data_source': str(data_dir)
        }
    }
    
    with open('raw_57_dataset_log.json', 'w', encoding='utf-8') as f:
        json.dump(log_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 处理日志已保存: raw_57_dataset_log.json")
    
    return point_clouds_array, keypoints_57_array, keypoints_12_array, sample_ids_array

def validate_raw_57_dataset():
    """验证原始57关键点数据集"""
    
    print(f"\n🔍 验证原始57关键点数据集...")
    print("=" * 50)
    
    try:
        data = np.load('raw_57_dataset.npz', allow_pickle=True)
        
        point_clouds = data['point_clouds']
        keypoints_57 = data['keypoints_57']
        keypoints_12 = data['keypoints_12']
        sample_ids = data['sample_ids']
        
        print(f"📊 数据集基本信息:")
        print(f"   样本数量: {len(sample_ids)}")
        print(f"   57关键点形状: {keypoints_57.shape}")
        print(f"   12关键点形状: {keypoints_12.shape}")
        
        # 验证12点提取的正确性
        print(f"\n✅ 12关键点提取验证:")
        
        if len(sample_ids) > 0:
            sample_id = sample_ids[0]
            kp_57 = keypoints_57[0]
            kp_12 = keypoints_12[0]
            
            print(f"   样本 {sample_id}:")
            
            # 验证关键映射
            mappings_to_check = [
                (0, 0, "F1-1"),
                (3, 12, "F1-13"),
                (4, 19, "F2-1"),
                (8, 38, "F3-1")
            ]
            
            all_correct = True
            for idx_12, idx_57, name in mappings_to_check:
                match = np.allclose(kp_12[idx_12], kp_57[idx_57], atol=1e-6)
                print(f"     {name}: 12点[{idx_12}] vs 57点[{idx_57}] = {match}")
                if not match:
                    print(f"       12点: {kp_12[idx_12]}")
                    print(f"       57点: {kp_57[idx_57]}")
                    all_correct = False
            
            if all_correct:
                print(f"   ✅ 所有映射验证通过")
            else:
                print(f"   ⚠️ 部分映射验证失败")
        
        # 检查坐标范围
        print(f"\n📏 坐标范围检查:")
        for axis, axis_name in enumerate(['X', 'Y', 'Z']):
            min_val = keypoints_57[:, :, axis].min()
            max_val = keypoints_57[:, :, axis].max()
            range_val = max_val - min_val
            print(f"   {axis_name}轴: {min_val:.1f} ~ {max_val:.1f} (范围: {range_val:.1f})")
        
        # 检查点云质量
        print(f"\n☁️ 点云质量检查:")
        pc_sizes = [len(pc) for pc in point_clouds]
        print(f"   点云大小范围: {min(pc_sizes)} ~ {max(pc_sizes)}")
        print(f"   平均点云大小: {np.mean(pc_sizes):.0f}")
        
        print(f"\n✅ 数据集验证完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据集验证失败: {e}")
        return False

def create_expansion_training_data():
    """创建12→57扩展训练数据"""
    
    print(f"\n🔄 创建12→57扩展训练数据...")
    print("=" * 50)
    
    try:
        data = np.load('raw_57_dataset.npz', allow_pickle=True)
        
        point_clouds = data['point_clouds']
        keypoints_57 = data['keypoints_57']
        keypoints_12 = data['keypoints_12']
        sample_ids = data['sample_ids']
        
        print(f"📊 训练数据信息:")
        print(f"   输入: 12个关键点 {keypoints_12.shape}")
        print(f"   目标: 57个关键点 {keypoints_57.shape}")
        print(f"   样本数: {len(sample_ids)}")
        
        # 保存扩展训练数据
        np.savez('expansion_training_data.npz',
                 input_12=keypoints_12,
                 target_57=keypoints_57,
                 point_clouds=point_clouds,
                 sample_ids=sample_ids)
        
        print(f"✅ 扩展训练数据已保存: expansion_training_data.npz")
        
        # 分析扩展任务的复杂度
        print(f"\n📈 扩展任务复杂度分析:")
        
        # 计算关键点分布
        print(f"   维度扩展: {12*3} → {57*3} ({57*3/(12*3):.1f}倍)")
        
        # 分析各区域的关键点密度
        regions = {
            'F1': (0, 19),
            'F2': (19, 38),
            'F3': (38, 57)
        }
        
        for region_name, (start, end) in regions.items():
            region_points = keypoints_57[:, start:end, :]
            
            # 计算区域内平均距离
            distances = []
            for sample_kp in region_points[:3]:  # 只计算前3个样本
                for i in range(len(sample_kp)):
                    for j in range(i+1, min(i+5, len(sample_kp))):
                        dist = np.linalg.norm(sample_kp[i] - sample_kp[j])
                        distances.append(dist)
            
            if distances:
                avg_dist = np.mean(distances)
                print(f"   {region_name}区域平均距离: {avg_dist:.2f}mm")
        
        return True
        
    except Exception as e:
        print(f"❌ 扩展训练数据创建失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🎯 从原始数据构建57关键点数据集")
    print("数据源: /home/<USER>/pjc/GCN/data/Data")
    print("=" * 80)
    
    # 步骤1: 从原始数据构建数据集
    result = build_57_dataset_from_raw()
    
    if result is not None:
        print(f"\n🎉 数据集构建成功！")
        
        # 步骤2: 验证数据集
        if validate_raw_57_dataset():
            # 步骤3: 创建扩展训练数据
            if create_expansion_training_data():
                print(f"\n🎉 57关键点数据集完整构建完成！")
                print(f"📋 生成的文件:")
                print(f"   - raw_57_dataset.npz (完整57点数据集)")
                print(f"   - raw_57_dataset_log.json (处理日志)")
                print(f"   - expansion_training_data.npz (12→57训练数据)")
                
                print(f"\n🚀 下一步:")
                print(f"   1. 实现12→57扩展网络")
                print(f"   2. 训练扩展模型")
                print(f"   3. 评估扩展效果")
                print(f"   4. 端到端57点模型训练")
                
                print(f"\n💡 如果测试结果满意，可以:")
                print(f"   - 修改 max_samples 处理更多样本")
                print(f"   - 开始实现扩展网络架构")
            else:
                print(f"❌ 扩展训练数据创建失败")
        else:
            print(f"❌ 数据集验证失败")
    else:
        print(f"❌ 数据集构建失败")

if __name__ == "__main__":
    main()
