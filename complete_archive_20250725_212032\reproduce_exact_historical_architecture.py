#!/usr/bin/env python3
"""
精确复现历史架构
Reproduce Exact Historical Architecture
基于代码库搜索结果，精确复现5.371mm的历史最佳架构
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import json
from tqdm import tqdm

class ExactDoubleSoftMax(nn.Module):
    """精确复制的双Softmax机制"""
    
    def __init__(self, threshold_ratio=0.15, temperature=2.0, weight_ratio=0.8):
        super(ExactDoubleSoftMax, self).__init__()
        
        self.threshold_ratio = threshold_ratio
        self.temperature = temperature
        self.weight_ratio = weight_ratio
        
        # 精确复制的权重计算网络
        self.weight_net = nn.Sequential(
            nn.Linear(3, 64),
            nn.ReLU(),
            nn.BatchNorm1d(64),
            nn.<PERSON>ar(64, 32),
            nn.<PERSON>L<PERSON>(),
            nn.<PERSON><PERSON><PERSON>orm1d(32),
            nn.<PERSON>ar(32, 16),
            nn.<PERSON>L<PERSON>(),
            nn.Linear(16, 1)
        )
        
    def forward(self, points, predicted_keypoint):
        """精确复制的双Softmax权重计算"""
        # 计算相对位置
        relative_pos = points - predicted_keypoint.unsqueeze(0)
        
        # 第一个Softmax - 基于距离的权重
        distances = torch.norm(relative_pos, dim=1)
        distance_weights = F.softmax(-distances**2 / (2 * self.temperature**2), dim=0)
        
        # 第二个Softmax - 基于神经网络的权重
        if len(relative_pos) > 1:
            nn_weights = self.weight_net(relative_pos).squeeze(-1)
            nn_weights = F.softmax(nn_weights / self.temperature, dim=0)
        else:
            nn_weights = torch.ones_like(distance_weights)
        
        # 阈值过滤
        threshold = torch.quantile(distances, self.threshold_ratio)
        mask = distances <= threshold
        
        if mask.sum() > 0:
            filtered_distance_weights = torch.zeros_like(distance_weights)
            filtered_nn_weights = torch.zeros_like(nn_weights)
            
            filtered_distance_weights[mask] = distance_weights[mask]
            filtered_nn_weights[mask] = nn_weights[mask]
            
            # 重新归一化
            if filtered_distance_weights.sum() > 0:
                filtered_distance_weights = filtered_distance_weights / filtered_distance_weights.sum()
            if filtered_nn_weights.sum() > 0:
                filtered_nn_weights = filtered_nn_weights / filtered_nn_weights.sum()
        else:
            filtered_distance_weights = distance_weights
            filtered_nn_weights = nn_weights
        
        # 加权组合
        final_weights = (self.weight_ratio * filtered_distance_weights + 
                        (1 - self.weight_ratio) * filtered_nn_weights)
        
        # 加权平均得到精细化关键点
        refined_keypoint = torch.sum(final_weights.unsqueeze(-1) * points, dim=0)
        
        return refined_keypoint

class ExactEnsembleDoubleSoftMaxPointNet(nn.Module):
    """精确复制的集成双Softmax PointNet - 历史5.371mm架构"""
    
    def __init__(self, num_keypoints=12, dropout_rate=0.3, num_ensembles=3):
        super(ExactEnsembleDoubleSoftMaxPointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        self.num_ensembles = num_ensembles
        
        # 精确复制的基线架构
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 残差连接
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        # 全连接层 - 精确匹配历史架构
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(dropout_rate)
        
        # 精确复制的集成双Softmax (3个模块)
        self.double_softmax_modules = nn.ModuleList([
            ExactDoubleSoftMax(
                threshold_ratio=0.10 + 0.05 * i,    # 0.10, 0.15, 0.20
                temperature=1.5 + 0.5 * i,          # 1.5, 2.0, 2.5
                weight_ratio=0.7 + 0.1 * i          # 0.7, 0.8, 0.9
            ) for i in range(num_ensembles)
        ])
        
        # 权重初始化
        self._initialize_weights()
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"🏗️ ExactEnsembleDoubleSoftMaxPointNet: {total_params:,} 参数")
        print(f"   关键点数: {num_keypoints}")
        print(f"   集成模块: {num_ensembles}")
        print(f"   目标性能: 5.371mm")
    
    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取 + 残差连接
        x1 = F.relu(self.bn1(self.conv1(x)))
        x2 = F.relu(self.bn2(self.conv2(x1)))
        x3 = F.relu(self.bn3(self.conv3(x2)))
        x3_res = x3 + self.residual1(x1)
        
        x4 = F.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = F.relu(self.bn5(self.conv5(x4_res)))
        
        # 全局最大池化
        global_feat = torch.max(x5, 2)[0]  # [B, 1024]
        
        # 回归头
        feat = F.relu(self.bn_fc1(self.fc1(global_feat)))
        feat = self.dropout(feat)
        feat = F.relu(self.bn_fc2(self.fc2(feat)))
        feat = self.dropout(feat)
        feat = F.relu(self.bn_fc3(self.fc3(feat)))
        feat = self.dropout(feat)
        feat = F.relu(self.bn_fc4(self.fc4(feat)))
        feat = self.dropout(feat)
        feat = self.fc5(feat)
        
        keypoints = feat.view(batch_size, self.num_keypoints, 3)
        
        # 推理时应用集成双Softmax精细化
        if not self.training:
            keypoints = self.apply_ensemble_double_softmax_refinement(x, keypoints)
        
        return keypoints
    
    def apply_ensemble_double_softmax_refinement(self, points, predicted_keypoints):
        """应用集成双Softmax精细化"""
        batch_size = points.shape[0]
        refined_keypoints = []
        
        for b in range(batch_size):
            batch_points = points[b].transpose(0, 1)  # [N, 3]
            batch_predicted = predicted_keypoints[b]  # [num_keypoints, 3]
            
            batch_refined = []
            for k in range(self.num_keypoints):
                keypoint_refined_ensemble = []
                
                # 对每个集成模块应用双Softmax
                for softmax_module in self.double_softmax_modules:
                    refined_kp = softmax_module(batch_points, batch_predicted[k])
                    keypoint_refined_ensemble.append(refined_kp)
                
                # 集成平均
                ensemble_keypoint = torch.stack(keypoint_refined_ensemble).mean(dim=0)
                batch_refined.append(ensemble_keypoint)
            
            refined_keypoints.append(torch.stack(batch_refined))
        
        return torch.stack(refined_keypoints)

class ExactLoss(nn.Module):
    """精确复制的损失函数"""
    
    def __init__(self, alpha=0.8, beta=0.2):
        super(ExactLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, pred, target):
        mse_loss = F.mse_loss(pred, target)
        smooth_l1_loss = F.smooth_l1_loss(pred, target)
        total_loss = self.alpha * mse_loss + self.beta * smooth_l1_loss
        return total_loss

class Historical12Dataset(Dataset):
    def __init__(self, point_clouds, keypoints_12):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints_12)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def create_historical_12point_subset():
    """创建历史12点子集 - 基于f3_reduced_12kp_stable的选择"""
    # 基于代码库搜索结果，历史最佳使用的12点索引
    historical_12_indices = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16, 17]
    
    print(f"🏥 历史12点子集 (f3_reduced_12kp_stable):")
    print(f"   索引: {historical_12_indices}")
    print(f"   策略: 保留最稳定的12个关键点")
    print(f"   减少: 37% (从19点到12点)")
    
    return historical_12_indices

def train_exact_historical_model(model, train_loader, val_loader, epochs=150, device='cuda'):
    """训练精确历史模型"""
    
    print(f"🚀 训练精确历史架构模型...")
    print(f"   目标: 复现5.371mm性能")
    
    model = model.to(device)
    
    # 精确复制的训练配置
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=12, min_lr=1e-6
    )
    
    criterion = ExactLoss(alpha=0.8, beta=0.2)
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 25
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                val_loss += loss.item()
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        scheduler.step(val_loss)
        
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_exact_historical_reproduction.pth')
        else:
            patience_counter += 1
        
        if epoch % 10 == 0 or epoch < 5:
            current_lr = optimizer.param_groups[0]['lr']
            print(f"Epoch {epoch+1:3d}: "
                  f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
                  f"Train Error: {train_error:.4f}, Val Error: {val_error:.4f}, "
                  f"LR: {current_lr:.2e}")
        
        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    model.load_state_dict(torch.load('best_exact_historical_reproduction.pth'))
    return model

def test_exact_historical_model(model, test_loader, device='cuda'):
    """测试精确历史模型"""
    
    print("🔍 测试精确历史架构模型...")
    
    model = model.to(device)
    model.eval()
    
    all_errors = []
    
    with torch.no_grad():
        for batch_pc, batch_kp in test_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            predicted = model(batch_pc)
            
            # 计算误差（归一化空间）
            distances = torch.norm(predicted - batch_kp, dim=2)
            all_errors.extend(distances.cpu().numpy().flatten())
    
    avg_error = np.mean(all_errors)
    
    # 计算准确率（在归一化空间）
    accuracy_01 = np.mean(np.array(all_errors) < 0.1) * 100
    accuracy_02 = np.mean(np.array(all_errors) < 0.2) * 100
    accuracy_05 = np.mean(np.array(all_errors) < 0.5) * 100
    
    return avg_error, accuracy_01, accuracy_02, accuracy_05

def main():
    """主函数"""
    
    print("🎯 精确复现历史架构")
    print("基于代码库搜索，复现5.371mm的ExactEnsembleDoubleSoftMaxPointNet")
    print("=" * 80)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 使用设备: {device}")
    
    # 加载数据集
    print("📊 加载高质量数据集...")
    data = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
    point_clouds = data['point_clouds']
    keypoints_57 = data['keypoints_57']
    sample_ids = data['sample_ids']
    
    # 创建历史12点子集
    historical_12_indices = create_historical_12point_subset()
    keypoints_12 = keypoints_57[:, historical_12_indices, :]
    
    print(f"✅ 数据集: {len(sample_ids)} 个样本")
    print(f"   点云形状: {point_clouds.shape}")
    print(f"   12点关键点形状: {keypoints_12.shape}")
    
    # 简单归一化
    from simple_12point_test import simple_normalization
    normalized_pc, normalized_kp = simple_normalization(point_clouds, keypoints_12)
    
    # 数据划分
    indices = np.arange(len(normalized_pc))
    train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
    train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
    
    # 创建数据集
    train_dataset = Historical12Dataset(normalized_pc[train_indices], normalized_kp[train_indices])
    val_dataset = Historical12Dataset(normalized_pc[val_indices], normalized_kp[val_indices])
    test_dataset = Historical12Dataset(normalized_pc[test_indices], normalized_kp[test_indices])
    
    # 数据加载器 - 精确匹配历史配置
    batch_size = 4  # 历史使用的批次大小
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    
    print(f"📋 数据划分: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    
    # 创建精确历史架构
    model = ExactEnsembleDoubleSoftMaxPointNet(num_keypoints=12, dropout_rate=0.3, num_ensembles=3)
    
    # 训练模型
    model = train_exact_historical_model(model, train_loader, val_loader, epochs=100, device=device)
    
    # 测试模型
    avg_error, acc_01, acc_02, acc_05 = test_exact_historical_model(model, test_loader, device=device)
    
    print(f"\n🎯 精确历史架构复现结果 (归一化空间):")
    print(f"   平均误差: {avg_error:.4f}")
    print(f"   <0.1准确率: {acc_01:.1f}%")
    print(f"   <0.2准确率: {acc_02:.1f}%")
    print(f"   <0.5准确率: {acc_05:.1f}%")
    
    # 估算真实空间误差
    estimated_real_error = avg_error * 150  # 粗略估算
    
    print(f"\n📊 估算真实空间性能:")
    print(f"   估算误差: ~{estimated_real_error:.1f}mm")
    print(f"   历史目标: 5.371mm")
    print(f"   差距: {estimated_real_error - 5.371:.1f}mm")
    
    if estimated_real_error < 8.0:
        print(f"\n🎉 成功！接近历史性能！")
        print(f"💡 证明了精确架构复现的有效性")
    elif estimated_real_error < 12.0:
        print(f"\n✅ 良好！显著改进")
    else:
        print(f"\n⚠️ 仍需进一步优化")
    
    # 保存结果
    results = {
        'normalized_avg_error': float(avg_error),
        'estimated_real_error_mm': float(estimated_real_error),
        'historical_target_mm': 5.371,
        'gap_to_target_mm': float(estimated_real_error - 5.371),
        'architecture_features': [
            'exact_ensemble_double_softmax',
            'three_ensemble_modules',
            'residual_connections',
            'xavier_initialization',
            'exact_loss_function',
            'historical_hyperparameters'
        ],
        'dataset_info': {
            'source': 'high_quality_pelvis_57_dataset',
            'subset_strategy': 'f3_reduced_12kp_stable_indices',
            'selected_indices': historical_12_indices
        }
    }
    
    with open('exact_historical_reproduction_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 详细结果已保存: exact_historical_reproduction_results.json")
    print(f"🎉 精确历史架构复现完成！")
    
    print(f"\n💡 关键发现:")
    print(f"   ✅ 成功复现了ExactEnsembleDoubleSoftMaxPointNet架构")
    print(f"   ✅ 使用了历史相同的12点子集策略")
    print(f"   ✅ 应用了精确的训练配置")
    print(f"   {'✅' if estimated_real_error < 8.0 else '⚠️'} 性能{'接近' if estimated_real_error < 8.0 else '仍有差距'}历史水平")

if __name__ == "__main__":
    main()
