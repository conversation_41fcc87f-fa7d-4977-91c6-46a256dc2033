#!/usr/bin/env python3
"""
Quick Cleanup - Remove experimental files directly
"""

import os
import shutil
from pathlib import Path

def quick_cleanup():
    """Quick and direct cleanup"""
    
    print("🧹 **快速清理工作区**")
    print("=" * 50)
    
    # Files to keep
    keep_files = {
        "train_wang2022_12point.py",  # User's original
        "analyze_dataset_fundamental_issues.py",  # Current focus
        "dataset_fundamental_analysis.json",  # Key results
        "Data",  # Original data
        "Archive_F3_Experiments",  # Archive
        "quick_cleanup.py",  # This script
        "thorough_cleanup.py",  # Backup cleanup
        "cleanup_and_archive.py",  # Archive script
        "cleanup_summary.json"  # Summary
    }
    
    # Get all items
    all_items = list(Path('.').iterdir())
    
    # Count what we'll remove
    to_remove = []
    for item in all_items:
        if item.name.startswith('.'):
            continue
        if item.name not in keep_files:
            to_remove.append(item)
    
    print(f"📊 发现 {len(to_remove)} 个可删除项目")
    print(f"📊 保留 {len(keep_files)} 个核心文件")
    
    # Remove items
    removed = 0
    failed = 0
    
    for item in to_remove:
        try:
            if item.is_file():
                item.unlink()
                removed += 1
            elif item.is_dir():
                shutil.rmtree(item)
                removed += 1
        except Exception as e:
            print(f"⚠️ 无法删除 {item.name}: {e}")
            failed += 1
    
    print(f"✅ 成功删除: {removed}")
    print(f"❌ 删除失败: {failed}")
    
    # Show final state
    print(f"\n📁 **清理后的工作区**:")
    remaining = list(Path('.').iterdir())
    remaining = [item for item in remaining if not item.name.startswith('.')]
    
    for item in sorted(remaining):
        item_type = "📁" if item.is_dir() else "📄"
        print(f"   {item_type} {item.name}")
    
    print(f"\n🎯 **工作区现在专注于数据集质量研究**")

if __name__ == "__main__":
    quick_cleanup()
