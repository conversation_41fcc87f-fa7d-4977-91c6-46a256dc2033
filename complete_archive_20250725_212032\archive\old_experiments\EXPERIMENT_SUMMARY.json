{"project": "Medical Point Cloud Keypoint Detection", "created": "2025-07-17 12:32:54", "experiments": {"baseline": {"description": "12关键点基线模型", "best_result": "6.208mm", "status": "✅ 成功 - 当前最佳", "key_insights": ["减少关键点数量从19到12个显著提升性能", "稳定性选择策略有效", "训练时间仅1.1分钟"]}, "phase1_improvements": {"description": "损失函数和超参数优化", "approaches": ["Wing Loss + Focal Loss", "保守Wing Loss改进", "超参数调优", "最终优化组合"], "best_result": "6.458mm (超参数调优)", "status": "❌ 未超越基线", "key_insights": ["复杂损失函数在小数据集上容易过拟合", "超参数调优空间有限", "需要架构级改进"]}, "phase2_architectures": {"description": "架构级改进", "approaches": ["Attention PointNet", "Feature Pyramid PointNet", "Double SoftMax PointNet", "Adaptive Double SoftMax PointNet"], "best_result": "6.384mm (Feature Pyramid)", "status": "✅ 轻微改进", "key_insights": ["Feature Pyramid PointNet表现最佳", "注意力机制参数过多导致过拟合", "双Softmax机制不适合当前数据规模"]}}, "current_best": {"model": "12关键点基线模型", "error": "6.208mm", "file": "experiments/baseline_models/best_reduced_12kp_f3.pth", "config": "12关键点 + 稳定性选择 + AdaptivePointNet"}, "next_steps": ["在基线模型上应用双Softmax机制", "优化Feature Pyramid PointNet", "数据质量改进", "集成学习方法"]}