<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="1280" height="720" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="url(#headerGrad)"/>
  <text x="640" y="50" text-anchor="middle" fill="white" 
        font-family="SimHei, Arial, sans-serif" font-size="36" font-weight="bold">
    创新点二：双SoftMax权重机制
  </text>
  
  <!-- Problem motivation -->
  <rect x="50" y="100" width="1180" height="100" rx="15" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <text x="640" y="130" text-anchor="middle" fill="#d97706" 
        font-family="SimHei, Arial, sans-serif" font-size="22" font-weight="bold">
    动机：解决权重分布挑战
  </text>
  <text x="70" y="160" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="16">
    <tspan font-weight="bold">问题：</tspan> 在密集点云中，当区域中心sp_i接近真值tp_i时，
    单一SoftMax由于点的相似性产生近似均匀权重，导致平均化效应降低精度。
  </text>
  <text x="70" y="185" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="16">
    <tspan font-weight="bold">解决方案：</tspan> 两阶段细化，强调高权重点并抑制低权重点。
  </text>
  
  <!-- Main workflow -->
  <rect x="50" y="220" width="1180" height="460" rx="15" fill="white" stroke="#7c3aed" stroke-width="3"/>
  <text x="640" y="250" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="24" font-weight="bold">
    双SoftMax工作流程
  </text>
  
  <!-- Step 1: Input -->
  <rect x="80" y="280" width="200" height="120" rx="10" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="2"/>
  <text x="180" y="305" text-anchor="middle" fill="#0c4a6e" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    输入：潜在区域
  </text>
  <text x="90" y="330" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 区域R_i中的k个点
  </text>
  <text x="90" y="350" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 来自PointNet的特征向量
  </text>
  <text x="90" y="370" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 高点密度/相似性
  </text>
  <text x="90" y="390" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 需要精确定位
  </text>
  
  <!-- Arrow 1 -->
  <path d="M 290 340 L 320 340" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
    </marker>
  </defs>
  
  <!-- Step 2: First SoftMax -->
  <rect x="330" y="280" width="200" height="120" rx="10" fill="#fef2f2" stroke="#ef4444" stroke-width="2"/>
  <text x="430" y="305" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    第一次SoftMax
  </text>
  <text x="340" y="330" fill="#374151" font-family="Arial, sans-serif" font-size="12">
    ω_j = exp(f_j) / Σexp(f_k)
  </text>
  <text x="340" y="350" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 初始权重计算
  </text>
  <text x="340" y="370" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 基于特征相似性
  </text>
  <text x="340" y="390" fill="#dc2626" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    问题：近似均匀权重
  </text>
  
  <!-- Weight visualization 1 -->
  <rect x="340" y="410" width="180" height="30" rx="5" fill="#fee2e2" stroke="#fca5a5" stroke-width="1"/>
  <rect x="345" y="415" width="25" height="20" fill="#ef4444" opacity="0.6"/>
  <rect x="375" y="415" width="23" height="20" fill="#ef4444" opacity="0.5"/>
  <rect x="403" y="415" width="24" height="20" fill="#ef4444" opacity="0.55"/>
  <rect x="432" y="415" width="22" height="20" fill="#ef4444" opacity="0.5"/>
  <rect x="459" y="415" width="26" height="20" fill="#ef4444" opacity="0.6"/>
  <rect x="490" y="415" width="25" height="20" fill="#ef4444" opacity="0.55"/>
  <text x="430" y="455" text-anchor="middle" fill="#7f1d1d" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    类均匀分布
  </text>
  
  <!-- Arrow 2 -->
  <path d="M 540 340 L 570 340" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Step 3: Weight SoftMax -->
  <rect x="580" y="280" width="200" height="120" rx="10" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="2"/>
  <text x="680" y="305" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    第二次SoftMax（权重）
  </text>
  <text x="590" y="330" fill="#374151" font-family="Arial, sans-serif" font-size="11">
    WS(ω_j) = 1/(1 + exp(-M·(ω_j - (1/m - ε))))
  </text>
  <text x="590" y="350" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 基于阈值的过滤
  </text>
  <text x="590" y="370" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 放大权重差异
  </text>
  <text x="590" y="390" fill="#7c3aed" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    结果：尖锐权重分布
  </text>
  
  <!-- Weight visualization 2 -->
  <rect x="590" y="410" width="180" height="30" rx="5" fill="#f3e8ff" stroke="#c084fc" stroke-width="1"/>
  <rect x="595" y="415" width="35" height="20" fill="#8b5cf6" opacity="0.9"/>
  <rect x="635" y="415" width="8" height="20" fill="#8b5cf6" opacity="0.2"/>
  <rect x="648" y="415" width="40" height="20" fill="#8b5cf6" opacity="0.8"/>
  <rect x="693" y="415" width="5" height="20" fill="#8b5cf6" opacity="0.1"/>
  <rect x="703" y="415" width="30" height="20" fill="#8b5cf6" opacity="0.7"/>
  <rect x="738" y="415" width="7" height="20" fill="#8b5cf6" opacity="0.15"/>
  <text x="680" y="455" text-anchor="middle" fill="#6b21a8" 
        font-family="SimHei, Arial, sans-serif" font-size="10">
    尖锐、选择性分布
  </text>
  
  <!-- Arrow 3 -->
  <path d="M 790 340 L 820 340" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Step 4: Weighted Average -->
  <rect x="830" y="280" width="200" height="120" rx="10" fill="#f0fdf4" stroke="#22c55e" stroke-width="2"/>
  <text x="930" y="305" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    加权平均
  </text>
  <text x="840" y="330" fill="#374151" font-family="Arial, sans-serif" font-size="11">
    kp_i = Σ(ω_j · WS(ω_j) · coord_j) / Σ(ω_j · WS(ω_j))
  </text>
  <text x="840" y="350" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 最终关键点坐标
  </text>
  <text x="840" y="370" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 高权重点占主导
  </text>
  <text x="840" y="390" fill="#15803d" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    实现精确定位
  </text>
  
  <!-- Arrow 4 -->
  <path d="M 1040 340 L 1070 340" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Step 5: Output -->
  <rect x="1080" y="280" width="150" height="120" rx="10" fill="#fef7ff" stroke="#a855f7" stroke-width="2"/>
  <text x="1155" y="305" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="16" font-weight="bold">
    最终关键点
  </text>
  <text x="1090" y="330" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 精确位置
  </text>
  <text x="1090" y="350" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 语义标签
  </text>
  <text x="1090" y="370" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="12">
    • 高准确性
  </text>
  <text x="1090" y="390" fill="#7c3aed" font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    临床可用
  </text>
  
  <!-- Mathematical details -->
  <rect x="80" y="480" width="550" height="180" rx="10" fill="#f8fafc" stroke="#64748b" stroke-width="2"/>
  <text x="355" y="505" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="18" font-weight="bold">
    数学公式详解
  </text>
  
  <text x="90" y="535" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    参数设置：
  </text>
  <text x="90" y="555" fill="#6b7280" font-family="Arial, sans-serif" font-size="12">
    • M = 1 × 10^(2×(γ+3)), 其中 γ = log₁₀⌈1/m⌉
  </text>
  <text x="90" y="575" fill="#6b7280" font-family="Arial, sans-serif" font-size="12">
    • ε = 5 × 10^(-1×(γ+2))
  </text>
  <text x="90" y="595" fill="#6b7280" font-family="SimHei, Arial, sans-serif" font-size="12">
    • m = 区域中的点数
  </text>
  
  <text x="90" y="625" fill="#374151" font-family="SimHei, Arial, sans-serif" font-size="14" font-weight="bold">
    阈值函数：
  </text>
  <text x="90" y="645" fill="#6b7280" font-family="Arial, sans-serif" font-size="12">
    WS(ω_j) = 1 / (1 + exp(-M × (ω_j - (1/m - ε))))
  </text>
  
  <!-- Performance comparison -->
  <rect x="650" y="480" width="580" height="180" rx="10" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="2"/>
  <text x="940" y="505" text-anchor="middle" fill="#0c4a6e" 
        font-family="SimHei, Arial, sans-serif" font-size="18" font-weight="bold">
    性能对比
  </text>
  
  <!-- Comparison table -->
  <rect x="670" y="520" width="540" height="130" rx="5" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  
  <!-- Table headers -->
  <rect x="670" y="520" width="180" height="30" fill="#f1f5f9" stroke="#cbd5e1" stroke-width="1"/>
  <text x="760" y="540" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    方法
  </text>
  
  <rect x="850" y="520" width="180" height="30" fill="#f1f5f9" stroke="#cbd5e1" stroke-width="1"/>
  <text x="940" y="540" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    平均径向误差 (mm)
  </text>
  
  <rect x="1030" y="520" width="180" height="30" fill="#f1f5f9" stroke="#cbd5e1" stroke-width="1"/>
  <text x="1120" y="540" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    改进幅度
  </text>
  
  <!-- Table rows -->
  <rect x="670" y="550" width="180" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="760" y="567" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="11">
    仅中心点
  </text>
  
  <rect x="850" y="550" width="180" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="940" y="567" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="11">
    1.81 ± 1.08
  </text>
  
  <rect x="1030" y="550" width="180" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="1120" y="567" text-anchor="middle" fill="#dc2626" 
        font-family="SimHei, Arial, sans-serif" font-size="11">
    基线
  </text>
  
  <rect x="670" y="575" width="180" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="760" y="592" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="11">
    PointNet + 残差
  </text>
  
  <rect x="850" y="575" width="180" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="940" y="592" text-anchor="middle" fill="#374151" 
        font-family="Arial, sans-serif" font-size="11">
    1.62 ± 1.04
  </text>
  
  <rect x="1030" y="575" width="180" height="25" fill="white" stroke="#cbd5e1" stroke-width="1"/>
  <text x="1120" y="592" text-anchor="middle" fill="#f59e0b" 
        font-family="SimHei, Arial, sans-serif" font-size="11">
    +0.19mm
  </text>
  
  <rect x="670" y="600" width="180" height="25" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="760" y="617" text-anchor="middle" fill="#374151" 
        font-family="SimHei, Arial, sans-serif" font-size="11" font-weight="bold">
    + 双SoftMax
  </text>
  
  <rect x="850" y="600" width="180" height="25" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="940" y="617" text-anchor="middle" fill="#15803d" 
        font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    1.43 ± 0.97
  </text>
  
  <rect x="1030" y="600" width="180" height="25" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="1120" y="617" text-anchor="middle" fill="#15803d" 
        font-family="SimHei, Arial, sans-serif" font-size="11" font-weight="bold">
    +0.38mm
  </text>
  
  <rect x="670" y="625" width="540" height="25" fill="#fef7ff" stroke="#a855f7" stroke-width="1"/>
  <text x="940" y="642" text-anchor="middle" fill="#7c3aed" 
        font-family="SimHei, Arial, sans-serif" font-size="12" font-weight="bold">
    核心创新：通过选择性加权实现21%的精度提升
  </text>
</svg>
