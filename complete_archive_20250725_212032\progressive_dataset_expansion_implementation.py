#!/usr/bin/env python3
"""
渐进式数据集扩展实施方案
Progressive Dataset Expansion Implementation
基于12关键点高性能模型的实际扩展
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import json
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

class ProgressiveExpansionImplementer:
    """渐进式扩展实施器"""
    
    def __init__(self, device='cuda:1'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        self.expansion_history = []
        self.performance_history = []
        
    def load_baseline_datasets(self):
        """加载基线数据集"""
        print("📥 加载基线12关键点数据集")
        print("=" * 50)
        
        try:
            # 加载女性数据集
            female_data = np.load('archive/old_experiments/f3_reduced_12kp_female.npz')
            female_pc = female_data['point_clouds']
            female_kp = female_data['keypoints']
            
            # 加载男性数据集
            male_data = np.load('archive/old_experiments/f3_reduced_12kp_male.npz')
            male_pc = male_data['point_clouds']
            male_kp = male_data['keypoints']
            
            print(f"✅ 基线数据集加载成功:")
            print(f"   女性: {len(female_pc)}样本, 点云{female_pc.shape}, 关键点{female_kp.shape}")
            print(f"   男性: {len(male_pc)}样本, 点云{male_pc.shape}, 关键点{male_kp.shape}")
            print(f"   总计: {len(female_pc) + len(male_pc)}样本")
            
            return female_pc, female_kp, male_pc, male_kp
            
        except Exception as e:
            print(f"❌ 基线数据集加载失败: {e}")
            return None, None, None, None
    
    def analyze_available_additional_data(self):
        """分析可用的额外数据"""
        print("\n🔍 分析可用的额外数据")
        print("=" * 50)
        
        # 扫描archive/old_experiments目录中的原始数据
        data_dir = Path('archive/old_experiments')
        keypoint_files = list(data_dir.glob('*_keypoints.npy'))
        pointcloud_files = list(data_dir.glob('*_pointcloud.npy'))
        
        print(f"发现原始数据文件:")
        print(f"  关键点文件: {len(keypoint_files)}个")
        print(f"  点云文件: {len(pointcloud_files)}个")
        
        # 提取样本ID
        sample_ids = []
        for kp_file in keypoint_files:
            sample_id = kp_file.stem.replace('_keypoints', '')
            pc_file = data_dir / f"{sample_id}_pointcloud.npy"
            if pc_file.exists():
                sample_ids.append(sample_id)
        
        print(f"  匹配的样本对: {len(sample_ids)}个")
        print(f"  样本ID范围: {min(sample_ids)} - {max(sample_ids)}")
        
        return sample_ids
    
    def extract_12kp_from_57kp(self, keypoints_57):
        """从57关键点中提取12个核心关键点"""
        # 定义12关键点到57关键点的映射
        mapping_12_to_57 = [
            0,   # F1-1 (髂前上棘)
            6,   # F1-7
            12,  # F1-13
            18,  # F1-19
            19,  # F2-1 (髂前上棘)
            25,  # F2-7
            31,  # F2-13
            37,  # F2-19
            39,  # F3-2 (骶骨岬)
            45,  # F3-8
            50,  # F3-13 (尾骨)
            56   # F3-19
        ]
        
        if len(keypoints_57) >= 57:
            keypoints_12 = keypoints_57[mapping_12_to_57]
        else:
            # 如果不足57个点，尝试直接使用前12个
            keypoints_12 = keypoints_57[:12] if len(keypoints_57) >= 12 else keypoints_57
        
        return keypoints_12
    
    def create_expanded_dataset(self, female_pc, female_kp, male_pc, male_kp, 
                               additional_samples=20, target_female_ratio=0.4):
        """创建扩展数据集"""
        print(f"\n📈 创建扩展数据集 (增加{additional_samples}个样本)")
        print("=" * 50)
        
        # 分析可用的额外数据
        sample_ids = self.analyze_available_additional_data()
        
        # 加载现有数据集中已使用的样本ID
        existing_female_ids = set()  # 这里需要根据实际情况确定
        existing_male_ids = set()    # 这里需要根据实际情况确定
        
        # 计算需要的女性和男性样本数
        target_female_count = int(additional_samples * target_female_ratio)
        target_male_count = additional_samples - target_female_count
        
        print(f"扩展目标:")
        print(f"  新增女性样本: {target_female_count}个")
        print(f"  新增男性样本: {target_male_count}个")
        
        # 尝试加载额外的样本
        new_female_pc, new_female_kp = [], []
        new_male_pc, new_male_kp = [], []
        
        loaded_count = 0
        for sample_id in sample_ids[:additional_samples]:  # 限制加载数量
            try:
                # 加载点云和关键点
                pc_path = f'archive/old_experiments/{sample_id}_pointcloud.npy'
                kp_path = f'archive/old_experiments/{sample_id}_keypoints.npy'
                
                pc = np.load(pc_path)
                kp = np.load(kp_path)
                
                # 检查数据质量
                if len(pc) < 1000 or len(kp) < 12:
                    continue
                
                # 提取12关键点
                kp_12 = self.extract_12kp_from_57kp(kp)
                if len(kp_12) != 12:
                    continue
                
                # 标准化点云大小到50000点
                if len(pc) > 50000:
                    indices = np.random.choice(len(pc), 50000, replace=False)
                    pc = pc[indices]
                elif len(pc) < 50000:
                    # 重采样到50000点
                    indices = np.random.choice(len(pc), 50000, replace=True)
                    pc = pc[indices]
                
                # 简单的性别分类（基于样本ID，实际应用中需要更准确的方法）
                sample_num = int(sample_id)
                if sample_num % 3 == 0 and len(new_female_pc) < target_female_count:
                    new_female_pc.append(pc)
                    new_female_kp.append(kp_12)
                elif len(new_male_pc) < target_male_count:
                    new_male_pc.append(pc)
                    new_male_kp.append(kp_12)
                
                loaded_count += 1
                
                if len(new_female_pc) >= target_female_count and len(new_male_pc) >= target_male_count:
                    break
                    
            except Exception as e:
                print(f"⚠️  样本{sample_id}加载失败: {e}")
                continue
        
        print(f"✅ 成功加载额外样本:")
        print(f"   新增女性: {len(new_female_pc)}个")
        print(f"   新增男性: {len(new_male_pc)}个")
        print(f"   总计新增: {len(new_female_pc) + len(new_male_pc)}个")
        
        # 合并数据集
        if new_female_pc:
            expanded_female_pc = np.vstack([female_pc, np.array(new_female_pc)])
            expanded_female_kp = np.vstack([female_kp, np.array(new_female_kp)])
        else:
            expanded_female_pc, expanded_female_kp = female_pc, female_kp
        
        if new_male_pc:
            expanded_male_pc = np.vstack([male_pc, np.array(new_male_pc)])
            expanded_male_kp = np.vstack([male_kp, np.array(new_male_kp)])
        else:
            expanded_male_pc, expanded_male_kp = male_pc, male_kp
        
        print(f"\n📊 扩展后数据集:")
        print(f"   女性: {len(expanded_female_pc)}样本 (原{len(female_pc)} + 新{len(new_female_pc)})")
        print(f"   男性: {len(expanded_male_pc)}样本 (原{len(male_pc)} + 新{len(new_male_pc)})")
        print(f"   总计: {len(expanded_female_pc) + len(expanded_male_pc)}样本")
        
        return expanded_female_pc, expanded_female_kp, expanded_male_pc, expanded_male_kp
    
    def create_simple_12kp_model(self):
        """创建简单的12关键点模型"""
        class Simple12KPModel(nn.Module):
            def __init__(self, num_points=50000, num_keypoints=12):
                super().__init__()
                self.num_keypoints = num_keypoints
                
                # 特征提取
                self.feature_extractor = nn.Sequential(
                    nn.Conv1d(3, 64, 1),
                    nn.BatchNorm1d(64),
                    nn.ReLU(),
                    nn.Conv1d(64, 128, 1),
                    nn.BatchNorm1d(128),
                    nn.ReLU(),
                    nn.Conv1d(128, 256, 1),
                    nn.BatchNorm1d(256),
                    nn.ReLU(),
                )
                
                # 回归头
                self.regressor = nn.Sequential(
                    nn.Linear(256, 128),
                    nn.ReLU(),
                    nn.Dropout(0.3),
                    nn.Linear(128, num_keypoints * 3)
                )
            
            def forward(self, x):
                batch_size = x.size(0)
                x = x.transpose(2, 1)  # [B, 3, N]
                features = self.feature_extractor(x)  # [B, 256, N]
                global_feat = torch.max(features, 2)[0]  # [B, 256]
                output = self.regressor(global_feat)  # [B, num_keypoints*3]
                return output.view(batch_size, self.num_keypoints, 3)
        
        return Simple12KPModel()
    
    def train_on_expanded_dataset(self, female_pc, female_kp, male_pc, male_kp, stage_name):
        """在扩展数据集上训练模型"""
        print(f"\n🎯 在扩展数据集上训练模型 - {stage_name}")
        print("=" * 50)
        
        # 合并数据
        all_pc = np.vstack([female_pc, male_pc])
        all_kp = np.vstack([female_kp, male_kp])
        
        print(f"训练数据: {len(all_pc)}样本")
        
        # 数据分割
        train_pc, test_pc, train_kp, test_kp = train_test_split(
            all_pc, all_kp, test_size=0.2, random_state=42)
        
        # 转换为张量
        train_pc_tensor = torch.FloatTensor(train_pc).to(self.device)
        train_kp_tensor = torch.FloatTensor(train_kp).to(self.device)
        test_pc_tensor = torch.FloatTensor(test_pc).to(self.device)
        test_kp_tensor = torch.FloatTensor(test_kp).to(self.device)
        
        # 创建数据加载器
        train_dataset = TensorDataset(train_pc_tensor, train_kp_tensor)
        train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True)
        
        # 创建模型
        model = self.create_simple_12kp_model().to(self.device)
        optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
        criterion = nn.MSELoss()
        
        print(f"模型参数: {sum(p.numel() for p in model.parameters()):,}")
        
        # 训练循环
        model.train()
        best_loss = float('inf')
        patience = 0
        
        for epoch in range(50):  # 较少的epoch用于快速测试
            epoch_loss = 0.0
            
            for batch_pc, batch_kp in train_loader:
                optimizer.zero_grad()
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                loss.backward()
                optimizer.step()
                epoch_loss += loss.item()
            
            avg_loss = epoch_loss / len(train_loader)
            
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience = 0
                torch.save(model.state_dict(), f'best_{stage_name}_model.pth')
            else:
                patience += 1
                if patience >= 10:
                    print(f"早停于epoch {epoch+1}")
                    break
            
            if epoch % 10 == 0:
                print(f"Epoch {epoch+1}: Loss = {avg_loss:.6f}")
        
        # 加载最佳模型并测试
        model.load_state_dict(torch.load(f'best_{stage_name}_model.pth'))
        model.eval()
        
        with torch.no_grad():
            predicted = model(test_pc_tensor)
            test_errors = torch.norm(predicted - test_kp_tensor, dim=2)
            avg_error = torch.mean(test_errors).item()
            
            # 计算准确率
            errors_5mm = torch.sum(torch.mean(test_errors, dim=1) <= 5.0).item()
            errors_10mm = torch.sum(torch.mean(test_errors, dim=1) <= 10.0).item()
            
            acc_5mm = (errors_5mm / len(test_pc)) * 100
            acc_10mm = (errors_10mm / len(test_pc)) * 100
        
        result = {
            'stage': stage_name,
            'samples': len(all_pc),
            'female_samples': len(female_pc),
            'male_samples': len(male_pc),
            'avg_error': avg_error,
            'accuracy_5mm': acc_5mm,
            'accuracy_10mm': acc_10mm,
            'medical_grade': avg_error <= 10.0
        }
        
        print(f"\n📊 {stage_name} 训练结果:")
        print(f"   样本数: {result['samples']} (女{result['female_samples']} + 男{result['male_samples']})")
        print(f"   平均误差: {result['avg_error']:.2f}mm")
        print(f"   5mm准确率: {result['accuracy_5mm']:.1f}%")
        print(f"   10mm准确率: {result['accuracy_10mm']:.1f}%")
        print(f"   医疗级达标: {'✅' if result['medical_grade'] else '❌'}")
        
        self.performance_history.append(result)
        return result
    
    def run_progressive_expansion(self):
        """运行渐进式扩展"""
        print("🚀 开始渐进式数据集扩展实施")
        print("=" * 60)
        
        # 1. 加载基线数据集
        female_pc, female_kp, male_pc, male_kp = self.load_baseline_datasets()
        if female_pc is None:
            print("❌ 无法加载基线数据集，退出")
            return
        
        # 2. 基线性能测试
        print(f"\n🎯 阶段0: 基线性能测试")
        baseline_result = self.train_on_expanded_dataset(
            female_pc, female_kp, male_pc, male_kp, "baseline")
        
        # 3. 渐进式扩展
        expansion_stages = [
            {"name": "stage1", "additional": 10, "description": "小幅扩展"},
            {"name": "stage2", "additional": 20, "description": "中等扩展"},
            {"name": "stage3", "additional": 30, "description": "大幅扩展"}
        ]
        
        current_female_pc, current_female_kp = female_pc, female_kp
        current_male_pc, current_male_kp = male_pc, male_kp
        
        for stage in expansion_stages:
            print(f"\n🎯 {stage['name']}: {stage['description']}")
            
            # 扩展数据集
            expanded_female_pc, expanded_female_kp, expanded_male_pc, expanded_male_kp = \
                self.create_expanded_dataset(
                    current_female_pc, current_female_kp,
                    current_male_pc, current_male_kp,
                    additional_samples=stage['additional'])
            
            # 训练和测试
            stage_result = self.train_on_expanded_dataset(
                expanded_female_pc, expanded_female_kp,
                expanded_male_pc, expanded_male_kp,
                stage['name'])
            
            # 更新当前数据集
            current_female_pc, current_female_kp = expanded_female_pc, expanded_female_kp
            current_male_pc, current_male_kp = expanded_male_pc, expanded_male_kp
        
        # 4. 保存结果
        self.save_expansion_results()
        
        return self.performance_history
    
    def save_expansion_results(self):
        """保存扩展结果"""
        results = {
            'expansion_history': self.performance_history,
            'summary': {
                'total_stages': len(self.performance_history),
                'final_samples': self.performance_history[-1]['samples'] if self.performance_history else 0,
                'final_performance': self.performance_history[-1]['avg_error'] if self.performance_history else 0,
                'improvement': (self.performance_history[0]['avg_error'] - self.performance_history[-1]['avg_error']) if len(self.performance_history) > 1 else 0
            },
            'timestamp': '2025-07-25'
        }
        
        with open('progressive_expansion_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 扩展结果已保存到 progressive_expansion_results.json")

def main():
    """主函数"""
    print("🎯 渐进式数据集扩展实施")
    print("Progressive Dataset Expansion Implementation")
    print("=" * 60)
    
    # 创建实施器
    implementer = ProgressiveExpansionImplementer()
    
    # 运行渐进式扩展
    results = implementer.run_progressive_expansion()
    
    if results:
        print(f"\n🎉 扩展完成总结:")
        print(f"✅ 完成{len(results)}个扩展阶段")
        print(f"📊 最终样本数: {results[-1]['samples']}")
        print(f"🎯 最终性能: {results[-1]['avg_error']:.2f}mm")
        print(f"📈 性能改进: {(results[0]['avg_error'] - results[-1]['avg_error']):.2f}mm")
    else:
        print("❌ 扩展过程中出现问题")

if __name__ == "__main__":
    main()
