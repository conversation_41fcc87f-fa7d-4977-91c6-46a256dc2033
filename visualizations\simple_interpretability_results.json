[{"sample_id": 1, "performance": {"mean_error": 3.834225654602051, "accuracy_5mm": 78.94736842105263}, "geometric_analysis": {"point_cloud_stats": {"center": [0.051373403519392014, 0.04850275069475174, 0.051767412573099136], "std": [22.495506286621094, 11.744010925292969, 15.912531852722168], "range": [99.32283020019531, 50.89665222167969, 64.17735290527344], "num_points": 512}, "keypoints_analysis": {"gt_center": [-0.4614696800708771, -0.4356701970100403, -0.4649496376514435], "pred_center": [0.11662128567695618, 0.45274341106414795, -0.68461674451828], "gt_spread": [14.697773933410645, 7.94027853012085, 12.176161766052246], "pred_spread": [12.979077339172363, 8.423603057861328, 10.900317192077637], "center_offset": [0.5780909657478333, 0.8884136080741882, -0.21966710686683655]}, "error_analysis": {"mean_error": 3.834225654602051, "std_error": 1.3374791145324707, "max_error": 7.40700626373291, "min_error": 1.531753659248352, "median_error": 3.729154586791992, "worst_keypoints": [27, 0, 50, 28, 9], "best_keypoints": [30, 33, 34, 44, 16]}}, "spatial_analysis": {"point_density": {"max_density": 10.0, "mean_density": 0.512, "density_std": 1.2616877585203083}, "keypoint_distribution": {"gt_occupied_cells": 53, "pred_occupied_cells": 50, "overlap_cells": 26}}}, {"sample_id": 2, "performance": {"mean_error": 2.323809862136841, "accuracy_5mm": 96.49122807017544}, "geometric_analysis": {"point_cloud_stats": {"center": [0.035291992127895355, -0.03725801408290863, 0.07704314589500427], "std": [22.11638641357422, 12.929505348205566, 17.66674041748047], "range": [99.9620361328125, 55.39862823486328, 72.99322509765625], "num_points": 512}, "keypoints_analysis": {"gt_center": [-0.317008912563324, 0.33466410636901855, -0.692034125328064], "pred_center": [0.1360979527235031, 0.5149219036102295, -0.7836349010467529], "gt_spread": [14.260712623596191, 9.800348281860352, 13.188993453979492], "pred_spread": [14.7569580078125, 9.58205509185791, 12.402819633483887], "center_offset": [0.4531068801879883, 0.18025779724121094, -0.09160077571868896]}, "error_analysis": {"mean_error": 2.323809862136841, "std_error": 1.175987958908081, "max_error": 7.041470527648926, "min_error": 0.5530835390090942, "median_error": 2.16899037361145, "worst_keypoints": [7, 52, 50, 18, 37], "best_keypoints": [21, 22, 51, 2, 14]}}, "spatial_analysis": {"point_density": {"max_density": 12.0, "mean_density": 0.512, "density_std": 1.3644984426520979}, "keypoint_distribution": {"gt_occupied_cells": 47, "pred_occupied_cells": 52, "overlap_cells": 27}}}, {"sample_id": 3, "performance": {"mean_error": 3.6782145500183105, "accuracy_5mm": 87.71929824561403}, "geometric_analysis": {"point_cloud_stats": {"center": [0.00880249310284853, -0.10852562636137009, 0.27474644780158997], "std": [21.315593719482422, 12.442938804626465, 16.066747665405273], "range": [99.68470764160156, 53.640445709228516, 66.0504150390625], "num_points": 512}, "keypoints_analysis": {"gt_center": [-0.07906726002693176, 0.9748199582099915, -2.467921495437622], "pred_center": [0.1200815811753273, 0.46928322315216064, -0.7015581130981445], "gt_spread": [14.760643005371094, 9.5027437210083, 12.774555206298828], "pred_spread": [13.300544738769531, 8.640548706054688, 11.174225807189941], "center_offset": [0.19914883375167847, -0.5055367350578308, 1.7663633823394775]}, "error_analysis": {"mean_error": 3.6782145500183105, "std_error": 1.7521802186965942, "max_error": 10.056107521057129, "min_error": 1.0179715156555176, "median_error": 3.4515304565429688, "worst_keypoints": [32, 9, 12, 36, 31], "best_keypoints": [2, 1, 4, 49, 21]}}, "spatial_analysis": {"point_density": {"max_density": 10.0, "mean_density": 0.512, "density_std": 1.2743060856795747}, "keypoint_distribution": {"gt_occupied_cells": 52, "pred_occupied_cells": 50, "overlap_cells": 22}}}, {"sample_id": 4, "performance": {"mean_error": 2.065392017364502, "accuracy_5mm": 96.49122807017544}, "geometric_analysis": {"point_cloud_stats": {"center": [-0.03403610363602638, -0.11350300908088684, 0.03502846881747246], "std": [21.28354263305664, 12.826481819152832, 15.157552719116211], "range": [98.66267395019531, 54.184165954589844, 65.11659240722656], "num_points": 512}, "keypoints_analysis": {"gt_center": [0.3057202398777008, 1.0195339918136597, -0.31460636854171753], "pred_center": [0.1235131323337555, 0.4867776036262512, -0.7344772815704346], "gt_spread": [13.76784610748291, 9.31416130065918, 12.151331901550293], "pred_spread": [13.837125778198242, 8.987420082092285, 11.636297225952148], "center_offset": [-0.1822071075439453, -0.5327563881874084, -0.41987091302871704]}, "error_analysis": {"mean_error": 2.065392017364502, "std_error": 0.9861341118812561, "max_error": 6.121842384338379, "min_error": 0.9129300117492676, "median_error": 1.7845953702926636, "worst_keypoints": [15, 4, 23, 37, 18], "best_keypoints": [47, 41, 46, 1, 51]}}, "spatial_analysis": {"point_density": {"max_density": 9.0, "mean_density": 0.512, "density_std": 1.2632719422198848}, "keypoint_distribution": {"gt_occupied_cells": 53, "pred_occupied_cells": 48, "overlap_cells": 35}}}, {"sample_id": 5, "performance": {"mean_error": 2.7240536212921143, "accuracy_5mm": 92.98245614035088}, "geometric_analysis": {"point_cloud_stats": {"center": [-0.0318019837141037, -0.0739361047744751, 0.0698494017124176], "std": [20.9384765625, 13.027485847473145, 16.46182632446289], "range": [97.97401428222656, 55.39275360107422, 67.86126708984375], "num_points": 512}, "keypoints_analysis": {"gt_center": [0.28566789627075195, 0.6641281247138977, -0.6274099946022034], "pred_center": [0.12669959664344788, 0.48827630281448364, -0.7349632382392883], "gt_spread": [14.201932907104492, 9.72043228149414, 12.515789985656738], "pred_spread": [14.083198547363281, 9.15000057220459, 11.848313331604004], "center_offset": [-0.15896829962730408, -0.17585182189941406, -0.10755324363708496]}, "error_analysis": {"mean_error": 2.7240536212921143, "std_error": 1.239218831062317, "max_error": 6.897726535797119, "min_error": 0.5040067434310913, "median_error": 2.626248359680176, "worst_keypoints": [5, 28, 7, 11, 25], "best_keypoints": [47, 48, 46, 20, 33]}}, "spatial_analysis": {"point_density": {"max_density": 12.0, "mean_density": 0.512, "density_std": 1.3220650513495924}, "keypoint_distribution": {"gt_occupied_cells": 51, "pred_occupied_cells": 49, "overlap_cells": 28}}}]