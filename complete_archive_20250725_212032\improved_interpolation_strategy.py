#!/usr/bin/env python3
"""
改进的插值策略
Improved interpolation strategy for better 57-point generation
"""

import numpy as np
import torch
import torch.nn as nn
from scipy.interpolate import Rbf, griddata
from scipy.spatial.distance import pdist, squareform
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler

class AnatomicalConstrainedInterpolation:
    """基于解剖学约束的插值器"""
    
    def __init__(self):
        # 定义解剖学约束
        self.anatomical_constraints = self.define_anatomical_constraints()
        self.region_templates = self.create_region_templates()
        
    def define_anatomical_constraints(self):
        """定义解剖学约束"""
        
        constraints = {
            # F1区域约束 (髂骨翼)
            'F1': {
                'known_points': [0, 1, 2, 12],  # 在区域内的索引
                'shape': 'curved_surface',
                'symmetry': 'bilateral',
                'max_curvature': 0.1,
                'typical_distances': {
                    'adjacent': (5, 15),  # 相邻点距离范围(mm)
                    'diagonal': (15, 35)
                }
            },

            # F2区域约束 (对侧髂骨翼)
            'F2': {
                'known_points': [0, 1, 2, 12],  # 在区域内的索引
                'shape': 'curved_surface',
                'symmetry': 'bilateral',
                'max_curvature': 0.1,
                'typical_distances': {
                    'adjacent': (5, 15),
                    'diagonal': (15, 35)
                }
            },

            # F3区域约束 (骶骨)
            'F3': {
                'known_points': [0, 12, 13, 14],  # 在区域内的索引
                'shape': 'ridge_like',
                'symmetry': 'midline',
                'max_curvature': 0.15,
                'typical_distances': {
                    'adjacent': (3, 12),
                    'diagonal': (10, 25)
                }
            }
        }
        
        return constraints
    
    def create_region_templates(self):
        """创建区域模板"""
        
        templates = {}
        
        # F1区域模板 (19个点的相对位置)
        templates['F1'] = self.create_f1_template()
        templates['F2'] = self.create_f2_template()
        templates['F3'] = self.create_f3_template()
        
        return templates
    
    def create_f1_template(self):
        """创建F1区域模板"""
        
        # 基于解剖学知识的F1区域点分布
        # 髂骨翼的典型形状：弧形分布
        template = np.zeros((19, 3))
        
        # 已知点位置 (相对坐标)
        known_positions = {
            0: np.array([0, 0, 0]),      # F1-1 (髂前上棘)
            1: np.array([10, 5, 2]),     # F1-2
            2: np.array([20, 8, 5]),     # F1-3
            12: np.array([15, -10, 8])   # F1-13
        }
        
        # 设置已知点
        for idx, pos in known_positions.items():
            template[idx] = pos
        
        # 插值其他点
        # 前段弧线 (0-11)
        for i in range(3, 12):
            t = (i - 3) / 8.0  # 归一化参数
            # 在已知点之间进行样条插值
            template[i] = self.spline_interpolate_3points(
                known_positions[0], known_positions[1], known_positions[2], t
            )
        
        # 后段弧线 (13-18)
        for i in range(13, 19):
            t = (i - 13) / 5.0
            # 从F1-13向外扩展
            base = known_positions[12]
            offset = np.array([t*8, t*5, t*3]) + np.random.normal(0, 1, 3)
            template[i] = base + offset
        
        return template
    
    def create_f2_template(self):
        """创建F2区域模板 (F1的镜像)"""
        
        f1_template = self.create_f1_template()
        
        # F2是F1在X轴的镜像
        f2_template = f1_template.copy()
        f2_template[:, 0] *= -1  # X坐标取反
        
        return f2_template
    
    def create_f3_template(self):
        """创建F3区域模板"""
        
        # 骶骨区域：中线对称，脊状结构
        template = np.zeros((19, 3))
        
        # 已知点位置
        known_positions = {
            0: np.array([0, 0, 0]),      # F3-1 (骶骨岬)
            12: np.array([0, -15, 5]),   # F3-13 (骶骨中部)
            13: np.array([0, -20, 8]),   # F3-14
            14: np.array([0, -30, 10])   # F3-15 (尾骨)
        }
        
        # 设置已知点
        for idx, pos in known_positions.items():
            template[idx] = pos
        
        # 脊状分布的其他点
        for i in range(1, 12):
            t = i / 11.0
            # 沿着脊线分布
            template[i] = np.array([
                np.random.normal(0, 2),  # X: 中线附近小幅变化
                -t * 15,                 # Y: 沿着前后方向
                t * 5 + np.random.normal(0, 1)  # Z: 高度变化
            ])
        
        # 后段点
        for i in range(15, 19):
            t = (i - 15) / 3.0
            base = known_positions[14]
            offset = np.array([0, -t*5, t*2]) + np.random.normal(0, 0.5, 3)
            template[i] = base + offset
        
        return template
    
    def spline_interpolate_3points(self, p1, p2, p3, t):
        """三点样条插值"""
        
        # 简单的二次贝塞尔曲线插值
        return (1-t)**2 * p1 + 2*(1-t)*t * p2 + t**2 * p3
    
    def rbf_interpolation(self, known_points, known_coords, target_coords):
        """径向基函数插值"""
        
        interpolated = np.zeros((len(target_coords), 3))
        
        for dim in range(3):  # X, Y, Z
            rbf = Rbf(known_coords[:, 0], known_coords[:, 1], known_coords[:, 2], 
                     known_points[:, dim], function='thin_plate')
            interpolated[:, dim] = rbf(target_coords[:, 0], target_coords[:, 1], target_coords[:, 2])
        
        return interpolated
    
    def apply_anatomical_constraints(self, points, region_name):
        """应用解剖学约束"""
        
        constraints = self.anatomical_constraints[region_name]
        
        # 1. 距离约束
        points = self.enforce_distance_constraints(points, constraints)
        
        # 2. 平滑约束
        points = self.enforce_smoothness_constraints(points, constraints)
        
        # 3. 对称约束
        if constraints['symmetry'] == 'bilateral':
            points = self.enforce_bilateral_symmetry(points)
        elif constraints['symmetry'] == 'midline':
            points = self.enforce_midline_symmetry(points)
        
        return points
    
    def enforce_distance_constraints(self, points, constraints):
        """强制距离约束"""
        
        adj_min, adj_max = constraints['typical_distances']['adjacent']
        
        # 检查相邻点距离
        for i in range(len(points) - 1):
            dist = np.linalg.norm(points[i+1] - points[i])
            
            if dist < adj_min:
                # 距离太近，拉开
                direction = (points[i+1] - points[i]) / dist
                points[i+1] = points[i] + direction * adj_min
            elif dist > adj_max:
                # 距离太远，拉近
                direction = (points[i+1] - points[i]) / dist
                points[i+1] = points[i] + direction * adj_max
        
        return points
    
    def enforce_smoothness_constraints(self, points, constraints):
        """强制平滑约束"""
        
        max_curvature = constraints['max_curvature']
        
        # 应用高斯滤波平滑
        from scipy.ndimage import gaussian_filter1d
        
        for dim in range(3):
            points[:, dim] = gaussian_filter1d(points[:, dim], sigma=1.0)
        
        return points
    
    def enforce_bilateral_symmetry(self, points):
        """强制双侧对称"""
        
        # 对于F1/F2区域，确保大致的双侧对称性
        # 这里简化处理，实际应该基于解剖学中线
        return points
    
    def enforce_midline_symmetry(self, points):
        """强制中线对称"""
        
        # 对于F3区域，确保X坐标接近0
        points[:, 0] = points[:, 0] * 0.1  # 大幅减少X方向偏移
        return points
    
    def interpolate_region(self, known_12_points, region_name, region_indices):
        """插值单个区域"""
        
        # 获取该区域的已知点
        constraints = self.anatomical_constraints[region_name]
        known_indices = constraints['known_points']
        
        # 映射到12点索引
        region_12_mapping = {
            'F1': [0, 1, 2, 3],
            'F2': [4, 5, 6, 7], 
            'F3': [8, 9, 10, 11]
        }
        
        known_12_indices = region_12_mapping[region_name]
        known_points_3d = known_12_points[known_12_indices]
        
        # 获取区域模板
        template = self.region_templates[region_name]
        
        # 将模板配准到已知点
        aligned_template = self.align_template_to_known_points(
            template, known_points_3d, known_indices
        )
        
        # 应用解剖学约束
        constrained_points = self.apply_anatomical_constraints(
            aligned_template, region_name
        )
        
        return constrained_points
    
    def align_template_to_known_points(self, template, known_points_3d, known_indices):
        """将模板对齐到已知点"""
        
        # 简化的仿射变换对齐
        # 实际应该使用更复杂的配准算法
        
        # 计算模板和真实点的中心
        template_center = np.mean(template[known_indices], axis=0)
        real_center = np.mean(known_points_3d, axis=0)
        
        # 平移
        aligned = template + (real_center - template_center)
        
        # 缩放 (基于已知点的分布范围)
        template_scale = np.std(template[known_indices], axis=0)
        real_scale = np.std(known_points_3d, axis=0)
        
        scale_factor = real_scale / (template_scale + 1e-6)
        aligned = (aligned - real_center) * scale_factor + real_center
        
        # 确保已知点位置精确
        for i, idx in enumerate(known_indices):
            aligned[idx] = known_points_3d[i]
        
        return aligned

def improved_57_point_generation(proven_12kp):
    """改进的57点生成"""
    
    print("🧬 使用改进的解剖学约束插值...")
    
    interpolator = AnatomicalConstrainedInterpolation()
    improved_57kp = []
    
    for i, kp_12 in enumerate(proven_12kp):
        kp_57 = np.zeros((57, 3))
        
        # 为每个区域进行约束插值
        regions = {
            'F1': list(range(0, 19)),
            'F2': list(range(19, 38)),
            'F3': list(range(38, 57))
        }
        
        for region_name, indices in regions.items():
            region_points = interpolator.interpolate_region(
                kp_12, region_name, indices
            )
            kp_57[indices] = region_points
        
        improved_57kp.append(kp_57)
        
        if (i + 1) % 20 == 0:
            print(f"   处理进度: {i+1}/{len(proven_12kp)}")
    
    return np.array(improved_57kp)

def compare_interpolation_methods():
    """比较不同插值方法"""
    
    print("📊 比较插值方法...")
    
    # 加载数据
    data = np.load('smart_expanded_57_dataset.npz', allow_pickle=True)
    proven_12kp = data['proven_12_keypoints']
    original_57kp = data['interpolated_57_keypoints']
    
    # 生成改进的57点
    improved_57kp = improved_57_point_generation(proven_12kp[:10])  # 先测试10个样本
    
    # 比较质量指标
    print(f"\n📈 质量对比:")
    
    # 1. 区域内点距离分布
    for method_name, kp_57 in [('原始插值', original_57kp[:10]), ('改进插值', improved_57kp)]:
        print(f"\n   {method_name}:")
        
        regions = {
            'F1': list(range(0, 19)),
            'F2': list(range(19, 38)),
            'F3': list(range(38, 57))
        }
        
        for region_name, indices in regions.items():
            region_points = kp_57[:, indices, :]
            
            # 计算区域内平均距离
            distances = []
            for sample in region_points:
                for i in range(len(sample)):
                    for j in range(i+1, len(sample)):
                        dist = np.linalg.norm(sample[i] - sample[j])
                        distances.append(dist)
            
            avg_dist = np.mean(distances)
            std_dist = np.std(distances)
            print(f"     {region_name}: {avg_dist:.2f}±{std_dist:.2f}mm")
    
    return improved_57kp

def main():
    """主函数"""
    
    print("🎯 改进的插值策略")
    print("基于解剖学约束的57点生成")
    print("=" * 80)
    
    # 比较插值方法
    improved_57kp = compare_interpolation_methods()
    
    # 保存改进的数据
    data = np.load('smart_expanded_57_dataset.npz', allow_pickle=True)
    
    np.savez('improved_57_dataset.npz',
             proven_point_clouds=data['proven_point_clouds'][:10],
             proven_12_keypoints=data['proven_12_keypoints'][:10],
             original_57_keypoints=data['interpolated_57_keypoints'][:10],
             improved_57_keypoints=improved_57kp,
             gender_labels=data['gender_labels'][:10])
    
    print(f"\n✅ 改进的数据集已保存: improved_57_dataset.npz")
    print(f"\n💡 主要改进:")
    print(f"   1. 基于解剖学模板的插值")
    print(f"   2. 距离和平滑约束")
    print(f"   3. 区域特异性处理")
    print(f"   4. 对称性约束")
    
    print(f"\n🚀 下一步:")
    print(f"   1. 在改进数据上重新训练模型")
    print(f"   2. 对比性能提升")
    print(f"   3. 进一步优化约束参数")

if __name__ == "__main__":
    main()
