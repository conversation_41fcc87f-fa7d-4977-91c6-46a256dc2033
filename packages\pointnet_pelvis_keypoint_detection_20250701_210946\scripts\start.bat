@echo off
echo 🚀 PointNet骨盆关键点检测项目
echo ================================

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装
    pause
    exit /b 1
)

echo ✅ 环境检查完成

echo.
echo 请选择运行模式:
echo 1. 测试预测
echo 2. 3D可视化  
echo 3. Web服务器
echo 4. 重新训练

set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" (
    echo 🧪 运行预测测试...
    python ../code/test_predictions.py
) else if "%choice%"=="2" (
    echo 🎮 启动3D可视化...
    python ../code/direct_3d_viewer.py
) else if "%choice%"=="3" (
    echo 🌐 启动Web服务器...
    python ../code/start_web_server.py
) else if "%choice%"=="4" (
    echo 🏋️ 开始重新训练...
    python ../code/improved_training.py
) else (
    echo ❌ 无效选择
)

pause
