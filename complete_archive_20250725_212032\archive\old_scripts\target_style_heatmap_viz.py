#!/usr/bin/env python3
"""
靶子风格热图可视化
创建像靶子一样的同心圆热图效果，中心高置信度，外围低置信度
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps

# 关键点名称
KEYPOINT_NAMES = {
    0: "L-ASIS", 1: "R-ASIS", 2: "L-PSIS", 3: "R-PSIS",
    4: "L-IC", 5: "R-IC", 6: "SP", 7: "L-SIJ", 8: "R-SIJ",
    9: "L-IS", 10: "R-IS", 11: "CT"
}

def create_target_colormap():
    """创建靶子风格的颜色映射 - 从蓝色(低)到红色(高)"""
    colors = [
        '#000080',  # 深蓝 (最低置信度)
        '#0000FF',  # 蓝色
        '#00FFFF',  # 青色
        '#00FF00',  # 绿色
        '#FFFF00',  # 黄色
        '#FF8000',  # 橙色
        '#FF0000',  # 红色 (最高置信度)
        '#FFFFFF'   # 白色 (峰值)
    ]
    return LinearSegmentedColormap.from_list('target_heatmap', colors, N=256)

def enhance_heatmap_for_target_effect(heatmap, enhancement_factor=3.0):
    """增强热图以产生靶子效果"""
    
    # 1. 找到最高置信度点作为中心
    max_idx = np.argmax(heatmap)
    max_value = heatmap[max_idx]
    
    # 2. 如果最大值太小，进行增强
    if max_value < 0.5:
        # 非线性增强 - 使用幂函数突出峰值
        enhanced = np.power(heatmap, 1.0/enhancement_factor)
    else:
        enhanced = heatmap.copy()
    
    # 3. 进一步增强对比度
    # 使用sigmoid函数增强中心区域
    enhanced = 1 / (1 + np.exp(-10 * (enhanced - 0.3)))
    
    # 4. 确保有明显的峰值
    enhanced = enhanced / np.max(enhanced)  # 归一化到[0,1]
    
    # 5. 创建更明显的梯度效果
    # 对高值区域进一步增强
    enhanced = np.where(enhanced > 0.1, enhanced, enhanced * 0.1)
    
    return enhanced

def create_target_style_visualization(point_cloud, pred_heatmaps, true_keypoints, 
                                    pred_keypoints, confidences, sample_id):
    """创建靶子风格的热图可视化"""
    
    print(f"🎯 Creating target-style heatmap for sample {sample_id}")
    
    # 创建靶子风格颜色映射
    target_cmap = create_target_colormap()
    
    # 选择要显示的关键点（显示最有趣的几个）
    keypoints_to_show = [0, 1, 6, 11]  # L-ASIS, R-ASIS, SP, CT
    
    fig = plt.figure(figsize=(20, 15))
    
    for idx, kp_idx in enumerate(keypoints_to_show):
        # 为每个关键点创建两个视图：原始热图 + 增强热图
        for view_type in range(2):
            subplot_idx = idx * 2 + view_type + 1
            ax = fig.add_subplot(4, 2, subplot_idx, projection='3d')
            
            # 获取该关键点的热图
            heatmap = pred_heatmaps[:, kp_idx]
            
            if view_type == 0:
                # 原始热图
                display_heatmap = heatmap
                title_suffix = "Original"
            else:
                # 增强的靶子效果
                display_heatmap = enhance_heatmap_for_target_effect(heatmap, enhancement_factor=2.0)
                title_suffix = "Target Enhanced"
            
            # 绘制背景点云 - 非常淡的灰色
            background_mask = display_heatmap < 0.05
            if np.any(background_mask):
                ax.scatter(point_cloud[background_mask, 0], 
                          point_cloud[background_mask, 1], 
                          point_cloud[background_mask, 2],
                          c='lightgray', s=0.1, alpha=0.2)
            
            # 绘制热图点云 - 靶子效果
            heatmap_mask = display_heatmap >= 0.05
            if np.any(heatmap_mask):
                scatter = ax.scatter(point_cloud[heatmap_mask, 0],
                                   point_cloud[heatmap_mask, 1], 
                                   point_cloud[heatmap_mask, 2],
                                   c=display_heatmap[heatmap_mask], 
                                   cmap=target_cmap, 
                                   s=3, alpha=0.8, vmin=0, vmax=1)
            
            # 添加真实关键点 - 黑色星形
            ax.scatter(true_keypoints[kp_idx, 0], true_keypoints[kp_idx, 1], 
                      true_keypoints[kp_idx, 2], c='black', s=150, marker='*', 
                      edgecolor='white', linewidth=2, alpha=0.9, label='Ground Truth')
            
            # 添加预测关键点 - 白色圆圈
            ax.scatter(pred_keypoints[kp_idx, 0], pred_keypoints[kp_idx, 1], 
                      pred_keypoints[kp_idx, 2], c='white', s=100, marker='o', 
                      edgecolor='black', linewidth=2, alpha=0.9, label='Predicted')
            
            # 计算误差
            error = np.linalg.norm(pred_keypoints[kp_idx] - true_keypoints[kp_idx])
            
            # 设置标题
            ax.set_title(f'{KEYPOINT_NAMES[kp_idx]} - {title_suffix}\n'
                        f'Confidence: {confidences[kp_idx]:.3f}, Error: {error:.1f}mm',
                        fontsize=11, fontweight='bold')
            
            # 设置视角 - 选择最佳观察角度
            if kp_idx in [0, 1]:  # ASIS点 - 前视角
                ax.view_init(elev=20, azim=45)
            elif kp_idx == 6:     # SP点 - 后视角  
                ax.view_init(elev=20, azim=225)
            else:                 # CT点 - 下视角
                ax.view_init(elev=60, azim=0)
            
            # 设置坐标轴
            center = pred_keypoints[kp_idx]
            radius = 25  # 显示半径
            ax.set_xlim([center[0] - radius, center[0] + radius])
            ax.set_ylim([center[1] - radius, center[1] + radius])
            ax.set_zlim([center[2] - radius, center[2] + radius])
            
            ax.set_xlabel('X (mm)', fontsize=9)
            ax.set_ylabel('Y (mm)', fontsize=9)
            ax.set_zlabel('Z (mm)', fontsize=9)
            ax.tick_params(labelsize=8)
            
            # 添加颜色条
            if view_type == 1 and np.any(heatmap_mask):  # 只在增强视图添加颜色条
                cbar = plt.colorbar(scatter, ax=ax, shrink=0.6, pad=0.1)
                cbar.set_label('Confidence', fontsize=9)
                cbar.ax.tick_params(labelsize=8)
            
            # 只在第一个子图显示图例
            if subplot_idx == 1:
                ax.legend(fontsize=9, loc='upper right')
    
    # 添加总标题
    plt.suptitle(f'Target-Style Heatmap Visualization - Sample {sample_id}\n'
                f'Bullseye Effect: High Confidence (Red/White) → Low Confidence (Blue)', 
                fontsize=16, fontweight='bold')
    
    plt.tight_layout(rect=[0, 0, 1, 0.94])
    
    save_path = f'target_heatmap_{sample_id}.png'
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"   📊 Target-style heatmap saved: {save_path}")
    plt.close()

def create_single_keypoint_target_view(point_cloud, heatmap, true_keypoint, 
                                     pred_keypoint, confidence, kp_idx, sample_id):
    """为单个关键点创建详细的靶子视图"""
    
    print(f"🎯 Creating detailed target view for keypoint {kp_idx}")
    
    target_cmap = create_target_colormap()
    
    fig = plt.figure(figsize=(18, 6))
    
    # 三种不同的增强级别
    enhancement_levels = [1.0, 2.0, 4.0]
    titles = ["Mild Enhancement", "Moderate Enhancement", "Strong Enhancement"]
    
    for i, (enhancement, title) in enumerate(zip(enhancement_levels, titles)):
        ax = fig.add_subplot(1, 3, i+1, projection='3d')
        
        # 增强热图
        enhanced_heatmap = enhance_heatmap_for_target_effect(heatmap, enhancement)
        
        # 创建分层显示
        # 1. 最低层：背景点云
        background_mask = enhanced_heatmap < 0.02
        if np.any(background_mask):
            ax.scatter(point_cloud[background_mask, 0],
                      point_cloud[background_mask, 1], 
                      point_cloud[background_mask, 2],
                      c='#E0E0E0', s=0.5, alpha=0.3)
        
        # 2. 中间层：低置信度区域
        low_conf_mask = (enhanced_heatmap >= 0.02) & (enhanced_heatmap < 0.3)
        if np.any(low_conf_mask):
            ax.scatter(point_cloud[low_conf_mask, 0],
                      point_cloud[low_conf_mask, 1],
                      point_cloud[low_conf_mask, 2],
                      c=enhanced_heatmap[low_conf_mask], 
                      cmap=target_cmap, s=2, alpha=0.6, vmin=0, vmax=1)
        
        # 3. 高层：高置信度区域 - 靶子中心
        high_conf_mask = enhanced_heatmap >= 0.3
        if np.any(high_conf_mask):
            scatter = ax.scatter(point_cloud[high_conf_mask, 0],
                               point_cloud[high_conf_mask, 1],
                               point_cloud[high_conf_mask, 2],
                               c=enhanced_heatmap[high_conf_mask], 
                               cmap=target_cmap, s=8, alpha=0.9, vmin=0, vmax=1)
        
        # 4. 最高层：峰值点 - 靶心
        peak_mask = enhanced_heatmap >= 0.8
        if np.any(peak_mask):
            ax.scatter(point_cloud[peak_mask, 0],
                      point_cloud[peak_mask, 1],
                      point_cloud[peak_mask, 2],
                      c='white', s=15, marker='o', 
                      edgecolor='red', linewidth=1, alpha=1.0)
        
        # 添加关键点标记
        ax.scatter(true_keypoint[0], true_keypoint[1], true_keypoint[2],
                  c='black', s=200, marker='*', edgecolor='yellow', 
                  linewidth=3, alpha=1.0, label='Ground Truth')
        
        ax.scatter(pred_keypoint[0], pred_keypoint[1], pred_keypoint[2],
                  c='yellow', s=150, marker='o', edgecolor='black', 
                  linewidth=3, alpha=1.0, label='Predicted')
        
        # 误差线
        ax.plot([pred_keypoint[0], true_keypoint[0]],
               [pred_keypoint[1], true_keypoint[1]],
               [pred_keypoint[2], true_keypoint[2]],
               color='red', linestyle='-', linewidth=3, alpha=0.8)
        
        # 计算误差
        error = np.linalg.norm(pred_keypoint - true_keypoint)
        
        # 设置标题
        ax.set_title(f'{title}\n{KEYPOINT_NAMES[kp_idx]}\n'
                    f'Conf: {confidence:.3f}, Error: {error:.1f}mm',
                    fontsize=12, fontweight='bold')
        
        # 设置视角和范围
        center = pred_keypoint
        radius = 20
        ax.set_xlim([center[0] - radius, center[0] + radius])
        ax.set_ylim([center[1] - radius, center[1] + radius])
        ax.set_zlim([center[2] - radius, center[2] + radius])
        
        # 选择最佳视角
        if kp_idx in [0, 1]:  # ASIS
            ax.view_init(elev=30, azim=45)
        elif kp_idx == 6:     # SP
            ax.view_init(elev=30, azim=225)
        else:
            ax.view_init(elev=45, azim=0)
        
        ax.set_xlabel('X (mm)')
        ax.set_ylabel('Y (mm)')
        ax.set_zlabel('Z (mm)')
        
        # 添加颜色条
        if i == 2 and np.any(high_conf_mask):  # 只在最后一个子图添加
            cbar = plt.colorbar(scatter, ax=ax, shrink=0.8, pad=0.1)
            cbar.set_label('Confidence (Target Effect)', fontsize=10)
        
        # 图例
        if i == 0:
            ax.legend(fontsize=10)
        
        # 添加统计信息
        max_conf = np.max(enhanced_heatmap)
        high_conf_points = np.sum(enhanced_heatmap > 0.5)
        total_points = len(enhanced_heatmap)
        
        stats_text = f'Max: {max_conf:.3f}\nHigh: {high_conf_points}/{total_points}'
        ax.text2D(0.02, 0.98, stats_text, transform=ax.transAxes, 
                 fontsize=9, verticalalignment='top',
                 bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    
    save_path = f'target_detail_{sample_id}_kp{kp_idx}_{KEYPOINT_NAMES[kp_idx]}.png'
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"   📊 Detailed target view saved: {save_path}")
    plt.close()

def create_all_keypoints_target_overview(point_cloud, pred_heatmaps, true_keypoints, 
                                        pred_keypoints, confidences, sample_id):
    """创建所有关键点的靶子效果概览"""
    
    print(f"🎯 Creating all keypoints target overview")
    
    target_cmap = create_target_colormap()
    
    fig = plt.figure(figsize=(24, 18))
    
    for i in range(12):
        ax = fig.add_subplot(3, 4, i+1, projection='3d')
        
        heatmap = pred_heatmaps[:, i]
        enhanced_heatmap = enhance_heatmap_for_target_effect(heatmap, 3.0)
        
        # 背景点云
        background_mask = enhanced_heatmap < 0.1
        if np.any(background_mask):
            ax.scatter(point_cloud[background_mask, 0],
                      point_cloud[background_mask, 1],
                      point_cloud[background_mask, 2],
                      c='lightgray', s=0.2, alpha=0.2)
        
        # 靶子效果热图
        target_mask = enhanced_heatmap >= 0.1
        if np.any(target_mask):
            scatter = ax.scatter(point_cloud[target_mask, 0],
                               point_cloud[target_mask, 1],
                               point_cloud[target_mask, 2],
                               c=enhanced_heatmap[target_mask],
                               cmap=target_cmap, s=2, alpha=0.8, vmin=0, vmax=1)
        
        # 峰值点 - 靶心
        peak_mask = enhanced_heatmap >= 0.7
        if np.any(peak_mask):
            ax.scatter(point_cloud[peak_mask, 0],
                      point_cloud[peak_mask, 1],
                      point_cloud[peak_mask, 2],
                      c='white', s=8, marker='o', 
                      edgecolor='red', linewidth=1)
        
        # 关键点标记
        ax.scatter(true_keypoints[i, 0], true_keypoints[i, 1], true_keypoints[i, 2],
                  c='black', s=80, marker='*', edgecolor='white', linewidth=1)
        
        ax.scatter(pred_keypoints[i, 0], pred_keypoints[i, 1], pred_keypoints[i, 2],
                  c='yellow', s=60, marker='o', edgecolor='black', linewidth=1)
        
        # 计算误差
        error = np.linalg.norm(pred_keypoints[i] - true_keypoints[i])
        
        # 设置标题
        ax.set_title(f'{KEYPOINT_NAMES[i]}\nConf: {confidences[i]:.3f}\nErr: {error:.1f}mm',
                    fontsize=10, fontweight='bold')
        
        # 设置局部视图
        center = pred_keypoints[i]
        radius = 15
        ax.set_xlim([center[0] - radius, center[0] + radius])
        ax.set_ylim([center[1] - radius, center[1] + radius])
        ax.set_zlim([center[2] - radius, center[2] + radius])
        
        # 简化坐标轴
        ax.set_xlabel('X', fontsize=8)
        ax.set_ylabel('Y', fontsize=8)
        ax.set_zlabel('Z', fontsize=8)
        ax.tick_params(labelsize=6)
        
        # 设置视角
        ax.view_init(elev=30, azim=45)
    
    # 添加全局颜色条
    if np.any(target_mask):
        cbar_ax = fig.add_axes([0.92, 0.15, 0.02, 0.7])
        cbar = plt.colorbar(scatter, cax=cbar_ax)
        cbar.set_label('Confidence (Target Effect)', fontsize=12)
    
    plt.suptitle(f'All Keypoints Target-Style Heatmap - Sample {sample_id}\n'
                f'Bullseye Pattern: Red/White Center (High Confidence) → Blue Edge (Low Confidence)', 
                fontsize=16, fontweight='bold')
    
    plt.tight_layout(rect=[0, 0, 0.9, 0.94])
    
    save_path = f'all_keypoints_target_{sample_id}.png'
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"   📊 All keypoints target overview saved: {save_path}")
    plt.close()

def main():
    """主函数"""
    print("🎯 Target-Style Heatmap Visualization System")
    print("Creating bullseye-effect heatmaps with confidence gradients")
    print("=" * 80)
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model.eval()
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # 选择代表性样本
    demo_indices = [0, 5]  # 减少样本数量以便详细展示
    
    for idx in demo_indices:
        sample_id = sample_ids[idx]
        point_cloud = point_clouds[idx]
        true_keypoints = keypoints[idx]
        
        print(f"\n🎯 Processing sample: {sample_id}")
        
        # 采样点云
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            pc_sampled = point_cloud[indices]
        else:
            pc_sampled = point_cloud
        
        # 预测热图
        pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
        
        with torch.no_grad():
            pred_heatmaps = model(pc_tensor)
        
        pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T  # [N, 12]
        pred_keypoints, confidences = extract_keypoints_from_heatmaps(
            pred_heatmaps_np.T, pc_sampled
        )
        
        # 计算误差
        errors = []
        for i in range(12):
            error = np.linalg.norm(pred_keypoints[i] - true_keypoints[i])
            errors.append(error)
        
        # 创建靶子风格可视化
        create_target_style_visualization(
            pc_sampled, pred_heatmaps_np, true_keypoints, 
            pred_keypoints, confidences, sample_id
        )
        
        # 创建所有关键点概览
        create_all_keypoints_target_overview(
            pc_sampled, pred_heatmaps_np, true_keypoints,
            pred_keypoints, confidences, sample_id
        )
        
        # 为最有趣的关键点创建详细视图
        interesting_kp = np.argmax(confidences)  # 置信度最高的
        create_single_keypoint_target_view(
            pc_sampled, pred_heatmaps_np[:, interesting_kp], 
            true_keypoints[interesting_kp], pred_keypoints[interesting_kp],
            confidences[interesting_kp], interesting_kp, sample_id
        )
        
        print(f"   Average error: {np.mean(errors):.2f}mm")
        print(f"   Average confidence: {np.mean(confidences):.3f}")
        print(f"   Best keypoint: {KEYPOINT_NAMES[interesting_kp]} (conf: {confidences[interesting_kp]:.3f})")
    
    print(f"\n🎉 Target-Style Heatmap Visualization Complete!")
    print(f"✅ Bullseye effect with confidence gradients")
    print(f"✅ Red/white center (high confidence) → Blue edge (low confidence)")
    print(f"✅ Multiple enhancement levels")
    print(f"✅ Detailed single keypoint views")
    print(f"✅ Comprehensive overview")

if __name__ == "__main__":
    main()
