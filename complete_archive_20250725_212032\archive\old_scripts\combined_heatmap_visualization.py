#!/usr/bin/env python3
"""
组合热力图可视化
将所有关键点的热力图叠加在一起显示
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps

# 关键点名称
KEYPOINT_NAMES = {
    0: "L-ASIS", 1: "R-ASIS", 2: "L-PSIS", 3: "R-PSIS",
    4: "L-IC", 5: "R-IC", 6: "SP", 7: "L-SIJ", 8: "R-SIJ",
    9: "L-IS", 10: "R-IS", 11: "CT"
}

def create_combined_colormap():
    """创建组合热力图的彩虹色配色方案"""
    colors = [
        '#F8F8FF',  # 幽灵白 - 背景
        '#E6E6FA',  # 薰衣草色
        '#9370DB',  # 中紫色
        '#4169E1',  # 皇家蓝
        '#00BFFF',  # 深天蓝
        '#00FFFF',  # 青色
        '#00FF7F',  # 春绿色
        '#ADFF2F',  # 绿黄色
        '#FFFF00',  # 黄色
        '#FFD700',  # 金色
        '#FFA500',  # 橙色
        '#FF4500',  # 橙红色
        '#FF0000',  # 红色
        '#DC143C',  # 深红色
        '#8B0000'   # 暗红色 - 最热
    ]
    return LinearSegmentedColormap.from_list('combined_rainbow', colors, N=512)

def combine_all_heatmaps(pred_heatmaps, method='max'):
    """组合所有关键点的热力图"""
    
    if method == 'max':
        # 取每个点在所有关键点中的最大置信度
        combined = np.max(pred_heatmaps, axis=0)
    elif method == 'sum':
        # 累加所有关键点的置信度
        combined = np.sum(pred_heatmaps, axis=0)
    elif method == 'mean':
        # 平均所有关键点的置信度
        combined = np.mean(pred_heatmaps, axis=0)
    elif method == 'weighted':
        # 加权组合（给重要关键点更高权重）
        weights = np.array([1.2, 1.2, 1.0, 1.0, 1.1, 1.1, 1.3, 1.0, 1.0, 1.1, 1.1, 1.2])  # 12个权重
        weighted_heatmaps = pred_heatmaps * weights[:, np.newaxis]
        combined = np.max(weighted_heatmaps, axis=0)
    
    # 归一化到[0,1]
    if np.max(combined) > 0:
        combined = combined / np.max(combined)
    
    return combined

def create_combined_heatmap_visualization(point_cloud, pred_heatmaps, true_keypoints, 
                                        pred_keypoints, sampled_pc, sample_id):
    """创建组合热力图可视化"""
    
    print(f"🌈 Creating combined heatmap visualization for sample {sample_id}")
    
    combined_cmap = create_combined_colormap()
    
    # 创建2x2子图，展示不同的组合方法
    fig = plt.figure(figsize=(20, 16))
    
    methods = ['max', 'sum', 'mean', 'weighted']
    method_titles = ['Maximum Confidence', 'Sum of All', 'Average Confidence', 'Weighted Combination']
    
    for i, (method, title) in enumerate(zip(methods, method_titles)):
        ax = fig.add_subplot(2, 2, i+1, projection='3d')
        
        # 组合热力图
        combined_heatmap = combine_all_heatmaps(pred_heatmaps, method)
        
        # 采样用于显示
        display_pc = sampled_pc
        display_heatmap = combined_heatmap
        
        if len(display_pc) > 8000:
            sample_indices = np.random.choice(len(display_pc), 8000, replace=False)
            display_pc = display_pc[sample_indices]
            display_heatmap = display_heatmap[sample_indices]
        
        # 分层显示不同置信度的点
        
        # 1. 背景点 - 极低置信度 (0-0.05)
        bg_mask = display_heatmap < 0.05
        if np.any(bg_mask):
            ax.scatter(display_pc[bg_mask, 0], display_pc[bg_mask, 1], display_pc[bg_mask, 2],
                      c=display_heatmap[bg_mask], cmap=combined_cmap, 
                      s=0.3, alpha=0.2, vmin=0, vmax=1)
        
        # 2. 低置信度区域 (0.05-0.15)
        low_mask = (display_heatmap >= 0.05) & (display_heatmap < 0.15)
        if np.any(low_mask):
            ax.scatter(display_pc[low_mask, 0], display_pc[low_mask, 1], display_pc[low_mask, 2],
                      c=display_heatmap[low_mask], cmap=combined_cmap, 
                      s=0.8, alpha=0.4, vmin=0, vmax=1)
        
        # 3. 中等置信度区域 (0.15-0.35)
        mid_mask = (display_heatmap >= 0.15) & (display_heatmap < 0.35)
        if np.any(mid_mask):
            ax.scatter(display_pc[mid_mask, 0], display_pc[mid_mask, 1], display_pc[mid_mask, 2],
                      c=display_heatmap[mid_mask], cmap=combined_cmap, 
                      s=2, alpha=0.6, vmin=0, vmax=1)
        
        # 4. 高置信度区域 (0.35-0.6)
        high_mask = (display_heatmap >= 0.35) & (display_heatmap < 0.6)
        if np.any(high_mask):
            ax.scatter(display_pc[high_mask, 0], display_pc[high_mask, 1], display_pc[high_mask, 2],
                      c=display_heatmap[high_mask], cmap=combined_cmap, 
                      s=5, alpha=0.8, vmin=0, vmax=1)
        
        # 5. 极高置信度区域 (0.6-0.85)
        very_high_mask = (display_heatmap >= 0.6) & (display_heatmap < 0.85)
        if np.any(very_high_mask):
            scatter = ax.scatter(display_pc[very_high_mask, 0], display_pc[very_high_mask, 1], display_pc[very_high_mask, 2],
                               c=display_heatmap[very_high_mask], cmap=combined_cmap, 
                               s=12, alpha=0.9, vmin=0, vmax=1)
        
        # 6. 峰值区域 (0.85-1.0)
        peak_mask = display_heatmap >= 0.85
        if np.any(peak_mask):
            ax.scatter(display_pc[peak_mask, 0], display_pc[peak_mask, 1], display_pc[peak_mask, 2],
                      c='white', s=25, marker='o', 
                      edgecolor='darkred', linewidth=2, alpha=1.0, zorder=10)
        
        # 显示所有关键点
        # 真实关键点 - 黑色星星
        ax.scatter(true_keypoints[:, 0], true_keypoints[:, 1], true_keypoints[:, 2],
                  c='black', s=200, marker='*', 
                  edgecolor='white', linewidth=2, 
                  alpha=1.0, label='True Keypoints', zorder=15)
        
        # 预测关键点 - 红色十字
        ax.scatter(pred_keypoints[:, 0], pred_keypoints[:, 1], pred_keypoints[:, 2],
                  c='red', s=150, marker='x', 
                  linewidth=3, alpha=1.0, label='Predicted', zorder=15)
        
        # 连接线
        for j in range(len(true_keypoints)):
            ax.plot([true_keypoints[j, 0], pred_keypoints[j, 0]], 
                    [true_keypoints[j, 1], pred_keypoints[j, 1]], 
                    [true_keypoints[j, 2], pred_keypoints[j, 2]], 
                    'k--', alpha=0.6, linewidth=1, zorder=12)
        
        # 计算统计信息
        max_confidence = np.max(display_heatmap)
        high_conf_points = np.sum(display_heatmap > 0.6)
        peak_points = np.sum(display_heatmap > 0.85)
        
        # 计算平均误差
        errors = [np.linalg.norm(pred_keypoints[j] - true_keypoints[j]) 
                 for j in range(len(true_keypoints))]
        avg_error = np.mean(errors)
        
        # 设置标题
        ax.set_title(f'{title}\n'
                    f'Max Conf: {max_confidence:.3f}\n'
                    f'Hot Points: {high_conf_points}\n'
                    f'Peaks: {peak_points}\n'
                    f'Avg Error: {avg_error:.1f}mm',
                    fontsize=12, fontweight='bold', pad=15)
        
        ax.set_xlabel('X (mm)', fontsize=10)
        ax.set_ylabel('Y (mm)', fontsize=10)
        ax.set_zlabel('Z (mm)', fontsize=10)
        ax.view_init(elev=20, azim=45)
        ax.grid(True, alpha=0.3)
        
        # 添加图例（只在第一个子图）
        if i == 0:
            ax.legend(loc='upper left', fontsize=10)
        
        # 添加置信度分布统计
        stats_text = f'Confidence Distribution:\n'
        stats_text += f'Peak (>0.85): {np.sum(display_heatmap > 0.85)}\n'
        stats_text += f'Very High (0.6-0.85): {np.sum((display_heatmap >= 0.6) & (display_heatmap < 0.85))}\n'
        stats_text += f'High (0.35-0.6): {np.sum((display_heatmap >= 0.35) & (display_heatmap < 0.6))}\n'
        stats_text += f'Medium (0.15-0.35): {np.sum((display_heatmap >= 0.15) & (display_heatmap < 0.35))}\n'
        stats_text += f'Low (<0.15): {np.sum(display_heatmap < 0.15)}'
        
        ax.text2D(0.02, 0.02, stats_text, transform=ax.transAxes, 
                 fontsize=8, verticalalignment='bottom',
                 bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # 添加颜色条
    if 'scatter' in locals():
        cbar = plt.colorbar(scatter, ax=fig.get_axes(), shrink=0.6, aspect=40)
        cbar.set_label('🌈 Combined Heatmap Confidence\n(All Keypoints Together)', 
                      fontsize=12, fontweight='bold')
    
    plt.suptitle(f'🌈 Combined Heatmap Visualization 🌈\n'
                f'Sample {sample_id} - All 12 Keypoints Combined in One View', 
                fontsize=16, fontweight='bold')
    
    plt.tight_layout(rect=[0, 0, 0.95, 0.92])
    
    # 保存
    filename = f'combined_heatmap_{sample_id}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"   🌈 Combined heatmap saved: {filename}")
    plt.close()

def create_single_combined_view(point_cloud, pred_heatmaps, true_keypoints, 
                              pred_keypoints, sampled_pc, sample_id, method='weighted'):
    """创建单一的组合视图 - 最佳效果"""
    
    print(f"🎯 Creating single combined view for sample {sample_id}")
    
    combined_cmap = create_combined_colormap()
    
    fig = plt.figure(figsize=(16, 12))
    ax = fig.add_subplot(111, projection='3d')
    
    # 组合热力图
    combined_heatmap = combine_all_heatmaps(pred_heatmaps, method)
    
    # 采样用于显示
    display_pc = sampled_pc
    display_heatmap = combined_heatmap
    
    if len(display_pc) > 10000:
        sample_indices = np.random.choice(len(display_pc), 10000, replace=False)
        display_pc = display_pc[sample_indices]
        display_heatmap = display_heatmap[sample_indices]
    
    # 显示热力图点云 - 更精细的分层
    scatter = ax.scatter(display_pc[:, 0], display_pc[:, 1], display_pc[:, 2],
                        c=display_heatmap, cmap=combined_cmap, 
                        s=3, alpha=0.7, vmin=0, vmax=1)
    
    # 突出显示高置信度区域
    peak_mask = display_heatmap > 0.8
    if np.any(peak_mask):
        ax.scatter(display_pc[peak_mask, 0], display_pc[peak_mask, 1], display_pc[peak_mask, 2],
                  c='white', s=15, marker='o', 
                  edgecolor='darkred', linewidth=1, alpha=1.0, zorder=10)
    
    # 显示所有关键点
    # 真实关键点 - 大黑星
    ax.scatter(true_keypoints[:, 0], true_keypoints[:, 1], true_keypoints[:, 2],
              c='black', s=300, marker='*', 
              edgecolor='white', linewidth=3, 
              alpha=1.0, label='Ground Truth', zorder=20)
    
    # 预测关键点 - 大红十字
    ax.scatter(pred_keypoints[:, 0], pred_keypoints[:, 1], pred_keypoints[:, 2],
              c='red', s=250, marker='x', 
              linewidth=4, alpha=1.0, label='Predictions', zorder=20)
    
    # 连接线
    for j in range(len(true_keypoints)):
        ax.plot([true_keypoints[j, 0], pred_keypoints[j, 0]], 
                [true_keypoints[j, 1], pred_keypoints[j, 1]], 
                [true_keypoints[j, 2], pred_keypoints[j, 2]], 
                'k-', alpha=0.7, linewidth=2, zorder=15)
    
    # 添加关键点标签
    for j in range(len(true_keypoints)):
        ax.text(true_keypoints[j, 0], true_keypoints[j, 1], true_keypoints[j, 2] + 5,
                KEYPOINT_NAMES[j], fontsize=8, ha='center', va='bottom',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
    
    # 计算统计信息
    errors = [np.linalg.norm(pred_keypoints[j] - true_keypoints[j]) 
             for j in range(len(true_keypoints))]
    avg_error = np.mean(errors)
    max_confidence = np.max(display_heatmap)
    
    ax.set_title(f'Combined Heatmap - All 12 Keypoints\n'
                f'Sample {sample_id} | Avg Error: {avg_error:.1f}mm | Max Conf: {max_confidence:.3f}',
                fontsize=14, fontweight='bold', pad=20)
    
    ax.set_xlabel('X (mm)', fontsize=12)
    ax.set_ylabel('Y (mm)', fontsize=12)
    ax.set_zlabel('Z (mm)', fontsize=12)
    ax.legend(fontsize=12)
    ax.view_init(elev=20, azim=45)
    ax.grid(True, alpha=0.3)
    
    # 添加颜色条
    cbar = plt.colorbar(scatter, ax=ax, shrink=0.8, aspect=30)
    cbar.set_label('Combined Confidence (All Keypoints)', fontsize=12, fontweight='bold')
    
    plt.tight_layout()
    
    # 保存
    filename = f'single_combined_view_{sample_id}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"   🎯 Single combined view saved: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🌈 Combined Heatmap Visualization")
    print("Display all keypoints together in combined heatmaps")
    print("=" * 60)
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model.eval()
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # 选择样本进行展示
    demo_samples = [0, 1, 2]  # 展示前3个样本
    
    for sample_idx in demo_samples:
        sample_id = sample_ids[sample_idx]
        point_cloud = point_clouds[sample_idx]
        true_keypoints = keypoints[sample_idx]
        
        print(f"\n🌈 Processing sample: {sample_id}")
        
        # 采样点云用于预测
        if len(point_cloud) > 8192:
            indices = np.random.choice(len(point_cloud), 8192, replace=False)
            pc_sampled = point_cloud[indices]
        else:
            pc_sampled = point_cloud
        
        # 预测
        pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
        
        with torch.no_grad():
            pred_heatmaps = model(pc_tensor)
        
        # 获取热力图和关键点
        pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze()  # [12, N]
        pred_keypoints, confidences = extract_keypoints_from_heatmaps(
            pred_heatmaps_np, pc_sampled
        )
        
        print(f"   📊 Point cloud size: {len(point_cloud)}")
        print(f"   🎯 Predicted {len(pred_keypoints)} keypoints")
        print(f"   📈 Average confidence: {np.mean(confidences):.3f}")
        
        # 创建组合热力图可视化 (4种方法对比)
        create_combined_heatmap_visualization(
            point_cloud, pred_heatmaps_np, true_keypoints, 
            pred_keypoints, pc_sampled, sample_id
        )
        
        # 创建单一最佳视图
        create_single_combined_view(
            point_cloud, pred_heatmaps_np, true_keypoints, 
            pred_keypoints, pc_sampled, sample_id, method='weighted'
        )
    
    print(f"\n🎉 Combined Heatmap Visualization Complete!")
    print("✅ All keypoints displayed together")
    print("✅ Multiple combination methods compared")
    print("✅ Single best view for each sample")
    print("✅ Rainbow target effects with all keypoints combined")

if __name__ == "__main__":
    main()
