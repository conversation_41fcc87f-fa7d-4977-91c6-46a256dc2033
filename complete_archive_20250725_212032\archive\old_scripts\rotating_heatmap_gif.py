#!/usr/bin/env python3
"""
旋转热力图GIF生成器
创建3D组合热力图的旋转动画
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.animation import FuncAnimation, PillowWriter
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps
import os

# 关键点名称
KEYPOINT_NAMES = {
    0: "L-ASIS", 1: "R-ASIS", 2: "L-PSIS", 3: "R-PSIS",
    4: "L-IC", 5: "R-IC", 6: "SP", 7: "L-SIJ", 8: "R-SIJ",
    9: "L-IS", 10: "R-IS", 11: "CT"
}

def create_gif_colormap():
    """创建GIF专用的鲜艳彩虹色配色方案"""
    colors = [
        '#F8F8FF',  # 幽灵白 - 背景
        '#E6E6FA',  # 薰衣草色
        '#9370DB',  # 中紫色
        '#4169E1',  # 皇家蓝
        '#00BFFF',  # 深天蓝
        '#00FFFF',  # 青色
        '#00FF7F',  # 春绿色
        '#ADFF2F',  # 绿黄色
        '#FFFF00',  # 黄色
        '#FFD700',  # 金色
        '#FFA500',  # 橙色
        '#FF4500',  # 橙红色
        '#FF0000',  # 红色
        '#DC143C',  # 深红色
        '#8B0000'   # 暗红色
    ]
    return LinearSegmentedColormap.from_list('gif_rainbow', colors, N=256)

def combine_heatmaps_for_gif(pred_heatmaps, method='weighted'):
    """为GIF组合热力图"""
    if method == 'weighted':
        # 给重要关键点更高权重
        weights = np.array([1.3, 1.3, 1.1, 1.1, 1.2, 1.2, 1.5, 1.1, 1.1, 1.2, 1.2, 1.4])  # 12个权重
        weighted_heatmaps = pred_heatmaps * weights[:, np.newaxis]
        combined = np.max(weighted_heatmaps, axis=0)
    else:
        combined = np.max(pred_heatmaps, axis=0)
    
    # 归一化并增强对比度
    if np.max(combined) > 0:
        combined = combined / np.max(combined)
        combined = np.power(combined, 0.7)  # 增强对比度
    
    return combined

class RotatingHeatmapGIF:
    """旋转热力图GIF生成器"""
    
    def __init__(self, point_cloud, pred_heatmaps, true_keypoints, pred_keypoints, 
                 sampled_pc, sample_id):
        self.point_cloud = point_cloud
        self.pred_heatmaps = pred_heatmaps
        self.true_keypoints = true_keypoints
        self.pred_keypoints = pred_keypoints
        self.sampled_pc = sampled_pc
        self.sample_id = sample_id
        self.gif_cmap = create_gif_colormap()
        
        # 准备数据
        self.setup_data()
        
    def setup_data(self):
        """准备GIF数据"""
        print(f"🎬 Preparing data for GIF animation...")
        
        # 组合热力图
        self.combined_heatmap = combine_heatmaps_for_gif(self.pred_heatmaps, 'weighted')
        
        # 采样用于显示 - 更多点以获得更好的视觉效果
        self.display_pc = self.sampled_pc
        self.display_heatmap = self.combined_heatmap
        
        if len(self.display_pc) > 6000:
            sample_indices = np.random.choice(len(self.display_pc), 6000, replace=False)
            self.display_pc = self.display_pc[sample_indices]
            self.display_heatmap = self.display_heatmap[sample_indices]
        
        # 分层数据 - 为了更好的视觉效果
        self.bg_mask = self.display_heatmap < 0.1
        self.low_mask = (self.display_heatmap >= 0.1) & (self.display_heatmap < 0.3)
        self.mid_mask = (self.display_heatmap >= 0.3) & (self.display_heatmap < 0.6)
        self.high_mask = (self.display_heatmap >= 0.6) & (self.display_heatmap < 0.85)
        self.peak_mask = self.display_heatmap >= 0.85
        
        # 计算统计信息
        errors = [np.linalg.norm(self.pred_keypoints[j] - self.true_keypoints[j]) 
                 for j in range(len(self.true_keypoints))]
        self.avg_error = np.mean(errors)
        self.max_confidence = np.max(self.display_heatmap)
        
        print(f"   📊 Display points: {len(self.display_pc)}")
        print(f"   🎯 Average error: {self.avg_error:.1f}mm")
        print(f"   📈 Max confidence: {self.max_confidence:.3f}")
    
    def create_frame(self, angle):
        """创建单帧"""
        fig = plt.figure(figsize=(12, 10), facecolor='black')
        ax = fig.add_subplot(111, projection='3d', facecolor='black')
        
        # 设置背景为黑色
        ax.xaxis.pane.fill = False
        ax.yaxis.pane.fill = False
        ax.zaxis.pane.fill = False
        ax.xaxis.pane.set_edgecolor('white')
        ax.yaxis.pane.set_edgecolor('white')
        ax.zaxis.pane.set_edgecolor('white')
        ax.xaxis.pane.set_alpha(0.1)
        ax.yaxis.pane.set_alpha(0.1)
        ax.zaxis.pane.set_alpha(0.1)
        
        # 分层显示热力图点云
        
        # 1. 背景点
        if np.any(self.bg_mask):
            ax.scatter(self.display_pc[self.bg_mask, 0], 
                      self.display_pc[self.bg_mask, 1], 
                      self.display_pc[self.bg_mask, 2],
                      c=self.display_heatmap[self.bg_mask], 
                      cmap=self.gif_cmap, s=0.5, alpha=0.3, vmin=0, vmax=1)
        
        # 2. 低置信度
        if np.any(self.low_mask):
            ax.scatter(self.display_pc[self.low_mask, 0], 
                      self.display_pc[self.low_mask, 1], 
                      self.display_pc[self.low_mask, 2],
                      c=self.display_heatmap[self.low_mask], 
                      cmap=self.gif_cmap, s=1.5, alpha=0.6, vmin=0, vmax=1)
        
        # 3. 中等置信度
        if np.any(self.mid_mask):
            ax.scatter(self.display_pc[self.mid_mask, 0], 
                      self.display_pc[self.mid_mask, 1], 
                      self.display_pc[self.mid_mask, 2],
                      c=self.display_heatmap[self.mid_mask], 
                      cmap=self.gif_cmap, s=4, alpha=0.8, vmin=0, vmax=1)
        
        # 4. 高置信度
        if np.any(self.high_mask):
            scatter = ax.scatter(self.display_pc[self.high_mask, 0], 
                               self.display_pc[self.high_mask, 1], 
                               self.display_pc[self.high_mask, 2],
                               c=self.display_heatmap[self.high_mask], 
                               cmap=self.gif_cmap, s=8, alpha=0.9, vmin=0, vmax=1)
        
        # 5. 峰值点 - 白色亮点
        if np.any(self.peak_mask):
            ax.scatter(self.display_pc[self.peak_mask, 0], 
                      self.display_pc[self.peak_mask, 1], 
                      self.display_pc[self.peak_mask, 2],
                      c='white', s=20, marker='o', 
                      edgecolor='yellow', linewidth=1, alpha=1.0, zorder=10)
        
        # 显示关键点
        # 真实关键点 - 大黄星
        ax.scatter(self.true_keypoints[:, 0], self.true_keypoints[:, 1], self.true_keypoints[:, 2],
                  c='yellow', s=400, marker='*', 
                  edgecolor='black', linewidth=2, 
                  alpha=1.0, zorder=20)
        
        # 预测关键点 - 大红十字
        ax.scatter(self.pred_keypoints[:, 0], self.pred_keypoints[:, 1], self.pred_keypoints[:, 2],
                  c='red', s=300, marker='x', 
                  linewidth=4, alpha=1.0, zorder=20)
        
        # 连接线 - 白色
        for j in range(len(self.true_keypoints)):
            ax.plot([self.true_keypoints[j, 0], self.pred_keypoints[j, 0]], 
                    [self.true_keypoints[j, 1], self.pred_keypoints[j, 1]], 
                    [self.true_keypoints[j, 2], self.pred_keypoints[j, 2]], 
                    'w-', alpha=0.8, linewidth=2, zorder=15)
        
        # 设置视角
        ax.view_init(elev=20, azim=angle)
        
        # 设置标题和标签 - 白色文字
        ax.set_title(f'3D Combined Heatmap - Sample {self.sample_id}\n'
                    f'Avg Error: {self.avg_error:.1f}mm | Max Conf: {self.max_confidence:.3f}',
                    fontsize=14, fontweight='bold', color='white', pad=20)
        
        ax.set_xlabel('X (mm)', fontsize=10, color='white')
        ax.set_ylabel('Y (mm)', fontsize=10, color='white')
        ax.set_zlabel('Z (mm)', fontsize=10, color='white')
        
        # 设置坐标轴颜色
        ax.tick_params(colors='white', labelsize=8)
        ax.grid(True, alpha=0.2)
        
        # 添加角度信息
        ax.text2D(0.02, 0.98, f'Angle: {angle:.0f}°', transform=ax.transAxes, 
                 fontsize=12, color='white', fontweight='bold',
                 bbox=dict(boxstyle='round', facecolor='black', alpha=0.7))
        
        # 添加图例
        legend_elements = [
            plt.Line2D([0], [0], marker='*', color='w', markerfacecolor='yellow', 
                      markersize=15, label='Ground Truth', linestyle='None'),
            plt.Line2D([0], [0], marker='x', color='red', markersize=12, 
                      label='Predictions', linestyle='None', markeredgewidth=3)
        ]
        ax.legend(handles=legend_elements, loc='upper right', 
                 fontsize=10, facecolor='black', edgecolor='white')
        
        plt.tight_layout()
        return fig
    
    def create_gif(self, duration=8, fps=15):
        """创建旋转GIF"""
        print(f"🎬 Creating rotating GIF animation...")
        
        # 计算帧数和角度
        total_frames = duration * fps
        angles = np.linspace(0, 360, total_frames, endpoint=False)
        
        print(f"   📹 Duration: {duration}s")
        print(f"   🎞️ FPS: {fps}")
        print(f"   📊 Total frames: {total_frames}")
        
        # 创建动画
        fig = plt.figure(figsize=(12, 10), facecolor='black')
        
        def animate(frame):
            plt.clf()  # 清除当前图形
            angle = angles[frame]
            
            # 重新创建3D轴
            ax = fig.add_subplot(111, projection='3d', facecolor='black')
            
            # 设置背景
            ax.xaxis.pane.fill = False
            ax.yaxis.pane.fill = False
            ax.zaxis.pane.fill = False
            ax.xaxis.pane.set_edgecolor('white')
            ax.yaxis.pane.set_edgecolor('white')
            ax.zaxis.pane.set_edgecolor('white')
            ax.xaxis.pane.set_alpha(0.1)
            ax.yaxis.pane.set_alpha(0.1)
            ax.zaxis.pane.set_alpha(0.1)
            
            # 绘制热力图点云
            if np.any(self.bg_mask):
                ax.scatter(self.display_pc[self.bg_mask, 0], 
                          self.display_pc[self.bg_mask, 1], 
                          self.display_pc[self.bg_mask, 2],
                          c=self.display_heatmap[self.bg_mask], 
                          cmap=self.gif_cmap, s=0.5, alpha=0.3, vmin=0, vmax=1)
            
            if np.any(self.low_mask):
                ax.scatter(self.display_pc[self.low_mask, 0], 
                          self.display_pc[self.low_mask, 1], 
                          self.display_pc[self.low_mask, 2],
                          c=self.display_heatmap[self.low_mask], 
                          cmap=self.gif_cmap, s=1.5, alpha=0.6, vmin=0, vmax=1)
            
            if np.any(self.mid_mask):
                ax.scatter(self.display_pc[self.mid_mask, 0], 
                          self.display_pc[self.mid_mask, 1], 
                          self.display_pc[self.mid_mask, 2],
                          c=self.display_heatmap[self.mid_mask], 
                          cmap=self.gif_cmap, s=4, alpha=0.8, vmin=0, vmax=1)
            
            if np.any(self.high_mask):
                ax.scatter(self.display_pc[self.high_mask, 0], 
                          self.display_pc[self.high_mask, 1], 
                          self.display_pc[self.high_mask, 2],
                          c=self.display_heatmap[self.high_mask], 
                          cmap=self.gif_cmap, s=8, alpha=0.9, vmin=0, vmax=1)
            
            if np.any(self.peak_mask):
                ax.scatter(self.display_pc[self.peak_mask, 0], 
                          self.display_pc[self.peak_mask, 1], 
                          self.display_pc[self.peak_mask, 2],
                          c='white', s=20, marker='o', 
                          edgecolor='yellow', linewidth=1, alpha=1.0, zorder=10)
            
            # 关键点
            ax.scatter(self.true_keypoints[:, 0], self.true_keypoints[:, 1], self.true_keypoints[:, 2],
                      c='yellow', s=400, marker='*', 
                      edgecolor='black', linewidth=2, alpha=1.0, zorder=20)
            
            ax.scatter(self.pred_keypoints[:, 0], self.pred_keypoints[:, 1], self.pred_keypoints[:, 2],
                      c='red', s=300, marker='x', 
                      linewidth=4, alpha=1.0, zorder=20)
            
            # 连接线
            for j in range(len(self.true_keypoints)):
                ax.plot([self.true_keypoints[j, 0], self.pred_keypoints[j, 0]], 
                        [self.true_keypoints[j, 1], self.pred_keypoints[j, 1]], 
                        [self.true_keypoints[j, 2], self.pred_keypoints[j, 2]], 
                        'w-', alpha=0.8, linewidth=2, zorder=15)
            
            # 设置视角
            ax.view_init(elev=20, azim=angle)
            
            # 标题和标签
            ax.set_title(f'3D Combined Heatmap - Sample {self.sample_id}\n'
                        f'Avg Error: {self.avg_error:.1f}mm | Max Conf: {self.max_confidence:.3f}',
                        fontsize=14, fontweight='bold', color='white', pad=20)
            
            ax.set_xlabel('X (mm)', fontsize=10, color='white')
            ax.set_ylabel('Y (mm)', fontsize=10, color='white')
            ax.set_zlabel('Z (mm)', fontsize=10, color='white')
            ax.tick_params(colors='white', labelsize=8)
            ax.grid(True, alpha=0.2)
            
            # 角度信息
            ax.text2D(0.02, 0.98, f'Angle: {angle:.0f}°', transform=ax.transAxes, 
                     fontsize=12, color='white', fontweight='bold',
                     bbox=dict(boxstyle='round', facecolor='black', alpha=0.7))
            
            # 进度信息
            progress = (frame + 1) / total_frames * 100
            print(f"\r   🎬 Rendering frame {frame+1}/{total_frames} ({progress:.1f}%)", end='')
            
            return ax
        
        # 创建动画
        anim = FuncAnimation(fig, animate, frames=total_frames, interval=1000/fps, blit=False)
        
        # 保存GIF
        filename = f'rotating_heatmap_{self.sample_id}.gif'
        print(f"\n   💾 Saving GIF: {filename}")
        
        writer = PillowWriter(fps=fps)
        anim.save(filename, writer=writer, dpi=100)
        
        plt.close()
        print(f"   ✅ GIF saved successfully!")
        return filename

def main():
    """主函数"""
    print("🎬 Rotating Heatmap GIF Generator")
    print("Create 3D rotating animation of combined heatmaps")
    print("=" * 60)
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model.eval()
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # 选择最佳样本 (600086)
    sample_idx = 2  # 600086
    sample_id = sample_ids[sample_idx]
    point_cloud = point_clouds[sample_idx]
    true_keypoints = keypoints[sample_idx]
    
    print(f"\n🎬 Creating GIF for sample: {sample_id}")
    
    # 采样点云用于预测
    if len(point_cloud) > 8192:
        indices = np.random.choice(len(point_cloud), 8192, replace=False)
        pc_sampled = point_cloud[indices]
    else:
        pc_sampled = point_cloud
    
    # 预测
    pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
    
    with torch.no_grad():
        pred_heatmaps = model(pc_tensor)
    
    # 获取热力图和关键点
    pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze()  # [12, N]
    pred_keypoints, confidences = extract_keypoints_from_heatmaps(
        pred_heatmaps_np, pc_sampled
    )
    
    print(f"   📊 Point cloud size: {len(point_cloud)}")
    print(f"   🎯 Predicted {len(pred_keypoints)} keypoints")
    print(f"   📈 Average confidence: {np.mean(confidences):.3f}")
    
    # 创建GIF生成器
    gif_generator = RotatingHeatmapGIF(
        point_cloud, pred_heatmaps_np, true_keypoints, 
        pred_keypoints, pc_sampled, sample_id
    )
    
    # 生成GIF
    gif_filename = gif_generator.create_gif(duration=8, fps=15)
    
    print(f"\n🎉 Rotating GIF Complete!")
    print(f"✅ File saved: {gif_filename}")
    print("✅ 8 seconds duration")
    print("✅ 15 FPS smooth rotation")
    print("✅ 360° full rotation")
    print("✅ Black background for better contrast")
    print("✅ Enhanced rainbow heatmap effects")

if __name__ == "__main__":
    main()
