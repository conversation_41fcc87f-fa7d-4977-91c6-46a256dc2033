import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import cv2
import matplotlib.pyplot as plt
from typing import List, Tuple
import json
import os
import random
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
matplotlib.rcParams['axes.unicode_minus'] = False


def generate_random_connected_shapes(image_dir: str, num_images: int, image_size: Tuple[int, int] = (256, 256)):
    """
    生成随机连通图形
    Args:
        image_dir: 保存图像的目录
        num_images: 要生成的图像数量
        image_size: 图像大小
    """
    for i in range(num_images):
        # 创建空白图像
        image = np.zeros(image_size, dtype=np.uint8)
        
        # 生成基础闭合图形（正方形或矩形）
        margin = 40
        width = random.randint(100, image_size[0]-2*margin)
        height = random.randint(100, image_size[1]-2*margin)
        start_x = random.randint(margin, image_size[0]-width-margin)
        start_y = random.randint(margin, image_size[1]-height-margin)
        
        # 画矩形
        points = [
            (start_x, start_y),
            (start_x + width, start_y),
            (start_x + width, start_y + height),
            (start_x, start_y + height)
        ]
        
        # 连接矩形的边
        for j in range(len(points)):
            cv2.line(image, points[j], points[(j+1)%4], 255, 2)
        
        # 添加对角线或内部线条以创建交叉点
        # 对角线
        cv2.line(image, points[0], points[2], 255, 2)  # 一条对角线
        
        # 随机添加额外的内部线条
        num_extra_lines = random.randint(1, 2)
        for _ in range(num_extra_lines):
            # 随机选择两个边上的点
            p1_edge = random.randint(0, 3)
            p2_edge = (p1_edge + 2) % 4  # 选择对边
            
            # 在边上随机选择点
            if p1_edge % 2 == 0:  # 水平边
                x1 = min(points[p1_edge][0], points[(p1_edge+1)%4][0])
                x2 = max(points[p1_edge][0], points[(p1_edge+1)%4][0])
                y1 = points[p1_edge][1]
                
                x_start = min(points[p2_edge][0], points[(p2_edge+1)%4][0])
                x_end = max(points[p2_edge][0], points[(p2_edge+1)%4][0])
                y2 = points[p2_edge][1]
                
                start_point = (random.randint(x1, x2), y1)
                end_point = (random.randint(x_start, x_end), y2)
            else:  # 垂直边
                x1 = points[p1_edge][0]
                y1 = min(points[p1_edge][1], points[(p1_edge+1)%4][1])
                y2 = max(points[p1_edge][1], points[(p1_edge+1)%4][1])
                
                x2 = points[p2_edge][0]
                y_start = min(points[p2_edge][1], points[(p2_edge+1)%4][1])
                y_end = max(points[p2_edge][1], points[(p2_edge+1)%4][1])
                
                start_point = (x1, random.randint(y1, y2))
                end_point = (x2, random.randint(y_start, y_end))
            
            cv2.line(image, start_point, end_point, 255, 2)
        
        # 保存图像
        image_name = f'image_{i}.png'
        image_path = os.path.join(image_dir, image_name)
        cv2.imwrite(image_path, image)
        
        # 显示图像以便手动标记
        plt.figure(figsize=(8, 8))
        plt.imshow(image, cmap='gray')
        plt.title(f'图像 {i}')
        plt.show()
        
        # 等待用户标记
        input(f"请标记图像 {image_name} 的奇偶点，然后按 Enter 键继续...")

if __name__ == "__main__":
    image_dir = 'dataset/images'
    num_images = 10
    os.makedirs(image_dir, exist_ok=True)
    generate_random_connected_shapes(image_dir, num_images)
    
    print(f"已生成 {num_images} 个随机连通图形并保存在 {image_dir} 目录中。")