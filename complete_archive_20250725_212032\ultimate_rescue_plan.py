#!/usr/bin/env python3
"""
终极救援计划 - 最后的希望
Ultimate Rescue Plan - The Last Hope
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import random
from pathlib import Path
import json
from datetime import datetime
from sklearn.model_selection import train_test_split

class UltimateKeypointNet(nn.Module):
    """终极关键点检测网络 - 集成所有先进技术"""
    
    def __init__(self, input_dim=3, hidden_dim=512, output_dim=19*3, dropout=0.2):
        super().__init__()
        
        # 多分支特征提取器
        self.branch1 = self._make_branch(input_dim, hidden_dim//4, [64, 128])
        self.branch2 = self._make_branch(input_dim, hidden_dim//4, [32, 64, 128])
        self.branch3 = self._make_branch(input_dim, hidden_dim//4, [128, 256])
        self.branch4 = self._make_branch(input_dim, hidden_dim//4, [64, 128, 256])
        
        # 特征融合
        self.fusion_conv = nn.Conv1d(hidden_dim, hidden_dim, 1)
        self.fusion_bn = nn.BatchNorm1d(hidden_dim)
        
        # 多头注意力
        self.multihead_attention = nn.MultiheadAttention(hidden_dim, num_heads=16, batch_first=True)
        
        # 残差块
        self.residual_blocks = nn.ModuleList([
            self._make_residual_block(hidden_dim) for _ in range(3)
        ])
        
        # 自适应池化
        self.adaptive_pool = nn.AdaptiveMaxPool1d(1)
        
        # 关键点回归器 - 多层设计
        self.keypoint_regressor = nn.Sequential(
            nn.Linear(hidden_dim, 1024),
            nn.BatchNorm1d(1024),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(dropout//2),
            
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            
            nn.Linear(128, output_dim)
        )
        
        # 医疗约束层
        self.medical_constraint = MedicalConstraintLayer()
        
    def _make_branch(self, input_dim, output_dim, hidden_dims):
        """创建特征提取分支"""
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Conv1d(prev_dim, hidden_dim, 1),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
            ])
            prev_dim = hidden_dim
        
        layers.extend([
            nn.Conv1d(prev_dim, output_dim, 1),
            nn.BatchNorm1d(output_dim),
            nn.ReLU()
        ])
        
        return nn.Sequential(*layers)
    
    def _make_residual_block(self, dim):
        """创建残差块"""
        return nn.Sequential(
            nn.Conv1d(dim, dim, 1),
            nn.BatchNorm1d(dim),
            nn.ReLU(),
            nn.Conv1d(dim, dim, 1),
            nn.BatchNorm1d(dim)
        )
    
    def forward(self, point_cloud):
        batch_size = point_cloud.size(0)
        x = point_cloud.transpose(2, 1)  # (B, 3, N)
        
        # 多分支特征提取
        feat1 = self.branch1(x)
        feat2 = self.branch2(x)
        feat3 = self.branch3(x)
        feat4 = self.branch4(x)
        
        # 特征融合
        fused_features = torch.cat([feat1, feat2, feat3, feat4], dim=1)
        fused_features = F.relu(self.fusion_bn(self.fusion_conv(fused_features)))
        
        # 残差连接
        for residual_block in self.residual_blocks:
            residual = residual_block(fused_features)
            fused_features = F.relu(fused_features + residual)
        
        # 全局特征
        global_feature = self.adaptive_pool(fused_features).squeeze(-1)  # (B, hidden_dim)
        
        # 多头注意力
        global_feature_expanded = global_feature.unsqueeze(1)
        attended_feature, _ = self.multihead_attention(
            global_feature_expanded, global_feature_expanded, global_feature_expanded
        )
        attended_feature = attended_feature.squeeze(1)
        
        # 预测关键点
        keypoints = self.keypoint_regressor(attended_feature)
        keypoints = keypoints.view(batch_size, 19, 3)
        
        # 医疗约束
        keypoints = self.medical_constraint(keypoints)
        
        return keypoints

class MedicalConstraintLayer(nn.Module):
    """医疗约束层 - 确保解剖学合理性"""
    
    def __init__(self):
        super().__init__()
        
    def forward(self, keypoints):
        """应用医疗约束"""
        # 这里可以添加解剖学约束
        # 例如：某些关键点之间的距离约束、对称性约束等
        
        # 简单的范围约束
        keypoints = torch.clamp(keypoints, min=-100, max=100)
        
        return keypoints

class UltimateAugmentation:
    """终极数据增强器"""
    
    def __init__(self):
        self.augmentation_strategies = [
            self.elastic_deformation,
            self.perspective_transform,
            self.intensity_variation,
            self.local_scaling,
            self.anatomical_aware_rotation
        ]
    
    def elastic_deformation(self, pc, kp):
        """弹性形变"""
        # 简化的弹性形变
        deformation_strength = np.random.uniform(0.01, 0.05)
        deformation = np.random.normal(0, deformation_strength, pc.shape)
        
        # 平滑形变
        for i in range(3):  # 3次平滑
            deformation = 0.8 * deformation + 0.2 * np.roll(deformation, 1, axis=0)
        
        deformed_pc = pc + deformation
        deformed_kp = kp + deformation_strength * np.random.normal(0, 0.1, kp.shape)
        
        return deformed_pc, deformed_kp
    
    def perspective_transform(self, pc, kp):
        """透视变换"""
        # 简化的透视变换
        perspective_strength = np.random.uniform(0.95, 1.05)
        
        # 应用透视效果
        z_factor = perspective_strength + 0.1 * pc[:, 2] / np.max(np.abs(pc[:, 2]))
        transformed_pc = pc.copy()
        transformed_pc[:, :2] *= z_factor[:, np.newaxis]
        
        transformed_kp = kp.copy()
        kp_z_factor = perspective_strength + 0.1 * kp[:, 2] / np.max(np.abs(kp[:, 2]))
        transformed_kp[:, :2] *= kp_z_factor[:, np.newaxis]
        
        return transformed_pc, transformed_kp
    
    def intensity_variation(self, pc, kp):
        """强度变化 (模拟扫描条件变化)"""
        # 添加系统性偏移
        bias = np.random.uniform(-0.5, 0.5, 3)
        pc_varied = pc + bias
        kp_varied = kp + bias
        
        return pc_varied, kp_varied
    
    def local_scaling(self, pc, kp):
        """局部缩放"""
        # 随机选择缩放中心
        center_idx = np.random.choice(len(pc))
        center = pc[center_idx]
        
        # 计算距离
        distances = np.linalg.norm(pc - center, axis=1)
        max_dist = np.max(distances)
        
        # 局部缩放因子
        scale_factor = 1.0 + 0.1 * np.random.uniform(-1, 1) * np.exp(-distances / (max_dist * 0.3))
        
        scaled_pc = center + (pc - center) * scale_factor[:, np.newaxis]
        
        # 关键点也应用相似的缩放
        kp_distances = np.linalg.norm(kp - center, axis=1)
        kp_scale_factor = 1.0 + 0.05 * np.random.uniform(-1, 1) * np.exp(-kp_distances / (max_dist * 0.3))
        scaled_kp = center + (kp - center) * kp_scale_factor[:, np.newaxis]
        
        return scaled_pc, scaled_kp
    
    def anatomical_aware_rotation(self, pc, kp):
        """解剖学感知旋转"""
        # 围绕解剖轴旋转
        axis = np.random.choice(['x', 'y', 'z'])
        angle = np.random.uniform(-0.1, 0.1)  # ±5.7度
        
        if axis == 'x':
            rotation_matrix = np.array([
                [1, 0, 0],
                [0, np.cos(angle), -np.sin(angle)],
                [0, np.sin(angle), np.cos(angle)]
            ])
        elif axis == 'y':
            rotation_matrix = np.array([
                [np.cos(angle), 0, np.sin(angle)],
                [0, 1, 0],
                [-np.sin(angle), 0, np.cos(angle)]
            ])
        else:  # z
            rotation_matrix = np.array([
                [np.cos(angle), -np.sin(angle), 0],
                [np.sin(angle), np.cos(angle), 0],
                [0, 0, 1]
            ])
        
        rotated_pc = pc @ rotation_matrix.T
        rotated_kp = kp @ rotation_matrix.T
        
        return rotated_pc, rotated_kp
    
    def ultimate_augment(self, point_clouds, keypoints, augment_factor=10):
        """终极增强"""
        augmented_pcs = []
        augmented_kps = []
        
        # 原始数据
        for pc, kp in zip(point_clouds, keypoints):
            augmented_pcs.append(pc)
            augmented_kps.append(kp)
        
        # 应用所有增强策略
        for pc, kp in zip(point_clouds, keypoints):
            for _ in range(augment_factor):
                # 随机选择1-3个增强策略组合
                num_strategies = np.random.randint(1, 4)
                strategies = np.random.choice(self.augmentation_strategies, num_strategies, replace=False)
                
                aug_pc, aug_kp = pc.copy(), kp.copy()
                for strategy in strategies:
                    aug_pc, aug_kp = strategy(aug_pc, aug_kp)
                
                augmented_pcs.append(aug_pc)
                augmented_kps.append(aug_kp)
        
        # 确保点云长度一致
        target_points = 4096
        processed_pcs = []
        for pc in augmented_pcs:
            if len(pc) > target_points:
                indices = np.random.choice(len(pc), target_points, replace=False)
                pc = pc[indices]
            elif len(pc) < target_points:
                indices = np.random.choice(len(pc), target_points, replace=True)
                pc = pc[indices]
            processed_pcs.append(pc)
        
        return np.stack(processed_pcs), np.stack(augmented_kps)

class UltimateTrainer:
    """终极训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        self.augmentation = UltimateAugmentation()
        
    def load_data(self, data_path='data/raw/high_quality_f3_dataset.npz'):
        """加载数据"""
        print(f"📦 加载终极救援数据: {data_path}")
        
        data = np.load(data_path, allow_pickle=True)
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        # 数据预处理
        processed_pcs = []
        for pc in point_clouds:
            if len(pc) > 4096:
                indices = np.random.choice(len(pc), 4096, replace=False)
                pc_sampled = pc[indices]
            else:
                indices = np.random.choice(len(pc), 4096, replace=True)
                pc_sampled = pc[indices]
            processed_pcs.append(pc_sampled)
        
        point_clouds = np.array(processed_pcs)
        
        # 数据划分
        indices = np.arange(len(sample_ids))
        train_val_indices, test_indices = train_test_split(
            indices, test_size=0.15, random_state=42
        )
        train_indices, val_indices = train_test_split(
            train_val_indices, test_size=0.18, random_state=42
        )
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
        
        print(f"✅ 终极数据准备完成: 训练{len(train_indices)}, 验证{len(val_indices)}, 测试{len(test_indices)}")
        return self.data
    
    def ultimate_train(self, k_shot, epochs=200, lr=0.0003):
        """终极训练"""
        print(f"\n🔥 终极训练 {k_shot}-shot 模型")
        
        # 创建终极模型
        model = UltimateKeypointNet().to(self.device)
        
        # 高级优化器
        optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-4, amsgrad=True)
        
        # 学习率调度器
        scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer, max_lr=lr*5, epochs=epochs, steps_per_epoch=1,
            pct_start=0.1, anneal_strategy='cos'
        )
        
        # 多重损失函数
        mse_loss = nn.MSELoss()
        smooth_l1_loss = nn.SmoothL1Loss()
        huber_loss = nn.HuberLoss(delta=1.0)
        
        best_val_error = float('inf')
        best_model_state = None
        patience = 0
        max_patience = 50
        
        for epoch in range(epochs):
            model.train()
            
            # 采样训练数据
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                min(k_shot, len(self.data['train']['point_clouds'])), 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 终极数据增强
            aug_pcs, aug_kps = self.augmentation.ultimate_augment(
                train_pcs, train_kps, augment_factor=15
            )
            
            # 分批训练
            batch_size = 8  # 减小批次大小以适应更大的模型
            total_loss = 0
            num_batches = 0
            
            for i in range(0, len(aug_pcs), batch_size):
                batch_pcs = torch.FloatTensor(aug_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(aug_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                
                pred_kps = model(batch_pcs)
                
                # 多重损失
                loss1 = mse_loss(pred_kps, batch_kps)
                loss2 = smooth_l1_loss(pred_kps, batch_kps)
                loss3 = huber_loss(pred_kps, batch_kps)
                
                # 加权组合
                loss = 0.5 * loss1 + 0.3 * loss2 + 0.2 * loss3
                
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
                
                optimizer.step()
                
                total_loss += loss.item()
                num_batches += 1
            
            scheduler.step()
            avg_loss = total_loss / num_batches if num_batches > 0 else 0
            
            # 验证
            if epoch % 10 == 0:
                val_error = self.evaluate_model(model, 'val')
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                    patience = 0
                else:
                    patience += 1
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.3f}, Val_Error={val_error:.2f}mm, LR={optimizer.param_groups[0]['lr']:.6f}")
                
                if patience >= max_patience:
                    print(f"早停在epoch {epoch}")
                    break
        
        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return model, best_val_error
    
    def evaluate_model(self, model, split='test'):
        """评估模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for pc, kp in zip(pcs, kps):
                pc_tensor = torch.FloatTensor(pc).unsqueeze(0).to(self.device)
                kp_tensor = torch.FloatTensor(kp).unsqueeze(0).to(self.device)
                
                pred_kp = model(pc_tensor)
                error = torch.mean(torch.norm(pred_kp - kp_tensor, dim=2))
                total_error += error.item()
                num_samples += 1
        
        return total_error / num_samples if num_samples > 0 else float('inf')

def run_ultimate_rescue():
    """执行终极救援"""
    print("🔥 终极救援任务 - 最后的希望！")
    print("=" * 60)
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    random.seed(42)
    
    # 初始化终极训练器
    trainer = UltimateTrainer()
    
    # 加载数据
    data = trainer.load_data()
    
    # 终极实验 - 只测试最有希望的配置
    shot_configs = [15, 20, 25, 30]  # 增加更多样本
    ultimate_results = {}
    
    for k_shot in shot_configs:
        print(f"\n{'='*60}")
        
        # 终极训练
        model, val_error = trainer.ultimate_train(k_shot, epochs=150, lr=0.0003)
        
        # 测试评估
        test_error = trainer.evaluate_model(model, 'test')
        ultimate_results[k_shot] = test_error
        
        print(f"🔥 {k_shot}-shot 终极结果: {test_error:.2f}mm")
    
    # 终极结果分析
    print(f"\n📊 终极救援结果")
    print("=" * 50)
    
    baseline_error = 15.91  # 之前的最佳救援结果
    
    for k_shot, error in ultimate_results.items():
        improvement = (baseline_error - error) / baseline_error * 100
        if error < 5.0:
            status = "🎉 医疗级成功"
        elif error < 10.0:
            status = "🏆 优秀结果"
        elif error < 15.0:
            status = "👍 良好改进"
        else:
            status = "📈 仍需努力"
        
        print(f"{k_shot:2d}-shot: {error:6.2f}mm (改进: {improvement:+5.1f}%) {status}")
    
    # 最终评估
    best_shot = min(ultimate_results.keys(), key=lambda k: ultimate_results[k])
    best_error = ultimate_results[best_shot]
    
    print(f"\n🏆 终极最佳结果: {best_shot}-shot")
    print(f"🎯 终极误差: {best_error:.2f}mm")
    print(f"📈 总体改进: {(baseline_error - best_error) / baseline_error * 100:+.1f}%")
    
    if best_error < 5.0:
        print("🎉🎉🎉 终极救援大成功！达到医疗级精度！")
    elif best_error < 10.0:
        print("🏆 终极救援成功！接近医疗级水平！")
    elif best_error < 15.0:
        print("👍 终极救援有效！显著改善！")
    else:
        print("😔 即使终极救援也有限，可能需要更多数据...")
    
    return ultimate_results

if __name__ == "__main__":
    results = run_ultimate_rescue()
