#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整基准测试分析 - 不同架构×关键点数×采样点数的全面分析
Full Benchmark Analysis - Comprehensive Analysis of Architecture × Keypoints × Sampling Points
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json

# 设置样式
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

def create_comprehensive_3d_analysis():
    """创建三维综合分析"""
    
    print("📊 创建三维综合分析...")
    
    # 读取结果
    df = pd.read_csv('full_comprehensive_benchmark_results.csv')
    
    print(f"✅ 加载了 {len(df)} 个实验结果")
    print(f"📊 模型类型: {', '.join(df['model'].unique())}")
    print(f"📊 关键点配置: {sorted(df['keypoints'].unique())}")
    print(f"📊 采样点数: {sorted(df['points'].unique())}")
    
    # 创建大型综合分析图
    fig = plt.figure(figsize=(20, 16))
    
    # 配色方案
    colors = {
        'PointNet': '#E74C3C',
        'PointNet++': '#3498DB', 
        'DGCNN': '#2ECC71',
        'Our_Adaptive': '#9B59B6'
    }
    
    # 1. 模型性能总览 (2x2 subplot)
    ax1 = plt.subplot(3, 3, 1)
    model_performance = df.groupby('model')['avg_error'].agg(['mean', 'std']).reset_index()
    
    bars = ax1.bar(model_performance['model'], model_performance['mean'], 
                   yerr=model_performance['std'], capsize=5,
                   color=[colors.get(model, '#95A5A6') for model in model_performance['model']],
                   alpha=0.8, edgecolor='black')
    
    for bar, mean, std in zip(bars, model_performance['mean'], model_performance['std']):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.5, 
                f'{mean:.1f}mm', ha='center', va='bottom', fontweight='bold')
    
    ax1.axhline(y=10, color='orange', linestyle='--', alpha=0.7, label='Medical Grade')
    ax1.set_ylabel('Average Error (mm)')
    ax1.set_title('Overall Model Performance')
    ax1.legend()
    ax1.grid(True, alpha=0.3, axis='y')
    plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')
    
    # 2. 关键点数量影响
    ax2 = plt.subplot(3, 3, 2)
    for model in df['model'].unique():
        model_data = df[df['model'] == model]
        keypoint_perf = model_data.groupby('keypoints')['avg_error'].mean()
        ax2.plot(keypoint_perf.index, keypoint_perf.values, 'o-', 
                color=colors.get(model, '#95A5A6'), linewidth=2, markersize=6, 
                label=model, alpha=0.8)
    
    ax2.set_xlabel('Number of Keypoints')
    ax2.set_ylabel('Average Error (mm)')
    ax2.set_title('Keypoint Count Impact')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 采样点数影响
    ax3 = plt.subplot(3, 3, 3)
    for model in df['model'].unique():
        model_data = df[df['model'] == model]
        points_perf = model_data.groupby('points')['avg_error'].mean()
        ax3.plot(points_perf.index/1000, points_perf.values, 'o-', 
                color=colors.get(model, '#95A5A6'), linewidth=2, markersize=6, 
                label=model, alpha=0.8)
    
    ax3.set_xlabel('Sampling Points (K)')
    ax3.set_ylabel('Average Error (mm)')
    ax3.set_title('Sampling Density Impact')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 热力图 - 模型 vs 关键点数
    ax4 = plt.subplot(3, 3, 4)
    heatmap_data = df.pivot_table(values='avg_error', index='model', columns='keypoints', aggfunc='mean')
    sns.heatmap(heatmap_data, annot=True, fmt='.1f', cmap='RdYlBu_r', ax=ax4)
    ax4.set_title('Model vs Keypoints Heatmap')
    
    # 5. 热力图 - 模型 vs 采样点数
    ax5 = plt.subplot(3, 3, 5)
    heatmap_data2 = df.pivot_table(values='avg_error', index='model', columns='points', aggfunc='mean')
    sns.heatmap(heatmap_data2, annot=True, fmt='.1f', cmap='RdYlBu_r', ax=ax5)
    ax5.set_title('Model vs Sampling Points Heatmap')
    
    # 6. 参数效率分析
    ax6 = plt.subplot(3, 3, 6)
    scatter = ax6.scatter(df['num_params']/1e6, df['avg_error'], 
                         c=[colors.get(model, '#95A5A6') for model in df['model']], 
                         s=60, alpha=0.7, edgecolors='black')
    
    ax6.set_xlabel('Parameters (M)')
    ax6.set_ylabel('Average Error (mm)')
    ax6.set_title('Parameter Efficiency')
    ax6.grid(True, alpha=0.3)
    
    # 7. 医疗级达标率对比
    ax7 = plt.subplot(3, 3, 7)
    medical_rates = df.groupby('model')['medical_rate'].mean()
    
    bars = ax7.bar(medical_rates.index, medical_rates.values,
                   color=[colors.get(model, '#95A5A6') for model in medical_rates.index],
                   alpha=0.8, edgecolor='black')
    
    for bar, rate in zip(bars, medical_rates.values):
        ax7.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    ax7.axhline(y=90, color='red', linestyle='--', alpha=0.7, label='90% Target')
    ax7.set_ylabel('Medical Grade Rate (%)')
    ax7.set_title('Medical Grade Achievement')
    ax7.legend()
    ax7.grid(True, alpha=0.3, axis='y')
    plt.setp(ax7.get_xticklabels(), rotation=45, ha='right')
    
    # 8. 最佳配置展示
    ax8 = plt.subplot(3, 3, 8)
    best_configs = df.nsmallest(8, 'avg_error')
    
    bars = ax8.barh(range(len(best_configs)), best_configs['avg_error'],
                   color=[colors.get(model, '#95A5A6') for model in best_configs['model']],
                   alpha=0.8, edgecolor='black')
    
    ax8.set_yticks(range(len(best_configs)))
    ax8.set_yticklabels([f"{row['model']}\n{row['keypoints']}kp-{int(row['points']/1000)}K" 
                        for _, row in best_configs.iterrows()], fontsize=8)
    ax8.set_xlabel('Average Error (mm)')
    ax8.set_title('Top 8 Best Configurations')
    ax8.grid(True, alpha=0.3, axis='x')
    
    # 9. 训练时间分析
    ax9 = plt.subplot(3, 3, 9)
    training_times = df.groupby('model')['training_time'].mean()
    
    bars = ax9.bar(training_times.index, training_times.values,
                   color=[colors.get(model, '#95A5A6') for model in training_times.index],
                   alpha=0.8, edgecolor='black')
    
    for bar, time in zip(bars, training_times.values):
        ax9.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.2, 
                f'{time:.1f}s', ha='center', va='bottom', fontweight='bold')
    
    ax9.set_ylabel('Training Time (s)')
    ax9.set_title('Training Efficiency')
    ax9.grid(True, alpha=0.3, axis='y')
    plt.setp(ax9.get_xticklabels(), rotation=45, ha='right')
    
    plt.suptitle('Full Comprehensive Benchmark Analysis\nArchitecture × Keypoint Count × Sampling Points', 
                 fontsize=18, fontweight='bold')
    plt.tight_layout()
    
    filename = 'full_comprehensive_benchmark_analysis.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print(f"✅ 完整综合分析图表已保存: {filename}")
    
    return filename

def create_detailed_comparison_table():
    """创建详细对比表格"""
    
    print("\n📊 创建详细对比表格...")
    
    df = pd.read_csv('full_comprehensive_benchmark_results.csv')
    
    # 创建表格
    fig, ax = plt.subplots(figsize=(20, 12))
    ax.axis('tight')
    ax.axis('off')
    
    # 准备表格数据
    headers = ['Model', 'Keypoints', 'Points', 'Avg Error\n(mm)', 'Std Error\n(mm)', 
               'Medical Rate\n(≤10mm)', 'Excellent Rate\n(≤5mm)', 'Parameters\n(M)', 
               'Training Time\n(s)', 'Config']
    
    table_data = []
    for _, row in df.iterrows():
        table_data.append([
            row['model'],
            f"{int(row['keypoints'])}",
            f"{int(row['points']/1000)}K",
            f"{row['avg_error']:.2f}",
            f"{row['std_error']:.2f}",
            f"{row['medical_rate']:.1f}%",
            f"{row['excellent_rate']:.1f}%",
            f"{row['num_params']/1e6:.2f}",
            f"{row['training_time']:.1f}",
            row['keypoint_config']
        ])
    
    # 按误差排序
    sorted_data = sorted(table_data, key=lambda x: float(x[3]))
    
    # 创建表格
    table = ax.table(cellText=sorted_data, colLabels=headers, cellLoc='center', loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(8)
    table.scale(1.2, 2.0)
    
    # 设置表格样式
    # 标题行
    for i in range(len(headers)):
        table[(0, i)].set_facecolor('#2C3E50')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    # 数据行着色
    colors_map = {
        'PointNet': '#FFE5E5', 
        'PointNet++': '#E5F4FD', 
        'DGCNN': '#E8F5E8',
        'Our_Adaptive': '#F3E5F5'
    }
    
    for i in range(1, len(sorted_data) + 1):
        model = sorted_data[i-1][0]
        color = colors_map.get(model, '#F8F9FA')
        
        for j in range(len(headers)):
            table[(i, j)].set_facecolor(color)
            
            # 突出显示最佳性能
            if j == 3:  # 误差列
                error = float(sorted_data[i-1][3])
                if error < 18:
                    table[(i, j)].set_text_props(weight='bold', color='green')
                elif error < 22:
                    table[(i, j)].set_text_props(weight='bold', color='orange')
                else:
                    table[(i, j)].set_text_props(color='red')
    
    plt.title('Detailed Performance Comparison Table\nFull Comprehensive Benchmark Results (Sorted by Performance)', 
              fontsize=16, fontweight='bold', pad=20)
    
    table_filename = 'full_detailed_performance_table.png'
    plt.savefig(table_filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print(f"✅ 详细对比表格已保存: {table_filename}")
    
    return table_filename

def create_performance_insights():
    """创建性能洞察分析"""
    
    print("\n📋 创建性能洞察分析...")
    
    df = pd.read_csv('full_comprehensive_benchmark_results.csv')
    
    print("\n" + "="*80)
    print("📄 FULL COMPREHENSIVE BENCHMARK INSIGHTS")
    print("="*80)
    
    print(f"\n🔬 BENCHMARK SCOPE:")
    print(f"   • Total experiments: {len(df)}")
    print(f"   • Models tested: {', '.join(df['model'].unique())}")
    print(f"   • Sampling points: {', '.join([f'{int(p/1000)}K' for p in sorted(df['points'].unique())])}")
    print(f"   • Keypoint configurations: {sorted(df['keypoints'].unique())}")
    print(f"   • Dataset: 57 real medical keypoints with selective sampling")
    
    print(f"\n🏆 BEST PERFORMING CONFIGURATIONS:")
    best_overall = df.loc[df['avg_error'].idxmin()]
    print(f"   • Overall best: {best_overall['model']} ({best_overall['avg_error']:.2f}mm)")
    print(f"     - Configuration: {int(best_overall['points']/1000)}K points, {int(best_overall['keypoints'])} keypoints")
    print(f"     - Medical grade rate: {best_overall['medical_rate']:.1f}%")
    print(f"     - Keypoint config: {best_overall['keypoint_config']}")
    
    # 每种模型的最佳配置
    print(f"\n   • Best configuration for each model:")
    for model in df['model'].unique():
        model_best = df[df['model'] == model].loc[df[df['model'] == model]['avg_error'].idxmin()]
        print(f"     - {model}: {model_best['avg_error']:.2f}mm ({int(model_best['points']/1000)}K pts, {int(model_best['keypoints'])}kp)")
    
    print(f"\n📊 PERFORMANCE STATISTICS:")
    print(f"   • Error range: {df['avg_error'].min():.2f} - {df['avg_error'].max():.2f}mm")
    print(f"   • Average error: {df['avg_error'].mean():.2f}±{df['avg_error'].std():.2f}mm")
    print(f"   • Medical grade rate: {df['medical_rate'].mean():.1f}% average")
    print(f"   • Excellent grade rate: {df['excellent_rate'].mean():.1f}% average")
    
    print(f"\n🏗️ ARCHITECTURE ANALYSIS:")
    model_stats = df.groupby('model').agg({
        'avg_error': ['mean', 'std', 'min'],
        'medical_rate': 'mean',
        'num_params': 'mean',
        'training_time': 'mean'
    }).round(2)
    
    for model in df['model'].unique():
        stats = model_stats.loc[model]
        print(f"   • {model:15s}: {stats['avg_error']['mean']:.2f}±{stats['avg_error']['std']:.2f}mm "
              f"(best: {stats['avg_error']['min']:.2f}mm, {stats['num_params']['mean']/1e6:.2f}M params)")
    
    print(f"\n🎯 KEY FINDINGS:")
    
    # 关键点数量影响
    keypoint_effect = df.groupby('keypoints')['avg_error'].mean().sort_values()
    print(f"   • Keypoint count impact (best to worst):")
    for kp, error in keypoint_effect.items():
        print(f"     - {kp} keypoints: {error:.2f}mm average")
    
    # 采样点数影响
    points_effect = df.groupby('points')['avg_error'].mean().sort_values()
    print(f"   • Sampling density impact (best to worst):")
    for points, error in points_effect.items():
        print(f"     - {int(points/1000)}K points: {error:.2f}mm average")
    
    # 我们的方法 vs 主流方法
    our_results = df[df['model'] == 'Our_Adaptive']
    baseline_results = df[df['model'] != 'Our_Adaptive']
    
    if len(our_results) > 0 and len(baseline_results) > 0:
        our_avg = our_results['avg_error'].mean()
        our_best = our_results['avg_error'].min()
        baseline_avg = baseline_results['avg_error'].mean()
        baseline_best = baseline_results['avg_error'].min()
        
        print(f"\n📊 METHOD COMPARISON:")
        print(f"   • Our Adaptive Method: {our_avg:.2f}mm average, {our_best:.2f}mm best")
        print(f"   • Mainstream Baselines: {baseline_avg:.2f}mm average, {baseline_best:.2f}mm best")
        
        if our_avg < baseline_avg:
            improvement = ((baseline_avg - our_avg) / baseline_avg * 100)
            print(f"   • Average performance improvement: {improvement:.1f}% better than baselines")
        
        if our_best < baseline_best:
            best_improvement = ((baseline_best - our_best) / baseline_best * 100)
            print(f"   • Best configuration improvement: {best_improvement:.1f}% better than best baseline")
    
    # 配置优化建议
    print(f"\n💡 OPTIMIZATION INSIGHTS:")
    
    # 找出最佳配置模式
    best_configs = df.nsmallest(5, 'avg_error')
    print(f"   • Top 5 configurations analysis:")
    for i, (_, row) in enumerate(best_configs.iterrows(), 1):
        print(f"     {i}. {row['model']} - {int(row['keypoints'])}kp/{int(row['points']/1000)}K: {row['avg_error']:.2f}mm")
    
    # 效率分析
    efficiency_score = df['medical_rate'] / (df['training_time'] / 60)  # 医疗级达标率/训练分钟数
    best_efficiency = df.loc[efficiency_score.idxmax()]
    print(f"   • Most efficient configuration: {best_efficiency['model']} "
          f"({int(best_efficiency['keypoints'])}kp/{int(best_efficiency['points']/1000)}K)")
    
    print(f"\n🎯 DATASET VALIDATION:")
    print(f"   • All architectures successfully trained on the dataset")
    print(f"   • Clear performance differences across configurations")
    print(f"   • Demonstrates dataset's research value and challenge level")
    print(f"   • Provides comprehensive benchmark for future research")
    print(f"   • Shows scalability across different keypoint counts and sampling densities")
    
    print("="*80)

def create_paper_ready_results():
    """创建论文级别的结果"""
    
    print("\n📄 创建论文级别的结果...")
    
    df = pd.read_csv('full_comprehensive_benchmark_results.csv')
    
    # 创建论文表格格式
    paper_results = []
    
    for model in df['model'].unique():
        model_data = df[df['model'] == model]
        
        # 计算平均性能
        avg_error = model_data['avg_error'].mean()
        std_error = model_data['avg_error'].std()
        best_error = model_data['avg_error'].min()
        avg_medical = model_data['medical_rate'].mean()
        avg_params = model_data['num_params'].mean()
        
        # 找到最佳配置
        best_config = model_data.loc[model_data['avg_error'].idxmin()]
        
        paper_results.append({
            'Method': model,
            'Avg Error (mm)': f"{avg_error:.2f}±{std_error:.2f}",
            'Best Error (mm)': f"{best_error:.2f}",
            'Medical Grade (%)': f"{avg_medical:.1f}",
            'Parameters (M)': f"{avg_params/1e6:.2f}",
            'Best Config': f"{int(best_config['keypoints'])}kp/{int(best_config['points']/1000)}K"
        })
    
    # 保存为CSV
    paper_df = pd.DataFrame(paper_results)
    paper_df.to_csv('full_paper_ready_benchmark_results.csv', index=False)
    
    print("💾 论文级别结果已保存: full_paper_ready_benchmark_results.csv")
    
    return paper_df

if __name__ == "__main__":
    print("📊 完整基准测试分析")
    print("不同架构×关键点数×采样点数的全面分析")
    print("=" * 80)
    
    # 创建三维综合分析
    analysis_file = create_comprehensive_3d_analysis()
    
    # 创建详细对比表格
    table_file = create_detailed_comparison_table()
    
    # 创建性能洞察
    create_performance_insights()
    
    # 创建论文级别结果
    paper_df = create_paper_ready_results()
    
    print(f"\n✅ 完成！生成的分析文件:")
    print(f"   📊 三维综合分析: {analysis_file}")
    print(f"   📋 详细对比表格: {table_file}")
    print(f"   📄 论文级别结果: full_paper_ready_benchmark_results.csv")
    print(f"   📊 原始数据: full_comprehensive_benchmark_results.csv")
    
    print(f"\n💡 这个完整基准测试分析提供了:")
    print(f"   • 4个主流点云模型的全面对比")
    print(f"   • 3种采样点数的影响分析")
    print(f"   • 3种关键点配置的性能评估")
    print(f"   • 基于57个真实关键点的选择策略")
    print(f"   • 36个完整实验配置的深度分析")
    print(f"   • 论文发表级别的结果表格和可视化")
    print(f"   • 多维度的性能洞察和优化建议")
