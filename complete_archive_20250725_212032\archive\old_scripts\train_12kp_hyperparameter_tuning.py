#!/usr/bin/env python3
"""
12关键点超参数调优 - 回到基础，专注于超参数优化
基于成功的6.208mm配置，通过超参数调优突破6mm
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import time
import json
import gc
import random

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class ImprovedLoss(nn.Module):
    """改进损失函数 - 简单有效的组合"""
    
    def __init__(self, alpha=0.8, beta=0.2):
        super(ImprovedLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, pred, target):
        mse_loss = F.mse_loss(pred, target)
        smooth_l1_loss = F.smooth_l1_loss(pred, target)
        total_loss = self.alpha * mse_loss + self.beta * smooth_l1_loss
        return total_loss

class ReducedKeypointsF3Dataset(Dataset):
    """12关键点F3数据集"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096, 
                 test_samples: list = None, augment: bool = False, seed: int = 42):
        
        self.num_points = num_points
        self.augment = augment
        self.split = split
        
        np.random.seed(seed)
        
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']
        keypoints = data['keypoints']
        
        if test_samples is None:
            test_samples = ['600114', '600115', '600116', '600117', '600118', 
                           '600119', '600120', '600121', '600122', '600123',
                           '600124', '600125', '600126', '600127', '600128']
        
        test_mask = np.isin(sample_ids, test_samples)
        train_val_mask = ~test_mask
        
        if split == 'test':
            self.sample_ids = sample_ids[test_mask]
            self.point_clouds = point_clouds[test_mask]
            self.keypoints = keypoints[test_mask]
            
        else:
            train_val_ids = sample_ids[train_val_mask]
            train_val_pcs = point_clouds[train_val_mask]
            train_val_kps = keypoints[train_val_mask]
            
            val_size = int(0.2 * len(train_val_ids))
            
            np.random.seed(seed)
            val_indices = np.random.choice(len(train_val_ids), size=val_size, replace=False)
            train_indices = np.setdiff1d(np.arange(len(train_val_ids)), val_indices)
            
            if split == 'train':
                self.sample_ids = train_val_ids[train_indices]
                self.point_clouds = train_val_pcs[train_indices]
                self.keypoints = train_val_kps[train_indices]
                
            elif split == 'val':
                self.sample_ids = train_val_ids[val_indices]
                self.point_clouds = train_val_pcs[val_indices]
                self.keypoints = train_val_kps[val_indices]
    
    def __len__(self):
        return len(self.sample_ids)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        # 数据增强
        if self.augment and self.split == 'train':
            if np.random.random() < 0.7:
                angle = np.random.uniform(-0.08, 0.08)
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
                point_cloud = point_cloud @ rotation.T
                keypoints = keypoints @ rotation.T
            
            if np.random.random() < 0.6:
                translation = np.random.uniform(-0.4, 0.4, 3)
                point_cloud += translation
                keypoints += translation
            
            if np.random.random() < 0.5:
                scale = np.random.uniform(0.99, 1.01, 3)
                point_cloud *= scale
                keypoints *= scale
            
            if np.random.random() < 0.6:
                noise_level = np.random.choice([0.02, 0.03, 0.04])
                noise = np.random.normal(0, noise_level, point_cloud.shape)
                point_cloud += noise
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints),
            'sample_id': self.sample_ids[idx]
        }

class AdaptivePointNet(nn.Module):
    """自适应关键点数量的PointNet"""
    
    def __init__(self, num_keypoints: int):
        super(AdaptivePointNet, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        self.residual1 = nn.Conv1d(64, 256, 1)
        self.residual2 = nn.Conv1d(128, 512, 1)
        
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, num_keypoints * 3)
        
        self.bn_fc1 = nn.BatchNorm1d(512)
        self.bn_fc2 = nn.BatchNorm1d(256)
        self.bn_fc3 = nn.BatchNorm1d(128)
        self.bn_fc4 = nn.BatchNorm1d(64)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        x1 = torch.relu(self.bn1(self.conv1(x)))
        x2 = torch.relu(self.bn2(self.conv2(x1)))
        x3 = torch.relu(self.bn3(self.conv3(x2)))
        
        x3_res = x3 + self.residual1(x1)
        
        x4 = torch.relu(self.bn4(self.conv4(x3_res)))
        x4_res = x4 + self.residual2(x2)
        
        x5 = torch.relu(self.bn5(self.conv5(x4_res)))
        
        global_feat = torch.max(x5, 2)[0]
        
        x = torch.relu(self.bn_fc1(self.fc1(global_feat)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc2(self.fc2(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc3(self.fc3(x)))
        x = self.dropout(x)
        x = torch.relu(self.bn_fc4(self.fc4(x)))
        x = self.dropout(x)
        x = self.fc5(x)
        
        return x.view(batch_size, self.num_keypoints, 3)

def calculate_metrics(pred, target):
    """计算评估指标"""
    distances = torch.norm(pred - target, dim=2)
    avg_distances = torch.mean(distances, dim=1)
    
    mean_dist = torch.mean(avg_distances).item()
    std_dist = torch.std(avg_distances).item() if len(avg_distances) > 1 else 0.0
    
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_3mm = (avg_distances <= 3.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    within_7mm = (avg_distances <= 7.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'std_distance': std_dist,
        'within_1mm_percent': within_1mm,
        'within_3mm_percent': within_3mm,
        'within_5mm_percent': within_5mm,
        'within_7mm_percent': within_7mm
    }

def train_with_hyperparameters(config):
    """使用指定超参数配置训练"""
    
    print(f"🧪 **测试配置**: {config['name']}")
    print(f"   学习率: {config['lr']}")
    print(f"   权重衰减: {config['weight_decay']}")
    print(f"   批次大小: {config['batch_size']}")
    print(f"   Dropout: {config['dropout']}")
    
    set_seed(42)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 数据集
    test_samples = ['600114', '600115', '600116', '600117', '600118', 
                   '600119', '600120', '600121', '600122', '600123',
                   '600124', '600125', '600126', '600127', '600128']
    
    train_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'train', 
                                            num_points=4096, test_samples=test_samples, 
                                            augment=True, seed=42)
    val_dataset = ReducedKeypointsF3Dataset('f3_reduced_12kp_stable.npz', 'val', 
                                          num_points=4096, test_samples=test_samples, 
                                          augment=False, seed=42)
    
    train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=0)
    
    # 模型
    model = AdaptivePointNet(num_keypoints=12).to(device)
    
    # 动态调整Dropout
    for module in model.modules():
        if isinstance(module, nn.Dropout):
            module.p = config['dropout']
    
    # 损失函数
    criterion = ImprovedLoss(alpha=config['loss_alpha'], beta=config['loss_beta'])
    
    # 优化器
    optimizer = optim.AdamW(model.parameters(), lr=config['lr'], weight_decay=config['weight_decay'])
    
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=config['lr_factor'], patience=config['lr_patience'], min_lr=1e-6
    )
    
    num_epochs = 120  # 减少轮数，快速测试
    best_val_error = float('inf')
    patience = 15
    patience_counter = 0
    min_delta = 0.005
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        # 训练
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0}
        
        for batch in train_loader:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            
            try:
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                
                with torch.no_grad():
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    train_metrics['mean_distance'] += metrics['mean_distance']
                        
            except RuntimeError as e:
                continue
        
        train_loss /= len(train_loader)
        train_metrics['mean_distance'] /= len(train_loader)
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_5mm_percent': 0, 'within_7mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                try:
                    pred_keypoints = model(point_cloud)
                    loss = criterion(pred_keypoints, keypoints)
                    val_loss += loss.item()
                    
                    metrics = calculate_metrics(pred_keypoints, keypoints)
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                        
                except RuntimeError as e:
                    continue
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        # 检查改进
        current_error = val_metrics['mean_distance']
        improvement = best_val_error - current_error
        
        if improvement > min_delta:
            best_val_error = current_error
            patience_counter = 0
        else:
            patience_counter += 1
        
        if patience_counter >= patience:
            break
        
        # 每10轮打印一次
        if (epoch + 1) % 10 == 0:
            print(f"   Epoch {epoch+1}: 验证误差={current_error:.3f}mm, 最佳={best_val_error:.3f}mm")
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    
    print(f"   ✅ 完成: 最佳验证误差={best_val_error:.3f}mm, 训练时间={total_time/60:.1f}分钟")
    
    return best_val_error, total_time

def hyperparameter_search():
    """超参数搜索"""
    
    print(f"🚀 **12关键点超参数调优**")
    print(f"🎯 **目标**: 从6.208mm突破到6mm以下")
    print(f"🔧 **策略**: 系统性超参数搜索")
    print("=" * 80)
    
    # 超参数配置
    configs = [
        {
            'name': '基线配置',
            'lr': 0.0008,
            'weight_decay': 1e-4,
            'batch_size': 4,
            'dropout': 0.3,
            'loss_alpha': 0.8,
            'loss_beta': 0.2,
            'lr_factor': 0.7,
            'lr_patience': 12
        },
        {
            'name': '更高学习率',
            'lr': 0.001,
            'weight_decay': 1e-4,
            'batch_size': 4,
            'dropout': 0.3,
            'loss_alpha': 0.8,
            'loss_beta': 0.2,
            'lr_factor': 0.7,
            'lr_patience': 12
        },
        {
            'name': '更低学习率',
            'lr': 0.0005,
            'weight_decay': 1e-4,
            'batch_size': 4,
            'dropout': 0.3,
            'loss_alpha': 0.8,
            'loss_beta': 0.2,
            'lr_factor': 0.7,
            'lr_patience': 12
        },
        {
            'name': '更强正则化',
            'lr': 0.0008,
            'weight_decay': 5e-4,
            'batch_size': 4,
            'dropout': 0.4,
            'loss_alpha': 0.8,
            'loss_beta': 0.2,
            'lr_factor': 0.7,
            'lr_patience': 12
        },
        {
            'name': '更弱正则化',
            'lr': 0.0008,
            'weight_decay': 5e-5,
            'batch_size': 4,
            'dropout': 0.2,
            'loss_alpha': 0.8,
            'loss_beta': 0.2,
            'lr_factor': 0.7,
            'lr_patience': 12
        },
        {
            'name': '更大批次',
            'lr': 0.001,
            'weight_decay': 1e-4,
            'batch_size': 8,
            'dropout': 0.3,
            'loss_alpha': 0.8,
            'loss_beta': 0.2,
            'lr_factor': 0.7,
            'lr_patience': 12
        },
        {
            'name': '优化损失权重',
            'lr': 0.0008,
            'weight_decay': 1e-4,
            'batch_size': 4,
            'dropout': 0.3,
            'loss_alpha': 0.9,
            'loss_beta': 0.1,
            'lr_factor': 0.7,
            'lr_patience': 12
        },
        {
            'name': '更激进学习率调度',
            'lr': 0.0008,
            'weight_decay': 1e-4,
            'batch_size': 4,
            'dropout': 0.3,
            'loss_alpha': 0.8,
            'loss_beta': 0.2,
            'lr_factor': 0.5,
            'lr_patience': 8
        }
    ]
    
    results = []
    
    for i, config in enumerate(configs, 1):
        print(f"\n📊 **配置 {i}/{len(configs)}**")
        print("-" * 40)
        
        try:
            best_error, training_time = train_with_hyperparameters(config)
            
            result = {
                'config': config,
                'best_val_error': float(best_error),
                'training_time_minutes': float(training_time / 60),
                'improvement_vs_baseline': float((6.208 - best_error) / 6.208 * 100)
            }
            results.append(result)
            
        except Exception as e:
            print(f"❌ 配置失败: {e}")
            continue
    
    # 保存结果
    final_results = {
        'method': 'Hyperparameter Tuning for 12 Keypoints',
        'baseline_error': 6.208,
        'results': results
    }
    
    with open('hyperparameter_tuning_results.json', 'w', encoding='utf-8') as f:
        json.dump(final_results, f, indent=2, ensure_ascii=False)
    
    # 分析结果
    print(f"\n🎉 **超参数调优完成!**")
    print("=" * 60)
    
    if results:
        # 按性能排序
        results.sort(key=lambda x: x['best_val_error'])
        
        print(f"📊 **结果排名**:")
        print(f"{'排名':<4} {'配置':<15} {'验证误差':<10} {'改进幅度':<10} {'训练时间':<10}")
        print("-" * 65)
        
        for i, result in enumerate(results, 1):
            config_name = result['config']['name']
            error = result['best_val_error']
            improvement = result['improvement_vs_baseline']
            time_min = result['training_time_minutes']
            
            print(f"{i:<4} {config_name:<15} {error:<10.3f} {improvement:+.1f}%{'':<4} {time_min:<10.1f}")
        
        best_result = results[0]
        print(f"\n🏆 **最佳配置**: {best_result['config']['name']}")
        print(f"🎯 **最佳误差**: {best_result['best_val_error']:.3f}mm")
        print(f"📈 **改进幅度**: {best_result['improvement_vs_baseline']:+.1f}%")
        
        if best_result['best_val_error'] < 6.0:
            print(f"🎉 **突破6mm目标!**")
        elif best_result['best_val_error'] < 6.208:
            print(f"✅ **超越基线!** 超参数调优有效")
        else:
            print(f"💡 **需要进一步优化** 考虑架构改进")
    
    return results

if __name__ == "__main__":
    set_seed(42)
    results = hyperparameter_search()
