#!/usr/bin/env python3
"""
整理训练模型文件
将所有训练生成的模型文件整理到专门的文件夹中
保持工作目录整洁，便于管理
"""

import os
import shutil
import json
import glob
from datetime import datetime

def create_model_directories():
    """创建模型管理目录结构"""
    
    directories = [
        "trained_models",
        "trained_models/current_session",
        "trained_models/improvements", 
        "trained_models/experiments",
        "trained_models/best_performers",
        "trained_models/archived"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ 创建目录: {directory}")

def organize_current_models():
    """整理当前目录中的模型文件"""
    
    print("\n📁 **整理当前训练模型**")
    print("=" * 50)
    
    # 获取当前目录的所有.pth文件
    current_pth_files = [f for f in glob.glob("*.pth") if os.path.isfile(f)]
    
    if not current_pth_files:
        print("❌ 当前目录没有找到.pth模型文件")
        return []
    
    moved_files = []
    
    # 分类移动文件
    for pth_file in current_pth_files:
        filename = os.path.basename(pth_file)
        
        # 根据文件名确定目标目录
        if "baseline_double_softmax" in filename:
            target_dir = "trained_models/best_performers"
            category = "最佳性能"
        elif "improvement" in filename or "optimized" in filename or "ensemble" in filename:
            target_dir = "trained_models/improvements"
            category = "改进实验"
        elif "attention" in filename or "pyramid" in filename or "softmax" in filename:
            target_dir = "trained_models/experiments"
            category = "架构实验"
        else:
            target_dir = "trained_models/current_session"
            category = "当前会话"
        
        # 移动文件
        target_path = os.path.join(target_dir, filename)
        shutil.move(pth_file, target_path)
        
        moved_files.append({
            "filename": filename,
            "category": category,
            "target_path": target_path,
            "size_mb": round(os.path.getsize(target_path) / (1024*1024), 2)
        })
        
        print(f"📦 移动: {filename}")
        print(f"   类别: {category}")
        print(f"   目标: {target_path}")
        print(f"   大小: {moved_files[-1]['size_mb']} MB")
    
    return moved_files

def organize_result_files():
    """整理结果文件"""
    
    print(f"\n📊 **整理结果文件**")
    print("=" * 50)
    
    # 创建结果目录
    results_dir = "trained_models/results"
    os.makedirs(results_dir, exist_ok=True)
    
    # 获取当前目录的JSON结果文件
    current_json_files = [f for f in glob.glob("*.json") if os.path.isfile(f)]
    
    moved_results = []
    
    for json_file in current_json_files:
        filename = os.path.basename(json_file)
        
        # 跳过系统文件
        if filename in ["model_registry.json", "cleanup_summary.json", "experiment_summary.json"]:
            continue
        
        target_path = os.path.join(results_dir, filename)
        shutil.move(json_file, target_path)
        
        moved_results.append({
            "filename": filename,
            "target_path": target_path
        })
        
        print(f"📊 移动结果: {filename} -> {target_path}")
    
    return moved_results

def create_model_inventory():
    """创建模型清单"""
    
    print(f"\n📋 **创建模型清单**")
    print("=" * 50)
    
    inventory = {
        "created": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "summary": {
            "total_models": 0,
            "best_performers": 0,
            "improvements": 0,
            "experiments": 0,
            "current_session": 0
        },
        "categories": {},
        "performance_ranking": [],
        "storage_info": {
            "total_size_mb": 0,
            "directories": {}
        }
    }
    
    # 统计各目录的模型
    model_dirs = {
        "best_performers": "trained_models/best_performers",
        "improvements": "trained_models/improvements", 
        "experiments": "trained_models/experiments",
        "current_session": "trained_models/current_session"
    }
    
    for category, directory in model_dirs.items():
        if os.path.exists(directory):
            pth_files = glob.glob(f"{directory}/*.pth")
            
            models_info = []
            total_size = 0
            
            for pth_file in pth_files:
                filename = os.path.basename(pth_file)
                size_mb = round(os.path.getsize(pth_file) / (1024*1024), 2)
                total_size += size_mb
                
                # 尝试从文件名提取性能信息
                performance = "未知"
                try:
                    if "mm.pth" in filename:
                        parts = filename.split("_")
                        for part in parts:
                            if "mm.pth" in part:
                                performance = part.replace(".pth", "")
                                break
                except:
                    pass
                
                model_info = {
                    "filename": filename,
                    "performance": performance,
                    "size_mb": size_mb,
                    "path": pth_file
                }
                models_info.append(model_info)
                
                # 添加到性能排名 (如果有性能数据)
                if performance != "未知" and "mm" in performance:
                    try:
                        error_value = float(performance.replace("mm", ""))
                        inventory["performance_ranking"].append({
                            "filename": filename,
                            "error_mm": error_value,
                            "category": category
                        })
                    except:
                        pass
            
            inventory["categories"][category] = {
                "count": len(models_info),
                "models": models_info,
                "total_size_mb": round(total_size, 2)
            }
            
            inventory["summary"][category] = len(models_info)
            inventory["summary"]["total_models"] += len(models_info)
            inventory["storage_info"]["total_size_mb"] += total_size
            inventory["storage_info"]["directories"][category] = round(total_size, 2)
    
    # 按性能排序
    inventory["performance_ranking"].sort(key=lambda x: x["error_mm"])
    
    # 保存清单
    with open("trained_models/model_inventory.json", "w", encoding="utf-8") as f:
        json.dump(inventory, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 模型清单已保存: trained_models/model_inventory.json")
    
    # 打印统计信息
    print(f"\n📊 **模型统计**:")
    print(f"   总模型数: {inventory['summary']['total_models']} 个")
    print(f"   最佳性能: {inventory['summary']['best_performers']} 个")
    print(f"   改进实验: {inventory['summary']['improvements']} 个")
    print(f"   架构实验: {inventory['summary']['experiments']} 个")
    print(f"   当前会话: {inventory['summary']['current_session']} 个")
    print(f"   总存储: {inventory['storage_info']['total_size_mb']:.1f} MB")
    
    # 显示性能排名前5
    if inventory["performance_ranking"]:
        print(f"\n🏆 **性能排名 (前5)**:")
        for i, model in enumerate(inventory["performance_ranking"][:5], 1):
            print(f"   {i}. {model['filename']} - {model['error_mm']}mm ({model['category']})")
    
    return inventory

def create_readme():
    """创建模型管理README"""
    
    readme_content = """# 训练模型管理

## 目录结构
```
trained_models/
├── best_performers/     # 最佳性能模型
├── improvements/        # 改进实验模型  
├── experiments/         # 架构实验模型
├── current_session/     # 当前会话模型
├── archived/           # 存档模型
├── results/            # 训练结果JSON文件
├── model_inventory.json # 模型清单
└── README.md           # 本文件
```

## 最佳性能模型

### 🥇 当前最佳: 集成双Softmax
- **文件**: `best_集成双softmax_improvement_5.829mm.pth`
- **性能**: 5.829mm
- **描述**: 3个双Softmax模块集成，突破5.959mm基线

### 🥈 基线+双Softmax  
- **文件**: `best_baseline_double_softmax_5.959mm.pth`
- **性能**: 5.959mm
- **描述**: 基线架构+双Softmax精细化，突破6mm目标

## 使用方法

### 加载最佳模型
```python
import torch
from baseline_with_double_softmax import BaselineAdaptivePointNet

# 加载集成双Softmax模型
model = BaselineAdaptivePointNet(num_keypoints=12, softmax_type="ensemble")
checkpoint = torch.load("trained_models/best_performers/best_集成双softmax_improvement_5.829mm.pth")
model.load_state_dict(checkpoint['model_state_dict'])
```

### 查看模型清单
```python
import json
with open("trained_models/model_inventory.json", "r") as f:
    inventory = json.load(f)
print(f"总模型数: {inventory['summary']['total_models']}")
```

## 性能进展

1. **12关键点基线**: 6.208mm
2. **基线+双Softmax**: 5.959mm (+4.0%)
3. **集成双Softmax**: 5.829mm (+2.2%)

## 下一步目标

- **目标**: 5.5mm (医疗级精度)
- **策略**: 文献方法借鉴 + 进一步集成优化
"""
    
    with open("trained_models/README.md", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print(f"✅ README已创建: trained_models/README.md")

def main():
    """主函数"""
    
    print("🗂️ **整理训练模型文件**")
    print("🎯 **目标**: 清理工作目录，规范模型管理")
    print("=" * 80)
    
    # 创建目录结构
    create_model_directories()
    
    # 整理模型文件
    moved_models = organize_current_models()
    
    # 整理结果文件
    moved_results = organize_result_files()
    
    # 创建模型清单
    inventory = create_model_inventory()
    
    # 创建README
    create_readme()
    
    # 创建整理总结
    organization_summary = {
        "organization_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "action": "整理训练模型文件到专门目录",
        "moved_models": len(moved_models),
        "moved_results": len(moved_results),
        "total_models": inventory["summary"]["total_models"],
        "total_size_mb": inventory["storage_info"]["total_size_mb"],
        "best_performance": "5.829mm (集成双Softmax)",
        "directory_structure": {
            "best_performers": inventory["summary"]["best_performers"],
            "improvements": inventory["summary"]["improvements"],
            "experiments": inventory["summary"]["experiments"],
            "current_session": inventory["summary"]["current_session"]
        }
    }
    
    with open("trained_models/organization_summary.json", "w", encoding="utf-8") as f:
        json.dump(organization_summary, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 **模型整理完成!**")
    print("=" * 50)
    print(f"📦 移动模型: {len(moved_models)} 个")
    print(f"📊 移动结果: {len(moved_results)} 个") 
    print(f"💾 总存储: {inventory['storage_info']['total_size_mb']:.1f} MB")
    print(f"🏆 当前最佳: 5.829mm (集成双Softmax)")
    print(f"📁 工作目录: 已清理整洁")
    
    print(f"\n💡 **下一步建议**:")
    print(f"   1. 查看 trained_models/README.md 了解模型管理")
    print(f"   2. 查看 trained_models/model_inventory.json 了解详细清单")
    print(f"   3. 开始文献调研，寻找更好的方法")
    print(f"   4. 目标: 从5.829mm突破到5.5mm医疗级精度")

if __name__ == "__main__":
    main()
