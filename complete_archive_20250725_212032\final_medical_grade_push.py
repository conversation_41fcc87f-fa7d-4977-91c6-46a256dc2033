#!/usr/bin/env python3
"""
冲击医疗级5mm精度 - 最终方案
Final Push for Medical-Grade 5mm Precision
基于所有实验洞察的精准策略
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pathlib import Path
from datetime import datetime
import json

class OptimalEnsemblePointNet(nn.Module):
    """最优集成PointNet - 精简而有效"""
    
    def __init__(self, num_keypoints=19, num_models=3):
        super().__init__()
        self.num_keypoints = num_keypoints
        self.num_models = num_models
        
        # 3个精心设计的子网络 - 保持简单但有差异
        self.models = nn.ModuleList()
        
        # 模型1: 标准深度
        model1 = nn.Sequential(
            nn.Conv1d(3, 64, 1), nn.BatchNorm1d(64), nn.ReLU(), nn.Dropout(0.1),
            nn.Conv1d(64, 128, 1), nn.<PERSON>chNorm1d(128), nn.<PERSON><PERSON><PERSON>(), nn.Dropout(0.1),
            nn.Conv1d(128, 256, 1), nn.BatchNorm1d(256), nn.ReLU(), nn.Dropout(0.1),
            nn.Conv1d(256, 512, 1), nn.BatchNorm1d(512), nn.ReLU(), nn.Dropout(0.1),
            nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 模型2: 更宽网络
        model2 = nn.Sequential(
            nn.Conv1d(3, 96, 1), nn.BatchNorm1d(96), nn.ReLU(), nn.Dropout(0.12),
            nn.Conv1d(96, 192, 1), nn.BatchNorm1d(192), nn.ReLU(), nn.Dropout(0.12),
            nn.Conv1d(192, 384, 1), nn.BatchNorm1d(384), nn.ReLU(), nn.Dropout(0.12),
            nn.Conv1d(384, 768, 1), nn.BatchNorm1d(768), nn.ReLU(), nn.Dropout(0.12),
            nn.Conv1d(768, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        # 模型3: 更深网络
        model3 = nn.Sequential(
            nn.Conv1d(3, 48, 1), nn.BatchNorm1d(48), nn.ReLU(), nn.Dropout(0.08),
            nn.Conv1d(48, 96, 1), nn.BatchNorm1d(96), nn.ReLU(), nn.Dropout(0.08),
            nn.Conv1d(96, 192, 1), nn.BatchNorm1d(192), nn.ReLU(), nn.Dropout(0.08),
            nn.Conv1d(192, 384, 1), nn.BatchNorm1d(384), nn.ReLU(), nn.Dropout(0.08),
            nn.Conv1d(384, 512, 1), nn.BatchNorm1d(512), nn.ReLU(), nn.Dropout(0.08),
            nn.Conv1d(512, 1024, 1), nn.BatchNorm1d(1024), nn.ReLU()
        )
        
        self.models.extend([model1, model2, model3])
        
        # 每个模型的回归器 - 更深更精确
        self.regressors = nn.ModuleList([
            nn.Sequential(
                nn.Linear(1024, 768),
                nn.ReLU(),
                nn.Dropout(0.15),
                nn.Linear(768, 512),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(512, 256),
                nn.ReLU(),
                nn.Dropout(0.05),
                nn.Linear(256, num_keypoints * 3)
            ) for _ in range(num_models)
        ])
        
        # 可学习的集成权重
        self.ensemble_weights = nn.Parameter(torch.ones(num_models) / num_models)
        
    def forward(self, point_cloud):
        B, N, _ = point_cloud.shape
        x = point_cloud.transpose(1, 2)  # (B, 3, N)
        
        predictions = []
        
        for i, (model, regressor) in enumerate(zip(self.models, self.regressors)):
            # 特征提取
            features = model(x)  # (B, 1024, N)
            global_feat = torch.max(features, dim=2)[0]  # (B, 1024)
            
            # 关键点预测
            pred = regressor(global_feat)  # (B, num_keypoints * 3)
            pred = pred.view(B, self.num_keypoints, 3)
            predictions.append(pred)
        
        # 加权集成
        weights = F.softmax(self.ensemble_weights, dim=0)
        ensemble_pred = sum(w * pred for w, pred in zip(weights, predictions))
        
        return ensemble_pred

class MedicalGradeLoss(nn.Module):
    """医疗级损失函数 - 精确而稳定"""
    
    def __init__(self, mse_weight=0.7, smooth_l1_weight=0.3):
        super().__init__()
        self.mse_loss = nn.MSELoss()
        self.smooth_l1_loss = nn.SmoothL1Loss(beta=0.5)  # 更小的beta，更敏感
        
        self.mse_weight = mse_weight
        self.smooth_l1_weight = smooth_l1_weight
        
    def forward(self, pred, target):
        mse = self.mse_loss(pred, target)
        smooth_l1 = self.smooth_l1_loss(pred, target)
        
        total_loss = self.mse_weight * mse + self.smooth_l1_weight * smooth_l1
        
        return total_loss, {
            'mse': mse.item(),
            'smooth_l1': smooth_l1.item(),
            'total': total_loss.item()
        }

class MedicalGradeTTA:
    """医疗级测试时增强"""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def apply_medical_tta(self, model, point_cloud, num_augmentations=30):
        """应用医疗级TTA"""
        model.eval()
        predictions = []
        
        with torch.no_grad():
            # 原始预测
            pred = model(point_cloud.unsqueeze(0))
            predictions.append(pred[0])
            
            # 多种精细增强
            for i in range(num_augmentations - 1):
                aug_pc = point_cloud.clone()
                
                # 1. 极小旋转 (±0.3度)
                if np.random.random() < 0.9:
                    angle = np.random.uniform(-0.005, 0.005)  # ±0.3度
                    axis = np.random.choice(['x', 'y', 'z'])
                    
                    cos_a, sin_a = np.cos(angle), np.sin(angle)
                    if axis == 'x':
                        rotation = torch.tensor([[1, 0, 0], [0, cos_a, -sin_a], [0, sin_a, cos_a]], 
                                              dtype=torch.float32, device=self.device)
                    elif axis == 'y':
                        rotation = torch.tensor([[cos_a, 0, sin_a], [0, 1, 0], [-sin_a, 0, cos_a]], 
                                              dtype=torch.float32, device=self.device)
                    else:  # z轴
                        rotation = torch.tensor([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], 
                                              dtype=torch.float32, device=self.device)
                    
                    aug_pc = aug_pc @ rotation.T
                
                # 2. 极小噪声 (0.01-0.03mm)
                if np.random.random() < 0.7:
                    noise_std = np.random.uniform(0.01, 0.03)
                    noise = torch.normal(0, noise_std, aug_pc.shape, device=self.device)
                    aug_pc = aug_pc + noise
                
                # 3. 极小缩放 (±0.1%)
                if np.random.random() < 0.5:
                    scale = np.random.uniform(0.999, 1.001)
                    aug_pc = aug_pc * scale
                
                # 预测
                aug_pred = model(aug_pc.unsqueeze(0))
                predictions.append(aug_pred[0])
            
            # 加权平均 (给原始预测更高权重)
            weights = torch.ones(len(predictions), device=self.device)
            weights[0] = 2.0  # 原始预测权重加倍
            weights = weights / weights.sum()
            
            final_pred = torch.zeros_like(predictions[0])
            for i, pred in enumerate(predictions):
                final_pred += weights[i] * pred
            
        return final_pred

class FinalMedicalTrainer:
    """最终医疗级训练器"""
    
    def __init__(self, device='cuda'):
        self.device = device
        self.tta = MedicalGradeTTA(device)
        
    def load_aligned_data(self):
        """加载对齐数据"""
        print("📦 加载F3对齐数据...")
        
        aligned_files = list(Path("data/processed").glob("f3_aligned_dataset_*.npz"))
        if not aligned_files:
            raise FileNotFoundError("未找到F3对齐数据集")
        
        latest_file = max(aligned_files, key=lambda x: x.stat().st_mtime)
        data = np.load(str(latest_file), allow_pickle=True)
        
        point_clouds = np.array(data['point_clouds'], dtype=np.float32)
        keypoints = np.array(data['keypoints'], dtype=np.float32)
        
        # 数据划分
        from sklearn.model_selection import train_test_split
        indices = np.arange(len(point_clouds))
        train_val_indices, test_indices = train_test_split(indices, test_size=0.15, random_state=42)
        train_indices, val_indices = train_test_split(train_val_indices, test_size=0.18, random_state=42)
        
        self.data = {
            'train': {
                'point_clouds': point_clouds[train_indices],
                'keypoints': keypoints[train_indices]
            },
            'val': {
                'point_clouds': point_clouds[val_indices],
                'keypoints': keypoints[val_indices]
            },
            'test': {
                'point_clouds': point_clouds[test_indices],
                'keypoints': keypoints[test_indices]
            }
        }
        
        print(f"✅ 数据加载完成: {point_clouds.shape}")
        print(f"   训练: {len(train_indices)}, 验证: {len(val_indices)}, 测试: {len(test_indices)}")
        
        return self.data
    
    def medical_augmentation(self, point_clouds, keypoints):
        """医疗级数据增强 - 精确而保守"""
        aug_pcs = []
        aug_kps = []
        
        for pc, kp in zip(point_clouds, keypoints):
            # 原始数据
            aug_pcs.append(pc)
            aug_kps.append(kp)
            
            # 精细旋转增强 (±0.2度)
            for _ in range(4):
                angle = np.random.uniform(-0.0035, 0.0035)  # ±0.2度
                axis = np.random.choice(['x', 'y', 'z'])
                
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                if axis == 'x':
                    rotation = np.array([[1, 0, 0], [0, cos_a, -sin_a], [0, sin_a, cos_a]], dtype=np.float32)
                elif axis == 'y':
                    rotation = np.array([[cos_a, 0, sin_a], [0, 1, 0], [-sin_a, 0, cos_a]], dtype=np.float32)
                else:  # z轴
                    rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]], dtype=np.float32)
                
                aug_pc = pc @ rotation.T
                aug_kp = kp @ rotation.T
                aug_pcs.append(aug_pc)
                aug_kps.append(aug_kp)
            
            # 精细噪声增强 (0.01-0.04mm)
            for _ in range(3):
                noise_std = np.random.uniform(0.01, 0.04)
                noise_pc = pc + np.random.normal(0, noise_std, pc.shape).astype(np.float32)
                aug_pcs.append(noise_pc)
                aug_kps.append(kp)
            
            # 精细缩放增强 (±0.2%)
            for _ in range(2):
                scale = np.random.uniform(0.998, 1.002)
                scaled_pc = pc * scale
                scaled_kp = kp * scale
                aug_pcs.append(scaled_pc)
                aug_kps.append(scaled_kp)
        
        return aug_pcs, aug_kps
    
    def train_final_model(self, epochs=250, lr=0.0002):
        """训练最终模型"""
        print(f"\n🎯 冲击医疗级5mm精度 - 最终训练")
        print(f"   策略: 最优集成 + 医疗级损失 + 精细TTA + 超长训练")
        print(f"   参数: epochs={epochs}, lr={lr}")
        
        # 创建模型
        model = OptimalEnsemblePointNet(num_keypoints=19, num_models=3).to(self.device)
        
        # 计算参数
        total_params = sum(p.numel() for p in model.parameters())
        print(f"   模型参数: {total_params:,}")
        
        # 优化器 - 保守设置
        optimizer = torch.optim.Adam(
            model.parameters(), 
            lr=lr, 
            weight_decay=2e-5,
            betas=(0.9, 0.999)
        )
        
        # 学习率调度器 - 平滑下降
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.8, patience=25, 
            verbose=True, min_lr=1e-7
        )
        
        # 医疗级损失函数
        criterion = MedicalGradeLoss(mse_weight=0.7, smooth_l1_weight=0.3)
        
        # 训练状态
        best_val_error = float('inf')
        best_model_state = None
        patience = 0
        max_patience = 80  # 更长的耐心
        
        train_history = []
        val_history = []
        
        for epoch in range(epochs):
            # 训练阶段
            model.train()
            epoch_losses = []
            
            # 渐进式k_shot
            if epoch < 50:
                k_shot = 35
            elif epoch < 100:
                k_shot = 40
            elif epoch < 150:
                k_shot = 45
            else:
                k_shot = 50  # 使用更多数据
            
            train_indices = np.random.choice(
                len(self.data['train']['point_clouds']), 
                min(k_shot, len(self.data['train']['point_clouds'])), 
                replace=False
            )
            
            train_pcs = self.data['train']['point_clouds'][train_indices]
            train_kps = self.data['train']['keypoints'][train_indices]
            
            # 医疗级增强
            aug_pcs, aug_kps = self.medical_augmentation(train_pcs, train_kps)
            
            # 渐进式批次大小
            if epoch < 50:
                batch_size = 6
            elif epoch < 150:
                batch_size = 8
            else:
                batch_size = 10
            
            for i in range(0, len(aug_pcs), batch_size):
                batch_pcs = torch.FloatTensor(aug_pcs[i:i+batch_size]).to(self.device)
                batch_kps = torch.FloatTensor(aug_kps[i:i+batch_size]).to(self.device)
                
                optimizer.zero_grad()
                pred_kps = model(batch_pcs)
                
                # 医疗级损失
                loss, loss_dict = criterion(pred_kps, batch_kps)
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
                
                optimizer.step()
                epoch_losses.append(loss.item())
                
                del batch_pcs, batch_kps, pred_kps, loss
                torch.cuda.empty_cache()
            
            avg_loss = np.mean(epoch_losses) if epoch_losses else 0
            train_history.append(avg_loss)
            
            # 验证
            if epoch % 2 == 0:  # 更频繁验证
                val_error = self.evaluate_model(model, 'val')
                val_history.append(val_error)
                scheduler.step(val_error)
                
                if val_error < best_val_error:
                    best_val_error = val_error
                    best_model_state = model.state_dict().copy()
                    patience = 0
                    
                    # 如果接近目标，保存检查点
                    if val_error <= 6.5:
                        self.save_checkpoint(model, val_error, epoch, "approaching_5mm")
                        print(f"🎯 接近目标！验证误差: {val_error:.3f}mm")
                    
                    if val_error <= 5.5:
                        self.save_checkpoint(model, val_error, epoch, "very_close_5mm")
                        print(f"🔥 非常接近！验证误差: {val_error:.3f}mm")
                        
                    if val_error <= 5.0:
                        self.save_checkpoint(model, val_error, epoch, "medical_grade_achieved")
                        print(f"🎉 医疗级精度达成！验证误差: {val_error:.3f}mm")
                else:
                    patience += 1
                
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, Val={val_error:.3f}mm, "
                      f"LR={optimizer.param_groups[0]['lr']:.7f}, K={k_shot}, BS={batch_size}, P={patience}")
                
                if patience >= max_patience:
                    print(f"早停在epoch {epoch}")
                    break
            else:
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f}, K={k_shot}, BS={batch_size}")
        
        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)
            print(f"✅ 加载最佳模型 (验证误差: {best_val_error:.3f}mm)")
        
        self.model = model
        self.training_history = {
            'train_losses': train_history,
            'val_errors': val_history,
            'best_val_error': best_val_error
        }
        
        return model, best_val_error
    
    def evaluate_model(self, model, split='test'):
        """评估模型"""
        model.eval()
        total_error = 0
        num_samples = 0
        
        with torch.no_grad():
            pcs = self.data[split]['point_clouds']
            kps = self.data[split]['keypoints']
            
            for i in range(0, len(pcs), 2):
                batch_pcs = torch.FloatTensor(pcs[i:i+2]).to(self.device)
                batch_kps = torch.FloatTensor(kps[i:i+2]).to(self.device)
                
                pred_kps = model(batch_pcs)
                
                for j in range(len(batch_pcs)):
                    error = torch.mean(torch.norm(pred_kps[j] - batch_kps[j], dim=1))
                    total_error += error.item()
                    num_samples += 1
                
                del batch_pcs, batch_kps, pred_kps
                torch.cuda.empty_cache()
        
        return total_error / num_samples if num_samples > 0 else float('inf')
    
    def evaluate_with_medical_tta(self, model, split='test'):
        """使用医疗级TTA评估"""
        print(f"\n🔬 医疗级TTA评估 ({split}集)...")
        
        model.eval()
        tta_errors = []
        
        pcs = self.data[split]['point_clouds']
        kps = self.data[split]['keypoints']
        
        for i, (pc, kp) in enumerate(zip(pcs, kps)):
            pc_tensor = torch.FloatTensor(pc).to(self.device)
            kp_tensor = torch.FloatTensor(kp).to(self.device)
            
            # 应用医疗级TTA
            tta_pred = self.tta.apply_medical_tta(model, pc_tensor, num_augmentations=30)
            
            error = torch.mean(torch.norm(tta_pred - kp_tensor, dim=1))
            tta_errors.append(error.item())
            
            if i < 3:
                print(f"   样本 {i+1}: 医疗级TTA误差 = {error:.3f}mm")
            
            del pc_tensor, kp_tensor, tta_pred
            torch.cuda.empty_cache()
        
        avg_error = np.mean(tta_errors)
        print(f"   医疗级TTA平均误差: {avg_error:.3f}mm")
        
        return avg_error, tta_errors
    
    def save_checkpoint(self, model, val_error, epoch, tag=""):
        """保存检查点"""
        output_dir = Path("trained_models/final_medical_grade")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"final_medical_{val_error:.3f}mm_epoch{epoch}_{tag}_{timestamp}.pth"
        model_path = output_dir / filename
        
        torch.save({
            'model_state_dict': model.state_dict(),
            'validation_error': val_error,
            'epoch': epoch,
            'timestamp': timestamp,
            'target': '5mm_medical_grade'
        }, model_path)
        
        print(f"💾 检查点已保存: {model_path}")
        return model_path

def run_final_medical_push():
    """运行最终医疗级冲击"""
    print("🎯 冲击医疗级5mm精度 - 最终决战")
    print("=" * 60)
    print("策略总结:")
    print("• 最优集成架构 (3个精心设计的子网络)")
    print("• 医疗级损失函数 (MSE + SmoothL1)")
    print("• 精细数据增强 (±0.2度, 0.01-0.04mm噪声)")
    print("• 医疗级TTA (30次精细增强)")
    print("• 超长训练 (250 epochs)")
    print("• 渐进式训练策略")
    
    trainer = FinalMedicalTrainer()
    data = trainer.load_aligned_data()
    
    # 最终训练
    model, val_error = trainer.train_final_model(epochs=250, lr=0.0002)
    
    # 标准测试
    test_error = trainer.evaluate_model(model, 'test')
    
    # 医疗级TTA测试
    medical_tta_error, tta_errors = trainer.evaluate_with_medical_tta(model, 'test')
    
    # 最终结果
    print(f"\n🏆 最终医疗级冲击结果:")
    print("=" * 50)
    print(f"验证误差:                 {val_error:.3f}mm")
    print(f"测试误差 (标准):          {test_error:.3f}mm")
    print(f"测试误差 (医疗级TTA):     {medical_tta_error:.3f}mm")
    
    # 医疗级评估
    best_error = min(test_error, medical_tta_error)
    medical_target = 5.0
    
    print(f"\n🎯 医疗级精度评估:")
    print(f"医疗级目标:               {medical_target:.1f}mm")
    print(f"最佳结果:                 {best_error:.3f}mm")
    
    if best_error <= medical_target:
        print("🎉🎉🎉 成功达到医疗级精度！🎉🎉🎉")
        status = "医疗级精度达成"
    elif best_error <= 5.5:
        remaining = best_error - medical_target
        print(f"🔥 极其接近医疗级！仅差{remaining:.3f}mm")
        status = f"极接近，仅差{remaining:.3f}mm"
    elif best_error <= 6.0:
        remaining = best_error - medical_target
        print(f"🎯 非常接近医疗级！还需{remaining:.3f}mm")
        status = f"很接近，还需{remaining:.3f}mm"
    else:
        remaining = best_error - medical_target
        print(f"📈 距离医疗级还需{remaining:.3f}mm")
        status = f"需要{remaining:.3f}mm改进"
    
    # 保存最终模型
    final_model_path = trainer.save_checkpoint(model, best_error, 250, "final_result")
    
    return trainer, {
        'val_error': val_error,
        'test_error': test_error,
        'medical_tta_error': medical_tta_error,
        'best_error': best_error,
        'medical_achieved': best_error <= medical_target,
        'status': status
    }

if __name__ == "__main__":
    trainer, results = run_final_medical_push()
