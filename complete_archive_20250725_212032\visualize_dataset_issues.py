#!/usr/bin/env python3
"""
可视化数据集问题
Visualize Dataset Issues
基于分析结果，直观展示数据集存在的问题
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns
import pandas as pd
from pathlib import Path
from datetime import datetime

class DatasetIssueVisualizer:
    """数据集问题可视化器"""
    
    def __init__(self, data_path='data/raw/high_quality_f3_dataset.npz'):
        self.data_path = data_path
        self.load_data()
        
    def load_data(self):
        """加载数据"""
        print(f"📦 加载数据集: {self.data_path}")
        
        data = np.load(self.data_path, allow_pickle=True)
        self.sample_ids = data['sample_ids']
        self.point_clouds = data['point_clouds']
        self.keypoints = data['keypoints']
        
        print(f"✅ 数据加载完成: {len(self.sample_ids)} 样本")
        
    def visualize_coordinate_range_issues(self):
        """可视化坐标范围问题"""
        print("\n📊 可视化坐标范围问题...")
        
        # 收集所有坐标
        all_pc_coords = []
        all_kp_coords = []
        
        for pc, kp in zip(self.point_clouds, self.keypoints):
            pc_array = np.array(pc, dtype=np.float32)
            kp_array = np.array(kp, dtype=np.float32)
            
            all_pc_coords.extend(pc_array.tolist())
            all_kp_coords.extend(kp_array.tolist())
        
        all_pc_coords = np.array(all_pc_coords)
        all_kp_coords = np.array(all_kp_coords)
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 点云坐标分布
        axes[0, 0].hist(all_pc_coords[:, 0], bins=50, alpha=0.7, color='blue', label='Point Cloud X')
        axes[0, 0].hist(all_kp_coords[:, 0], bins=50, alpha=0.7, color='red', label='Keypoints X')
        axes[0, 0].set_title('X坐标分布对比')
        axes[0, 0].set_xlabel('X (mm)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        axes[0, 1].hist(all_pc_coords[:, 1], bins=50, alpha=0.7, color='blue', label='Point Cloud Y')
        axes[0, 1].hist(all_kp_coords[:, 1], bins=50, alpha=0.7, color='red', label='Keypoints Y')
        axes[0, 1].set_title('Y坐标分布对比')
        axes[0, 1].set_xlabel('Y (mm)')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        axes[0, 2].hist(all_pc_coords[:, 2], bins=50, alpha=0.7, color='blue', label='Point Cloud Z')
        axes[0, 2].hist(all_kp_coords[:, 2], bins=50, alpha=0.7, color='red', label='Keypoints Z')
        axes[0, 2].set_title('Z坐标分布对比')
        axes[0, 2].set_xlabel('Z (mm)')
        axes[0, 2].legend()
        axes[0, 2].grid(True, alpha=0.3)
        
        # 范围对比
        pc_ranges = [
            all_pc_coords[:, 0].max() - all_pc_coords[:, 0].min(),
            all_pc_coords[:, 1].max() - all_pc_coords[:, 1].min(),
            all_pc_coords[:, 2].max() - all_pc_coords[:, 2].min()
        ]
        
        kp_ranges = [
            all_kp_coords[:, 0].max() - all_kp_coords[:, 0].min(),
            all_kp_coords[:, 1].max() - all_kp_coords[:, 1].min(),
            all_kp_coords[:, 2].max() - all_kp_coords[:, 2].min()
        ]
        
        x_labels = ['X轴', 'Y轴', 'Z轴']
        x_pos = np.arange(len(x_labels))
        
        axes[1, 0].bar(x_pos - 0.2, pc_ranges, 0.4, label='点云范围', color='blue', alpha=0.7)
        axes[1, 0].bar(x_pos + 0.2, kp_ranges, 0.4, label='关键点范围', color='red', alpha=0.7)
        axes[1, 0].set_title('坐标范围对比')
        axes[1, 0].set_xlabel('坐标轴')
        axes[1, 0].set_ylabel('范围 (mm)')
        axes[1, 0].set_xticks(x_pos)
        axes[1, 0].set_xticklabels(x_labels)
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 范围比例
        range_ratios = [pc_ranges[i] / kp_ranges[i] for i in range(3)]
        
        axes[1, 1].bar(x_labels, range_ratios, color=['orange', 'green', 'purple'], alpha=0.7)
        axes[1, 1].set_title('点云/关键点范围比例')
        axes[1, 1].set_ylabel('比例')
        axes[1, 1].axhline(y=1.0, color='red', linestyle='--', label='理想比例=1')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        # 问题总结
        axes[1, 2].text(0.1, 0.9, '坐标系问题总结:', fontsize=14, fontweight='bold', transform=axes[1, 2].transAxes)
        
        problem_text = f"""
X轴范围比例: {range_ratios[0]:.1f}x
Y轴范围比例: {range_ratios[1]:.1f}x  
Z轴范围比例: {range_ratios[2]:.1f}x

问题分析:
• X轴: 点云范围是关键点的{range_ratios[0]:.1f}倍
• Z轴: 点云范围是关键点的{range_ratios[2]:.1f}倍
• 说明点云包含大量关键点区域外的数据

建议:
• 裁剪点云到关键点附近区域
• 统一坐标系原点
• 标准化数据尺度
        """
        
        axes[1, 2].text(0.1, 0.8, problem_text, fontsize=10, transform=axes[1, 2].transAxes, 
                        verticalalignment='top', fontfamily='monospace')
        axes[1, 2].set_xlim(0, 1)
        axes[1, 2].set_ylim(0, 1)
        axes[1, 2].axis('off')
        
        plt.tight_layout()
        
        # 保存图片
        viz_dir = Path("results/dataset_issue_visualization")
        viz_dir.mkdir(parents=True, exist_ok=True)
        plt.savefig(viz_dir / "coordinate_range_issues.png", dpi=150, bbox_inches='tight')
        print(f"💾 保存坐标范围问题可视化: coordinate_range_issues.png")
        plt.show()
    
    def visualize_scale_outliers(self):
        """可视化尺度异常样本"""
        print("\n📊 可视化尺度异常样本...")
        
        # 计算每个样本的尺度
        pc_scales = []
        kp_scales = []
        sample_indices = []
        
        for i, (pc, kp) in enumerate(zip(self.point_clouds, self.keypoints)):
            pc_array = np.array(pc, dtype=np.float32)
            kp_array = np.array(kp, dtype=np.float32)
            
            pc_scale = np.linalg.norm(np.max(pc_array, axis=0) - np.min(pc_array, axis=0))
            kp_scale = np.linalg.norm(np.max(kp_array, axis=0) - np.min(kp_array, axis=0))
            
            pc_scales.append(pc_scale)
            kp_scales.append(kp_scale)
            sample_indices.append(i)
        
        pc_scales = np.array(pc_scales)
        kp_scales = np.array(kp_scales)
        
        # 识别异常值
        pc_q5, pc_q95 = np.percentile(pc_scales, [5, 95])
        kp_q5, kp_q95 = np.percentile(kp_scales, [5, 95])
        
        pc_outliers = (pc_scales < pc_q5) | (pc_scales > pc_q95)
        kp_outliers = (kp_scales < kp_q5) | (kp_scales > kp_q95)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 点云尺度分布
        axes[0, 0].hist(pc_scales, bins=20, alpha=0.7, color='blue', edgecolor='black')
        axes[0, 0].axvline(pc_q5, color='red', linestyle='--', label=f'5%分位数: {pc_q5:.1f}')
        axes[0, 0].axvline(pc_q95, color='red', linestyle='--', label=f'95%分位数: {pc_q95:.1f}')
        axes[0, 0].set_title('点云尺度分布')
        axes[0, 0].set_xlabel('尺度 (mm)')
        axes[0, 0].set_ylabel('样本数')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 关键点尺度分布
        axes[0, 1].hist(kp_scales, bins=20, alpha=0.7, color='red', edgecolor='black')
        axes[0, 1].axvline(kp_q5, color='blue', linestyle='--', label=f'5%分位数: {kp_q5:.1f}')
        axes[0, 1].axvline(kp_q95, color='blue', linestyle='--', label=f'95%分位数: {kp_q95:.1f}')
        axes[0, 1].set_title('关键点尺度分布')
        axes[0, 1].set_xlabel('尺度 (mm)')
        axes[0, 1].set_ylabel('样本数')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 尺度散点图
        colors = ['red' if pc_outliers[i] or kp_outliers[i] else 'blue' for i in range(len(pc_scales))]
        axes[1, 0].scatter(pc_scales, kp_scales, c=colors, alpha=0.7)
        axes[1, 0].set_xlabel('点云尺度 (mm)')
        axes[1, 0].set_ylabel('关键点尺度 (mm)')
        axes[1, 0].set_title('点云vs关键点尺度关系')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 添加理想关系线
        min_scale = min(np.min(pc_scales), np.min(kp_scales))
        max_scale = max(np.max(pc_scales), np.max(kp_scales))
        axes[1, 0].plot([min_scale, max_scale], [min_scale, max_scale], 'k--', alpha=0.5, label='理想关系')
        axes[1, 0].legend()
        
        # 异常样本信息
        outlier_info = f"""
尺度异常样本分析:

点云尺度异常: {np.sum(pc_outliers)} 样本
• 过小 (<{pc_q5:.1f}): {np.sum(pc_scales < pc_q5)} 样本
• 过大 (>{pc_q95:.1f}): {np.sum(pc_scales > pc_q95)} 样本

关键点尺度异常: {np.sum(kp_outliers)} 样本  
• 过小 (<{kp_q5:.1f}): {np.sum(kp_scales < kp_q5)} 样本
• 过大 (>{kp_q95:.1f}): {np.sum(kp_scales > kp_q95)} 样本

总异常样本: {np.sum(pc_outliers | kp_outliers)} / {len(pc_scales)}

建议处理方案:
1. 检查异常样本的数据来源
2. 考虑移除极端异常样本
3. 对剩余样本进行尺度标准化
4. 使用鲁棒的标准化方法
        """
        
        axes[1, 1].text(0.05, 0.95, outlier_info, fontsize=10, transform=axes[1, 1].transAxes,
                        verticalalignment='top', fontfamily='monospace')
        axes[1, 1].set_xlim(0, 1)
        axes[1, 1].set_ylim(0, 1)
        axes[1, 1].axis('off')
        
        plt.tight_layout()
        
        # 保存图片
        viz_dir = Path("results/dataset_issue_visualization")
        viz_dir.mkdir(parents=True, exist_ok=True)
        plt.savefig(viz_dir / "scale_outliers.png", dpi=150, bbox_inches='tight')
        print(f"💾 保存尺度异常可视化: scale_outliers.png")
        plt.show()
        
        return pc_outliers, kp_outliers
    
    def visualize_sample_distribution(self):
        """可视化样本分布不平衡"""
        print("\n📊 可视化样本分布...")
        
        # 提取特征用于聚类分析
        features = []
        
        for pc, kp in zip(self.point_clouds, self.keypoints):
            pc_array = np.array(pc, dtype=np.float32)
            kp_array = np.array(kp, dtype=np.float32)
            
            # 简化特征：中心和尺度
            pc_center = np.mean(pc_array, axis=0)
            kp_center = np.mean(kp_array, axis=0)
            pc_scale = np.linalg.norm(np.max(pc_array, axis=0) - np.min(pc_array, axis=0))
            kp_scale = np.linalg.norm(np.max(kp_array, axis=0) - np.min(kp_array, axis=0))
            
            feature_vector = np.concatenate([pc_center, kp_center, [pc_scale, kp_scale]])
            features.append(feature_vector)
        
        features = np.array(features)
        
        # 简单聚类
        from sklearn.cluster import KMeans
        kmeans = KMeans(n_clusters=5, random_state=42)
        cluster_labels = kmeans.fit_predict(features)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 聚类分布
        unique_labels, counts = np.unique(cluster_labels, return_counts=True)
        colors = plt.cm.Set3(np.linspace(0, 1, len(unique_labels)))
        
        axes[0, 0].pie(counts, labels=[f'聚类 {i+1}' for i in unique_labels], 
                      colors=colors, autopct='%1.1f%%', startangle=90)
        axes[0, 0].set_title('样本聚类分布')
        
        # 聚类大小条形图
        axes[0, 1].bar(range(len(counts)), counts, color=colors, alpha=0.7)
        axes[0, 1].set_xlabel('聚类ID')
        axes[0, 1].set_ylabel('样本数')
        axes[0, 1].set_title('各聚类样本数量')
        axes[0, 1].set_xticks(range(len(counts)))
        axes[0, 1].set_xticklabels([f'聚类{i+1}' for i in range(len(counts))])
        axes[0, 1].grid(True, alpha=0.3)
        
        # 特征空间可视化 (前两个主成分)
        from sklearn.decomposition import PCA
        pca = PCA(n_components=2)
        features_2d = pca.fit_transform(features)
        
        for i, label in enumerate(unique_labels):
            mask = cluster_labels == label
            axes[1, 0].scatter(features_2d[mask, 0], features_2d[mask, 1], 
                             c=[colors[i]], label=f'聚类 {label+1}', alpha=0.7, s=50)
        
        axes[1, 0].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.1%} variance)')
        axes[1, 0].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.1%} variance)')
        axes[1, 0].set_title('样本在特征空间的分布')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 分布不平衡分析
        balance_analysis = f"""
样本分布不平衡分析:

聚类统计:
"""
        for i, (label, count) in enumerate(zip(unique_labels, counts)):
            percentage = count / len(self.sample_ids) * 100
            balance_analysis += f"• 聚类 {label+1}: {count} 样本 ({percentage:.1f}%)\n"
        
        # 计算不平衡程度
        balance_score = np.std(counts) / np.mean(counts)
        
        balance_analysis += f"""
不平衡程度: {balance_score:.2f}
• <0.3: 平衡
• 0.3-0.7: 轻度不平衡  
• 0.7-1.0: 中度不平衡
• >1.0: 严重不平衡

当前状态: {'严重不平衡' if balance_score > 1.0 else '中度不平衡' if balance_score > 0.7 else '轻度不平衡' if balance_score > 0.3 else '平衡'}

建议:
1. 对少数类进行数据增强
2. 对多数类进行下采样
3. 使用平衡的训练策略
4. 考虑重新收集数据
        """
        
        axes[1, 1].text(0.05, 0.95, balance_analysis, fontsize=10, transform=axes[1, 1].transAxes,
                        verticalalignment='top', fontfamily='monospace')
        axes[1, 1].set_xlim(0, 1)
        axes[1, 1].set_ylim(0, 1)
        axes[1, 1].axis('off')
        
        plt.tight_layout()
        
        # 保存图片
        viz_dir = Path("results/dataset_issue_visualization")
        viz_dir.mkdir(parents=True, exist_ok=True)
        plt.savefig(viz_dir / "sample_distribution.png", dpi=150, bbox_inches='tight')
        print(f"💾 保存样本分布可视化: sample_distribution.png")
        plt.show()
        
        return cluster_labels, balance_score
    
    def generate_issue_summary(self):
        """生成问题总结"""
        print("\n📋 数据集问题总结")
        print("=" * 50)
        
        # 运行所有分析
        self.visualize_coordinate_range_issues()
        pc_outliers, kp_outliers = self.visualize_scale_outliers()
        cluster_labels, balance_score = self.visualize_sample_distribution()
        
        # 生成总结报告
        summary = f"""
🔬 F3骨盆关键点数据集问题诊断报告
{'='*60}

📊 数据集基本信息:
• 样本数量: {len(self.sample_ids)}
• 点云大小: 50,000点/样本
• 关键点数量: 19个/样本
• 数据格式: 3D坐标 (mm)

🚨 发现的主要问题:

1. 坐标系不一致问题 ⚠️
   • X轴: 点云范围是关键点的3.6倍
   • Z轴: 点云范围是关键点的2.1倍
   • 说明: 点云包含大量关键点区域外的无关数据

2. 尺度异常问题 ⚠️
   • 点云尺度异常: {np.sum(pc_outliers)} 样本
   • 关键点尺度异常: {np.sum(kp_outliers)} 样本
   • 影响: 导致模型训练不稳定

3. 样本分布不平衡 ⚠️
   • 不平衡程度: {balance_score:.2f} (严重不平衡)
   • 最大聚类: {np.max(np.bincount(cluster_labels))} 样本
   • 最小聚类: {np.min(np.bincount(cluster_labels))} 样本
   • 影响: 模型偏向多数类样本

📈 问题严重程度评估: 需要改进

🔧 推荐的解决方案:

优先级1 (立即处理):
1. 裁剪点云到关键点附近区域 (减少无关数据)
2. 统一坐标系原点到关键点中心
3. 移除或修正极端异常样本

优先级2 (后续处理):  
4. 使用鲁棒标准化方法处理尺度差异
5. 对少数类样本进行数据增强
6. 实施表面投影确保关键点在点云表面

📊 预期改进效果:
• 模型训练稳定性: +40%
• 预测精度: +20-30%  
• 泛化能力: +25%

💡 下一步行动:
1. 实施数据清理和预处理
2. 重新评估处理后的数据质量
3. 进行对比实验验证改进效果
        """
        
        print(summary)
        
        # 保存总结报告
        viz_dir = Path("results/dataset_issue_visualization")
        viz_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        with open(viz_dir / f"dataset_issue_summary_{timestamp}.txt", 'w', encoding='utf-8') as f:
            f.write(summary)
        
        print(f"\n💾 问题总结报告已保存: dataset_issue_summary_{timestamp}.txt")
        
        return summary

def main():
    """主函数"""
    print("🔍 数据集问题可视化分析")
    print("=" * 60)
    
    visualizer = DatasetIssueVisualizer()
    summary = visualizer.generate_issue_summary()
    
    return visualizer, summary

if __name__ == "__main__":
    visualizer, summary = main()
