#!/usr/bin/env python3
"""
Train Simple PointNet on F3 Dataset

Based on successful tests, train a simple but effective PointNet model.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import time
import json
from sklearn.model_selection import train_test_split

class SimpleF3Dataset(Dataset):
    """Simple dataset loader for F3 training"""
    
    def __init__(self, data_path: str, split: str = 'train', num_points: int = 4096,
                 test_size: float = 0.2, val_size: float = 0.1, random_state: int = 42, 
                 augment: bool = False):
        
        self.num_points = num_points
        self.augment = augment
        
        # Load dataset
        data = np.load(data_path, allow_pickle=True)
        
        sample_ids = data['sample_ids']
        point_clouds = data['point_clouds']  # (97, 50000, 3)
        keypoints = data['keypoints']        # (97, 19, 3)
        
        # Split data
        indices = np.arange(len(sample_ids))
        train_indices, temp_indices = train_test_split(
            indices, test_size=test_size + val_size, random_state=random_state
        )
        val_indices, test_indices = train_test_split(
            temp_indices, test_size=test_size/(test_size + val_size), random_state=random_state
        )
        
        if split == 'train':
            self.indices = train_indices
        elif split == 'val':
            self.indices = val_indices
        elif split == 'test':
            self.indices = test_indices
        
        self.point_clouds = point_clouds[self.indices]
        self.keypoints = keypoints[self.indices]
        
        print(f"   {split}: {len(self.indices)} 样本")
    
    def __len__(self):
        return len(self.indices)
    
    def __getitem__(self, idx):
        point_cloud = self.point_clouds[idx].copy()
        keypoints = self.keypoints[idx].copy()
        
        # Downsample
        if len(point_cloud) > self.num_points:
            indices = np.random.choice(len(point_cloud), self.num_points, replace=False)
            point_cloud = point_cloud[indices]
        
        # Simple augmentation
        if self.augment:
            # Small rotation
            angle = np.random.uniform(-0.1, 0.1)
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation = np.array([[cos_a, -sin_a, 0], [sin_a, cos_a, 0], [0, 0, 1]])
            point_cloud = point_cloud @ rotation.T
            keypoints = keypoints @ rotation.T
            
            # Small translation
            translation = np.random.uniform(-0.5, 0.5, 3)
            point_cloud += translation
            keypoints += translation
        
        return {
            'point_cloud': torch.FloatTensor(point_cloud),
            'keypoints': torch.FloatTensor(keypoints)
        }

class SimplePointNet(nn.Module):
    """Simple PointNet for F3 keypoint detection"""
    
    def __init__(self, num_keypoints: int = 19):
        super(SimplePointNet, self).__init__()
        
        # Point-wise MLPs
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        
        # Global MLPs
        self.fc1 = nn.Linear(256, 128)
        self.fc2 = nn.Linear(128, 64)
        self.fc3 = nn.Linear(64, num_keypoints * 3)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        # x: (batch, points, 3) -> (batch, 3, points)
        batch_size = x.size(0)
        x = x.transpose(2, 1)
        
        # Point-wise features
        x = torch.relu(self.bn1(self.conv1(x)))
        x = torch.relu(self.bn2(self.conv2(x)))
        x = torch.relu(self.bn3(self.conv3(x)))
        
        # Global max pooling
        x = torch.max(x, 2)[0]  # (batch, 256)
        
        # Global features
        x = torch.relu(self.fc1(x))
        x = self.dropout(x)
        x = torch.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x.view(batch_size, 19, 3)

def calculate_metrics(pred, target):
    """Calculate distance metrics"""
    distances = torch.norm(pred - target, dim=2)  # (batch, 19)
    avg_distances = torch.mean(distances, dim=1)  # (batch,)
    
    mean_dist = torch.mean(avg_distances).item()
    within_1mm = (avg_distances <= 1.0).float().mean().item() * 100
    within_5mm = (avg_distances <= 5.0).float().mean().item() * 100
    
    return {
        'mean_distance': mean_dist,
        'within_1mm_percent': within_1mm,
        'within_5mm_percent': within_5mm
    }

def train_simple_pointnet():
    """Train simple PointNet model"""
    
    print("🚀 **训练简单PointNet - F3关键点检测**")
    print("=" * 60)
    
    # Setup
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  设备: {device}")
    
    # Dataset
    dataset_path = "high_quality_f3_dataset.npz"
    num_points = 4096
    
    print(f"📦 创建数据集...")
    train_dataset = SimpleF3Dataset(dataset_path, 'train', num_points, augment=True)
    val_dataset = SimpleF3Dataset(dataset_path, 'val', num_points, augment=False)
    test_dataset = SimpleF3Dataset(dataset_path, 'test', num_points, augment=False)
    
    # Data loaders
    batch_size = 8
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=2)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=2)
    
    print(f"📊 数据统计:")
    print(f"   训练: {len(train_dataset)}, 验证: {len(val_dataset)}, 测试: {len(test_dataset)}")
    print(f"   批大小: {batch_size}, 点云: {num_points} 点")
    
    # Model
    model = SimplePointNet(num_keypoints=19).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"🧠 模型参数: {total_params:,}")
    
    # Training setup
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=8, factor=0.8)
    
    # Training loop
    num_epochs = 80
    best_val_error = float('inf')
    patience = 12
    patience_counter = 0
    history = []
    
    print(f"\n🎯 开始训练 (目标: <5mm医疗精度)")
    start_time = time.time()
    
    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")
        
        # Training
        model.train()
        train_loss = 0.0
        train_metrics = {'mean_distance': 0, 'within_1mm_percent': 0, 'within_5mm_percent': 0}
        
        for batch in train_loader:
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            optimizer.zero_grad()
            pred_keypoints = model(point_cloud)
            loss = criterion(pred_keypoints, keypoints)
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            
            train_loss += loss.item()
            
            with torch.no_grad():
                metrics = calculate_metrics(pred_keypoints, keypoints)
                for key in train_metrics:
                    train_metrics[key] += metrics[key]
        
        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
        
        # Validation
        model.eval()
        val_loss = 0.0
        val_metrics = {'mean_distance': 0, 'within_1mm_percent': 0, 'within_5mm_percent': 0}
        
        with torch.no_grad():
            for batch in val_loader:
                point_cloud = batch['point_cloud'].to(device)
                keypoints = batch['keypoints'].to(device)
                
                pred_keypoints = model(point_cloud)
                loss = criterion(pred_keypoints, keypoints)
                val_loss += loss.item()
                
                metrics = calculate_metrics(pred_keypoints, keypoints)
                for key in val_metrics:
                    val_metrics[key] += metrics[key]
        
        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)
        
        scheduler.step(val_metrics['mean_distance'])
        
        # Print results
        print(f"训练: Loss={train_loss:.4f}, 误差={train_metrics['mean_distance']:.3f}mm, "
              f"1mm={train_metrics['within_1mm_percent']:.1f}%, 5mm={train_metrics['within_5mm_percent']:.1f}%")
        print(f"验证: Loss={val_loss:.4f}, 误差={val_metrics['mean_distance']:.3f}mm, "
              f"1mm={val_metrics['within_1mm_percent']:.1f}%, 5mm={val_metrics['within_5mm_percent']:.1f}%")
        
        # Save history
        history.append({
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics
        })
        
        # Check improvement
        current_error = val_metrics['mean_distance']
        if current_error < best_val_error:
            best_val_error = current_error
            patience_counter = 0
            torch.save(model.state_dict(), 'best_simple_pointnet_f3.pth')
            print(f"🎉 新最佳: {best_val_error:.3f}mm")
        else:
            patience_counter += 1
            print(f"⏳ 无改善 ({patience_counter}/{patience})")
        
        if patience_counter >= patience:
            print("🛑 早停")
            break
    
    total_time = time.time() - start_time
    
    # Test evaluation
    print(f"\n🧪 **测试评估**")
    model.load_state_dict(torch.load('best_simple_pointnet_f3.pth'))
    model.eval()
    
    test_metrics = {'mean_distance': 0, 'within_1mm_percent': 0, 'within_5mm_percent': 0}
    
    with torch.no_grad():
        for batch in test_loader:
            point_cloud = batch['point_cloud'].to(device)
            keypoints = batch['keypoints'].to(device)
            
            pred_keypoints = model(point_cloud)
            metrics = calculate_metrics(pred_keypoints, keypoints)
            
            for key in test_metrics:
                test_metrics[key] += metrics[key]
    
    for key in test_metrics:
        test_metrics[key] /= len(test_loader)
    
    print(f"📊 最终测试结果:")
    print(f"   测试误差: {test_metrics['mean_distance']:.3f}mm")
    print(f"   1mm精度: {test_metrics['within_1mm_percent']:.1f}%")
    print(f"   5mm精度: {test_metrics['within_5mm_percent']:.1f}%")
    print(f"   训练时间: {total_time/60:.1f}分钟")
    
    # Assessment
    if test_metrics['mean_distance'] <= 1.0:
        print(f"🏆 **优秀!** 达到医疗级精度 (<1mm)")
    elif test_metrics['mean_distance'] <= 5.0:
        print(f"✅ **良好!** 达到医疗可用精度 (<5mm)")
    else:
        print(f"⚠️ **需要改进** 未达到医疗精度要求")
    
    # Save results
    results = {
        'model_name': 'Simple_PointNet_F3',
        'best_val_error_mm': best_val_error,
        'test_metrics': test_metrics,
        'training_time_minutes': total_time / 60,
        'total_epochs': len(history),
        'dataset_samples': len(train_dataset) + len(val_dataset) + len(test_dataset),
        'model_parameters': total_params,
        'training_history': history
    }
    
    with open('simple_pointnet_f3_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    return test_metrics

if __name__ == "__main__":
    try:
        results = train_simple_pointnet()
        print(f"\n🎉 **训练完成!**")
        print(f"🎯 最终误差: {results['mean_distance']:.3f}mm")
        print(f"📊 医疗精度: {results['within_5mm_percent']:.1f}% ≤5mm")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
