{"summary_timestamp": "2025-07-20T13:34:34.320470", "dataset_info": {"name": "F3对齐骨盆点云数据", "total_samples": 97, "train_samples": 67, "val_samples": 15, "test_samples": 15, "num_keypoints": 19, "point_cloud_size": 4096}, "medical_target": 5.0, "best_validation_error": 7.041, "best_test_error": 7.19, "total_experiments": 19, "experiment_categories": ["基础方法", "小样本学习", "高级方法", "前沿方法", "优化尝试"], "all_results": [{"实验类别": "高级方法", "方法名称": "Mixup模型", "验证误差(mm)": 7.041, "测试误差(mm)": 8.363, "训练策略": "Mixup增强", "特殊技术": "数据混合", "参数量": "约300万", "训练时间": "中等", "稳定性": "中等", "备注": "验证误差最佳"}, {"实验类别": "高级方法", "方法名称": "Point Transformer", "验证误差(mm)": 7.129, "测试误差(mm)": 8.127, "训练策略": "注意力机制", "特殊技术": "点云Transformer", "参数量": "约500万", "训练时间": "长", "稳定性": "很高", "备注": "历史最稳定"}, {"实验类别": "高级方法", "方法名称": "一致性正则化", "验证误差(mm)": 7.176, "测试误差(mm)": 8.012, "训练策略": "双网络一致性", "特殊技术": "一致性损失", "参数量": "约400万", "训练时间": "中等", "稳定性": "很高", "备注": "测试性能最佳"}, {"实验类别": "基础方法", "方法名称": "简单集成PointNet", "验证误差(mm)": 7.19, "测试误差(mm)": 7.19, "训练策略": "3模型集成", "特殊技术": "模型集成", "参数量": "约400万", "训练时间": "中等", "稳定性": "高", "备注": "早期最佳结果"}, {"实验类别": "小样本学习", "方法名称": "基于梯度的元学习", "验证误差(mm)": 7.277, "测试误差(mm)": 8.039, "训练策略": "简化MAML", "特殊技术": "快速适应", "参数量": "约250万", "训练时间": "中等", "稳定性": "高", "备注": "元学习最佳"}, {"实验类别": "前沿方法", "方法名称": "注意力机制/Transformer", "验证误差(mm)": 7.383, "测试误差(mm)": 9.588, "训练策略": "Transformer编码器", "特殊技术": "自注意力+位置编码", "参数量": "约450万", "训练时间": "长", "稳定性": "中等", "备注": "前沿方法最佳"}, {"实验类别": "小样本学习", "方法名称": "原型网络", "验证误差(mm)": 7.426, "测试误差(mm)": 8.027, "训练策略": "原型学习", "特殊技术": "距离度量学习", "参数量": "约300万", "训练时间": "中等", "稳定性": "中等", "备注": "小样本方法最佳"}, {"实验类别": "小样本学习", "方法名称": "迁移学习", "验证误差(mm)": 7.469, "测试误差(mm)": 8.258, "训练策略": "冻结+微调", "特殊技术": "预训练特征", "参数量": "约180万(可训练)", "训练时间": "短", "稳定性": "高", "备注": "实用性强"}, {"实验类别": "前沿方法", "方法名称": "集成元学习", "验证误差(mm)": 7.587, "测试误差(mm)": 8.487, "训练策略": "动态权重集成", "特殊技术": "自适应集成", "参数量": "约600万", "训练时间": "长", "稳定性": "中等", "备注": "多模型集成"}, {"实验类别": "小样本学习", "方法名称": "自监督学习", "验证误差(mm)": 7.602, "测试误差(mm)": 8.968, "训练策略": "多任务学习", "特殊技术": "旋转+噪声预测", "参数量": "约350万", "训练时间": "长", "稳定性": "中等", "备注": "多任务辅助"}, {"实验类别": "前沿方法", "方法名称": "图神经网络", "验证误差(mm)": 7.655, "测试误差(mm)": 8.294, "训练策略": "k-NN图+图卷积", "特殊技术": "图结构建模", "参数量": "约350万", "训练时间": "长", "稳定性": "中等", "备注": "几何关系建模"}, {"实验类别": "前沿方法", "方法名称": "对比学习", "验证误差(mm)": 7.855, "测试误差(mm)": 8.497, "训练策略": "InfoNCE对比学习", "特殊技术": "对比损失", "参数量": "约380万", "训练时间": "中等", "稳定性": "中等", "备注": "表示学习"}, {"实验类别": "小样本学习", "方法名称": "匹配网络", "验证误差(mm)": 8.47, "测试误差(mm)": 10.536, "训练策略": "注意力匹配", "特殊技术": "多头注意力", "参数量": "约320万", "训练时间": "长", "稳定性": "低", "备注": "注意力机制"}, {"实验类别": "小样本学习", "方法名称": "关系网络", "验证误差(mm)": 8.551, "测试误差(mm)": 10.912, "训练策略": "关系学习", "特殊技术": "关系建模", "参数量": "约280万", "训练时间": "中等", "稳定性": "低", "备注": "复杂度过高"}, {"实验类别": "前沿方法", "方法名称": "变分自编码器", "验证误差(mm)": 8.679, "测试误差(mm)": 8.451, "训练策略": "VAE潜在空间学习", "特殊技术": "变分推断", "参数量": "约320万", "训练时间": "中等", "稳定性": "中等", "备注": "生成建模"}, {"实验类别": "优化尝试", "方法名称": "精准微调优化", "验证误差(mm)": 11.052, "测试误差(mm)": 10.881, "训练策略": "架构微调", "特殊技术": "精准TTA", "参数量": "约580万", "训练时间": "长", "稳定性": "低", "备注": "过度优化失败"}, {"实验类别": "优化尝试", "方法名称": "增强Mixup优化", "验证误差(mm)": 11.935, "测试误差(mm)": 12.641, "训练策略": "增强架构", "特殊技术": "高级Mixup", "参数量": "约450万", "训练时间": "长", "稳定性": "低", "备注": "优化适得其反"}, {"实验类别": "基础方法", "方法名称": "简单PointNet", "验证误差(mm)": 15.234, "测试误差(mm)": 16.892, "训练策略": "标准训练", "特殊技术": "无", "参数量": "约200万", "训练时间": "短", "稳定性": "中等", "备注": "基础基线"}, {"实验类别": "优化尝试", "方法名称": "最小化改进", "验证误差(mm)": 15.418, "测试误差(mm)": 11.673, "训练策略": "保守训练", "特殊技术": "最小TTA", "参数量": "约400万", "训练时间": "长", "稳定性": "低", "备注": "复现失败"}]}