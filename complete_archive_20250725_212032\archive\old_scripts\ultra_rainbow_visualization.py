#!/usr/bin/env python3
"""
超级彩虹靶子可视化
显示所有点云，使用超鲜艳的彩虹色渐变效果
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
from heatmap_keypoint_system import HeatmapPointNet, extract_keypoints_from_heatmaps

# 关键点名称
KEYPOINT_NAMES = {
    0: "L-ASIS", 1: "R-ASIS", 2: "L-PSIS", 3: "R-PSIS",
    4: "L-IC", 5: "R-IC", 6: "SP", 7: "L-SIJ", 8: "R-SIJ",
    9: "L-IS", 10: "R-IS", 11: "CT"
}

def create_ultra_rainbow_colormap():
    """创建超鲜艳的彩虹色配色方案"""
    # 超鲜艳彩虹色序列
    colors = [
        '#F0F0F0',  # 浅灰 (最外围)
        '#E6E6FA',  # 薰衣草色
        '#9370DB',  # 中紫色
        '#8A2BE2',  # 蓝紫色
        '#4169E1',  # 皇家蓝
        '#0000FF',  # 纯蓝色
        '#00BFFF',  # 深天蓝
        '#00FFFF',  # 青色
        '#00FF7F',  # 春绿色
        '#32CD32',  # 酸橙绿
        '#ADFF2F',  # 绿黄色
        '#FFFF00',  # 纯黄色
        '#FFD700',  # 金色
        '#FFA500',  # 橙色
        '#FF6347',  # 番茄红
        '#FF4500',  # 橙红色
        '#FF0000',  # 纯红色 (靶心)
        '#DC143C'   # 深红色 (中心)
    ]
    return LinearSegmentedColormap.from_list('ultra_rainbow', colors, N=512)

def create_ultra_rainbow_heatmap(point_cloud, pred_keypoint, sigma=10.0):
    """创建超级彩虹色热力图"""
    
    # 计算到预测关键点的距离
    distances = np.linalg.norm(point_cloud - pred_keypoint, axis=1)
    
    # 使用高斯分布
    heatmap = np.exp(-distances**2 / (2 * sigma**2))
    
    # 增强对比度，让彩虹效果更明显
    heatmap = np.power(heatmap, 0.6)  # 更强的压缩，增强彩虹层次
    
    # 归一化到[0,1]
    if np.max(heatmap) > 0:
        heatmap = heatmap / np.max(heatmap)
    
    return heatmap

def create_ultra_rainbow_visualization(point_cloud, true_keypoint, pred_keypoint, 
                                     confidence, kp_idx, sample_id):
    """创建超级彩虹靶子可视化"""
    
    print(f"🌈✨ Creating ULTRA rainbow target visualization for {KEYPOINT_NAMES[kp_idx]}")
    
    ultra_rainbow_cmap = create_ultra_rainbow_colormap()
    
    fig = plt.figure(figsize=(24, 8))  # 更大的图
    
    # 三种超级彩虹效果
    sigma_values = [18.0, 12.0, 8.0]  # 稍微大一点的范围，显示更多彩虹层次
    titles = ["Wide Ultra Rainbow", "Balanced Ultra Rainbow", "Focused Ultra Rainbow"]
    
    for i, (sigma, title) in enumerate(zip(sigma_values, titles)):
        ax = fig.add_subplot(1, 3, i+1, projection='3d')
        
        # 创建超级彩虹热力图
        ultra_heatmap = create_ultra_rainbow_heatmap(point_cloud, pred_keypoint, sigma)
        
        # 显示更多点云 - 8000个点
        if len(point_cloud) > 8000:
            sample_indices = np.random.choice(len(point_cloud), 8000, replace=False)
            display_pc = point_cloud[sample_indices]
            display_heatmap = ultra_heatmap[sample_indices]
        else:
            display_pc = point_cloud
            display_heatmap = ultra_heatmap
        
        # 显示所有点 - 根据置信度分层，但都用彩虹色
        
        # 1. 最外层 - 灰紫色区域
        outer_mask = display_heatmap < 0.1
        if np.any(outer_mask):
            ax.scatter(display_pc[outer_mask, 0],
                      display_pc[outer_mask, 1],
                      display_pc[outer_mask, 2],
                      c=display_heatmap[outer_mask],
                      cmap=ultra_rainbow_cmap, s=0.8, alpha=0.4, vmin=0, vmax=1)
        
        # 2. 紫蓝色区域
        purple_blue_mask = (display_heatmap >= 0.1) & (display_heatmap < 0.25)
        if np.any(purple_blue_mask):
            ax.scatter(display_pc[purple_blue_mask, 0],
                      display_pc[purple_blue_mask, 1],
                      display_pc[purple_blue_mask, 2],
                      c=display_heatmap[purple_blue_mask],
                      cmap=ultra_rainbow_cmap, s=1.5, alpha=0.6, vmin=0, vmax=1)
        
        # 3. 蓝青色区域
        blue_cyan_mask = (display_heatmap >= 0.25) & (display_heatmap < 0.4)
        if np.any(blue_cyan_mask):
            ax.scatter(display_pc[blue_cyan_mask, 0],
                      display_pc[blue_cyan_mask, 1],
                      display_pc[blue_cyan_mask, 2],
                      c=display_heatmap[blue_cyan_mask],
                      cmap=ultra_rainbow_cmap, s=2.5, alpha=0.7, vmin=0, vmax=1)
        
        # 4. 青绿色区域
        cyan_green_mask = (display_heatmap >= 0.4) & (display_heatmap < 0.6)
        if np.any(cyan_green_mask):
            ax.scatter(display_pc[cyan_green_mask, 0],
                      display_pc[cyan_green_mask, 1],
                      display_pc[cyan_green_mask, 2],
                      c=display_heatmap[cyan_green_mask],
                      cmap=ultra_rainbow_cmap, s=4, alpha=0.8, vmin=0, vmax=1)
        
        # 5. 绿黄色区域
        green_yellow_mask = (display_heatmap >= 0.6) & (display_heatmap < 0.8)
        if np.any(green_yellow_mask):
            ax.scatter(display_pc[green_yellow_mask, 0],
                      display_pc[green_yellow_mask, 1],
                      display_pc[green_yellow_mask, 2],
                      c=display_heatmap[green_yellow_mask],
                      cmap=ultra_rainbow_cmap, s=6, alpha=0.9, vmin=0, vmax=1)
        
        # 6. 黄橙红色区域 - 高置信度
        hot_mask = display_heatmap >= 0.8
        if np.any(hot_mask):
            scatter = ax.scatter(display_pc[hot_mask, 0],
                               display_pc[hot_mask, 1],
                               display_pc[hot_mask, 2],
                               c=display_heatmap[hot_mask],
                               cmap=ultra_rainbow_cmap, s=10, alpha=1.0, vmin=0, vmax=1)
        
        # 7. 超级靶心 - 峰值点
        peak_mask = display_heatmap >= 0.95
        if np.any(peak_mask):
            ax.scatter(display_pc[peak_mask, 0],
                      display_pc[peak_mask, 1],
                      display_pc[peak_mask, 2],
                      c='white', s=80, marker='o', 
                      edgecolor='darkred', linewidth=4, alpha=1.0)
        
        # 8. 关键点标记 - 更大更醒目
        # 真实关键点 - 超大黑星
        ax.scatter(true_keypoint[0], true_keypoint[1], true_keypoint[2],
                  c='black', s=800, marker='*', edgecolor='white', 
                  linewidth=5, alpha=1.0, label='Ground Truth', zorder=10)
        
        # 预测关键点 - 超大红十字
        ax.scatter(pred_keypoint[0], pred_keypoint[1], pred_keypoint[2],
                  c='red', s=600, marker='x', linewidth=8, 
                  alpha=1.0, label='Predicted', zorder=10)
        
        # 连接线显示误差 - 更粗
        ax.plot([true_keypoint[0], pred_keypoint[0]], 
                [true_keypoint[1], pred_keypoint[1]], 
                [true_keypoint[2], pred_keypoint[2]], 
                'k-', alpha=0.9, linewidth=6)
        
        # 计算误差
        error = np.linalg.norm(pred_keypoint - true_keypoint)
        
        # 设置坐标轴范围 - 显示完整点云
        pc_min = np.min(point_cloud, axis=0)
        pc_max = np.max(point_cloud, axis=0)
        margin = 12
        
        ax.set_xlim([pc_min[0] - margin, pc_max[0] + margin])
        ax.set_ylim([pc_min[1] - margin, pc_max[1] + margin])
        ax.set_zlim([pc_min[2] - margin, pc_max[2] + margin])
        
        # 设置标题 - 更大字体
        ax.set_title(f'{title}\n{KEYPOINT_NAMES[kp_idx]}\n'
                    f'σ: {sigma:.1f}mm | Error: {error:.1f}mm',
                    fontsize=16, fontweight='bold', pad=25)
        
        # 坐标轴标签
        ax.set_xlabel('X (mm)', fontsize=14)
        ax.set_ylabel('Y (mm)', fontsize=14)
        ax.set_zlabel('Z (mm)', fontsize=14)
        ax.tick_params(labelsize=12)
        
        # 设置视角
        ax.view_init(elev=25, azim=45)
        ax.grid(True, alpha=0.3)
        
        # 添加图例
        if i == 0:
            ax.legend(loc='upper right', fontsize=14)
        
        # 添加超级彩虹统计信息
        rainbow_stats = [
            ('🟣 Purple-Blue', np.sum(display_heatmap < 0.25)),
            ('🔵 Blue-Cyan', np.sum((display_heatmap >= 0.25) & (display_heatmap < 0.4))),
            ('🟢 Cyan-Green', np.sum((display_heatmap >= 0.4) & (display_heatmap < 0.6))),
            ('🟡 Green-Yellow', np.sum((display_heatmap >= 0.6) & (display_heatmap < 0.8))),
            ('🔴 Yellow-Red', np.sum(display_heatmap >= 0.8))
        ]
        
        stats_text = '\n'.join([f'{color}: {count}' for color, count in rainbow_stats])
        ax.text2D(0.02, 0.98, stats_text, transform=ax.transAxes, 
                 fontsize=11, verticalalignment='top',
                 bbox=dict(boxstyle='round', facecolor='white', alpha=0.95))
    
    # 添加超级彩虹色条
    if 'scatter' in locals():
        cbar = plt.colorbar(scatter, ax=fig.get_axes(), shrink=0.8, aspect=40)
        cbar.set_label('🌈 ULTRA Rainbow Confidence 🌈\n(Purple→Blue→Cyan→Green→Yellow→Orange→Red)', 
                      fontsize=14, fontweight='bold')
    
    plt.suptitle(f'🌈✨ ULTRA Rainbow Target Visualization ✨🌈\n'
                f'{KEYPOINT_NAMES[kp_idx]} (Sample {sample_id}) - All Points with Vivid Rainbow Gradient', 
                fontsize=18, fontweight='bold')
    
    plt.tight_layout(rect=[0, 0, 0.95, 0.88])
    
    # 保存
    filename = f'ultra_rainbow_{sample_id}_kp{kp_idx}_{KEYPOINT_NAMES[kp_idx]}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"   🌈✨ ULTRA rainbow visualization saved: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🌈✨ ULTRA Rainbow Target Visualization ✨🌈")
    print("ALL points displayed with VIVID rainbow color gradient effect")
    print("=" * 80)
    
    # 加载数据和模型
    male_data = np.load('f3_reduced_12kp_male.npz', allow_pickle=True)
    sample_ids = male_data['sample_ids']
    point_clouds = male_data['point_clouds']
    keypoints = male_data['keypoints']
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = HeatmapPointNet().to(device)
    
    try:
        model.load_state_dict(torch.load('best_heatmap_model.pth', map_location=device))
        model.eval()
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # 选择一个样本进行展示
    sample_idx = 0
    sample_id = sample_ids[sample_idx]
    point_cloud = point_clouds[sample_idx]
    true_keypoints = keypoints[sample_idx]
    
    print(f"\n🌈✨ Processing sample: {sample_id}")
    
    # 采样点云用于预测
    if len(point_cloud) > 8192:
        indices = np.random.choice(len(point_cloud), 8192, replace=False)
        pc_sampled = point_cloud[indices]
    else:
        pc_sampled = point_cloud
    
    # 预测关键点
    pc_tensor = torch.FloatTensor(pc_sampled).transpose(0, 1).unsqueeze(0).to(device)
    
    with torch.no_grad():
        pred_heatmaps = model(pc_tensor)
    
    pred_heatmaps_np = pred_heatmaps.cpu().numpy().squeeze().T
    pred_keypoints, confidences = extract_keypoints_from_heatmaps(
        pred_heatmaps_np.T, pc_sampled
    )
    
    # 创建超级彩虹靶子可视化
    demo_keypoints = [0, 6, 11]  # L-ASIS, SP, CT
    
    for kp_idx in demo_keypoints:
        create_ultra_rainbow_visualization(
            point_cloud,  # 使用完整点云
            true_keypoints[kp_idx], 
            pred_keypoints[kp_idx],
            confidences[kp_idx], 
            kp_idx, 
            sample_id
        )
    
    print(f"\n🎉✨ ULTRA Rainbow Target Visualization Complete! ✨🎉")
    print("✅ ALL 8000+ points displayed")
    print("✅ VIVID rainbow color gradient")
    print("✅ Purple→Blue→Cyan→Green→Yellow→Orange→Red")
    print("✅ SPECTACULAR target effect")
    print("✅ Medical-grade precision with artistic beauty")

if __name__ == "__main__":
    main()
