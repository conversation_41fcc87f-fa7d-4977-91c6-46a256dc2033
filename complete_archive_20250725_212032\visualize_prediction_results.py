#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测结果可视化
Prediction Results Visualization
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import torch
import torch.nn as nn
import json
from sklearn.model_selection import train_test_split
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class AdaptiveKeypointModel(nn.Module):
    """自适应关键点检测模型"""
    def __init__(self, num_points=50000, num_keypoints=12, architecture_type='balanced'):
        super(AdaptiveKeypointModel, self).__init__()
        self.num_keypoints = num_keypoints
        self.architecture_type = architecture_type
        
        # 根据架构类型设置参数
        if architecture_type == 'lightweight':
            hidden_dims = [64, 128, 256]
        elif architecture_type == 'balanced':
            hidden_dims = [128, 256, 512]
        elif architecture_type == 'enhanced':
            hidden_dims = [256, 512, 1024]
        elif architecture_type == 'auto':
            # 根据关键点数量自动调整
            if num_keypoints <= 12:
                hidden_dims = [128, 256, 512]
            elif num_keypoints <= 30:
                hidden_dims = [256, 512, 1024]
            else:
                hidden_dims = [512, 1024, 2048]
        else:
            hidden_dims = [128, 256, 512]
        
        # 特征提取层
        self.conv1 = nn.Conv1d(3, hidden_dims[0], 1)
        self.conv2 = nn.Conv1d(hidden_dims[0], hidden_dims[1], 1)
        self.conv3 = nn.Conv1d(hidden_dims[1], hidden_dims[2], 1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(hidden_dims[0])
        self.bn2 = nn.BatchNorm1d(hidden_dims[1])
        self.bn3 = nn.BatchNorm1d(hidden_dims[2])
        
        # 全连接层
        fc_input_dim = hidden_dims[2]
        self.fc1 = nn.Linear(fc_input_dim, hidden_dims[1])
        self.fc2 = nn.Linear(hidden_dims[1], hidden_dims[0])
        self.fc3 = nn.Linear(hidden_dims[0], num_keypoints * 3)
        
        # Dropout
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        # x: (batch_size, 3, num_points)
        x = torch.relu(self.bn1(self.conv1(x)))
        x = torch.relu(self.bn2(self.conv2(x)))
        x = torch.relu(self.bn3(self.conv3(x)))
        
        # 全局最大池化
        x = torch.max(x, 2)[0]
        
        # 全连接层
        x = torch.relu(self.fc1(x))
        x = self.dropout(x)
        x = torch.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        # 重塑为关键点坐标
        x = x.view(-1, self.num_keypoints, 3)
        return x

def load_data():
    """加载数据"""
    try:
        # 尝试加载不同的数据文件
        data_files = [
            ('high_quality_pelvis_57_dataset.npz', 'point_clouds', 'keypoints_57'),
            ('enhanced_high_quality_57_dataset.npz', 'point_clouds', 'keypoints_57'),
            ('keypoints_57_dataset.npz', None, 'keypoints_57')
        ]

        for data_file, pc_key, kp_key in data_files:
            if os.path.exists(data_file):
                print(f"📥 加载数据文件: {data_file}")
                try:
                    data = np.load(data_file, allow_pickle=True)

                    if pc_key and pc_key in data:
                        point_clouds = data[pc_key]
                    else:
                        # 如果没有点云数据，生成模拟点云
                        keypoints = data[kp_key]
                        print("⚠️ 没有点云数据，生成模拟点云")
                        point_clouds = generate_mock_point_clouds(keypoints)

                    keypoints = data[kp_key]
                    print(f"✅ 数据加载成功: 样本数={len(point_clouds)}, 关键点数={keypoints.shape[1]}")
                    return point_clouds, keypoints

                except Exception as e:
                    print(f"❌ 加载 {data_file} 失败: {e}")
                    continue

        raise FileNotFoundError("未找到合适的数据文件")

    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None, None

def generate_mock_point_clouds(keypoints):
    """生成模拟点云数据"""
    print("🔧 生成模拟点云数据...")
    point_clouds = []

    for kp in keypoints:
        # 基于关键点生成点云
        center = np.mean(kp, axis=0)
        scale = np.max(np.linalg.norm(kp - center, axis=1)) * 2

        # 生成随机点云
        pc = np.random.randn(5000, 3) * scale * 0.3 + center
        point_clouds.append(pc)

    return np.array(point_clouds)

def generate_keypoint_subsets(keypoints, target_num):
    """生成指定数量的关键点子集"""
    if target_num >= keypoints.shape[1]:
        return keypoints
    
    # 均匀采样关键点
    indices = np.linspace(0, keypoints.shape[1]-1, target_num, dtype=int)
    return keypoints[:, indices, :]

def visualize_predictions(point_clouds, true_keypoints, pred_keypoints, keypoint_counts, sample_idx=0):
    """可视化预测结果"""
    print(f"\n🎨 创建预测结果可视化 (样本 {sample_idx})")
    
    # 选择要可视化的关键点数量
    selected_counts = [3, 15, 47, 57] if max(keypoint_counts) >= 57 else keypoint_counts[:4]
    
    fig = plt.figure(figsize=(20, 15))
    
    for i, num_kp in enumerate(selected_counts):
        if num_kp not in keypoint_counts:
            continue
            
        ax = fig.add_subplot(2, 2, i+1, projection='3d')
        
        # 获取对应的数据
        kp_idx = keypoint_counts.index(num_kp)
        true_kp = true_keypoints[kp_idx][sample_idx]
        pred_kp = pred_keypoints[kp_idx][sample_idx]
        pc = point_clouds[sample_idx]
        
        # 绘制点云（浅灰色，小点）
        ax.scatter(pc[:, 0], pc[:, 1], pc[:, 2], 
                  c='lightgray', s=0.1, alpha=0.3, label='Point Cloud')
        
        # 绘制真实关键点（绿色，大点）
        ax.scatter(true_kp[:, 0], true_kp[:, 1], true_kp[:, 2], 
                  c='green', s=100, alpha=0.8, label='Ground Truth', marker='o')
        
        # 绘制预测关键点（红色，大点）
        ax.scatter(pred_kp[:, 0], pred_kp[:, 1], pred_kp[:, 2], 
                  c='red', s=100, alpha=0.8, label='Prediction', marker='^')
        
        # 绘制连接线显示误差
        for j in range(len(true_kp)):
            ax.plot([true_kp[j, 0], pred_kp[j, 0]], 
                   [true_kp[j, 1], pred_kp[j, 1]], 
                   [true_kp[j, 2], pred_kp[j, 2]], 
                   'b--', alpha=0.5, linewidth=1)
        
        # 计算平均误差
        errors = np.linalg.norm(true_kp - pred_kp, axis=1)
        avg_error = np.mean(errors)
        
        ax.set_title(f'{num_kp} Keypoints\nAvg Error: {avg_error:.2f}mm', 
                    fontsize=14, fontweight='bold')
        ax.set_xlabel('X (mm)')
        ax.set_ylabel('Y (mm)')
        ax.set_zlabel('Z (mm)')
        ax.legend()
        
        # 设置相同的坐标范围
        ax.set_xlim([pc[:, 0].min(), pc[:, 0].max()])
        ax.set_ylim([pc[:, 1].min(), pc[:, 1].max()])
        ax.set_zlim([pc[:, 2].min(), pc[:, 2].max()])
    
    plt.tight_layout()
    plt.savefig(f'prediction_visualization_sample_{sample_idx}.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 可视化已保存: prediction_visualization_sample_{sample_idx}.png")

def run_prediction_visualization():
    """运行预测结果可视化"""
    print("🎯 预测结果可视化")
    print("=" * 60)
    
    # 加载数据
    point_clouds, keypoints = load_data()
    if point_clouds is None:
        return
    
    # 设置设备
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 关键点数量配置
    keypoint_counts = [3, 6, 9, 12, 15, 19, 24, 28, 33, 38, 43, 47, 52, 57]
    
    # 数据分割
    train_pc, test_pc, train_kp, test_kp = train_test_split(
        point_clouds, keypoints, test_size=0.2, random_state=42)
    
    print(f"📊 数据分割: 训练{len(train_pc)}, 测试{len(test_pc)}")
    
    # 加载最佳模型配置
    best_configs = {
        3: 'enhanced', 6: 'enhanced', 9: 'enhanced', 12: 'enhanced',
        15: 'balanced', 19: 'balanced', 24: 'balanced', 28: 'auto',
        33: 'lightweight', 38: 'balanced', 43: 'balanced', 47: 'enhanced',
        52: 'balanced', 57: 'balanced'
    }
    
    # 为每个关键点数量生成预测
    true_keypoints_list = []
    pred_keypoints_list = []
    available_counts = []
    
    for num_kp in keypoint_counts:
        try:
            print(f"\n🔮 生成 {num_kp} 关键点预测...")
            
            # 生成关键点子集
            test_kp_subset = generate_keypoint_subsets(test_kp, num_kp)
            
            # 创建和加载模型
            arch_type = best_configs.get(num_kp, 'balanced')
            model = AdaptiveKeypointModel(
                num_points=50000, 
                num_keypoints=num_kp, 
                architecture_type=arch_type
            ).to(device)
            
            # 尝试加载训练好的模型
            model_path = f'best_{num_kp}kp_{arch_type}.pth'
            if os.path.exists(model_path):
                model.load_state_dict(torch.load(model_path, map_location=device))
                print(f"✅ 加载模型: {model_path}")
            else:
                print(f"⚠️ 模型文件不存在: {model_path}，使用随机初始化")
            
            model.eval()
            
            # 生成预测
            with torch.no_grad():
                test_pc_tensor = torch.FloatTensor(test_pc).transpose(1, 2).to(device)
                predictions = model(test_pc_tensor).cpu().numpy()
            
            true_keypoints_list.append(test_kp_subset)
            pred_keypoints_list.append(predictions)
            available_counts.append(num_kp)
            
        except Exception as e:
            print(f"❌ {num_kp}关键点预测失败: {e}")
            continue
    
    if not available_counts:
        print("❌ 没有成功的预测结果")
        return
    
    # 创建可视化
    for sample_idx in [0, 1, 2]:  # 可视化前3个样本
        if sample_idx < len(test_pc):
            visualize_predictions(test_pc, true_keypoints_list, pred_keypoints_list, 
                                available_counts, sample_idx)

if __name__ == "__main__":
    run_prediction_visualization()
