#!/usr/bin/env python3
"""
关键点相互辅助定位策略：利用解剖学约束提升小数据集性能
Keypoint Mutual Assistance: Using Anatomical Constraints for Small Dataset Enhancement
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import os
from tqdm import tqdm
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

class AnatomicalConstraintLoss(nn.Module):
    """解剖学约束损失函数"""
    
    def __init__(self, alpha=1.0, beta=0.5, gamma=0.3, delta=0.2):
        super(AnatomicalConstraintLoss, self).__init__()
        self.alpha = alpha    # 基础MSE权重
        self.beta = beta      # 距离约束权重
        self.gamma = gamma    # 角度约束权重
        self.delta = delta    # 对称性约束权重
        
        # 定义关键点索引 (基于12个关键点)
        self.f1_indices = [0, 1, 2, 3]      # F1区域关键点
        self.f2_indices = [4, 5, 6, 7]      # F2区域关键点
        self.f3_indices = [8, 9, 10, 11]    # F3区域关键点
        
        # 定义重要的解剖学距离约束 (基于医学知识)
        self.distance_constraints = [
            # F1-F2对称性约束
            ([0, 4], 'symmetric_pair'),  # F1-1 和 F2-1 应该大致对称
            ([1, 5], 'symmetric_pair'),  # F1-2 和 F2-2 应该大致对称
            ([2, 6], 'symmetric_pair'),  # F1-3 和 F2-3 应该大致对称
            ([3, 7], 'symmetric_pair'),  # F1-4 和 F2-4 应该大致对称
            
            # 区域内距离约束
            ([0, 1], 'region_distance'),  # F1区域内相邻点
            ([1, 2], 'region_distance'),  # F1区域内相邻点
            ([4, 5], 'region_distance'),  # F2区域内相邻点
            ([5, 6], 'region_distance'),  # F2区域内相邻点
            ([8, 9], 'region_distance'),  # F3区域内相邻点
            ([9, 10], 'region_distance'), # F3区域内相邻点
            
            # 跨区域关键距离
            ([0, 8], 'cross_region'),     # F1-1 到 F3-1
            ([4, 8], 'cross_region'),     # F2-1 到 F3-1
        ]
        
    def forward(self, pred_kp, target_kp):
        batch_size = pred_kp.shape[0]
        
        # 1. 基础MSE损失
        mse_loss = F.mse_loss(pred_kp, target_kp)
        
        # 2. 距离约束损失
        distance_loss = self.compute_distance_constraints(pred_kp, target_kp)
        
        # 3. 角度约束损失
        angle_loss = self.compute_angle_constraints(pred_kp, target_kp)
        
        # 4. 对称性约束损失
        symmetry_loss = self.compute_symmetry_constraints(pred_kp, target_kp)
        
        # 总损失
        total_loss = (self.alpha * mse_loss + 
                     self.beta * distance_loss + 
                     self.gamma * angle_loss + 
                     self.delta * symmetry_loss)
        
        return total_loss, mse_loss, distance_loss, angle_loss, symmetry_loss
    
    def compute_distance_constraints(self, pred_kp, target_kp):
        """计算距离约束损失"""
        distance_losses = []
        
        for constraint in self.distance_constraints:
            indices, constraint_type = constraint
            idx1, idx2 = indices
            
            # 预测距离
            pred_dist = torch.norm(pred_kp[:, idx1] - pred_kp[:, idx2], dim=1)
            # 真实距离
            target_dist = torch.norm(target_kp[:, idx1] - target_kp[:, idx2], dim=1)
            
            # 距离一致性损失
            dist_loss = F.mse_loss(pred_dist, target_dist)
            
            # 根据约束类型调整权重
            if constraint_type == 'symmetric_pair':
                dist_loss *= 1.5  # 对称性更重要
            elif constraint_type == 'region_distance':
                dist_loss *= 1.2  # 区域内距离重要
            elif constraint_type == 'cross_region':
                dist_loss *= 1.0  # 跨区域距离
                
            distance_losses.append(dist_loss)
        
        return torch.mean(torch.stack(distance_losses))
    
    def compute_angle_constraints(self, pred_kp, target_kp):
        """计算角度约束损失"""
        angle_losses = []
        
        # 定义重要的角度约束
        angle_constraints = [
            # F1区域的角度
            ([0, 1, 2], 'region_angle'),  # F1-1, F1-2, F1-3形成的角度
            ([1, 2, 3], 'region_angle'),  # F1-2, F1-3, F1-4形成的角度
            
            # F2区域的角度
            ([4, 5, 6], 'region_angle'),  # F2-1, F2-2, F2-3形成的角度
            ([5, 6, 7], 'region_angle'),  # F2-2, F2-3, F2-4形成的角度
            
            # F3区域的角度
            ([8, 9, 10], 'region_angle'), # F3-1, F3-2, F3-3形成的角度
            ([9, 10, 11], 'region_angle'), # F3-2, F3-3, F3-4形成的角度
            
            # 跨区域角度
            ([0, 8, 4], 'cross_angle'),   # F1-1, F3-1, F2-1形成的角度
        ]
        
        for constraint in angle_constraints:
            indices, constraint_type = constraint
            idx1, idx2, idx3 = indices
            
            # 计算角度
            pred_angle = self.compute_angle(pred_kp[:, idx1], pred_kp[:, idx2], pred_kp[:, idx3])
            target_angle = self.compute_angle(target_kp[:, idx1], target_kp[:, idx2], target_kp[:, idx3])
            
            # 角度一致性损失
            angle_loss = F.mse_loss(pred_angle, target_angle)
            angle_losses.append(angle_loss)
        
        if angle_losses:
            return torch.mean(torch.stack(angle_losses))
        else:
            return torch.tensor(0.0).to(pred_kp.device)
    
    def compute_angle(self, p1, p2, p3):
        """计算三点形成的角度"""
        v1 = p1 - p2  # 向量1
        v2 = p3 - p2  # 向量2
        
        # 计算余弦值
        cos_angle = F.cosine_similarity(v1, v2, dim=1)
        # 限制在[-1, 1]范围内避免数值问题
        cos_angle = torch.clamp(cos_angle, -1.0, 1.0)
        
        return cos_angle
    
    def compute_symmetry_constraints(self, pred_kp, target_kp):
        """计算对称性约束损失"""
        symmetry_losses = []
        
        # F1和F2的对称性约束
        for i in range(4):
            f1_idx = self.f1_indices[i]
            f2_idx = self.f2_indices[i]
            
            # 计算中心点 (假设F3区域的第一个点作为参考)
            center = (pred_kp[:, 8] + target_kp[:, 8]) / 2  # F3-1作为中心参考
            
            # 预测的对称性
            pred_f1_to_center = pred_kp[:, f1_idx] - center
            pred_f2_to_center = pred_kp[:, f2_idx] - center
            
            # 真实的对称性
            target_f1_to_center = target_kp[:, f1_idx] - center
            target_f2_to_center = target_kp[:, f2_idx] - center
            
            # 对称性损失 (Y坐标应该相反，X和Z坐标应该相似)
            pred_symmetry = torch.stack([
                pred_f1_to_center[:, 0] + pred_f2_to_center[:, 0],  # X坐标和应该接近0
                pred_f1_to_center[:, 1] - pred_f2_to_center[:, 1],  # Y坐标差应该接近0
                pred_f1_to_center[:, 2] + pred_f2_to_center[:, 2],  # Z坐标和应该接近0
            ], dim=1)
            
            target_symmetry = torch.stack([
                target_f1_to_center[:, 0] + target_f2_to_center[:, 0],
                target_f1_to_center[:, 1] - target_f2_to_center[:, 1],
                target_f1_to_center[:, 2] + target_f2_to_center[:, 2],
            ], dim=1)
            
            symmetry_loss = F.mse_loss(pred_symmetry, target_symmetry)
            symmetry_losses.append(symmetry_loss)
        
        if symmetry_losses:
            return torch.mean(torch.stack(symmetry_losses))
        else:
            return torch.tensor(0.0).to(pred_kp.device)

class MutualAssistanceNet(nn.Module):
    """相互辅助网络：关键点之间相互帮助定位"""
    
    def __init__(self, num_points=50000, num_keypoints=12):
        super(MutualAssistanceNet, self).__init__()
        
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        
        # 基础特征提取
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Conv1d(256, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
        )
        
        # 全局特征
        self.global_pool = nn.AdaptiveMaxPool1d(1)
        
        # 初始关键点预测
        self.initial_predictor = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, num_keypoints * 3)
        )
        
        # 相互辅助模块
        self.mutual_assistance = MutualAssistanceModule(num_keypoints)
        
        # 最终精化模块
        self.refinement = nn.Sequential(
            nn.Linear(num_keypoints * 3 + 512, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, num_keypoints * 3)
        )
        
    def forward(self, x):
        batch_size = x.size(0)
        
        # 特征提取
        x = x.transpose(2, 1)  # [B, 3, N]
        features = self.feature_extractor(x)  # [B, 512, N]
        
        # 全局特征
        global_feat = self.global_pool(features).squeeze(-1)  # [B, 512]
        
        # 初始关键点预测
        initial_kp = self.initial_predictor(global_feat)  # [B, num_keypoints*3]
        initial_kp = initial_kp.view(batch_size, self.num_keypoints, 3)
        
        # 相互辅助优化
        assisted_kp = self.mutual_assistance(initial_kp, global_feat)
        
        # 最终精化
        combined_feat = torch.cat([assisted_kp.view(batch_size, -1), global_feat], dim=1)
        refinement_delta = self.refinement(combined_feat)
        refinement_delta = refinement_delta.view(batch_size, self.num_keypoints, 3)
        
        # 最终关键点
        final_kp = assisted_kp + refinement_delta
        
        return final_kp, initial_kp, assisted_kp

class MutualAssistanceModule(nn.Module):
    """相互辅助模块：让关键点相互帮助定位"""
    
    def __init__(self, num_keypoints=12):
        super(MutualAssistanceModule, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 关键点特征编码
        self.kp_encoder = nn.Sequential(
            nn.Linear(3, 64),
            nn.ReLU(),
            nn.Linear(64, 128)
        )
        
        # 关系建模
        self.relation_net = nn.Sequential(
            nn.Linear(128 * 2, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 64)
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(embed_dim=128, num_heads=8, batch_first=True)
        
        # 位置更新
        self.position_updater = nn.Sequential(
            nn.Linear(128 + 512, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 3)
        )
        
    def forward(self, initial_kp, global_feat):
        batch_size, num_kp, _ = initial_kp.shape
        
        # 编码关键点特征
        kp_features = self.kp_encoder(initial_kp)  # [B, num_kp, 128]
        
        # 自注意力：让关键点相互关注
        attended_features, _ = self.attention(kp_features, kp_features, kp_features)
        
        # 为每个关键点计算位置更新
        assisted_kp = []
        
        for i in range(num_kp):
            # 当前关键点的特征
            current_feat = attended_features[:, i, :]  # [B, 128]
            
            # 结合全局特征
            combined_feat = torch.cat([current_feat, global_feat], dim=1)  # [B, 128+512]
            
            # 计算位置调整
            position_delta = self.position_updater(combined_feat)  # [B, 3]
            
            # 更新位置
            updated_position = initial_kp[:, i, :] + position_delta
            assisted_kp.append(updated_position)
        
        assisted_kp = torch.stack(assisted_kp, dim=1)  # [B, num_kp, 3]
        
        return assisted_kp

def load_clean_data():
    """加载清洁数据"""
    
    print("📊 加载清洁数据...")
    
    # 加载女性原始数据
    female_original_path = "archive/old_experiments/f3_reduced_12kp_female.npz"
    if os.path.exists(female_original_path):
        female_data = np.load(female_original_path, allow_pickle=True)
        female_pc = female_data['point_clouds']
        female_kp = female_data['keypoints']
        print(f"✅ 女性原始数据: {len(female_pc)}个样本")
    else:
        print(f"❌ 女性原始数据不存在")
        return None
    
    # 加载男性原始数据
    male_original_path = "archive/old_experiments/f3_reduced_12kp_male.npz"
    if os.path.exists(male_original_path):
        male_data = np.load(male_original_path, allow_pickle=True)
        male_pc = male_data['point_clouds']
        male_kp = male_data['keypoints']
        print(f"✅ 男性原始数据: {len(male_pc)}个样本")
    else:
        print(f"❌ 男性原始数据不存在")
        return None
    
    return female_pc, female_kp, male_pc, male_kp

def analyze_anatomical_relationships(kp_data, gender_name):
    """分析解剖学关系"""
    
    print(f"\n🔍 分析{gender_name}解剖学关系")
    
    # 计算关键点间距离统计
    distances = {}
    
    # F1-F2对称性分析
    for i in range(4):
        f1_idx = i
        f2_idx = i + 4
        
        dists = []
        for sample in kp_data:
            dist = np.linalg.norm(sample[f1_idx] - sample[f2_idx])
            dists.append(dist)
        
        distances[f'F1_{i+1}-F2_{i+1}'] = {
            'mean': np.mean(dists),
            'std': np.std(dists),
            'min': np.min(dists),
            'max': np.max(dists)
        }
    
    # 区域内距离分析
    regions = {'F1': [0, 1, 2, 3], 'F2': [4, 5, 6, 7], 'F3': [8, 9, 10, 11]}
    
    for region_name, indices in regions.items():
        for i in range(len(indices)-1):
            idx1, idx2 = indices[i], indices[i+1]
            
            dists = []
            for sample in kp_data:
                dist = np.linalg.norm(sample[idx1] - sample[idx2])
                dists.append(dist)
            
            distances[f'{region_name}_{i+1}-{i+2}'] = {
                'mean': np.mean(dists),
                'std': np.std(dists),
                'min': np.min(dists),
                'max': np.max(dists)
            }
    
    # 打印统计结果
    print(f"📏 {gender_name}关键点距离统计:")
    for name, stats in distances.items():
        print(f"   {name}: {stats['mean']:.2f}±{stats['std']:.2f}mm (范围: {stats['min']:.2f}-{stats['max']:.2f})")
    
    return distances

def train_mutual_assistance_model(train_pc, train_kp, val_pc, val_kp, gender_name):
    """训练相互辅助模型"""
    
    print(f"\n🤝 训练{gender_name}相互辅助模型")
    print("=" * 60)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    
    # 模型初始化
    model = MutualAssistanceNet(num_points=50000, num_keypoints=12)
    model = model.to(device)
    
    print(f"🏗️ {gender_name}相互辅助模型:")
    print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"   训练样本: {len(train_pc)}")
    print(f"   验证样本: {len(val_pc)}")
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=0.0001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=100, eta_min=1e-6)
    criterion = AnatomicalConstraintLoss(alpha=1.0, beta=0.3, gamma=0.2, delta=0.2)
    
    # 训练参数
    num_epochs = 100
    batch_size = 4
    best_val_error = float('inf')
    patience = 30
    patience_counter = 0
    
    print(f"🎯 训练参数:")
    print(f"   训练轮数: {num_epochs}")
    print(f"   批次大小: {batch_size}")
    print(f"   损失函数: 解剖学约束损失")
    
    training_history = {
        'train_loss': [], 'val_error': [],
        'mse_loss': [], 'distance_loss': [], 'angle_loss': [], 'symmetry_loss': []
    }
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_losses = []
        loss_components = {'mse': [], 'distance': [], 'angle': [], 'symmetry': []}
        
        n_train_batches = len(train_pc) // batch_size
        
        for i in range(n_train_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(train_pc))
            
            batch_pc = torch.FloatTensor(train_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(train_kp[start_idx:end_idx]).to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            final_kp, initial_kp, assisted_kp = model(batch_pc)
            
            # 计算损失
            total_loss, mse_loss, distance_loss, angle_loss, symmetry_loss = criterion(final_kp, batch_kp)
            
            # 反向传播
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_losses.append(total_loss.item())
            loss_components['mse'].append(mse_loss.item())
            loss_components['distance'].append(distance_loss.item())
            loss_components['angle'].append(angle_loss.item())
            loss_components['symmetry'].append(symmetry_loss.item())
        
        avg_train_loss = np.mean(train_losses)
        
        # 验证阶段
        model.eval()
        val_errors = []
        
        with torch.no_grad():
            n_val_batches = len(val_pc) // batch_size + (1 if len(val_pc) % batch_size > 0 else 0)
            
            for i in range(n_val_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(val_pc))
                
                batch_pc = torch.FloatTensor(val_pc[start_idx:end_idx]).to(device)
                batch_kp = torch.FloatTensor(val_kp[start_idx:end_idx]).to(device)
                
                final_kp, initial_kp, assisted_kp = model(batch_pc)
                
                for j in range(len(batch_kp)):
                    error = torch.mean(torch.norm(final_kp[j] - batch_kp[j], dim=1))
                    val_errors.append(error.item())
        
        avg_val_error = np.mean(val_errors)
        
        # 记录训练历史
        training_history['train_loss'].append(avg_train_loss)
        training_history['val_error'].append(avg_val_error)
        training_history['mse_loss'].append(np.mean(loss_components['mse']))
        training_history['distance_loss'].append(np.mean(loss_components['distance']))
        training_history['angle_loss'].append(np.mean(loss_components['angle']))
        training_history['symmetry_loss'].append(np.mean(loss_components['symmetry']))
        
        # 学习率调度
        scheduler.step()
        
        if epoch % 10 == 0:
            print(f"Epoch {epoch+1}/{num_epochs}:")
            print(f"  总损失: {avg_train_loss:.4f}, 验证误差: {avg_val_error:.2f}mm")
            print(f"  MSE: {np.mean(loss_components['mse']):.4f}, 距离: {np.mean(loss_components['distance']):.4f}")
            print(f"  角度: {np.mean(loss_components['angle']):.4f}, 对称: {np.mean(loss_components['symmetry']):.4f}")
        
        # 早停和模型保存
        if avg_val_error < best_val_error:
            best_val_error = avg_val_error
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'val_error': avg_val_error,
                'training_history': training_history
            }, f'mutual_assistance_{gender_name}.pth')
        else:
            patience_counter += 1
            if patience_counter >= patience:
                break
    
    print(f"✅ {gender_name}相互辅助模型训练完成，最佳验证误差: {best_val_error:.2f}mm")
    
    return model, best_val_error, training_history

def test_mutual_assistance_model(model, test_pc, test_kp, gender_name):
    """测试相互辅助模型"""
    
    print(f"\n🧪 测试{gender_name}相互辅助模型")
    print("=" * 60)
    
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    model.eval()
    
    test_errors = []
    initial_errors = []
    assisted_errors = []
    
    batch_size = 4
    with torch.no_grad():
        n_test_batches = len(test_pc) // batch_size + (1 if len(test_pc) % batch_size > 0 else 0)
        
        for i in range(n_test_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(test_pc))
            
            batch_pc = torch.FloatTensor(test_pc[start_idx:end_idx]).to(device)
            batch_kp = torch.FloatTensor(test_kp[start_idx:end_idx]).to(device)
            
            final_kp, initial_kp, assisted_kp = model(batch_pc)
            
            for j in range(len(batch_kp)):
                # 最终误差
                final_error = torch.mean(torch.norm(final_kp[j] - batch_kp[j], dim=1))
                test_errors.append(final_error.item())
                
                # 初始误差
                initial_error = torch.mean(torch.norm(initial_kp[j] - batch_kp[j], dim=1))
                initial_errors.append(initial_error.item())
                
                # 辅助后误差
                assisted_error = torch.mean(torch.norm(assisted_kp[j] - batch_kp[j], dim=1))
                assisted_errors.append(assisted_error.item())
    
    final_avg_error = np.mean(test_errors)
    initial_avg_error = np.mean(initial_errors)
    assisted_avg_error = np.mean(assisted_errors)
    
    print(f"📊 {gender_name}相互辅助模型测试结果:")
    print(f"   测试样本数: {len(test_pc)}")
    print(f"   初始预测误差: {initial_avg_error:.2f}mm")
    print(f"   相互辅助后误差: {assisted_avg_error:.2f}mm")
    print(f"   最终精化误差: {final_avg_error:.2f}mm")
    print(f"   相互辅助改进: {initial_avg_error - assisted_avg_error:.2f}mm")
    print(f"   总体改进: {initial_avg_error - final_avg_error:.2f}mm")
    print(f"   医疗级状态: {'✅ 达标' if final_avg_error < 5.0 else '❌ 未达标'}")
    
    return final_avg_error, initial_avg_error, assisted_avg_error

def main():
    """主函数：关键点相互辅助优化"""
    
    print("🤝 关键点相互辅助定位：解决小数据集挑战")
    print("🎯 策略: 利用解剖学约束让关键点相互帮助定位")
    print("🔧 方法: 距离约束 + 角度约束 + 对称性约束 + 相互注意力")
    print("=" * 80)
    
    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 加载数据
    data_result = load_clean_data()
    if data_result is None:
        return
    
    female_pc, female_kp, male_pc, male_kp = data_result
    
    # 分析解剖学关系
    female_distances = analyze_anatomical_relationships(female_kp, "女性")
    male_distances = analyze_anatomical_relationships(male_kp, "男性")
    
    results = {}
    
    # 处理女性数据
    print(f"\n" + "="*80)
    print("👩 女性数据：相互辅助优化")
    print("="*80)
    
    # 数据分割 (保持简单的分割以避免过度复杂)
    from practical_model_optimization import analyze_data_quality, remove_outliers, split_clean_data, smart_data_augmentation
    
    female_quality = analyze_data_quality(female_pc, female_kp, "女性")
    female_clean_pc, female_clean_kp, _ = remove_outliers(
        female_pc, female_kp, female_quality, removal_ratio=0.01)
    
    (female_train_pc, female_train_kp), (female_val_pc, female_val_kp), (female_test_pc, female_test_kp) = \
        split_clean_data(female_clean_pc, female_clean_kp, "女性")
    
    # 适度数据增强
    female_aug_train_pc, female_aug_train_kp = smart_data_augmentation(
        female_train_pc, female_train_kp, target_size=150)
    
    # 训练相互辅助模型
    female_model, female_val_error, female_history = train_mutual_assistance_model(
        female_aug_train_pc, female_aug_train_kp,
        female_val_pc, female_val_kp, "女性")
    
    # 测试女性模型
    female_final_error, female_initial_error, female_assisted_error = test_mutual_assistance_model(
        female_model, female_test_pc, female_test_kp, "女性")
    
    results['female'] = {
        'final_error': female_final_error,
        'initial_error': female_initial_error,
        'assisted_error': female_assisted_error,
        'val_error': female_val_error,
        'test_samples': len(female_test_pc)
    }
    
    # 处理男性数据
    print(f"\n" + "="*80)
    print("👨 男性数据：相互辅助优化")
    print("="*80)
    
    male_quality = analyze_data_quality(male_pc, male_kp, "男性")
    male_clean_pc, male_clean_kp, _ = remove_outliers(
        male_pc, male_kp, male_quality, removal_ratio=0.01)
    
    (male_train_pc, male_train_kp), (male_val_pc, male_val_kp), (male_test_pc, male_test_kp) = \
        split_clean_data(male_clean_pc, male_clean_kp, "男性")
    
    # 适度数据增强
    male_aug_train_pc, male_aug_train_kp = smart_data_augmentation(
        male_train_pc, male_train_kp, target_size=450)
    
    # 训练相互辅助模型
    male_model, male_val_error, male_history = train_mutual_assistance_model(
        male_aug_train_pc, male_aug_train_kp,
        male_val_pc, male_val_kp, "男性")
    
    # 测试男性模型
    male_final_error, male_initial_error, male_assisted_error = test_mutual_assistance_model(
        male_model, male_test_pc, male_test_kp, "男性")
    
    results['male'] = {
        'final_error': male_final_error,
        'initial_error': male_initial_error,
        'assisted_error': male_assisted_error,
        'val_error': male_val_error,
        'test_samples': len(male_test_pc)
    }
    
    # 总结结果
    print(f"\n" + "="*80)
    print("🎉 关键点相互辅助优化结果总结")
    print("="*80)
    
    print(f"📊 女性相互辅助模型:")
    print(f"   初始预测误差: {results['female']['initial_error']:.2f}mm")
    print(f"   相互辅助后误差: {results['female']['assisted_error']:.2f}mm")
    print(f"   最终精化误差: {results['female']['final_error']:.2f}mm")
    print(f"   总体改进: {results['female']['initial_error'] - results['female']['final_error']:.2f}mm")
    print(f"   测试样本: {results['female']['test_samples']}个")
    
    print(f"\n📊 男性相互辅助模型:")
    print(f"   初始预测误差: {results['male']['initial_error']:.2f}mm")
    print(f"   相互辅助后误差: {results['male']['assisted_error']:.2f}mm")
    print(f"   最终精化误差: {results['male']['final_error']:.2f}mm")
    print(f"   总体改进: {results['male']['initial_error'] - results['male']['final_error']:.2f}mm")
    print(f"   测试样本: {results['male']['test_samples']}个")
    
    # 数据集质量评估
    avg_error = (results['female']['final_error'] + results['male']['final_error']) / 2
    both_medical_grade = results['female']['final_error'] < 5.0 and results['male']['final_error'] < 5.0
    
    print(f"\n🎯 相互辅助策略效果:")
    print(f"   平均测试误差: {avg_error:.2f}mm")
    print(f"   医疗级精度: {'✅ 整体达标' if both_medical_grade else '❌ 部分达标'}")
    print(f"   女性达标: {'✅' if results['female']['final_error'] < 5.0 else '❌'} {results['female']['final_error']:.2f}mm")
    print(f"   男性达标: {'✅' if results['male']['final_error'] < 5.0 else '❌'} {results['male']['final_error']:.2f}mm")
    
    if both_medical_grade:
        print(f"\n🎉 相互辅助策略成功!")
        print(f"   ✅ 通过解剖学约束实现了医疗级精度")
        print(f"   ✅ 关键点相互辅助定位策略有效")
        print(f"   ✅ 小数据集问题得到有效解决")
        print(f"   ✅ 数据集质量得到充分证明")
    else:
        print(f"\n📈 相互辅助策略显著改进")
        print(f"   ✅ 相比初始预测有明显提升")
        print(f"   ✅ 解剖学约束策略有效")
        print(f"   ⚠️ 仍需进一步优化以达到完全医疗级标准")
    
    return results

if __name__ == "__main__":
    main()
