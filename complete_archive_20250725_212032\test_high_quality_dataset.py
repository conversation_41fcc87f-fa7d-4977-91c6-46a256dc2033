#!/usr/bin/env python3
"""
测试高质量数据集的效果
Test high-quality dataset performance
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import json
from tqdm import tqdm

class SimplePointNet57(nn.Module):
    """简单的PointNet57模型 - 用于验证数据集质量"""
    
    def __init__(self, num_keypoints=57, dropout_rate=0.3):
        super(SimplePointNet57, self).__init__()
        
        self.num_keypoints = num_keypoints
        
        # 简单的特征提取
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        self.conv4 = nn.Conv1d(256, 512, 1)
        self.conv5 = nn.Conv1d(512, 1024, 1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        self.bn4 = nn.BatchNorm1d(512)
        self.bn5 = nn.BatchNorm1d(1024)
        
        # 简单的回归头
        self.fc1 = nn.Linear(1024, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, num_keypoints * 3)
        
        # 批归一化
        self.fc_bn1 = nn.BatchNorm1d(512)
        self.fc_bn2 = nn.BatchNorm1d(256)
        
        # Dropout
        self.dropout = nn.Dropout(dropout_rate)
        
        # 权重初始化
        self._initialize_weights()
        
        total_params = sum(p.numel() for p in self.parameters())
        print(f"🏗️ SimplePointNet57: {total_params:,} 参数")
    
    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # [B, 3, N]
        
        # 特征提取
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        x = F.relu(self.bn4(self.conv4(x)))
        x = F.relu(self.bn5(self.conv5(x)))
        
        # 全局最大池化
        global_feat = torch.max(x, 2)[0]
        
        # 回归
        x = F.relu(self.fc_bn1(self.fc1(global_feat)))
        x = self.dropout(x)
        x = F.relu(self.fc_bn2(self.fc2(x)))
        x = self.dropout(x)
        
        keypoints = self.fc3(x)
        keypoints = keypoints.view(batch_size, self.num_keypoints, 3)
        
        return keypoints

class Dataset57(Dataset):
    def __init__(self, point_clouds, keypoints):
        self.point_clouds = torch.FloatTensor(point_clouds)
        self.keypoints = torch.FloatTensor(keypoints)
        
    def __len__(self):
        return len(self.point_clouds)
    
    def __getitem__(self, idx):
        return self.point_clouds[idx], self.keypoints[idx]

def train_and_test_model(point_clouds, keypoints_57, dataset_name, epochs=100):
    """训练和测试模型"""
    
    print(f"\n🚀 测试 {dataset_name} 数据集")
    print(f"   样本数: {len(point_clouds)}")
    print(f"   点云形状: {point_clouds.shape}")
    print(f"   关键点形状: {keypoints_57.shape}")
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 数据归一化
    print("🔧 执行数据归一化...")
    normalized_pc = []
    normalized_kp = []
    scalers = []
    
    for i in range(len(point_clouds)):
        pc = point_clouds[i].copy()
        kp = keypoints_57[i].copy()
        
        combined_data = np.vstack([pc, kp])
        scaler = StandardScaler()
        combined_normalized = scaler.fit_transform(combined_data)
        
        pc_normalized = combined_normalized[:len(pc)]
        kp_normalized = combined_normalized[len(pc):]
        
        normalized_pc.append(pc_normalized)
        normalized_kp.append(kp_normalized)
        scalers.append(scaler)
    
    normalized_pc = np.array(normalized_pc)
    normalized_kp = np.array(normalized_kp)
    
    # 数据划分
    indices = np.arange(len(normalized_pc))
    train_indices, test_indices = train_test_split(indices, test_size=0.2, random_state=42)
    train_indices, val_indices = train_test_split(train_indices, test_size=0.2, random_state=42)
    
    # 创建数据集
    train_dataset = Dataset57(normalized_pc[train_indices], normalized_kp[train_indices])
    val_dataset = Dataset57(normalized_pc[val_indices], normalized_kp[val_indices])
    test_dataset = Dataset57(normalized_pc[test_indices], normalized_kp[test_indices])
    
    # 数据加载器
    batch_size = 8
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    
    print(f"📋 数据划分: 训练{len(train_dataset)}, 验证{len(val_dataset)}, 测试{len(test_dataset)}")
    
    # 创建模型
    model = SimplePointNet57(num_keypoints=57, dropout_rate=0.3).to(device)
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.8, patience=10, min_lr=1e-6
    )
    criterion = nn.MSELoss()
    
    # 训练
    print(f"🚀 开始训练...")
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 20
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0.0
        train_error = 0.0
        
        for batch_pc, batch_kp in train_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            optimizer.zero_grad()
            predicted = model(batch_pc)
            loss = criterion(predicted, batch_kp)
            loss.backward()
            
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            
            with torch.no_grad():
                distances = torch.norm(predicted - batch_kp, dim=2)
                train_error += torch.mean(distances).item()
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_error = 0.0
        
        with torch.no_grad():
            for batch_pc, batch_kp in val_loader:
                batch_pc = batch_pc.to(device)
                batch_kp = batch_kp.to(device)
                
                predicted = model(batch_pc)
                loss = criterion(predicted, batch_kp)
                
                val_loss += loss.item()
                distances = torch.norm(predicted - batch_kp, dim=2)
                val_error += torch.mean(distances).item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_error /= len(train_loader)
        val_error /= len(val_loader)
        
        scheduler.step(val_loss)
        
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), f'best_{dataset_name}_model.pth')
        else:
            patience_counter += 1
        
        if epoch % 10 == 0 or epoch < 5:
            current_lr = optimizer.param_groups[0]['lr']
            print(f"Epoch {epoch+1:3d}: "
                  f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
                  f"Train Error: {train_error:.4f}, Val Error: {val_error:.4f}, "
                  f"LR: {current_lr:.2e}")
        
        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch+1} 轮停止训练")
            break
    
    # 加载最佳模型并测试
    model.load_state_dict(torch.load(f'best_{dataset_name}_model.pth'))
    model.eval()
    
    # 测试
    print(f"🔍 测试模型性能...")
    test_predictions = []
    test_targets = []
    
    with torch.no_grad():
        for batch_pc, batch_kp in test_loader:
            batch_pc = batch_pc.to(device)
            batch_kp = batch_kp.to(device)
            
            predicted = model(batch_pc)
            
            test_predictions.append(predicted.cpu().numpy())
            test_targets.append(batch_kp.cpu().numpy())
    
    test_predictions = np.vstack(test_predictions)
    test_targets = np.vstack(test_targets)
    
    # 反归一化
    print(f"🔄 反归一化预测结果...")
    real_predictions = []
    real_targets = []
    
    for i, orig_idx in enumerate(test_indices):
        if i < len(test_predictions):
            pred_norm = test_predictions[i]
            target_norm = test_targets[i]
            
            scaler = scalers[orig_idx]
            
            # 反归一化
            dummy_pc = np.zeros((50000, 3))
            
            combined_pred = np.vstack([dummy_pc, pred_norm])
            combined_pred_denorm = scaler.inverse_transform(combined_pred)
            pred_real = combined_pred_denorm[50000:]
            
            combined_target = np.vstack([dummy_pc, target_norm])
            combined_target_denorm = scaler.inverse_transform(combined_target)
            target_real = combined_target_denorm[50000:]
            
            real_predictions.append(pred_real)
            real_targets.append(target_real)
    
    real_predictions = np.array(real_predictions)
    real_targets = np.array(real_targets)
    
    # 计算真实误差
    total_error = 0.0
    region_errors = {'F1': [], 'F2': [], 'F3': []}
    all_errors = []
    
    for i in range(len(real_predictions)):
        pred = real_predictions[i]
        target = real_targets[i]
        
        distances = np.linalg.norm(pred - target, axis=1)
        total_error += np.mean(distances)
        all_errors.extend(distances)
        
        # 分区域
        region_errors['F1'].extend(distances[0:19])
        region_errors['F2'].extend(distances[19:38])
        region_errors['F3'].extend(distances[38:57])
    
    avg_error = total_error / len(real_predictions)
    
    # 计算准确率
    accuracy_5mm = np.mean(np.array(all_errors) < 5.0) * 100
    accuracy_10mm = np.mean(np.array(all_errors) < 10.0) * 100
    
    print(f"\n🎯 {dataset_name} 数据集结果:")
    print(f"   整体平均误差: {avg_error:.2f}mm")
    
    for region, errors in region_errors.items():
        if errors:
            mean_error = np.mean(errors)
            std_error = np.std(errors)
            print(f"   {region}区域: {mean_error:.2f}±{std_error:.2f}mm")
    
    print(f"   医疗级准确率:")
    print(f"     <5mm: {accuracy_5mm:.1f}%")
    print(f"     <10mm: {accuracy_10mm:.1f}%")
    
    return {
        'dataset_name': dataset_name,
        'avg_error': avg_error,
        'region_errors': {k: np.mean(v) for k, v in region_errors.items()},
        'accuracy_5mm': accuracy_5mm,
        'accuracy_10mm': accuracy_10mm,
        'sample_count': len(point_clouds)
    }

def main():
    """主函数 - 对比测试不同数据集"""
    
    print("🎯 高质量数据集效果验证")
    print("对比原始unified数据集 vs 高质量重建数据集")
    print("=" * 80)
    
    results = {}
    
    # 测试1: 原始unified数据集
    print("\n📋 测试1: 原始unified数据集")
    try:
        data1 = np.load('unified_pelvis_57_dataset.npz', allow_pickle=True)
        pc1 = data1['point_clouds']
        kp1 = data1['keypoints_57']
        
        results['unified'] = train_and_test_model(pc1, kp1, 'unified', epochs=80)
    except Exception as e:
        print(f"❌ 无法加载unified数据集: {e}")
    
    # 测试2: 高质量重建数据集
    print("\n📋 测试2: 高质量重建数据集")
    try:
        data2 = np.load('high_quality_pelvis_57_dataset.npz', allow_pickle=True)
        pc2 = data2['point_clouds']
        kp2 = data2['keypoints_57']
        
        results['high_quality'] = train_and_test_model(pc2, kp2, 'high_quality', epochs=80)
    except Exception as e:
        print(f"❌ 无法加载高质量数据集: {e}")
    
    # 对比结果
    print(f"\n📊 数据集质量对比结果")
    print("=" * 80)
    
    if len(results) >= 2:
        unified = results.get('unified', {})
        high_quality = results.get('high_quality', {})
        
        print(f"数据集对比:")
        print(f"                    Unified     HighQuality   改进")
        print(f"  样本数:          {unified.get('sample_count', 0):6d}      {high_quality.get('sample_count', 0):6d}")
        print(f"  整体误差:        {unified.get('avg_error', 0):6.2f}mm    {high_quality.get('avg_error', 0):6.2f}mm   {((unified.get('avg_error', 0)-high_quality.get('avg_error', 0))/unified.get('avg_error', 1)*100):+5.1f}%")
        
        if 'region_errors' in unified and 'region_errors' in high_quality:
            for region in ['F1', 'F2', 'F3']:
                u_err = unified['region_errors'].get(region, 0)
                h_err = high_quality['region_errors'].get(region, 0)
                improvement = ((u_err - h_err) / u_err * 100) if u_err > 0 else 0
                print(f"  {region}区域误差:      {u_err:6.2f}mm    {h_err:6.2f}mm   {improvement:+5.1f}%")
        
        print(f"  <5mm准确率:      {unified.get('accuracy_5mm', 0):6.1f}%     {high_quality.get('accuracy_5mm', 0):6.1f}%    {(high_quality.get('accuracy_5mm', 0)-unified.get('accuracy_5mm', 0)):+5.1f}%")
        print(f"  <10mm准确率:     {unified.get('accuracy_10mm', 0):6.1f}%     {high_quality.get('accuracy_10mm', 0):6.1f}%    {(high_quality.get('accuracy_10mm', 0)-unified.get('accuracy_10mm', 0)):+5.1f}%")
        
        # 保存对比结果
        comparison = {
            'unified_dataset': unified,
            'high_quality_dataset': high_quality,
            'improvement_summary': {
                'error_reduction': ((unified.get('avg_error', 0) - high_quality.get('avg_error', 0)) / unified.get('avg_error', 1) * 100),
                'accuracy_5mm_improvement': (high_quality.get('accuracy_5mm', 0) - unified.get('accuracy_5mm', 0)),
                'data_quality_impact': 'significant' if high_quality.get('avg_error', 0) < unified.get('avg_error', 0) else 'minimal'
            }
        }
        
        with open('dataset_quality_comparison.json', 'w') as f:
            json.dump(comparison, f, indent=2, default=str)
        
        print(f"\n💾 对比结果已保存: dataset_quality_comparison.json")
        
        if high_quality.get('avg_error', 0) < unified.get('avg_error', 0):
            improvement = ((unified.get('avg_error', 0) - high_quality.get('avg_error', 0)) / unified.get('avg_error', 1) * 100)
            print(f"\n✅ 高质量数据集显著提升性能！")
            print(f"💡 误差降低: {improvement:.1f}%")
            print(f"🎯 证明了数据质量改进的重要性")
        else:
            print(f"\n⚠️ 数据集改进效果有限")
    
    print(f"\n🎉 数据集质量验证完成！")

if __name__ == "__main__":
    main()
