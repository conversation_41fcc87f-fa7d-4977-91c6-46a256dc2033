"""
交互式3D预测可视化工具
支持鼠标拖动旋转、缩放等交互操作
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.widgets as widgets
from pathlib import Path
import tkinter as tk
from tkinter import ttk
import threading

from save_best_model import BestSimplePointNet
from improved_data_loader import ImprovedDataLoader

class Interactive3DViewer:
    """交互式3D查看器"""
    
    def __init__(self, data_root="output/training_fixed"):
        self.data_root = data_root
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 创建输出目录
        self.output_dir = Path("output/interactive_3d")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🎮 交互式3D查看器初始化")
        
        # 数据存储
        self.samples = []
        self.current_sample_idx = 0
        self.show_point_cloud = True
        self.show_connections = True
        self.show_error_colors = True
        
        # matplotlib设置
        plt.style.use('default')
        
    def load_model_and_data(self):
        """加载模型和数据"""
        print("加载模型和数据...")
        
        # 加载模型
        model = BestSimplePointNet(num_keypoints=57)
        model_path = "output/scale_corrected_training/best_baseline_model.pth"
        
        if Path(model_path).exists():
            checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"✅ 模型加载成功")
        
        model = model.to(self.device)
        model.eval()
        
        # 加载数据
        data_loader_manager = ImprovedDataLoader(
            data_root=self.data_root,
            batch_size=1,
            num_workers=0,
            num_points=512
        )
        
        _, val_loader = data_loader_manager.create_dataloaders(train_ratio=0.8)
        
        # 获取多个样本进行预测
        print("进行预测...")
        with torch.no_grad():
            for i, (point_cloud, keypoints) in enumerate(val_loader):
                if i >= 10:  # 加载10个样本
                    break
                
                point_cloud = point_cloud.to(self.device)
                keypoints = keypoints.to(self.device)
                
                pred_keypoints = model(point_cloud)
                error = torch.norm(pred_keypoints - keypoints, dim=2).cpu().numpy()
                
                sample_data = {
                    'id': i,
                    'point_cloud': point_cloud.cpu().numpy()[0],
                    'ground_truth': keypoints.cpu().numpy()[0],
                    'prediction': pred_keypoints.cpu().numpy()[0],
                    'error': error[0],
                    'mean_error': np.mean(error[0]),
                    'accuracy_5mm': (error[0] <= 5.0).mean() * 100
                }
                
                self.samples.append(sample_data)
                print(f"样本 {i+1}: 误差 {sample_data['mean_error']:.2f}mm, 准确率 {sample_data['accuracy_5mm']:.1f}%")
        
        print(f"✅ 加载了 {len(self.samples)} 个样本")
        return model
    
    def create_interactive_plot(self):
        """创建交互式3D图表"""
        print("创建交互式3D图表...")
        
        # 创建图形和3D轴
        self.fig = plt.figure(figsize=(16, 12))
        self.ax = self.fig.add_subplot(111, projection='3d')
        
        # 设置初始视角
        self.ax.view_init(elev=20, azim=45)
        
        # 绘制初始样本
        self.update_plot()
        
        # 添加控制面板
        self.add_control_panel()
        
        # 设置交互事件
        self.setup_interactions()
        
        plt.tight_layout()
        return self.fig
    
    def update_plot(self):
        """更新3D图表"""
        # 清除当前图表
        self.ax.clear()
        
        # 获取当前样本数据
        sample = self.samples[self.current_sample_idx]
        pc = sample['point_cloud']
        gt = sample['ground_truth']
        pred = sample['prediction']
        error = sample['error']
        
        # 1. 绘制点云（如果启用）
        if self.show_point_cloud:
            self.ax.scatter(pc[:, 0], pc[:, 1], pc[:, 2], 
                           c='lightgray', alpha=0.1, s=1, label='Point Cloud')
        
        # 2. 绘制真实关键点
        self.ax.scatter(gt[:, 0], gt[:, 1], gt[:, 2], 
                       c='red', s=80, alpha=0.9, label='Ground Truth', 
                       marker='o', edgecolors='darkred', linewidth=2)
        
        # 3. 绘制预测关键点（根据误差着色）
        if self.show_error_colors:
            scatter = self.ax.scatter(pred[:, 0], pred[:, 1], pred[:, 2], 
                                    c=error, s=80, alpha=0.9, cmap='RdYlGn_r', 
                                    vmin=0, vmax=8, marker='^', 
                                    edgecolors='black', linewidth=1, label='Prediction')
            
            # 添加颜色条
            if hasattr(self, 'cbar'):
                self.cbar.remove()
            self.cbar = plt.colorbar(scatter, ax=self.ax, shrink=0.8, pad=0.1)
            self.cbar.set_label('Error (mm)', rotation=270, labelpad=20)
        else:
            self.ax.scatter(pred[:, 0], pred[:, 1], pred[:, 2], 
                           c='blue', s=80, alpha=0.9, label='Prediction', 
                           marker='^', edgecolors='darkblue', linewidth=2)
        
        # 4. 绘制连接线（如果启用）
        if self.show_connections:
            for i in range(len(gt)):
                if self.show_error_colors:
                    color = plt.cm.RdYlGn_r(error[i] / 8.0)
                    alpha = min(1.0, error[i] / 5.0)
                else:
                    color = 'gray'
                    alpha = 0.5
                
                self.ax.plot([gt[i, 0], pred[i, 0]], 
                           [gt[i, 1], pred[i, 1]], 
                           [gt[i, 2], pred[i, 2]], 
                           color=color, alpha=alpha, linewidth=1.5)
        
        # 5. 设置标题和标签
        self.ax.set_title(f'Sample {sample["id"]+1} - Interactive 3D View\n'
                         f'Error: {sample["mean_error"]:.2f}mm, Accuracy: {sample["accuracy_5mm"]:.1f}%', 
                         fontsize=14, fontweight='bold', pad=20)
        
        self.ax.set_xlabel('X (mm)', fontsize=12)
        self.ax.set_ylabel('Y (mm)', fontsize=12)
        self.ax.set_zlabel('Z (mm)', fontsize=12)
        
        # 6. 设置坐标范围（保持一致）
        self.ax.set_xlim([-50, 50])
        self.ax.set_ylim([-50, 50])
        self.ax.set_zlim([-50, 50])
        
        # 7. 添加图例
        self.ax.legend(loc='upper left', bbox_to_anchor=(0, 1))
        
        # 8. 添加网格
        self.ax.grid(True, alpha=0.3)
        
        # 刷新图表
        self.fig.canvas.draw()
    
    def add_control_panel(self):
        """添加控制面板"""
        # 调整主图位置为控制面板留出空间
        self.fig.subplots_adjust(bottom=0.25)
        
        # 样本选择滑块
        ax_sample = plt.axes([0.1, 0.15, 0.5, 0.03])
        self.sample_slider = widgets.Slider(
            ax_sample, 'Sample', 1, len(self.samples), 
            valinit=1, valfmt='%d', valstep=1
        )
        self.sample_slider.on_changed(self.on_sample_change)
        
        # 点云显示复选框
        ax_pc = plt.axes([0.1, 0.10, 0.15, 0.04])
        self.pc_checkbox = widgets.CheckButtons(ax_pc, ['Show Point Cloud'], [self.show_point_cloud])
        self.pc_checkbox.on_clicked(self.on_pc_toggle)
        
        # 连接线显示复选框
        ax_conn = plt.axes([0.3, 0.10, 0.15, 0.04])
        self.conn_checkbox = widgets.CheckButtons(ax_conn, ['Show Connections'], [self.show_connections])
        self.conn_checkbox.on_clicked(self.on_conn_toggle)
        
        # 误差颜色复选框
        ax_error = plt.axes([0.5, 0.10, 0.15, 0.04])
        self.error_checkbox = widgets.CheckButtons(ax_error, ['Error Colors'], [self.show_error_colors])
        self.error_checkbox.on_clicked(self.on_error_toggle)
        
        # 重置视角按钮
        ax_reset = plt.axes([0.7, 0.15, 0.1, 0.04])
        self.reset_button = widgets.Button(ax_reset, 'Reset View')
        self.reset_button.on_clicked(self.on_reset_view)
        
        # 保存视图按钮
        ax_save = plt.axes([0.82, 0.15, 0.1, 0.04])
        self.save_button = widgets.Button(ax_save, 'Save View')
        self.save_button.on_clicked(self.on_save_view)
        
        # 添加信息文本
        info_text = f"Instructions:\n• Drag to rotate\n• Scroll to zoom\n• Use controls below"
        self.fig.text(0.02, 0.02, info_text, fontsize=10, 
                     bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    def setup_interactions(self):
        """设置交互事件"""
        # 鼠标事件
        self.fig.canvas.mpl_connect('button_press_event', self.on_mouse_press)
        self.fig.canvas.mpl_connect('button_release_event', self.on_mouse_release)
        self.fig.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)
        self.fig.canvas.mpl_connect('scroll_event', self.on_scroll)
        
        # 键盘事件
        self.fig.canvas.mpl_connect('key_press_event', self.on_key_press)
        
        # 初始化鼠标状态
        self.mouse_pressed = False
        self.last_mouse_pos = None
    
    def on_sample_change(self, val):
        """样本选择改变事件"""
        self.current_sample_idx = int(val) - 1
        self.update_plot()
    
    def on_pc_toggle(self, label):
        """点云显示切换事件"""
        self.show_point_cloud = not self.show_point_cloud
        self.update_plot()
    
    def on_conn_toggle(self, label):
        """连接线显示切换事件"""
        self.show_connections = not self.show_connections
        self.update_plot()
    
    def on_error_toggle(self, label):
        """误差颜色切换事件"""
        self.show_error_colors = not self.show_error_colors
        self.update_plot()
    
    def on_reset_view(self, event):
        """重置视角事件"""
        self.ax.view_init(elev=20, azim=45)
        self.fig.canvas.draw()
    
    def on_save_view(self, event):
        """保存视图事件"""
        filename = f"interactive_view_sample_{self.current_sample_idx+1}.png"
        filepath = self.output_dir / filename
        self.fig.savefig(filepath, dpi=300, bbox_inches='tight')
        print(f"✅ 视图已保存: {filepath}")
    
    def on_mouse_press(self, event):
        """鼠标按下事件"""
        if event.inaxes == self.ax:
            self.mouse_pressed = True
            self.last_mouse_pos = (event.xdata, event.ydata)
    
    def on_mouse_release(self, event):
        """鼠标释放事件"""
        self.mouse_pressed = False
        self.last_mouse_pos = None
    
    def on_mouse_move(self, event):
        """鼠标移动事件"""
        if self.mouse_pressed and event.inaxes == self.ax and self.last_mouse_pos:
            # 这里可以添加自定义的旋转逻辑
            # matplotlib的3D图表已经内置了拖动旋转功能
            pass
    
    def on_scroll(self, event):
        """滚轮缩放事件"""
        if event.inaxes == self.ax:
            # matplotlib的3D图表已经内置了滚轮缩放功能
            pass
    
    def on_key_press(self, event):
        """键盘按键事件"""
        if event.key == 'left' and self.current_sample_idx > 0:
            self.current_sample_idx -= 1
            self.sample_slider.set_val(self.current_sample_idx + 1)
        elif event.key == 'right' and self.current_sample_idx < len(self.samples) - 1:
            self.current_sample_idx += 1
            self.sample_slider.set_val(self.current_sample_idx + 1)
        elif event.key == 'r':
            self.on_reset_view(None)
        elif event.key == 's':
            self.on_save_view(None)
    
    def create_summary_stats(self):
        """创建统计摘要"""
        all_errors = [sample['mean_error'] for sample in self.samples]
        all_accuracies = [sample['accuracy_5mm'] for sample in self.samples]
        
        stats = {
            'num_samples': len(self.samples),
            'mean_error': np.mean(all_errors),
            'std_error': np.std(all_errors),
            'min_error': np.min(all_errors),
            'max_error': np.max(all_errors),
            'mean_accuracy': np.mean(all_accuracies),
            'min_accuracy': np.min(all_accuracies),
            'max_accuracy': np.max(all_accuracies)
        }
        
        # 保存统计信息
        with open(self.output_dir / 'interactive_stats.txt', 'w', encoding='utf-8') as f:
            f.write("交互式3D查看器统计信息\n")
            f.write("=" * 30 + "\n\n")
            f.write(f"样本数量: {stats['num_samples']}\n")
            f.write(f"平均误差: {stats['mean_error']:.2f} ± {stats['std_error']:.2f}mm\n")
            f.write(f"误差范围: [{stats['min_error']:.2f}, {stats['max_error']:.2f}]mm\n")
            f.write(f"平均5mm准确率: {stats['mean_accuracy']:.1f}%\n")
            f.write(f"准确率范围: [{stats['min_accuracy']:.1f}%, {stats['max_accuracy']:.1f}%]\n\n")
            f.write("操作说明:\n")
            f.write("• 鼠标拖动: 旋转视角\n")
            f.write("• 滚轮: 缩放\n")
            f.write("• 左右箭头键: 切换样本\n")
            f.write("• R键: 重置视角\n")
            f.write("• S键: 保存当前视图\n")
        
        return stats
    
    def run_interactive_viewer(self):
        """运行交互式查看器"""
        print("🚀 启动交互式3D查看器...")
        
        # 1. 加载数据
        model = self.load_model_and_data()
        
        # 2. 创建统计摘要
        stats = self.create_summary_stats()
        
        # 3. 创建交互式图表
        fig = self.create_interactive_plot()
        
        print(f"\n🎮 交互式3D查看器已启动!")
        print(f"📊 加载了 {len(self.samples)} 个样本")
        print(f"📈 平均误差: {stats['mean_error']:.2f}mm")
        print(f"🎯 平均准确率: {stats['mean_accuracy']:.1f}%")
        print(f"\n操作说明:")
        print(f"• 鼠标拖动旋转视角")
        print(f"• 滚轮缩放")
        print(f"• 使用下方控制面板")
        print(f"• 左右箭头键切换样本")
        print(f"• R键重置视角，S键保存视图")
        
        # 显示图表
        plt.show()
        
        return fig, stats

def main():
    """主函数"""
    viewer = Interactive3DViewer(data_root="output/training_fixed")
    
    try:
        fig, stats = viewer.run_interactive_viewer()
        print("🎉 交互式3D查看器运行完成!")
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
